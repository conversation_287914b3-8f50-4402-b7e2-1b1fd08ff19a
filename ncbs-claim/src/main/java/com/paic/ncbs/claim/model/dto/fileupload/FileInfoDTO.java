package com.paic.ncbs.claim.model.dto.fileupload;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("文件信息")
public class FileInfoDTO extends EntityDTO {

    private static final long serialVersionUID = -5701957359009304420L;
    @ApiModelProperty("主键")
    private String idAhcsFileInfo;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("环节号")
    private String taskCode;
    @ApiModelProperty("环节号数组")
    private List<String> taskCodeList;
    @ApiModelProperty(" 文件组ID")
    private String documentGroupId;
    @ApiModelProperty("文件名")
    private String fileName;
    @ApiModelProperty("文件链接")
    private String fileUrl;
    @ApiModelProperty("上传人员")
    private String uploadPersonnel;
    @ApiModelProperty("上传时间")
    private String uploadDate;
    @ApiModelProperty("备案类型：01是，00否，02其他")
    private String recordType;
    @ApiModelProperty("备案类型名称")
    private String recordTypeName;
    @ApiModelProperty("附件组内容ID(pk)")
    private String documentGroupItemsId;
    @ApiModelProperty("文件id数组")
    private List<String> documentItemIdList;
    @ApiModelProperty("文件id")
    private String fileId;
    @ApiModelProperty("文件格式")
    private String fileFormat;
    @ApiModelProperty("前端文件上传中的inputName")
    private String FileInputName;
    @ApiModelProperty("文件大小")
    private long fileSize;
    @ApiModelProperty("文件")
    private byte[] fileContentBytes;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("流程类型（01-首案 02-二次赔付 03-三次赔付... 51-申诉1 52-申诉2...）")
    private String flowType;
    @ApiModelProperty("AHCS_DOCUMENT_TYPE表小类代码")
    private String smallCode;
    @ApiModelProperty("AHCS_DOCUMENT_TYPE表大类代码")
    private String bigCode;
    @ApiModelProperty("补充")
    private String supplement;
    @ApiModelProperty("首传标志")
    private String firstUpload;
    @ApiModelProperty("文件组内容id数组")
    private String[] documentGroupItemsIdArr;
    @ApiModelProperty("补充")
    private String suplement;
    @ApiModelProperty("相似度")
    private String similarityScore;
    @ApiModelProperty("AI图像对比状态信息")
    private String compareResultMsg;
    @ApiModelProperty("录音流水号")
    private String recordNo;
    @ApiModelProperty("记录路径")
    private String recordPath;
    @ApiModelProperty("录音文件url")
    private String recordUrl;
    @ApiModelProperty("流程状态")
    private String processStatus;
    @ApiModelProperty("图片上传来源（拍照/相册）")
    private String imgUploadSource;
    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("录音流水号集合")
    private List<String> recordNoList;
    @ApiModelProperty("零注申请录音数量")
    private int recordZeroApplyCount;
    @ApiModelProperty("文件id集合")
    private List<String> fileIdList;
    @ApiModelProperty("立案申请审批")
    private RefuseNoticeDTO refuseNoticeDTO;
    @ApiModelProperty("iobs bucket")
    private String bucketName;
    @ApiModelProperty("子系统 文件服务器标识(01:IM,02:IOBS)")
    private String storageType;

    private Integer applyTimes;

    private List<FileInfoDTO> fileList;

    private String scanUpload;

    private String share;

    private Boolean isAutoCancel;

    private String[] idAhcsScanImageArr;

    private String selfSmallCode;

    private String customerId;

    private String batchNo = "";

    private String uploadSource;

    private String rescueId;

    private int pageId;
    private int filePage;

    private Integer clickOrder;

    private String contentType;

    private String ahcsLv3Code;

    private String channelSource;

    private String leaderFileId;

    private List<String> followFileIdList;

    /**
     * cos文件下载真实地址过期时间
     */
    private Date expireTime;

    private String flowName;
    private String smallName;
    private String[] fileUrls;

}
