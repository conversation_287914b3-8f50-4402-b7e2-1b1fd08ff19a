package com.paic.ncbs.claim.model.dto.pay;

import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.model.vo.pay.BatchPaymentDetailVO;

import java.util.List;

/**
 * 批量支付明细返回
 */
public class BatchPaymentDetailResult {

    /**
     * 支付列表明细
     */
    private List<BatchPaymentDetailVO> batchPaymentDetailList;

    private Pager pager;

    public List<BatchPaymentDetailVO> getBatchPaymentDetailList() {
        return batchPaymentDetailList;
    }

    public void setBatchPaymentDetailList(List<BatchPaymentDetailVO> batchPaymentDetailList) {
        this.batchPaymentDetailList = batchPaymentDetailList;
    }

    public Pager getPager() {
        return pager;
    }

    public void setPager(Pager pager) {
        this.pager = pager;
    }

}
