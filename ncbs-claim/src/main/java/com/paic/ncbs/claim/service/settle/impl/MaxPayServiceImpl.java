package com.paic.ncbs.claim.service.settle.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity;
import com.paic.ncbs.claim.dao.entity.duty.ReportDutyVo;
import com.paic.ncbs.claim.dao.entity.duty.ReportPlanDutyVo;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.service.common.ResidueAmountService;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyPayService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Slf4j
@Service("MaxPayService")
public class MaxPayServiceImpl implements MaxPayService {

    @Autowired
    private DutyDetailPayMapper dutyDetailPayInfoDao;
    @Autowired
    private DutyPayMapper dutyPayMapper;

    @Autowired
    private ResidueAmountService residueAmountService;
    @Autowired
    private RiskPropertyPayService riskPropertyPayService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private GlobalPolicyService globalPolicyService;
    @Autowired
    private ReportInfoMapper reportInfoMapper;

    @Override
    public void initPoliciesPayMaxPay(List<PolicyPayDTO> policyPays, String scene) {
        if (ListUtils.isEmptyList(policyPays)) {
            return;
        }
        if(riskPropertyService.displayRiskProperty(policyPays.get(0).getReportNo(),null)){
            LogUtil.info("责任险最大给付额");
            // 雇责不用计算剩余保额
            for (PolicyPayDTO policy : policyPays) {
                for (PlanPayDTO plan : policy.getPlanPayArr()) {
                    for (DutyPayDTO duty : plan.getDutyPayArr()) {
                        duty.setMaxAmountPay(duty.getBaseAmountPay());
                        for (DutyDetailPayDTO dutyDetail : duty.getDutyDetailPayArr()) {
                            dutyDetail.setMaxAmountPay(dutyDetail.getBaseAmountPay());
                        }
                    }
                }
            }
            if (ListUtils.isEmptyList(policyPays.get(0).getRiskGroupList()) && !policyPays.get(0).isRollback()) {
                riskPropertyService.setRiskGroup(policyPays);
            }

            /*if(ListUtils.isEmptyList(policyPays.get(0).getRiskGroupList())){
                riskPropertyService.setRiskProperty(policyPays);
            }
            riskPropertyPayService.initRiskPropertyMaxPay(policyPays);*/
            return;
        }

        for (PolicyPayDTO policy : policyPays) {
            String reportNo = policy.getReportNo();
            String policyNo = policy.getPolicyNo();
            Map<String,BigDecimal> hisPayMap = new HashMap<>();
            if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                //抄单表查询 抄单要用的入参
                CopyPolicyQueryVO copyPolicyQueryVO = reportInfoMapper.getCopyReqVo(policyNo,reportNo);
                hisPayMap = globalPolicyService.getHisPayAmount(copyPolicyQueryVO);
            }

            for (PlanPayDTO plan : policy.getPlanPayArr()) {
                String planCode = plan.getPlanCode();
                List<DutyPayDTO> dutyPayArr = plan.getDutyPayArr();
                Boolean isShareAmount = dutyPayArr.stream().anyMatch(DutyPayDTO::getIsShareAmount);
                for (DutyPayDTO duty : dutyPayArr) {
                    String dutyCode = duty.getDutyCode();
                    //查询责任历史赔付金额
                    BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay(scene, reportNo, policyNo, planCode,
                            dutyCode, duty.getIsDutyShareAmount(), duty.getShareDutyGroup(), isShareAmount);
                    HistoryPayInfoDTO historyPayInfo = new HistoryPayInfoDTO();
                    historyPayInfo.setPolicyNo(policyNo);
                    historyPayInfo.setPlanCode(planCode);
                    historyPayInfo.setDutyCode(dutyCode);
                    historyPayInfo.setDutyBaseAmount(duty.getBaseAmountPay());
                    historyPayInfo.setDutyHistoryPay(dutyHistoryPay);
                    //global保单的历史赔付信息通过抄单实时获取最新的，不走新非车理赔计算逻辑
                    if(ObjectUtil.isNotEmpty(hisPayMap)){
                        historyPayInfo.setDutyHistoryPay(ObjectUtil.isNotEmpty(hisPayMap.get(dutyCode)) ? hisPayMap.get(dutyCode) : BigDecimal.ZERO);
                    }

                    BigDecimal dutyMaxPay = residueAmountService.getDutyMaxPay(historyPayInfo);
                    duty.setMaxAmountPay(dutyMaxPay);

                    // 如果责任有赔偿限额，计算责任剩余赔偿限额
                    BigDecimal payLimit = getPayLimit(duty.getAttributes());
                    BigDecimal dutyMaxPayLimit = getDutyMaxPayLimit(payLimit, historyPayInfo);
                    duty.setPayLimit(dutyMaxPayLimit);

                    for (DutyDetailPayDTO dutyDetail : duty.getDutyDetailPayArr()) {
                        String dutyDetailCode = dutyDetail.getDutyDetailCode();

                        //查询责任明细历史赔付金额
                        BigDecimal dutyDetailHistoryPay = residueAmountService.getDutyDetailHistoryPay(scene, reportNo, policyNo,
                                planCode, dutyCode, dutyDetailCode, isShareAmount);
                        historyPayInfo.setDutyDetailCode(dutyDetailCode);
                        historyPayInfo.setDutyDetailBaseAmount(dutyDetail.getBaseAmountPay());
                        historyPayInfo.setDutyDetailHistoryPay(dutyDetailHistoryPay);
                        BigDecimal dutyDetailMaxPay =  residueAmountService.getDutyDetailMaxPay(historyPayInfo);
                        dutyDetail.setDutyMaxPay(dutyMaxPay);

                        //责任明细剩余理赔金额不能大于责任剩余理赔金额
                        if (dutyMaxPay.compareTo(dutyDetailMaxPay) < 0) {
                            dutyDetail.setMaxAmountPay(dutyMaxPay);
                        } else {
                            dutyDetail.setMaxAmountPay(dutyDetailMaxPay);
                        }

                        if(Objects.nonNull(dutyDetail.getPayLimitType())){
                            log.info("赔偿限额类型为={}",dutyDetail.getPayLimitType());
                        }else{
                            // 如果责任有赔偿限额，计算责任明细剩余赔偿限额
                            historyPayInfo.setDetailLimitAmount(dutyDetail.getDetailLimitAmount());
                            BigDecimal dutyDetailMaxPayLimit = getDutyDetailMaxPayLimit(payLimit, historyPayInfo);
                            dutyDetail.setPayLimit(dutyDetailMaxPayLimit);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void initEstPoliciesPayMaxPay(List<EstimatePolicyDTO> estimatePolicyList, String scene) {
        if(riskPropertyService.displayRiskProperty(estimatePolicyList.get(0).getReportNo(),null)){
            LogUtil.info("责任险立案最大给付额");
            // 雇责不用计算剩余保额
            for (EstimatePolicyDTO policy : estimatePolicyList) {
                for (EstimatePlanDTO plan : policy.getEstimatePlanList()) {
                    for (EstimateDutyRecordDTO duty : plan.getEstimateDutyRecordList()) {
                        duty.setDutyMaxPay(duty.getBaseAmountPay());
                    }
                }
            }
            if (!estimatePolicyList.get(0).isRollback()) {
                riskPropertyService.setEstimateRiskGroup(estimatePolicyList);
            }

//            riskPropertyService.setEstRiskProperty(estimatePolicyList,null);
//            riskPropertyPayService.initEstRiskPropertyMaxPay(estimatePolicyList);
//            if(estimatePolicyList.get(0).isRollback()){
//                riskPropertyService.getEstRiskPropertyPlan(estimatePolicyList);
//            }
            return;
        }
        for (EstimatePolicyDTO policy : estimatePolicyList) {
            String reportNo = policy.getReportNo();
            String policyNo = policy.getPolicyNo();
            Map<String,BigDecimal> hisPayMap = new HashMap<>();
            if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                //抄单表查询 抄单要用的入参
                CopyPolicyQueryVO copyPolicyQueryVO = reportInfoMapper.getCopyReqVo(policyNo,reportNo);
                hisPayMap = globalPolicyService.getHisPayAmount(copyPolicyQueryVO);
            }

            for (EstimatePlanDTO plan : policy.getEstimatePlanList()) {
                String planCode = plan.getPlanCode();
                List<EstimateDutyRecordDTO> estimateDutyRecordList = plan.getEstimateDutyRecordList();
                // 这里感觉有点问题感觉应该到循环里分开取EstimateDutyRecordDTO::getIsShareAmount 不是有一个是true就全部共享。
                Boolean isShareAmount = estimateDutyRecordList.stream().anyMatch(EstimateDutyRecordDTO::getIsShareAmount);
                for (EstimateDutyRecordDTO duty : estimateDutyRecordList) {
                    String dutyCode = duty.getDutyCode();

                    //查询责任历史赔付金额
                    BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay(scene, reportNo, policyNo, planCode,
                            dutyCode, duty.getIsDutyShareAmount(), duty.getShareDutyGroup(), isShareAmount);
                    HistoryPayInfoDTO historyPayInfo = new HistoryPayInfoDTO();
                    historyPayInfo.setPolicyNo(policyNo);
                    historyPayInfo.setPlanCode(planCode);
                    historyPayInfo.setDutyCode(dutyCode);
                    historyPayInfo.setDutyBaseAmount(duty.getBaseAmountPay());
                    historyPayInfo.setDutyHistoryPay(dutyHistoryPay);
                    //global保单的历史赔付信息通过抄单实时获取最新的，不走新非车理赔计算逻辑
                    if(ObjectUtil.isNotEmpty(hisPayMap)){
                        historyPayInfo.setDutyHistoryPay(ObjectUtil.isNotEmpty(hisPayMap.get(dutyCode)) ? hisPayMap.get(dutyCode) : BigDecimal.ZERO);
                    }

                    BigDecimal dutyMaxPay = residueAmountService.getDutyMaxPay(historyPayInfo);
                    duty.setDutyMaxPay(dutyMaxPay);
                }
            }
        }
    }

    /**
     * 查询责任赔偿限额
     *
     * @param attributes
     * @return
     */
    private BigDecimal getPayLimit(List<DutyAttributeDTO> attributes) {
        if (CollectionUtils.isEmpty(attributes)) {
            return null;
        }
        DutyAttributeDTO attribute = attributes.stream().filter(i -> BaseConstant.STRING_6.equals(i.getAttrCode())).findFirst().orElse(null);
        if (Objects.isNull(attribute) || StringUtils.isBlank(attribute.getAttrValue())) {
            return null;
        }

        try {
            return new BigDecimal(attribute.getAttrValue());
        } catch (Exception e) {
            log.error("MaxPayServiceImpl.getPayLimit payLimit data error, payLimit={}", attribute.getAttrValue(), e);
            return null;
        }
    }

    /**
     * 计算责任剩余赔偿限额
     *
     * @param payLimit       责任赔偿限额
     * @param dutyHistoryPay
     * @return
     */
    private BigDecimal getDutyMaxPayLimit(BigDecimal payLimit, HistoryPayInfoDTO dutyHistoryPay) {
        if (Objects.isNull(payLimit)) {
            return null;
        }

        try {
            BigDecimal historyPaySum = dutyHistoryPay.getDutyHistoryPay() == null ? BigDecimal.ZERO : dutyHistoryPay.getDutyHistoryPay();
            BigDecimal dutyMaxPayLimit = payLimit.subtract(historyPaySum);
            if (dutyMaxPayLimit.compareTo(BigDecimal.ZERO) < 0) {
                log.info("历史责任已赔付额大于责任赔偿限额, payLimit={}, dutyHistoryPay={}", payLimit, JSON.toJSONString(dutyHistoryPay));
                dutyMaxPayLimit = BigDecimal.ZERO;
            }
            return dutyMaxPayLimit;
        } catch (Exception e) {
            log.error("MaxPayServiceImpl.getDutyMaxPayLimit error, payLimit={}, dutyHistoryPay={}", payLimit, JSON.toJSONString(dutyHistoryPay), e);
            return null;
        }
    }

    /**
     * 如果责任有赔偿限额，计算责任明细剩余赔偿限额
     *
     * @param payLimit       责任赔偿限额
     * @param dutyHistoryPay
     * @return
     */
    private BigDecimal getDutyDetailMaxPayLimit(BigDecimal payLimit, HistoryPayInfoDTO dutyHistoryPay) {
        if (Objects.isNull(payLimit)) {
            return null;
        }

        try {
            // 若有限额比例，则用责任赔偿限额乘以限额比例
            BigDecimal detailLimitAmount = dutyHistoryPay.getDetailLimitAmount();
            BigDecimal detailPayLimit = Objects.isNull(detailLimitAmount) ? payLimit : payLimit.multiply(detailLimitAmount);

            BigDecimal dutyDetailHistoryPay = dutyHistoryPay.getDutyDetailHistoryPay() == null ? BigDecimal.ZERO : dutyHistoryPay.getDutyDetailHistoryPay();
            BigDecimal dutyDetailMaxPayLimit = detailPayLimit.subtract(dutyDetailHistoryPay);
            if (dutyDetailMaxPayLimit.compareTo(BigDecimal.ZERO) < 0) {
                log.info("历史责任明细已赔付额大于责任明细赔偿限额, payLimit={}, dutyHistoryPay={}", payLimit, JSON.toJSONString(dutyHistoryPay));
                dutyDetailMaxPayLimit = BigDecimal.ZERO;
            }
            return dutyDetailMaxPayLimit;
        } catch (Exception e) {
            log.error("MaxPayServiceImpl.getDetailPayLimit error, payLimit={}, dutyHistoryPay={}", payLimit, JSON.toJSONString(dutyHistoryPay), e);
            return null;
        }
    }

    @Override
    public Map<String, Long> getDutyPayNum(String policyNo, String reportNo) {
        List<DutyPayNumDTO> result = dutyPayMapper.getDutyPayNum(policyNo, reportNo);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(result)) {
            return Collections.EMPTY_MAP;
        }
//        List<DutyPayNumDTO> dutyPayNum1List = result.stream().filter(item-> item.getCaseTimes() ==1).collect(Collectors.toList());
//        List<DutyPayNumDTO> dutyPayNum2List = result.stream().filter(item-> item.getCaseTimes() ==2).collect(Collectors.toList());
//        List<String> dutyPlanKeys = dutyPayNum1List.stream().map(item -> item.getKey()).collect(Collectors.toList());
//        for(DutyPayNumDTO dutyPayNum:dutyPayNum2List){
//            if(dutyPlanKeys.contains(dutyPayNum.getKey())){
//                continue;
//            }
//            dutyPayNum1List.add(dutyPayNum);
//            dutyPlanKeys.add(dutyPayNum.getKey());
//        }
        return result.stream().collect(Collectors.groupingBy(DutyPayNumDTO::getKey, Collectors.counting()));
    }

    /**
     * 查询前一次责任赔付金额
     * @param estimatePolicyList
     */
    public void initEstClaimCasePayMaxPay(List<EstimatePolicyDTO> estimatePolicyList) {
        for (EstimatePolicyDTO policy : estimatePolicyList) {
            String caseNo = policy.getCaseNo();
            String policyNo = policy.getPolicyNo();
            Integer caseTimes  = policy.getCaseTimes();
            for (EstimatePlanDTO plan : policy.getEstimatePlanList()) {
                String planCode = plan.getPlanCode();
                List<EstimateDutyRecordDTO> estimateDutyRecordList = plan.getEstimateDutyRecordList();
                for (EstimateDutyRecordDTO duty : estimateDutyRecordList) {
                    duty.setLastPayAmount(residueAmountService.getNowCaseDutyPrePay(caseNo, policyNo, planCode,duty.getDutyCode(), caseTimes));
                }
            }
        }
    }

    @Override
    public void initPoliciesPayMaxPayToPersonTrace(List<ReportPlanDutyVo> reportPlanDutyVoList, String scene) {
        if (ListUtils.isEmptyList(reportPlanDutyVoList)) {
            return;
        }
        if(riskPropertyService.displayRiskProperty(reportPlanDutyVoList.get(0).getReportNo(),null)){
            LogUtil.info("责任险最大给付额");
            return;
        }
        for (ReportPlanDutyVo plan : reportPlanDutyVoList) {
            String reportNo = plan.getReportNo();
            String policyNo = plan.getPolicyNo();
            String planCode = plan.getPlanCode();
            List<ReportDutyVo> dutyPayArr = plan.getReportDutyVoList();
            dutyPayArr.stream().forEach(duty -> {
                    duty.setDutyShareAmount("1".equals(duty.getIsDutySharedAmount()));
            });
            Boolean isShareAmount = dutyPayArr.stream().anyMatch(ReportDutyVo::getDutyShareAmount);

            for (ReportDutyVo duty : dutyPayArr) {
                String dutyCode = duty.getDutyCode();
                //查询责任历史赔付金额
                BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay(scene, reportNo, policyNo, planCode,
                        dutyCode, duty.getDutyShareAmount(), duty.getDutySharedAmountMerge(), isShareAmount);
                HistoryPayInfoDTO historyPayInfo = new HistoryPayInfoDTO();
                historyPayInfo.setPolicyNo(policyNo);
                historyPayInfo.setPlanCode(planCode);
                historyPayInfo.setDutyCode(dutyCode);
                historyPayInfo.setDutyBaseAmount(duty.getDutyAmount());
                historyPayInfo.setDutyHistoryPay(dutyHistoryPay);

                BigDecimal dutyMaxPay = residueAmountService.getDutyMaxPay(historyPayInfo);
                duty.setDutyAmount(dutyMaxPay);


//                for (AhcsPolicyDutyDetailEntity dutyDetail : duty.getAhcsPolicyDutyDetail()) {
//                    String dutyDetailCode = dutyDetail.getDutyDetailCode();
//
//                    //查询责任明细历史赔付金额
//                    BigDecimal dutyDetailHistoryPay = residueAmountService.getDutyDetailHistoryPay(scene, reportNo, policyNo,
//                            planCode, dutyCode, dutyDetailCode, isShareAmount);
//                    historyPayInfo.setDutyDetailCode(dutyDetailCode);
//                    historyPayInfo.setDutyDetailBaseAmount(dutyDetail.getDutyAmount());
//                    historyPayInfo.setDutyDetailHistoryPay(dutyDetailHistoryPay);
//                    BigDecimal dutyDetailMaxPay =  residueAmountService.getDutyDetailMaxPay(historyPayInfo);
//                    dutyDetail.setDutyAmount(dutyDetailMaxPay);
//
//                    //责任明细剩余理赔金额不能大于责任剩余理赔金额
//                    if (dutyMaxPay.compareTo(dutyDetailMaxPay) < 0) {
//                        dutyDetail.setDutyAmount(dutyMaxPay);
//                    } else {
//                        dutyDetail.setDutyAmount(dutyDetailMaxPay);
//                    }
//
//                }
            }
        }

    }
}
