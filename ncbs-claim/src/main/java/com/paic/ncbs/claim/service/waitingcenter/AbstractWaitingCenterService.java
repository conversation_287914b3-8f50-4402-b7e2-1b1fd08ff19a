package com.paic.ncbs.claim.service.waitingcenter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.feign.WaitingCenterFeign;
import com.paic.ncbs.claim.model.dto.waitingcenter.SummaryDto;
import com.paic.ncbs.claim.model.dto.waitingcenter.WaitingCenterReqDto;
import com.paic.ncbs.claim.model.dto.waitingcenter.WaitingCenterResDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 抽象办事中心服务类
 * @author: justinwu
 * @create 2025/5/21 17:24
 */
@Slf4j
public abstract class AbstractWaitingCenterService implements WaitingCenterService{
    @Autowired
    private WaitingCenterFeign waitingCenterFeign;

    /**
     * 基于业务创建办事中心审批
     * @param bussinessId
     */
    public final WaitingCenterResDto createBussinessInstance(String bussinessId) {
        // step1 构建业务办事中心请求
        WaitingCenterReqDto waitingCenterReqDto = this.buildWaitingCenterReq(bussinessId);
        // step2 调用办事中心创建实例
        WaitingCenterResDto waitingCenterResDto = this.createWaitingCenterInstance(waitingCenterReqDto);
        return waitingCenterResDto;
    }

    /**
     * 通用创建办事中心审批
     * @param waitingCenterReqDto
     * @return
     */
    private WaitingCenterResDto createWaitingCenterInstance(WaitingCenterReqDto waitingCenterReqDto) {
        String bussinessId = waitingCenterReqDto.getBussinessId();
        String body = JSON.toJSONString(waitingCenterReqDto);
        WaitingCenterResDto resDto = new WaitingCenterResDto();
        resDto.setCode(0);
        try {
            log.info("{}-办事中心-接口请求-开始，入参：{}", bussinessId, body);
            String result = waitingCenterFeign.createInstance(body);
            log.info("{}-办事中心-接口请求-结束：出参：{}", bussinessId, result);
            if(StrUtil.isBlank(result)) {
                resDto.setMessage(bussinessId+"请求办事中心出参为空");
                log.error("{}-办事中心-接口请求-结束：出参为空", bussinessId);
            }
        }catch (Exception e) {
            log.error("{}-办事中心-接口请求-结束：异常", bussinessId, e);
            resDto.setMessage(bussinessId+"请求办事中心异常"+e.getMessage());
        }
        return resDto;
    }

    /**
    * 抽象构建业务办事中心请求
     */
    protected abstract WaitingCenterReqDto buildWaitingCenterReq(String bussinessId);

    /**
     * 添加summary
     * @param list  数据集合
     * @param key   属性名
     * @param value 属性值
     * @param style 样式
     */
    protected void addSummary(List<SummaryDto> list, String key, String value, String style) {
        list.add(new SummaryDto(key, value, style));
    }


}
