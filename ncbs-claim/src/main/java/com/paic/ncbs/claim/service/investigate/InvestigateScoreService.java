package com.paic.ncbs.claim.service.investigate;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateScoreDTO;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;


public interface InvestigateScoreService {

	String getInvestigateScore(InvestigateScoreDTO score);
	List<InvestigateScoreDTO> listInvestigateScore(@PathVariable("idAhcsInvestigate") String idAhcsInvestigate,
												   @PathVariable("queryType") String queryType) throws GlobalBusinessException;


    InvestigateScoreDTO listScore(@PathVariable("idAhcsInvestigate") String idAhcsInvestigate);

	void saveInvestigateScore(@RequestBody InvestigateScoreDTO investigateScoreDTO) throws GlobalBusinessException ;

}