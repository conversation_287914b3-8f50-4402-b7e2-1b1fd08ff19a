package com.paic.ncbs.claim.service.supplements.impl;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;
import com.paic.ncbs.claim.service.supplements.BatchDealCustomerSMTaskService;
import com.paic.ncbs.claim.service.supplements.SupplementsMaterialService;
import com.paic.ncbs.claim.service.fileupload.DocumentTypeService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户补材业务处理
 */
@Service
public class BatchDealCustomerSMTaskServiceImpl implements BatchDealCustomerSMTaskService {

    @Autowired
    private SupplementsMaterialService supplementsMaterialService;

    @Autowired
    private DocumentTypeService documentTypeService;

    /**
     * 批量更新超期未处理的补材任务
     * @param confDays
     */
    @Override
    public void batchDealServiceData(Integer confDays) {
        //获取到补材任务记录表clms_supplements_material_task中状态为00-补材中的数据
        List<SupplementsMaterialDTO> dtos = supplementsMaterialService.getServiceData(confDays);
        LogUtil.info("超期未处理的客户补材任务有={}", JsonUtils.toJsonString(dtos));
        for (SupplementsMaterialDTO dto : dtos) {
            try{
                documentTypeService.updateServiceData(dto);
                LogUtil.info("案件号={},客户补材回销成功",dto.getReportNo());
            }catch (Exception e){
               LogUtil.info("案件号={},回销异常信息={},异常原因={}",dto.getReportNo(),e.getMessage(),e.getCause());
            }

        }
    }
}
