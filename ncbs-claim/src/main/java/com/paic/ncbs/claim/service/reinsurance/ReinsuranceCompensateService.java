package com.paic.ncbs.claim.service.reinsurance;

import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;

import java.util.List;

/**
 * 再保补推服务
 */
public interface ReinsuranceCompensateService {

    /**
     * 再保补推
     * @param dtos 需要补推再保的数据
     */
    void reinsuranceCompensate(List<RepayCalDTO> dtos);

    /**
     * 再保补推根据理赔送再保的业务环节
     * @param dtos 需要补推再保的数据
     */
    void compensateOnBusinessLink(List<RepayCalDTO> dtos);
}
