package com.paic.ncbs.claim.service.mqcompensation.impl;

import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.dao.entity.mq.MqMessageRecordEntity;
import com.paic.ncbs.claim.dao.mapper.mqcompensation.MqCompensationMapper;
import com.paic.ncbs.claim.service.mqcompensation.MqCompensationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-06-12 9:57
 */
@Service
public class MqCompensationServiceImpl implements MqCompensationService {

    @Autowired
    private MqCompensationMapper mqCompensationMapper;

    @Override
    public List<MqMessageRecordEntity> getFailureRecordList() {
        return mqCompensationMapper.getFailureRecordList();
    }

    @Override
    public void updateMqSendStatus(MqMessageRecordEntity mqMessageRecordEntity, boolean isSuccess) {
        Integer resendCount = mqMessageRecordEntity.getResendCount() + 1;
        mqMessageRecordEntity.setResendCount(resendCount);
        mqMessageRecordEntity.setUpdatedBy(WebServletContext.getUserId());
        if(isSuccess){
            mqMessageRecordEntity.setSendStatus(BaseConstant.INT_1);
            mqCompensationMapper.updateMqSendStatus(mqMessageRecordEntity);
        }else{
            mqMessageRecordEntity.setSendStatus(BaseConstant.INT_2);
            mqCompensationMapper.updateMqSendStatus(mqMessageRecordEntity);
        }
    }

    @Override
    public void addMqCompensation(String topic, String messageBody, Integer resendCount) {
        MqMessageRecordEntity entity = new MqMessageRecordEntity();
        entity.setCreatedBy(BaseConstant.SYSTEM);
        entity.setUpdatedBy(BaseConstant.SYSTEM);
        entity.setTopic(topic);
        entity.setMessageBody(messageBody);
        entity.setSystemCode("claim");
        entity.setResendCount(resendCount);
        entity.setSendStatus(BaseConstant.INT_2);
        mqCompensationMapper.addEntity(entity);
    }
}
