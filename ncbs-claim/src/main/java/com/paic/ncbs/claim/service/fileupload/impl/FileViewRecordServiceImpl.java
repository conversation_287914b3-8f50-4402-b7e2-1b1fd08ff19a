package com.paic.ncbs.claim.service.fileupload.impl;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.fileupload.FileViewRecordService;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileViewRecordMapper;
import com.paic.ncbs.claim.model.dto.fileupload.FileViewRecordDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("fileViewRecordService")
public class FileViewRecordServiceImpl implements FileViewRecordService {
	

	@Autowired
    FileViewRecordMapper fileViewRecordDao;


	@Override
	public Integer getFileViewRecord(FileViewRecordDTO fileViewRecordDTO) throws GlobalBusinessException {
		
		return fileViewRecordDao.getFileViewRecord(fileViewRecordDTO);
	}


	@Override
	public void addFileViewRecord(FileViewRecordDTO fileViewRecordDTO) throws GlobalBusinessException {
		String [] documentGroupItemsIdArr = fileViewRecordDTO.getDocumentItemsIdArr();
		String userId = fileViewRecordDTO.getUserId();
		FileViewRecordDTO fileViewRecordDto= null;
		for(int i=0;i<documentGroupItemsIdArr.length;i++){
			fileViewRecordDto = new FileViewRecordDTO(userId, documentGroupItemsIdArr[i],userId,userId);

			fileViewRecordDao.addFileViewRecord(fileViewRecordDto);
		}
	}

}
