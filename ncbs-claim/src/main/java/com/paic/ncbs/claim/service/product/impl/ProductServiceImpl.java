package com.paic.ncbs.claim.service.product.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.base.constant.Constants;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentPlanMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.product.ProductMapper;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.pay.ProductPlanInfo;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.service.product.ProductService;
import com.paic.ncbs.policy.dto.PlanDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service("productService")
@Transactional
@Slf4j
@RefreshScope
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private ClmsPaymentPlanMapper paymentPlanMapper;

    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;

    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private AhcsPolicyInfoMapper policyInfoMapper;

    @Override
    public BigDecimal selectTaxRateByPlanCode(String productClassCode) {
        BigDecimal taxRate = new BigDecimal(0);
        List<BigDecimal> taxRateList = productMapper.selectTaxRateByProductClass(productClassCode);
        if(CollectionUtil.isNotEmpty(taxRateList)) {
            return taxRateList.get(0);
        }
        return taxRate;
    }

    public Map<String, Boolean> getPlanTaxRate(String paySerialNo) {
        //查询保单号
        PaymentItemDTO itemDTO = new PaymentItemDTO();
        itemDTO.setIdClmPaymentItem(paySerialNo);
        List<PaymentItemDTO> PaymentItemDTO = paymentItemMapper.getAllPaymentItem(itemDTO);
        String policyNo = PaymentItemDTO.get(0).getPolicyNo();
        String reportNo = PaymentItemDTO.get(0).getReportNo();
        PolicyInfoDTO policyInfoDTO = policyInfoMapper.getPolicyBaseInfo(reportNo);
        PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(policyNo,reportNo);
        // k:险种code v:true有税，false:免税
        Map<String, Boolean> map = new HashMap<>();
        if("Global".equals(policyInfoDTO.getDataSource())) {
            PlanDTO planDTO = policyInfoMapper.selectTaxRate(reportNo, paySerialNo);
            BigDecimal taxRate = BigDecimal.valueOf(planDTO.getTaxRate());
            if (taxRate.compareTo(new BigDecimal(0)) > 0) {
                map.put(planDTO.getPlanCode(), true);
            } else {
                map.put(planDTO.getPlanCode(), false);
            }
        }else{
            //查询保单起止时间
            List<ProductPlanInfo> productClassList = paymentPlanMapper.getProductClass(paySerialNo);
            if(CollectionUtil.isEmpty(productClassList)) {
                return map;
            }
            productClassList.stream().forEach(productPlanInfo -> {
                BigDecimal taxRate = selectTaxRateByPlanCode(productPlanInfo.getProductClass());
                //健康险一年期免税特殊处理
                if (Constants.PRODUCT_CLASS_HEATH.equals(productPlanInfo.getProductClass())) {
                    if (DateUtils.daysOfTwoDate(policyDto.getPolicyEndDate(), policyDto.getPolicyStartDate()) < 365) {
                        map.put(productPlanInfo.getPlanCode(),true);
                    }else {
                        map.put(productPlanInfo.getPlanCode(),false);
                    }
                }else {
                    if (taxRate.compareTo(new BigDecimal(0)) > 0) {
                        map.put(productPlanInfo.getPlanCode(), true);
                    } else {
                        map.put(productPlanInfo.getPlanCode(), false);
                    }
                }
            });
        }
        return map;
    }
}
