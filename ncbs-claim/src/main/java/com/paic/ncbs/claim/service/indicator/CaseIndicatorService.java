package com.paic.ncbs.claim.service.indicator;

import com.paic.ncbs.claim.model.dto.indicator.CaseIndicatorDTO;

import java.text.ParseException;

/**
 * @author: justinwu
 * @create 2025/3/7 10:18
 */
public interface CaseIndicatorService {
    /**
     * 结案时效计算-未结案
     * @param caseIndicatorDTO 查询参数
     * @throws ParseException
     */
    public void settleDeadlineForUnsettle(CaseIndicatorDTO caseIndicatorDTO) throws ParseException;

    /**
     * 结案时效计算-结案
     * @param caseIndicatorDTO 查询参数
     * @throws ParseException
     */
    public void settleDeadlineForSettle(CaseIndicatorDTO caseIndicatorDTO) throws ParseException;

    /**
     * 立案时效计算-未立案
     * @param caseIndicatorDTO 查询参数
     * @throws ParseException
     */
    public void regDeadlineForUnReg(CaseIndicatorDTO caseIndicatorDTO) throws ParseException;

    /**
     * 立案时效计算-立案
     * @param caseIndicatorDTO 查询参数
     * @throws ParseException
     */
    public void regDeadlineForReg(CaseIndicatorDTO caseIndicatorDTO) throws ParseException;

    /**
     * 重开结案时效计算
     * @param caseIndicatorDTO 查询参数
     * @throws ParseException
     */
    public void reopenDeadline(CaseIndicatorDTO caseIndicatorDTO) throws ParseException;

    /**
     * 计算立案时效
     * @param reportNo
     * @param caseTimes
     */
    public void calCaseRegIndicator(String reportNo,Integer caseTimes);

    /**
     * 计算结案时效
     * @param reportNo
     * @param caseTimes
     */
    public void calCaseSettleIndicator(String reportNo,Integer caseTimes);
}
