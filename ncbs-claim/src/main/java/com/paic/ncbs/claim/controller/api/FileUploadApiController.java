package com.paic.ncbs.claim.controller.api;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.api.DocumentListDTO;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentReqDTO;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentResDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Api(tags = "（对外）上传单证文件")
@RestController
@RequestMapping("/public/doc/fileUploadApiController")
public class FileUploadApiController extends BaseController {

    @Autowired
    private FileUploadService fileUploadService;

    private static final List<String> DOCUMENT_FILE_TYPE_LIST = new ArrayList<>();

    static {
        DOCUMENT_FILE_TYPE_LIST.add("bmp");
        DOCUMENT_FILE_TYPE_LIST.add("jpg");
        DOCUMENT_FILE_TYPE_LIST.add("jpeg");
        DOCUMENT_FILE_TYPE_LIST.add("png");
        DOCUMENT_FILE_TYPE_LIST.add("pdf");
        DOCUMENT_FILE_TYPE_LIST.add("mp3");
        DOCUMENT_FILE_TYPE_LIST.add("wma");
        DOCUMENT_FILE_TYPE_LIST.add("wav");
        DOCUMENT_FILE_TYPE_LIST.add("gif");
        DOCUMENT_FILE_TYPE_LIST.add("mp4");
        DOCUMENT_FILE_TYPE_LIST.add("avi");
        DOCUMENT_FILE_TYPE_LIST.add("mov");
        DOCUMENT_FILE_TYPE_LIST.add("wmv");
        DOCUMENT_FILE_TYPE_LIST.add("mkv");
//        DOCUMENT_FILE_TYPE_LIST.add("flv");
        DOCUMENT_FILE_TYPE_LIST.add("webm");
        DOCUMENT_FILE_TYPE_LIST.add("midi");

        DOCUMENT_FILE_TYPE_LIST.add("zip");
//        DOCUMENT_FILE_TYPE_LIST.add("rar");
//        DOCUMENT_FILE_TYPE_LIST.add("7z");
        DOCUMENT_FILE_TYPE_LIST.add("tar");
        DOCUMENT_FILE_TYPE_LIST.add("gz");

        DOCUMENT_FILE_TYPE_LIST.add("xls");
        DOCUMENT_FILE_TYPE_LIST.add("xlsx");
        DOCUMENT_FILE_TYPE_LIST.add("eml");

        DOCUMENT_FILE_TYPE_LIST.add("tiff");
    }

    @ApiOperation("（对外）上传单证")
    @RequestMapping(value = "/uploadDocument", method = RequestMethod.POST)
    public ResponseResult uploadDocument(@RequestBody UploadDocumentReqDTO uploadDocumentReqDTO) {
        LogUtil.audit("#对外上传单证#入参：uploadDocumentReqDTO=" + JSONObject.toJSONString(uploadDocumentReqDTO));
        // 1、校验入参
        String returnMsg = this.validUploadDocumentParams(uploadDocumentReqDTO);
        if (StringUtils.isNotEmpty(returnMsg)) {
            return ResponseResult.fail(FileUploadConstants.FAIL_CODE, returnMsg);
        }

        // 2、（对外）上传单证
        UploadDocumentResDTO fileInfo = fileUploadService.addFilesInfo(uploadDocumentReqDTO);
        LogUtil.audit("单证上传成功 fileInfo信息为" + JSONObject.toJSONString(fileInfo));
        return ResponseResult.success(fileInfo);
    }


    private String getSeek(FileInfoDTO dto){
        return "_" + dto.getRecordNo() + ".";
    }

    /**
     * 参数校验
     * @param fileInfoDTO
     * @return
     */
    private String validUploadDocumentParams(UploadDocumentReqDTO fileInfoDTO) {
        String returnMsg = "";
        String taskCode = fileInfoDTO.getTaskCode();
        if (StringUtils.isEmptyStr(fileInfoDTO.getReportNo())) {
            returnMsg = "报案号不能为空!";
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getCaseTimes())) {
            returnMsg = "赔付次数不能为空!";
        } else if (StringUtils.isEmptyStr(taskCode)) {
            returnMsg = "环节号不能为空!";
        }
        if (CollectionUtils.isEmpty(fileInfoDTO.getDocumentList())) {
            returnMsg = "单证集合不能为空!";
        } else {
            for (DocumentListDTO documentListDTO : fileInfoDTO.getDocumentList()) {
                if (StringUtils.isEmptyStr(documentListDTO.getSmallTypeCode())) {
                    returnMsg = "细类代码不能为空!";
                } else if (StringUtils.isEmptyStr(documentListDTO.getFileUrl())) {
                    returnMsg = "下载地址url!";
                } else if (StringUtils.isEmptyStr(documentListDTO.getFileType())) {
                    returnMsg = "文件类型不能为空!";
                } else if (StringUtils.isEmptyStr(documentListDTO.getFileName())) {
                    returnMsg = "文件名称不能为空!";
                } else if (StringUtils.isEmptyStr(documentListDTO.getFileSize())) {
                    returnMsg = "文件大小不能为空!";
                }
                if (StringUtils.isNotEmpty(documentListDTO.getFileType())){
                    String fileType = documentListDTO.getFileType().toLowerCase();
                    if (!DOCUMENT_FILE_TYPE_LIST.contains(fileType)) {
                        LogUtil.audit("上传的单证文件格式不正确! fileType=" + fileType);
                        returnMsg = "上传的单证文件格式不正确!";
                    }
                }
                try {
                    if (StringUtils.isNotEmpty(documentListDTO.getFileName())) {
                        if (documentListDTO.getFileName().getBytes("UTF-8").length > 200) {
                            LogUtil.audit("文件名称过长");
                            returnMsg = "文件名称过长!";
                        }
                    }
                } catch (UnsupportedEncodingException e) {
                    LogUtil.error("文件名称转UTF-8格式失败 ", e);
                    returnMsg = "文件名称转UTF-8格式失败 !";
                }

            }
        }
        return returnMsg;
    }

//    @ApiOperation("查询单证信息列表（对外）")
//    @RequestMapping(value ="/queryDocumentInfo", method = RequestMethod.POST)
//    public ResponseResult<List<FileDocumentDTO>> queryDocumentInfo(@RequestBody QueryDocumentInfoDTO queryDocumentInfoDto) {
//        LogUtil.audit("#对外查询单证信息列表#入参：queryDocumentInfoDto=" + JSONObject.toJSONString(queryDocumentInfoDto));
//        //入参校验
//        this.validQueryDocumentInfoParams(queryDocumentInfoDto);
//        //查询单证清单
//        List<FileDocumentDTO> documentListDTOList = fileUploadService.queryDocumentInfo(queryDocumentInfoDto);
//        LogUtil.audit("查询单证信息列表成功 documentListDTOList信息为" + JSONObject.toJSONString(documentListDTOList));
//        return ResponseResult.success(documentListDTOList);
//    }

//    private void validQueryDocumentInfoParams(QueryDocumentInfoDTO queryDocumentInfoDto) {
//
//        if (StringUtils.isEmptyStr(queryDocumentInfoDto.getReportNo())) {
//            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "报案号");
//        } /*else if () {
//            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "赔付次数");
//        }*/
//    }
}

