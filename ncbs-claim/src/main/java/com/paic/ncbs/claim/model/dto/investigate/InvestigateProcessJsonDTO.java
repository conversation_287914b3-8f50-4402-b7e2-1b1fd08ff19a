package com.paic.ncbs.claim.model.dto.investigate;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Setter
@Getter
@ApiModel("调查经过JSON")
public class InvestigateProcessJsonDTO {

    @ApiModelProperty("调查经过ID")
    private String idAhcsInvestigateProcess;

    @ApiModelProperty("关联任务Id")
    private String idAhcsInvestigateTask;

    @ApiModelProperty("调查途径")
    private String investigateWay;

    @ApiModelProperty("调查时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date investigateDate;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("调查结果描述")
    private String resultDescription;

    @ApiModelProperty("自定义其他信息（json）")
    private String otherInformation;

    @ApiModelProperty("责任")
    private String duties;

    @ApiModelProperty("职业")
    private String occupation;

    @ApiModelProperty("身份核实方式")
    private String verifMethod;

    @ApiModelProperty("走访地点")
    private String visitingLocation;

    @ApiModelProperty("走访目的")
    private String visitingPurpose;

    @ApiModelProperty("走访对象")
    private String visitingObjects;

    @ApiModelProperty("联系电话")
    private String telephone;

    @ApiModelProperty("医院名称")
    private String hospitalName;

    @ApiModelProperty("诊断名称")
    private String diagnoseName;

    @ApiModelProperty("现场查勘")
    private String siteSurvey;

    @ApiModelProperty("事故类型")
    private String accidentType;

    @ApiModelProperty("见证人")
    private String witness;

    @ApiModelProperty("工作单位")
    private String workUnit;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("与事故者关系")
    private String relationship;

    @ApiModelProperty("投保险种")
    private String insuranceCoverage;

    @ApiModelProperty("投保时间")
    private String insuranceTime;

    @ApiModelProperty("承保范围")
    private String coverage;
}