package com.paic.ncbs.claim.dao.entity.qualitychecke;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 质检意见明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Getter
@Setter
@TableName("clms_quality_detail")
public class ClmsQualityDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 序号
     */
    @TableField("serial_no")
    private String serialNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Short caseTimes;

    /**
     * 质检环节
     */
    @TableField("inspn_stage")
    private String inspnStage;

    /**
     * 质检规范
     */
    @TableField("inspn_standard")
    private String inspnStandard;

    /**
     * 差错等级(低、中、高)
     */
    @TableField("error_level")
    private String errorLevel;

    /**
     * 偏差金额
     */
    @TableField("diff_amount")
    private BigDecimal diffAmount;

    /**
     * 减损金额
     */
    @TableField("loss_amount")
    private BigDecimal lossAmount;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sysUtime;
}
