package com.paic.ncbs.claim.service.ahcs.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.enums.PolicyCertificateTypeEnum;
import com.paic.ncbs.claim.common.enums.RelationTypeWithInsured;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.common.util.RapeListUtils;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity;
import com.paic.ncbs.claim.dao.entity.estimate.PackageInfoEntity;
import com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity;
import com.paic.ncbs.claim.dao.entity.user.DepartmentRelationEntity;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.ProfessionDefineMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsInsuredPresonDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDutyDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyPlanDTO;
import com.paic.ncbs.claim.model.dto.ahcs.PolicyCopySendDetail;
import com.paic.ncbs.claim.model.dto.ahcs.RescueCompanyDTO;
import com.paic.ncbs.claim.model.dto.checkloss.SmallTermDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.ocas.*;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.dto.report.RatingQueryVO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.PolicyGroupDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskGroupDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.BeneficaryDTO;
import com.paic.ncbs.claim.model.dto.settle.EdrApplyHistoryInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorseDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPlanDutySumDTO;
import com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.RiskPackageDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.ocas.OcasPolicyQueryVO;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.ahcs.AhcsQueryPolicyService;
import com.paic.ncbs.claim.service.checkloss.BaseProdPifTermEntityService;
import com.paic.ncbs.claim.service.common.ResidueAmountService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.estimate.MarketProductInfoService;
import com.paic.ncbs.claim.service.estimate.PackageInfoService;
import com.paic.ncbs.claim.service.report.PolicyService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.taskdeal.TechnicProductInfoService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.DepartmentRelationService;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class AhcsQueryPolicyServiceImpl implements AhcsQueryPolicyService {

    @Autowired
    private DepartmentRelationService departmentRelationService;

    @Autowired
    private DepartmentDefineService departmentDefineService;

    @Autowired
    private MarketProductInfoService marketProductInfoService;

    @Autowired
    private PackageInfoService packageInfoService;

    @Autowired
    private TechnicProductInfoService technicProductInfoService;

    @Autowired
    private BaseProdPifTermEntityService baseProdPifTermEntityService;

    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;

    @Autowired
    private DepartmentDefineMapper departmentDefineMapper ;

    @Autowired
    private ProfessionDefineMapper professionDefineMapper;
    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordMapper ;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService ;

    @Autowired
    private OcasRequest ocasRequest;
    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private ResidueAmountService residueAmountService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private RiskPropertyPayMapper riskPropertyPayMapper;
    @Autowired
    private FileCommonService fileCommonService;
    @Autowired
    private PolicyService policyService;

    @Value("${ocas.ngUrl}")
    private String ngUrl;
    @Value("${pos.api.enable:true}")
    private boolean posApiEnable;

    @Override
    public Map<String, Object> getPolicyDomainInfoByPolicyNo(String reportNo, String policyNo, String policyCerNo, String selfCardNo, String clientNo) {
        return getPolicyDomainInfoByPolicyNoStore(reportNo,policyNo,policyCerNo,selfCardNo,clientNo);
    }

    private List<AhcsPolicyDomainDTO> transfrom(List<AhcsPolicyDomainDTO> dto) {
        List<AhcsPolicyDomainDTO> policyDomainDTOs = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(dto)) {
            for (Object obj : dto) {
                AhcsPolicyDomainDTO policyDomainDTO = JSONObject.parseObject(JSONObject.toJSONString(obj), AhcsPolicyDomainDTO.class);
                policyDomainDTOs.add(policyDomainDTO);
            }
        }
        return policyDomainDTOs;
    }

    public List<AhcsPolicyPlanDTO> getMergePolicyInfo(List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs) {
        LogUtil.audit("保单信息的险种责任合并入参为:{}", JSON.toJSONString(ahcsPolicyPlanDTOs));
        Map<String, List<AhcsPolicyPlanDTO>> collect = ahcsPolicyPlanDTOs.stream().collect(Collectors.groupingBy(e -> e.getAhcsPolicyPlan().getPlanCode()));
        Iterator<Entry<String, List<AhcsPolicyPlanDTO>>> iterator = collect.entrySet().iterator();
        List<AhcsPolicyPlanDTO> MergePolicyPlanDTOs = new ArrayList<>();

        while (iterator.hasNext()) {
            Entry<String, List<AhcsPolicyPlanDTO>> next = iterator.next();
            List<AhcsPolicyPlanDTO> planDTOs = next.getValue();
            if (planDTOs != null && planDTOs.size() > 1) {

                List<AhcsPolicyDutyDTO> dutyDtos = new ArrayList<>();
                for (AhcsPolicyPlanDTO ahcsPolicyPlanDTO : planDTOs) {

                    dutyDtos.addAll(ahcsPolicyPlanDTO.getAhcsPolicyDutyDTOs());
                }

                List<AhcsPolicyDutyDTO> policyDutyDtos = new ArrayList<>();
                if (RapeCheckUtil.isNotEmpty(dutyDtos)) {
                    policyDutyDtos = this.getMergeDutyInfo(dutyDtos);
                }

                AhcsPolicyPlanDTO ahcsPolicyPlanDTO = planDTOs.get(0);

                BigDecimal applyNum = planDTOs.stream().map(x -> x.getApplyNum()).reduce(BigDecimal.ZERO, BigDecimal::add);
                ahcsPolicyPlanDTO.setApplyNum(applyNum);

                ahcsPolicyPlanDTO.setAhcsPolicyDutyDTOs(policyDutyDtos);

                MergePolicyPlanDTOs.add(ahcsPolicyPlanDTO);
            } else if (planDTOs != null && planDTOs.size() == 1) {

                AhcsPolicyPlanDTO ahcsPolicyPlanDTO = planDTOs.get(0);
                List<AhcsPolicyDutyDTO> policyDutys = ahcsPolicyPlanDTO.getAhcsPolicyDutyDTOs();
                for (AhcsPolicyDutyDTO ahcsPolicyDutyDTO : policyDutys) {
                    ahcsPolicyDutyDTO.setIsMergePolicyDuty("N");
                }
                MergePolicyPlanDTOs.add(ahcsPolicyPlanDTO);
            }
        }
        LogUtil.audit("意健险险种责任合并处理之后出参：{}", JSONObject.toJSONString(MergePolicyPlanDTOs));
        return MergePolicyPlanDTOs;

    }

    private boolean existsRescueCompany(List<RescueCompanyDTO> rescueCompanyList) {
        return RapeCheckUtil.isListNotEmpty(rescueCompanyList)
                && rescueCompanyList.stream().anyMatch(company -> Constants.COMPANY_TYPE_RESCUE.equals(company.getCompanyType()));
    }

    public List<AhcsPolicyDutyDTO> getMergeDutyInfo(List<AhcsPolicyDutyDTO> dutyDtos) {
        LogUtil.audit("保单信息的责任信息入参为:{}", JSON.toJSONString(dutyDtos));
        Map<String, List<AhcsPolicyDutyDTO>> collectDuty = dutyDtos.stream().collect(Collectors.groupingBy(e -> e.getAhcsPolicyDuty().getDutyCode()));
        Iterator<Entry<String, List<AhcsPolicyDutyDTO>>> iterator2 = collectDuty.entrySet().iterator();
        List<AhcsPolicyDutyDTO> policyDutyDtos = new ArrayList<>();
        AhcsPolicyDutyDTO MergePolicyDutyDTO = null;
        while (iterator2.hasNext()) {
            Entry<String, List<AhcsPolicyDutyDTO>> dutyEntry = iterator2.next();
            List<AhcsPolicyDutyDTO> dutys = dutyEntry.getValue();
            MergePolicyDutyDTO = new AhcsPolicyDutyDTO();
            if (dutys != null && dutys.size() > 1) {
                AhcsPolicyDutyEntity policyDuty = dutys.get(0).getAhcsPolicyDuty();
                List<AhcsPolicyDutyDetailEntity> dutyDetails = new ArrayList<>();
                for (AhcsPolicyDutyDTO ahcsPolicyDutyDTO : dutys) {
                    dutyDetails.addAll(ahcsPolicyDutyDTO.getAhcsPolicyDutyDetail());
                }

                BigDecimal planAmount = dutys.stream().map(x -> x.getAhcsPolicyDuty().getDutyAmount()).reduce(BigDecimal.ZERO, (x, y) -> x.add(y));
                policyDuty.setDutyAmount(planAmount);
                MergePolicyDutyDTO.setAhcsPolicyDuty(policyDuty);
                MergePolicyDutyDTO.setIsMergePolicyDuty("Y");
                List<AhcsPolicyDutyDetailEntity> policyDutyDetail = new ArrayList<>();
                if (RapeCheckUtil.isNotEmpty(dutyDetails)) {
                    policyDutyDetail = this.getMergeDutyDetailInfo(dutyDetails);
                }
                MergePolicyDutyDTO.setAhcsPolicyDutyDetail(policyDutyDetail);
                MergePolicyDutyDTO.setAhcsDutyAttributeDTOs(dutys.get(0).getAhcsDutyAttributeDTOs());
                policyDutyDtos.add(MergePolicyDutyDTO);
            } else if (dutys != null && dutys.size() == 1) {

                MergePolicyDutyDTO.setAhcsPolicyDuty(dutys.get(0).getAhcsPolicyDuty());
                MergePolicyDutyDTO.setIsMergePolicyDuty("N");
                policyDutyDtos.add(MergePolicyDutyDTO);
            }
        }
        return policyDutyDtos;
    }

    public List<AhcsPolicyDutyDetailEntity> getMergeDutyDetailInfo(List<AhcsPolicyDutyDetailEntity> dutyDetails) {
        LogUtil.audit("保单信息的责任明细信息入参为:{}", JSON.toJSONString(dutyDetails));
        Map<String, List<AhcsPolicyDutyDetailEntity>> collectDutyDetail = dutyDetails.stream().collect(Collectors.groupingBy(e -> e.getDutyDetailCode()));
        Iterator<Entry<String, List<AhcsPolicyDutyDetailEntity>>> iterator = collectDutyDetail.entrySet().iterator();
        List<AhcsPolicyDutyDetailEntity> policyDutyDetails = new ArrayList<>();
        while (iterator.hasNext()) {
            Entry<String, List<AhcsPolicyDutyDetailEntity>> dutyEntry = iterator.next();
            List<AhcsPolicyDutyDetailEntity> dutys = dutyEntry.getValue();
            if (dutys != null && dutys.size() > 1) {
                AhcsPolicyDutyDetailEntity policyDutyDetail = dutys.get(0);

                BigDecimal dutyAmount = dutys.stream().map(x -> x.getDutyAmount()).reduce(BigDecimal.ZERO, (x, y) -> x.add(y));
                policyDutyDetail.setDutyAmount(dutyAmount);
                policyDutyDetails.add(policyDutyDetail);
            } else if (dutys != null && dutys.size() == 1) {
                policyDutyDetails.add(dutys.get(0));
            }
        }
        return policyDutyDetails;
    }

    @Override
    public Map<String, Object> getPolicyDomainInfoByPolicyNoStore(String reportNo, String policyNo, String policyCerNo, String selfCardNo, String clientNo) {
        List<AhcsPolicyDomainDTO> policyDomainDTOs = new ArrayList<>();
        if(posApiEnable){
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(policyNo,null);
            queryVO.setReportNo(reportNo);
            policyDomainDTOs.add(customerInfoStoreSAO.getPolicyDomainInfo(queryVO));
        }else{
            Map<String,String> param = new HashMap<>();
            param.put("policyNo",policyNo);
            param.put("productKind","0");
            param.put("isElecSubPolicyNo","0");
            policyDomainDTOs = customerInfoStoreSAO.getPolicyDomainInfo(param);
        }

        if (RapeCheckUtil.isEmpty(policyDomainDTOs)) {
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("保单数据为空"));
        }
        policyDomainDTOs = transfrom(policyDomainDTOs);
        AhcsPolicyDomainDTO dto = policyDomainDTOs.get(CommonConstant.ZERO);
        String departmentCode = dto.getAhcsPolicyInfo().getDepartmentCode();
        DepartmentDefineEntity department = departmentDefineService.getDepartmentInfo(departmentCode);
        DepartmentRelationEntity departmentRelation = departmentRelationService.getSecondDepartmentRelation(departmentCode);
        if (StringUtils.isEmpty(dto.getPolicyInfoExDTO().getDepartmentName())) {
            if (department != null) {
                String departmentAbbrName = department.getDepartmentAbbrName();
                dto.getPolicyInfoExDTO().setDepartmentName(departmentAbbrName);
                if (departmentRelation != null && department.getDepartmentLevel() > CommonConstant.DEPARTMENT_LEVLEL_SECOND) {

                    String secondDepartmentCode = departmentRelation.getParentDepartmentCode();
                    DepartmentDefineEntity secondDepartment = departmentDefineService.getDepartmentInfo(secondDepartmentCode);
                    String secondDepartmentName = secondDepartment.getDepartmentAbbrName();
                    String departmentName = secondDepartmentName + dto.getPolicyInfoExDTO().getDepartmentName();
                    dto.getPolicyInfoExDTO().setDepartmentName(departmentName);
                }
            }
        }
        if (("PAS".equals(dto.getPolicySystem()) || "IBCS".equals(dto.getPolicySystem())) && StringUtils.isNotEmpty(policyCerNo)) {
            policyNo = policyCerNo;
        }
        List<EndorseDTO> endorseDTOs = new ArrayList<>();//pasMockService.getHistoryEndorseInfo(policyNo);
        if ("PAS".equals(dto.getPolicySystem())) {
            if (RapeCheckUtil.isNotEmpty(endorseDTOs)) {
                String endorseItem = endorseDTOs.get(CommonConstant.ZERO).getEndorseItem();
                Date date = endorseDTOs.get(CommonConstant.ZERO).getInsuranceBeginTime();

                if ("20011".equals(endorseItem)) {
                    dto.getPolicyInfoExDTO().setCancellationTime(date);
                }

                if ("00006".equals(endorseItem)) {
                    dto.getPolicyInfoExDTO().setCancellationTime(date);
                }

                if ("00017".equals(endorseItem)) {
                    dto.getPolicyInfoExDTO().setTerminateTime(date);
                }
            }
        }

        dto.setTechnicProductCode(technicProductInfoService.getTechnicProductCode(dto.getAhcsPolicyInfo().getProductCode()));

        MarketProductInfoEntity marketProductInfo = marketProductInfoService
                .getMarketProductInfoByProductCode(dto.getAhcsPolicyInfo().getProductCode(), dto.getAhcsPolicyInfo().getProductVersion());
        if (marketProductInfo != null) {
            LogUtil.audit("marketProductInfo:{}", JSONObject.toJSONString(marketProductInfo));
            dto.getPolicyInfoExDTO().setPageType(marketProductInfo.getPageType());

            dto.getPolicyInfoExDTO().setIsAutoClaim(marketProductInfo.getIsAutoClaim());

            dto.getPolicyInfoExDTO().setClaimBeforeInsurance(marketProductInfo.getClaimBeforeInsurance());

            List<RiskPackageDTO> riskPackageDTOs = dto.getRiskPackageDTOs();
            if (RapeCheckUtil.isNotEmpty(riskPackageDTOs)) {
                for (RiskPackageDTO riskPackageDTO : riskPackageDTOs) {
                    PackageInfoEntity packageInfo = packageInfoService.getPackageInfoByCode(riskPackageDTO.getPackageCode(),
                            marketProductInfo.getIdMarketproductInfo());
                    if (packageInfo != null) {
                        riskPackageDTO.setPackageName(packageInfo.getPackageName());
                    }
                }
            }
            if (BaseConstant.STRING_1.equals(marketProductInfo.getIsFamilyAccount())) {
                if (StringUtils.isEmpty(dto.getAhcsPolicyInfo().getRemark())) {
                    dto.getAhcsPolicyInfo().setRemark("家庭账户保单");
                } else {
                    dto.getAhcsPolicyInfo().setRemark("家庭账户保单，" + dto.getAhcsPolicyInfo().getRemark());
                }
            }
        }

        if (RapeCheckUtil.isNotEmpty(dto.getAhcsCoinsure())) {
            for (AhcsCoinsureEntity ahcsCoinsu : dto.getAhcsCoinsure()) {
                if ("1".equals(ahcsCoinsu.getCoinsuranceType())) {
                    break;
                }
                DepartmentDefineEntity defineEntity = null;
                if (StringUtils.isEmpty(ahcsCoinsu.getReinsureCompanyName())) {
                    if (StringUtils.isNotEmpty(ahcsCoinsu.getReinsureCompanyCode())) {
                        defineEntity = departmentDefineService.getDepartmentInfo(ahcsCoinsu.getReinsureCompanyCode());
                        if (defineEntity != null) {
                            ahcsCoinsu.setReinsureCompanyName(defineEntity.getDepartmentChineseName());
                        }
                    }
                }
                if ("PAS".equals(dto.getPolicySystem())) {
                    if ("3005".equals(ahcsCoinsu.getReinsureCompanyCode())) {
                        dto.getPolicyInfoExDTO().setAcceptInsuranceFlag(ahcsCoinsu.getAcceptInsuranceFlag());
                    }
                }
            }
        }
        if (StringUtils.isEmpty(dto.getPolicyInfoExDTO().getCertificateTypeName()) && RapeCheckUtil.isNotEmpty(dto.getAhcsInsuredPresonDTOs())) {
            AhcsInsuredPresonDTO presonDTO = dto.getAhcsInsuredPresonDTOs().get(CommonConstant.ZERO);

            if (BaseConstant.STRING_0.equals(presonDTO.getPersonnelType())) {
                dto.getPolicyInfoExDTO().setCertificateTypeName(CertificateTypeEnum.getName(presonDTO.getAhcsInsuredPreson().getCertificateType()));
            } else {
                dto.getPolicyInfoExDTO().setCertificateTypeName(CertificateTypeEnum.getName(presonDTO.getAhcsInsuredPreson().getCertificateType()));
            }
        }
        LogUtil.audit("合并接口开始dto:{}", JSON.toJSONString(policyDomainDTOs));

        if (RapeCheckUtil.isNotEmpty(dto.getAhcsPolicyPlanDTOs())) {
            List<AhcsPolicyPlanDTO> MergePolicyPlanDTOs = getMergePolicyInfo(dto.getAhcsPolicyPlanDTOs());
            for (AhcsPolicyPlanDTO ahcsPolicyPlanDTO : MergePolicyPlanDTOs) {
                List<AhcsPolicyDutyDTO> policyDutyDTOs = ahcsPolicyPlanDTO.getAhcsPolicyDutyDTOs();
                for (AhcsPolicyDutyDTO ahcsPolicyDutyDTO : policyDutyDTOs) {

                    if ("Y".equals(ahcsPolicyDutyDTO.getIsMergePolicyDuty())) {
                        dto.setIsMergePolicyFlg("Y");
                    }
                }
            }
            dto.setAhcsPolicyPlanDTOs(MergePolicyPlanDTOs);
        }
        LogUtil.audit("合并接口结束dto:{}", JSON.toJSONString(policyDomainDTOs));

        if (RapeCheckUtil.isNotEmpty(dto.getAhcsPolicyPlanDTOs()) && RapeCheckUtil.isListNotEmpty(dto.getPolicyPlanDutySumDTOs())) {
            for (AhcsPolicyPlanDTO ahcsPolicyPlanDTO : dto.getAhcsPolicyPlanDTOs()) {
                for (PolicyPlanDutySumDTO planDutySumDto : dto.getPolicyPlanDutySumDTOs()) {
                    if (ahcsPolicyPlanDTO.getAhcsPolicyPlan() != null && planDutySumDto != null && ahcsPolicyPlanDTO.getAhcsPolicyPlan().getPlanCode().equals(planDutySumDto.getPlanCode())) {
                        planDutySumDto.setGroupCode(ahcsPolicyPlanDTO.getAhcsPolicyPlan().getGroupCode());
                    }
                }

            }
        }

        if (RapeCheckUtil.isNotEmpty(dto.getAhcsPolicyPlanDTOs())) {
            LogUtil.info("根据groupCode对 AhcsPolicyPlan排序开始", JSON.toJSONString(dto.getAhcsPolicyPlanDTOs()));
            List planList = new ArrayList<>();

            List<AhcsPolicyPlanDTO> planNull =
                    dto.getAhcsPolicyPlanDTOs().stream().filter(t -> RapeCheckUtil.isBlank(t.getAhcsPolicyPlan().getGroupCode()))
                            .collect(Collectors.toList());
            planList.addAll(planNull);

            List<AhcsPolicyPlanDTO> planNotNull =
                    dto.getAhcsPolicyPlanDTOs().stream().filter(t -> RapeCheckUtil.isNotBlank(t.getAhcsPolicyPlan().getGroupCode()))
                            .sorted(Comparator.comparing(e -> e.getAhcsPolicyPlan().getGroupCode()))
                            .collect(Collectors.toList());
            planList.addAll(planNotNull);
            dto.setAhcsPolicyPlanDTOs(planList);
            LogUtil.info("根据groupCode对 AhcsPolicyPlan排序结果:", JSON.toJSONString(planList));
        }
        
        Map<String, Object> map = new HashMap<>();
        map.put("AhcsPolicyDomainDTO", dto);

        if (dto != null && dto.getPolicyInfoExDTO() != null) {
            List<SmallTermDTO> smallTerms = dto.getPolicyInfoExDTO().getSmallTerms();
            LogUtil.info("保单抄件-团意小条款抄单结果：smallTerms={}", JSON.toJSONString(smallTerms));
            if (RapeListUtils.isNotEmpty(smallTerms)) {
                List<String> termCodesList = new ArrayList<>();
                for (SmallTermDTO smallTerm : smallTerms) {
                    if (StringUtils.isNotEmpty(smallTerm.getTermCode())) {
                        termCodesList.add(smallTerm.getTermCode());
                    }
                }

                if (RapeListUtils.isNotEmpty(termCodesList)) {
                    smallTerms = baseProdPifTermEntityService.getTermNamesByTermCodes(termCodesList);
                    LogUtil.info("保单抄件-团意小条款查询结果：smallTerms={}", JSON.toJSONString(smallTerms));
                    dto.getPolicyInfoExDTO().setSmallTerms(smallTerms);
                }
            }
        }

        return map;
    }

    @Override
    public  Map<String, Object>  policyCopyAndSendDetail(String reportNo, String policyNo, Integer caseTimes, String insuredName, String certificateNo, Pager pager, String telephone, String certificateType) {
        AhcsPolicyDomainDTO ahcsPolicyDomainDTO = null;
        if(posApiEnable){
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(policyNo,null);
            queryVO.setCertNo(certificateNo);
            queryVO.setClientName(insuredName);
            queryVO.setName(insuredName);
            queryVO.setCertificateType(certificateType);
            queryVO.setCertificateNo(certificateNo);
            queryVO.setInvokePurpose("01");
            queryVO.setReportNo(reportNo);
            ahcsPolicyDomainDTO = customerInfoStoreSAO.getPolicyDomainInfo(queryVO);
        }else{
            Map<String, String> param = new HashMap<>();
            param.put("policyNo", policyNo);
            param.put("name", insuredName);
            param.put("certificateType", certificateType);
            param.put("certificateNo", certificateNo);
            ahcsPolicyDomainDTO = customerInfoStoreSAO.getPolicyDomainInfoByPolicyNo(param);
        }
        Map<String, Object> resultMap = new HashMap<>();
        List<SubProfessionDefineDTO> allSubProfessionDefines = professionDefineMapper.getAllSubProfessionDefines();
        String phoneNum = null;
        String clientNo = null;
        if (!CollectionUtils.isEmpty(ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs())){
            List<AhcsInsuredPresonDTO> ahcsInsuredPresonDTOs = ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs() ;
            if (!CollectionUtils.isEmpty(ahcsInsuredPresonDTOs) ){
                String finalInsuredName = insuredName;
                ahcsInsuredPresonDTOs= ahcsInsuredPresonDTOs.stream().filter(e->
                        (null != e.getAhcsInsuredPreson() && certificateNo.equals(e.getAhcsInsuredPreson().getCertificateNo())
                                && finalInsuredName.equals(e.getAhcsInsuredPreson().getName())
                        )

                ).distinct().collect(Collectors.toList());
                ahcsInsuredPresonDTOs =  ahcsInsuredPresonDTOs.size() > 1 ? ahcsInsuredPresonDTOs.subList(0,1) :ahcsInsuredPresonDTOs ;
                ahcsInsuredPresonDTOs.forEach(per->{
                    per.getAhcsInsuredPreson().setPersonnelName(getProfession(per.getAhcsInsuredPreson().getProfessionCode(),allSubProfessionDefines));
                    per.getAhcsInsuredPreson().setCertificateName(CertificateTypeEnum.getName(per.getAhcsInsuredPreson().getCertificateType()));
                });
            }
            if(ListUtils.isNotEmpty(ahcsInsuredPresonDTOs)){
                phoneNum = ahcsInsuredPresonDTOs.get(0).getAhcsInsuredPreson().getMobileTelephone();
                clientNo = ahcsInsuredPresonDTOs.get(0).getAhcsInsuredPreson().getClientNo();
            }
            ahcsPolicyDomainDTO.setAhcsInsuredPresonDTOs(ahcsInsuredPresonDTOs);
        }
        //保单基本信息
        PolicyCopySendDetail policyCopySendDetail = ocasMapper.policyCopyAndSendDetail(policyNo);
        policyCopySendDetail.setIsFamily(ahcsPolicyDomainDTO.getIsFamily());

        RatingQueryVO ratingQueryVO = new RatingQueryVO(insuredName);
        ratingQueryVO.setPhoneNumber(phoneNum);
        if(CertificateTypeEnum.ID_CARD.getType().equals(certificateType)){
            ratingQueryVO.setIdentificationNumber(certificateNo);
        }
        policyCopySendDetail.setClientType(customerInfoStoreSAO.queryCustomerRating(ratingQueryVO));
        if(!CollectionUtils.isEmpty(ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs())){
            ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs().get(0).getAhcsInsuredPreson().setClientType(policyCopySendDetail.getClientType());
        }
        policyCopySendDetail.setOcasUrl(ngUrl);

        DepartmentDTO departmentDTO = departmentDefineMapper.queryDepartmentInfoByDeptCode(policyCopySendDetail.getDepartmentCode());

        policyCopySendDetail.setProfessionName(getProfession(policyCopySendDetail.getProfessionCode(),allSubProfessionDefines));

        if (null != departmentDTO){
            policyCopySendDetail.setDepartmentName(departmentDTO.getDepartmentAbbrName());
        }
        // 00006是退保，00017是注销
        if (null != policyCopySendDetail.getStatus()){
            if ("00006".equals(policyCopySendDetail.getStatus())){
                policyCopySendDetail.setEdrEffectiveDate(policyCopySendDetail.getEffectiveDate());
            }else if ("00017".equals(policyCopySendDetail.getStatus())){
                policyCopySendDetail.setReturnEffectiveDate(policyCopySendDetail.getEffectiveDate());
            }
        }

        List<OcasPolicyDTO> policyList;
        boolean displayRiskProperty = riskPropertyService.isRiskProperty(ahcsPolicyDomainDTO.getTargetType(), ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProductClass());
        resultMap.put("displayTargets", displayRiskProperty);
        if(!displayRiskProperty){
            policyList = getOcasPolicyDTOS(policyNo, insuredName,  certificateNo,clientNo);
        } else {
            policyList = getOcasPolicys(policyNo, insuredName, certificateNo);
        }

        List<BeneficaryDTO> beneficaryDTO = ocasMapper.benefitInfos(policyNo,certificateNo,insuredName);

        policyCopySendDetail.setApplyNum(ahcsPolicyDomainDTO.getApplyNum());
        bulidPolicyCopySendDetail(ahcsPolicyDomainDTO, policyCopySendDetail);
        resultMap.put("policyCopySendDetail", policyCopySendDetail);
        resultMap.put("estimateData", policyList);
        resultMap.put("ahcsPolicyDomainDTO", ahcsPolicyDomainDTO);

        beneficaryDTO.forEach(beneficary->{
            beneficary.setCertificateTypeName(PolicyCertificateTypeEnum.getName(beneficary.getCertificateType()));
            beneficary.setRelationshipWithInsured(RelationTypeWithInsured.getName(beneficary.getRelationshipWithInsured()));
        });
        resultMap.put("beneficaryDTO",beneficaryDTO) ;

        // 理赔记录
        pager.setPageRows(100000); //默认前端分页
        resultMap.put("historySettleInfo",reportCustomerInfoService.getHistoryByPolicyNo(policyNo,pager,certificateNo,insuredName)) ;

        //保全信息
        Map<String,Object> paramx = new HashMap<>();
        paramx.put("policyNo", policyNo);
        paramx.put("isElecSubPolicyNo","0");
        paramx.put("productKind","0");
        paramx.put("umsDisplayFlag","claim");
        String  result = ocasRequest.queryEdrApplyHistoryList(paramx);
        List<EdrApplyHistoryInfoDTO> edrApplyHistoryInfoDTOS = JSONObject.parseArray(result, EdrApplyHistoryInfoDTO.class);

        edrApplyHistoryInfoDTOS.forEach(ed->{
            if (ed.getEffectiveDate().after(new Date())){
                ed.setIsEffective("N");
            }else {
                ed.setIsEffective("Y");
            }
            //批单单证信息
            Map<String,Object> paramEPolicy = new HashMap<>();
            paramEPolicy.put("policyNo", policyNo);
            paramEPolicy.put("applyType","8");
            paramEPolicy.put("endorseNo",ed.getEndorseNo());
            paramEPolicy.put("endorseApplyNo",ed.getEndorseApplyNo());
            String resultEPolicy = ocasRequest.queryEPolicy(paramEPolicy);
            Map ePolicyMap = JSON.parseObject(resultEPolicy, Map.class);
            List ePolicyList = (List) ePolicyMap.get("data");
            if (RapeCheckUtil.isNotEmpty(ePolicyList)) {
                Map<String, Object> ePolicyInfo = (Map<String, Object>) ePolicyList.get(0);
                ed.setEPolicyPdfUrl((String)ePolicyInfo.get("pdfUrl"));
            }
        });
        resultMap.put("policyInfos",edrApplyHistoryInfoDTOS) ;
        PolicyQueryVO queryVO = new PolicyQueryVO();
        queryVO.setPolicyNo(policyNo);
        queryVO.setInsuredName(insuredName);
        queryVO.setCertificateNo(certificateNo);
        queryVO.setCertificateType(certificateType);
        List<PolicyFilesInfoVO> filesList = policyService.getPolicyFileListNew(queryVO);
        List<PolicyFilesInfoVO> filteredDocuments = filesList.stream()
                .filter(doc -> !Objects.equals(doc.getFileType(), "P010"))
                .collect(Collectors.toList());
        resultMap.put("filesInfos",filteredDocuments);
        //个人凭证
        List<PolicyFilesInfoVO> personalCertPdfList = filesList.stream()
                .filter(doc -> Objects.equals(doc.getFileType(), "P010"))
                .collect(Collectors.toList());
        if (personalCertPdfList != null && !personalCertPdfList.isEmpty()) {
            resultMap.put("personalCertUrl",personalCertPdfList.get(0).getUrl());
        }
        try {
            String policyDocumentId = ocasMapper.getDocumentIdByPolicyNo(policyNo);
            UserInfoDTO userDTO = WebServletContext.getUser();
            String previewUrl = fileCommonService.getPreviewUrl(policyDocumentId,userDTO.getUserName());
            resultMap.put("electronicGuaranteeUrl",previewUrl);
        } catch (Exception e){
            LogUtil.error("保单号：{}获取保函失败，失败原因：{}",policyNo,e.getMessage());
        }
        try {
            //查应交、实交保费
            AhcsPolicyInfoEntity policyInfoEntity = Optional.ofNullable(ocasMapper.getPolicyActualPremium(policyNo)).orElse(new AhcsPolicyInfoEntity());
            policyCopySendDetail.setTotalAgreePremium(policyInfoEntity.getTotalAgreePremium());
            policyCopySendDetail.setTotalActualPremium(policyInfoEntity.getTotalActualPremium());
        }catch (Exception e){
            LogUtil.error("查询保费失败",e);
        }

        /*try {
            resultMap.put("displayTargets",false);
            if(displayRiskProperty){
                List<ReportRiskPropertyDTO> reportRiskList = ahcsPolicyDomainDTO.getReportRiskPropertyList();
                if(ListUtils.isNotEmpty(reportRiskList)){
                    List<CaseRiskPropertyDTO> caseRiskList = new ArrayList<>();
                    CaseRiskPropertyDTO caseRisk = null;
                    for (ReportRiskPropertyDTO reportRisk : reportRiskList) {
                        caseRisk = new CaseRiskPropertyDTO();
                        BeanUtils.copyProperties(reportRisk,caseRisk);
                        caseRiskList.add(caseRisk);
                    }
                    List<RiskGroupDTO> riskGroupList = null;
                    List<PolicyGroupDTO> policyGroupList = riskPropertyService.buildPlyGroupDTO(caseRiskList,null);
                    if(ListUtils.isNotEmpty(policyGroupList)){
                        riskGroupList = policyGroupList.get(0).getRiskGroupList();
                    }
                    resultMap.put("riskGroupList",riskGroupList);
                }
                resultMap.put("displayTargets",true);
            }
        }catch (Exception e){
            LogUtil.info("查询标的失败,不影响原有流程",e);
        }*/
        //家庭单保单列表
//        if(!"1".equals(ahcsPolicyDomainDTO.getIsFamily())){
//            resultMap.put("displayFamilyPolList",true);
//            String onlineOrderNo = ahcsPolicyDomainDTO.getonlineOrderNo();
//            List<String> familyPolicyNoList = new ArrayList<>();
//            familyPolicyNoList.add("233213123123123123");
//            familyPolicyNoList.add("423321323123213213123");
//            resultMap.put("familyPolList",familyPolicyNoList);
//        }
        try {
            //查询电子保单预览URL
            String policyDocumentId = ocasMapper.getPolicyDocumentId(policyNo);
            UserInfoDTO userDTO = WebServletContext.getUser();
            String previewUrl = fileCommonService.getPreviewUrl(policyDocumentId,userDTO.getUserName());
            policyCopySendDetail.setPolicyElectricPdfUrl(previewUrl);
        } catch (Exception e){
            LogUtil.error("保单号：{}获取预览失败，失败原因：{}",policyNo,e.getMessage());
        }
        return resultMap ;
    }

    public void bulidPolicyCopySendDetail(AhcsPolicyDomainDTO ahcsPolicyDomainDTO,
                                          PolicyCopySendDetail policyCopySendDetail){
        policyCopySendDetail.setIsTransferInsure("1".equals(ahcsPolicyDomainDTO.getIsTransferInsure()) ? "是" : "否");
        policyCopySendDetail.setTransferInsurancePolicyNo(ahcsPolicyDomainDTO.getTransferInsurancePolicyNo());
        policyCopySendDetail.setTransferInsuranceEndDate(ahcsPolicyDomainDTO.getTransferInsuranceEndDate());
        policyCopySendDetail.setTransferInsuranceProductName(ahcsPolicyDomainDTO.getTransferInsuranceProductName());
    }

    public List<OcasPolicyDTO> getOcasPolicyDTOS(String policyNo,String insuredName, String certificateNo,String clientNo) {
        List<OcasPolicyDTO> policyList = new ArrayList<>();
        List<OcasPolicyPlanDutyDTO> policyPlanDutyDTOList = ocasMapper.getPolicyCopyDuty(policyNo,certificateNo,insuredName);
        Map<String, List<OcasPolicyPlanDutyDTO>> policyMap = policyPlanDutyDTOList.stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPolicyNo));
        for (Entry<String, List<OcasPolicyPlanDutyDTO>> policyEntry : policyMap.entrySet()) {
            Map<String, List<OcasPolicyPlanDutyDTO>> planMap = policyEntry.getValue().stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPlanCode));
            List<OcasPlanDTO> planList = new ArrayList<>();
            for (Entry<String, List<OcasPolicyPlanDutyDTO>> planEntry : planMap.entrySet()) {
                List<OcasDutyDTO> dutyList = new ArrayList<>();
                String planCode = planEntry.getKey();
                for (OcasPolicyPlanDutyDTO duty : planEntry.getValue()) {
                    OcasDutyDTO ocasDutyDTO = new OcasDutyDTO(duty.getDutyCode(), duty.getDutyName(), duty.getDutyAmount());
                    ocasDutyDTO.setIsDutyShareAmount(duty.getDutyShareAmount());
                    ocasDutyDTO.setShareDutyGroup(duty.getShareDutyGroup());
                    ocasDutyDTO.setIsShareAmount(duty.getShareAmount());
                    dutyList.add(ocasDutyDTO);
                }
                planList.add(new OcasPlanDTO(planCode, planMap.get(planCode).get(0).getPlanName(), dutyList));
            }

            String policyNox = policyEntry.getKey();
            OcasPolicyPlanDutyDTO policy = policyMap.get(policyNox).get(0);
            policyList.add(new OcasPolicyDTO(policyNo, policy, planList));
        }

        //立案金额
        WholeCaseVO queryVO = new WholeCaseVO();
        queryVO.setCertificateNo(certificateNo);
        queryVO.setName(insuredName);
        queryVO.setPolicyNo(policyNo);
        queryVO.setPerPageSize(10000);

        List<HistoryCaseDTO> reportInfos = reportInfoService.getHistoryCaseNewByCopy(queryVO);
        List<String> caseNos = new ArrayList<>() ;
        List<String> reports =new ArrayList<>();
        if (reportInfos.size() > 0){
            caseNos = reportInfos.stream().distinct().map(HistoryCaseDTO :: getCaseNo).collect(Collectors.toList());
            reports = reportInfos.stream().distinct().map(HistoryCaseDTO :: getReportNo).collect(Collectors.toList());
        }
        LogUtil.info("reports:{}",reports);

        if (!CollectionUtils.isEmpty(policyList)) {

            final Map<String, BigDecimal> registMap = new HashMap<>();
            List<EstimateDutyRecordDTO> totalRegistAmount = estimateDutyRecordMapper.getTotalRegistAmount(policyNo, caseNos);
            if (CollectionUtils.isEmpty(caseNos) && ! CollectionUtils.isEmpty(totalRegistAmount)){
                totalRegistAmount.forEach(
                        totalRegistAmountEx->totalRegistAmountEx.setEstimateAmount(new BigDecimal("0"))
                );
            }
            LogUtil.info("getOcasPolicyDTOS保单号={},被保险人={}，证件号={},责任未决记录={}",policyNo,insuredName,certificateNo, JSON.toJSONString(totalRegistAmount));
            if (ListUtils.isNotEmpty(totalRegistAmount)) {
                //key:plancode+dutycode
                registMap.putAll(totalRegistAmount.stream().collect(Collectors.toMap(esDTO ->esDTO.getPlanCode()+esDTO.getDutyCode(),EstimateDutyRecordDTO::getEstimateAmount)));
                //registMap.putAll(totalRegistAmount.stream().collect(Collectors.toMap(EstimateDutyRecordDTO::getDutyCode, EstimateDutyRecordDTO::getEstimateAmount)));
            }

            policyList.forEach(e->{
                List<OcasPlanDTO> planList = e.getPlanList();
                if (!CollectionUtils.isEmpty(planList)){
                    planList.forEach(e1->{
                        List<OcasDutyDTO> dutyList = e1.getDutyList();
                        Boolean isShareAmount = dutyList.stream().anyMatch(OcasDutyDTO::getIsShareAmount);
                        for (OcasDutyDTO ex:dutyList){
                            //立案金额
                            ex.setEstimateAmount(registMap.getOrDefault(e1.getPlanCode()+ex.getDutyCode(),BigDecimal.ZERO));

                            BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay(policyNo, e1.getPlanCode(),
                                    ex.getDutyCode(), ex.getIsDutyShareAmount(), ex.getShareDutyGroup(), isShareAmount, clientNo);
                            //最大给付额=保额-已赔付金额-已预赔金额
                            ex.setDutyMaxPay(ex.getDutyAmount().subtract(dutyHistoryPay));
                            if(BigDecimalUtils.compareBigDecimalPlus(BigDecimal.ZERO,ex.getDutyMaxPay())){
                                ex.setDutyMaxPay(BigDecimal.ZERO);
                            }
                        }
                    });
                }
            });
            }
        return policyList;
    }

    private List<OcasPolicyDTO> getOcasPolicys(String policyNo, String insuredName, String certificateNo) {
        List<OcasPolicyDTO> policyList = new ArrayList<>();
        List<OcasPolicyPlanDutyDTO> policyPlanDutyDTOList = ocasMapper.getPolicyCopyDuty(policyNo, certificateNo, insuredName);
        Map<String, List<OcasPolicyPlanDutyDTO>> policyMap = policyPlanDutyDTOList.stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPolicyNo));
        for (Map.Entry<String, List<OcasPolicyPlanDutyDTO>> policyEntry : policyMap.entrySet()) {
            Map<String, List<OcasPolicyPlanDutyDTO>> riskGroupMap = policyEntry.getValue().stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getRiskGroupNo));
            List<OcasRiskGroupDTO> riskGroupList = new ArrayList<>();
            for (Map.Entry<String, List<OcasPolicyPlanDutyDTO>> riskGroupEntry : riskGroupMap.entrySet()) {
                OcasPolicyPlanDutyDTO riskGroup = riskGroupEntry.getValue().get(0);
                Map<String, List<OcasPolicyPlanDutyDTO>> planMap = riskGroupEntry.getValue().stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPlanCode));
                List<OcasPlanDTO> planList = new ArrayList<>();
                for (Entry<String, List<OcasPolicyPlanDutyDTO>> planEntry : planMap.entrySet()) {
                    String planCode = planEntry.getKey();
                    List<OcasDutyDTO> dutyList = new ArrayList<>();
                    for (OcasPolicyPlanDutyDTO duty : planEntry.getValue()) {
                        dutyList.add(new OcasDutyDTO(duty.getDutyCode(), duty.getDutyName(), duty.getDutyAmount()));
                    }
                    planList.add(new OcasPlanDTO(planCode, planMap.get(planCode).get(0).getPlanName(), dutyList));
                }
                riskGroupList.add(new OcasRiskGroupDTO(riskGroup.getRiskGroupNo(), riskGroup.getRiskGroupName(),"",
                        planList));
            }

            String key = policyEntry.getKey();
            OcasPolicyPlanDutyDTO policy = policyMap.get(key).get(0);
            OcasPolicyDTO ocasPolicy = new OcasPolicyDTO(key, policy, null);
            ocasPolicy.setRiskGroupList(riskGroupList);
            policyList.add(ocasPolicy);
        }

        //立案金额
        List<EstimateDutyRecordDTO> registList = Optional.ofNullable(estimateDutyRecordMapper.getTotalRegistAmountForRiskProperty(policyNo)).orElse(new ArrayList<>());
        Map<String, BigDecimal> registMap = registList.stream().collect(Collectors.toMap(EstimateDutyRecordDTO::getDutyCode, EstimateDutyRecordDTO::getEstimateAmount));
        for (OcasPolicyDTO policy : policyList) {
            for (OcasRiskGroupDTO riskGroup : policy.getRiskGroupList()) {
                for (OcasPlanDTO plan : riskGroup.getPlanList()) {
                    for (OcasDutyDTO duty : plan.getDutyList()) {
                        String key = riskGroup.getRiskGroupNo() + plan.getPlanCode() + duty.getDutyCode();
                        duty.setEstimateAmount(registMap.getOrDefault(key, BigDecimal.ZERO));
                    }
                }
            }
        }
        return policyList;
    }

    public  String getProfession (String  professonCode , List<SubProfessionDefineDTO> allSubProfessionDefines ){
        String  professonName ="" ;
        if (!CollectionUtils.isEmpty(allSubProfessionDefines)){
            for (SubProfessionDefineDTO subPro : allSubProfessionDefines){
                if (subPro.getProfessionCode().equals(professonCode)){
                    professonName = subPro.getProfessionChnAbbrName();
                }
            }
        }
        return  professonName ;
    }

    @Override
    public List<OcasPolicyDTO> getRiskGroupPlanList(OcasPolicyQueryVO queryVO) {
        return getOcasPolicyList(queryVO);
    }

    public List<OcasPolicyDTO> getOcasPolicyList(OcasPolicyQueryVO queryVO) {
        String policyNo = queryVO.getPolicyNo();
        String idRiskProperty = queryVO.getIdPlyRiskProperty();
        List<OcasPolicyDTO> policyList = new ArrayList<>();
        List<OcasPolicyPlanDutyDTO> policyPlanDutyDTOList = ocasMapper.getPolicyCopyDutyList(queryVO);
        Map<String, List<OcasPolicyPlanDutyDTO>> policyMap = policyPlanDutyDTOList.stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPolicyNo));
        for (Entry<String, List<OcasPolicyPlanDutyDTO>> policyEntry : policyMap.entrySet()) {
            Map<String, List<OcasPolicyPlanDutyDTO>> planMap = policyEntry.getValue().stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPlanCode));
            List<OcasPlanDTO> planList = new ArrayList<>();
            for (Entry<String, List<OcasPolicyPlanDutyDTO>> planEntry : planMap.entrySet()) {
                List<OcasDutyDTO> dutyList = new ArrayList<>();
                String planCode = planEntry.getKey();
                for (OcasPolicyPlanDutyDTO duty : planEntry.getValue()) {
                    dutyList.add(new OcasDutyDTO(duty.getDutyCode(), duty.getDutyName(), duty.getDutyAmount()));
                }
                planList.add(new OcasPlanDTO(planCode, planMap.get(planCode).get(0).getPlanName(), dutyList));
            }

            String policyNox = policyEntry.getKey();
            OcasPolicyPlanDutyDTO policy = policyMap.get(policyNox).get(0);
            policyList.add(new OcasPolicyDTO(policyNo, policy, planList));
        }

        if(ListUtils.isEmptyList(policyList)){
            return policyList;
        }

        String insuredCode = null;
        if(ListUtils.isNotEmpty(policyPlanDutyDTOList)){
            insuredCode = policyPlanDutyDTOList.get(0).getClientNo();
        }
        if(insuredCode == null){
            throw new GlobalBusinessException("客户号为空");
        }

        //立案金额
        List<EstimateDutyRecordDTO> registList = Optional.ofNullable(estimateDutyRecordMapper.getTotalRegistAmountByIdRiskProperty(idRiskProperty,insuredCode)).orElse(new ArrayList<>());
        Map<String, BigDecimal> registMap = registList.stream().collect(Collectors.toMap(e -> e.getDutyCode(), EstimateDutyRecordDTO::getEstimateAmount));

        //最大给付额
        RiskPropertyPayDTO queryDTO = new RiskPropertyPayDTO();
        queryDTO.setIdPlyRiskProperty(idRiskProperty);
        queryDTO.setInsuredCode(insuredCode);
        List<RiskPropertyPayDTO> riskPayedList = Optional.ofNullable(riskPropertyPayMapper.getRiskPropertyDutyPay(queryDTO)).orElse(new ArrayList<>());
        Map<String, BigDecimal> payedMap = riskPayedList.stream().collect(Collectors.toMap(e -> e.getDutyDetailCode(),RiskPropertyPayDTO::getDutyDetailPay));

        policyList.forEach(e->{
            List<OcasPlanDTO> planList = e.getPlanList();
            if (ListUtils.isNotEmpty(planList)){
                planList.forEach(e1->{
                    List<OcasDutyDTO> dutyList = e1.getDutyList();
                    for (OcasDutyDTO ex : dutyList){
                        String key = e1.getPlanCode()+ex.getDutyCode();
                        //立案金额
                        ex.setEstimateAmount(registMap.getOrDefault(key,BigDecimal.ZERO));
                        //最大给付额=保额-已赔付金额-已预赔金额
                        BigDecimal dutyAmt = BigDecimalUtils.nvl(ex.getDutyAmount(),BaseConstant.STRING_0);
                        BigDecimal payedAmt = payedMap.getOrDefault(key,BigDecimal.ZERO);
                        BigDecimal maxAmt = dutyAmt.subtract(payedAmt);
                        if(BigDecimalUtils.compareBigDecimalPlus(BigDecimal.ZERO,maxAmt)){
                            maxAmt = BigDecimal.ZERO;
                        }
                        ex.setDutyMaxPay(maxAmt);
                    }
                });
            }
        });
        return policyList;
    }

    @Override
    public List<RiskGroupDTO> getRiskGroupList(OcasPolicyQueryVO queryVO) {
        if(ListUtils.isEmptyList(queryVO.getIdPlyRiskPropertyList())){
            queryVO.setIdPlyRiskPropertyList(null);
        }
        List<CaseRiskPropertyDTO> riskPropertyList = ocasMapper.getRiskPropertyList(queryVO);
        List<PolicyGroupDTO> policyGroupList = riskPropertyService.buildPlyGroupDTO(riskPropertyList,null);
        if(ListUtils.isNotEmpty(policyGroupList)){
            return policyGroupList.get(0).getRiskGroupList();
        }
        return new ArrayList<>();
    }
}
