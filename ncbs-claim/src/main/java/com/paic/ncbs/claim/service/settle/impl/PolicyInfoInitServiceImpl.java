package com.paic.ncbs.claim.service.settle.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.constant.PolicyConstant;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.enums.DigitalProductTypeEnum;
import com.paic.ncbs.claim.common.enums.GasTypeEnum;
import com.paic.ncbs.claim.common.enums.PolicyStatusEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ahcs.*;
import com.paic.ncbs.claim.dao.entity.checkloss.BaseRimsOpencoverTreatyEntity;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.*;
import com.paic.ncbs.claim.model.dto.checkloss.SmallTermDTO;
import com.paic.ncbs.claim.model.dto.doc.DocumentDTO;
import com.paic.ncbs.claim.model.dto.doc.DocumentGroupDTO;
import com.paic.ncbs.claim.model.dto.ocas.PlyApplyFreeze;
import com.paic.ncbs.claim.model.dto.ocas.PropertyDTO;
import com.paic.ncbs.claim.model.dto.pay.PayInfoDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.riskppt.ReportRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.ClmsPolicyHistoryUwInfoDTO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.PolicyUwHistoryDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.taskdeal.RiskObjectDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.RiskPackageDTO;
import com.paic.ncbs.claim.service.checkloss.BaseRimsOpencoverTreatyService;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.PolicyInfoInitService;
import com.paic.ncbs.policy.dto.RiskPropsubGroupDTO;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.SettleConst.BUSINESS_TYPE_ORG;

@Service
public class PolicyInfoInitServiceImpl implements PolicyInfoInitService {

    @Autowired
    private BaseRimsOpencoverTreatyService baseRimsOpencoverTreatyService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    @Value("${pos.api.enable:true}")
    private boolean posApiEnable;

    @SuppressWarnings({"unchecked", "rawtypes"})
    private AhcsPolicyDomainDTO initPolicyInfoPas(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map policyInfoMap) {
        AhcsPolicyInfoEntity ahcsPolicyInfo = new AhcsPolicyInfoEntity();
        PolicyInfoExDTO policyInfoExDTO = new PolicyInfoExDTO();
        ahcsPolicyDomainDTO.setPolicyInfoExDTO(policyInfoExDTO);

        Map extendInfo = (Map) policyInfoMap.get("extendInfo");
        if (extendInfo != null) {
            if (policyInfoMap.get("batchNo") != null) {
                ahcsPolicyInfo.setBatchNo((String) policyInfoMap.get("batchNo"));
            } else {
                ahcsPolicyInfo.setBatchNo((String) extendInfo.get("batchNo"));
            }
            ahcsPolicyInfo.setRemark((String) extendInfo.get("remark"));
            ahcsPolicyInfo.setIsPolicyBeforePayfee((String) extendInfo.get("isPolicyBeforePayfee"));
            ahcsPolicyInfo.setIsFacultativeBusiness((String) extendInfo.get("isFacultativeBusiness"));

            ahcsPolicyInfo.setTotalDutyLimit(BigDecimalUtils.convertToBigDecimal(extendInfo.get("totalDutyLimit")));
            ahcsPolicyInfo.setOnceDutyLimit(BigDecimalUtils.convertToBigDecimal(extendInfo.get("onceDutyLimit")));
            ahcsPolicyInfo.setTotalLawDutyLimit(BigDecimalUtils.convertToBigDecimal(extendInfo.get("totalLawDutyLimit")));
            ahcsPolicyInfo.setOnceLawDutyLimit(BigDecimalUtils.convertToBigDecimal(extendInfo.get("onceLawDutyLimit")));
            ahcsPolicyInfo.setRemitCondition(StringUtils.stripToEmpty((String) extendInfo.get("remitCondition")));
            ahcsPolicyInfo.setPolicySpecialPromise(StringUtils.stripToEmpty((String) extendInfo.get("policySpecialPromise")));
            ahcsPolicyInfo.setProsecutionPeriod((Integer) extendInfo.get("prosecutionPeriod"));
            ahcsPolicyInfo.setExtendReportDate((Integer) extendInfo.get("extendReportDate"));
        }

        Map baseInfoMap = (Map) policyInfoMap.get("baseInfo");
        if (baseInfoMap == null) {
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("保单基本信息为空"));
        }

        if (StringUtils.stripToEmpty((String) baseInfoMap.get("status")).equals(PolicyStatusEnum.COMPANY_TERMINATE.getType()) && !"01".equals(ahcsPolicyDomainDTO.getInvokePurpose())) {
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("保单已失效，不可报案"));
        }

        String policyNo = StringUtils.stripToEmpty((String) baseInfoMap.get("policyNo"));
        ahcsPolicyInfo.setPolicyNo(policyNo);
        //todo tzj 标的信息，新理赔保持原逻辑，global由接口提供
        if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
            ahcsPolicyDomainDTO.setTargetType(String.valueOf(baseInfoMap.get("targetType")));
        }else{
            ahcsPolicyDomainDTO.setTargetType(ocasMapper.getTargetType(policyNo));
        }
        //todo tzj 标的类型存入基本信息表，单标的。。。 policyInfo表没加targetType字段，当前只在类中添加，是否需要再看
        ahcsPolicyInfo.setTargetType(ahcsPolicyDomainDTO.getTargetType());
        String generateFlag = StringUtils.stripToEmpty((String) baseInfoMap.get("generateFlag"));
        ahcsPolicyInfo.setGenerateFlag(generateFlag);

        ahcsPolicyInfo.setProductVersion(StringUtils.stripToEmpty((String) baseInfoMap.get("productVersion")));

        String acceptInsuranceDate = null;
        if (baseInfoMap.get("acceptInsuranceDate") != null) {
            acceptInsuranceDate = StringUtils.stripToEmpty(String.valueOf(baseInfoMap.get("acceptInsuranceDate")));
        }

        String insuranceBeginDate = null;
        String insuranceEndDate = null;
        if (baseInfoMap.get("insuranceBeginDate") != null) {
            insuranceBeginDate = StringUtils.stripToEmpty(baseInfoMap.get("insuranceBeginDate").toString());
        }
        if (baseInfoMap.get("insuranceEndDate") != null) {
            insuranceEndDate = StringUtils.stripToEmpty(baseInfoMap.get("insuranceEndDate").toString());
        }

        String underwriteDate = null;
        if (baseInfoMap.get("underwriteDate") != null) {
            underwriteDate = StringUtils.stripToEmpty(String.valueOf(baseInfoMap.get("underwriteDate")));
        }
        Long number = 1L;
        try {
            if (StringUtils.isNotEmpty(acceptInsuranceDate)) {
                if (number != null && number > 0 || PolicyConstant.EBCS.equals(ahcsPolicyDomainDTO.getPolicySystem())) {
                    ahcsPolicyInfo.setAcceptInsuranceDate(RapeDateUtil.parseToFormatDate(acceptInsuranceDate, RapeDateUtil.FULL_DATE_STR));
                } else {
                    ahcsPolicyInfo.setAcceptInsuranceDate(RapeDateUtil.stringFormatToParseDate(acceptInsuranceDate, RapeDateUtil.FULL_DATE_STR));
                }
            }
            if (StringUtils.isNotEmpty(insuranceBeginDate)) {
                Date insuranceBeginTime = null;
                if (number != null && number > 0 || PolicyConstant.EBCS.equals(ahcsPolicyDomainDTO.getPolicySystem())) {
                    insuranceBeginTime = RapeDateUtil.parseToFormatDate(insuranceBeginDate, RapeDateUtil.FULL_DATE_STR);
                } else {
                    insuranceBeginTime = RapeDateUtil.stringFormatToParseDate(insuranceBeginDate, RapeDateUtil.FULL_DATE_STR);
                }
                ahcsPolicyInfo.setInsuranceBeginTime(insuranceBeginTime);
            }
            if (StringUtils.isNotEmpty(insuranceEndDate)) {
                Date insuranceEndTime = null;
                if (number != null && number > 0 || PolicyConstant.EBCS.equals(ahcsPolicyDomainDTO.getPolicySystem())) {
                    insuranceEndTime = RapeDateUtil.parseToFormatDate(insuranceEndDate, RapeDateUtil.FULL_DATE_STR);
                } else {
                    insuranceEndTime = RapeDateUtil.stringFormatToParseDate(insuranceEndDate, RapeDateUtil.FULL_DATE_STR);
                }
                ahcsPolicyInfo.setInsuranceEndTime(insuranceEndTime);
            }
            if (StringUtils.isNotEmpty(underwriteDate)) {
                if (number != null && number > 0 || PolicyConstant.EBCS.equals(ahcsPolicyDomainDTO.getPolicySystem())) {
                    ahcsPolicyInfo.setUnderwriteDate(RapeDateUtil.parseToFormatDate(underwriteDate, RapeDateUtil.FULL_DATE_STR));
                } else {
                    ahcsPolicyInfo.setUnderwriteDate(RapeDateUtil.stringFormatToParseDate(underwriteDate, RapeDateUtil.FULL_DATE_STR));
                }
            }
        } catch (ParseException e) {
            LogUtil.info("INTF抄单接口返回数据转化时间格式异常");
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("{转化INTF PolicyInfo数据模型(保单基本数据) 时间}"));
        }

        ahcsPolicyInfo.setDepartmentCode(StringUtils.stripToEmpty((String) baseInfoMap.get("departmentCode")));
//        ahcsPolicyInfo.setDepartmentCodeNew(StringUtils.stripToEmpty((String) baseInfoMap.get("departmentCode")));

        ahcsPolicyInfo.setPolicyStatus(StringUtils.stripToEmpty((String) baseInfoMap.get("status")));

        ahcsPolicyInfo.setProductCode(StringUtils.stripToEmpty((String) baseInfoMap.get("productCode")));

        ahcsPolicyInfo.setProductName(StringUtils.stripToEmpty((String) baseInfoMap.get("productName")));

        ahcsPolicyInfo.setOrgProductCode(StringUtils.stripToEmpty((String) baseInfoMap.get("orgProductCode")));

        ahcsPolicyInfo.setOrgProductName(StringUtils.stripToEmpty((String) baseInfoMap.get("orgProductName")));

        ahcsPolicyInfo.setBusinessType(StringUtils.stripToEmpty((String) baseInfoMap.get("businessType")));

        ahcsPolicyInfo.setRelatedType(StringUtils.stripToEmpty((String) baseInfoMap.get("relatedType")));

        String totalAgreePremium = null;
        if (baseInfoMap.get("totalAgreePremium") != null) {
            totalAgreePremium = (StringUtils.stripToEmpty(String.valueOf(baseInfoMap.get("totalAgreePremium"))));
            ahcsPolicyInfo.setTotalAgreePremium("".equals(totalAgreePremium) ? null : new BigDecimal(totalAgreePremium));
        }

        String totalActualPremium = null;
        if (baseInfoMap.get("totalActualPremium") != null) {
            totalActualPremium = StringUtils.stripToEmpty(String.valueOf(baseInfoMap.get("totalActualPremium")));
            ahcsPolicyInfo.setTotalActualPremium("".equals(totalActualPremium) ? null : new BigDecimal(totalActualPremium));
        }

        ahcsPolicyInfo.setPremiumCurrencyCode(StringUtils.stripToEmpty((String) baseInfoMap.get("premiumCurrencyCode")));

        ahcsPolicyInfo.setAmountCurrencyCode(StringUtils.stripToEmpty((String) baseInfoMap.get("amountCurrencyCode")));

        String dataSource = StringUtils.stripToEmpty((String) baseInfoMap.get("dataSource"));
        ahcsPolicyInfo.setDataSource(dataSource);

        ahcsPolicyInfo.setCoinsuranceMark(StringUtils.stripToEmpty((String) baseInfoMap.get("coinsuranceMark")));

        if (baseInfoMap.get("totalInsuredAmount") != null) {
            String totalInsuredAmount = StringUtils.stripToEmpty(baseInfoMap.get("totalInsuredAmount").toString());
            ahcsPolicyInfo.setTotalInsuredAmount("".equals(totalInsuredAmount) ? null : new BigDecimal(totalInsuredAmount));
        }

        ahcsPolicyInfo.setSystemId(StringUtils.stripToEmpty((String) baseInfoMap.get("systemId")));

        ahcsPolicyInfo.setEndorseSystemId(StringUtils.stripToEmpty((String) baseInfoMap.get("endorseSystemId")));

        ahcsPolicyInfo.setRenewalType(StringUtils.stripToEmpty((String) baseInfoMap.get("renewalType")));

        ahcsPolicyInfo.setLastPolicyNo(StringUtils.stripToEmpty((String) baseInfoMap.get("lastPolicyNo")));

        ahcsPolicyInfo.setPayTermNo(MapUtils.getInteger(baseInfoMap,"payTermNo"));

        ahcsPolicyInfo.setProductClass(StringUtils.stripToEmpty((String) baseInfoMap.get("productClass")));
        ahcsPolicyInfo.setEndorseNo(StringUtils.stripToEmpty((String) baseInfoMap.get("endorseNo")));

        List selfCardActivateDTOList = (List) policyInfoMap.get("selfCardActivateDTOList");
        if (RapeCheckUtil.isNotEmpty(selfCardActivateDTOList)) {
            Map<String, Object> selfCardActivateDTO = (Map<String, Object>) selfCardActivateDTOList.get(CommonConstant.ZERO);

            ahcsPolicyInfo.setSelfCardNo((String) selfCardActivateDTO.get("selfCardNo"));

            ahcsPolicyInfo.setPolicyCerNo((String) selfCardActivateDTO.get("elecSubPolicyNo"));
            Date insuranceBeginTime = null;
            Date insuranceEndTime = null;
            try {
                if (number != null && number > 0 || PolicyConstant.EBCS.equals(ahcsPolicyDomainDTO.getPolicySystem())) {
                    insuranceBeginTime = RapeDateUtil.parseToFormatDate((String) selfCardActivateDTO.get("insuranceBeginTime"), RapeDateUtil.FULL_DATE_STR);
                    insuranceEndTime = RapeDateUtil.parseToFormatDate((String) selfCardActivateDTO.get("insuranceEndTime"), RapeDateUtil.FULL_DATE_STR);
                } else {
                    insuranceBeginTime = new Date((Long) selfCardActivateDTO.get("insuranceBeginTime"));
                    insuranceEndTime = new Date((Long) selfCardActivateDTO.get("insuranceEndTime"));
                }
            } catch (Exception e) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("时间格式异常"));
            }
            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setInsuranceBeginTime(insuranceBeginTime);
            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setInsuranceEndTime(insuranceEndTime);
        }

        Map reinsuranceInfoMap = (Map) policyInfoMap.get("reinsuranceInfo");
        if (reinsuranceInfoMap != null && !reinsuranceInfoMap.isEmpty()) {

            ahcsPolicyInfo.setReplyCode((String) reinsuranceInfoMap.get("facultativeNo"));
            if (StringUtils.isNotEmpty(ahcsPolicyInfo.getReplyCode())) {
                BaseRimsOpencoverTreatyEntity rimsOpencoverTreatyEntity = baseRimsOpencoverTreatyService
                        .getInfoByTreatyNo(ahcsPolicyInfo.getReplyCode());
                if (rimsOpencoverTreatyEntity != null) {
                    ahcsPolicyInfo.setReplyName(rimsOpencoverTreatyEntity.getTreatyName());
                }
            }
            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setOpencoverNo(ahcsPolicyInfo.getReplyCode());
            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setTreatyName(ahcsPolicyInfo.getReplyName());
        }

        List insuranceNotificationList = (List) policyInfoMap.get("insuranceNotificationList");
        if (RapeCheckUtil.isListNotEmpty(insuranceNotificationList)) {
            for (int i = 0; i < insuranceNotificationList.size(); i++) {
                Map<String, Object> insuranceNotification = (Map<String, Object>) insuranceNotificationList.get(i);
                InsuranceNotificationDTO insuranceNotificationDTO = new InsuranceNotificationDTO();
                insuranceNotificationDTO.setNotificationResult((String) insuranceNotification.get("notificationResult"));
                insuranceNotificationDTO.setNotificationType((String) insuranceNotification.get("notificationType"));
                insuranceNotificationDTO.setNotificationNoDesc((String) insuranceNotification.get("notificationNoDesc"));
                insuranceNotificationDTO.setNotificationValue((String) insuranceNotification.get("notificationValue"));
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceNotificationDTOs().add(insuranceNotificationDTO);
            }
        }

        Map saleMap = (Map) policyInfoMap.get("saleInfo");
        if (saleMap != null) {
            List agentInfoList = (List) saleMap.get("agentInfoList");
            if (RapeCheckUtil.isListNotEmpty(agentInfoList)) {
                for (int i = 0; i < agentInfoList.size(); i++) {
                    Map<String, Object> agentInfo = (Map<String, Object>) agentInfoList.get(i);
                    ahcsPolicyDomainDTO.getPolicyInfoExDTO().getAgentNames().add((String) agentInfo.get("agentName"));
                }

                Map<String, Object> agentInfo = (Map<String, Object>) agentInfoList.get(0);
                ahcsPolicyInfo.setAgentBrokerCode((String) agentInfo.get("agentCode"));
                ahcsPolicyInfo.setAgentBrokerName((String) agentInfo.get("agentName"));

                ahcsPolicyInfo.setAgentBrokerType(BaseConstant.STRING_1);

            }
            List brokerInfoList = (List) saleMap.get("brokerInfoList");
            if (RapeCheckUtil.isNotEmpty(brokerInfoList)) {
                for (int i = 0; i < brokerInfoList.size(); i++) {
                    Map<String, Object> brokerInfo = (Map<String, Object>) brokerInfoList.get(i);
                    ahcsPolicyDomainDTO.getPolicyInfoExDTO().getBrokerNames().add((String) brokerInfo.get("brokerName"));
                }

                Map<String, Object> brokerInfo = (Map<String, Object>) brokerInfoList.get(0);
                ahcsPolicyInfo.setAgentBrokerCode((String) brokerInfo.get("brokerCode"));
                ahcsPolicyInfo.setAgentBrokerName((String) brokerInfo.get("brokerName"));

                ahcsPolicyInfo.setAgentBrokerType(BaseConstant.STRING_2);

            }

            List partnerInfoList = (List) saleMap.get("partnerInfoList");
            if (RapeCheckUtil.isNotEmpty(partnerInfoList)) {

                Map<String, Object> partnerInfo = (Map<String, Object>) partnerInfoList.get(0);
                ahcsPolicyInfo.setPartnerCode(StringUtils.stripToEmpty((String) partnerInfo.get("partnerCode")));
                ahcsPolicyInfo.setPartnerName(StringUtils.stripToEmpty((String) partnerInfo.get("partnerName")));
            }
            if(Objects.nonNull(saleMap.get("performanceAttributionCode"))) {
                ahcsPolicyInfo.setDepartmentCodeNew(StringUtils.stripToEmpty((String) saleMap.get("performanceAttributionCode")));
            }
        }

        String applyDate = null;
        if (saleMap != null && saleMap.get("applyDate") != null) {
            applyDate = StringUtils.stripToEmpty(saleMap.get("applyDate").toString());
        }
        if (StringUtils.isNotEmpty(applyDate)) {
            try {
                if (number != null && number > 0 || PolicyConstant.EBCS.equals(ahcsPolicyDomainDTO.getPolicySystem())) {
                    ahcsPolicyInfo.setApplyDate(RapeDateUtil.parseToFormatDate(applyDate, RapeDateUtil.FULL_DATE_STR));
                } else {
                    ahcsPolicyInfo.setApplyDate(RapeDateUtil.stringFormatToParseDate(applyDate, RapeDateUtil.FULL_DATE_STR));
                }
            } catch (ParseException e) {
                LogUtil.info("INTF抄单接口返回数据转化时间格式异常");
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("{转化INTF PolicyInfo数据模型(保单基本数据) 时间}"));
            }
        }

        if (saleMap != null && RapeCheckUtil.isListNotEmpty((List)saleMap.get("employeeInfoList"))) {
            List employeeInfoList = (List) saleMap.get("employeeInfoList");
            Map<String, Object> employeeDTO = (Map) employeeInfoList.get(0);

            ahcsPolicyInfo.setSaleAgentCode(StringUtils.stripToEmpty((String) employeeDTO.get("employeeCode")));

            ahcsPolicyInfo.setSaleAgentName(StringUtils.stripToEmpty((String) employeeDTO.get("employeeName")));
        }

        if (saleMap != null) {

            ahcsPolicyInfo.setBusinessSourceCode(StringUtils.stripToEmpty((String) saleMap.get("businessSourceCode")));

            ahcsPolicyInfo.setBusinessSourceName(StringUtils.stripToEmpty((String) saleMap.get("businessSourceName")));

            ahcsPolicyInfo.setBusinessSourceDetailCode(StringUtils.stripToEmpty((String) saleMap.get("businessSourceDetailCode")));

            ahcsPolicyInfo.setBusinessSourceDetailName(StringUtils.stripToEmpty((String) saleMap.get("businessSourceDetailName")));

            ahcsPolicyInfo.setChannelSourceCode(StringUtils.stripToEmpty((String) saleMap.get("channelSourceCode")));

            ahcsPolicyInfo.setChannelSourceName(StringUtils.stripToEmpty((String) saleMap.get("channelSourceName")));

            ahcsPolicyInfo.setChannelSourceDetailCode(StringUtils.stripToEmpty((String) saleMap.get("channelSourceDetailCode")));

            ahcsPolicyInfo.setChannelSourceDetailName(StringUtils.stripToEmpty((String) saleMap.get("channelSourceDetailName")));

            Map<String, Object> primaryIntroducerInfo = (Map<String, Object>) saleMap.get("primaryIntroducerInfo");
            String primaryIntroducerCode = "";
            if (primaryIntroducerInfo != null) {
                primaryIntroducerCode = (String) primaryIntroducerInfo.get("primaryIntroducerCode");
            }
            if (StringUtils.isNotEmpty(primaryIntroducerCode)) {
                ahcsPolicyInfo.setPrimaryIntroducerCode(primaryIntroducerCode);
            }
        }

        List riskGroupInfoList = (List) policyInfoMap.get("riskGroupInfoList");
        if (RapeCheckUtil.isListNotEmpty(riskGroupInfoList)) {

            Map<String, Object> riskGroupInfoMap = (Map) riskGroupInfoList.get(0);

            if ((Integer) riskGroupInfoMap.get("virtualRiskNum") != null) {
                long virtualRiskNum = Long.valueOf((Integer) riskGroupInfoMap.get("virtualRiskNum"));
                ahcsPolicyInfo.setVirtualTargetNum(virtualRiskNum);
            }
            Map groupDetail = JSON.parseObject((String) riskGroupInfoMap.get("groupDetail"), Map.class);
            if (groupDetail != null) {
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setWorkingArea((String) groupDetail.get("workingArea"));
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setSecurityMark((String) groupDetail.get("securityMarkName"));
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setWarZone((String) groupDetail.get("warZoneName"));
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setProjectType((String) groupDetail.get("projectType"));
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setEmpower((String) groupDetail.get("empower"));
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setCrisisPackage((String) groupDetail.get("crisisPackage"));
                if (groupDetail.get("crisisPackageLimit") != null) {
                    ahcsPolicyDomainDTO.getPolicyInfoExDTO().setCrisisPackageLimit(String.valueOf(groupDetail.get("crisisPackageLimit")));
                }

                if (groupDetail.get("smallTerms") != null) {
                    try {
                        List<SmallTermDTO> smalls = JSONObject.parseArray(JSONObject.toJSONString(groupDetail.get("smallTerms")), SmallTermDTO.class);
                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().setSmallTerms(smalls);
                    } catch (Exception e) {
                        LogUtil.info("团意小条款抄单DTO构建异常：" + e.getMessage());
                        LogUtil.info("团意小条款抄单DTO构建异常,抄单返回smallTerms：" + groupDetail.get("smallTerms"));
                    }
                }
            }
            ahcsPolicyDomainDTO.setProductClass((String) riskGroupInfoMap.get("productClass"));

            String packageCode = "";

            if ("03".equals(ahcsPolicyDomainDTO.getProductClass()) || "02".equals(ahcsPolicyDomainDTO.getProductClass())) {
                packageCode = (String) riskGroupInfoMap.get("productPackageType");
                RiskPackageDTO riskPackageDTO = new RiskPackageDTO();
                riskPackageDTO.setPackageCode(packageCode);
                ahcsPolicyDomainDTO.getRiskPackageDTOs().add(riskPackageDTO);
            }

            if ("18".equals(ahcsPolicyDomainDTO.getProductClass())) {
                List riskPackageInfoList = (List) riskGroupInfoMap.get("riskPackageInfoList");
                if (RapeCheckUtil.isNotEmpty(riskPackageInfoList)) {
                    for (int r = 0; r < riskPackageInfoList.size(); r++) {
                        Map<String, Object> riskPackageInfo = (Map<String, Object>) riskPackageInfoList.get(r);
                        RiskPackageDTO riskPackage = new RiskPackageDTO();
                        String productPackageCode = (String) riskPackageInfo.get("productPackageCode");
                        riskPackage.setPackageCode(productPackageCode);
                        ahcsPolicyDomainDTO.getRiskPackageDTOs().add(riskPackage);
                        if (r == 0) {
                            packageCode = productPackageCode;
                        } else {
                            packageCode = productPackageCode + "," + packageCode;
                        }
                    }
                }
            }
            ahcsPolicyInfo.setProductPackageType(packageCode);
        }
        //添加是否是家庭单、家庭单单号
        ahcsPolicyInfo.setIsFamily(ahcsPolicyDomainDTO.getIsFamily());
        ahcsPolicyInfo.setOnlineOrderNo(ahcsPolicyDomainDTO.getonlineOrderNo());
        ahcsPolicyInfo.setProfitCenter(ahcsPolicyDomainDTO.getProfitCenter());
        ahcsPolicyDomainDTO.setAhcsPolicyInfo(ahcsPolicyInfo);

        List applicantList = (List) policyInfoMap.get("applicantInfoList");
        if (RapeCheckUtil.isListNotEmpty(applicantList)) {
            List<AhcsPolicyHolderEntity> policyHolderList = new ArrayList<AhcsPolicyHolderEntity>();
            for (int i = 0; i < applicantList.size(); i++) {
                Map applicant = (Map) applicantList.get(i);
                AhcsPolicyHolderEntity policyHolder = new AhcsPolicyHolderEntity();

                policyHolder.setName(StringUtils.stripToEmpty((String) applicant.get("name")));

                policyHolder.setAddress(StringUtils.stripToEmpty((String) applicant.get("address")));

                policyHolder.setTelephone(StringUtils.stripToEmpty((String) applicant.get("mobileTelephone")));

                policyHolder.setCertificateNo(StringUtils.stripToEmpty((String) applicant.get("certificateNo")));

                policyHolder.setCertificateType(StringUtils.stripToEmpty((String) applicant.get("certificateType")));

                policyHolder.setEmail(StringUtils.stripToEmpty((String) applicant.get("email")));

                policyHolder.setPersonnelType((StringUtils.stripToEmpty((String) applicant.get("personnelType"))));

                policyHolder.setClientNo((StringUtils.stripToEmpty((String) applicant.get("clientNo"))));
                policyHolderList.add(policyHolder);
            }
            ahcsPolicyDomainDTO.setAhcsPolicyHolder(policyHolderList);
        }

        List<String> groupIds = new ArrayList<>();
        List<DocumentGroupDTO> documentGroupDTOs = new ArrayList<>();
        List attachmentGroupList = (List) policyInfoMap.get("attachmentGroupList");
        String documentGroupIDs = "";
        if (RapeCheckUtil.isNotEmpty(attachmentGroupList)) {
            int size = 100;
            if (attachmentGroupList.size() < size) {
                size = attachmentGroupList.size();
            }
            for (int i = 0; i < size; i++) {
                Map<String, Object> attachmentGroup = (Map<String, Object>) attachmentGroupList.get(i);
                String documentGroupId = (String) attachmentGroup.get("documentGroupId");
                groupIds.add(documentGroupId);
                if (i == 0) {
                    documentGroupIDs = documentGroupId;
                } else {
                    documentGroupIDs = documentGroupIDs + "," + documentGroupId;
                }
                DocumentGroupDTO documentGroupDTO = new DocumentGroupDTO();
                documentGroupDTOs.add(documentGroupDTO);
                documentGroupDTO.setDocumentGroupId(documentGroupId);

                List<DocumentDTO> documentDTOs = new ArrayList<>();
                documentGroupDTO.setDocumentDTOs(documentDTOs);
                List documentInfoList = (List) attachmentGroup.get("documentInfoList");
                if (RapeCheckUtil.isNotEmpty(documentInfoList)) {
                    for (int j = 0, length = documentInfoList.size(); j < length; j++) {
                        DocumentDTO documentDTO = new DocumentDTO();
                        documentDTOs.add(documentDTO);
                        Map<String, Object> document = (Map<String, Object>) documentInfoList.get(j);
                        documentDTO.setUploadPath((String) document.get("uploadPath"));
                    }
                }
            }
        }
        policyInfoExDTO.setGroupIds(groupIds);
        policyInfoExDTO.setDocumentGroupDTOs(documentGroupDTOs);
        ahcsPolicyDomainDTO.getAhcsPolicyInfo().setGroupId(documentGroupIDs);
        return ahcsPolicyDomainDTO;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private void initPolicyPayInfo(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map policyInfoMap) {

        List payInfoList = (List) policyInfoMap.get("payInfoList");
//        LogUtil.info("---- 获取支付信息 ----{}", JSON.toJSON(payInfoList));

        List<PayInfoDTO> paidList = new ArrayList<>();

        List<PayInfoDTO> unPaidList = new ArrayList<>();

        BigDecimal totalPaidPremium = new BigDecimal(0);
        if (RapeCheckUtil.isNotEmpty(payInfoList)) {
            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setToalatPeriod(payInfoList.size());
            for (int i = 0; i < payInfoList.size(); i++) {
                Map<String, Object> payInfo = (Map<String, Object>) payInfoList.get(i);
                PayInfoDTO payInfoDTO = new PayInfoDTO();
                if (payInfo.get("actualPremium") != null) {
                    payInfoDTO.setActualPremium(new BigDecimal(String.valueOf(payInfo.get("actualPremium"))));
                }
                if (payInfo.get("agreePremium") != null) {
                    payInfoDTO.setAgreePremium(new BigDecimal(String.valueOf(payInfo.get("agreePremium"))));
                }
                payInfoDTO.setBankAccountNo(StringUtils.stripToEmpty((String) payInfo.get("bankAccountNo")));
                payInfoDTO.setBankAttribute(StringUtils.stripToEmpty((String) payInfo.get("bankAttribute")));
                payInfoDTO.setBankCode(StringUtils.stripToEmpty((String) payInfo.get("bankCode")));
                payInfoDTO.setBankHeadquartersCode(StringUtils.stripToEmpty((String) payInfo.get("bankHeadquartersCode")));
                payInfoDTO.setCurrencyCode(StringUtils.stripToEmpty((String) payInfo.get("currencyCode")));
                payInfoDTO.setNoticeNo(StringUtils.stripToEmpty((String) payInfo.get("noticeNo")));
                payInfoDTO.setNoticeStatus(StringUtils.stripToEmpty((String) payInfo.get("noticeStatus")));
                payInfoDTO.setPaymentApproach(StringUtils.stripToEmpty((String) payInfo.get("paymentApproach")));

                try {
                    if (payInfo.get("paymentBeginDate") != null) {
                        String date1 = (String) payInfo.get("paymentBeginDate");
                        payInfoDTO.setPaymentBeginDate(RapeDateUtil.parseToFormatDate(date1, RapeDateUtil.FULL_DATE_STR));
                    }
                    if (payInfo.get("paymentEndDate") != null) {
                        String paymentEnd = (String) payInfo.get("paymentEndDate");
                        payInfoDTO.setPaymentEndDate(RapeDateUtil.parseToFormatDate(paymentEnd, RapeDateUtil.FULL_DATE_STR));
                    }
                    String gainDate = (String) payInfo.get("gainDate");
                    if (gainDate != null) {
                        payInfoDTO.setGainDate(RapeDateUtil.parseToFormatDate(gainDate, RapeDateUtil.FULL_DATE_STR));
                    }
                    if (BaseConstant.STRING_1.equals(String.valueOf(payInfo.get("termNo")))) {
                        String gainConfirmDate = (String) payInfo.get("gainConfirmDate");
                        if (gainConfirmDate != null) {
                            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPaymentDate(RapeDateUtil.parseToFormatDate(gainConfirmDate, RapeDateUtil.FULL_DATE_STR));
                        } else if (gainConfirmDate == null && gainDate != null) {
                            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPaymentDate(RapeDateUtil.parseToFormatDate(gainDate, RapeDateUtil.FULL_DATE_STR));
                        }
                    }

                } catch (ParseException e) {
                    throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("日期格式转换错误"));
                }

                Integer termNo = MapUtils.getInteger(payInfo, "termNo");
                if (termNo==null){
                    termNo = 0 ;
                }
                payInfoDTO.setPaymentPath(StringUtils.stripToEmpty((String) payInfo.get("paymentPath")));
                payInfoDTO.setPaymentPersonName(StringUtils.stripToEmpty((String) payInfo.get("paymentPersonName")));
                payInfoDTO.setStatus(StringUtils.stripToEmpty((String) payInfo.get("status")));
                payInfoDTO.setTermNo(termNo);
                if (StringUtils.isNotEmpty(payInfoDTO.getNoticeNo()) && ("71".equals(payInfoDTO.getNoticeStatus()) || "72".equals(payInfoDTO.getNoticeStatus())
                        || "2".equals(payInfoDTO.getNoticeStatus()))) {
                    paidList.add(payInfoDTO);
                    if (payInfoDTO.getActualPremium() != null) {
                        totalPaidPremium = totalPaidPremium.add(payInfoDTO.getActualPremium());
                    }
                } else {
                    unPaidList.add(payInfoDTO);
                }
                ahcsPolicyDomainDTO.getPayInfoList().add(payInfoDTO);
                if (termNo > 1) {
                    ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPaymentMethods("1");
                } else {
                    ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPaymentMethods("2");
                }
            }

            if (PolicyConstant.PRODUCT_CLASS_03.equals(ahcsPolicyDomainDTO.getProductClass()) && payInfoList.size() > 1) {
                ahcsPolicyDomainDTO.getAhcsPolicyInfo().setTotalActualPremium(totalPaidPremium);
                PayInfoDTO lastPaidInfo = paidList.stream().max(Comparator.comparing(PayInfoDTO::getTermNo)).orElse(null);
                if (lastPaidInfo != null) {
                    ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPaymentDate(lastPaidInfo.getGainDate());
                }
            }

            if (PolicyConstant.PRODUCT_CLASS_02.equals(ahcsPolicyDomainDTO.getProductClass()) && payInfoList.size() > 1) {
                ahcsPolicyDomainDTO.getAhcsPolicyInfo().setTotalActualPremium(totalPaidPremium);
                PayInfoDTO lastPaidInfo = paidList.stream().max(Comparator.comparing(PayInfoDTO::getTermNo)).orElse(null);
                if (lastPaidInfo != null) {
                    ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPaymentDate(lastPaidInfo.getGainDate());
                }
            }

//            LogUtil.info("已缴支付信息{}", JSONObject.toJSONString(paidList));
//            LogUtil.info("未缴支付信息{}", JSONObject.toJSONString(unPaidList));
            PayInfoDTO firstUnPaidInfo = unPaidList.stream().min(Comparator.comparing(PayInfoDTO::getTermNo)).orElse(null);
            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPaidPeriod(paidList.size());
            if (firstUnPaidInfo != null) {
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPaymentEndDate(firstUnPaidInfo.getPaymentEndDate());
            }
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private AhcsPolicyDomainDTO initPolicyRiskGroupIntfPas(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map policyInfoMap) {

        List riskGroupList = (List) policyInfoMap.get("riskGroupInfoList");
        if (RapeCheckUtil.isListNotEmpty(riskGroupList)) {
            for (int i = 0; i < riskGroupList.size(); i++) {
                Map riskGroupObject = (Map) riskGroupList.get(i);
                if (riskGroupObject.get("applyNum") == null) {
                    ahcsPolicyDomainDTO.setApplyNum(new BigDecimal(BaseConstant.INT_1));
                    ahcsPolicyDomainDTO.getAhcsPolicyInfo().setApplyNum(new BigDecimal(BaseConstant.INT_1));
                } else {
                    String applyNum = String.valueOf(riskGroupObject.get("applyNum"));
                    ahcsPolicyDomainDTO.setApplyNum(new BigDecimal(applyNum));
                    ahcsPolicyDomainDTO.getAhcsPolicyInfo().setApplyNum(new BigDecimal(applyNum));
                }

                Map<String, String> shareMap = this.initShareMap(ahcsPolicyDomainDTO, riskGroupObject);

                Map<String, String> riskPackageMap = this.initRiskPackageMap(riskGroupObject);

                this.initPlanInfo(ahcsPolicyDomainDTO, riskGroupObject, shareMap, riskPackageMap,null,null);

                this.initPropertyInfo(ahcsPolicyDomainDTO, riskGroupObject);
            }
        }
        if (StringUtils.isEmpty(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getSocialFlag())) {
            ahcsPolicyDomainDTO.getAhcsPolicyInfo().setSocialFlag(CommonConstant.NO_SOCIAL);
        }
        this.initAhcsPlanDutySum(ahcsPolicyDomainDTO);
        return ahcsPolicyDomainDTO;
    }

    private AhcsPolicyDomainDTO initPolicyRiskGroup(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map policyInfoMap, CopyPolicyQueryVO queryVO) {

        List riskGroupList = (List) policyInfoMap.get("riskGroupInfoList");
        boolean riskPersonIdMatch = StringUtils.isNotEmpty(queryVO.getRiskPersonId());
        boolean nameMatch = StringUtils.isNotEmpty(queryVO.getCertNo()) && StringUtils.isNotEmpty(queryVO.getClientName());
        // 退运险特殊处理
        boolean onlyNameMatch = StringUtils.isEmpty(queryVO.getCertNo()) && StringUtils.isNotEmpty(queryVO.getClientName());

        // 雇责险抄所有方案
        boolean isRiskProperty = riskPropertyService.isRiskProperty(ahcsPolicyDomainDTO.getTargetType(), ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProductClass());

        if (RapeCheckUtil.isListNotEmpty(riskGroupList)) {
            for (int i = 0; i < riskGroupList.size(); i++) {
                boolean riskGroupMatched = false;
                // 团单加减人的时候会用到
                String idRiskClass = null;
                String isTransferInsure = null;
                String transferInsurancePolicyNo = null;
                Date transferInsuranceEndDate = null;
                String transferInsuranceProductName = null;
                Map riskGroupObject = (Map) riskGroupList.get(i);
                List riskPersonInfoList = (List) riskGroupObject.get("riskPersonInfoList");
                //团高会出现方案下面没有被保人的场景，针对此场景跳过无被保人校验 begin
//                String applyMode = ocasMapper.getPolicyApplyMode(queryVO.getPolicyNo());
                if(ListUtils.isEmptyList(riskPersonInfoList)){
                    continue;
                }
                //团高会出现方案下面没有被保人的场景，针对此场景跳过无被保人校验 end
//                if(ListUtils.isEmptyList(riskPersonInfoList)){
//                    throw new GlobalBusinessException("保单无被保人");
//                }

                for (int j = 0; j < riskPersonInfoList.size(); j++) {
                    Map insurantInfoMap = (Map) riskPersonInfoList.get(j);
                    String id = MapUtils.getString(insurantInfoMap, "id");


                    if (riskPersonIdMatch) {
                        //传入id不为空就用id匹配
                        if(queryVO.getRiskPersonId().equals(id)){
                            riskGroupMatched = true;
                            idRiskClass = MapUtils.getString(insurantInfoMap, "idRiskClass");
                            isTransferInsure = MapUtils.getString(insurantInfoMap, "isTransferInsure");
                            transferInsurancePolicyNo = MapUtils.getString(insurantInfoMap, "transferInsurancePolicyNo");
                            try{
                                transferInsuranceEndDate =
                                        RapeDateUtil.parseToFormatDate(MapUtils.getString(insurantInfoMap, "transferInsuranceEndDate"), RapeDateUtil.FULL_DATE_STR);
                            } catch (ParseException e) {
                                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("转保止期格式转换错误"));
                            }
                            transferInsuranceProductName = MapUtils.getString(insurantInfoMap, "transferInsuranceProductName");
                            break;
                        }

                    }else if(nameMatch){
                        //传入id为空就用姓名+证件号匹配
                        String personName = MapUtils.getString(insurantInfoMap, "name");
                        String personCertNo = MapUtils.getString(insurantInfoMap, "certificateNo");
                        if(personName != null
//                                && personName.equals(queryVO.getClientName())
                                && personCertNo != null && personCertNo.equals(queryVO.getCertNo())){
                            riskGroupMatched = true;
                            idRiskClass = MapUtils.getString(insurantInfoMap, "idRiskClass");
                            isTransferInsure = MapUtils.getString(insurantInfoMap, "isTransferInsure");
                            transferInsurancePolicyNo = MapUtils.getString(insurantInfoMap, "transferInsurancePolicyNo");
                            try{
                                transferInsuranceEndDate =
                                        RapeDateUtil.parseToFormatDate(MapUtils.getString(insurantInfoMap, "transferInsuranceEndDate"), RapeDateUtil.FULL_DATE_STR);
                            } catch (ParseException e) {
                                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("转保止期格式转换错误"));
                            }
                            transferInsuranceProductName = MapUtils.getString(insurantInfoMap, "transferInsuranceProductName");
                            break;
                        }
                    }else if(onlyNameMatch) {
                        // 退运险特殊处理 没有被保险人其他信息
                        String personName = MapUtils.getString(insurantInfoMap, "name");
                        if(personName != null && personName.equals(queryVO.getClientName())){
                            riskGroupMatched = true;
                            idRiskClass = MapUtils.getString(insurantInfoMap, "idRiskClass");
                            isTransferInsure = MapUtils.getString(insurantInfoMap, "isTransferInsure");
                            transferInsurancePolicyNo = MapUtils.getString(insurantInfoMap, "transferInsurancePolicyNo");
                            try{
                                transferInsuranceEndDate =
                                        RapeDateUtil.parseToFormatDate(MapUtils.getString(insurantInfoMap, "transferInsuranceEndDate"), RapeDateUtil.FULL_DATE_STR);
                            } catch (ParseException e) {
                                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("转保止期格式转换错误"));
                            }
                            transferInsuranceProductName = MapUtils.getString(insurantInfoMap, "transferInsuranceProductName");
                            break;
                        }
                    }
                }
                if(!riskGroupMatched && !isRiskProperty){
                    continue;
                }
                ahcsPolicyDomainDTO.setIsTransferInsure(isTransferInsure);
                ahcsPolicyDomainDTO.setTransferInsurancePolicyNo(transferInsurancePolicyNo);
                ahcsPolicyDomainDTO.setTransferInsuranceEndDate(transferInsuranceEndDate);
                ahcsPolicyDomainDTO.setTransferInsuranceProductName(transferInsuranceProductName);
                ahcsPolicyDomainDTO.getAhcsPolicyInfo().setIsTransferInsure(isTransferInsure);
                if (riskGroupObject.get("applyNum") == null) {
                    ahcsPolicyDomainDTO.setApplyNum(new BigDecimal(BaseConstant.INT_1));
                    ahcsPolicyDomainDTO.getAhcsPolicyInfo().setApplyNum(new BigDecimal(BaseConstant.INT_1));
                } else {
                    String applyNum = String.valueOf(riskGroupObject.get("applyNum"));
                    ahcsPolicyDomainDTO.setApplyNum(new BigDecimal(applyNum));
                    ahcsPolicyDomainDTO.getAhcsPolicyInfo().setApplyNum(new BigDecimal(applyNum));
                }

                ahcsPolicyDomainDTO.getAhcsPolicyInfo().setRiskGroupType(MapUtils.getString(riskGroupObject, "riskGroupType"));
                Map<String, String> shareMap = this.initShareMap(ahcsPolicyDomainDTO, riskGroupObject);

                Map<String, String> riskPackageMap = this.initRiskPackageMap(riskGroupObject);

                this.initPlanInfo(ahcsPolicyDomainDTO, riskGroupObject, shareMap, riskPackageMap,idRiskClass,queryVO.getTime());

                this.initPropertyInfo(ahcsPolicyDomainDTO, riskGroupObject);
                //处理小条款信息
                this.dealTermContent(ahcsPolicyDomainDTO,riskGroupObject);
            }
        }
        if (StringUtils.isEmpty(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getSocialFlag())) {
            ahcsPolicyDomainDTO.getAhcsPolicyInfo().setSocialFlag(CommonConstant.NO_SOCIAL);
        }
        this.initAhcsPlanDutySum(ahcsPolicyDomainDTO);
        return ahcsPolicyDomainDTO;
    }



    private void initAhcsPlanDutySum(AhcsPolicyDomainDTO ahcsPolicyDomainDTO) {
        List<PolicyPlanDutySumDTO> policyPlanDutySumDTOs = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyDomainDTO.getAhcsPolicyPlanDTOs())) {
            for (AhcsPolicyPlanDTO ahcsPolicyPlanDTO : ahcsPolicyDomainDTO.getAhcsPolicyPlanDTOs()) {
                if (RapeCheckUtil.isNotEmpty(ahcsPolicyPlanDTO.getAhcsPolicyDutyDTOs())) {
                    for (AhcsPolicyDutyDTO ahcsPolicyDutyDTO : ahcsPolicyPlanDTO.getAhcsPolicyDutyDTOs()) {
                        PolicyPlanDutySumDTO policyPlanDutySumDTO = new PolicyPlanDutySumDTO();
                        policyPlanDutySumDTO.setPlanCode(ahcsPolicyPlanDTO.getAhcsPolicyPlan().getPlanCode());
                        policyPlanDutySumDTO.setPlanName(ahcsPolicyPlanDTO.getAhcsPolicyPlan().getPlanName());
                        policyPlanDutySumDTO.setDutyCode(ahcsPolicyDutyDTO.getAhcsPolicyDuty().getDutyCode());
                        policyPlanDutySumDTO.setDutyName(ahcsPolicyDutyDTO.getAhcsPolicyDuty().getDutyName());
                        policyPlanDutySumDTO.setAmount(ahcsPolicyDutyDTO.getAhcsPolicyDuty().getDutyAmount());
                        policyPlanDutySumDTOs.add(policyPlanDutySumDTO);
                    }
                }
            }
            ahcsPolicyDomainDTO.setPolicyPlanDutySumDTOs(policyPlanDutySumDTOs);
        }
    }

    private Map<String, String> initShareMap(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map riskGroupObject) {
        List shareRelationInfoList = (List) riskGroupObject.get("shareRelationInfoList");
        Map<String, String> map = new HashMap<>();
        if (RapeCheckUtil.isListNotEmpty(shareRelationInfoList)) {
            for (int k = 0; k < shareRelationInfoList.size(); k++) {
                Map shareRelationInfoMap = (Map) shareRelationInfoList.get(k);
                map.put((String) shareRelationInfoMap.get("planCode"), (String) shareRelationInfoMap.get("groupCode"));
            }
        }
        List shareInfoList = (List) riskGroupObject.get("shareInfoList");

       /* if (RapeCheckUtil.isListNotEmpty(shareInfoList)) {
            Map shareInfoMap = (Map) shareInfoList.get(0);
            if ("riskShareAmount".equals(shareInfoMap.get("dutyAttrCode"))) {
                ahcsPolicyDomainDTO.getAhcsPolicyInfo().setShareInsuredAmount("1");
            } else {
                ahcsPolicyDomainDTO.getAhcsPolicyInfo().setShareInsuredAmount("0");
            }
        }*/
        return map;
    }

    private Map initRiskPackageMap(Map riskGroupObject) {
        Map<String, String> riskPackageMap = new HashMap<String, String>();
        List riskPackageInfoList = (List) riskGroupObject.get("riskPackageInfoList");
        if (RapeCheckUtil.isNotEmpty(riskPackageInfoList)) {
            for (int j = 0; j < riskPackageInfoList.size(); j++) {
                Map<String, Object> riskPackageInfo = (Map<String, Object>) riskPackageInfoList.get(j);
                String id = (String) riskPackageInfo.get("id");
                String applyNum;
                if (riskPackageInfo.get("applyNum") != null) {
                    applyNum = String.valueOf(riskPackageInfo.get("applyNum"));
                } else {
                    applyNum = "1";
                }
                riskPackageMap.put(id, applyNum);
            }
        }
        return riskPackageMap;
    }

    private void initPlanInfo(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map riskGroupObject, Map<String, String> map,
                              Map<String, String> riskPackageMap, String idRiskClass,String accidentDate) {
        List planInfoList = (List) riskGroupObject.get("planInfoList");
        RapeCheckUtil.checkListResultError(planInfoList, "保单中未查询到险种信息");
        if (RapeCheckUtil.isListNotEmpty(planInfoList)) {
            for (int b = 0; b < planInfoList.size(); b++) {
                Map planInfoMap = (Map) planInfoList.get(b);
                AhcsPolicyPlanDTO policyPlanDto = new AhcsPolicyPlanDTO();
                AhcsPolicyPlanEntity policyPlan = new AhcsPolicyPlanEntity();
                String cancelMark = StringUtils.stripToEmpty((String) planInfoMap.get("cancelMark"));

                if (CommonConstant.YES.equals(cancelMark)) {
                    continue;
                }

                String riskPackageId = StringUtils.stripToEmpty((String) planInfoMap.get("riskPackageId"));
                if (riskPackageMap.get(riskPackageId) != null) {
                    policyPlanDto.setApplyNum(new BigDecimal(riskPackageMap.get(riskPackageId)));
                } else {
                    policyPlanDto.setApplyNum(new BigDecimal(1));
                }
                policyPlan.setApplyNum(policyPlanDto.getApplyNum());

                //  BigDecimal taxRate = (BigDecimal) planInfoMap.get("taxRate");
                if (planInfoMap.get("taxRate") != null) {
                    String taxRate = (StringUtils.stripToEmpty(String.valueOf(planInfoMap.get("taxRate"))));
                    policyPlan.setTaxRate("".equals(taxRate) ? null : new BigDecimal(taxRate));
                } else {
                    policyPlan.setTaxRate(BigDecimal.ZERO);
                }

                policyPlan.setPlanCode(StringUtils.stripToEmpty((String) planInfoMap.get("planCode")));

                policyPlan.setGroupCode(map.get(policyPlan.getPlanCode()));

                policyPlan.setPlanName(StringUtils.stripToEmpty((String) planInfoMap.get("planName")));

                policyPlan.setOrgPlanCode(StringUtils.stripToEmpty((String) planInfoMap.get("orgPlanCode")));

                policyPlan.setOrgPlanName(StringUtils.stripToEmpty((String) planInfoMap.get("orgPlanName")));

                policyPlan.setRescueCompany(StringUtils.stripToEmpty((String) planInfoMap.get("rescueCompany")));
                // 和出单保持一致
                String isMain = StringUtils.stripToEmpty((String) planInfoMap.get("isMain"));
//                if (BaseConstant.STRING_0.equals(isMain)) {
//                    policyPlan.setIsMain(BaseConstant.UPPER_CASE_Y);
//                } else if (BaseConstant.STRING_1.equals(isMain)) {
//                    policyPlan.setIsMain(BaseConstant.UPPER_CASE_N);
//                }
                policyPlan.setIsMain(isMain);
                policyPlan.setIdPlyRiskGroup((String)planInfoMap.get("riskGroupId"));
                policyPlan.setRiskGroupNo((String)riskGroupObject.get("riskGroupNo"));
                policyPlan.setRiskGroupName((String)riskGroupObject.get("riskGroupName"));
                policyPlanDto.setAhcsPolicyPlan(policyPlan);

                this.initPolicyDuty4IntfPas(planInfoMap, policyPlanDto, ahcsPolicyDomainDTO,idRiskClass,accidentDate);
                if(!policyPlanDto.getAhcsPolicyDutyDTOs().isEmpty()) {
                    ahcsPolicyDomainDTO.getAhcsPolicyPlanDTOs().add(policyPlanDto);
                }
            }
            //判断出险日期是否在责任起止日期内
            Date insuranceBeginDate = ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceBeginTime();
            Date insuranceEndDate = ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceEndTime();
            if (StringUtils.isNotEmpty(accidentDate)) {
                try {
                    Date acciDate = DateUtils.parseToDate(accidentDate);
                    int prosecutionPeriod = Objects.nonNull(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProsecutionPeriod()) ? ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProsecutionPeriod() : 0;
                    int extendReportDate = Objects.nonNull(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getExtendReportDate()) ? ahcsPolicyDomainDTO.getAhcsPolicyInfo().getExtendReportDate() : 0;
                    //追溯期、报告期:保单起期-追溯期<=事故日期<=保单止期+报告期
                    if (DateUtils.addDate(DateUtils.beginOfDay(insuranceBeginDate),-prosecutionPeriod).after(acciDate) || DateUtils.addDate(DateUtils.endOfDay(insuranceEndDate),extendReportDate).before(acciDate)) {
                        //调整为list接收险种，最后判断出险日期
                        throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("出险日期不在保单下的责任起止时间范围内，请排查！"));
                    }
                } catch (ParseException e) {
                    throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("出险日期格式转换错误"));
                }
            }


        }
    }

    private void initPropertyInfo(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map riskGroupObject) {
        List riskPropertyInfoList = (List) riskGroupObject.get("riskPropertyInfoList");
        List<HouseInfoDTO> houseInfoDTOs = new ArrayList<>();
        if (RapeCheckUtil.isListNotEmpty(riskPropertyInfoList)) {
            if (ahcsPolicyDomainDTO.getReportRiskPropertyList() == null) {
                ahcsPolicyDomainDTO.setReportRiskPropertyList(Lists.newArrayList());
            }
            for (Object obj : riskPropertyInfoList) {
                Map riskPropertyInfo = (Map) obj;
                Map riskPropertyMap = (Map) riskPropertyInfo.get("riskPropertyMap");
                String riskDetail = (String) riskPropertyInfo.get("riskDetail");
                if(riskPropertyMap == null && riskDetail != null){
                    riskPropertyMap = JSON.parseObject(riskDetail,Map.class);
                }
                if (riskPropertyMap != null) {
                    PetDetailDTO petDetailDTO = new PetDetailDTO();
                    if (riskPropertyMap.get("petAge") != null) {
                        petDetailDTO.setPetAge(String.valueOf(riskPropertyMap.get("petAge")));
                    }
                    petDetailDTO.setPetAgeDesc((String) (riskPropertyMap.get("petAgeChineseName")));
                    petDetailDTO.setPetBreed((String) (riskPropertyMap.get("petBreed")));
                    petDetailDTO.setPetBreedDesc((String) (riskPropertyMap.get("petBreedChineseName")));
                    petDetailDTO.setPetHairColour((String) (riskPropertyMap.get("petHairColour")));
                    petDetailDTO.setPetName((String) (riskPropertyMap.get("petName")));
                    petDetailDTO.setPetSex((String) (riskPropertyMap.get("petSex")));
                    petDetailDTO.setPetSexDesc((String) (riskPropertyMap.get("petSexChineseName")));
                    petDetailDTO.setPetType((String) (riskPropertyMap.get("petType")));
                    petDetailDTO.setPetTypeDesc((String) (riskPropertyMap.get("petTypeChineseName")));
                    petDetailDTO.setPetCity((String) (riskPropertyMap.get("petCity")));
                    petDetailDTO.setPetProvince((String) (riskPropertyMap.get("petProvince")));
                    petDetailDTO.setIdentityNo((String) (riskPropertyMap.get("identityNo")));
                    petDetailDTO.setDogPedigreeCerNo((String) (riskPropertyMap.get("dogPedigreeCerNo")));
                    petDetailDTO.setDogRegisterNo((String) (riskPropertyMap.get("dogRegisterNo")));
                    petDetailDTO.setIsSterilization((String) (riskPropertyMap.get("isSterilization")));
                    petDetailDTO.setPetBreedLevel(String.valueOf(riskPropertyMap.get("petBreedLevel")));
                    petDetailDTO.setPetWeight((String) (riskPropertyMap.get("petWeight")));
                    petDetailDTO.setPetAgeChineseName((String) (riskPropertyMap.get("petAgeChineseName")));
                    petDetailDTO.setPetSocialSecurityCard((String) (riskPropertyMap.get("petSocialSecurityCard")));

                    DigitalInfoDTO digitalInfoDTO = new DigitalInfoDTO();
                    digitalInfoDTO.setImeiCode((String) riskPropertyMap.get("imeiCode"));
                    digitalInfoDTO.setBrand((String) riskPropertyMap.get("brand"));
                    digitalInfoDTO.setBrandType((String) riskPropertyMap.get("brandType"));
                    digitalInfoDTO.setProductType((String) riskPropertyMap.get("productType"));
                    digitalInfoDTO.setProductTypeName(DigitalProductTypeEnum.getName(digitalInfoDTO.getProductType()));

                    NonMotorVehicleDTO nonMotorVehicleDTO = new NonMotorVehicleDTO();
                    nonMotorVehicleDTO.setBrandModel((String) riskPropertyMap.get("brandModel"));
                    nonMotorVehicleDTO.setMotorNo((String) riskPropertyMap.get("motorNo"));
                    nonMotorVehicleDTO.setVehicleFrameNo((String) riskPropertyMap.get("vehicleFrameNo"));
                    nonMotorVehicleDTO.setVehicleLicenceCode((String) riskPropertyMap.get("vehicleLicenceCode"));

                    HouseInfoDTO houseInfoDTO = new HouseInfoDTO();
                    houseInfoDTO.setAddress((String) (riskPropertyMap.get("address")));
                    houseInfoDTO.setBuildingStructure((String) (riskPropertyMap.get("buildingStructure")));
                    houseInfoDTO.setBuildingStructureName((String) (riskPropertyMap.get("buildingStructureChineseName")));
                    houseInfoDTO.setRiskTxtDesc((String) (riskPropertyMap.get("riskTxtDesc")));
                    if (riskPropertyMap.get("roomNumber") != null) {
                        houseInfoDTO.setRoomNumber(String.valueOf(riskPropertyMap.get("roomNumber")));
                    }
                    if (StringUtils.isNotEmpty(houseInfoDTO.getAddress())) {
                        houseInfoDTOs.add(houseInfoDTO);
                    }
                    ahcsPolicyDomainDTO.getPolicyInfoExDTO().getHouseInfoDTOs().add(houseInfoDTO);
                    RiskObjectDTO riskObjectDTO = new RiskObjectDTO();
                    riskObjectDTO.setPetDetailDTO(petDetailDTO);
                    riskObjectDTO.setDigitalInfoDTO(digitalInfoDTO);
                    riskObjectDTO.setNonMotorVehicleDTO(nonMotorVehicleDTO);
                    ahcsPolicyDomainDTO.getRiskObjectList().add(riskObjectDTO);

                    if (riskPropertyService.isRiskProperty(ahcsPolicyDomainDTO.getTargetType(), ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProductClass())) {
                        ahcsPolicyDomainDTO.getReportRiskPropertyList().addAll(buildReportRiskProperty(ahcsPolicyDomainDTO, riskPropertyInfo,riskGroupObject));
                    }
                }
            }
        }
    }

    private List<ReportRiskPropertyDTO> buildReportRiskProperty(AhcsPolicyDomainDTO policyDomain,Map riskPropertyInfo,Map riskGroupObject){
        JSONArray propSubArr = (JSONArray) riskPropertyInfo.get("riskPropsubGroupDTOList");
        if(propSubArr == null || propSubArr.size() == 0){
//            throw new GlobalBusinessException("抄单标的为空");
            return Collections.emptyList();
        }

        List<RiskPropsubGroupDTO> riskPropsubGroupDTOList = propSubArr.toJavaList(RiskPropsubGroupDTO.class);
        List<ReportRiskPropertyDTO> reportRiskPropertyList = new ArrayList<>();
        for (RiskPropsubGroupDTO dto : riskPropsubGroupDTOList) {
            ReportRiskPropertyDTO riskProperty = new ReportRiskPropertyDTO();
            riskProperty.setIdPlyRiskProperty(dto.getId());
            riskProperty.setRiskDetail((String) riskPropertyInfo.get("riskDetail"));
            riskProperty.setIdPlyRiskGroup((String) riskGroupObject.get("id"));
            riskProperty.setRiskGroupNo((String) riskGroupObject.get("riskGroupNo"));
            riskProperty.setRiskGroupName((String) riskGroupObject.get("riskGroupName"));
            riskProperty.setPolicyNo((String) riskGroupObject.get("policyNo"));
            riskProperty.setRiskType(policyDomain.getTargetType());
            riskProperty.setCertificateType(dto.getCertificateType());
            riskProperty.setCertificateNo(dto.getCertificateNo());
            riskProperty.setName(dto.getName());
            riskProperty.setSex(dto.getSex());
            riskProperty.setAge(dto.getAge());
//            riskProperty.setBirthDay();
            reportRiskPropertyList.add(riskProperty);
        }
        return reportRiskPropertyList;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private void initPolicyDuty4IntfPas(Map planInfoMap, AhcsPolicyPlanDTO policyPlanDto, AhcsPolicyDomainDTO ahcsPolicyDomainDTO,String idRiskClass,String accidentDate) {
        List<Long> insuranceBeginDates = new ArrayList<>();
        List<Long> insuranceEndDates = new ArrayList<>();
        List dutyInfoList = (List) planInfoMap.get("dutyInfoList");
        String businessType = ahcsPolicyDomainDTO.getAhcsPolicyInfo().getBusinessType();

        if (RapeCheckUtil.isListNotEmpty(dutyInfoList)) {
            for (int c = 0; c < dutyInfoList.size(); c++) {
                Map<String, Object> dutyInfoMap = (Map) dutyInfoList.get(c);
                if (dutyInfoMap != null && !dutyInfoMap.isEmpty()) {
                    if (CommonConstant.YES.equals(StringUtils.stripToEmpty((String) dutyInfoMap.get("cancelMark")))) {
                        continue;
                    }
                    AhcsPolicyDutyDTO policyDutyDto = new AhcsPolicyDutyDTO();
                    AhcsPolicyDutyEntity policyDuty = new AhcsPolicyDutyEntity();
                    String dutyCode = StringUtils.stripToEmpty((String) dutyInfoMap.get("dutyCode"));

                    policyDuty.setDutyCode(dutyCode);

                    policyDuty.setDutyDesc(StringUtils.stripToEmpty((String) dutyInfoMap.get("dutyDesc")));

                    policyDuty.setDutyName(StringUtils.stripToEmpty((String) dutyInfoMap.get("dutyName")));

                    policyDuty.setOrgDutyCode(StringUtils.stripToEmpty((String) dutyInfoMap.get("orgDutyCode")));

                    policyDuty.setOrgDutyName(StringUtils.stripToEmpty((String) dutyInfoMap.get("orgDutyName")));

                    if (dutyInfoMap.get("insuredAmount") == null) {
                        throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("保单数据异常，责任" + dutyCode + "下，责任保额为空"));
                    }
                    String dutyAmount = StringUtils.stripToEmpty(dutyInfoMap.get("insuredAmount").toString());
                    if (StringUtils.isNotEmpty(dutyAmount)) {
                        BigDecimal a = new BigDecimal(dutyAmount);
                        BigDecimal amount = a.multiply(ahcsPolicyDomainDTO.getApplyNum().multiply(policyPlanDto.getApplyNum()));

                        policyDuty.setDutyAmount(amount);
                    }

                    //标的共享
                    policyDuty.setIsDutySharedAmount(StringUtils.stripToEmpty((String) dutyInfoMap.get("isDutySharedAmount")));
                    if ("1".equals(StringUtils.stripToEmpty((String) dutyInfoMap.get("isDutySharedAmount")))) {
                        ahcsPolicyDomainDTO.getAhcsPolicyInfo().setShareInsuredAmount("1");
                    }
                    //责任共享
                    policyDuty.setDutySharedAmountMerge(StringUtils.stripToEmpty((String) dutyInfoMap.get("dutySharedAmountMerge")));


                    List<Map<String, Object>> riskDutyRelationInfoList = (List<Map<String, Object>>) dutyInfoMap.get("riskDutyRelationInfoList");
                    // 需要判断是团单还是个单 团单加人减人的逻辑不太一样
                    if (RapeCheckUtil.isNotEmpty(riskDutyRelationInfoList)) {
                        Map<String, Object> riskDutyRelationInfo = new HashMap<>();
                        // 判断是否是团单
                        if (BUSINESS_TYPE_ORG.equals(businessType) && StringUtils.isNotEmpty(idRiskClass) ){
                            // 团单 根据 idRiskClass 来处理
                            riskDutyRelationInfo = riskDutyRelationInfoList.stream().filter(dutyRelationInfo -> idRiskClass.equals(MapUtils.getString(dutyRelationInfo, "idRiskClass"))).findFirst()
                                    .orElseThrow(() -> new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(), GlobalResultStatus.ERROR.format("保单下的责任起止时间获取异常请排查！")));
                        } else {
                            riskDutyRelationInfo = riskDutyRelationInfoList.get(0);
                        }
                        try {
                            Date insuranceBeginDate = RapeDateUtil.parseToFormatDate((String) riskDutyRelationInfo.get("insuranceBeginDate"), RapeDateUtil.FULL_DATE_STR);
                            Date insuranceEndDate = RapeDateUtil.parseToFormatDate((String) riskDutyRelationInfo.get("insuranceEndDate"), RapeDateUtil.FULL_DATE_STR);
                            if (insuranceBeginDate != null && insuranceEndDate != null) {
                                insuranceBeginDates.add(insuranceBeginDate.getTime());
                                insuranceEndDates.add(insuranceEndDate.getTime());
                                // 一个责任只有一个起期和止期 不会存在多个
                                policyDuty.setInsuranceBeginDate(insuranceBeginDate);
                                policyDuty.setInsuranceEndDate(insuranceEndDate);
                            } else {
                                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("保单下的责任起止时间获取异常请排查！"));
                            }
                        } catch (ParseException e) {
                            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("责任起止日期格式转换错误"));
                        }

                    }else {
                        throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("保单下的责任起止时间为空请排查！"));
                    }
                    try {
                        if (StringUtils.isNotEmpty(accidentDate)) {
                            Date date = DateUtils.parseToDate(accidentDate);
                            //追溯期、报告期:保单起期-追溯期<=事故日期<=保单止期+报告期
                            int extendReportDate = Objects.nonNull(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getExtendReportDate()) ? ahcsPolicyDomainDTO.getAhcsPolicyInfo().getExtendReportDate() : 0;
                            int prosecutionPeriod = Objects.nonNull(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProsecutionPeriod()) ? ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProsecutionPeriod() : 0;
                            if((DateUtils.addDate(DateUtils.beginOfDay(policyDuty.getInsuranceBeginDate()),-prosecutionPeriod).before(date) && DateUtils.addDate(DateUtils.endOfDay(policyDuty.getInsuranceEndDate()),extendReportDate).after(date) )
                                    || DateUtils.addDate(DateUtils.endOfDay(policyDuty.getInsuranceEndDate()),extendReportDate).equals(date)
                            || DateUtils.addDate(DateUtils.beginOfDay(policyDuty.getInsuranceBeginDate()),-prosecutionPeriod).equals(date) ) {
                                policyDutyDto.setAhcsPolicyDuty(policyDuty);
                                this.initPolicyDutyDetail4IntfPas(null, dutyInfoMap, policyDutyDto, ahcsPolicyDomainDTO);
                                policyPlanDto.getAhcsPolicyDutyDTOs().add(policyDutyDto);
                            }
                        } else {
                            policyDutyDto.setAhcsPolicyDuty(policyDuty);
                            this.initPolicyDutyDetail4IntfPas(null, dutyInfoMap, policyDutyDto, ahcsPolicyDomainDTO);
                            policyPlanDto.getAhcsPolicyDutyDTOs().add(policyDutyDto);
                        }
                    } catch (ParseException e) {
                        throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("出险日期格式转换错误"));
                    }
                }
            }
        }
        if (RapeCheckUtil.isNotEmpty(insuranceBeginDates) && RapeCheckUtil.isNotEmpty(insuranceEndDates)) {

            Long insuranceBeginDate = insuranceBeginDates.get(0);
            Long insuranceEndDate = insuranceEndDates.get(0);
            for (int i = 1; i < insuranceBeginDates.size(); i++) {
                if (insuranceBeginDate > insuranceBeginDates.get(i)) {
                    insuranceBeginDate = insuranceBeginDates.get(i);
                }
            }
            for (int i = 1; i < insuranceEndDates.size(); i++) {
                if (insuranceEndDate < insuranceEndDates.get(i)) {
                    insuranceEndDate = insuranceEndDates.get(i);
                }
            }
//            if (StringUtils.isNotEmpty(accidentDate)) {
//                try {
//                    Date date = DateUtils.parseToDate(accidentDate);
//                    if (date.getTime() < insuranceBeginDate || date.getTime() > insuranceEndDate) {
//                        throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("出险日期不在保单下的责任起止时间范围内，请排查！"));
//                    }
//                } catch (ParseException e) {
//                    throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("出险日期格式转换错误"));
//                }
//            }
            if (ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceBeginTime() == null || ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceBeginTime().getTime() > insuranceBeginDate) {
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setInsuranceBeginTime(new Date(insuranceBeginDate));
            }
            if (ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceEndTime() == null || ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceEndTime().getTime() < insuranceEndDate) {
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setInsuranceEndTime(new Date(insuranceEndDate));
            }
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private void initPolicyDutyDetail4IntfPas(BigDecimal reinsureScale, Map<String, Object> dutyInfoMap, AhcsPolicyDutyDTO policyDutyDto,
                                              AhcsPolicyDomainDTO ahcsPolicyDomainDTO) {
        boolean targetIsPet = "12".equals(ahcsPolicyDomainDTO.getTargetType());
        boolean targetIsLoss = riskPropertyService.isRiskProperty(ahcsPolicyDomainDTO.getTargetType(), ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProductClass());
        BigDecimal dutyAmount = policyDutyDto.getAhcsPolicyDuty().getDutyAmount();
        List dutyDetailInfoList = (List) dutyInfoMap.get("dutyDetailInfoList");
        AhcsPolicyDutyDetailEntity policyDutyAttr = new AhcsPolicyDutyDetailEntity();
        List dutyAttributeInfoList = (List) dutyInfoMap.get("dutyAttributeInfoList");
        if (RapeCheckUtil.isListNotEmpty(dutyAttributeInfoList)) {
            for (int k = 0; k < dutyAttributeInfoList.size(); k++) {
                AhcsDutyAttributeDTO ahcsDutyAttributeDTO = new AhcsDutyAttributeDTO();
                AhcsDutyAttributeEntity ahcsDutyAttributeEntity = new AhcsDutyAttributeEntity();

                Map<String, Object> dutyAtributeMap = (Map<String, Object>) dutyAttributeInfoList.get(k);

                String dutyAttrCode = StringUtils.stripToEmpty((String) dutyAtributeMap.get("dutyAttrCode"));
                ahcsDutyAttributeEntity.setAttributeCode(dutyAttrCode);

                if ("361".equals(dutyAttrCode)) {
                    ahcsPolicyDomainDTO.getAhcsPolicyInfo().setSocialFlag(CommonConstant.IS_SOCIAL);
                }

                String dutyAttrAmountValue = StringUtils.stripToEmpty(String.valueOf(dutyAtributeMap.get("dutyAttrAmountValue")));
                if (StringUtils.isNotEmpty(dutyAttrAmountValue) && !"null".equals(dutyAttrAmountValue)) {
                    ahcsDutyAttributeEntity.setAttributeValue(dutyAttrAmountValue);
                }

                if (dutyAtributeMap.get("dutyAttrRateValue") != null) {
                    String AttrRateValue = StringUtils.stripToEmpty(String.valueOf(dutyAtributeMap.get("dutyAttrRateValue")));

                    if ("18".equals(ahcsPolicyDomainDTO.getProductClass()) && !"60".equals(dutyAttrCode) && !"61".equals(ahcsPolicyDomainDTO.getProductClass())) {
                        ahcsDutyAttributeEntity.setAttributeValue(AttrRateValue);
                    } else {
                        BigDecimal dutyAttrRateValue = new BigDecimal(AttrRateValue);
                        ahcsDutyAttributeEntity.setAttrRateValue(dutyAttrRateValue);
                    }
                }

                ahcsDutyAttributeDTO.setAhcsDutyAttribute(ahcsDutyAttributeEntity);
                List dutyAttrDetailList = (List) dutyAtributeMap.get("dutyAttrDetailList");
                if (RapeCheckUtil.isListNotEmpty(dutyAttrDetailList)) {
                    for (int z = 0; z < dutyAttrDetailList.size(); z++) {
                        Map<String, Object> dutyAtributeDetailMap = (Map<String, Object>) dutyAttrDetailList.get(z);
                        AhcsDutyAttributeDetailEntity ahcsDutyAttributeDetail = new AhcsDutyAttributeDetailEntity();
                        String dutyAttrDetailCode = StringUtils.stripToEmpty((String) dutyAtributeDetailMap.get("dutyAttrDetailCode"));
                        String dutyAttrDetailValue = StringUtils.stripToEmpty((String) dutyAtributeDetailMap.get("dutyAttrDetailValue"));
                        String attributeRowNo = StringUtils.stripToEmpty((String) dutyAtributeDetailMap.get("attrRowNo"));
                        String attributeColumnNo = StringUtils.stripToEmpty((String) dutyAtributeDetailMap.get("attrColumnNo"));

                        ahcsDutyAttributeDetail.setAttributeDetailCode(dutyAttrDetailCode);

                        ahcsDutyAttributeDetail.setAttributeDetailValue(dutyAttrDetailValue);
                        ahcsDutyAttributeDetail.setAttributeColumnNo(attributeColumnNo);
                        ahcsDutyAttributeDetail.setAttributeRowNo(attributeRowNo);
                        ahcsDutyAttributeDTO.getAhcsDutyAttributeDetail().add(ahcsDutyAttributeDetail);
                    }
                }
                policyDutyDto.getAhcsDutyAttributeDTOs().add(ahcsDutyAttributeDTO);
            }
        }

        if (RapeCheckUtil.isListEmpty(dutyDetailInfoList)) {
            AhcsPolicyDutyDetailEntity policyDutyDetail = new AhcsPolicyDutyDetailEntity();

            String dutyDetailCode = "DD" + policyDutyDto.getAhcsPolicyDuty().getDutyCode();

            String dutyDetailName = policyDutyDto.getAhcsPolicyDuty().getDutyName();

            policyDutyDetail.setDutyAmount(dutyAmount);
            policyDutyDetail.setDutyDetailName(dutyDetailName);
            policyDutyDetail.setDutyDetailCode(dutyDetailCode);
            if(targetIsPet){
                policyDutyDetail.setDutyDetailtype(SettleConst.DETAIL_TYPE_OTHERS);
            }else if(targetIsLoss){
                policyDutyDetail.setDutyDetailtype(SettleConst.DETAIL_TYPE_LOSS);
            }
            policyDutyDto.getAhcsPolicyDutyDetail().add(policyDutyDetail);
        }
        if (RapeCheckUtil.isListNotEmpty(dutyDetailInfoList)) {
            for (int j = 0; j < dutyDetailInfoList.size(); j++) {
                Map<String, Object> dutyDetailMap = (Map<String, Object>) dutyDetailInfoList.get(j);
                if (dutyDetailMap == null) {
                    continue;
                }
                AhcsPolicyDutyDetailEntity policyDutyDetail = new AhcsPolicyDutyDetailEntity();
                BeanUtils.copyProperties(policyDutyAttr, policyDutyDetail);

                String dutyDetailCode = StringUtils.stripToEmpty((String) dutyDetailMap.get("dutyDetailCode"));

                String dutyDetailName = StringUtils.stripToEmpty((String) dutyDetailMap.get("dutyDetailName"));

                policyDutyDetail.setDutyDetailtype(StringUtils.stripToEmpty((String) dutyDetailMap.get("dutyDetailType")));

                policyDutyDetail.setOrgDutyDetailCode(StringUtils.stripToEmpty((String) dutyDetailMap.get("orgDutyDetailCode")));

                policyDutyDetail.setOrgDutyDetailName(StringUtils.stripToEmpty((String) dutyDetailMap.get("orgDutyDetailName")));

                if (null != dutyDetailMap.get("limitAmount")) {
                    String limitAmount = StringUtils.stripToEmpty((String.valueOf(dutyDetailMap.get("limitAmount"))));
                    BigDecimal detailLimitAmount=Optional.ofNullable(new BigDecimal(limitAmount)).orElseGet(()->new BigDecimal("0"));
                    policyDutyDetail.setDetailLimitAmount(detailLimitAmount);
                    policyDutyDetail.setDutyAmount(dutyAmount.multiply(detailLimitAmount));
                } else if (ObjectUtil.isNotEmpty(dutyDetailMap.get("fixedLimit"))) {
                    //固定限额
                    String fixedLimitStr = dutyDetailMap.get("fixedLimit").toString();
                    BigDecimal fixedLimit = new BigDecimal(fixedLimitStr);
                    policyDutyDetail.setFixedLimit(fixedLimit);
                    policyDutyDetail.setDutyAmount(fixedLimit);
                } else {
                    policyDutyDetail.setDutyAmount(dutyAmount);
                }
                policyDutyDetail.setDutyDetailName(dutyDetailName);
                policyDutyDetail.setDutyDetailCode(dutyDetailCode);
                if(targetIsPet){
                    policyDutyDetail.setDutyDetailtype(SettleConst.DETAIL_TYPE_OTHERS);
                }else if(targetIsLoss){
                    policyDutyDetail.setDutyDetailtype(SettleConst.DETAIL_TYPE_LOSS);
                }
                policyDutyDto.getAhcsPolicyDutyDetail().add(policyDutyDetail);
            }
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private AhcsPolicyDomainDTO initPolicyCoinsurance4IntfPas(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map policyInfoMap) {
        String policyDeptCode = ahcsPolicyDomainDTO.getAhcsPolicyInfo().getDepartmentCode();
        Map<String, Object> coinsuranceInfoMap = (Map<String, Object>) policyInfoMap.get("coinsuranceInfo");

        if (coinsuranceInfoMap != null && !coinsuranceInfoMap.isEmpty()) {
            String jointInsuranceBusiness = (String) coinsuranceInfoMap.get("jointInsuranceBusiness");
            List coinsuranceDetailList = (List) coinsuranceInfoMap.get("coinsuranceDetailList");
            if (RapeCheckUtil.isListNotEmpty(coinsuranceDetailList)) {
                List<AhcsCoinsureEntity> orderList = new ArrayList<>();
                for (int coin_i = 0; coin_i < coinsuranceDetailList.size(); coin_i++) {
                    Map<String, Object> coinsuranceDetaiMap = (Map) coinsuranceDetailList.get(coin_i);
                    AhcsCoinsureEntity ahcsCoinsure = new AhcsCoinsureEntity();

                    String reinsureCompanyCode = StringUtils.stripToEmpty((String) coinsuranceDetaiMap.get("jointInsuranceName"));
                    ahcsCoinsure.setReinsureCompanyCode(reinsureCompanyCode);

                    if(coinsuranceDetaiMap.get("reinsureCompanyCode") != null){
                        String reinsureCompanyName = (String) coinsuranceDetaiMap.getOrDefault("reinsureCompanyCode","");
                        int i = reinsureCompanyName.indexOf("-");
                        if(i > 0){
                            reinsureCompanyName = reinsureCompanyName.substring(i+1);
                        }
                        ahcsCoinsure.setReinsureCompanyName(reinsureCompanyName);
                    }else{
                        ahcsCoinsure.setReinsureCompanyName(departmentDefineMapper.queryDepartmentNameByDeptCode(reinsureCompanyCode));
                    }

                    //联共保业务[0:内部联保(我司主联)、1:外部联保(我司主承)、2:外部联保(我司非主承)]
                    ahcsCoinsure.setCoinsuranceType(jointInsuranceBusiness);
                    if(reinsureCompanyCode.equals(policyDeptCode)){
                        if("0".equals(jointInsuranceBusiness) || "1".equals(jointInsuranceBusiness)){
                            //我司主承
                            ahcsCoinsure.setAcceptInsuranceFlag("1");
                        }else{
                            ahcsCoinsure.setAcceptInsuranceFlag("0");
                        }
                    }else{
                        if("0".equals(jointInsuranceBusiness) || "1".equals(jointInsuranceBusiness)){
                            ahcsCoinsure.setAcceptInsuranceFlag("0");
                        }else{
                            ahcsCoinsure.setAcceptInsuranceFlag("0");
                            //承保比例最大的为主承
                            orderList.add(ahcsCoinsure);
                        }
                    }

                    if (coinsuranceDetaiMap.get("jointInsuranceShare") != null) {
                        String reinsureScale = StringUtils.stripToEmpty(String.valueOf(coinsuranceDetaiMap.get("jointInsuranceShare")));
                        ahcsCoinsure.setReinsureScale("".equals(reinsureScale) ? null : new BigDecimal(reinsureScale));
                    }

                    if (coinsuranceDetaiMap.get("insuredAmount") != null) {
                        String insuredAmount = StringUtils.stripToEmpty(String.valueOf(coinsuranceDetaiMap.get("insuredAmount")));
                        ahcsCoinsure.setInsuredAmount("".equals(insuredAmount) ? null : new BigDecimal(insuredAmount));
                    }

                    if (coinsuranceDetaiMap.get("premium") != null) {
                        String premium = StringUtils.stripToEmpty(String.valueOf(coinsuranceDetaiMap.get("premium")));
                        ahcsCoinsure.setPremium("".equals(premium) ? null : new BigDecimal(premium));
                    }
                    ahcsPolicyDomainDTO.getAhcsCoinsure().add(ahcsCoinsure);
                }
                if(orderList.size() == 1 ){
                    orderList.get(0).setAcceptInsuranceFlag("1");
                }
                if(orderList.size() > 2){
                    orderList = orderList.stream().sorted(Comparator.comparing(AhcsCoinsureEntity::getReinsureScale)).collect(Collectors.toList());
                    orderList.get(orderList.size()-1).setAcceptInsuranceFlag("1");
                }
            }
        }
        return ahcsPolicyDomainDTO;
    }

    @SuppressWarnings({"rawtypes"})
    private AhcsPolicyDomainDTO initPolicySpecialPromisePas(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map policyInfoMap) {
        List specialPromiseList = (List) policyInfoMap.get("specialPromiseList");
        if (RapeCheckUtil.isListNotEmpty(specialPromiseList)) {
            for (int i = 0; i < specialPromiseList.size(); i++) {
                Map specialPromiseMap = (Map) specialPromiseList.get(i);
                AhcsSpecialPromiseEntity specialPromise = new AhcsSpecialPromiseEntity();

                specialPromise.setPromiseCode(StringUtils.stripToEmpty((String) specialPromiseMap.get("promiseCode")));

                specialPromise.setPromiseDesc(StringUtils.stripToEmpty((String) specialPromiseMap.get("promiseDesc")));

                specialPromise.setPromiseType(StringUtils.stripToEmpty((String) specialPromiseMap.get("promiseType")));

                specialPromise.setBusinessType(StringUtils.stripToEmpty((String) specialPromiseMap.get("businessType")));

                specialPromise.setContentType(StringUtils.stripToEmpty((String) specialPromiseMap.get("contentType")));

                specialPromise.setRiskGroupName(StringUtils.stripToEmpty((String) specialPromiseMap.get("riskGroupName")));

                if (specialPromiseMap.get("sortIndex") != null) {
                    LogUtil.info("initPolicySpecialPromisePas 中 sortIndex :"+specialPromiseMap.get("sortIndex") );
                    String sortIndex = StringUtils.stripToEmpty(specialPromiseMap.get("sortIndex").toString());
                    if (StringUtils.isNotEmpty(sortIndex)) {
                        if (sortIndex.contains(".")){
                            sortIndex=sortIndex.split("\\.")[0]  ;
                        }
                        specialPromise.setSortIndex(sortIndex);
                    }
                }
                ahcsPolicyDomainDTO.getAhcsSpecialPromise().add(specialPromise);
            }

        }

        return ahcsPolicyDomainDTO;
    }

    @SuppressWarnings({"all"})
    private AhcsPolicyDomainDTO initPolicyInsured4IntfPas(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map policyInfoMap, String idPlyRiskPerson) {
        List<String> rescueCompanys = new ArrayList<>();
        List<String> rescueCompanyCodes = new ArrayList<>();
        List<String> rescueServiceCodes = new ArrayList<>();
        List<SubjectDTO> subjectDTOs = new ArrayList<SubjectDTO>();
        List accountInfoList = (List) policyInfoMap.get("accountInfoList");
        List riskGroupInfoList = (List) policyInfoMap.get("riskGroupInfoList");
        boolean hasInsuredPerson = false;
        if (RapeCheckUtil.isListNotEmpty(riskGroupInfoList)) {
            for (int i = 0; i < riskGroupInfoList.size(); i++) {
                Map<String, Object> riskGroupInfoMap = (Map) riskGroupInfoList.get(i);
                List riskPersonInfoList = (List) riskGroupInfoMap.get("riskPersonInfoList");
                if (RapeCheckUtil.isListNotEmpty(riskPersonInfoList)) {
                    hasInsuredPerson = true;
                    for (int j = 0; j < riskPersonInfoList.size(); j++) {
                        Map insurantInfoMap = (Map) riskPersonInfoList.get(j);
                        String id = MapUtils.getString(insurantInfoMap, "id");
                        // idPlyRiskPerson 为null 是查全部被保人
                        if (idPlyRiskPerson != null && !idPlyRiskPerson.equals(id)) {
                            continue;
                        }
                        AhcsInsuredPresonEntity ahcsInsuredPreson = new AhcsInsuredPresonEntity();
                        AhcsInsuredPresonDTO insuredPresonDTO = new AhcsInsuredPresonDTO();
                        AhcsInsuredPersonExtEntity ahcsInsuredPresonExt = new AhcsInsuredPersonExtEntity();
                        RiskObjectDTO riskObjectDTO = new RiskObjectDTO();

                        this.initGasInfo(ahcsPolicyDomainDTO, insurantInfoMap, riskObjectDTO);

                        try {
                            if (insurantInfoMap.get("trafficNo") != null
                                    && ahcsPolicyDomainDTO.getAhcsPolicyInfo() != null) {
                                String trafficNo = (String) insurantInfoMap.get("trafficNo");
                                ahcsPolicyDomainDTO.getAhcsPolicyInfo().setTrafficNo(trafficNo);
                            }
                        } catch (Exception e) {
                        }

                        ahcsInsuredPreson.setPersonnelAttribute(StringUtils.stripToEmpty((String) insurantInfoMap.get("personnelAttribute")));

                        ahcsInsuredPreson.setSubPolicyNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("riskGroupId")));

                        ahcsInsuredPreson.setName(StringUtils.stripToEmpty((String) insurantInfoMap.get("name")));

                        ahcsInsuredPreson.setSexCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("sexCode")));

                        if (insurantInfoMap.get("age") != null) {
                            ahcsInsuredPreson.setAge((int) insurantInfoMap.get("age"));
                        }

                        String birthday = null;
                        if (insurantInfoMap.get("birthday") != null) {
                            LogUtil.info("insurantInfoMap-info{}:"+insurantInfoMap);
                            birthday = StringUtils.stripToEmpty(String.valueOf(insurantInfoMap.get("birthday")));
                        }
                        try {
                            if (StringUtils.isNotEmpty(birthday)) {
                                ahcsInsuredPreson.setBirthday(RapeDateUtil.stringFormatToParseDate(birthday, RapeDateUtil.FULL_DATE_STR));
                            }
                        } catch (ParseException e) {
                            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("{转化组装被保险人信息 转换时间}"));
                        }

                        if(insurantInfoMap.get("birthdayStr") != null){
                            String birthdayStr = (String) insurantInfoMap.get("birthdayStr");
                            ahcsInsuredPreson.setBirthdayStr(birthdayStr);
                            try {
                                ahcsInsuredPreson.setBirthday(RapeDateUtil.stringFormatToParseDate(birthdayStr, RapeDateUtil.SIMPLE_DATE_STR));
                            } catch (ParseException e) {
                                LogUtil.error("转换出生日期错误",e);
                            }
                        }else{
                            ahcsInsuredPreson.setBirthdayStr(DateUtils.parseToFormatStr(ahcsInsuredPreson.getBirthday(), RapeDateUtil.SIMPLE_DATE_STR));
                        }
                        ahcsInsuredPreson.setCertificateNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("certificateNo")));

                        ahcsInsuredPreson.setCertificateType(StringUtils.stripToEmpty((String) insurantInfoMap.get("certificateType")));

                        ahcsInsuredPreson.setProfessionCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("professionCode")));
//                        if(insurantInfoMap.get("clientNo") != null){
//                            ahcsInsuredPreson.setPersonnelCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("clientNo")));
//                        }

                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().setInsuredProfessionName(StringUtils.stripToEmpty((String) insurantInfoMap.get("professionChineseName")));
                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().setInsuredProfessionGradeChineseName(StringUtils.stripToEmpty((String) insurantInfoMap.get("professionGradeChineseName")));

                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().setExtCertificateNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("extCertificateNo")));

                        ahcsInsuredPreson.setTelephone(StringUtils.stripToEmpty((String) insurantInfoMap.get("homeTelephone")));

                        ahcsInsuredPreson.setMobileTelephone(StringUtils.stripToEmpty((String) insurantInfoMap.get("mobileTelephone")));

                        ahcsInsuredPreson.setAddress(StringUtils.stripToEmpty((String) insurantInfoMap.get("address")));

                        ahcsInsuredPreson.setEmail(StringUtils.stripToEmpty((String) insurantInfoMap.get("email")));

                        ahcsInsuredPreson.setClientNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("clientNo")));

                        ahcsInsuredPreson.setSchemeNo(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupNo")));

                        ahcsInsuredPreson.setSchemeName(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupName")));

                        insuredPresonDTO.setSubjectName(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupName")));
                        String isSociaSecurity = StringUtils.stripToEmpty((String) insurantInfoMap.get("isSociaSecurity"));

                        insuredPresonDTO.setIsSociaSecurity(isSociaSecurity);
                        ahcsInsuredPreson.setIsSociaSecurity(isSociaSecurity);

                        ahcsInsuredPreson.setAcceptNo(StringUtils.stripToEmpty(id));

                        ahcsInsuredPreson.setPersonnelCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("clientNo")));

                        ahcsInsuredPreson.setRiskPersonNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("riskPersonNo")));

                        ahcsInsuredPresonExt
                                .setVehicleLicenceCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("vehicleLicenceCode")));

                        ahcsInsuredPresonExt.setVehicleFrameNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("vehicleFrameNo")));

                        ahcsInsuredPresonExt.setEngineNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("engineNo")));

                        ahcsInsuredPresonExt.setFlightNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("flightNo")));

                         if (insurantInfoMap.get("flightDate") != null) {
                            String flightDate = StringUtils.stripToEmpty(String.valueOf(insurantInfoMap.get("flightDate")));
                            try {
                                ahcsInsuredPresonExt
                                        .setFlightDate(RapeDateUtil.stringFormatToParseDate(flightDate, RapeDateUtil.FULL_DATE_STR));
                            } catch (ParseException e) {
                                LogUtil.info("INTF转换航班日期格式异常");
                            }
                        }

                        ahcsInsuredPresonExt.setOriginal(StringUtils.stripToEmpty((String) insurantInfoMap.get("original")));

                        ahcsInsuredPresonExt.setDestination(StringUtils.stripToEmpty((String) insurantInfoMap.get("destination")));

                        ahcsInsuredPresonExt.setIdAhcsInsuredPersonExt(StringUtils.stripToEmpty(id));

                        ahcsInsuredPreson.setEffectiveDate(ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceBeginTime());
                        ahcsInsuredPreson.setInvalidateDate(ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceEndTime());
                        insuredPresonDTO.setAhcsInsuredPreson(ahcsInsuredPreson);
                        insuredPresonDTO.setAhcsInsuredPersonExt(ahcsInsuredPresonExt);
                        ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs().add(insuredPresonDTO);

                        Map<String, Object> riskPersonMap = (Map<String, Object>) insurantInfoMap.get("riskPersonMap");
                        if (riskPersonMap != null) {
                            String provinceName = (String) riskPersonMap.get("permanentProvinceName");
                            String cityName = (String) riskPersonMap.get("permanentCityName");
                            String address = provinceName + cityName;
                            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPermanentAddress(address);

                            ahcsPolicyDomainDTO.getAhcsPolicyInfo().setHealthNotification((String) riskPersonMap.get("healthNotification"));

                            List personHealthNoticeList = (List) riskPersonMap.get("personHealthNotice");
                            if (RapeCheckUtil.isNotEmpty(personHealthNoticeList)) {
                                for (int z = 0; z < personHealthNoticeList.size(); z++) {
                                    Map<String, Object> noticeMap = (Map<String, Object>) personHealthNoticeList.get(z);
                                    if (noticeMap != null) {
                                        InsuranceNotificationDTO insuranceNotificationDTO = new InsuranceNotificationDTO();
                                        insuranceNotificationDTO.setNotificationCode((String) noticeMap.get("code"));
                                        insuranceNotificationDTO.setNotificationNoDesc((String) noticeMap.get("detail"));
                                        insuranceNotificationDTO.setNotificationType("2");
                                        insuranceNotificationDTO.setNotificationResult((String) noticeMap.get("result"));
                                        insuranceNotificationDTO.setNotificationValue((String) noticeMap.get("label"));
                                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceNotificationDTOs().add(insuranceNotificationDTO);
                                    }
                                }
                            }
                        }
                    }

                    for (AhcsInsuredPresonDTO dto : ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs()) {
                        if (RapeCheckUtil.isNotEmpty(accountInfoList)) {
                            for (int k = 0; k < accountInfoList.size(); k++) {
                                Map<String, Object> accountInfo = (Map<String, Object>) accountInfoList.get(k);
                                String businessType = (String) accountInfo.get("businessType");
                                String idPerson = (String) accountInfo.get("idPerson");
                                if (dto.getAhcsInsuredPersonExt().getIdAhcsInsuredPersonExt().equals(idPerson) && BUSINESS_TYPE_ORG.equals(businessType)) {

                                    dto.getAhcsInsuredPersonExt().setBankCode((String) accountInfo.get("bankCode"));

                                    dto.getAhcsInsuredPersonExt().setBankAccount((String) accountInfo.get("accountNo"));

                                    dto.getAhcsInsuredPersonExt().setBankName((String) accountInfo.get("bankName"));
                                }
                            }
                        }
                    }
                } else {

                    hasInsuredPerson = initInsurantInfoList(ahcsPolicyDomainDTO, riskGroupInfoMap, riskGroupInfoList,
                            hasInsuredPerson);
                    if (!hasInsuredPerson) {
                        List riskPropertyInfoList = (List) riskGroupInfoMap.get("riskPropertyInfoList");
                        if (RapeCheckUtil.isNotEmpty(riskPropertyInfoList)) {
                            for (Object object : riskPropertyInfoList) {
                                Map<String, Object> riskPropertyInfoMap = (Map<String, Object>) object;
                                hasInsuredPerson = initInsurantInfoList(ahcsPolicyDomainDTO, riskPropertyInfoMap, riskGroupInfoList,
                                        hasInsuredPerson);
                            }
                        }
                    }
                }

                List rescueServiceList = (List) policyInfoMap.get("rescueServiceList");
                if (RapeCheckUtil.isNotEmpty(rescueServiceList)) {
                    for (int z = 0; z < rescueServiceList.size(); z++) {
                        Map<String, Object> rescueService = (Map<String, Object>) rescueServiceList.get(z);
                        String companyName = (String) rescueService.get("companyName");
                        String companyCode = (String) rescueService.get("companyCode");
                        String serviceCode = (String) rescueService.get("serviceCode");
                        rescueCompanyCodes.add(companyCode);
                        rescueCompanys.add(companyName);
                        rescueServiceCodes.add(serviceCode);
                    }
                }

                List checkEmpty = (List) riskGroupInfoMap.get("rescueServiceList");
                if (checkEmpty != null && !checkEmpty.isEmpty()) {
                    rescueServiceList = checkEmpty;
                }
                Map<String, String> rescueServiceCodeName = new HashMap<>();
                if (RapeCheckUtil.isNotEmpty(rescueServiceList)) {
                    for (int z = 0; z < rescueServiceList.size(); z++) {
                        Map<String, Object> rescueService = (Map<String, Object>) rescueServiceList.get(z);
                        String companyName = (String) rescueService.get("companyName");
                        String companyCode = (String) rescueService.get("companyCode");
                        String serviceCode = (String) rescueService.get("serviceCode");
                        String serviceName = (String) rescueService.get("serviceName");
                        rescueCompanyCodes.add(companyCode);
                        rescueCompanys.add(companyName);
                        rescueServiceCodes.add(serviceCode);
                        rescueServiceCodeName.put(serviceCode, serviceName);
                    }
                }

                ahcsPolicyDomainDTO.getPolicyInfoExDTO().getRescueCompanys().addAll(rescueCompanys);
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().getRescueCompanyCodes().addAll(rescueCompanyCodes);
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().getRescueServiceCodes().addAll(rescueServiceCodes);
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setRescueService(rescueServiceCodeName);

                SubjectDTO subjectDTO = new SubjectDTO();
                subjectDTO.setSubjectId(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("id")));
                subjectDTO.setSubjectName(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupName")));
                subjectDTOs.add(subjectDTO);
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setSubjectDTOs(subjectDTOs);
            }
        }

        hasInsuredPerson = initInsurantInfoList(ahcsPolicyDomainDTO, policyInfoMap, riskGroupInfoList,
                hasInsuredPerson);
        return ahcsPolicyDomainDTO;
    }

    private AhcsPolicyDomainDTO initPolicyInsured(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map policyInfoMap, CopyPolicyQueryVO queryVO) {
        List<String> rescueCompanys = new ArrayList<>();
        List<String> rescueCompanyCodes = new ArrayList<>();
        List<String> rescueServiceCodes = new ArrayList<>();
        List<SubjectDTO> subjectDTOs = new ArrayList<SubjectDTO>();
        List accountInfoList = (List) policyInfoMap.get("accountInfoList");
        List riskGroupInfoList = (List) policyInfoMap.get("riskGroupInfoList");
        boolean riskPersonIdMatch = StringUtils.isNotEmpty(queryVO.getRiskPersonId());
        boolean riskGroupNoMatch = StringUtils.isNotEmpty(queryVO.getRiskGroupNo());
        boolean nameMatch = StringUtils.isNotEmpty(queryVO.getCertNo()) && StringUtils.isNotEmpty(queryVO.getClientName());
        // 退运险特殊处理 + 虚拟被保人证件号为空特殊处理
        boolean onlyNameMatch = StringUtils.isEmpty(queryVO.getCertNo()) && StringUtils.isNotEmpty(queryVO.getClientName());
        boolean hasInsuredPerson = false;
        if (RapeCheckUtil.isListNotEmpty(riskGroupInfoList)) {
            for (int i = 0; i < riskGroupInfoList.size(); i++) {
                Map<String, Object> riskGroupInfoMap = (Map) riskGroupInfoList.get(i);
                List riskPersonInfoList = (List) riskGroupInfoMap.get("riskPersonInfoList");
                if (RapeCheckUtil.isListNotEmpty(riskPersonInfoList)) {
                    hasInsuredPerson = true;
                    for (int j = 0; j < riskPersonInfoList.size(); j++) {
                        Map insurantInfoMap = (Map) riskPersonInfoList.get(j);
                        String id = MapUtils.getString(insurantInfoMap, "id");
                        if (riskPersonIdMatch) {
                            //传入id不为空就用id匹配
                            if(!queryVO.getRiskPersonId().equals(id)){
                                continue;
                            }

                        }else if(riskGroupNoMatch){
                            //传入riskGroupNo不为空就用riskGroupNo匹配
                            if(!queryVO.getRiskGroupNo().equals(riskGroupInfoMap.get("riskGroupNo"))){
                                continue;
                            }
                        }else if(nameMatch){
                            //传入id为空就用姓名+证件号匹配
                            String personName = MapUtils.getString(insurantInfoMap, "name");
                            String personCertNo = MapUtils.getString(insurantInfoMap, "certificateNo");
                            if(personName == null
//                                    || !personName.equals(queryVO.getClientName())
                                    || personCertNo == null || !personCertNo.equals(queryVO.getCertNo())){
                                continue;
                            }
                        }else if(onlyNameMatch) {
                            // 退运险特殊处理 没有被保险人其他信息
                            String personName = MapUtils.getString(insurantInfoMap, "name");
                            if(personName == null || !personName.equals(queryVO.getClientName())){
                                continue;
                            }
                        }
                        AhcsInsuredPresonEntity ahcsInsuredPreson = new AhcsInsuredPresonEntity();
                        AhcsInsuredPresonDTO insuredPresonDTO = new AhcsInsuredPresonDTO();
                        AhcsInsuredPersonExtEntity ahcsInsuredPresonExt = new AhcsInsuredPersonExtEntity();
                        RiskObjectDTO riskObjectDTO = new RiskObjectDTO();

                        this.initGasInfo(ahcsPolicyDomainDTO, insurantInfoMap, riskObjectDTO);

                        try {
                            if (insurantInfoMap.get("trafficNo") != null
                                    && ahcsPolicyDomainDTO.getAhcsPolicyInfo() != null) {
                                String trafficNo = (String) insurantInfoMap.get("trafficNo");
                                ahcsPolicyDomainDTO.getAhcsPolicyInfo().setTrafficNo(trafficNo);
                            }
                        } catch (Exception e) {
                        }

                        ahcsInsuredPreson.setPersonnelAttribute(StringUtils.stripToEmpty((String) insurantInfoMap.get("personnelAttribute")));

                        ahcsInsuredPreson.setSubPolicyNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("riskGroupId")));

                        ahcsInsuredPreson.setName(StringUtils.stripToEmpty((String) insurantInfoMap.get("name")));

                        ahcsInsuredPreson.setSexCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("sexCode")));

                        if (insurantInfoMap.get("age") != null) {
                            ahcsInsuredPreson.setAge((int) insurantInfoMap.get("age"));
                        }

                        if (insurantInfoMap.get("personnelNature") != null) {
                            ahcsInsuredPreson.setPersonnelNature((Integer) insurantInfoMap.get("personnelNature"));
                        }

                        String birthday = null;
                        if (insurantInfoMap.get("birthday") != null) {
                            LogUtil.info("insurantInfoMap-info:"+insurantInfoMap);
                            birthday = StringUtils.stripToEmpty(String.valueOf(insurantInfoMap.get("birthday")));
                        }
                        try {
                            if (StringUtils.isNotEmpty(birthday)) {
                                ahcsInsuredPreson.setBirthday(RapeDateUtil.stringFormatToParseDate(birthday, RapeDateUtil.FULL_DATE_STR));
                            }
                        } catch (ParseException e) {
                            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("{转化组装被保险人信息 转换时间}"));
                        }

                        if(ObjectUtil.isNotEmpty(insurantInfoMap.get("birthdayStr"))){
                            String birthdayStr = (String) insurantInfoMap.get("birthdayStr");
                            ahcsInsuredPreson.setBirthdayStr(birthdayStr);
                            try {
                                ahcsInsuredPreson.setBirthday(RapeDateUtil.stringFormatToParseDate(birthdayStr, RapeDateUtil.SIMPLE_DATE_STR));
                            } catch (ParseException e) {
                                LogUtil.error("转换出生日期错误",e);
                            }
                        }else{
                            ahcsInsuredPreson.setBirthdayStr(DateUtils.parseToFormatStr(ahcsInsuredPreson.getBirthday(), RapeDateUtil.SIMPLE_DATE_STR));
                        }
                        ahcsInsuredPreson.setCertificateNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("certificateNo")));

                        ahcsInsuredPreson.setCertificateType(StringUtils.stripToEmpty((String) insurantInfoMap.get("certificateType")));

                        ahcsInsuredPreson.setProfessionCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("professionCode")));

                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().setInsuredProfessionName(StringUtils.stripToEmpty((String) insurantInfoMap.get("professionChineseName")));
                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().setInsuredProfessionGradeChineseName(StringUtils.stripToEmpty((String) insurantInfoMap.get("professionGradeChineseName")));

                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().setExtCertificateNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("extCertificateNo")));

                        ahcsInsuredPreson.setTelephone(StringUtils.stripToEmpty((String) insurantInfoMap.get("homeTelephone")));

                        ahcsInsuredPreson.setMobileTelephone(StringUtils.stripToEmpty((String) insurantInfoMap.get("mobileTelephone")));

                        ahcsInsuredPreson.setAddress(StringUtils.stripToEmpty((String) insurantInfoMap.get("address")));

                        ahcsInsuredPreson.setEmail(StringUtils.stripToEmpty((String) insurantInfoMap.get("email")));

                        ahcsInsuredPreson.setClientNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("clientNo")));

                        ahcsInsuredPreson.setSchemeNo(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupNo")));

                        ahcsInsuredPreson.setSchemeName(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupName")));

                        insuredPresonDTO.setSubjectName(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupName")));
                        String isSociaSecurity = StringUtils.stripToEmpty((String) insurantInfoMap.get("isSociaSecurity"));

                        insuredPresonDTO.setIsSociaSecurity(isSociaSecurity);
                        ahcsInsuredPreson.setIsSociaSecurity(isSociaSecurity);

                        ahcsInsuredPreson.setAcceptNo(StringUtils.stripToEmpty(id));

                        ahcsInsuredPreson.setPersonnelCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("clientNo")));
//                        if(insurantInfoMap.get("clientNo") != null){
//                            ahcsInsuredPreson.setPersonnelCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("clientNo")));
//                        }

                        ahcsInsuredPreson.setRiskPersonNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("riskPersonNo")));

                        ahcsInsuredPresonExt
                                .setVehicleLicenceCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("vehicleLicenceCode")));

                        ahcsInsuredPresonExt.setVehicleFrameNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("vehicleFrameNo")));

                        ahcsInsuredPresonExt.setEngineNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("engineNo")));

                        ahcsInsuredPresonExt.setFlightNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("flightNo")));

                        if (insurantInfoMap.get("flightDate") != null) {
                            String flightDate = StringUtils.stripToEmpty(String.valueOf(insurantInfoMap.get("flightDate")));
                            try {
                                ahcsInsuredPresonExt
                                        .setFlightDate(RapeDateUtil.stringFormatToParseDate(flightDate, RapeDateUtil.FULL_DATE_STR));
                            } catch (ParseException e) {
                                LogUtil.info("INTF转换航班日期格式异常");
                            }
                        }

                        ahcsInsuredPresonExt.setOriginal(StringUtils.stripToEmpty((String) insurantInfoMap.get("original")));

                        ahcsInsuredPresonExt.setDestination(StringUtils.stripToEmpty((String) insurantInfoMap.get("destination")));

                        ahcsInsuredPresonExt.setIdAhcsInsuredPersonExt(StringUtils.stripToEmpty(id));

                        ahcsInsuredPreson.setEffectiveDate(ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceBeginTime());
                        ahcsInsuredPreson.setInvalidateDate(ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceEndTime());
                        ahcsInsuredPreson.setRelationshipWithApplicant(StringUtils.stripToEmpty((String) insurantInfoMap.get("relationshipWithApplicant")));
                        insuredPresonDTO.setAhcsInsuredPreson(ahcsInsuredPreson);
                        insuredPresonDTO.setAhcsInsuredPersonExt(ahcsInsuredPresonExt);
                        ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs().add(insuredPresonDTO);

                        Map<String, Object> riskPersonMap = (Map<String, Object>) insurantInfoMap.get("riskPersonMap");
                        if (riskPersonMap != null) {
                            String provinceName = (String) riskPersonMap.get("permanentProvinceName");
                            String cityName = (String) riskPersonMap.get("permanentCityName");
                            String address = provinceName + cityName;
                            ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPermanentAddress(address);

                            ahcsPolicyDomainDTO.getAhcsPolicyInfo().setHealthNotification((String) riskPersonMap.get("healthNotification"));

                            List personHealthNoticeList = (List) riskPersonMap.get("personHealthNotice");
                            if (RapeCheckUtil.isNotEmpty(personHealthNoticeList)) {
                                for (int z = 0; z < personHealthNoticeList.size(); z++) {
                                    Map<String, Object> noticeMap = (Map<String, Object>) personHealthNoticeList.get(z);
                                    if (noticeMap != null) {
                                        InsuranceNotificationDTO insuranceNotificationDTO = new InsuranceNotificationDTO();
                                        insuranceNotificationDTO.setNotificationCode((String) noticeMap.get("code"));
                                        insuranceNotificationDTO.setNotificationNoDesc((String) noticeMap.get("detail"));
                                        insuranceNotificationDTO.setNotificationType("2");
                                        insuranceNotificationDTO.setNotificationResult((String) noticeMap.get("result"));
                                        insuranceNotificationDTO.setNotificationValue((String) noticeMap.get("label"));
                                        ahcsPolicyDomainDTO.getPolicyInfoExDTO().getInsuranceNotificationDTOs().add(insuranceNotificationDTO);
                                    }
                                }
                            }
                        }
                    }

                    for (AhcsInsuredPresonDTO dto : ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs()) {
                        if (RapeCheckUtil.isNotEmpty(accountInfoList)) {
                            for (int k = 0; k < accountInfoList.size(); k++) {
                                Map<String, Object> accountInfo = (Map<String, Object>) accountInfoList.get(k);
                                String businessType = (String) accountInfo.get("businessType");
                                String idPerson = (String) accountInfo.get("idPerson");
                                if (dto.getAhcsInsuredPersonExt().getIdAhcsInsuredPersonExt().equals(idPerson) && BUSINESS_TYPE_ORG.equals(businessType)) {

                                    dto.getAhcsInsuredPersonExt().setBankCode((String) accountInfo.get("bankCode"));

                                    dto.getAhcsInsuredPersonExt().setBankAccount((String) accountInfo.get("accountNo"));

                                    dto.getAhcsInsuredPersonExt().setBankName((String) accountInfo.get("bankName"));
                                }
                            }
                        }
                    }
                } else {

                    hasInsuredPerson = initInsurantInfoList(ahcsPolicyDomainDTO, riskGroupInfoMap, riskGroupInfoList,
                            hasInsuredPerson);
                    if (!hasInsuredPerson) {
                        List riskPropertyInfoList = (List) riskGroupInfoMap.get("riskPropertyInfoList");
                        if (RapeCheckUtil.isNotEmpty(riskPropertyInfoList)) {
                            for (Object object : riskPropertyInfoList) {
                                Map<String, Object> riskPropertyInfoMap = (Map<String, Object>) object;
                                hasInsuredPerson = initInsurantInfoList(ahcsPolicyDomainDTO, riskPropertyInfoMap, riskGroupInfoList,
                                        hasInsuredPerson);
                            }
                        }
                    }
                }

                List rescueServiceList = (List) policyInfoMap.get("rescueServiceList");
                if (RapeCheckUtil.isNotEmpty(rescueServiceList)) {
                    for (int z = 0; z < rescueServiceList.size(); z++) {
                        Map<String, Object> rescueService = (Map<String, Object>) rescueServiceList.get(z);
                        String companyName = (String) rescueService.get("companyName");
                        String companyCode = (String) rescueService.get("companyCode");
                        String serviceCode = (String) rescueService.get("serviceCode");
                        rescueCompanyCodes.add(companyCode);
                        rescueCompanys.add(companyName);
                        rescueServiceCodes.add(serviceCode);
                    }
                }

                List checkEmpty = (List) riskGroupInfoMap.get("rescueServiceList");
                if (checkEmpty != null && !checkEmpty.isEmpty()) {
                    rescueServiceList = checkEmpty;
                }
                Map<String, String> rescueServiceCodeName = new HashMap<>();
                if (RapeCheckUtil.isNotEmpty(rescueServiceList)) {
                    for (int z = 0; z < rescueServiceList.size(); z++) {
                        Map<String, Object> rescueService = (Map<String, Object>) rescueServiceList.get(z);
                        String companyName = (String) rescueService.get("companyName");
                        String companyCode = (String) rescueService.get("companyCode");
                        String serviceCode = (String) rescueService.get("serviceCode");
                        String serviceName = (String) rescueService.get("serviceName");
                        rescueCompanyCodes.add(companyCode);
                        rescueCompanys.add(companyName);
                        rescueServiceCodes.add(serviceCode);
                        rescueServiceCodeName.put(serviceCode, serviceName);
                    }
                }

                ahcsPolicyDomainDTO.getPolicyInfoExDTO().getRescueCompanys().addAll(rescueCompanys);
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().getRescueCompanyCodes().addAll(rescueCompanyCodes);
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().getRescueServiceCodes().addAll(rescueServiceCodes);
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setRescueService(rescueServiceCodeName);

                SubjectDTO subjectDTO = new SubjectDTO();
                subjectDTO.setSubjectId(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("id")));
                subjectDTO.setSubjectName(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupName")));
                subjectDTOs.add(subjectDTO);
                ahcsPolicyDomainDTO.getPolicyInfoExDTO().setSubjectDTOs(subjectDTOs);
            }
        }

        hasInsuredPerson = initInsurantInfoList(ahcsPolicyDomainDTO, policyInfoMap, riskGroupInfoList,
                hasInsuredPerson);
        return ahcsPolicyDomainDTO;
    }

    private void initGasInfo(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map insurantInfoMap, RiskObjectDTO riskObjectDTO) {
        GasInfoDTO gasInfoDTO = new GasInfoDTO();
        gasInfoDTO.setGasType((String) insurantInfoMap.get("gasType"));
        gasInfoDTO.setGasTypeName(GasTypeEnum.getName(gasInfoDTO.getGasType()));
        gasInfoDTO.setHouseAddress((String) insurantInfoMap.get("houseAddress"));
        Map riskPersonMap = (Map) insurantInfoMap.get("riskPersonMap");
        if (riskPersonMap != null) {
            gasInfoDTO.setGasUserNumber((String) riskPersonMap.get("gasUserNumber"));
        }
        riskObjectDTO.setGasInfoDTO(gasInfoDTO);
        ahcsPolicyDomainDTO.getRiskObjectList().add(riskObjectDTO);
    }

    private boolean initInsurantInfoList(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map map, List riskGroupInfoList, boolean hasInsuredPerson) {
        List riskPersonInfoList = (List) map.get("insurantInfoList");
        if (RapeCheckUtil.isListNotEmpty(riskPersonInfoList)) {
            hasInsuredPerson = true;
            for (int j = 0; j < riskPersonInfoList.size(); j++) {
                AhcsInsuredPresonDTO insuredPresonDTO = new AhcsInsuredPresonDTO();
                Map insurantInfoMap = (Map) riskPersonInfoList.get(j);
                AhcsInsuredPresonEntity ahcsInsuredPreson = new AhcsInsuredPresonEntity();
                insuredPresonDTO.setPersonnelType(StringUtils.stripToEmpty((String) insurantInfoMap.get("personnelType")));
                ahcsInsuredPreson.setPersonnelAttribute(StringUtils.stripToEmpty((String) insurantInfoMap.get("personnelAttribute")));

                // 批改废弃了该字段，目前用clientNo作为唯一标识，为防止改漏 PersonnelCode 也置为 clientNo
//                ahcsInsuredPreson.setPersonnelCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("personnelCode")));
                ahcsInsuredPreson.setPersonnelCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("clientNo")));

                ahcsInsuredPreson.setCertificateNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("certificateNo")));

                ahcsInsuredPreson.setMobileTelephone(StringUtils.stripToEmpty((String) insurantInfoMap.get("mobileTelephone")));

                ahcsInsuredPreson.setName(StringUtils.stripToEmpty((String) insurantInfoMap.get("name")));

                ahcsInsuredPreson.setCertificateType(StringUtils.stripToEmpty((String) insurantInfoMap.get("certificateType")));

                ahcsInsuredPreson.setAddress(StringUtils.stripToEmpty((String) insurantInfoMap.get("address")));

                ahcsInsuredPreson.setEmail(StringUtils.stripToEmpty((String) insurantInfoMap.get("email")));
                if (RapeCheckUtil.isNotEmpty(riskGroupInfoList)) {
                    Map<String, Object> riskGroupInfoMap = (Map<String, Object>) riskGroupInfoList.get(CommonConstant.ZERO);
                    if (riskGroupInfoMap != null) {
                        ahcsInsuredPreson.setSchemeName(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupName")));

                        insuredPresonDTO.setSubjectName(StringUtils.stripToEmpty((String) riskGroupInfoMap.get("riskGroupName")));
                    }
                }


                String birthday = null;
                if (insurantInfoMap.get("birthday") != null) {
                    birthday = StringUtils.stripToEmpty(String.valueOf(insurantInfoMap.get("birthday")));
                }
                try {
                    if (StringUtils.isNotEmpty(birthday)) {
                        ahcsInsuredPreson.setBirthday(RapeDateUtil.stringFormatToParseDate(birthday, RapeDateUtil.FULL_DATE_STR));
                    }
                } catch (ParseException e) {
                    throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("{转化组装被保险人信息 转换时间}"));
                }


                ahcsInsuredPreson.setClientNo(StringUtils.stripToEmpty((String) insurantInfoMap.get("clientNo")));

                ahcsInsuredPreson.setSexCode(StringUtils.stripToEmpty((String) insurantInfoMap.get("sexCode")));

                if (insurantInfoMap.get("age") != null) {
                    ahcsInsuredPreson.setAge((int) insurantInfoMap.get("age"));
                }

                insuredPresonDTO.setAhcsInsuredPreson(ahcsInsuredPreson);

                ahcsPolicyDomainDTO.getAhcsInsuredPresonDTOs().add(insuredPresonDTO);
            }
        }
        return hasInsuredPerson;
    }


    @Override
    public List<AhcsPolicyDomainDTO> buildPolicyDomain(AhcsPolicyDomainDTO policyDomainDTO, String result, String idPlyRiskPerson) {
        List<AhcsPolicyDomainDTO> policyDomainDTOs = new ArrayList<>();
        Map resultMap = JSON.parseObject(result, Map.class);

        Map<String, Object> policyInfoMap = (Map) resultMap.get("contractDTO");

        policyDomainDTO.setPolicySystem(PolicyConstant.PAS);

//        Map<String, Object> baseInfo = (Map) policyInfoMap.get("baseInfo");
        // 保单抄可显示状态无效的保单，其他情况过滤
//        if (!this.checkPolicyStatus((String) baseInfo.get("status"))) {
//            return null;
//        }
        // 转化PAS-->>INTF PolicyDTO数据模型(保单、投保)
        this.initPolicyInfoPas(policyDomainDTO, policyInfoMap);
        // 转化PAS-->>INTF payInfoDTO 保单支付信息
        this.initPolicyPayInfo(policyDomainDTO, policyInfoMap);
        // 转化PAS-->>INTF PolicyCoinsurance数据模型(共保)
        this.initPolicyCoinsurance4IntfPas(policyDomainDTO, policyInfoMap);
        // 转化PAS-->>INTF RiskGroup保单标的数据模型(险种、责任、免赔、财标的信息)
        this.initPolicyRiskGroupIntfPas(policyDomainDTO, policyInfoMap);
        // 转化PAS-->>INTF PolicyInsured数据模型(特约)
        this.initPolicySpecialPromisePas(policyDomainDTO, policyInfoMap);
        // 转化PAS-->>INTF PolicyInsured数据模型(被保险人)
        this.initPolicyInsured4IntfPas(policyDomainDTO, policyInfoMap, idPlyRiskPerson);

        //转化核保信息underwritingDTOList
        initUnderwritingInfo(policyDomainDTO,policyInfoMap);
        policyDomainDTOs.add(policyDomainDTO);

        return policyDomainDTOs;
    }

    @Override
    public AhcsPolicyDomainDTO buildPolicyDomain(String result, CopyPolicyQueryVO queryVO) {
        Map resultMap = JSON.parseObject(result, Map.class);
        AhcsPolicyDomainDTO policyDomainDTO = new AhcsPolicyDomainDTO();
        Map<String, Object> policyInfoMap = (Map) resultMap.get("data");
        Map<String, Object> contractDTO = (Map) policyInfoMap.get("contractDTO");
        if (contractDTO.get("plyApplyFreezes") != null) {
            JSONArray jarr = (JSONArray) contractDTO.get("plyApplyFreezes");
            policyDomainDTO.setPlyApplyFreezes(jarr.toJavaList(PlyApplyFreeze.class));
        }
        policyDomainDTO.setPolicySystem(PolicyConstant.PAS);
        //在途状态校验新增字段
        policyDomainDTO.setIsProcess(String.valueOf(policyInfoMap.get("isProcess")));
        policyDomainDTO.setEndorseApplyNo(String.valueOf(policyInfoMap.get("endorseApplyNo")));
        policyDomainDTO.setScenceList(String.valueOf(policyInfoMap.get("scenceList")));
        policyDomainDTO.setInvokePurpose(queryVO.getInvokePurpose());

        if (contractDTO.get("propertyList") != null) {
            JSONArray propertyJson = (JSONArray) contractDTO.get("propertyList");
            List<PropertyDTO> propertyList = propertyJson.toJavaList(PropertyDTO.class);
            propertyList.forEach(i-> {
                switch (i.getPropertyCode()) {
                    case "isFamily":
                        policyDomainDTO.setIsFamily(i.getPropertyValue());
                        break;
                    case "profitCenter":
                        policyDomainDTO.setProfitCenter(i.getPropertyValue());
                        break;
                    case "isClaimFamily":
                        policyDomainDTO.setIsClaimFamily(i.getPropertyValue());
                        break;
                }
            });
        }
        if (contractDTO.get("baseInfo") != null) {
            Map<String, Object> baseInfo = (Map) contractDTO.get("baseInfo");
            if (baseInfo.get("onlineOrderNo") != null) {
                policyDomainDTO.setOnlineOrderNo(String.valueOf(baseInfo.get("onlineOrderNo")));
            }
        }
        try {
            if (policyInfoMap.get("inputDate") != null) {
                String date = String.valueOf(policyInfoMap.get("inputDate"));
                policyDomainDTO.setInputDate(RapeDateUtil.parseToFormatDate(date, RapeDateUtil.FULL_DATE_STR));
            }
        } catch (ParseException e) {
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(), GlobalResultStatus.ERROR.format("日期格式转换错误"));
        }

        // 转化PAS-->>INTF PolicyDTO数据模型(保单、投保)
        this.initPolicyInfoPas(policyDomainDTO, contractDTO);
        // 转化PAS-->>INTF payInfoDTO 保单支付信息
        this.initPolicyPayInfo(policyDomainDTO, contractDTO);
        // 转化PAS-->>INTF PolicyCoinsurance数据模型(共保)
        this.initPolicyCoinsurance4IntfPas(policyDomainDTO, contractDTO);
        // 转化PAS-->>INTF RiskGroup保单标的数据模型(险种、责任、免赔、财标的信息)
//      this.initPolicyRiskGroupIntfPas(policyDomainDTO, policyInfoMap);
        initPolicyRiskGroup(policyDomainDTO, contractDTO, queryVO);
        // 转化PAS-->>INTF PolicyInsured数据模型(特约)
        this.initPolicySpecialPromisePas(policyDomainDTO, contractDTO);
        // 转化PAS-->>INTF PolicyInsured数据模型(被保险人)
//      his.initPolicyInsured4IntfPas(policyDomainDTO, policyInfoMap, queryVO.getRiskPersonId());
        initPolicyInsured(policyDomainDTO, contractDTO, queryVO);

        //转化核保信息underwritingDTOList
        initUnderwritingInfo(policyDomainDTO,contractDTO);

        policyDomainDTO.setPaymentCompanyMode(queryVO.getPaymentCompanyMode());

        if("1000".equals(policyDomainDTO.getTargetType())) {
            policyDomainDTO.setIsShowEPolicy("Y");
        }

        return policyDomainDTO;
    }

    /**
     * 处理小条款信息
     * @param ahcsPolicyDomainDTO
     * @param riskGroupObject
     */
    private void dealTermContent(AhcsPolicyDomainDTO ahcsPolicyDomainDTO, Map riskGroupObject) {
        List termContentList = (List) riskGroupObject.get("termContentList");
        if(CollectionUtil.isEmpty(termContentList)){
            return ;
        }
        List<PlanTermContentDTO> planTermContentDTOList=new ArrayList<>();
       for(int i=0;i<termContentList.size();i++){
           Map termContentDtoMap = (Map) termContentList.get(i);
           PlanTermContentDTO dto =new PlanTermContentDTO();
           dto.setTermCode(MapUtils.getString(termContentDtoMap,"termCode"));
           dto.setTermName(MapUtils.getString(termContentDtoMap,"termName"));
           dto.setTermContent(MapUtils.getString(termContentDtoMap,"termContent"));
           dto.setRiskGroupId(MapUtils.getString(termContentDtoMap,"riskGroupId"));
           dto.setSortNo(MapUtils.getIntValue(termContentDtoMap,"sortNo"));
           planTermContentDTOList.add(dto);
       }
       ahcsPolicyDomainDTO.getPolicyInfoExDTO().setPlanTermContentDTOList(planTermContentDTOList);
    }

    /**
     * 转化处理核保信息
     * @param policyDomainDTO
     * @param contractDTOMap
     */
    private void initUnderwritingInfo(AhcsPolicyDomainDTO policyDomainDTO, Map<String, Object> contractDTOMap) {
        JSONArray jsonArray = (JSONArray) contractDTOMap.get("underwritingHistoryDTOList");
        if(Objects.isNull(jsonArray)){
            return;
        }
        List<PolicyUwHistoryDTO> uwList = JSONObject.parseArray(jsonArray.toJSONString(), PolicyUwHistoryDTO.class);
        if(CollectionUtil.isEmpty(uwList)){
            return;
        }
        List<ClmsPolicyHistoryUwInfoDTO> uwInfoDTOList = new ArrayList<>();
        for (PolicyUwHistoryDTO uwDto : uwList) {
            ClmsPolicyHistoryUwInfoDTO dto = new ClmsPolicyHistoryUwInfoDTO();
            dto.setPolicyNo(uwDto.getPolicyNo());
            dto.setBusinessType(uwDto.getBusinessType());
            dto.setPlanName(uwDto.getPlanName());
            dto.setSchemeName(uwDto.getRiskGroupName());
            dto.setProductName(uwDto.getProductName());
            dto.setPlanStatus(uwDto.getPlanStatus());
            try {
                dto.setUwCompleteDate(DateUtils.parseToFormatDate(uwDto.getUnderwriteDate(),DateUtils.FULL_DATE_STR));
                dto.setInsuranceBeginDate(DateUtils.parseToFormatDate(uwDto.getInsuranceBeginDate(),DateUtils.FULL_DATE_STR));
                dto.setInsuranceEndDate(DateUtils.parseToFormatDate(uwDto.getInsuranceEndDate(),DateUtils.FULL_DATE_STR));
            } catch (ParseException e) {
                throw new GlobalBusinessException("抄单核保信息日期转换异常");
            }
            dto.setUwExceptions(uwDto.getUnderwritingOpinion());
            dto.setUwConclusion(uwDto.getUnderwritingConclusion());
            //1-表示数据来自于抄单
            dto.setUwDataSource("1");
            dto.setInsuredName(uwDto.getName());
            dto.setClientNo(uwDto.getClientNo());
            uwInfoDTOList.add(dto);
        }
        policyDomainDTO.setPolicyHistoryUwInfoDTOList(uwInfoDTOList);


    }
}
