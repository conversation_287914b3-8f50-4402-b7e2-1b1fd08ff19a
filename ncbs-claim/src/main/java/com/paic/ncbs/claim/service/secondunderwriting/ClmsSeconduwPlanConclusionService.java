package com.paic.ncbs.claim.service.secondunderwriting;


import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPlanConclusionEntity;

import java.util.List;

/**
 * 理赔二核险种层核保结论表(ClmsSeconduwPlanConclusionEntity)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-14 14:07:13
 */
public interface ClmsSeconduwPlanConclusionService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSeconduwPlanConclusionEntity queryById(String id);
    
     /**
     * 通过报案号查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSeconduwPlanConclusionEntity queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsSeconduwPlanConclusionEntity 实例对象
     * @return 实例对象
     */
    ClmsSeconduwPlanConclusionEntity insert(ClmsSeconduwPlanConclusionEntity clmsSeconduwPlanConclusionEntity);

    /**
     * 批量保存
     * @param entity
     */
    void saveBatch(List<ClmsSeconduwPlanConclusionEntity> entity);

    /**
     * 修改数据
     *
     * @param clmsSeconduwPlanConclusionEntity 实例对象
     * @return 实例对象
     */
    ClmsSeconduwPlanConclusionEntity update(ClmsSeconduwPlanConclusionEntity clmsSeconduwPlanConclusionEntity);
    
    /**
     * 根据报案号修改数据
     *
     * @param clmsSeconduwPlanConclusion 实例对象
     * @return 影响行数
     */
    int updateByReportNo( String reportNo, int caseTimes);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

}
