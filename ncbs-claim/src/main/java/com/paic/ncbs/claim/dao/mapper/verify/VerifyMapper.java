package com.paic.ncbs.claim.dao.mapper.verify;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface VerifyMapper extends BaseDao<VerifyDTO> {

    void insertVerify(VerifyDTO verify);

    VerifyDTO getVerify(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<VerifyDTO> getVerifyList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void updateVerify(VerifyDTO verify);

    void deleteVerify(VerifyDTO verify);

    String getverifyunPerson (@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getSettleumPerson (@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}
