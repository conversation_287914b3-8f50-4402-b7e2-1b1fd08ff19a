package com.paic.ncbs.claim.model.dto.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.page.Pager;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 打包支付列表DTO
 */
public class BatchPaymentDTO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 机构编码
     */
    private String departmentAbbrCode;

    /**
     * 机构名称
     */
    private String departmentAbbrName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 支付用途
     */
    private String paymentType;

    /**
     * 结算总金额
     */
    private BigDecimal sumAmount;

    /**
     * 支付状态
     */
    private String mergePaymentStatus;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sysCtime;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 支付修改信息记录
     */
    private String modifyInfo;

    /**
     * 创建时间-开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sysCtimeStart;

    /**
     * 创建时间-结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sysCtimeEnd;

    /**
     * 帐号类型:个人帐号=1,公司帐号=0
     */
    private String bankAccountAttribute;

    /**
     * 领款方式
     */
    private String payType;

    /**
     * 收款人
     */
    private String clientName;

    /**
     * 收款人账号
     */
    private String clientBankAccount;

    /**
     * 客户银行代码
     */
    private String clientBankCode;

    /**
     * 客户开户银行
     */
    private String clientBankName;

    /**
     * 开户行明细
     */
    private String bankDetailCode;

    /**
     * 开户行明细
     */
    private String bankDetail;

    /**
     * 分页
     */
    private Pager pager;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getDepartmentAbbrCode() {
        return departmentAbbrCode;
    }

    public void setDepartmentAbbrCode(String departmentAbbrCode) {
        this.departmentAbbrCode = departmentAbbrCode;
    }

    public String getDepartmentAbbrName() {
        return departmentAbbrName;
    }

    public void setDepartmentAbbrName(String departmentAbbrName) {
        this.departmentAbbrName = departmentAbbrName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(BigDecimal sumAmount) {
        this.sumAmount = sumAmount;
    }

    public String getMergePaymentStatus() {
        return mergePaymentStatus;
    }

    public void setMergePaymentStatus(String mergePaymentStatus) {
        this.mergePaymentStatus = mergePaymentStatus;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getModifyInfo() {
        return modifyInfo;
    }

    public void setModifyInfo(String modifyInfo) {
        this.modifyInfo = modifyInfo;
    }

    public Date getSysCtimeStart() {
        return sysCtimeStart;
    }

    public void setSysCtimeStart(Date sysCtimeStart) {
        this.sysCtimeStart = sysCtimeStart;
    }

    public Date getSysCtimeEnd() {
        return sysCtimeEnd;
    }

    public void setSysCtimeEnd(Date sysCtimeEnd) {
        this.sysCtimeEnd = sysCtimeEnd;
    }

    public String getBankAccountAttribute() {
        return bankAccountAttribute;
    }

    public void setBankAccountAttribute(String bankAccountAttribute) {
        this.bankAccountAttribute = bankAccountAttribute;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount;
    }

    public String getClientBankCode() {
        return clientBankCode;
    }

    public void setClientBankCode(String clientBankCode) {
        this.clientBankCode = clientBankCode;
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName;
    }

    public String getBankDetailCode() {
        return bankDetailCode;
    }

    public void setBankDetailCode(String bankDetailCode) {
        this.bankDetailCode = bankDetailCode;
    }

    public String getBankDetail() {
        return bankDetail;
    }

    public void setBankDetail(String bankDetail) {
        this.bankDetail = bankDetail;
    }

    public Pager getPager() {
        return pager;
    }

    public void setPager(Pager pager) {
        this.pager = pager;
    }

}
