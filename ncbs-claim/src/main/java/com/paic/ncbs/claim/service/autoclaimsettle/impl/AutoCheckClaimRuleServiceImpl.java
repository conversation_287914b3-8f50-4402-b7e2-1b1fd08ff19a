package com.paic.ncbs.claim.service.autoclaimsettle.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.enums.RuleTypeEnums;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.autoclaimsettle.ClaimRuleResultDTO;
import com.paic.ncbs.claim.model.dto.autoclaimsettle.RuleSubResultDTO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO;
import com.paic.ncbs.claim.service.autoclaimsettle.AutoCheckClaimRuleService;
import com.paic.ncbs.claim.service.rule.AutoRuleRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自动核责
 */
@Service
public class AutoCheckClaimRuleServiceImpl implements AutoCheckClaimRuleService {
    @Autowired
    private CaseClassMapper caseClassMapper;
    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;

    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private AutoRuleRecordService autoRuleRecordService;
    /**
     * 返回true
     * @param reportNo
     * @return
     */
    @Override
    @Transactional
    public ClaimRuleResultDTO autoCheckRule(String reportNo,Integer caseTimes) {
        ClaimRuleResultDTO claimRuleResultDTO =new ClaimRuleResultDTO();
        //默认自动核责通过通过
        claimRuleResultDTO.setAutoPass(true);
        List<RuleSubResultDTO> resutlDTOList = new ArrayList<>();
        checkRule(reportNo,caseTimes,resutlDTOList);
        if(CollectionUtils.isNotEmpty(resutlDTOList)){
            claimRuleResultDTO.setAutoPass(false);
        }
        claimRuleResultDTO.setRuleSubResultList(resutlDTOList);
        //记录自动理赔
        claimRuleResultDTO.setReportNo(reportNo);
        claimRuleResultDTO.setCaseTimes(caseTimes);
        claimRuleResultDTO.setRuleType(BpmConstants.OC_MANUAL_SETTLE);
        autoRuleRecordService.saveRuleRecord(claimRuleResultDTO);
        return claimRuleResultDTO;
    }

    /**
     * 规则判断
     * @param reportNo
     * @param caseTimes
     * @param resutlDTOList
     */
    private void checkRule(String reportNo, Integer caseTimes, List<RuleSubResultDTO> resutlDTOList) {

        if(caseTimes>1){
            setValue(RuleTypeEnums.RULE_TYPE_000001.getCode(),RuleTypeEnums.RULE_TYPE_000001.getName(),resutlDTOList,null);
        }
        List<MedicalBillInfoVO> billInfoVOList = medicalBillInfoMapper.getAllBillInfo(reportNo,caseTimes);
        //查询保单有效期
        AhcsPolicyInfoEntity policyEntity = ahcsPolicyInfoMapper.selectByReportNo(reportNo).get(0);

        //案件类别
        checkCaseClass(reportNo,caseTimes,billInfoVOList,resutlDTOList);
        //查询被保险人姓名
        ReportCustomerInfoEntity entity =reportCustomerInfoMapper.getCustomerName(reportNo);
        //校验发票 交款人  性别等
        checkBIllInfo(entity,billInfoVOList,resutlDTOList,policyEntity);

    }



    /**
     * 损信息栏内的所有发票，发票“交款人姓名”均与被保险人一致。若交款人姓名为空时，默认该张发票的姓名一致性校验结果通过
     * 定损信息栏内的所有发票，发票“就诊人性别”均与被保险人的性别一致。若就诊人性别为空时，默认该张发票的性别一致性校验结果通过
     * //定损信息栏内的所有发票，开始日期、结束日期，均在当前案件抄单的保单有效期内
     *
     * @param entity
     * @param billInfoVOList
     * @param resutlDTOList
     * @param policyEntity
     */
    private void checkBIllInfo(ReportCustomerInfoEntity entity, List<MedicalBillInfoVO> billInfoVOList, List<RuleSubResultDTO> resutlDTOList, AhcsPolicyInfoEntity policyEntity) {
        if(CollectionUtils.isEmpty(billInfoVOList)){
            return;
        }
        if(Objects.isNull(entity)){
            throw new GlobalBusinessException("被保险人信息不存在！");
        }
        for (MedicalBillInfoVO bill : billInfoVOList) {
            if(StringUtils.isNotEmpty(bill.getPayer())){
                if(!Objects.equals(entity.getName(),bill.getPayer())){
                    String reason=bill.getBillNo()+RuleTypeEnums.RULE_TYPE_000003.getName();
                    setValue(RuleTypeEnums.RULE_TYPE_000003.getCode(),reason,resutlDTOList,bill.getBillNo());
                }
            }
            if(StringUtils.isNotEmpty(bill.getGender())){
                if(Objects.equals(entity.getSexCode(),bill.getGender())){
                    String reason=bill.getBillNo()+RuleTypeEnums.RULE_TYPE_000004.getName();
                    setValue(RuleTypeEnums.RULE_TYPE_000004.getCode(),reason,resutlDTOList,bill.getBillNo());
                }
            }
            //返回保单开始日期大于发票日期
            if(DateUtils.compareTimeBetweenDate(bill.getStartDate(),policyEntity.getInsuranceBeginTime())||DateUtils.compareTimeBetweenDate(policyEntity.getInsuranceEndTime(),bill.getStartDate())){
                String reason=bill.getBillNo()+RuleTypeEnums.RULE_TYPE_000005.getName();
                setValue(RuleTypeEnums.RULE_TYPE_000005.getCode(),reason,resutlDTOList,bill.getBillNo());
            }
            if(!Objects.equals(CommonConstant.PUBLIC_HOSPITAL,bill.getHospitalPropertyDes())){
                String reason=bill.getBillNo()+RuleTypeEnums.RULE_TYPE_000006.getName();
                setValue(RuleTypeEnums.RULE_TYPE_000006.getCode(),reason,resutlDTOList,bill.getBillNo());
            }

        }
    }

    /**
     * 案件类别校验
     */
    private void checkCaseClass(String reportNo, Integer caseTimes, List<MedicalBillInfoVO> billInfoVOList, List<RuleSubResultDTO> resutlDTOList) {
        //案件类别判断:案件类别仅是“意健险”，且二级分类仅含“意外医疗”、“疾病住院医疗”、“疾病门急诊医疗”时，定损信息不得为空
        List<String>  caseClassList = caseClassMapper.getCaseClassParentAll(reportNo,caseTimes, BpmConstants.CHECK_DUTY,"1");
        if(CollectionUtils.isEmpty(caseClassList)){
            throw new GlobalBusinessException("案件类别不存在！");
        }
        //1表示大类为意健险
        List<String>  eqOnelist = caseClassList.stream().filter(s -> Objects.equals("1",s)).collect(Collectors.toList());
        //不为意健险
        List<String>  noEqOnelist = caseClassList.stream().filter(s -> !Objects.equals("1",s)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(eqOnelist)&&CollectionUtils.isEmpty(noEqOnelist)){
            //查询二级类型
            List<String> caseSubClassList = caseClassMapper.getCaseClassList(reportNo, caseTimes, BpmConstants.CHECK_DUTY);
            if(CollectionUtils.isEmpty(caseSubClassList)){
                throw new GlobalBusinessException("案件类别不存在！");
            }
            List<String> subClassList = caseSubClassList.stream().filter(s ->Objects.equals(InsuredApplyTypeEnum.ACCIDENTAL_MEDICAL_TREATMENT.getType(),s)||Objects.equals(InsuredApplyTypeEnum.SICKNESS_HOSPITALIZATION.getType(),s)||Objects.equals(InsuredApplyTypeEnum.EMERGENCY_MEDICINE.getType(),s)).collect(Collectors.toList());
            List<String> nosubClassList = caseSubClassList.stream().filter(s ->!Objects.equals(InsuredApplyTypeEnum.ACCIDENTAL_MEDICAL_TREATMENT.getType(),s) && !Objects.equals(InsuredApplyTypeEnum.SICKNESS_HOSPITALIZATION.getType(),s) && !Objects.equals(InsuredApplyTypeEnum.EMERGENCY_MEDICINE.getType(),s)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(subClassList) && CollectionUtils.isEmpty(nosubClassList)){
                if(CollectionUtils.isEmpty(billInfoVOList)){
                    setValue(RuleTypeEnums.RULE_TYPE_000002.getCode(),RuleTypeEnums.RULE_TYPE_000002.getName(),resutlDTOList,null);
                }
            }
        }
    }

    public void setValue(String ruleCode, String ruleReason, List<RuleSubResultDTO> resutlDTOList, String billNo){
        RuleSubResultDTO resutlDTO=new RuleSubResultDTO();
        resutlDTO.setRuleCode(ruleCode);
        resutlDTO.setRuleReason(ruleReason);
        resutlDTO.setBillNo(billNo);
        resutlDTOList.add(resutlDTO);
    }
}
