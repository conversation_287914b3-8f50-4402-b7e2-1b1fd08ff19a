package com.paic.ncbs.claim.model.vo.taskdeal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("工作台页面显示")
public class WorkBenchTaskVO {

    @ApiModelProperty("报案号")
    private String reportNo;

    /**
     * 核赔、立案审批、拒赔审批、零注审批、提调审批、调查审批、
     * 支付修改审批、报案跟踪、收单、理算、调查、沟通、支付修改。
     * 追偿、追偿审批、追偿费用审批
     */
    @ApiModelProperty("任务分类")
    private String taskDefinitionBpmKey;

    @ApiModelProperty("被保险人")
    private String insuredName;

    @ApiModelProperty("被保险人级别-五星Y/普通N")
    private String insuredLevel;

    @ApiModelProperty("整案时长")
    private String caseLength;

    @ApiModelProperty("当前时长")
    private String currentLength;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("保单归属机构名称")
    private String policyDepartmentName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("派工时间")
    private Date assigneeTime;

    @ApiModelProperty("未决金额")
    private String estimateAmount;

    @ApiModelProperty("提交人")
    private String submitName;

    @ApiModelProperty("操作 Y:处理/N：认领")
    private String operate;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("案件等级")
    private String taskGrade;

    @ApiModelProperty("审核等级")
    private String auditGrade;

    @ApiModelProperty("提调审批任务跳转用到id")
    private String idAhcsInvestigate;

    @ApiModelProperty("调查任务跳转用到id")
    private String idAhcsInvestigateTask;

    @ApiModelProperty("调查审核跳转用到id")
    private String idAhcsInvestigateTaskAudit;

    @ApiModelProperty("调查任务跳转用到id")
    private String idAhcsInvestigateTaskReject;

    @ApiModelProperty("沟通任务跳转用到id")
    private String idAhcsCommunicateBase;

    private String personnelAttribute = "";

    @ApiModelProperty(value = "上一节点提交人姓名")
    private String applyerName;
    @ApiModelProperty(value = "上一节点提交人")
    private String applyer;

    @ApiModelProperty("修正未决金额")
    private String estimateChangeAmount;

    @ApiModelProperty("重开金额")
    private String restartAmount;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("方案名称")
    private String riskGroupName;

    @ApiModelProperty("限制保单处理标识：Y-限制处理，N-不限制处理")
    private String limitPolicyDealFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("起保日期")
    private Date policyBeginTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("终保日期")
    private Date policyEndTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("报案日期")
    private Date reportDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("事故日期")
    private Date accidentDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("结案日期")
    private Date completeTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("立案日期")
    private Date registerDate;

    @ApiModelProperty("赔案号")
    private String caseNo;
    @ApiModelProperty("费用金额")
    private BigDecimal feeAmount;
    @ApiModelProperty("退回原因")
    private String returnReason;
    @ApiModelProperty("退回类型,默认:发票退回")
    private String returnType;
    @ApiModelProperty("退回日期")
    private String returnDate;
    @ApiModelProperty("费用发票修改标记 0:未修改 1:待修改 2:修改完成")
    private String isModifiedFlag;
    @ApiModelProperty("理算赔款金额")
    private BigDecimal settleAmount;
    @ApiModelProperty("理算费用金额")
    private BigDecimal dutyFee;
    @ApiModelProperty("未决修正金额合计")
    private BigDecimal sumEstimateChangeAmount;
    @ApiModelProperty("未决修正差额")
    private BigDecimal estimateChangeDifference;
    @ApiModelProperty("流程互斥标识00-无待处理子流程，11-有子流程，10-有互斥子流程")
    private String subProcessFlag;

    @ApiModelProperty("理赔支出金额（赔款/费用）")
    private BigDecimal paymentAmount;
    @ApiModelProperty("理赔支出类型（赔款、预赔赔款、费用、预赔费用）")
    private String clmPaymentTypeName;
    @ApiModelProperty("业务key")
    private  String businessKey;
    @ApiModelProperty("序号")
    private Integer serialNo;
    @ApiModelProperty("追偿金额")
    private BigDecimal replevyAmount;
    @ApiModelProperty("追偿费用")
    private BigDecimal replevyFee;
    @ApiModelProperty("追偿号")
    private String replevyNo;


    @ApiModelProperty("关联的黑名单ID")
    private String blackListId;
    @ApiModelProperty("姓名/名称")
    private String partyName;
    @ApiModelProperty("证件类型")
    private String idType;
    @ApiModelProperty("证件号码")
    private String idNum;
    @ApiModelProperty("电话号码")
    private String phoneNum;
    @ApiModelProperty("风险类型")
    private String riskType;
    @ApiModelProperty("风险类型中文描述")
    private String riskTypeName;
    @ApiModelProperty("证件类型中文描述")
    private String idTypeName;
    @ApiModelProperty("来源")
    private String blackSource;
    @ApiModelProperty("关联报案号")
    private String relatedReportNo;
    @ApiModelProperty("诉讼案编号")
    private String lawsuitNo;
    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("第三方类型：01-公估，02-律师，03-其他")
    private String thirdPartyType;
    @ApiModelProperty("第三方类型名称：公估，律师，其他")
    private String thirdPartyName;
    @ApiModelProperty("公估名称")
    private String entrustDpmName;
    @ApiModelProperty("案件状态")
    private String caseStatus;

    @ApiModelProperty("提交人")
    private String submitter;



}
