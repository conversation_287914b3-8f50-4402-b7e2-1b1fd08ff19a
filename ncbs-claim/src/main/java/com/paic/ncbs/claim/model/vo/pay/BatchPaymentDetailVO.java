package com.paic.ncbs.claim.model.vo.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 批量支付明细
 */
public class BatchPaymentDetailVO {

    /**
     * 报案来源
     */
    private String reportMode;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 机构编码
     */
    private String departmentAbbrCode;

    /**
     * 机构名称
     */
    private String departmentAbbrName;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 支付用途
     */
    private String paymentType;

    /**
     * 支付状态
     */
    private String mergePaymentStatus;

    /**
     * 支付成功时间
     */
    private String sysUtime ;

    /**
     * 金额
     */
    private BigDecimal paymentAmount;

    /**
     * 收款人
     */
    private String clientName;

    /**
     * 收款人账号
     */
    private String clientBankAccount;

    /**
     * 结案时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date endCaseDate;

    /**
     * 结案时间-开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endCaseDateStart;

    /**
     * 结案时间-结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endCaseDateEnd;

    public String getReportMode() {
        return reportMode;
    }

    public void setReportMode(String reportMode) {
        this.reportMode = reportMode;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getDepartmentAbbrCode() {
        return departmentAbbrCode;
    }

    public void setDepartmentAbbrCode(String departmentAbbrCode) {
        this.departmentAbbrCode = departmentAbbrCode;
    }

    public String getDepartmentAbbrName() {
        return departmentAbbrName;
    }

    public void setDepartmentAbbrName(String departmentAbbrName) {
        this.departmentAbbrName = departmentAbbrName;
    }

    public String getPaySerialNo() {
        return paySerialNo;
    }

    public void setPaySerialNo(String paySerialNo) {
        this.paySerialNo = paySerialNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getMergePaymentStatus() {
        return mergePaymentStatus;
    }

    public void setMergePaymentStatus(String mergePaymentStatus) {
        this.mergePaymentStatus = mergePaymentStatus;
    }

    public String getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(String sysUtime) {
        this.sysUtime = sysUtime;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount;
    }

    public Date getEndCaseDate() {
        return endCaseDate;
    }

    public void setEndCaseDate(Date endCaseDate) {
        this.endCaseDate = endCaseDate;
    }

    public Date getEndCaseDateStart() {
        return endCaseDateStart;
    }

    public void setEndCaseDateStart(Date endCaseDateStart) {
        this.endCaseDateStart = endCaseDateStart;
    }

    public Date getEndCaseDateEnd() {
        return endCaseDateEnd;
    }

    public void setEndCaseDateEnd(Date endCaseDateEnd) {
        this.endCaseDateEnd = endCaseDateEnd;
    }

}
