package com.paic.ncbs.claim.model.dto.print;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * <p>
 * 共保摊回通知书打印日志记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ClmCoinsPrintRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 发送次数，用于补偿机制
     */
    private Integer sendTimes;

    /**
     * 预赔/赔付
     */
    private String claimType;

    /**
     * 请求是否成功，1-成功 0-失败
     */
    private String isSuccess;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private LocalDateTime sysUtime;
}
