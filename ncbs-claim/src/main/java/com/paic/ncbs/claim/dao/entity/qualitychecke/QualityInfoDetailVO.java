package com.paic.ncbs.claim.dao.entity.qualitychecke;
import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo;
import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.List;
@Data
public class QualityInfoDetailVO implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 质检信息主表数据
     */
    @ApiModelProperty("质检信息主表数据")
    private ClmsQualityInfo qualityInfo;

    /**
     * 质检详情列表
     */
    @ApiModelProperty("质检详情列表")
    private List<ClmsQualityDetail> qualityDetails;
    /**
     * 质检详情列表
     */
    @ApiModelProperty("质检轨迹列表")
    List<ClmsQualityRecord> qualityRecords;

    /**
     * 标志
     */
    @ApiModelProperty("提交标志")
    private String submitflag;
}
