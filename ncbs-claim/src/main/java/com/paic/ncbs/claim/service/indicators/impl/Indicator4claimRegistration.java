package com.paic.ncbs.claim.service.indicators.impl;

import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 立案时效计算
 */
@Service("Indicator4claimRegistration")
public class Indicator4claimRegistration extends  ClmsCaseIndicatorServiceImpl{
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    protected void saveStablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.stable4claimRegistration(lastLog, now);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    protected void resaveUnstablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.unstable4claimRegistration(lastLog, now);
    }
}
