package com.paic.ncbs.claim.controller.who.channelprocess;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseInfoParameterDTO;
import com.paic.ncbs.claim.service.checkloss.ChannelProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "通道")
@RestController
@RequestMapping("/who/app/channelProcessAction")
public class ChannelProcessController extends BaseController {

	@Resource(name = "channelProcessService")
	private ChannelProcessService channelProcessService;


	@ApiOperation("添加通道号")
	@PostMapping("/addChannelProcessId")
	public ResponseResult<Object> addChannelProcessId(@RequestBody CaseInfoParameterDTO caseInfoParameter) throws GlobalBusinessException {
		caseInfoParameter.setUserId(WebServletContext.getUserId());
		LogUtil.audit("添加通道号 CaseInfoParameterDTO：{}" , JSON.toJSONString(caseInfoParameter));
		channelProcessService.addChannelProcessId(caseInfoParameter);
		return ResponseResult.success();
	}

}
