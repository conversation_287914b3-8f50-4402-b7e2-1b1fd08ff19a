package com.paic.ncbs.claim.dao.entity.copypolicy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * global信息主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Getter
@Setter
@TableName("clm_global_return_info")
public class ClmGlobalReturnInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField("id")
    private String id;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 报案号(年)
     */
    @TableField("rc00_rciv_year")
    private String rc00RcivYear;

    /**
     * 报案号
     */
    @TableField("rc00_rciv_seq_num")
    private Long rc00RcivSeqNum;

    /**
     * 立案号(yyyyMMdd)
     */
    @TableField("ac00_rciv_date")
    private String ac00RcivDate;

    /**
     * 立案号
     */
    @TableField("ac00_rciv_seq_num")
    private Long ac00RcivSeqNum;

    /**
     * 立案号
     */
    @TableField("agrm_seq_num")
    private Long agrmSeqNum;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 最新修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;
}
