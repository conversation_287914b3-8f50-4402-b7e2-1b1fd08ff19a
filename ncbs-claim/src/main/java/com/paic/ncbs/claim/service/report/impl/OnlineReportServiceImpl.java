package com.paic.ncbs.claim.service.report.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ahcs.*;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.checkloss.HospitalInfoMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonDiagnoseMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.report.InsuredPersonMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.*;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO;
import com.paic.ncbs.claim.model.dto.duty.SurveyDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO;
import com.paic.ncbs.claim.model.dto.ocas.PlyApplyFreeze;
import com.paic.ncbs.claim.model.dto.other.CityDefineDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.report.*;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoExDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyPropertyRequestVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyRiskPropertyQueryVO;
import com.paic.ncbs.claim.model.vo.report.*;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.checkloss.PersonHospitalService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.other.CityDefineService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.report.EstimateLossService;
import com.paic.ncbs.claim.service.report.OnlineReportService;
import com.paic.ncbs.claim.service.report.PolicyService;
import com.paic.ncbs.claim.service.report.TelReportTransationService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RefreshScope
@Service("onlineReportService")
public class OnlineReportServiceImpl implements OnlineReportService {

    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;
    @Autowired
    private CommonService commonService;
    @Autowired
    private EstimateLossService estimateLossService;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private CityDefineService cityDefineService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private TelReportTransationService telReportTransationService;

    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    private PersonHospitalService personHospitalService;

    @Autowired
    private CommonParameterMapper commonParameterMapper;

    @Autowired
    private PersonDiagnoseMapper personDiagnoseMapper;

    @Autowired
    private DutySurveyService dutySurveyService;

    @Autowired
    private HospitalInfoMapper hospitalInfoDao;

    @Autowired
    private InsuredPersonMapper insuredPersonMapper;

    @Autowired
    private SmsInfoService smsInfoService;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    @Autowired
    private PolicyService policyService;

    @Value("${special.productCode}")
    private String specialProduct;

    @Override
    public OnlineReportResponseVO saveOnlineReport(OnlineReportVO onlineReportVO) {
        String reportNo ;
        String msg ;
        long a = System.currentTimeMillis();
        //校验
        this.checkReportInfo(onlineReportVO);
        long b = System.currentTimeMillis();

        List<String> expiredPolicyNos = new ArrayList<>();
        //补充报案领域模型
        ReportDomainDTO reportDomain = this.initReportDomain(onlineReportVO,expiredPolicyNos);
        long c = System.currentTimeMillis();

        //保存案件信息
        telReportTransationService.insertReportDomain(reportDomain);
        long d = System.currentTimeMillis();

        //生成报案跟踪
        reportNo = reportDomain.getReportNo();
        //操作记录
        String name = ReportSubModeEnum.getName(reportDomain.getReportInfo().getReportSubMode());
        if(StringUtils.isEmptyStr(name)){
            name = reportDomain.getReportAcceptUm();
        }
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REPORT, "线上报案", null, name);
        bpmService.startProcessTelReport(reportNo, reportDomain.getCaseTimes(),
                BpmConstants.OC_REPORT_TRACK, reportDomain.getAcceptDepartmentCode());
        boolean isInited = estimateService.checkExists(reportNo, reportDomain.getCaseTimes(), EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        if (!isInited) {
            EstimatePolicyFormDTO epf = new EstimatePolicyFormDTO();
            epf.setReportNo(reportNo);
            epf.setCaseTimes(reportDomain.getCaseTimes());
            epf.setEstimateType(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
            estimateService.initEstimateData(epf);
        }
        long e = System.currentTimeMillis();
        LogUtil.info("REPORT_TIME:检验入参耗时={}，构建报案领域模型耗时={}，保存案件信息耗时={}，生成报案跟踪耗时={}", b-a, c-b, d-c, e-d);

        //执行报即立规则
        AhcsDomainDTO ahcsDomainDTO = new AhcsDomainDTO();
        ahcsDomainDTO.setReportNo(reportNo);
        ahcsDomainDTO.setAhcsPolicyDomainDTOs(reportDomain.getAhcsPolicyDomainDTOs());
        estimateLossService.runReportEstimateRule(ahcsDomainDTO,onlineReportVO.getInsuredApplyType(),onlineReportVO.getReportAcceptUm());
        // 构建领款人信息并保存
        savePayMent(onlineReportVO,reportNo);
        //global保单回流报案接口
        List<String> policyNos = onlineReportVO.getPolicyNos();
        if(policyNos!=null && policyNos.size()>0){
            for(String policyNo:policyNos){
                if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                    //global保单
                    globalPolicyService.returnReportToGlobal(reportNo);
                }
            }
        }


        // 构建医院、伤害原因、疾病 信息并保存
        saveHospitalAndDisease(onlineReportVO,reportNo,reportDomain.getChannelProcess().getIdAhcsChannelProcess());
        // 报案成功返回报案号
        msg = null;
        if(!expiredPolicyNos.isEmpty()){
            msg = "被剔除的保单:"+expiredPolicyNos;
        }
        //查询客户号
        InsuredPersonDTO insuredPersonDTO = insuredPersonMapper.getInsuredPersonDTO(reportNo);
        String clientNo = Objects.nonNull(insuredPersonDTO)?insuredPersonDTO.getClientNo() : null;

        //400客服报案（新核心非互联网业务）短信通知
        if (StringUtils.isNotEmpty(onlineReportVO.getReportMode()) && ReportConstant.CUSTOMER_SERVICE_SYSTEM.equals(onlineReportVO.getReportMode())) {
            try {
                TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, reportDomain.getCaseTimes(),BpmConstants.OC_REPORT_TRACK);
                if (taskInfoDTO != null){
                    if (StringUtils.isNotEmpty(taskInfoDTO.getAssigner())) {
                        smsInfoService.sendBusinessSmsOnCreateReport(SmsTypeEnum.CREATE_REPORT,reportNo,reportDomain.getCaseTimes(),taskInfoDTO.getAssigner());
                    } else {
                        LogUtil.info("400客服报案案件：{}，未查询到报案跟踪处理人信息！",reportNo);
                    }
                } else {
                    LogUtil.info("400客服报案案件：{}，未查询到报案跟踪任务信息！",reportNo);
                }
            } catch (Exception exception){
                LogUtil.error("400客服报案案件："+reportNo+"，发送短信失败！",exception.getMessage());
            }
        }


        long f = System.currentTimeMillis();
        LogUtil.info("REPORT_TIME:报案总耗时={}, reportNo:{}", f - a, reportNo);
        return new OnlineReportResponseVO(reportNo,msg,clientNo);
    }

    @Override
    public OnlineReportCheckResponseVO checkOnlineReport(OnlineReportCheckVO onlineReportCheckVO) {
        OnlineReportCheckResponseVO onlineReportCheckResponseVO = new OnlineReportCheckResponseVO();
        onlineReportCheckResponseVO.setIsCanReport("N");
        String certificateNo = onlineReportCheckVO.getCertificateNo();
        Date accidentDate = onlineReportCheckVO.getAccidentDate();
        String clientName = onlineReportCheckVO.getClientName();
        String policyNo = onlineReportCheckVO.getPolicyNo();
        RapeCheckUtil.checkParamEmpty(accidentDate, "事故日期");
        //RapeCheckUtil.checkParamEmpty(certificateNo, "被保险人证件号");
        //RapeCheckUtil.checkParamEmpty(onlineReportCheckVO.getCertificateType(), "被保险人证件类型");
        RapeCheckUtil.checkParamEmpty(clientName, "被保险人姓名");
        RapeCheckUtil.checkParamEmpty(policyNo, "保单号");
        Date currDate = new Date();
        if (accidentDate.getTime() > currDate.getTime()){
            onlineReportCheckResponseVO.setIsCanReport("N");
            onlineReportCheckResponseVO.setNotReportReason("事故日期不能在当前时间之后");
            return onlineReportCheckResponseVO;
        }
        OcasPolicyPlanDutyDTO policyInfoByPolicyNo = Optional.ofNullable(ocasMapper.getPolicyInfoByPolicyNo(policyNo)).orElseThrow(()-> new GlobalBusinessException("保单号对应的保单不存在！"));
        if (policyInfoByPolicyNo.getInsuranceBeginDate().getTime() > accidentDate.getTime()
                || policyInfoByPolicyNo.getInsuranceEndDate().getTime() < accidentDate.getTime()){
            onlineReportCheckResponseVO.setNotReportReason("事故日期不在保单起止时间范围内");
            return onlineReportCheckResponseVO;
        }
        CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(policyNo,DateUtils.dateFormat(accidentDate,DateUtils.FULL_DATE_STR));
        queryVO.setCertNo(certificateNo);
        queryVO.setClientName(clientName);
        try {
            AhcsPolicyDomainDTO policyDomainInfo = customerInfoStoreSAO.getPolicyDomainInfo(queryVO);
            AhcsPolicyInfoEntity policy = policyDomainInfo.getAhcsPolicyInfo();
            if(PolicyStatusEnum.TWO.getType().equals(policy.getPolicyStatus())){
                //注销
                onlineReportCheckResponseVO.setNotReportReason("保单已注销！");
                return onlineReportCheckResponseVO;
            }
            if (policy.getInsuranceBeginTime().equals(policy.getInsuranceEndTime())){
                // 未生效退保
                onlineReportCheckResponseVO.setNotReportReason("保单未生效退保！");
                return onlineReportCheckResponseVO;
            }

            if(PolicyStatusEnum.STOP.getType().equals(policy.getPolicyStatus())){
                Date stopDate = ocasMapper.getPolicyStopDate(policy.getPolicyNo());
                if(stopDate != null && stopDate.before(accidentDate)){
                    //中止：批改生效日期
                    onlineReportCheckResponseVO.setNotReportReason("保单中止！");
                    return onlineReportCheckResponseVO;
                }

            }

            if(ListUtils.isNotEmpty(policyDomainInfo.getPlyApplyFreezes())){
                //冻结起止期
                for (PlyApplyFreeze freeze : policyDomainInfo.getPlyApplyFreezes()) {
                    if(freeze.getFreezeStarttime()==null || freeze.getFreezeEndtime()==null){
                        continue;
                    }
                    LocalDateTime accidentLocalDate = DateUtils.date2LocalDateTime(accidentDate);
                    if(freeze.getFreezeStarttime().isBefore(accidentLocalDate) && freeze.getFreezeEndtime().isAfter(accidentLocalDate)){
                        //在冻结期内
                        onlineReportCheckResponseVO.setNotReportReason("保单在冻结期内！");
                        return onlineReportCheckResponseVO;
                    }
                }
            }
            // 删除批改在途
            // if (ConfigConstValues.YES.equals(policyDomainInfo.getIsProcess()) && !ConstValues.SCENE20022.equals(policyDomainInfo.getScenceList())) {
            //     onlineReportCheckResponseVO.setNotReportReason("保单批改在途！");
            //     return onlineReportCheckResponseVO;
            // }

            List<AhcsInsuredPresonDTO> insuredList = policyDomainInfo.getAhcsInsuredPresonDTOs();
            if(RapeCheckUtil.isEmpty(insuredList) || insuredList.get(CommonConstant.ZERO) == null){
                onlineReportCheckResponseVO.setNotReportReason("保单被保险人信息抄单异常！");
                return onlineReportCheckResponseVO;
            }
        } catch (Exception e) {
            onlineReportCheckResponseVO.setNotReportReason("调用批改抄单异常");
            return onlineReportCheckResponseVO;
        }
        onlineReportCheckResponseVO.setIsCanReport("Y");
        return onlineReportCheckResponseVO;
    }

    @Override
    @Transactional
    public OnlineReportResponseVO saveReport(OnlineReportVO onlineReportVO) {

        String reportNo ;
        String msg ;
        long a = System.currentTimeMillis();
        //校验
        this.checkReportInfo(onlineReportVO);
        long b = System.currentTimeMillis();

        List<String> expiredPolicyNos = new ArrayList<>();
        //补充报案领域模型
        ReportDomainDTO reportDomain = this.initReportDomain(onlineReportVO,expiredPolicyNos);
        long c = System.currentTimeMillis();

        //保存案件信息
        telReportTransationService.insertReportDomain(reportDomain);
        long d = System.currentTimeMillis();

        //生成报案跟踪
        reportNo = reportDomain.getReportNo();
        //操作记录
        String name = ReportSubModeEnum.getName(reportDomain.getReportInfo().getReportSubMode());
        if(StringUtils.isEmptyStr(name)){
            name = reportDomain.getReportAcceptUm();
        }
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REPORT, "线上报案", null, name);
        bpmService.startProcessTelReport(reportNo, reportDomain.getCaseTimes(),
                BpmConstants.OC_REPORT_TRACK, reportDomain.getAcceptDepartmentCode());

        //对于需要下发谐筑的案件，报案时就下发给谐筑
        if ("42300010".equals(onlineReportVO.getCompanyId()) && ("1".equals(onlineReportVO.getClaimDealWay()) || "2".equals(onlineReportVO.getClaimDealWay()))) {
            TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
            //更新条件
            taskInfoDTO.setCaseTimes(reportDomain.getCaseTimes());
            taskInfoDTO.setReportNo(reportNo);
            taskInfoDTO.setStatus(BpmConstants.TASK_STATUS_PENDING);
            taskInfoDTO.setTaskDefinitionBpmKey(BpmConstants.OC_REPORT_TRACK);
            //更新内容
            taskInfoDTO.setAssigner(onlineReportVO.getTpaCom());
            taskInfoDTO.setAssigneeName(onlineReportVO.getTpaComName());
            taskInfoDTO.setUpdatedDate(new Date());
            taskInfoMapper.updateTaskAssigner(taskInfoDTO);
        }

        boolean isInited = estimateService.checkExists(reportNo, reportDomain.getCaseTimes(), EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        if (!isInited) {
            EstimatePolicyFormDTO epf = new EstimatePolicyFormDTO();
            epf.setReportNo(reportNo);
            epf.setCaseTimes(reportDomain.getCaseTimes());
            epf.setEstimateType(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
            estimateService.initEstimateData(epf);
        }
        long e = System.currentTimeMillis();
        LogUtil.info("TPA_REPORT_TIME:检验入参耗时={}，构建报案领域模型耗时={}，保存案件信息耗时={}，生成报案跟踪耗时={}", b-a, c-b, d-c, e-d);
        // 构建领款人信息并保存
        savePayMent(onlineReportVO,reportNo);
        //global保单回流报案接口
        List<String> policyNos = onlineReportVO.getPolicyNos();
        if(policyNos!=null && policyNos.size()>0){
            for(String policyNo:policyNos){
                if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                    //global保单
                    globalPolicyService.returnReportToGlobal(reportNo);
                }
            }
        }

        // 构建医院、伤害原因、疾病 信息并保存
        saveHospitalAndDisease(onlineReportVO,reportNo,reportDomain.getChannelProcess().getIdAhcsChannelProcess());
        // 报案成功返回报案号
        msg = null;
        if(!expiredPolicyNos.isEmpty()){
            msg = "被剔除的保单:"+expiredPolicyNos;
        }
        //查询客户号
        InsuredPersonDTO insuredPersonDTO = insuredPersonMapper.getInsuredPersonDTO(reportNo);
        String clientNo = Objects.nonNull(insuredPersonDTO)?insuredPersonDTO.getClientNo() : null;

        //400客服报案（新核心非互联网业务）短信通知
        if (StringUtils.isNotEmpty(onlineReportVO.getReportMode()) && ReportConstant.CUSTOMER_SERVICE_SYSTEM.equals(onlineReportVO.getReportMode())) {
            try {
                TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, reportDomain.getCaseTimes(),BpmConstants.OC_REPORT_TRACK);
                if (taskInfoDTO != null){
                    if (StringUtils.isNotEmpty(taskInfoDTO.getAssigner())) {
                        smsInfoService.sendBusinessSmsOnCreateReport(SmsTypeEnum.CREATE_REPORT,reportNo,reportDomain.getCaseTimes(),taskInfoDTO.getAssigner());
                    } else {
                        LogUtil.info("400客服报案案件：{}，未查询到报案跟踪处理人信息！",reportNo);
                    }
                } else {
                    LogUtil.info("400客服报案案件：{}，未查询到报案跟踪任务信息！",reportNo);
                }
            } catch (Exception exception){
                LogUtil.error("400客服报案案件："+reportNo+"，发送短信失败！",exception.getMessage());
            }
        }
        //小程序雇主报案发送短信给业务人员
        if (StringUtils.isNotEmpty(onlineReportVO.getReportMode()) && ReportConstant.REPORTMODE_SMALL_PROGRAM.equals(onlineReportVO.getReportMode())
                && StringUtils.isNotEmpty(onlineReportVO.getRiskFlag()) && "GZ".equals(onlineReportVO.getRiskFlag())) {
            /*try {
                TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, reportDomain.getCaseTimes(),BpmConstants.OC_REPORT_TRACK);
                if (taskInfoDTO != null){
                    if (StringUtils.isNotEmpty(taskInfoDTO.getAssigner())) {
                        smsInfoService.sendBusinessSmsOnCreateReport(SmsTypeEnum.CREATE_REPORT,reportNo,reportDomain.getCaseTimes(),taskInfoDTO.getAssigner());
                    } else {
                        LogUtil.info("报案案件：{}，未查询到报案跟踪处理人信息！",reportNo);
                    }
                } else {
                    LogUtil.info("报案案件：{}，未查询到报案跟踪任务信息！",reportNo);
                }
            } catch (Exception exception){
                LogUtil.error("报案案件："+reportNo+"，发送短信失败！",exception.getMessage());
            }*/
        }
        long f = System.currentTimeMillis();
        LogUtil.info("TPA_REPORT_TIME:报案总耗时={}, reportNo:{}", f - a, reportNo);
        return new OnlineReportResponseVO(reportNo,msg,clientNo);
    }

    /**
     *
     * @Description 插入医院等信息
     * <AUTHOR>
     * @Date 2023/11/7 16:16
     **/
    private void saveHospitalAndDisease(OnlineReportVO onlineReportVO, String reportNo, String idAhcsChannelProcess) {
        Integer caseTimes = Integer.valueOf(ReportConstant.INIT_CASE_TIMES);
        // 治疗中 治疗结束 的保存医院信息
        if (TreatCondition.TREATMENING.getType().equals(onlineReportVO.getMedicalStatus())||
                TreatCondition.TREATMEN_ED.getType().equals(onlineReportVO.getMedicalStatus())){
            String medicalType = onlineReportVO.getMedicalType();
            List<PersonHospitalDTO> hospitalList = new ArrayList<>();
            PersonHospitalDTO hospital = new PersonHospitalDTO();
            hospital.setCreatedBy(ConstValues.SYSTEM);
            hospital.setUpdatedBy(ConstValues.SYSTEM);
            hospital.setIdAhcsPersonHospital(UuidUtil.getUUID());
            hospital.setReportNo(reportNo);
            hospital.setCaseTimes(caseTimes);
            hospital.setIdAhcsChannelProcess(idAhcsChannelProcess);
            hospital.setTaskId(TacheConstants.REPORT + ReportConstant.NORMAL);
            hospital.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
            // 就诊类型：01-门急诊，02-住院医疗
            if ("01".equals(medicalType)){
                hospital.setTherapyType(ChecklossConst.AHCS_THERAPY_ONE);
            }else {
                hospital.setTherapyType(ChecklossConst.AHCS_THERAPY_TWO);
            }
            hospital.setMedicalStatus(onlineReportVO.getMedicalStatus());
            hospital.setHospitalCode(onlineReportVO.getHospitalCode());
            hospital.setHospitalName(onlineReportVO.getHospitalName());
            List<HospitalInfoVO> hospitalInfoVOS = hospitalInfoDao.getHospitalList(onlineReportVO.getHospitalName(), NcbsConstant.ORG_TYPE_ONE);
            if (CollectionUtils.isNotEmpty(hospitalInfoVOS) && hospitalInfoVOS.size()==1){
                HospitalInfoVO hospitalInfoVO = hospitalInfoVOS.get(0);
                hospital.setHospitalGrade(hospitalInfoVO.getGrade());
                hospital.setHospitalPropertyDes(hospitalInfoVO.getHospitalPropertyDes());
                hospital.setIsAppointedHospital(hospitalInfoVO.getIsAppointedHospital());
                hospital.setIsSocialInsurance(hospitalInfoVO.getIsSocialInsurance());
                hospital.setIsCooperation(hospitalInfoVO.getIsCooperate());
            }
            hospitalList.add(hospital);
            personHospitalService.addPersonHospitalList(hospitalList);
        }
        if (StringUtils.isNotEmpty(onlineReportVO.getDiseaseCode())){
            PersonDiagnoseDTO personDiagnoseDTO = new PersonDiagnoseDTO();
            personDiagnoseDTO.setDiagnosticTypologyCode(DiagnosticTypologyEnum.DiagnosticTypology01.getType());
            personDiagnoseDTO.setDiagnosticTypologyName(DiagnosticTypologyEnum.DiagnosticTypology01.getName());
            personDiagnoseDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
            personDiagnoseDTO.setTaskId(TacheConstants.REPORT + ReportConstant.NORMAL);
            personDiagnoseDTO.setReportNo(reportNo);
            personDiagnoseDTO.setCaseTimes(1);
            personDiagnoseDTO.setIdAhcsChannelProcess(idAhcsChannelProcess);
            personDiagnoseDTO.setDiagnoseCode(onlineReportVO.getDiseaseCode());
            personDiagnoseDTO.setDiagnoseName(onlineReportVO.getDiseaseName());
            personDiagnoseDTO.setIsSurgical("N");
            personDiagnoseDTO.setArchiveTime(new Date());
            personDiagnoseDTO.setCreatedBy(ConstValues.SYSTEM);
            personDiagnoseDTO.setUpdatedBy(ConstValues.SYSTEM);
            personDiagnoseDTO.setCreatedDate(new Date());
            personDiagnoseDTO.setUpdatedDate(new Date());
            personDiagnoseMapper.savePersonDiagnose(personDiagnoseDTO);
        }
        if(StringUtils.isNotEmpty(onlineReportVO.getAccidentDetail())) {
            SurveyDTO surveyDTO = new SurveyDTO();
            surveyDTO.setReportNo(reportNo) ;
            surveyDTO.setCaseTimes(caseTimes);
            surveyDTO.setTaskId(TacheConstants.REPORT + ReportConstant.NORMAL);
            surveyDTO.setStatus(ChecklossConst.STATUS_TMP_SAVE);
            surveyDTO.setSurveyUm(ConstValues.SYSTEM);
            surveyDTO.setSurveyDate( new Date());
            surveyDTO.setPhoneSurveyDetail(onlineReportVO.getAccidentDetail());
            dutySurveyService.saveSurvey(reportNo, caseTimes, ConstValues.SYSTEM, surveyDTO);
        }
    }

    private void savePayMent(OnlineReportVO onlineReportVO,String reportNo) {
        List<PaymentInfoVO> paymentList = onlineReportVO.getPaymentList();
        if (CollectionUtils.isNotEmpty(paymentList)){
            paymentList.forEach(p->{
                PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
                BeanUtils.copyProperties(p,paymentInfoDTO);
                paymentInfoDTO.setProvinceName(p.getProvinceCode());
                paymentInfoDTO.setReportNo(reportNo);
                // 数据库字段问题
                paymentInfoDTO.setProvinceName(p.getProvinceCode());
                String cityCode = p.getCityCode();
                if (BaseConstant.OTHER_CITY_CODE.equals(cityCode)){
                    paymentInfoDTO.setRegionCode(BaseConstant.OTHER_COUNTRY_CODE);
                } else {
                    List<CityDefineDTO> countys = cityDefineService.getCityDefineDTOList(cityCode, "city");
                    paymentInfoDTO.setRegionCode(Optional.ofNullable(countys).orElse(new ArrayList<>()).get(0).getCityCode());
                }
                if (StringUtils.isEmptyStr(p.getCollectPayApproach())){
                    paymentInfoDTO.setCollectPayApproach("02");
                }
                String bankDetail = p.getBankDetail();
                String bankDetailCode = checkBankIfExist(bankDetail);
                if (StringUtils.isEmptyStr(bankDetailCode)){
                    paymentInfoDTO.setBankDetail(null);
                } else {
                    paymentInfoDTO.setBankDetailCode(bankDetailCode);
                }
                paymentInfoDTO.setCaseTimes(Integer.valueOf(ReportConstant.INIT_CASE_TIMES));
                if(StringUtils.isNotEmpty(onlineReportVO.getCompanyId())){
                    paymentInfoDTO.setCreatedBy("system");
                    paymentInfoDTO.setUpdatedBy("system");
                }
                paymentInfoDTO.setOpenId(p.getOpenId());
                paymentInfoDTO.setPayType(null == p.getPayType() ? "2":p.getPayType());
                paymentInfoDTO.setClientRelation(p.getClientRelation());
                paymentInfoService.addPaymentInfo(paymentInfoDTO);
            });
        }
    }

    private String checkBankIfExist(String bankDetail) {
        return commonParameterMapper.getBankIfExistByBranchBankName(bankDetail);
    }

    private void checkReportInfo(OnlineReportVO onlineReportVO) {
        //RapeCheckUtil.checkParamEmpty(onlineReportVO.getInsuredApplyStatus(), "出险者现状");
        //RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentType(), "事故类型");
        //RapeCheckUtil.checkParamEmpty(onlineReportVO.getInsuredApplyType(), "出险类型");
        //出险事故经过描述非空校验
//        RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentDetail(), "出险过程描述");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentCauseLevel1(), "出险原因大类");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentCauseLevel2(), "出险原因细类");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getIsHugeAccident(), "是否重大灾难");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getIsSpecialReport(), "是否特殊报案");
//        String insuredApplyName = InsuredApplyTypeEnum.getName(onlineReportVO.getInsuredApplyType());
//        RapeCheckUtil.checkParamEmpty(insuredApplyName, "出险类型映射失败");
        if(Objects.equals(onlineReportVO.getAccidentCauseLevel1(), AccidentReasonTypeEnum.ACCIDENT_004.getCode()) && Objects.equals(onlineReportVO.getAccidentCauseLevel2(),AccidentReasonDetailTypeEnum.ACCIDENT_DETAIL_TYPE_2.getCode())){
            RapeCheckUtil.checkParamEmpty(onlineReportVO.getInjuryReasonCode(), "损伤外部原因代码");
        }
//        if (insuredApplyName.contains("意外")) {
//            RapeCheckUtil.checkParamEmpty(onlineReportVO.getInjuryReasonCode(), "损伤外部原因代码");
//        }
        //RapeCheckUtil.checkParamEmpty(onlineReportVO.getCertificateType(), "被保人证件类型"); 虚拟被保人无证件号需注释
        Date accidentDate = onlineReportVO.getAccidentDate();
        RapeCheckUtil.checkParamEmpty(accidentDate, "事故日期");
        Date currDate = new Date();
        if (accidentDate.getTime() > currDate.getTime()){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(), GlobalResultStatus.ERROR.format("出险时间不能在当前时间之后"));
        }
        //RapeCheckUtil.checkParamEmpty(onlineReportVO.getCertificateNo(), "被保人证件号"); 虚拟被保人无证件号需注释
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentPlace(), "事故地点空");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getClientName(), "被保人姓名");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getMedicalStatus(), "医疗状态");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getWhetherOutSideAccident(), "是否境外出险");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getSubProfessionCode(), "职业小类类型");

        if ("0".equals(onlineReportVO.getWhetherOutSideAccident())) {
            RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentProvince(), "省份");
            if (StringUtils.isEmptyStr(onlineReportVO.getAccidentCity())){
                onlineReportVO.setAccidentCity(BaseConstant.OTHER_CITY_CODE);
                onlineReportVO.setAccidentCounty(BaseConstant.OTHER_COUNTRY_CODE);
            }
            if (StringUtils.isEmptyStr(onlineReportVO.getAccidentCounty())){
                onlineReportVO.setAccidentCounty(BaseConstant.OTHER_COUNTRY_CODE);
            }
//            RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentCity(), "城市");
//            RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentCounty(), "区县");
        } else {
            RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentArea(), "大洲编码");
            RapeCheckUtil.checkParamEmpty(onlineReportVO.getAccidentNation(), "国家编码");
        }
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getCaseClass(), "案件类别");
//        RapeCheckUtil.checkParamEmpty(onlineReportVO.getResidenceProvince(), "居住省编码");
//        RapeCheckUtil.checkParamEmpty(onlineReportVO.getResidenceAddress(), "居住详细地址");
        RapeCheckUtil.checkParamEmpty(onlineReportVO.getIsSuffice(), "单证");
        if (StringUtils.isEmptyStr(onlineReportVO.getResidenceCity())){
            onlineReportVO.setResidenceCity(BaseConstant.OTHER_CITY_CODE);
            onlineReportVO.setResidenceDistrict(BaseConstant.OTHER_COUNTRY_CODE);
        }
        if (StringUtils.isEmptyStr(onlineReportVO.getResidenceDistrict())){
            onlineReportVO.setResidenceDistrict(BaseConstant.OTHER_COUNTRY_CODE);
        }
//        RapeCheckUtil.checkParamEmpty(onlineReportVO.getResidenceCity(), "居住市编码");
//        RapeCheckUtil.checkParamEmpty(onlineReportVO.getResidenceDistrict(), "居住区编码");
        RapeCheckUtil.checkListEmpty(onlineReportVO.getPolicyNos(), "保单号");
        List<LinkManEntity> linkManList = onlineReportVO.getLinkManList();

        RapeCheckUtil.checkListEmpty(linkManList, "联系人");

        for (LinkManEntity linkMan : linkManList) {
            RapeCheckUtil.checkParamEmpty(linkMan.getLinkManName(), "联系人姓名");
            RapeCheckUtil.checkParamEmpty(linkMan.getApplicantType(), "报案人类型");
            RapeCheckUtil.checkParamEmpty(linkMan.getApplicantPerson(), "报案人");
            if (RapeCheckUtil.isLengthExceed(linkMan.getLinkManName(), 40)) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(), GlobalResultStatus.ERROR.format("联系人长度不能超过40"));
            }
            if (RapeCheckUtil.isLengthExceed(linkMan.getApplicantPerson(), 40)) {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(), GlobalResultStatus.ERROR.format("报案人长度不能超过40"));
            }
            RapeCheckUtil.checkParamEmpty(linkMan.getLinkManRelation(), "与被保险人关系");
            RapeCheckUtil.checkParamEmpty(linkMan.getLinkManTelephone(), "联系电话");
//            RapeCheckUtil.checkParamEmpty(linkMan.getSendMessage(), "短信发送");
        }
        List<PaymentInfoVO> paymentList = onlineReportVO.getPaymentList();
        if (CollectionUtils.isNotEmpty(paymentList)) {
            for (PaymentInfoVO paymentInfoVO : paymentList) {
                if("1".equals(paymentInfoVO.getPayType())) {
                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getOpenId(),"领款信息-微信OpenId");
                } else {
//                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getProvinceCode(), "领款信息-银行省编码");
                    if (StringUtils.isEmptyStr(paymentInfoVO.getCityCode())){
                        paymentInfoVO.setCityCode(BaseConstant.OTHER_CITY_CODE);
                        paymentInfoVO.setRegionCode(BaseConstant.OTHER_COUNTRY_CODE);
                    }
                    // 一期微保没有区
//                RapeCheckUtil.checkParamEmpty(paymentInfoVO.getRegionCode(), "领款信息-银行区编码");
                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getBankAccountAttribute(), "领款信息- 帐号类型");
                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getBankDetail(), "领款信息-开户行明细");
                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getClientBankAccount(), "领款信息- 开户行帐号");
//                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getClientBankCode(), "领款信息-银行代码");
                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getClientBankName(), "领款信息-客户开户银行");
                    if ("1".equals(paymentInfoVO.getBankAccountAttribute())) {
                        RapeCheckUtil.checkParamEmpty(paymentInfoVO.getClientCertificateNo(), "领款信息-客户证件号码");
                        RapeCheckUtil.checkParamEmpty(paymentInfoVO.getClientCertificateType(), "领款信息-客户证件类型");
                    } else {
                        RapeCheckUtil.checkParamEmpty(paymentInfoVO.getOrganizeCode(), "领款信息-组织机构");
                    }
                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getClientMobile(), "领款信息- 客户联系电话");
                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getClientName(), "领款信息-领款人姓名");
                    RapeCheckUtil.checkParamEmpty(paymentInfoVO.getClientType(), "领款信息- 客户类型");
                }
                RapeCheckUtil.checkParamEmpty(paymentInfoVO.getCollectPayApproach(), "领款信息-收款方式");
                RapeCheckUtil.checkParamEmpty(paymentInfoVO.getPaymentUsage(), "领款信息- 支付用途");
            }
        }

    }

    private void checkPolicyData(OnlineReportVO onlineReportVO, List<AhcsPolicyDomainDTO> policyDomainDtoList, List<String> expiredPolicyNos) {
        // 剔除保单出险日都在范围外的保单
//        Date accidentDate = onlineReportVO.getAccidentDate();
        String date = DateUtils.dateFormat(onlineReportVO.getAccidentDate(),DateUtils.FULL_DATE_STR);
        try {
            Date accidentDate = DateUtils.parseToFormatDate(date.substring(0, date.length()-8) + "23:59:59",DateUtils.FULL_DATE_STR);
            for (AhcsPolicyDomainDTO policyDomain : policyDomainDtoList) {
                AhcsPolicyInfoEntity policy = policyDomain.getAhcsPolicyInfo();
                PolicyInfoExDTO policyInfoExDTO = policyDomain.getPolicyInfoExDTO();
                if(PolicyStatusEnum.TWO.getType().equals(policy.getPolicyStatus())){
                    //注销
                    expiredPolicyNos.add(policy.getPolicyNo());
                    continue;
                }
                if (policy.getInsuranceBeginTime().equals(policy.getInsuranceEndTime())){
                    // 未生效退保
                    expiredPolicyNos.add(policy.getPolicyNo());
                    continue;
                }
                //追溯期、报告期:保单起期-追溯期<=事故日期<=保单止期+报告期
                int extendReportDate = Objects.nonNull(policy.getExtendReportDate()) ? policy.getExtendReportDate() : 0;
                int prosecutionPeriod = Objects.nonNull(policy.getProsecutionPeriod()) ? policy.getProsecutionPeriod() : 0;
                if ((DateUtils.addDate(DateUtils.beginOfDay(policy.getInsuranceBeginTime()),-prosecutionPeriod).after(accidentDate) && DateUtils.addDate(DateUtils.beginOfDay(policyInfoExDTO.getInsuranceBeginTime()),-prosecutionPeriod).after(accidentDate))
                        || (DateUtils.addDate(DateUtils.endOfDay(policy.getInsuranceEndTime()),extendReportDate).before(accidentDate) && DateUtils.addDate(DateUtils.endOfDay(policyInfoExDTO.getInsuranceEndTime()),extendReportDate).before(accidentDate))){
                    //不在起止期内
                    expiredPolicyNos.add(policy.getPolicyNo());
                    continue;
                }

                if(PolicyStatusEnum.STOP.getType().equals(policy.getPolicyStatus())){
                    Date stopDate = ocasMapper.getPolicyStopDate(policy.getPolicyNo());
                    if(stopDate != null && stopDate.before(accidentDate)){
                        //中止：批改生效日期
                        expiredPolicyNos.add(policy.getPolicyNo());
                        continue;
                    }
                }

                if(ListUtils.isNotEmpty(policyDomain.getPlyApplyFreezes())){
                    //冻结起止期
                    for (PlyApplyFreeze freeze : policyDomain.getPlyApplyFreezes()) {
                        if(freeze.getFreezeStarttime()==null || freeze.getFreezeEndtime()==null){
                            continue;
                        }
                        LocalDateTime accidentLocalDate = DateUtils.date2LocalDateTime(accidentDate);
                        if(freeze.getFreezeStarttime().isBefore(accidentLocalDate) && freeze.getFreezeEndtime().isAfter(accidentLocalDate)){
                            //在冻结期内
                            expiredPolicyNos.add(policy.getPolicyNo());
                            break;
                        }
                    }
                }
                // 删除批改在途控制
                // if (ConfigConstValues.YES.equals(policyDomain.getIsProcess()) && !ConstValues.SCENE20022.equals(policyDomain.getScenceList())) {
                //     throw new GlobalBusinessException(GlobalResultStatus.ERROR.format(policy.getPolicyNo()+"批改在途！"));
                // }

                List<AhcsInsuredPresonDTO> insuredList = policyDomain.getAhcsInsuredPresonDTOs();
                if(RapeCheckUtil.isEmpty(insuredList) || insuredList.get(CommonConstant.ZERO) == null){
                    throw new GlobalBusinessException(GlobalResultStatus.ERROR.format(policy.getPolicyNo()+"无被保险人信息"));
                }
                insuredList.stream().forEach(v ->{
                    String attr = v.getAhcsInsuredPreson().getPersonnelAttribute();
                    if (!"200".equals(attr) && !"020".equals(attr) && StringUtils.isEmptyStr(v.getAhcsInsuredPreson().getCertificateNo())){
                        throw new GlobalBusinessException(GlobalResultStatus.ERROR.format(policy.getPolicyNo()+"被保险人证件信息为空"));
                    }
                });

            }
        }catch (ParseException e) {
            LogUtil.error("出险日期转化失败：{}",e.getMessage());
        }


    }

    private ReportDomainDTO initReportDomain(OnlineReportVO onlineReportVO, List<String> expiredPolicyNos) {
        onlineReportVO.setPolicyNos(onlineReportVO.getPolicyNos().stream().distinct().collect(Collectors.toList()));
        List<AhcsPolicyDomainDTO> policyDomainDtoList = new ArrayList<>();
        List<String> policyNos = onlineReportVO.getPolicyNos();

        // 保单号为空校验
        if(ListUtils.isEmptyList(policyNos)){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.format("保单号不能为空"));
        }

        for (int i = 0; i < policyNos.size(); i++) {
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(policyNos.get(i),
                    DateUtils.dateFormat(onlineReportVO.getAccidentDate(),DateUtils.FULL_DATE_STR));
            //todo tzj global 不考虑这段逻辑
            if(!globalPolicyService.checkGlobalPolicyNo(queryVO.getPolicyNo())){
                Map<String, String> plyBaseInfo  = ocasMapper.getPlyBaseInfo(policyNos.get(i));
                String productCode = plyBaseInfo.get("productCode");
                if(specialProduct.contains(productCode)){
                    OcasPolicyPlanDutyDTO policyInfoByPolicyNo = ocasMapper.getPolicyInfoByPolicyNo(policyNos.get(i));
                    Date minDate = new Date();
                    if(null != policyInfoByPolicyNo && null != policyInfoByPolicyNo.getInsuranceEndDate()){
                        minDate = policyInfoByPolicyNo.getInsuranceEndDate().before(minDate) ? policyInfoByPolicyNo.getInsuranceEndDate() : minDate;
                    }
                    queryVO.setTime(DateUtils.parseToFormatString(DateUtils.endOfDay(minDate)));
                }
            }
//            else {
//                queryVO.setTime(queryVO.getTime().substring(0, queryVO.getTime().length()-8) + "23:59:59");
//            }
            queryVO.setCertNo(onlineReportVO.getCertificateNo());
            queryVO.setClientName(onlineReportVO.getClientName());
            queryVO.setName(onlineReportVO.getClientName());
            queryVO.setCertificateNo(onlineReportVO.getCertificateNo());
            queryVO.setCertificateType(onlineReportVO.getCertificateType());
            policyDomainDtoList.add(customerInfoStoreSAO.getPolicyDomainInfo(queryVO));
        }

        checkPolicyData(onlineReportVO,policyDomainDtoList,expiredPolicyNos);

        LogUtil.info("剔除保单出险日都在范围外的保单-1-={}", JSON.toJSONString(expiredPolicyNos));
        policyDomainDtoList.removeIf(policyDTO -> expiredPolicyNos.contains(policyDTO.getAhcsPolicyInfo().getPolicyNo()));

        LogUtil.info("剔除保单出险日都在范围外的保单-2-={}", JSON.toJSONString(expiredPolicyNos));

        if (RapeCheckUtil.isEmpty(policyDomainDtoList)) {
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.format("客户出险时间无有效保单"));
        }

        /*
         * 重新赋值出险时间：如果出险时间小于每一个保单的生效时间，则将出险时间赋值为出险时间当天+ 23:59:59
         * 不太好的逻辑补偿，针对门诊险这样的t+0责任处理（为什么一开始不要求他们正常传值）
         * 当出险时间小于所有保单的生效时间时，将出险时间赋值为出险时间当天+ 23:59:59
         * 过滤保单的生效时间小于出险时间的保单 ： 重新赋值出险时间
         *
         */
        Optional<AhcsPolicyDomainDTO> conformPolicyOpt = policyDomainDtoList.stream().filter(item ->
                DateUtils.compareTimeBetweenDate(
                        DateUtils.addDate(item.getAhcsPolicyInfo().getInsuranceBeginTime(),Objects.nonNull(item.getAhcsPolicyInfo().getProsecutionPeriod()) ? -item.getAhcsPolicyInfo().getProsecutionPeriod() : 0), onlineReportVO.getAccidentDate())
                        || DateUtils.addDate(item.getAhcsPolicyInfo().getInsuranceBeginTime(),Objects.nonNull(item.getAhcsPolicyInfo().getProsecutionPeriod()) ? -item.getAhcsPolicyInfo().getProsecutionPeriod() : 0).equals(onlineReportVO.getAccidentDate())).findAny();
        if (!conformPolicyOpt.isPresent()) {
            // accidentDate 这里上面已经使用endOfDay处理了 直接赋值
//            onlineReportVO.setAccidentDate(DateUtils.endOfDay(onlineReportVO.getAccidentDate()));
            //20241212改动赋值当前时间
            onlineReportVO.setAccidentDate(new Date());
        }

        String departmentCode = policyDomainDtoList.get(CommonConstant.ZERO).getAhcsPolicyInfo().getDepartmentCode();
        ReportDomainDTO reportDomain = new ReportDomainDTO(onlineReportVO);
        //生成报案号
        reportDomain.setReportNo(commonService.generateNoOnline(NoConstants.REPORT_NO, VoucherTypeEnum.REPORT_CASE_NO,departmentCode));

        reportDomain.setAcceptDepartmentCode(departmentCode);
        reportDomain.setAhcsPolicyDomainDTOs(policyDomainDtoList);
        reportDomain.setReportAcceptUm(onlineReportVO.getReportAcceptUm());
        buildLinkMan(reportDomain);
        buildCustomer(reportDomain);
        ReportCustomerInfoEntity reportCustomerInfo = reportDomain.getReportCustomerInfo();
        String clientNo = reportCustomerInfo.getClientNo();
        if (StringUtils.isEmptyStr(clientNo)){
            ReportCustomerInfoEntity customerQuery = new ReportCustomerInfoEntity();
            customerQuery.setCertificateNo(reportCustomerInfo.getCertificateNo());
            customerQuery.setCertificateType(reportCustomerInfo.getCertificateType());
            customerQuery.setName(reportCustomerInfo.getName());
            clientNo = ocasMapper.getClientInfoByFiveInfo(customerQuery);
            reportCustomerInfo.setClientNo(clientNo);
        }
        if (StringUtils.isEmptyStr(clientNo)){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.format("被保险人客户号为空，请联系批改维护！"));
        }
        reportDomain.setClientNo(clientNo);
        buildReportInfo(reportDomain);
        buildReportInfoEx(reportDomain);
        buildReportAccident(reportDomain);
        buildReportAccidentEx(reportDomain);
        buildWholeCaseBase(reportDomain);
        buildWholeCaseBaseEx(reportDomain);
        //组装标的信息
        bulidCaseRiskProperty(reportDomain,policyDomainDtoList);
        buildCaseBase(reportDomain);
        buildPolicyDomain(reportDomain,policyDomainDtoList);
        buildAcceptRecord(reportDomain);
        buildCaseProcess(reportDomain);
        buildChannelProcess(reportDomain);
        buildCaseClass(reportDomain);
        buidPolicyClaimCase(reportDomain);
        buildPersonAccident(reportDomain);
        //处理小条款信息
        bulidPlanTermInfo(reportDomain,policyDomainDtoList);
        //查询保单历史核保信息用
        reportDomain.setQueryUwInfoPolicys(policyNos);
        return reportDomain;
    }



    public void buildLinkMan(ReportDomainDTO reportDomain){
        short i = 1;
        for (LinkManEntity linkMan : reportDomain.getOnlineReportVO().getLinkManList()) {
            linkMan.setIdAhcsLinkMan(UuidUtil.getUUID());
            linkMan.setIsReport(CommonConstant.YES);
            linkMan.setReportNo(reportDomain.getReportNo());
            linkMan.setCreatedDate(reportDomain.getReportDate());
            linkMan.setUpdatedDate(reportDomain.getReportDate());
            linkMan.setCreatedBy(reportDomain.getReportAcceptUm());
            linkMan.setUpdatedBy(reportDomain.getReportAcceptUm());
            linkMan.setLinkManNo(i++);
            linkMan.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
            if (linkMan.getLinkManRelation() == null) {
                linkMan.setLinkManRelation("98");
            }
        }
        reportDomain.setLinkMans(reportDomain.getOnlineReportVO().getLinkManList());

    }

    public void buildCustomer(ReportDomainDTO reportDomain){
        ReportCustomerInfoEntity customer = new ReportCustomerInfoEntity();
        AhcsInsuredPresonEntity insuredPreson = reportDomain.getAhcsPolicyDomainDTOs().get(CommonConstant.ZERO)
                .getAhcsInsuredPresonDTOs().get(CommonConstant.ZERO).getAhcsInsuredPreson();
        BeanUtils.copyProperties(insuredPreson,customer);
        customer.setCertificateType(reportDomain.getOnlineReportVO().getCertificateType());
        customer.setName(reportDomain.getOnlineReportVO().getClientName());
        customer.setClientCluster(insuredPreson.getPersonnelAttribute());
        if("200".equals(customer.getClientCluster()) || "020".equals(customer.getClientCluster())){
            customer.setCertificateType(StringUtils.cancelNull(customer.getCertificateType()));
            customer.setCertificateNo(StringUtils.cancelNull(customer.getCertificateNo()));
        } else {
            RapeCheckUtil.checkParamEmpty(customer.getCertificateNo(), "证件号");
        }
        customer.setCreatedBy(reportDomain.getReportAcceptUm());
        customer.setUpdatedBy(reportDomain.getReportAcceptUm());
        customer.setCreatedDate(reportDomain.getReportDate());
        customer.setUpdatedDate(reportDomain.getReportDate());
        customer.setIdAhcsReportCustomer(UuidUtil.getUUID());
        customer.setReportNo(reportDomain.getReportNo());
        reportDomain.setReportCustomerInfo(customer);
    }

    public void buildReportInfo(ReportDomainDTO reportDomain) {
        ReportInfoEntity reportInfo = new ReportInfoEntity();
        reportInfo.setReportType(ReportConstant.NORMAL);// 正常报案
        String reportMode = reportDomain.getOnlineReportVO().getReportMode();
        reportInfo.setReportMode(StringUtils.isEmptyStr(reportMode)?ReportConstant.REPORTMODE_ONLINE:reportMode);
        reportInfo.setReportSubMode(ReportConstant.CUSTOMER_SERVICE_SYSTEM.equals(reportMode)?ReportSubModeEnum.CUSTOMER_SERVICE_SYSTEM_01.getType():reportDomain.getOnlineReportVO().getReportSubMode());
        LinkManEntity linkMan = reportDomain.getOnlineReportVO().getLinkManList().get(CommonConstant.ZERO);
        reportInfo.setReporterName(linkMan.getApplicantPerson());
        //报案人为空则取联系人
        if(StringUtils.isEmptyStr(reportInfo.getReporterName())){
            reportInfo.setReporterName(linkMan.getLinkManName());
        }
        reportInfo.setReporterCallNo(linkMan.getLinkManTelephone());
        reportInfo.setReporterRegisterTel(linkMan.getLinkManTelephone());
        reportInfo.setReportNo(reportDomain.getReportNo());
        reportInfo.setIdClmReportInfo(UuidUtil.getUUID());
        reportInfo.setCreatedBy(reportDomain.getReportAcceptUm());
        reportInfo.setUpdatedBy(reportDomain.getReportAcceptUm());
        reportInfo.setCreatedDate(reportDomain.getReportDate());
        reportInfo.setUpdatedDate(reportDomain.getReportDate());
        reportInfo.setReportDate(reportDomain.getReportDate());
        reportInfo.setReportRegisterUm(reportDomain.getReportAcceptUm());
        reportInfo.setRemark(reportDomain.getOnlineReportVO().getRemark());
        reportInfo.setAcceptDepartmentCode(reportDomain.getAcceptDepartmentCode());
        reportInfo.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        if (StringUtils.isNotEmpty(reportDomain.getOnlineReportVO().getApplyAmount())){
            reportInfo.setReportedLossAmount(new BigDecimal(reportDomain.getOnlineReportVO().getApplyAmount()));
        }
        reportDomain.setReportInfo(reportInfo);
    }

    public void buildReportInfoEx(ReportDomainDTO reportDomain) {
        OnlineReportVO onlineReportVO = reportDomain.getOnlineReportVO();
        ReportInfoExEntity reportInfoEx = new ReportInfoExEntity();
        reportInfoEx.setSuccorService(CommonConstant.NO);
        reportInfoEx.setIdAhcsReportInfoEx(UuidUtil.getUUID());
        reportInfoEx.setIsSpecialReport(onlineReportVO.getIsSpecialReport());
        reportInfoEx.setReportNo(reportDomain.getReportNo());
        reportInfoEx.setCreatedBy(reportDomain.getReportAcceptUm());
        reportInfoEx.setUpdatedBy(reportDomain.getReportAcceptUm());
        reportInfoEx.setCreatedDate(reportDomain.getReportDate());
        reportInfoEx.setUpdatedDate(reportDomain.getReportDate());
        reportInfoEx.setCaseClass("6");//默认为其他
        reportInfoEx.setIdAhcsReportInfoEx(UuidUtil.getUUID());
        LinkManEntity linkMan = onlineReportVO.getLinkManList().get(0);
        reportInfoEx.setLinkManName(linkMan.getLinkManName());
        reportInfoEx.setLinkManRelation(linkMan.getLinkManRelation());
        reportInfoEx.setRelationWithReporter(linkMan.getApplicantType());
        reportInfoEx.setSendMessage(linkMan.getSendMessage());
        reportInfoEx.setResidenceAddress(onlineReportVO.getResidenceAddress());
        reportInfoEx.setResidenceCity(onlineReportVO.getResidenceCity());
        reportInfoEx.setResidenceDistrict(onlineReportVO.getResidenceDistrict());
        reportInfoEx.setResidenceProvince(onlineReportVO.getResidenceProvince());
        reportInfoEx.setWesureAutoClaim(onlineReportVO.getWesureAutoClaim());
        reportInfoEx.setRiskLevelScore(onlineReportVO.getRiskLevelScore());
        reportInfoEx.setRiskLevelDesc(onlineReportVO.getRiskLevelDesc());
        reportInfoEx.setCompanyId(onlineReportVO.getCompanyId());
        reportInfoEx.setClaimDealWay(onlineReportVO.getClaimDealWay());
        reportInfoEx.setIsQuickPay(onlineReportVO.getIsQuickPay());
        reportInfoEx.setAcceptanceNumber(onlineReportVO.getAcceptanceNumber());
        reportInfoEx.setReportExtend(onlineReportVO.getExtendInfo());
        reportDomain.setReportInfoExs(reportInfoEx);
    }

    public void buildReportAccident(ReportDomainDTO reportDomain) {
        OnlineReportVO onlineReportVO = reportDomain.getOnlineReportVO();
        ReportAccidentEntity reportAccident = new ReportAccidentEntity();
        reportAccident.setAccidentArea(onlineReportVO.getAccidentArea());
        reportAccident.setOverseaNationCode(onlineReportVO.getAccidentNation());
        reportAccident.setAccidentDate(onlineReportVO.getAccidentDate());
        reportAccident.setAccidentCityCode(onlineReportVO.getAccidentCity());
        reportAccident.setProvinceCode(onlineReportVO.getAccidentProvince());
        reportAccident.setAccidentCountyCode(onlineReportVO.getAccidentCounty());
        reportAccident.setAccidentPlace(onlineReportVO.getAccidentPlace());
        reportAccident.setOverseasOccur(onlineReportVO.getWhetherOutSideAccident());
        reportAccident.setCreatedBy(reportDomain.getReportAcceptUm());
        reportAccident.setUpdatedBy(reportDomain.getReportAcceptUm());
        reportAccident.setCreatedDate(reportDomain.getReportDate());
        reportAccident.setUpdatedDate(reportDomain.getReportDate());
        reportAccident.setIdClmReportAccident(UuidUtil.getUUID());
        reportAccident.setReportNo(reportDomain.getReportNo());
        //出险原因大类
        reportAccident.setAccidentCauseLevel1(onlineReportVO.getAccidentCauseLevel1());
        //出险原因细类
        reportAccident.setAccidentCauseLevel2(onlineReportVO.getAccidentCauseLevel2());
        // {被保人}于{事故日期}因{出险原因}申请理赔
        // 就诊原因
        String visitReason = onlineReportVO.getVisitReason();
        String accidentDateStr = DateUtils.dateFormat(onlineReportVO.getAccidentDate(), DateUtils.FULL_DATE_STR);
        String accidentDetail;
        if(StringUtils.isEmptyStr(visitReason)){
            accidentDetail = onlineReportVO.getClientName() + "于" + accidentDateStr + "因"
                    + AccidentReasonDetailTypeEnum.getName(onlineReportVO.getAccidentCauseLevel2()) + "申请理赔";
        } else {
            accidentDetail = onlineReportVO.getClientName() + "于" + accidentDateStr + "因"
                    + AccidentReasonDetailTypeEnum.getName(onlineReportVO.getAccidentCauseLevel2()) + visitReason + "申请理赔";
        }
        reportAccident.setAccidentDetail(StringUtils.isEmptyStr(onlineReportVO.getAccidentDetail()) ? accidentDetail : onlineReportVO.getAccidentDetail());
        reportAccident.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        reportDomain.setReportAccident(reportAccident);
    }

    public void buildReportAccidentEx(ReportDomainDTO reportDomain) {
        OnlineReportVO onlineReportVO = reportDomain.getOnlineReportVO();
        ReportAccidentExEntity reportAccidentEx = new ReportAccidentExEntity();
        reportAccidentEx.setAccidentType(onlineReportVO.getAccidentType());// 事故类型
        // 出险者现状
        reportAccidentEx.setInsuredApplyStatus(onlineReportVO.getInsuredApplyStatus());
        // 新增出险类型
        reportAccidentEx.setInsuredApplyType(onlineReportVO.getInsuredApplyType());
        reportAccidentEx.setCreatedBy(reportDomain.getReportAcceptUm());
        reportAccidentEx.setUpdatedBy(reportDomain.getReportAcceptUm());
        reportAccidentEx.setCreatedDate(reportDomain.getReportDate());
        reportAccidentEx.setUpdatedDate(reportDomain.getReportDate());
        reportAccidentEx.setIdAhcsReportAccidentEx(UuidUtil.getUUID());
        reportAccidentEx.setReportNo(reportDomain.getReportNo());
        reportDomain.setReportAccidentEx(reportAccidentEx);
    }

    public void buildWholeCaseBase(ReportDomainDTO reportDomain) {
        WholeCaseBaseEntity wholeCaseBase = new WholeCaseBaseEntity();
        wholeCaseBase.setIsHugeAccident(reportDomain.getOnlineReportVO().getIsHugeAccident());// 是否重灾
        wholeCaseBase.setCaseType("00");
        wholeCaseBase.setCreatedBy(reportDomain.getReportAcceptUm());
        wholeCaseBase.setUpdatedBy(reportDomain.getReportAcceptUm());
        wholeCaseBase.setCreatedDate(reportDomain.getReportDate());
        wholeCaseBase.setUpdatedDate(reportDomain.getReportDate());
        wholeCaseBase.setIdClmWholeCaseBase(UuidUtil.getUUID());
        wholeCaseBase.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        wholeCaseBase.setAllowQuickFinish(CommonConstant.NO);
        wholeCaseBase.setReportNo(reportDomain.getReportNo());
        wholeCaseBase.setReceiveVoucherUm(reportDomain.getReportAcceptUm());
        wholeCaseBase.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
        wholeCaseBase.setWholeCaseStatus(ReportConstant.REPROTED);
        wholeCaseBase.setCaseIdentification("01");
        if(ConstValues.YES.equals(reportDomain.getOnlineReportVO().getIsSuffice())){
            wholeCaseBase.setDocumentFullDate(new Date());
        }
        reportDomain.setWholeCaseBase(wholeCaseBase);
    }

    public void buildWholeCaseBaseEx(ReportDomainDTO reportDomain) {
        WholeCaseBaseExEntity wholeCaseBaseEx = new WholeCaseBaseExEntity();
        wholeCaseBaseEx.setCreatedBy(reportDomain.getReportAcceptUm());
        wholeCaseBaseEx.setUpdatedBy(reportDomain.getReportAcceptUm());
        wholeCaseBaseEx.setCreatedDate(reportDomain.getReportDate());
        wholeCaseBaseEx.setUpdatedDate(reportDomain.getReportDate());
        wholeCaseBaseEx.setIdClmWholeCaseBase(reportDomain.getWholeCaseBase().getIdClmWholeCaseBase());
        wholeCaseBaseEx.setIdClmWholeCaseBaseEx(UuidUtil.getUUID());
        wholeCaseBaseEx.setReportNo(reportDomain.getReportNo());
        wholeCaseBaseEx.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
        reportDomain.setWholeCaseBaseEx(wholeCaseBaseEx);
    }

    public void buildCaseBase(ReportDomainDTO reportDomain) {
        List<CaseBaseEntity> caseBaseList =  new ArrayList<>();
        for (AhcsPolicyDomainDTO policyDomainDto : reportDomain.getAhcsPolicyDomainDTOs()) {
            String departmentCode = policyDomainDto.getAhcsPolicyInfo().getDepartmentCode();
            CaseBaseEntity caseBase = new CaseBaseEntity();
            caseBase.setCreatedDate(new Date());
            caseBase.setUpdatedDate(new Date());
            caseBase.setReportNo(reportDomain.getReportNo());
            String caseNo = commonService.generateNoOnline( NoConstants.CASE_NO, VoucherTypeEnum.CLAIM_NO,departmentCode);
            String registNo = commonService.generateNoOnline( NoConstants.REGIST_NO, VoucherTypeEnum.CASE_NO, departmentCode);
            caseBase.setRegistNo(registNo);
            caseBase.setCaseNo(caseNo);
            policyDomainDto.getAhcsPolicyInfo().setCaseNo(caseNo);
            caseBase.setIdClmCaseBase(UuidUtil.getUUID());
            caseBase.setPolicyNo(policyDomainDto.getAhcsPolicyInfo().getPolicyNo());
            caseBase.setDepartmentCode(departmentCode);
            caseBase.setCreatedBy(reportDomain.getReportAcceptUm());
            caseBase.setUpdatedBy(reportDomain.getReportAcceptUm());
            caseBase.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
            caseBase.setCaseStatus(ReportConstant.INIT_CASE_STATUS);
            caseBase.setMigrateFrom(CommonConstant.MIGRATE_FROM);
            caseBaseList.add(caseBase);
            AhcsPolicyDomainDTO ahcsPolicyDomainDTO = reportDomain.getAhcsPolicyDomainDTOs().get(0);
            if(Objects.nonNull(ahcsPolicyDomainDTO)){
                if(Objects.nonNull(ahcsPolicyDomainDTO.getAhcsPolicyInfo()) && riskPropertyService.displayRiskProperty(null, ahcsPolicyDomainDTO.getAhcsPolicyInfo().getPolicyNo())
                        || ("Global".equals(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getDataSource()) && "04".equals(ahcsPolicyDomainDTO.getAhcsPolicyInfo().getProductClass()))) {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(reportDomain.getRiskGroupNo())) {
                        caseBase.setRiskGroupNo(reportDomain.getRiskGroupNo());
                        caseBase.setRiskGroupName(reportDomain.getRiskGroupName());
                    } else {
                        List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs = ahcsPolicyDomainDTO.getAhcsPolicyPlanDTOs();
                        if (CollectionUtils.isNotEmpty(ahcsPolicyPlanDTOs)) {
                            AhcsPolicyPlanEntity ahcsPolicyPlan = ahcsPolicyPlanDTOs.get(0).getAhcsPolicyPlan();
                            if (Objects.nonNull(ahcsPolicyPlan)) {
                                caseBase.setRiskGroupNo(ahcsPolicyPlan.getRiskGroupNo());
                                caseBase.setRiskGroupName(ahcsPolicyPlan.getRiskGroupName());
                            }
                        }
                    }
                }
            }
        }

        reportDomain.setCaseBases(caseBaseList);
    }

    public void buildPolicyDomain(ReportDomainDTO reportDomain, List<AhcsPolicyDomainDTO> policyDomainDtoList){
        String userId = reportDomain.getReportAcceptUm();
        Date now = reportDomain.getReportDate();
        for (AhcsPolicyDomainDTO policyDomainDto : policyDomainDtoList) {
            String policyId = UuidUtil.getUUID();
            AhcsPolicyInfoEntity policyInfo = policyDomainDto.getAhcsPolicyInfo();

            policyInfo.setCreatedBy(userId);
            policyInfo.setUpdatedBy(userId);
            policyInfo.setCreatedDate(now);
            policyInfo.setUpdatedDate(now);
            policyInfo.setPolicyValid(BaseConstant.UPPER_CASE_Y);
            policyInfo.setIdAhcsPolicyInfo(policyId);
            policyInfo.setReportNo(reportDomain.getReportNo());
            policyInfo.setPolicyExtend(buildPolicyExtends(policyDomainDto.getPolicyInfoExDTO()));
            policyInfo.setIsTransferInsure(policyDomainDto.getIsTransferInsure());
            reportDomain.getPolicyInfoList().add(policyInfo);

            for (AhcsInsuredPresonDTO insuredPresonDto : policyDomainDto.getAhcsInsuredPresonDTOs()) {
                AhcsInsuredPresonEntity insuredPreson = insuredPresonDto.getAhcsInsuredPreson();
                insuredPreson.setCreatedBy(userId);
                insuredPreson.setUpdatedBy(userId);
                insuredPreson.setCreatedDate(now);
                insuredPreson.setUpdatedDate(now);
                insuredPreson.setIdAhcsInsuredPerson(UuidUtil.getUUID());
                insuredPreson.setIdAhcsPolicyInfo(policyId);
                insuredPreson.setPlyCertificateType(insuredPreson.getCertificateType());
                insuredPreson.setCertificateType(insuredPreson.getCertificateType());
                reportDomain.getInsuredPresonList().add(insuredPreson);
                AhcsInsuredPersonExtEntity insuredPersonExt = new AhcsInsuredPersonExtEntity();
                BeanUtils.copyProperties(insuredPresonDto.getAhcsInsuredPersonExt(),insuredPersonExt);
                insuredPersonExt.setCreatedBy(userId);
                insuredPersonExt.setUpdatedBy(userId);
                insuredPersonExt.setCreatedDate(now);
                insuredPersonExt.setUpdatedDate(now);
                insuredPersonExt.setIdAhcsInsuredPerson(insuredPreson.getIdAhcsInsuredPerson());
                insuredPersonExt.setIdAhcsInsuredPersonExt(UuidUtil.getUUID());
                reportDomain.getInsuredPresonExList().add(insuredPersonExt);
            }

            for (AhcsPolicyHolderEntity policyHolder : policyDomainDto.getAhcsPolicyHolder()) {
                AhcsPolicyHolderEntity holder = policyHolder;
                holder.setCreatedBy(userId);
                holder.setUpdatedBy(userId);
                holder.setCreatedDate(now);
                holder.setUpdatedDate(now);
                holder.setIdAhcsPolicyHolder(UuidUtil.getUUID());
                holder.setIdAhcsPolicyInfo(policyId);
                reportDomain.getPolicyHolderList().add(holder);
            }

            for (AhcsSpecialPromiseEntity specialPromise : policyDomainDto.getAhcsSpecialPromise()) {
                AhcsSpecialPromiseEntity promiseEntity = specialPromise;
                promiseEntity.setCreatedBy(userId);
                promiseEntity.setUpdatedBy(userId);
                promiseEntity.setCreatedDate(now);
                promiseEntity.setUpdatedDate(now);
                promiseEntity.setIdAhcsSpecialPromise(UuidUtil.getUUID());
                promiseEntity.setIdAhcsPolicyInfo(policyId);
                reportDomain.getSpecialPromiseList().add(promiseEntity);
            }

            for (AhcsCoinsureEntity coinsure : policyDomainDto.getAhcsCoinsure()) {
                AhcsCoinsureEntity coinsureEntity = coinsure;
                coinsureEntity.setCreatedBy(userId);
                coinsureEntity.setUpdatedBy(userId);
                coinsureEntity.setCreatedDate(now);
                coinsureEntity.setUpdatedDate(now);
                coinsureEntity.setIdAhcsCoinsure(UuidUtil.getUUID());
                coinsureEntity.setIdAhcsPolicyInfo(policyId);
                reportDomain.getCoinsureList().add(coinsureEntity);
            }

            for (AhcsPolicyPlanDTO policyPlanDTO : policyDomainDto.getAhcsPolicyPlanDTOs()) {
                AhcsPolicyPlanEntity policyPlan = new AhcsPolicyPlanEntity();
                BeanUtils.copyProperties(policyPlanDTO.getAhcsPolicyPlan(),policyPlan);
                policyPlan.setCreatedBy(userId);
                policyPlan.setUpdatedBy(userId);
                policyPlan.setCreatedDate(now);
                policyPlan.setUpdatedDate(now);
                String policyPlanId = UuidUtil.getUUID();
                policyPlan.setIdAhcsPolicyPlan(policyPlanId);
                policyPlan.setIdAhcsPolicyInfo(policyId);
                policyPlanDTO.setAhcsPolicyPlan(policyPlan);
                for (AhcsPolicyDutyDTO policyDutyDTO : policyPlanDTO.getAhcsPolicyDutyDTOs()) {
                    AhcsPolicyDutyEntity policyDuty = new AhcsPolicyDutyEntity();
                    BeanUtils.copyProperties(policyDutyDTO.getAhcsPolicyDuty(),policyDuty);
                    policyDuty.setCreatedBy(userId);
                    policyDuty.setUpdatedBy(userId);
                    policyDuty.setCreatedDate(now);
                    policyDuty.setUpdatedDate(now);
                    policyDuty.setIdAhcsPolicyPlan(policyPlanId);
                    String policyDutyId = UuidUtil.getUUID();
                    policyDuty.setIdAhcsPolicyDuty(policyDutyId);
                    policyDutyDTO.setAhcsPolicyDuty(policyDuty);

                    List<AhcsPolicyDutyDetailEntity> dutyDetailList = new ArrayList<>();
                    for (AhcsPolicyDutyDetailEntity policyDutyDetail : policyDutyDTO.getAhcsPolicyDutyDetail()) {
                        AhcsPolicyDutyDetailEntity dutyDetailEntity = policyDutyDetail;
                        dutyDetailEntity.setCreatedBy(userId);
                        dutyDetailEntity.setUpdatedBy(userId);
                        dutyDetailEntity.setCreatedDate(now);
                        dutyDetailEntity.setUpdatedDate(now);
                        dutyDetailEntity.setIdAhcsPolicyDutyDetail(UuidUtil.getUUID());
                        dutyDetailEntity.setIdAhcsPolicyDuty(policyDutyId);
                        dutyDetailList.add(dutyDetailEntity);
                    }
                    policyDutyDTO.setAhcsPolicyDutyDetail(dutyDetailList);

                    for (AhcsDutyAttributeDTO attributeDTO : policyDutyDTO.getAhcsDutyAttributeDTOs()) {
                        AhcsDutyAttributeEntity dutyAttributeEntity = new AhcsDutyAttributeEntity();
                        BeanUtils.copyProperties(attributeDTO.getAhcsDutyAttribute(),dutyAttributeEntity);
                        String dutyAttributeId = UuidUtil.getUUID();
                        dutyAttributeEntity.setCreatedBy(userId);
                        dutyAttributeEntity.setUpdatedBy(userId);
                        dutyAttributeEntity.setCreatedDate(now);
                        dutyAttributeEntity.setUpdatedDate(now);
                        dutyAttributeEntity.setIdAhcsPolicyDuty(policyDutyId);
                        dutyAttributeEntity.setIdAhcsDutyAttribute(dutyAttributeId);
                        attributeDTO.setAhcsDutyAttribute(dutyAttributeEntity);
                        List<AhcsDutyAttributeDetailEntity> attrDetailList = new ArrayList<>();
                        for (AhcsDutyAttributeDetailEntity attributeDetailEntity : attributeDTO.getAhcsDutyAttributeDetail()) {
                            AhcsDutyAttributeDetailEntity attrDetailEntity = attributeDetailEntity;
                            attrDetailEntity.setCreatedBy(userId);
                            attrDetailEntity.setUpdatedBy(userId);
                            attrDetailEntity.setCreatedDate(now);
                            attrDetailEntity.setUpdatedDate(now);
                            attrDetailEntity.setIdAhcsDutyAttribute(dutyAttributeId);
                            attrDetailEntity.setIdAhcsDutyAttributeDetail(UuidUtil.getUUID());
                            attrDetailList.add(attrDetailEntity);
                        }
                        attributeDTO.setAhcsDutyAttributeDetail(attrDetailList);
                    }
                }
            }

            List<AhcsPolicyPlanDTO> planList =  mergePlanAndDutyInfo(policyDomainDto.getAhcsPolicyPlanDTOs());

            for (AhcsPolicyPlanDTO policyPlanDTO : planList) {
                reportDomain.getPolicyPlanList().add(policyPlanDTO.getAhcsPolicyPlan());
                for (AhcsPolicyDutyDTO policyDutyDTO : policyPlanDTO.getAhcsPolicyDutyDTOs()) {
                    reportDomain.getPolicyDutyList().add(policyDutyDTO.getAhcsPolicyDuty());
                    reportDomain.getPolicyDutyDetailList().addAll(policyDutyDTO.getAhcsPolicyDutyDetail());
                    for (AhcsDutyAttributeDTO attributeDTO : policyDutyDTO.getAhcsDutyAttributeDTOs()) {
                        reportDomain.getDutyAttributeList().add(attributeDTO.getAhcsDutyAttribute());
                        reportDomain.getDutyAttributeDetailList().addAll(attributeDTO.getAhcsDutyAttributeDetail());
                    }
                }
            }

        }
    }

    private String buildPolicyExtends(PolicyInfoExDTO policyInfoExDTO) {
        String policyExtend = "";
        try {
            if (policyInfoExDTO != null) {
                List<String> param = new ArrayList<>();
                List<String> agentNames = policyInfoExDTO.getAgentNames();
                if (agentNames != null && !agentNames.isEmpty()) {
                    StringBuilder agentNameParam = new StringBuilder(BaseConstant.AGENT_NAME + ":" + agentNames.get(0));
                    for (int i = 1; i < agentNames.size(); i++) {
                        agentNameParam.append(BaseConstant.SEPARATE_CHAR).append(agentNames.get(i));
                    }
                    param.add(agentNameParam.toString());
                }

                if (param == null || param.size() == 0) {
                    return null;
                }
                policyExtend = param.get(0);
                for (int i = 1; i < param.size(); i++) {
                    policyExtend = policyExtend + "," + param.get(i);
                }
            }
        } catch (Exception e1) {
            LogUtil.info("构建保单表扩展字段异常： policyInfoExDTO ：", JSONObject.toJSONString(policyInfoExDTO));
        }
        return policyExtend;
    }

    private List<AhcsPolicyPlanDTO> mergePlanAndDutyInfo(List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyPlanDTO> mergePolicyPlanList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyPlanDTOs)) {
            for (AhcsPolicyPlanDTO planDto : ahcsPolicyPlanDTOs) {
                String planId = planDto.getAhcsPolicyPlan().getIdAhcsPolicyPlan();
                if (!codeList.contains(planDto.getAhcsPolicyPlan().getPlanCode())) {
                    planDto.setAhcsPolicyDutyDTOs(mergeDutyInfo(planDto.getAhcsPolicyDutyDTOs(), planId));
                    mergePolicyPlanList.add(planDto);
                    codeList.add(planDto.getAhcsPolicyPlan().getPlanCode());
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyPlanList)) {
                        for (AhcsPolicyPlanDTO mergePlanDto : mergePolicyPlanList) {

                            if (mergePlanDto.getAhcsPolicyPlan().getPlanCode().equals(planDto.getAhcsPolicyPlan().getPlanCode())) {
                                mergePlanDto.getAhcsPolicyDutyDTOs().addAll(planDto.getAhcsPolicyDutyDTOs());
                                mergePlanDto.setApplyNum(mergePlanDto.getApplyNum().add(planDto.getApplyNum()));
                                mergePlanDto.setAhcsPolicyDutyDTOs(mergeDutyInfo(mergePlanDto.getAhcsPolicyDutyDTOs(), mergePlanDto.getAhcsPolicyPlan().getIdAhcsPolicyPlan()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyPlanList;
    }

    private List<AhcsPolicyDutyDTO> mergeDutyInfo(List<AhcsPolicyDutyDTO> ahcsPolicyDutyDTOs, String planId) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyDutyDTO> mergePolicyDutyList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyDutyDTOs)) {
            for (AhcsPolicyDutyDTO dutyDto : ahcsPolicyDutyDTOs) {
                dutyDto.getAhcsPolicyDuty().setIdAhcsPolicyPlan(planId);
                if (!codeList.contains(dutyDto.getAhcsPolicyDuty().getDutyCode())) {
                    mergePolicyDutyList.add(dutyDto);
                    codeList.add(dutyDto.getAhcsPolicyDuty().getDutyCode());
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyDutyList)) {
                        for (AhcsPolicyDutyDTO mergeDutyuDto : mergePolicyDutyList) {

                            if (mergeDutyuDto.getAhcsPolicyDuty().getDutyCode().equals(dutyDto.getAhcsPolicyDuty().getDutyCode())) {

                                mergeDutyuDto.getAhcsPolicyDuty().setDutyAmount(mergeDutyuDto.getAhcsPolicyDuty().getDutyAmount().add(dutyDto.getAhcsPolicyDuty().getDutyAmount()));
                                mergeDutyuDto.getAhcsPolicyDutyDetail().addAll(dutyDto.getAhcsPolicyDutyDetail());
                                mergeDutyuDto.setAhcsPolicyDutyDetail(mergeDutyDetailInfo(mergeDutyuDto.getAhcsPolicyDutyDetail(), mergeDutyuDto.getAhcsPolicyDuty().getIdAhcsPolicyDuty()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyDutyList;
    }

    private List<AhcsPolicyDutyDetailEntity> mergeDutyDetailInfo(List<AhcsPolicyDutyDetailEntity> ahcsPolicyDutyDetailDTOs, String dutyId) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyDutyDetailEntity> mergePolicyDutyDetailList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyDutyDetailDTOs)) {
            for (AhcsPolicyDutyDetailEntity ahcsPolicyDutyDetail : ahcsPolicyDutyDetailDTOs) {

                ahcsPolicyDutyDetail.setIdAhcsPolicyDuty(dutyId);
                if (!codeList.contains(ahcsPolicyDutyDetail.getDutyDetailCode())) {
                    mergePolicyDutyDetailList.add(ahcsPolicyDutyDetail);
                    codeList.add(ahcsPolicyDutyDetail.getDutyDetailCode());
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyDutyDetailList)) {
                        for (AhcsPolicyDutyDetailEntity mergeDutyDetail : mergePolicyDutyDetailList) {
                            if (mergeDutyDetail.getDutyDetailCode().equals(ahcsPolicyDutyDetail.getDutyDetailCode())) {
                                mergeDutyDetail.setDutyAmount(mergeDutyDetail.getDutyAmount().add(ahcsPolicyDutyDetail.getDutyAmount()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyDutyDetailList;
    }

    public void buildAcceptRecord(ReportDomainDTO reportDomain){
        AcceptRecordDTO acceptRecordDTO = new AcceptRecordDTO();
        acceptRecordDTO.setReportNo(reportDomain.getReportNo());
        acceptRecordDTO.setCaseTimes(reportDomain.getCaseTimes());
        acceptRecordDTO.setIsSuffice(reportDomain.getOnlineReportVO().getIsSuffice());
        acceptRecordDTO.setCreatedBy(reportDomain.getReportAcceptUm());
        acceptRecordDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
        acceptRecordDTO.setReceiveVoucherUm(reportDomain.getReportAcceptUm());
        acceptRecordDTO.setDepartmentCode(reportDomain.getAcceptDepartmentCode());
        acceptRecordDTO.setClaimantApplyDate(reportDomain.getReportDate());
        acceptRecordDTO.setDisposableToldDate(reportDomain.getReportDate());
        reportDomain.setAcceptRecordDTO(acceptRecordDTO);
    }

    public void buildCaseProcess(ReportDomainDTO reportDomain){
        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
        caseProcessDTO.setReportNo(reportDomain.getReportNo());
        caseProcessDTO.setCaseTimes(reportDomain.getCaseTimes());
        caseProcessDTO.setProcessStatus(CaseProcessStatus.PENDING_REGISTER.getCode());
        caseProcessDTO.setIsNewProcess(ConstValues.YES);
        caseProcessDTO.setArchiveTime(reportDomain.getReportDate());
        caseProcessDTO.setCompanyCode(reportDomain.getAcceptDepartmentCode());
        caseProcessDTO.setCommissionUm(reportDomain.getReportAcceptUm());
        reportDomain.setCaseProcessDTO(caseProcessDTO);
    }

    public void buildCaseClass(ReportDomainDTO reportDomain){
        OnlineReportVO onlineReportVO = reportDomain.getOnlineReportVO();
        List<String> reportTypeList = onlineReportVO.getReportTypeList();
        List<CaseClassDTO> caseClassList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reportTypeList)){
            reportTypeList.forEach(r->{
                CaseClassDTO caseClassDTO = new CaseClassDTO();
                caseClassDTO.setCreatedBy(reportDomain.getReportAcceptUm());
                caseClassDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
                caseClassDTO.setUserId(reportDomain.getReportAcceptUm());
                caseClassDTO.setTaskId(TacheConstants.REPORT + ReportConstant.NORMAL);
                caseClassDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
                caseClassDTO.setReportNo(reportDomain.getReportNo());
                caseClassDTO.setCaseTimes(reportDomain.getCaseTimes());
                caseClassDTO.setCaseSubClass(r);
                caseClassList.add(caseClassDTO);
            });
        } else {
            CaseClassDTO caseClassDTO = new CaseClassDTO();
            caseClassDTO.setCreatedBy(reportDomain.getReportAcceptUm());
            caseClassDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
            caseClassDTO.setUserId(reportDomain.getReportAcceptUm());
            caseClassDTO.setTaskId(TacheConstants.REPORT + ReportConstant.NORMAL);
            caseClassDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
            caseClassDTO.setReportNo(reportDomain.getReportNo());
            caseClassDTO.setCaseTimes(reportDomain.getCaseTimes());
            caseClassDTO.setCaseSubClass(onlineReportVO.getInsuredApplyType());
            caseClassList.add(caseClassDTO);
        }
        reportDomain.setCaseClassList(caseClassList);
    }

    private void buildChannelProcess(ReportDomainDTO reportDomain){
        String channelProcessId = UuidUtil.getUUID();
        ChannelProcessDTO channelProcess = new ChannelProcessDTO();
        channelProcess.setCreatedBy(reportDomain.getReportAcceptUm());
        channelProcess.setUpdatedBy(reportDomain.getReportAcceptUm());
        channelProcess.setIdAhcsChannelProcess(channelProcessId);
        channelProcess.setReportNo(reportDomain.getReportNo());
        channelProcess.setCaseTimes(reportDomain.getCaseTimes());
        channelProcess.setChannelType(BaseConstant.STRING_1);
        channelProcess.setAssessUm(reportDomain.getReportAcceptUm());
        channelProcess.setLossObjectNo(TimeMillisSequence.getTimeMillisSequence());
        reportDomain.setChannelProcess(channelProcess);
    }

    private void buidPolicyClaimCase(ReportDomainDTO reportDomain){
        String deptName = departmentDefineMapper.queryDepartmentNameByDeptCode(reportDomain.getAcceptDepartmentCode());
        List<EstimateDutyRecordDTO> estimateDutyRecordList = new ArrayList<>();
        List<EstimatePolicyDTO> policyList = new ArrayList<>();
        List<EstimatePlanDTO> planList = new ArrayList<>();
        List<EstimateDutyDTO> dutyList = new ArrayList<>();
        List<PolicyClaimCaseDTO> policyClaimCaseList = new ArrayList<>();
        Integer caseTimes = reportDomain.getCaseTimes();
        for (AhcsPolicyDomainDTO policyDomain : reportDomain.getAhcsPolicyDomainDTOs()) {
            AhcsPolicyInfoEntity policy = policyDomain.getAhcsPolicyInfo();
            PolicyClaimCaseDTO policyClaimCaseDTO = new PolicyClaimCaseDTO();
            policyClaimCaseDTO.setCaseNo(policy.getCaseNo());
            policyClaimCaseDTO.setDepartmentCode(policy.getDepartmentCode());
            policyClaimCaseDTO.setDepartmentName(deptName);
            policyClaimCaseDTO.setPartyNo("1");
            policyClaimCaseDTO.setPolicyNo(policy.getPolicyNo());
            policyClaimCaseDTO.setReportNo(reportDomain.getReportNo());
            policyClaimCaseDTO.setSubpolicyNo(reportDomain.getInsuredPresonList().get(CommonConstant.ZERO).getSubPolicyNo());
            policyClaimCaseDTO.setInsuredCode(reportDomain.getInsuredPresonList().get(CommonConstant.ZERO).getClientNo());
            policyClaimCaseDTO.setName(policyDomain.getAhcsPolicyHolder().get(CommonConstant.ZERO).getName());
            policyClaimCaseDTO.setCreatedBy(reportDomain.getReportAcceptUm());
            policyClaimCaseDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
            policyClaimCaseList.add(policyClaimCaseDTO);

            String policyId = UuidUtil.getUUID();
            EstimatePolicyDTO estimatePolicyDTO = new EstimatePolicyDTO();
            estimatePolicyDTO.setCreatedBy(reportDomain.getReportAcceptUm());
            estimatePolicyDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
            estimatePolicyDTO.setIdAhcsEstimatePolicy(policyId);
            estimatePolicyDTO.setPolicyNo(policy.getPolicyNo());
            estimatePolicyDTO.setCaseNo(policy.getCaseNo());
            estimatePolicyDTO.setReportNo(reportDomain.getReportNo());
            estimatePolicyDTO.setCaseTimes(caseTimes);
            estimatePolicyDTO.setInsuranceBeginTime(policy.getInsuranceBeginTime());
            estimatePolicyDTO.setInsuranceEndTime(policy.getInsuranceEndTime());
            policyList.add(estimatePolicyDTO);

            for (AhcsPolicyPlanDTO planDomain : policyDomain.getAhcsPolicyPlanDTOs()) {
                String planId = UuidUtil.getUUID();
                AhcsPolicyPlanEntity planEntity = planDomain.getAhcsPolicyPlan();
                EstimatePlanDTO estimatePlanDTO = new EstimatePlanDTO();
                estimatePlanDTO.setCreatedBy(reportDomain.getReportAcceptUm());
                estimatePlanDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
                estimatePlanDTO.setPolicyNo(estimatePolicyDTO.getPolicyNo());
                estimatePlanDTO.setCaseTimes(caseTimes);
                estimatePlanDTO.setCaseNo(estimatePolicyDTO.getCaseNo());
                estimatePlanDTO.setIdAhcsEstimatePlan(planId);
                estimatePlanDTO.setIdAhcsEstimatePolicy(policyId);
                estimatePlanDTO.setPlanCode(planEntity.getPlanCode());
                estimatePlanDTO.setPlanName(planEntity.getPlanName());
                estimatePlanDTO.setIsMain(planEntity.getIsMain());
                planList.add(estimatePlanDTO);
                for (AhcsPolicyDutyDTO dutyDomain : planDomain.getAhcsPolicyDutyDTOs()) {
                    AhcsPolicyDutyEntity dutyEntity = dutyDomain.getAhcsPolicyDuty();
                    EstimateDutyDTO estimateDutyDTO = new EstimateDutyDTO();
                    estimateDutyDTO.setIdAhcsEstimatePlan(planId);
                    estimateDutyDTO.setPlanCode(planEntity.getPlanCode());
                    estimateDutyDTO.setPolicyNo(estimatePolicyDTO.getPolicyNo());
                    estimateDutyDTO.setCaseNo(estimatePolicyDTO.getCaseNo());
                    estimateDutyDTO.setCaseTimes(caseTimes);
                    estimateDutyDTO.setIdAhcsEstimateDuty(UuidUtil.getUUID());
                    estimateDutyDTO.setCreatedBy(reportDomain.getReportAcceptUm());
                    estimateDutyDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
                    estimateDutyDTO.setDutyCode(dutyEntity.getDutyCode());
                    estimateDutyDTO.setDutyName(dutyEntity.getDutyName());
                    estimateDutyDTO.setBaseAmountPay(dutyEntity.getDutyAmount());
                    dutyList.add(estimateDutyDTO);
                    estimateDutyRecordList.add(buildEsitmateDutyRecord(estimateDutyDTO));
                }
            }
        }
        reportDomain.setPolicyClaimCaseList(policyClaimCaseList);
        reportDomain.setEstimateDutyRecordList(estimateDutyRecordList);
        reportDomain.setPolicyList(policyList);
        reportDomain.setPlanList(planList);
        reportDomain.setDutyList(dutyList);

    }

    private EstimateDutyRecordDTO buildEsitmateDutyRecord(EstimateDutyDTO duty){
        EstimateDutyRecordDTO recordDTO = new EstimateDutyRecordDTO();
        recordDTO.setCreatedBy(duty.getCreatedBy());
        recordDTO.setUpdatedBy(duty.getCreatedBy());
        recordDTO.setIdAhcsEstimateDutyRecord(duty.getIdAhcsEstimateDuty());
        recordDTO.setTaskId(TacheConstants.ESTIMATE);
        recordDTO.setPolicyNo(duty.getPolicyNo());
        recordDTO.setCaseNo(duty.getCaseNo());
        recordDTO.setCaseTimes(duty.getCaseTimes());
        recordDTO.setPlanCode(duty.getPlanCode());
        recordDTO.setDutyCode(duty.getDutyCode());
        recordDTO.setDutyName(duty.getDutyName());
        recordDTO.setBaseAmountPay(duty.getBaseAmountPay());
        recordDTO.setEstimateAmount(duty.getEstimateAmount());
        recordDTO.setEstimateType(EstimateConstValues.ESTIMATE_TYPE_ESTIMATE);
        recordDTO.setChgPayValue(duty.getEstimateAmount());
        return recordDTO;
    }

    private void buildPersonAccident(ReportDomainDTO reportDomain){
        OnlineReportVO onlineReportVO = reportDomain.getOnlineReportVO();
        PersonAccidentDTO personAccidentDTO = new PersonAccidentDTO();
        personAccidentDTO.setReportNo(reportDomain.getReportNo());
        personAccidentDTO.setCaseTimes(reportDomain.getCaseTimes());
        personAccidentDTO.setCreatedBy(reportDomain.getReportAcceptUm());
        personAccidentDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
        personAccidentDTO.setIdAhcsChannelProcess(reportDomain.getChannelProcess().getIdAhcsChannelProcess());
        personAccidentDTO.setTaskId(TacheConstants.REPORT + ReportConstant.NORMAL);
        personAccidentDTO.setOverseasOccur(onlineReportVO.getWhetherOutSideAccident());
        personAccidentDTO.setAccidentArea(onlineReportVO.getAccidentArea());
        personAccidentDTO.setAccidentNation(onlineReportVO.getAccidentNation());
        personAccidentDTO.setProvinceCode(onlineReportVO.getAccidentProvince());
        personAccidentDTO.setAccidentCityCode(onlineReportVO.getAccidentCity());
        personAccidentDTO.setAccidentCountyCode(onlineReportVO.getAccidentCounty());
        personAccidentDTO.setAccidentPlace(onlineReportVO.getAccidentPlace());
        personAccidentDTO.setInsuredApplyStatus(onlineReportVO.getInsuredApplyStatus());
        personAccidentDTO.setAccidentType(onlineReportVO.getAccidentType());
        personAccidentDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
        personAccidentDTO.setIsHugeAccident(onlineReportVO.getIsHugeAccident());
        personAccidentDTO.setAccidentTime(onlineReportVO.getAccidentDate());
        personAccidentDTO.setPersonAccidentId(UuidUtil.getUUID());
        String subProfessionCode = onlineReportVO.getSubProfessionCode();
        if (StringUtils.isNotEmpty(subProfessionCode)){
            // 反正两边翻版不同步
            if(subProfessionCode.startsWith("A")){
                personAccidentDTO.setProfessionBigdCode(subProfessionCode.substring(0,3));
                personAccidentDTO.setProfessionCode(subProfessionCode.substring(0,5));
            } else {
                personAccidentDTO.setProfessionBigdCode(subProfessionCode.substring(0,2));
                personAccidentDTO.setProfessionCode(subProfessionCode.substring(0,4));
            }
            personAccidentDTO.setSubProfessionCode(subProfessionCode);
        }
        String injuryMechanism = onlineReportVO.getInjuryMechanism();
        if (StringUtils.isNotEmpty(injuryMechanism)){
            personAccidentDTO.setInjuryMechanism(injuryMechanism);
            personAccidentDTO.setInjuryMechanismDesc(onlineReportVO.getInjuryMechanismDesc());
        }
        reportDomain.setPersonAccidentDTO(personAccidentDTO);
    }

    /**
     * 处理小条款
     * @param reportDomain
     * @param policyDomainDtoList
     */
    private void bulidPlanTermInfo(ReportDomainDTO reportDomain, List<AhcsPolicyDomainDTO> policyDomainDtoList) {
        if(CollectionUtil.isEmpty(policyDomainDtoList)){
            return;
        }

        //只有一个保单数据
        for (AhcsPolicyDomainDTO dto : policyDomainDtoList) {
            if(Objects.isNull(dto.getPolicyInfoExDTO())){
                continue;
            }
            List<PlanTermContentDTO> plandtoList= dto.getPolicyInfoExDTO().getPlanTermContentDTOList();
            if(CollectionUtil.isEmpty(plandtoList)){
                continue;
            }
            List<PlanTermContentEntity> entityList = new ArrayList<>();
            String reportNo = reportDomain.getReportNo();
            Integer caseTimes = reportDomain.getCaseTimes();
            String um = reportDomain.getReportAcceptUm();
            for (PlanTermContentDTO pdto : plandtoList) {
                PlanTermContentEntity entity = new PlanTermContentEntity();
                BeanUtils.copyProperties(pdto,entity);
                entity.setId(UuidUtil.getUUID());
                entity.setReportNo(reportNo);
                entity.setCaseTimes(caseTimes);
                entity.setCreatedBy(um);
                entity.setUpdatedBy(um);
                entityList.add(entity);
            }
            reportDomain.setPlanTermContentEntityList(entityList);
        }
    }

    private void bulidCaseRiskProperty(ReportDomainDTO reportDomain,List<AhcsPolicyDomainDTO> ahcsDomainDTOs){
        try {
            for (AhcsPolicyDomainDTO policyDomain : ahcsDomainDTOs) {
                //判断是否global标的
                AhcsPolicyInfoEntity policyInfo =  policyDomain.getAhcsPolicyInfo();
                boolean globalFlag = false;
                if(policyInfo != null && "Global".equals(policyInfo.getDataSource()) && "04".equals(policyInfo.getProductClass())){
                    globalFlag = true;
                }
                // 判断是否存储标的信息
                if (!globalFlag && !riskPropertyService.isRiskProperty(policyDomain.getTargetType(), policyDomain.getAhcsPolicyInfo().getProductClass())) {
                    continue;
                }
                // 组装标的信息
                if(ListUtils.isNotEmpty(reportDomain.getOnlineReportVO().getRiskPropertyList())) {
                    for (CaseRiskPropertyDTO caseRiskPropertyDTO : reportDomain.getOnlineReportVO().getRiskPropertyList()) {
                        caseRiskPropertyDTO.setIdCaseRiskProperty(UuidUtil.getUUID());
                        caseRiskPropertyDTO.setReportNo(reportDomain.getReportNo());
                        caseRiskPropertyDTO.setCaseTimes(1);
                        caseRiskPropertyDTO.setPolicyNo(policyDomain.getAhcsPolicyInfo().getPolicyNo());
                        caseRiskPropertyDTO.setTaskId("report1");
                        caseRiskPropertyDTO.setCreatedBy(reportDomain.getReportAcceptUm());
                        caseRiskPropertyDTO.setUpdatedBy(reportDomain.getReportAcceptUm());
                        // 雇主责任险的情况，雇员信息按字段存储，数据组报送或取数用
                        if(globalFlag ||"996".equals(caseRiskPropertyDTO.getRiskGroupType())){
                            Map<String, Object> riskPropertyMap = caseRiskPropertyDTO.getRiskPropertyMap();
                            String employeeId = riskPropertyMap.get("employeeId").toString();
                            String employeeName = riskPropertyMap.get("employeeName").toString();
                            String employeeIdType = riskPropertyMap.get("employeeIdType").toString();
                            String employeeIdNo = riskPropertyMap.get("employeeIdNo").toString();
                            String riskGroupNo = caseRiskPropertyDTO.getRiskGroupNo();
                            String riskGroupType = caseRiskPropertyDTO.getRiskGroupType();

                            List<PolicyRiskSubPropDTO> plyRiskSubPropList = null;
                            if(globalFlag){
                                PolicyRiskPropertyQueryVO riskPropQueryVO = new PolicyRiskPropertyQueryVO();
                                PolicyPropertyRequestVO requestVO = new PolicyPropertyRequestVO();
                                requestVO.setPolicyNo(policyInfo.getPolicyNo());
                                requestVO.setName(employeeName);
                                requestVO.setEmployeeIdNo(employeeIdNo);
                                requestVO.setBaseDate(DateUtils.parseToFormatStr(reportDomain.getOnlineReportVO().getAccidentDate(),DateUtils.DATE_FORMAT_YYYYMMDD));
                                requestVO.setObjectType(riskGroupType);
                                requestVO.setObjectSeq(riskGroupNo);
                                requestVO.setProductCode(policyInfo.getProductCode());
                                riskPropQueryVO.setCondition(requestVO);
                                riskPropQueryVO.setPageInfo(new Pager(1,10));

                                plyRiskSubPropList = globalPolicyService.getRiskPropertyGlobal(riskPropQueryVO);
                            } else {
                                PolicyRiskQueryVO riskPropQueryVO = new PolicyRiskQueryVO();
                                riskPropQueryVO.setPolicyNo(caseRiskPropertyDTO.getPolicyNo());
                                riskPropQueryVO.setRiskGroupType(riskGroupType);
                                riskPropQueryVO.setRiskGroupNo(riskGroupNo);
                                riskPropQueryVO.setAccidentDate(reportDomain.getOnlineReportVO().getAccidentDate());
                                Map<String, Object> paramMap = new HashMap<>();
                                paramMap.put("name", employeeName);
                                paramMap.put("certificateType", employeeIdType);
                                paramMap.put("certificateNo", employeeIdNo);
                                riskPropQueryVO.setParamMap(paramMap);
                                plyRiskSubPropList = policyService.getEsPlyRiskSubPropList(riskPropQueryVO);
                            }
                            if (CollectionUtils.isNotEmpty(plyRiskSubPropList)) {
                                Map riskPropertyMapNew = plyRiskSubPropList.get(0).getRiskPropertyMap();
                                if (null != riskPropertyMapNew) {
                                    caseRiskPropertyDTO.setRiskPropertyMap(riskPropertyMapNew);
                                    caseRiskPropertyDTO.setRiskDetail(JSON.toJSONString(riskPropertyMapNew));
                                    caseRiskPropertyDTO.setRiskGroupName((String) riskPropertyMapNew.get("riskGroupName"));
                                    caseRiskPropertyDTO.setIdPlyRiskProperty((String) riskPropertyMapNew.get("idPlyRiskProperty"));
                                    caseRiskPropertyDTO.setCertificateType((String) riskPropertyMapNew.get("certificateType"));
                                    caseRiskPropertyDTO.setCertificateNo((String) riskPropertyMapNew.get("certificateNo"));
                                    caseRiskPropertyDTO.setName((String) riskPropertyMapNew.get("name"));
                                    caseRiskPropertyDTO.setAge((Integer) riskPropertyMapNew.get("age"));
                                    caseRiskPropertyDTO.setSex((String) riskPropertyMapNew.get("sex"));
                                    caseRiskPropertyDTO.setBirthDay(DateUtils.parse2Date((String) riskPropertyMapNew.get("birthday")));
                                    reportDomain.setRiskGroupNo(caseRiskPropertyDTO.getRiskGroupNo());
                                    reportDomain.setRiskGroupName(caseRiskPropertyDTO.getRiskGroupName());
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            LogUtil.warn("组装【标的信息】异常,不影响报案流程",e);
        }
    }
}
