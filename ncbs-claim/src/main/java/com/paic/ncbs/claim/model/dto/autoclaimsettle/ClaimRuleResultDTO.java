package com.paic.ncbs.claim.model.dto.autoclaimsettle;

import com.google.common.collect.Lists;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.model.dto.rule.RuleResultDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 核责结果DTO
 */
@Data
public class ClaimRuleResultDTO {
    /**
     * 自动核责通过标志：true 所有规则完全通过进入自动理算并且理算自动提交
     * false ：触发了不通过的规则，
     */
    private String reportNo;
    private Integer caseTimes;
    private boolean autoPass;
    private String ruleType;
    private boolean autoZeroCancel;
    private String zeroCancelType;
    private String zeroCancelReason;
    /**
     * 触发规则结果集
     */
    private List<RuleSubResultDTO> ruleSubResultList;


    /**
     * 根据自动核责规则结果集，对象封装。
     * @param ruleResultDTO
     */
    public void updateRuleResult(RuleResultDTO ruleResultDTO){
        if (null == ruleResultDTO) {
            this.autoPass = false;
            RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
            ruleSubResultDTO.setRuleCode("999999");
            ruleSubResultDTO.setRuleReason("规则引擎返回结果为空");
            ruleSubResultDTO.setBillNo("");
            this.ruleSubResultList = Lists.newArrayList(ruleSubResultDTO);
        } else if(ruleResultDTO.isSuccess()) {
            // true && CollectionUtils.isEmpty(this.ruleSubResultList);  期望多个保单多次调用返回的错误明细都是空的
            this.autoPass = CollectionUtils.isEmpty(this.ruleSubResultList);
        } else if (StringUtils.equals(this.ruleType, BpmConstants.OC_MANUAL_SETTLE)
                && "988888".equals(ruleResultDTO.getCode())) {
            // 理算明细特殊处理
            this.ruleSubResultList = ruleResultDTO.convertToSubRule();
        } else {
            this.autoPass = false;

            // add by shenwen 20250909 规则引擎错误信息支持多个错误原因保存 start
            if(ruleResultDTO.getErrorMap() != null && !ruleResultDTO.getErrorMap().isEmpty()){
                List<RuleSubResultDTO> ruleSubResultList = new ArrayList<>();
                for (Map.Entry<String, String> entry : ruleResultDTO.getErrorMap().entrySet()) {
                    RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
                    ruleSubResultDTO.setBillNo("");
                    ruleSubResultDTO.setRuleCode(entry.getKey());
                    ruleSubResultDTO.setRuleReason(entry.getValue());
                    ruleSubResultList.add(ruleSubResultDTO);
                }
                this.ruleSubResultList = ruleSubResultList;
            }else{
                RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
                ruleSubResultDTO.setBillNo("");
                ruleSubResultDTO.setRuleCode(ruleResultDTO.getCode());
                ruleSubResultDTO.setRuleReason(ruleResultDTO.getMessage()); // 原逻辑-单个错误原因
                this.ruleSubResultList = Lists.newArrayList(ruleSubResultDTO);
            }
            // add by shenwen 20250909 规则引擎错误信息支持多个错误原因保存 end


        }

        // 零注返回代码判断:1、理算规则,理赔规则返回失败时
        if (StringUtils.equals(this.ruleType, BpmConstants.OC_MANUAL_SETTLE)
                && null != ruleResultDTO && !ruleResultDTO.isSuccess()) {
            String zeroCancleCode = ruleResultDTO.getZeroCancleCode();
            String zeroCancelType = transZeroCancelType(zeroCancleCode);
            String zeroCancelMsg = transZeroCancelMsg(zeroCancleCode,ruleResultDTO.getZeroCancleMsg());
            if (StringUtils.isNotBlank(zeroCancelType)) {
                this.autoZeroCancel = true;
                // 代码可能需要转换一下
                this.zeroCancelType = zeroCancelType;
                this.zeroCancelReason = zeroCancelMsg;
                RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
                ruleSubResultDTO.setRuleCode(ruleResultDTO.getZeroCancleCode());
                ruleSubResultDTO.setRuleReason("自动零注原因：" + zeroCancelMsg);
                ruleSubResultDTO.setBillNo("");
                List<RuleSubResultDTO> ruleSubResultList =
                        Optional.ofNullable(this.ruleSubResultList).orElseGet(ArrayList::new);
                ruleSubResultList.add(ruleSubResultDTO);
                this.ruleSubResultList = ruleSubResultList;
            }
        }
    }

    /**
     *
     * @param zeroCancleCode
     */
    public void addCancelExtendRule(String zeroCancleCode){
        this.autoPass = false;
        // 零注返回代码判断:1、理算规则,理赔规则返回失败时
        if (StringUtils.equals(this.ruleType, BpmConstants.OC_MANUAL_SETTLE)) {
            // 022 超索赔天数
            String zeroCancelType = transZeroCancelType(zeroCancleCode);
            String zeroCancelMsg = transZeroCancelMsg(zeroCancleCode,"");
            if (StringUtils.isNotBlank(zeroCancelType)) {
                this.autoZeroCancel = true;
                // 代码可能需要转换一下
                this.zeroCancelType = zeroCancelType;
                this.zeroCancelReason = zeroCancelMsg;
                RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
                ruleSubResultDTO.setRuleCode(zeroCancleCode);
                ruleSubResultDTO.setRuleReason("自动零注原因：" + zeroCancelMsg);
                ruleSubResultDTO.setBillNo("");
                List<RuleSubResultDTO> ruleSubResultList =
                        Optional.ofNullable(this.ruleSubResultList).orElseGet(ArrayList::new);
                ruleSubResultList.add(ruleSubResultDTO);
                this.ruleSubResultList = ruleSubResultList;
            }
        }
    }

    /**
     * 获取零注类型: 实现零注申请原因转换
     *  零注申请码表类型对应：select * FROM CLM_COMMON_PARAMETER WHERE  COLLECTION_CODE in ('AHCS_CANCEL_REASON');
     * @return
     */
    public String transZeroCancelType(String zeroCancleCode){
        if(StringUtils.isBlank(zeroCancleCode)){
            return "";
        }else if(StringUtils.equals(zeroCancleCode,"300006")){
            // 021 缺少医疗发票
            return "021";
        }else if(StringUtils.equals(zeroCancleCode,"300005")){
            // 005 无可赔付责任
            return "005";
        }else if(StringUtils.equals(zeroCancleCode,"300002")){
            // 007 不在保期
            return "007";
        }else if(StringUtils.equals(zeroCancleCode,"300004")){
            // 009  非指定医院就诊
            return "009";
        }else if(StringUtils.equals(zeroCancleCode,"300003")){
            // 010  就诊疾病不属于保险责任
            return "010";
        }else if(StringUtils.equals(zeroCancleCode,"300001")){
            // 016 发票姓名不符
            return "016";
        }else if(StringUtils.equals(zeroCancleCode,"310001")){
            // 022 超索赔天数
            return "022";
        }
        return "";
    }

    public String transZeroCancelMsg(String zeroCancleCode,String zeroCancelMsg){
        if(StringUtils.isBlank(zeroCancleCode)){
            return "";
        }else if(StringUtils.equals(zeroCancleCode,"300004")){
            return "经审核，您本次就诊" + deduplicateZeroCancelMsg(zeroCancelMsg) + "非条款约定的医院，故本案歉难给付。";
        }else if(StringUtils.equals(zeroCancleCode,"300001")){
            return "根据您提供的理赔材料，本次就诊(" + deduplicateZeroCancelMsg(zeroCancelMsg) + ")与被保人不一致，故歉难给付。";
        }else if(StringUtils.equals(zeroCancleCode,"310001")){
            return "根据您的历史索赔记录，本次申请理赔已超保单约定的索赔天数，故本案歉难给付。";
        }
        return zeroCancelMsg;
    }

    private String deduplicateZeroCancelMsg(String zeroCancelMsg){
        String tempZeroCancleMsg = StringUtils.trimToEmpty(zeroCancelMsg);
        String[] zeroCancleMsgArr = StringUtils.split(tempZeroCancleMsg, Constants.SEPARATOR);
        return Arrays.stream(zeroCancleMsgArr).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(Constants.SEPARATOR));
    }

}
