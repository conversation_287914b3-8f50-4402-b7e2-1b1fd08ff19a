package com.paic.ncbs.claim.service.investigate.impl;


import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.chase.ChaseApplyDTO;
import com.paic.ncbs.claim.dao.mapper.chase.ChaseMapper;
import com.paic.ncbs.claim.service.investigate.ChaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("chaseService")
public class ChaseServiceImpl extends BaseController implements ChaseService {


    @Autowired
    private ChaseMapper chaseDao;

    @Override
    public ChaseApplyDTO getChaseApply(String reportNo, Integer caseTimes) {
        Integer applyTimes = chaseDao.getApplyTimes(reportNo, caseTimes);
        return getChaseApply(reportNo, caseTimes, applyTimes);
    }

    @Override
    public ChaseApplyDTO getChaseApply(String reportNo, Integer caseTimes, Integer applyTimes) {
        return chaseDao.getChaseApply(reportNo, caseTimes, applyTimes);
    }

}
