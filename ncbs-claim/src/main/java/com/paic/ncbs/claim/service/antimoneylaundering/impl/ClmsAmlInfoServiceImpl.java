package com.paic.ncbs.claim.service.antimoneylaundering.impl;

import cn.hutool.core.util.StrUtil;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.enums.BankAccountTypeEnum;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.service.antimoneylaundering.AntiMoneyLaunderingService;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsAmlCompanyInfoService;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsAmlInfoService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.Objects;

@Slf4j
@Service
public class ClmsAmlInfoServiceImpl implements ClmsAmlInfoService {
    @Autowired
    private ClmsAmlCompanyInfoService clmsAmlCompanyInfoService;

    @Autowired
    private AntiMoneyLaunderingService antiMoneyLaunderingService;

    @Autowired
    private PaymentInfoMapper paymentInfoMapper;

    @Autowired
    private CommonParameterService commonService;
    /**
     *
     *
     * 1:首次保存一条为个人的反洗钱后，然后将这条支付信息账户类型改为公司在录入反洗钱信息处理逻辑
     *   第一步 ：根据clm_payment_info 表主键 判断是否是更新操作：主键有值为更新操作，主键没值为新增操作
     *   第二步：根据主键查询clm_payment_info 信息数据
     *          判断原始数据的账户类型和入参的账户类型是否一致
     *              如果 原始账户类型和入参账户类型相同 属于正常的数据更新
     *                      正常更新clm_payment_info表和反洗钱表即可
     *              如果：原始账户类型为公司，入参账户类型为个人，先更新原始的支付信息为个人，同时删除原来个的公司反洗钱信息，并保存新的个人反洗钱信息
     *              如果：原始账户类型为个人，入参账户类型为公司：先更新原始的支付信息为公司，同时删除原来的个人反洗钱信息，并保存新的公司反洗钱信息
     *
     *
     *保存/更新反洗钱信息
     */
    @Override
    @Transactional
    public void saveAmlInfo(ClmsAntiMoneyLaunderingInfoDto dto) {
        if(StrUtil.isEmpty(dto.getBankAccountAttribute())){
            throw new GlobalBusinessException("账户类型不能为空");
        }

        try {
            log.info("客户号问题此处延时一秒");
            Thread.currentThread().sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        //判断 支付信息主键是否为空：更新必传，根据这个字段判断是更新还是新增
        if(StrUtil.isNotEmpty(dto.getIdClmPaymentInfo())){
           //更新操作 查询支付信息表
            PaymentInfoDTO paydto = paymentInfoMapper.getPaymentInfoById(dto.getIdClmPaymentInfo());
            if(Objects.isNull(paydto)){
                throw new GlobalBusinessException("更新失败没有查询到相关数据");
            }
            //更新操作时 账户类型没有发生变化的情况
            if(Objects.equals(dto.getBankAccountAttribute(),paydto.getBankAccountAttribute())){
                judgmentIdvOrCompany(dto);
            }else{ //更新操作时账户类型发生了变化的情况
                //入参账户类型为公司，但原始数据账户类型为个人：即把原来的个人支付信息修改为了公司支付信息
                if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),dto.getBankAccountAttribute())&&Objects.equals(BankAccountTypeEnum.BankAccountType_ONE.getCode(),paydto.getBankAccountAttribute())){
                    //1：删除原来的个人反洗钱信息
                    antiMoneyLaunderingService.deleteClmsAmlInfoByCustomerNo(setParamsDtoValue(paydto));
                    //2:新增保存 公司反洗钱信息
                    clmsAmlCompanyInfoService.dealServiceData(dto);
                }
                //入参账户类型为个人，但原始数据账户类型为公司：即把原来的公司支付信息修改为个人支付信息
                if(Objects.equals(BankAccountTypeEnum.BankAccountType_ONE.getCode(),dto.getBankAccountAttribute())&&Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),paydto.getBankAccountAttribute())){
                    //1：删除原来的公司反洗钱信息
                    clmsAmlCompanyInfoService.delAmlCompanyInfo(setParamsDtoValue(paydto));
                    //2：新增保存 个人反洗钱信息
                    //个人反洗钱信息保存
                    antiMoneyLaunderingService.addClmsAntiMoneyLaunderingInfo(dto);
                }

            }

        }else{
            //直接新增
            judgmentIdvOrCompany(dto);
        }

    }

    /**
     * 设置查询参数
     * @param paydto
     * @return
     */
    public ClmsAntiMoneyLaunderingInfoDto setParamsDtoValue(PaymentInfoDTO paydto){
        ClmsAntiMoneyLaunderingInfoDto parmsDto = new ClmsAntiMoneyLaunderingInfoDto();
        parmsDto.setReportNo(paydto.getReportNo());
        parmsDto.setCustomerNo(paydto.getCustomerNo());
        parmsDto.setCaseTimes(paydto.getCaseTimes());
        return parmsDto;
    }
    /**
     * 判断调用公司反洗钱还是个人反洗钱信息保存服务
     */
    public void judgmentIdvOrCompany(ClmsAntiMoneyLaunderingInfoDto dto){
        if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),dto.getBankAccountAttribute())){
            //公司反洗钱信息处理
            clmsAmlCompanyInfoService.dealServiceData(dto);

        }else{
            //个人反洗钱信息保存
            antiMoneyLaunderingService.addClmsAntiMoneyLaunderingInfo(dto);
        }
    }
    /**
     * 查询反洗钱信息
     */
    @Override
    public ClmsAntiMoneyLaunderingInfoDto getClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto dto) {
        ClmsAntiMoneyLaunderingInfoDto result = new ClmsAntiMoneyLaunderingInfoDto();
        if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),dto.getBankAccountAttribute())){
            result =  clmsAmlCompanyInfoService.getAmlCompanyInfo(dto);
        }else{
            result = antiMoneyLaunderingService.getClmsAntiMoneyLaunderingInfo(dto);
        }
        return result;
    }

    /**
     * 删除反洗钱信息
     * @param dto
     */
    @Override
    public void deleteClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto dto) {
        if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),dto.getBankAccountAttribute())){
            //公司反洗钱：根据报案号，赔付次数，客户号删除
            clmsAmlCompanyInfoService.delAmlCompanyInfo(dto);
        }else{
            //个人反洗钱
            antiMoneyLaunderingService.deleteClmsAntiMoneyLaunderingInfo(dto);
        }
    }

    /**
     * 根据姓名，证件类型，证件号查询反洗钱信息
     * @param dto
     * @return
     */
    @Override
    public ClmsAntiMoneyLaunderingInfoDto getAmlByNameAndCardTypeAndCardNo(ClmsAntiMoneyLaunderingInfoDto dto) {
        ClmsAntiMoneyLaunderingInfoDto returnDto = new ClmsAntiMoneyLaunderingInfoDto();
        if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),dto.getBankAccountAttribute())){
            //查询公司反洗钱信息表
            returnDto = clmsAmlCompanyInfoService.getAmlCompanyByNameAndCardTypeAndCardNo(dto);
            returnDto.setClientName(returnDto.getCompanyName());
            //省市县中文名称反显
            AdressSearchDto adressSearchDto  = new AdressSearchDto();
            adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(returnDto.getProvinceCode())
                    .setAccidentCountyCode(returnDto.getCountyCode()).setAccidentCityCode(returnDto.getCityCode());
            AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
            log.info("公司反洗钱省市县反显数据={}"+ JsonUtils.toJsonString(detailAdressFormCode));
            returnDto.setProName(detailAdressFormCode.getAccidentProvinceName());
            returnDto.setCiName(detailAdressFormCode.getAccidentCityName());
            returnDto.setCountryName(detailAdressFormCode.getAccidentCountyName());


        }else{
            //查询个人反洗钱信息表
            returnDto = antiMoneyLaunderingService.getAmlByNameAndCardTypeAndCardNo(dto);
        }
        if(Objects.isNull(returnDto)||StrUtil.isEmpty(returnDto.getClientName())){
            //返回null给前端用，前端判断没有反洗钱信息 就不用展示反洗钱页
            return null;
        }
        return returnDto;
    }

    @Override
    public ClmsAntiMoneyLaunderingInfoDto getClmsAntiMoneyLaunderingInfoNew(ClmsAntiMoneyLaunderingInfoDto dto) {
        ClmsAntiMoneyLaunderingInfoDto amlDto = getClmsAntiMoneyLaunderingInfo(dto);
        if (Objects.isNull(amlDto) || (StrUtil.isEmpty(amlDto.getClientName()) && StrUtil.isEmpty(amlDto.getCompanyName()))) {
            return getAmlByCustomerNo(dto);
        }
        return amlDto;
    }

    @Override
    public ClmsAntiMoneyLaunderingInfoDto getAmlByCustomerNo(ClmsAntiMoneyLaunderingInfoDto dto) {
        ClmsAntiMoneyLaunderingInfoDto result;
        if (Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(), dto.getBankAccountAttribute())) {
            result = clmsAmlCompanyInfoService.getAmlCompanyByCustomerNo(dto);
        } else {
            result = antiMoneyLaunderingService.getAmlByCustomerNo(dto);
        }
        return result;
    }
}
