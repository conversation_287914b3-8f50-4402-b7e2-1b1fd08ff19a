package com.paic.ncbs.claim.service.noPeopleHurt.impl;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsLegalLiabilityClassInfoMapper;
import com.paic.ncbs.claim.dao.entity.clms.ClmsLegalLiabilityClassInfo;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsLegalLiabilityClassInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 法律责任及其他信息(ClmsLegalLiabilityClassInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:47
 */
@Service("clmsLegalLiabilityClassInfoService")
public class ClmsLegalLiabilityClassInfoServiceImpl implements ClmsLegalLiabilityClassInfoService {
    @Resource
    private ClmsLegalLiabilityClassInfoMapper clmsLegalLiabilityClassInfoMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsLegalLiabilityClassInfo queryById(String id) {
        return this.clmsLegalLiabilityClassInfoMapper.queryById(id);
    }

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public List<ClmsLegalLiabilityClassInfo> queryByReportNo(String reportNo, int caseTimes) {
        return this.clmsLegalLiabilityClassInfoMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsLegalLiabilityClassInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsLegalLiabilityClassInfo insert(ClmsLegalLiabilityClassInfo clmsLegalLiabilityClassInfo) {
        clmsLegalLiabilityClassInfo.setCreatedBy(WebServletContext.getUserId());
        clmsLegalLiabilityClassInfo.setCreatedDate(new Date());
        clmsLegalLiabilityClassInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsLegalLiabilityClassInfo.setUpdatedDate(new Date());
        clmsLegalLiabilityClassInfo.setId(UuidUtil.getUUID());
        this.clmsLegalLiabilityClassInfoMapper.insert(clmsLegalLiabilityClassInfo);
        return clmsLegalLiabilityClassInfo;
    }

    /**
     * 修改数据
     *
     * @param clmsLegalLiabilityClassInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsLegalLiabilityClassInfo update(ClmsLegalLiabilityClassInfo clmsLegalLiabilityClassInfo) {
        clmsLegalLiabilityClassInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsLegalLiabilityClassInfo.setUpdatedDate(new Date());
        this.clmsLegalLiabilityClassInfoMapper.update(clmsLegalLiabilityClassInfo);
        return this.queryById(clmsLegalLiabilityClassInfo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsLegalLiabilityClassInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByCondition(String reportNo, int caseTimes) {
        clmsLegalLiabilityClassInfoMapper.deleteByCondition(reportNo, caseTimes);
    }
}
