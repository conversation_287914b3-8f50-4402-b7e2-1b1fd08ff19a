package com.paic.ncbs.claim.model.dto.other;

import java.util.Date;

/**
 * 医院别名表DTO
 */
public class HospitalAliasInfoDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建人员
     */
    private String createdBy;

    /**
     * 修改人员
     */
    private String updatedBy;

    /**
     * 删除标记
     */
    private String deleteFlag;

    /**
     * 医院表id
     */
    private String hospitalId;

    /**
     * 医院别名
     */
    private String hospitalAlias;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 修改时间
     */
    private Date sysUtime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(String hospitalId) {
        this.hospitalId = hospitalId;
    }

    public String getHospitalAlias() {
        return hospitalAlias;
    }

    public void setHospitalAlias(String hospitalAlias) {
        this.hospitalAlias = hospitalAlias;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}