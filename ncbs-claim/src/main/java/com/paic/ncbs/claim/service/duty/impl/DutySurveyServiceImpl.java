package com.paic.ncbs.claim.service.duty.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.BankAccountTypeEnum;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.DiagnosticTypologyEnum;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.EstimateUtil;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.entity.clms.*;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.*;
import com.paic.ncbs.claim.dao.mapper.communicate.CommunicateBaseMapper;
import com.paic.ncbs.claim.dao.mapper.doc.PrintMapper;
import com.paic.ncbs.claim.dao.mapper.duty.BigDiseaseDetailMapper;
import com.paic.ncbs.claim.dao.mapper.duty.BigDiseaseMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyRejectDetailMapper;
import com.paic.ncbs.claim.dao.mapper.duty.OperationDefineMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.ClaimRejectionApprovalRecordEntityMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.other.SurveyMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.pet.ClmsPetLossMapper;
import com.paic.ncbs.claim.dao.mapper.pet.PetInjureExMapper;
import com.paic.ncbs.claim.dao.mapper.pet.PetInjureMapper;
import com.paic.ncbs.claim.dao.mapper.pet.PetTreatmentDetailMapper;
import com.paic.ncbs.claim.dao.mapper.prepay.PrePayMapper;
import com.paic.ncbs.claim.dao.mapper.restartcase.RestartCaseRecordMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyDutyDetailMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.ProfessionDefineMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TravelAlertInvoiceMapper;
import com.paic.ncbs.claim.dao.mapper.trace.ClmsTraceRecordMapper;
import com.paic.ncbs.claim.dao.mapper.verify.VerifyConclusionCauseMapper;
import com.paic.ncbs.claim.dao.mapper.verify.VerifyConclusionMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.HugeAccidentInfoDTO;
import com.paic.ncbs.claim.model.dto.checkloss.*;
import com.paic.ncbs.claim.model.dto.duty.*;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseInfoParameterDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.other.PropertyLossDetailReasonDTO;
import com.paic.ncbs.claim.model.dto.other.PropertyLossReasonDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.pet.PetInjureDTO;
import com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskDomainDTO;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalDTO;
import com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.ThirdCarDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertInvoiceDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO;
import com.paic.ncbs.claim.model.dto.user.UserDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO;
import com.paic.ncbs.claim.model.vo.checkloss.DisabilityAppraisalVO;
import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.model.vo.duty.*;
import com.paic.ncbs.claim.model.vo.other.ResultAmountVO;
import com.paic.ncbs.claim.model.vo.pet.PetInjureVO;
import com.paic.ncbs.claim.model.vo.pet.TypeAndAmountVO;
import com.paic.ncbs.claim.model.vo.report.RunRoleResultVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyInfoVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import com.paic.ncbs.claim.service.accident.HugeAccidentInfoService;
import com.paic.ncbs.claim.service.base.impl.SwitchServiceUtil;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.checkloss.*;
import com.paic.ncbs.claim.service.common.ClaimUpdateDocumentFullDateService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.customer.CustomerInfoService;
import com.paic.ncbs.claim.service.duty.ClmsCargoInfoService;
import com.paic.ncbs.claim.service.duty.ClmsItemLossService;
import com.paic.ncbs.claim.service.duty.DutyRejectDetailService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.dynamic.IDynamicFieldResultService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.noPeopleHurt.*;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.pet.PetInjureService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.report.ReportService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.*;
import com.paic.ncbs.claim.service.settle.factor.interfaces.ClaimSettleService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.taskdeal.ThirdCarService;
import com.paic.ncbs.claim.service.taskdeal.TravelAlertService;
import com.paic.ncbs.claim.service.trace.PersonTraceService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("dutySurveyService")
public class DutySurveyServiceImpl implements DutySurveyService {

    @Autowired
    private ChannelProcessService channelProcessService;
    @Autowired
    private BatchService batchService;
    @Autowired
    VerifyConclusionCauseMapper verifyConclusionCauseMapper;
    @Autowired
    private DisabilityAppraisalService disabilityAppraisalService;
    @Autowired
    private PersonHospitalService personHospitalService;
    @Autowired
    private EstimateService estimateService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private HugeAccidentInfoService accidentInfoService;
    @Autowired
    private VerifyConclusionMapper verifyConclusiondao;
    @Autowired
    private LossReduceMapper lossReduceMapper;
    @Autowired
    private CaseClassMapper caseClassDao;
    @Autowired
    private PersonAccidentMapper personAccidentDao;
    @Autowired
    private PersonDiseaseMapper personDiseaseDao;
    @Autowired
    private PersonDiagnoseMapper personDiagnoseDao;
    @Autowired
    private PersonDisabilityMapper personDisabilityDao;
    @Autowired
    private PersonDeathMapper personDeathDao;
    @Autowired
    private BigDiseaseMapper bigDiseaseDao;
    @Autowired
    private BigDiseaseDetailMapper bigDiseaseDetailDao;
    @Autowired
    private PersonObjectExService personObjectExService;
    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private EndorsementService endorsementService;
    @Autowired
    private PersonBenefitService personBenefitService;
    @Autowired
    private PersonRescueService personRescueService;
    @Autowired
    private PersonOtherLossService personOtherLossService;
    @Autowired
    PolicyInfoMapper policyInfoMapper;
    @Autowired
    private PolicyDutyDetailMapper policyDutyDetailDAO;

    @Autowired
    private MedicalBillService medicalBillService;
    @Autowired
    private VehiclDelayOtherService vehiclDelayOtherService;
    @Autowired
    private BaggageDelayService baggageDelayService;
    @Autowired
    private ExaminFailService examinFailService;
    @Autowired
    private TravelAlertService travelAlertService;
    @Autowired
    private OtherLossService otherLossService;
    @Autowired
    private PropertyLossService propertyLossService;
    @Autowired
    private ProfessionDefineMapper professionDefineDao;
    @Autowired
    CommonParameterService commonService;
    @Autowired
    private DutyRejectDetailMapper dutyRejectDetailDao;

    @Autowired
    private RegisterCaseService registerCaseService;
    @Autowired
    private ThirdCarService thirdCarService;

    @Autowired
    private OtherLossPptService otherLossPptService;

    @Autowired
    TravelAlertInvoiceMapper travelAlertInvoiceDao;

    @Autowired
    DutyRejectDetailService dutyRejectDetailService;

    @Autowired
    PolicyPayService policyPayService;

    @Autowired
    private CommonParameterMapper commonParameterMapper;

    @Autowired
    private ReportService reportService;

    @Autowired
    private SurveyMapper surveyMapper;

    @Autowired
    private ReportInfoService reportInfoService;

    @Autowired
    private ClaimRejectionApprovalRecordEntityMapper claimRejectionApprovalRecordEntityMapper;

    @Autowired
    private CommunicateBaseMapper communicateBaseMapper;

    @Autowired
    private OperationDefineMapper operationDefineMapper;
    @Autowired
    private PrePayMapper prePayMapper;
    @Autowired
    private HospitalInfoMapper hospitalInfoDao;

    @Autowired
    private HospitalInfoService hospitalInfoService;
    @Autowired
    PetInjureService petInjureService;
    @Autowired
    private FlightDelayService flightDelayService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    private ClmsAllowanceInfoService clmsAllowanceInfoService;

    @Autowired
    private ClmsCashLossInfoService clmsCashLossInfoService;

    @Autowired
    private ClmsLegalLiabilityClassInfoService clmsLegalLiabilityClassInfoService;

    @Autowired
    private ClmsPersonalInjuryDeathInfoService clmsPersonalInjuryDeathInfoService;

    @Autowired
    private ClmsPropertyLossInfoService clmsPropertyLossInfoService;

    @Autowired
    private ClmsRescueInfoService clmsRescueInfoService;

    @Autowired
    private ClmsSubstanceLossInfoService clmsSubstanceLossInfoService;
    @Autowired
    private PropLossService propLossService;
    @Autowired
    private ClmsTravelDelayInfoService clmsTravelDelayInfoService;

    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private HugeAccidentInfoMapper hugeAccidentInfoMapper;

    @Autowired
    private PaymentItemMapper paymentItemMapper;

    @Autowired
    private PaymentInfoMapper paymentInfoMapper;

    @Autowired
    private ClaimUpdateDocumentFullDateService claimUpdateDocumentFullDateService;

    @Autowired
    private ClaimSettleService claimSettleService;

    @Autowired
    private EstimateChangeService estimateChangeService;

    @Autowired
    private IDynamicFieldResultService dynamicFieldResultService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private ClmsTraceRecordMapper clmsTraceRecordMapper;

    @Autowired
    private RestartCaseRecordMapper recordMapper;

    @Autowired
    private PrintMapper printMapper;

    @Autowired
    private ReportAccidentService reportAccidentService;

    @Autowired
    private OtherLossMapper otherLossMapper;
    @Autowired
    private PersonTraceService personTraceService;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    @Autowired
    private ClmsItemLossService clmsItemLossService;

    @Autowired
    private ClmsCargoInfoService clmsCargoInfoService;

    @Autowired
    private ClmsPetLossMapper clmsPetLossMapper;

    @Autowired
    private PetInjureMapper petInjureMapper;

    @Autowired
    private PetInjureExMapper petInjureExMapper;

    @Autowired
    private PetTreatmentDetailMapper petTreatmentDetailMapper;

    private void completeDutySurvey(DutySurveyVO dutySurveyVO, String loginUm) throws GlobalBusinessException {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        String taskId = dutySurveyVO.getTaskId();
        //收单提交校验存在人伤跟踪时必须上传一条单证数据
        PersonTranceRequestVo personTranceRequestVo = new PersonTranceRequestVo();
        personTranceRequestVo.setReportNo(reportNo);
        personTranceRequestVo.setCaseTimes(caseTimes);
        personTranceRequestVo.setTaskDefinitionBpmKey(BpmConstants.OC_HUMAN_INJURY_TRACKING);
        personTranceRequestVo.setFlag("0");
        personTranceRequestVo.setBpmKey(BpmConstants.OC_CHECK_DUTY);
        personTraceService.getChekPersonTrace(personTranceRequestVo);
        LogUtil.audit("#提交至工作流完成任务-开始#:reportNo=%s,caseTimes=%s,taskId=%s", reportNo, caseTimes, taskId);
        if (BpmConstants.CHECK_DUTY.equals(taskId)) {
//            TaskEventPublisher.publishEvent(reportNo, caseTimes, BpmConstants.OC_CHECK_DUTY);
            // 赔付结论(1-赔付 2-零结 3-商业险拒赔 4-整案拒赔 5-注销)
            String conclusion = taskInfoService.getConclusion(reportNo, caseTimes);
            //收单页面选择拒赔 创建拒赔审批记录 挂起收单
            if (BaseConstant.STRING_4.equals(conclusion)) {
                /* zjtang  旧校验逻辑删除
                //校验是否有在途的未决审批
                if(estimateChangeService.checkEstimateChangePending(reportNo, caseTimes)){
                    throw new GlobalBusinessException("当前案件存在审批中的未决修正，不能发起拒赔！");
                }
                 */
                //校验当前流程是否有冲突 拒赔审批
                bpmService.processCheck(reportNo,BpmConstants.OC_REJECT_REVIEW,BpmConstants.OPERATION_SUBMIT);
                //操作记录
                operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_CHECK_DUTY, "拒赔", null);
                bpmService.newSuspendOrActiveTask_oc(reportNo,caseTimes, BpmConstants.OC_CHECK_DUTY,true);
                //操作记录
                operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REJECT_REVIEW, "发起", null, loginUm);
                bpmService.startProcess_oc(reportNo, caseTimes, BpmConstants.OC_REJECT_REVIEW);

                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.WAIT_REJECT.getCode());
                return;
            }
            //校验当前流程是否有冲突 收单
            bpmService.processCheck(reportNo,BpmConstants.OC_CHECK_DUTY,BpmConstants.OPERATION_SUBMIT);
            //操作记录
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_CHECK_DUTY, "提交", null);
            bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_CHECK_DUTY);
            bpmService.startProcess_oc(reportNo, caseTimes, BpmConstants.OC_MANUAL_SETTLE);
            caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.PENDING_SETTLE.getCode());
            //更新ES同步时间
            caseBaseService.updateEsUpdatedDate(reportNo,caseTimes);

//            if(conclusion!= null && conclusion.startsWith(BaseConstant.STRING_1)){
//                initPolicyPay(reportNo,caseTimes); 海文在这里初始化的目的是因为在理算页面初始化时嫌慢，全流程需求5.9号上线，不能在此处初始化
//            }
        }
        LogUtil.audit("#提交至工作流完成任务-结束#:reportNo=%s,caseTimes=%s,taskId=%s", reportNo, caseTimes, taskId);
    }

    @Override
    public ResultAmountVO checkDutyAmountLeTotalAmount(DutySurveyVO dutyVO) throws GlobalBusinessException {
        ResultAmountVO vo = this.getTotalAmount(dutyVO);
        if (null != vo) {
            //判断 其他人伤信息中的损失金额 与 总保额
            if (null != dutyVO.getPeopleHurtVO() && null != dutyVO.getPeopleHurtVO().getPersonOtherLossVO()) {
                List<PersonOtherLossDTO> pleInjuredlist = dutyVO.getPeopleHurtVO().getPersonOtherLossVO().getPersonOtherLossList();
                if (null != vo.getDutyAmount() && !CollectionUtils.isEmpty(pleInjuredlist)) {
                    for (PersonOtherLossDTO pd : pleInjuredlist) {
                        if (null == pd.getLossAmount()) {
                            continue;
                        }
                        if (pd.getLossAmount().compareTo(vo.getDutyAmount()) == 1) {
                            throw new GlobalBusinessException(ErrorCode.Settle.CHECK_DUTY_GT_MAX_AMOUNT);
                        }
                    }
                }
            }
            //判断 其他损失中的定损金额 与 总保额
          /*  if (null != vo.getDutyAmount() && null != dutyVO.getNoPeopleHurtVO() && null != dutyVO.getNoPeopleHurt().getExaminFailVO()
                    && null != dutyVO.getNoPeopleHurtVO().getExaminFailVO().getLossAmount() && null != dutyVO.getNoPeopleHurtVO().getOtherLossVO()
                    && null != dutyVO.getNoPeopleHurtVO().getOtherLossVO().getLossAmount()) {
                if ((dutyVO.getNoPeopleHurtVO().getExaminFailVO().getLossAmount()).compareTo(vo.getDutyAmount()) == 1
                        || (dutyVO.getNoPeopleHurtVO().getOtherLossVO().getLossAmount()).compareTo(vo.getDutyAmount()) == 1) {
                    throw new GlobalBusinessException(ErrorCode.Settle.CHECK_DUTY_GT_MAX_AMOUNT);
                }
            }
            //判断 旅行变更表中的定损金额 与 总保额
            if (null != dutyVO.getNoPeopleHurtVO() && null != dutyVO.getNoPeopleHurtVO().getPropertyLossVO()) {
                List<PropertyLossDTO> nonPleInjuredlistPro = dutyVO.getNoPeopleHurtVO().getPropertyLossVO().getPropertyLossList();
                if (null != vo.getDutyAmount() && !CollectionUtils.isEmpty(nonPleInjuredlistPro)) {
                    for (PropertyLossDTO pd : nonPleInjuredlistPro) {
                        if (null == pd.getLossAmount()) {
                            continue;
                        }
                        if (pd.getLossAmount().compareTo(vo.getDutyAmount()) == 1) {
                            throw new GlobalBusinessException(ErrorCode.Settle.CHECK_DUTY_GT_MAX_AMOUNT);
                        }
                    }
                }
            }
            //判断 旅行变更表中的定损金额 与 总保额
            if (null != dutyVO.getNoPeopleHurtVO() && null != dutyVO.getNoPeopleHurtVO().getTravelAlertVO()) {
                List<TravelAlertDTO> nonPleInjuredlistTrav = dutyVO.getNoPeopleHurtVO().getTravelAlertVO().getTravelAlertlist();
                if (null != vo.getDutyAmount() && !CollectionUtils.isEmpty(nonPleInjuredlistTrav)) {
                    for (TravelAlertDTO td : nonPleInjuredlistTrav) {
                        if (null == td.getLossAmount()) {
                            continue;
                        }
                        if (td.getLossAmount().compareTo(vo.getDutyAmount()) == 1) {
                            throw new GlobalBusinessException(ErrorCode.Settle.CHECK_DUTY_GT_MAX_AMOUNT);
                        }
                    }
                }
            }*/
        }
        return null;
    }

    @Override
    @Transactional
    public void handleDutySurvey(DutySurveyVO dutyVO) throws Exception {
        this.checkDutyAmountLeTotalAmount(dutyVO);
        String isCompleteDuty = dutyVO.getIsCompleteDuty();
        String status = dutyVO.getStatus();
        String userId = WebServletContext.getUserId();
        String reportNo = dutyVO.getReportNo();
        Integer caseTimes = dutyVO.getCaseTimes();

        //是否完成收单
        if (!ChecklossConst.IS_COMPLATE_DUTY.equals(isCompleteDuty)) {
            RunRoleResultVO dutySurveyResultVO = this.saveDutySurveyInfo(dutyVO, userId, status);
            if (dutySurveyResultVO != null) {
                return;
            }
        }
        if(BaseConstant.STRING_1.equals(dutyVO.getStatus())) {
            checkPaymentInfo(dutyVO);//领款人信息校验
        }
        // 添加已结案校验
        String wholeCaseStatus = wholeCaseBaseService.getWholeCaseStatus(reportNo, caseTimes);
        LogUtil.audit("案件已经结案(0是已结案,1是已报案)={}", wholeCaseStatus);
        if (EndCaseConstValues.WHOLE_CASE_END_STATUS.equals(wholeCaseStatus)) {
            LogUtil.info("案件：{}已经结案,无需处理收单！", reportNo);
            throw new GlobalBusinessException(GlobalResultStatus.PAYMENT_STATUS_INVALID.getCode(), MessageFormat.format("案件{0}已经结案,无法处理收单！", reportNo));
        }
        if ("N".equals(dutyVO.getIsSettle())) {
            //提交至工作流完成任务
            if (ChecklossConst.STATUS_TMP_SUBMIT.equals(status)) {
                this.completeDutySurvey(dutyVO, WebServletContext.getUserId());
            }
            //正常赔付结论时更新资料齐全时间
            if(Objects.equals("1",dutyVO.getVerifyConclusionVO().getIndemnityConclusion())){
                claimUpdateDocumentFullDateService.updateDocFullDate(reportNo,caseTimes,null);
            }

        }
        if ("Y".equals(dutyVO.getIsSettle())) {
            String caseProcessStatus = caseProcessService.getCaseProcessStatus(reportNo, caseTimes);
            if (CaseProcessStatus.CASE_CLOSED.getCode().equals(caseProcessStatus)
                || CaseProcessStatus.CASE_CANCELLED.getCode().equals(caseProcessStatus)
                    || CaseProcessStatus.WAIT_VERIFICATION.getCode().equals(caseProcessStatus)) {
                throw new GlobalBusinessException("案件当前状态无法进行收单修改操作！");
            }
            //清空支付项
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(reportNo);
            paymentItemDTO.setCaseTimes(caseTimes);
            paymentItemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
            paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
            paymentItemService.delPaymentItem(paymentItemDTO);
            //重新计算计算金额  单责任明细理算取消自动重新计算
//            policyPayService.reCalculateSettleAmount(reportNo, caseTimes);
            //删除前次理算数据，重新初始化
            claimSettleService.reSettleInit(reportNo, caseTimes);
            if (dutyVO.getVerifyConclusionVO().getIndemnityConclusion().equals(BaseConstant.STRING_4)) {
//                //校验是否有在途的未决审批
//                if(estimateChangeService.checkEstimateChangePending(reportNo, caseTimes)){
//                    throw new GlobalBusinessException("当前案件存在审批中的未决修正，不能发起拒赔！");
//                }
                //如果是重开赔案，且之前是赔付，协议赔付，通融赔付，不能拒赔，否则提升“重开案件不能拒赔”
                /*Integer verfiyCodeCount = wholeCaseBaseMapper.getverfiyCodeCount(reportNo,caseTimes);
                if(caseTimes>1 && verfiyCodeCount>0){
                    throw new GlobalBusinessException("重开案件不能拒赔");
                }*/
                //校验当前流程是否有冲突 拒赔审批
                bpmService.processCheck(reportNo,BpmConstants.OC_REJECT_REVIEW,BpmConstants.OPERATION_SUBMIT);

                bpmService.newSuspendOrActiveTask_oc(reportNo, caseTimes, BpmConstants.OC_MANUAL_SETTLE,true);
                bpmService.startProcess_oc(reportNo, caseTimes, BpmConstants.OC_REJECT_REVIEW);
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.WAIT_REJECT.getCode());
                //操作记录
                operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_CHECK_DUTY, "拒赔", null);
                //操作记录
                operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_REJECT_REVIEW, "发起", null);
            } else {
                //校验当前流程是否有冲突 收单
                if (ChecklossConst.STATUS_TMP_SUBMIT.equals(status)) {
                    bpmService.processCheck(reportNo,BpmConstants.OC_CHECK_DUTY,BpmConstants.OPERATION_SUBMIT);
                }
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.PENDING_SETTLE.getCode());
                //操作记录
                operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_CHECK_DUTY, "提交", null);
                //更新ES同步时间
                caseBaseService.updateEsUpdatedDate(reportNo,caseTimes);
            }
            if(Objects.equals("1",dutyVO.getVerifyConclusionVO().getIndemnityConclusion())){
                claimUpdateDocumentFullDateService.updateDocFullDate(reportNo,caseTimes,null);
            }

        }
    }

    @Override
    @Transactional
    public RunRoleResultVO saveDutySurveyInfo(DutySurveyVO dutySurveyVO, String loginUm, String status) throws Exception {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        dutySurveyVO.setUserId(loginUm);
        String taskId = dutySurveyVO.getTaskId();
        String recordTaskId = dutySurveyVO.getRecordTaskId();
        LogUtil.audit("#保存收单数据,reportNo=%s,taskId=%s,status=%s,recordTaskId=%s", reportNo, taskId, status, dutySurveyVO.getRecordTaskId());
        String conclusionKey = "";
        VerifyConclusionVO verifyConclusionVO = null;
        if (dutySurveyVO.getVerifyConclusionVO() != null) {
            verifyConclusionVO = dutySurveyVO.getVerifyConclusionVO();
            conclusionKey = this.getConclusionKey(verifyConclusionVO);
        }
        if (ChecklossConst.STATUS_TMP_SUBMIT.equals(status)) {
            this.checkPolicyInfo(reportNo);

            if (BpmConstants.CHECK_DUTY.equals(taskId) || BpmConstants.MANUAL_SETTLE.equals(taskId)) {
                this.isValidSaveForDuty(conclusionKey, reportNo, caseTimes);
                if (ChecklossConst.VERIFY_REFUSE.equals(conclusionKey)) {
                    LogUtil.audit("核责拒赔校验签报是否同意reportNo={}", reportNo);
                    boolean agree = true;
                    LogUtil.audit("核责拒赔校验签报是否同意reportNo={},agree={}", reportNo, agree);
                }
            }
        } else if (ChecklossConst.STATUS_TMP_SAVE.equals(status)) {
            SurveyDTO surveyDTO = new SurveyDTO();
            surveyDTO.setCaseTimes(caseTimes);
            surveyDTO.setReportNo(reportNo);
            surveyDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
            surveyDTO.setTaskId(taskId);
        }
        List<String> cscs = dutySurveyVO.getCaseSubClass();
        CaseClassDTO ccDTO = new CaseClassDTO();
        ccDTO.setReportNo(reportNo);
        ccDTO.setCaseTimes(caseTimes);
        ccDTO.setTaskId(taskId);
        ccDTO.setUpdatedBy(loginUm);
        caseClassDao.updateEffective(ccDTO);
        if (!CollectionUtils.isEmpty(cscs)) {
            List<CaseClassDTO> caseClassList = new ArrayList<>();
            Date date= new Date();
            for (String caseSubClass : cscs) {
                CaseClassDTO caseClassDTO = new CaseClassDTO();
                caseClassDTO.setReportNo(reportNo);
                caseClassDTO.setCaseTimes(caseTimes);
                caseClassDTO.setTaskId(taskId);
                caseClassDTO.setStatus(status);
                caseClassDTO.setCaseSubClass(caseSubClass);
                //加五秒存是为了保证和立案时的创建时间不在同一个点，线上同时调用立案接口和收单接口时 创建时间会一样
                caseClassDTO.setCreatedDate(DateUtil.offsetSecond(date,25));
                caseClassDTO.setUpdatedDate(DateUtil.offsetSecond(date,25));
                caseClassList.add(caseClassDTO);
            }
            LogUtil.info("date创建时间={}收单案件类别报案号={}，案件类别信息={}",DateUtils.parseToFormatString(date),reportNo, JsonUtils.toJsonString(caseClassList));
            caseClassDao.saveCaseClassList(caseClassList, caseTimes, loginUm,null);
        }

        String channelProcessId = null;
        PeopleHurtVO peopleHurtVO = dutySurveyVO.getPeopleHurtVO();
        if (peopleHurtVO != null) {
            peopleHurtVO.setTaskId(taskId);
            peopleHurtVO.setStatus(status);
            channelProcessId = peopleHurtVO.getIdAhcsChannelProcess();
            if (StringUtils.isEmptyStr(channelProcessId)) {
                channelProcessId = this.getChannelProcessId(reportNo, caseTimes, dutySurveyVO.getPartyNo(), ConstValues.CASECLASS_PEOPLE_HURT, loginUm);
                peopleHurtVO.setIdAhcsChannelProcess(channelProcessId);
            }
            modifyPeopleHurtVO(reportNo, caseTimes, loginUm, peopleHurtVO, cscs);
        }
        //更新下是否人伤跟踪标识
        //wholeCaseBaseService.updateIsPersonTrace(reportNo, caseTimes, dutySurveyVO.getIsPersonTrace());
        NoPeopleHurtVO noPeopleHurtVO = dutySurveyVO.getNoPeopleHurtVO();
        if (noPeopleHurtVO != null) {
            noPeopleHurtVO.setTaskId(taskId);
            noPeopleHurtVO.setStatus(status);
            channelProcessId = noPeopleHurtVO.getIdAhcsChannelProcess();
            if (StringUtils.isEmptyStr(channelProcessId)) {
                channelProcessId = this.getChannelProcessId(reportNo, caseTimes, dutySurveyVO.getPartyNo(), ConstValues.CASECLASS_NO_PEOPLE_HURT, loginUm);
                noPeopleHurtVO.setIdAhcsChannelProcess(channelProcessId);
            }
            modifyNoPeopleHurtVO(reportNo, caseTimes, loginUm, noPeopleHurtVO);
        }

        if (dutySurveyVO.getPersonRescueVO() != null) {
            savePersonRescue(dutySurveyVO.getPersonRescueVO(), reportNo, caseTimes, loginUm, taskId, status);
        }

        //动态字段处理
        if(!CollectionUtils.isEmpty(dutySurveyVO.getDynamicFieldResult())) {
            dynamicFieldResultService.saveDynamicFieldResult(dutySurveyVO.getDynamicFieldResult());
        }

        dutySurveyVO.setAccidentCauseLevel3(dutySurveyVO.getAccidentCauseLevel1());
        dutySurveyVO.setAccidentCauseLevel4(dutySurveyVO.getAccidentCauseLevel2());
        //更新CLM_REPORT_ACCIDENT表的出险原因字段
        reportAccidentService.updateAccidentReasonByReportNo(reportNo,dutySurveyVO.getAccidentCauseLevel3(),dutySurveyVO.getAccidentCauseLevel4(),loginUm);

        if (TacheConstants.TEL_SURVEY.equals(taskId) || TacheConstants.FIELD_SURVEY.equals(taskId)) {
            this.saveForSurvey(dutySurveyVO, loginUm, status);


        } else if (BpmConstants.CHECK_DUTY.equals(taskId) || BpmConstants.MANUAL_SETTLE.equals(taskId)) {
            dutySurveyVO.setRecordTaskId(recordTaskId);
            try {
                if(ListUtils.isNotEmpty(dutySurveyVO.getPolicyRiskGroupList())){
                    RiskDomainDTO riskDomainDTO = new RiskDomainDTO(reportNo,caseTimes,taskId,loginUm,dutySurveyVO.getPolicyRiskGroupList(),null);
                    riskPropertyService.saveCaseRiskPropertyList(riskDomainDTO);
                }else {
                    CaseRiskPropertyDTO caseRiskPropertyDTO = new CaseRiskPropertyDTO();
                    caseRiskPropertyDTO.setReportNo(reportNo);
                    caseRiskPropertyDTO.setCaseTimes(caseTimes);
                    String caseRiskPropertyLastTaskId = riskPropertyService.getCaseRiskPropertyLastTaskId(caseRiskPropertyDTO);
                    if (StringUtils.isNotEmpty(caseRiskPropertyLastTaskId)) {
                        caseRiskPropertyDTO.setUpdatedBy(loginUm);
                        caseRiskPropertyDTO.setTaskId(taskId);
                        riskPropertyService.updateReportRiskPropertyTaskId(caseRiskPropertyDTO);
                    }
                }


                if(ListUtils.isNotEmpty(dutySurveyVO.getRiskPropertyList()) && dutySurveyVO.getRiskPropertyList().get(0) != null){
                    CaseRiskPropertyDTO riskPropertyDTO = dutySurveyVO.getRiskPropertyList().get(0);
                    String riskGroupNo = riskPropertyDTO.getRiskGroupNo();
                    String riskGroupName = riskPropertyDTO.getRiskGroupName();
                    String policyNo = riskPropertyDTO.getPolicyNo();

                    CaseBaseEntity caseBase = caseBaseService.getCaseBaseInfo(reportNo,policyNo,caseTimes);
                    if(!StrUtil.equals(caseBase.getRiskGroupNo(), riskGroupNo)) {
                        caseBaseService.updateRiskGroup(caseBase.getIdClmCaseBase(), riskGroupNo, riskGroupName);
                        policyPayService.deletePolicyPays(reportNo, caseTimes);
                    }
                    RiskDomainDTO riskDomainDTO = new RiskDomainDTO(reportNo,caseTimes,taskId,loginUm,null,dutySurveyVO.getRiskPropertyList());
                    riskPropertyService.dealRiskProperty(riskDomainDTO);
                }

                if(!"SYSTEM".equals(loginUm) && ListUtils.isEmptyList(dutySurveyVO.getPolicyRiskGroupList()) && ListUtils.isEmptyList(dutySurveyVO.getRiskPropertyList())){
                    CaseRiskPropertyDTO caseRiskPropertyDTO = new CaseRiskPropertyDTO();
                    caseRiskPropertyDTO.setReportNo(reportNo);
                    caseRiskPropertyDTO.setCaseTimes(caseTimes);
                    caseRiskPropertyDTO.setUpdatedBy(loginUm);
                    caseRiskPropertyDTO.setTaskId(taskId);
                    riskPropertyService.removeCaseRiskProperty(caseRiskPropertyDTO);
                }
            }catch (Exception e){
                LogUtil.info("收单保存标的失败,不影响原有流程",e);
            }
            return this.saveForDuty(dutySurveyVO, loginUm, status, channelProcessId, conclusionKey);
        }

        return null;
    }

    /**
     * 抽取原先收单方法，做相应调整
     * */
    @Transactional
    public RunRoleResultVO saveDutySurvey(DutySurveyVO dutySurveyVO, String loginUm, String status) throws GlobalBusinessException {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        dutySurveyVO.setUserId(loginUm);
        String taskId = dutySurveyVO.getTaskId();
        String recordTaskId = dutySurveyVO.getRecordTaskId();
        LogUtil.audit("#保存收单数据,reportNo=%s,taskId=%s,status=%s,recordTaskId=%s", reportNo, taskId, status, dutySurveyVO.getRecordTaskId());
        //因为默认赔付，所以直接置为1
        String conclusionKey = "1";
        //暂时都是只有发送状态
        if (ChecklossConst.STATUS_TMP_SUBMIT.equals(status)) {
            this.checkPolicyInfo(reportNo);
            if (BpmConstants.CHECK_DUTY.equals(taskId) || BpmConstants.MANUAL_SETTLE.equals(taskId)) {
                this.isValidSaveForDuty(conclusionKey, reportNo, caseTimes);
                if (ChecklossConst.VERIFY_REFUSE.equals(conclusionKey)) {
                    LogUtil.audit("核责拒赔校验签报是否同意reportNo={}", reportNo);
                    boolean agree = true;
                    LogUtil.audit("核责拒赔校验签报是否同意reportNo={},agree={}", reportNo, agree);
                }
            }
        }
        //案件小类，这里直接查表获取
//        List<String> cscs = dutySurveyVO.getCaseSubClass();
        List<String> caseClassCodeList = caseClassDao.getCaseSubClassList(reportNo, caseTimes, taskId);
        CaseClassDTO ccDTO = new CaseClassDTO();
        ccDTO.setReportNo(reportNo);
        ccDTO.setCaseTimes(caseTimes);
        ccDTO.setTaskId(taskId);
        ccDTO.setUpdatedBy(loginUm);
        caseClassDao.updateEffective(ccDTO);
        if (caseClassCodeList.size() > 0) {
            List<CaseClassDTO> caseClassList = new ArrayList<>();
            for (String caseSubClass : caseClassCodeList) {
                CaseClassDTO caseClassDTO = new CaseClassDTO();
                caseClassDTO.setReportNo(reportNo);
                caseClassDTO.setCaseTimes(caseTimes);
                caseClassDTO.setTaskId(taskId);
                caseClassDTO.setStatus(status);
                caseClassDTO.setCaseSubClass(caseSubClass);
                caseClassList.add(caseClassDTO);
            }
            caseClassDao.addCaseClassList(caseClassList, caseTimes, loginUm);
        }

        String channelProcessId = null;
        if (TacheConstants.TEL_SURVEY.equals(taskId) || TacheConstants.FIELD_SURVEY.equals(taskId)) {
            this.saveForSurvey(dutySurveyVO, loginUm, status);
        } else if (BpmConstants.CHECK_DUTY.equals(taskId) || BpmConstants.MANUAL_SETTLE.equals(taskId)) {
            dutySurveyVO.setRecordTaskId(recordTaskId);
            return this.saveForDuty(dutySurveyVO, loginUm, status, channelProcessId, conclusionKey);
        }

        return null;
    }

    @Override
    public void saveEndorsementRemark(String reportNo, Integer caseTimes, String loginUm, String endorsementRemark) {
        if (StringUtils.isEmptyStr(endorsementRemark)) {
            return;
        }
        endorsementService.deleteByPolicyPayId(reportNo, caseTimes);
        EndorsementDTO endorsement = new EndorsementDTO();
        endorsement.setReportNo(reportNo);
        endorsement.setCaseTimes(caseTimes);
        endorsement.setUpdatedBy(loginUm);
        endorsement.setUpdatedDate(new Date());
        endorsement.setIdAhcsEndorsement(UuidUtil.getUUID());
        endorsement.setEndorsementRemark(endorsementRemark);
        endorsement.setCreatedBy(loginUm);
        endorsement.setCreatedDate(new Date());
        endorsementService.addEndorsementInfo(endorsement);
    }

    @Override
    public DutySurveyVO getDutySurvey(String reportNo, int caseTimes, String taskId) throws Exception {
        LogUtil.audit("getDutySurvey reportNo={},taskId={}", reportNo, taskId);
        boolean fromDutyPage = "dutyPage".equals(taskId);
        if (fromDutyPage) {
            taskId = null;
        }

        DutySurveyVO dutySurveyVO = new DutySurveyVO();
        dutySurveyVO.setStandardVersionSwitch(SwitchServiceUtil.getStandardVersionSwitch());
        if (StringUtils.isEmptyStr(taskId)) {
            taskId = caseClassDao.getCurrentTaskCodeFromCaseClass(reportNo, caseTimes, "");
        }

        LogUtil.audit("getDuty reportNo={},taskId={}", reportNo, taskId);

        VerifyConclusionDTO dutyDTO = verifyConclusiondao.getDuty(reportNo, caseTimes, "", taskId);

        if (dutyDTO == null) {
            dutyDTO = new VerifyConclusionDTO();
        }

        List<String> caseSubClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, taskId);

        List<CaseClassDTO> caseClassList = caseClassDao.getBigCaseClassList(reportNo, caseTimes, "", taskId);

        List<String> caseClasses = new ArrayList<>();

        if (caseClassList != null && caseClassList.size() > 0) {
            for (CaseClassDTO caseClass : caseClassList) {
                caseClasses.add(caseClass.getCaseSubClass());
            }
        }

        if (ListUtils.isNotEmpty(caseSubClassList) ) {
            ClmsTraceRecordDTO traceRecordDTO = new ClmsTraceRecordDTO();
            traceRecordDTO.setReportNo(reportNo);
            traceRecordDTO.setCaseTimes(caseTimes);
            List records = clmsTraceRecordMapper.selectPage(traceRecordDTO);
            if(records != null && records.size() > 0 && !caseSubClassList.contains("IAT_10_01")){
                caseClasses.add("10");
                caseSubClassList.add("IAT_10_01");
            }
            dutySurveyVO.setCaseSubClass(caseSubClassList);
            List<String> caseSubClassName = new ArrayList<>();
            caseSubClassList.forEach(e -> {
                caseSubClassName.add(InsuredApplyTypeEnum.getName(e));
            });
            dutySurveyVO.setCaseSubClassName(caseSubClassName);
        }
        dutySurveyVO.setCaseClass(caseClasses);

        List<ChannelProcessDTO> channelProcessList = channelProcessService.getChanelProcessIdList(reportNo, caseTimes);

        if (channelProcessList != null && !channelProcessList.isEmpty()) {
            for (ChannelProcessDTO channelProcess : channelProcessList) {
                switch (channelProcess.getChannelType()) {
                    case ChecklossConst.CASECLASS_PEOPLE_HURT:
                        PeopleHurtVO peopleHurtVO = getPeopleHurtVO(reportNo, caseTimes, channelProcess.getIdAhcsChannelProcess(), taskId);
                        peopleHurtVO.setChannelType(ChecklossConst.CASECLASS_PEOPLE_HURT);
                        peopleHurtVO.setIdAhcsChannelProcess(channelProcess.getIdAhcsChannelProcess());
                        dutySurveyVO.setPeopleHurtVO(peopleHurtVO);
                        break;
                    case ChecklossConst.CASECLASS_NO_PEOPLE_HURT:
                        NoPeopleHurtVO noPeopleHurtVO = getNoPeopleHurtVO(reportNo, caseTimes, taskId, null);
                        noPeopleHurtVO.setChannelType(ChecklossConst.CASECLASS_NO_PEOPLE_HURT);
                        noPeopleHurtVO.setIdAhcsChannelProcess(channelProcess.getIdAhcsChannelProcess());
                        dutySurveyVO.setNoPeopleHurtVO(noPeopleHurtVO);
                        break;

                    default:
                        break;
                }
            }
        }
        dutySurveyVO.setSurveyVO(getSurveyVOByCaseType(reportNo, caseTimes, 1));
        VerifyConclusionVO verifyConclusionVO = new VerifyConclusionVO();
        BeanUtils.copyProperties(dutyDTO, verifyConclusionVO);

        List<DutyRejectDetailVO> policyNoList = dutyRejectDetailDao.getEstimatePolicyByReportNo(reportNo, caseTimes);
        verifyConclusionVO.setPolicyNoList(policyNoList);
        List<DutyRejectDetailVO> dutyRejectDetailList = dutyRejectDetailDao.getDutyRejectDetailList(reportNo, caseTimes, "", null);
        if (ListUtils.isEmptyList(dutyRejectDetailList)) {
            BigDecimal rejectAmount = new BigDecimal(0);
            MedicalBillInfoDTO medicalBillInfoDTO = medicalBillService.getMedicalBillAllAmount(reportNo, caseTimes, null);
            if (medicalBillInfoDTO != null) {
                BigDecimal reasonableAmount = medicalBillInfoDTO.getReasonableAmount();
                if (reasonableAmount != null) {
                    rejectAmount = rejectAmount.add(reasonableAmount);
                }
            }

            DutyRejectDetailVO dutyRejectDetail = new DutyRejectDetailVO();
            dutyRejectDetail.setRejectAmount(rejectAmount);
            if (ListUtils.isNotEmpty(policyNoList)) {
                dutyRejectDetail.setPolicyNo(policyNoList.get(0).getPolicyNo());
                dutyRejectDetail.setPolicyCerNo(policyNoList.get(0).getPolicyCerNo());
            }
            dutyRejectDetailList = new ArrayList<>();
            dutyRejectDetailList.add(dutyRejectDetail);
        }
        verifyConclusionVO.setDutyRejectDetailList(dutyRejectDetailList);

        dutySurveyVO.setReportNo(reportNo);
        dutySurveyVO.setCaseTimes(caseTimes);
        EndorsementDTO endorsementDTO = endorsementService.getByReportNoAndCaseTime(reportNo, caseTimes);
        if (endorsementDTO != null) {
            verifyConclusionVO.setEndorsementRemark(endorsementDTO.getEndorsementRemark());
        }

        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase2(reportNo, caseTimes);
        if (wholeCaseBaseDTO != null) {
            verifyConclusionVO.setIndemnityConclusion(wholeCaseBaseDTO.getIndemnityConclusion());
            verifyConclusionVO.setIndemnityModel(wholeCaseBaseDTO.getIndemnityModel());
            dutySurveyVO.setMigrateFrom(wholeCaseBaseDTO.getMigrateFrom());
            //dutySurveyVO.setIsPersonTrace(wholeCaseBaseDTO.getIsPersonTrace());
        }
        LossReduceDTO lossReduce = lossReduceMapper.getLossReduce(reportNo, caseTimes);
        if (lossReduce != null) {
            verifyConclusionVO.setReduceAmount(lossReduce.getReduceAmount());
        }
        dutySurveyVO.setVerifyConclusionVO(verifyConclusionVO);

        String taskIdHis = caseClassDao.getCurrentTaskCodeFromCaseClass(reportNo, caseTimes, ChecklossConst.STATUS_TMP_SUBMIT);
        PersonAccidentDTO personAccidentDTO = personAccidentDao.getPersonAccidentInfo(reportNo, String.valueOf(caseTimes), taskIdHis, ChecklossConst.STATUS_TMP_SUBMIT);
        if (personAccidentDTO != null && StringUtils.isNotEmpty(personAccidentDTO.getIsHugeAccident())) {
            dutySurveyVO.setIsHugeAccident(personAccidentDTO.getIsHugeAccident());
        }
        ReportBaseInfoResData reportBaseInfoResData = reportInfoService.requestReportBaseInfo(reportNo);
        if (Objects.nonNull(reportBaseInfoResData)) {
            dutySurveyVO.setName(reportBaseInfoResData.getName());
            dutySurveyVO.setBirthDay(reportBaseInfoResData.getBirthday());
            dutySurveyVO.setCertificateType(reportBaseInfoResData.getCertificateType());
            dutySurveyVO.setCertificateNo(reportBaseInfoResData.getCertificateNo());
            dutySurveyVO.setRiskFlag(reportBaseInfoResData.getRiskFlag());
            dutySurveyVO.setRiskRemark(reportBaseInfoResData.getRiskRemark());
            dutySurveyVO.setAccidentCauseLevel1(reportBaseInfoResData.getAccidentCauseLevel3Code());
            dutySurveyVO.setAccidentCauseLevel2(reportBaseInfoResData.getAccidentCauseLevel4Code());
            LogUtil.audit("完整dutySurveyVO参数：{}", JSON.toJSONString(dutySurveyVO));
        }

        AdressSearchDto adressSearchDto = new AdressSearchDto();

        PersonAccidentVO personAccidentVO = new PersonAccidentVO();
        if (null != dutySurveyVO.getPeopleHurtVO() && null != dutySurveyVO.getPeopleHurtVO().getAccidentVO() && null != personAccidentVO.getOverseasOccur()) {
            personAccidentVO = dutySurveyVO.getPeopleHurtVO().getAccidentVO();
            if (personAccidentVO.getOverseasOccur().equals("0")) {
                adressSearchDto.setOverseasOccur(personAccidentVO.getOverseasOccur()).setAccidentProvinceCode(personAccidentVO.getAccidentProvince())
                        .setAccidentCountyCode(personAccidentVO.getAccidentCounty()).setAccidentCityCode(personAccidentVO.getAccidentCity());
            } else {
                adressSearchDto.setOverseasOccur(personAccidentVO.getOverseasOccur()).setAccidentAreaCode(personAccidentVO.getAccidentArea())
                        .setAccidentNationCode(personAccidentVO.getAccidentNation());
            }
        }

        AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
        personAccidentVO.setAccidentAreaName(detailAdressFormCode.getAccidentAreaName());
        personAccidentVO.setAccidentNationName(detailAdressFormCode.getAccidentNationName());
        personAccidentVO.setAccidentProvinceName(detailAdressFormCode.getAccidentProvinceName());
        personAccidentVO.setAccidentCityName(detailAdressFormCode.getAccidentCityName());
        personAccidentVO.setAccidentCountyName(detailAdressFormCode.getAccidentCountyName());
        List<ClaimRejectionApprovalRecordEntity> claimRejectionApprovalRecordEntities = claimRejectionApprovalRecordEntityMapper.selectByReportNo(reportNo,caseTimes);
        dutySurveyVO.setClaimRejectionApprovalRecordEntity(claimRejectionApprovalRecordEntities);

        dutySurveyVO.setDynamicFieldResult(dynamicFieldResultService.getDynamicFieldResultReport(reportNo, caseTimes, caseSubClassList));

        try {
            CaseRiskPropertyDTO caseRiskQuery = new CaseRiskPropertyDTO(reportNo,caseTimes,taskId);
            caseRiskQuery.setMatchSelected(true);
            dutySurveyVO.setPolicyRiskGroupList(riskPropertyService.getCaseRiskProperty(caseRiskQuery));
            CaseRiskPropertyDTO caseRiskPropertyDTO = new CaseRiskPropertyDTO();
            caseRiskPropertyDTO.setReportNo(reportNo);
            caseRiskPropertyDTO.setCaseTimes(caseTimes);
            dutySurveyVO.setRiskPropertyList(riskPropertyService.getLastTaskIdCaseRiskPropertyList(caseRiskPropertyDTO));
        }catch (Exception e){
            LogUtil.info("收单查询标的失败,不影响原有流程",e);
        }
        return dutySurveyVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyPeopleHurtVO(String reportNo, int caseTimes, String loginUm, PeopleHurtVO peopleHurtVO, List<String> subCaseList) throws GlobalBusinessException {
        String channelProcessId = peopleHurtVO.getIdAhcsChannelProcess();
        String taskId = peopleHurtVO.getTaskId();
        String status = peopleHurtVO.getStatus();
        String isHugeAccident = "";
        // String hugeAccidentId = "";
        String hugeAccidentCode = null;
        String tempCaseTimes = String.valueOf(caseTimes);

        PersonAccidentDTO personAccident = personAccidentDao.getPersonAccident(channelProcessId, taskId, null);
        if (personAccident != null) {
            thirdCarService.removeThirdCarList(personAccident.getPersonAccidentId());

            PersonAccidentDTO paDTO = new PersonAccidentDTO();
            paDTO.setIdAhcsChannelProcess(channelProcessId);
            paDTO.setTaskId(taskId);
            paDTO.setUpdatedBy(loginUm);
            personAccidentDao.updateEffective(paDTO);
        }
        if (peopleHurtVO.getAccidentVO() != null) {
            PersonAccidentVO personAccidentVO = peopleHurtVO.getAccidentVO();

            isHugeAccident = personAccidentVO.getIsHugeAccident();
            // hugeAccidentId = personAccidentVO.getIdHugeAccidentInfo();
            if (CommonConstant.YES.equals(personAccidentVO.getIsHugeAccident())) {
                hugeAccidentCode = accidentInfoService.getAccidentCode(personAccidentVO.getHugeAccidentName());
            }

            PersonAccidentDTO personAccidentDTO = new PersonAccidentDTO();
            BeanUtils.copyProperties(personAccidentVO, personAccidentDTO);
            personAccidentDTO.setHugeAccidentCode(hugeAccidentCode);
            personAccidentDTO.setIdAhcsChannelProcess(channelProcessId);
            personAccidentDTO.setTaskId(taskId);
            personAccidentDTO.setStatus(status);
            if (!ConstValues.NO.equals(personAccidentDTO.getIsFullAmount())) {
                personAccidentDTO.setConstructionPayRate(null);
            }
            StringBuilder workInjuryTypes = new StringBuilder();
            if (ListUtils.isNotEmpty(personAccidentVO.getWorkInjuryTypeList())) {
                for (String workInjuryType : personAccidentVO.getWorkInjuryTypeList()) {
                    workInjuryTypes.append(workInjuryType).append(",");
                }
                personAccidentDTO.setWorkInjuryType(workInjuryTypes.substring(0, workInjuryTypes.length() - 1));
            }
            String idAhcsPersonAccident = UuidUtil.getUUID();
            personAccidentDTO.setPersonAccidentId(idAhcsPersonAccident);

            personAccidentDTO.setProvinceCode(personAccidentVO.getAccidentProvince());
            personAccidentDTO.setAccidentCityCode(personAccidentVO.getAccidentCity());
            personAccidentDTO.setAccidentCountyCode(personAccidentVO.getAccidentCounty());
            personAccidentDTO.setReportNo(reportNo);
            if(Objects.isNull(personAccidentDTO.getAccidentTime())){
                //查询事故时间并赋值
                if(Objects.nonNull(personAccident)){
                    personAccidentDTO.setAccidentTime(personAccident.getAccidentTime());
                }else{
                    //查询数据
                    PersonAccidentDTO dto= personAccidentDao.getPersonAccidentByReportNo(reportNo,null,caseTimes);
                    personAccidentDTO.setAccidentTime(dto.getAccidentTime());
                }
            }
            this.savePersonAccident(reportNo, caseTimes, loginUm, personAccidentDTO);
            thirdCarService.addThirdCarList(personAccidentVO.getThirdCarList(), loginUm, idAhcsPersonAccident);
        }


        if (!TacheConstants.TEL_ESTIAMTE_TRACK.equals(taskId)) {
            WholeCaseBaseDTO wholeCaseBase = new WholeCaseBaseDTO();
            wholeCaseBase.setReportNo(reportNo);
            wholeCaseBase.setCaseTimes(caseTimes);
            wholeCaseBase.setUpdatedBy(loginUm);
            wholeCaseBase.setHugeAccidentCode(hugeAccidentCode);
            wholeCaseBase.setIsHugeAccident(isHugeAccident);
            wholeCaseBaseService.modifyHugeAccident(wholeCaseBase);
        }


        if (peopleHurtVO.getDiseaseVO() != null) {
            PersonDiseaseVO personDiseaseVO = peopleHurtVO.getDiseaseVO();
            PersonDiseaseDTO personDiseaseDTO = new PersonDiseaseDTO();
            BeanUtils.copyProperties(personDiseaseVO, personDiseaseDTO);
            personDiseaseDTO.setIdAhcsChannelProcess(channelProcessId);
            personDiseaseDTO.setTaskId(taskId);
            personDiseaseDTO.setStatus(status);
            personDiseaseDTO.setUpdatedBy(loginUm);
            personDiseaseDTO.setInformReason(personDiseaseVO.getInformReason());
            personDiseaseDao.updateEffective(personDiseaseDTO);
            savePersonDisease(reportNo, caseTimes, loginUm, personDiseaseDTO);
        } else {

            PersonDiseaseDTO personDiseaseDTO = new PersonDiseaseDTO();
            personDiseaseDTO.setIdAhcsChannelProcess(channelProcessId);
            personDiseaseDTO.setTaskId(taskId);
            personDiseaseDTO.setUpdatedBy(loginUm);
            personDiseaseDao.updateEffective(personDiseaseDTO);
        }

        if (peopleHurtVO.getDiagnoseVO() != null) {
            PersonDiagnoseVO personDiagnoseVO = peopleHurtVO.getDiagnoseVO();
            List<PersonDiagnoseDTO> diagnoses = personDiagnoseVO.getDiagnoseDTOs();

            PersonDiagnoseDTO pdDTO = new PersonDiagnoseDTO();
            pdDTO.setIdAhcsChannelProcess(channelProcessId);
            pdDTO.setTaskId(taskId);
            pdDTO.setUpdatedBy(loginUm);
            personDiagnoseDao.updateEffective(pdDTO);

            HashSet<String> repeatSet = new HashSet<>();
            if (!CollectionUtils.isEmpty(diagnoses)) {
                for (PersonDiagnoseDTO personDiagnoseDTO : diagnoses) {
                    if (StringUtils.isEmptyStr(personDiagnoseDTO.getDiagnoseCode()) && StringUtils.isEmptyStr(personDiagnoseDTO.getIsSurgical())
                            && StringUtils.isEmptyStr(personDiagnoseDTO.getSurgicalName())) {
                        continue;
                    }
                    if (repeatSet.contains(personDiagnoseDTO.getDiagnoseCode())) {
                        continue;
                    } else {
                        repeatSet.add(personDiagnoseDTO.getDiagnoseCode());
                    }
                    personDiagnoseDTO.setIdAhcsChannelProcess(channelProcessId);
                    personDiagnoseDTO.setTaskId(taskId);
                    personDiagnoseDTO.setStatus(status);

                    savePersonDiagnose(reportNo, caseTimes, loginUm, personDiagnoseDTO);
                }
            }
        } else {

            PersonDiagnoseDTO pdDTO = new PersonDiagnoseDTO();
            pdDTO.setIdAhcsChannelProcess(channelProcessId);
            pdDTO.setTaskId(taskId);
            pdDTO.setUpdatedBy(loginUm);
            personDiagnoseDao.updateEffective(pdDTO);
        }

        if (peopleHurtVO.getDisabilityVO() != null) {
            PersonDisabilityVO personDisabilityVO = peopleHurtVO.getDisabilityVO();
            List<PersonDisabilityDTO> bisabilitys = personDisabilityVO.getDisabilityDTOs();
            if (ListUtils.isNotEmpty(bisabilitys)) {

                PersonDisabilityDTO pdDTO = new PersonDisabilityDTO();
                pdDTO.setIdAhcsChannelProcess(channelProcessId);
                pdDTO.setTaskId(taskId);
                pdDTO.setUpdatedBy(loginUm);
                personDisabilityDao.updateEffective(pdDTO);

                for (PersonDisabilityDTO personDisabilityDTO : bisabilitys) {
                    checkTime(personDisabilityDTO.getAssessmentDate(), "评残", reportNo, tempCaseTimes, channelProcessId);

                    personDisabilityDTO.setIdAhcsChannelProcess(channelProcessId);
                    personDisabilityDTO.setTaskId(taskId);
                    personDisabilityDTO.setStatus(status);

                    savePersonDisability(reportNo, caseTimes, loginUm, personDisabilityDTO);
                }
            }
        } else {

            PersonDisabilityDTO pdDTO = new PersonDisabilityDTO();
            pdDTO.setIdAhcsChannelProcess(channelProcessId);
            pdDTO.setTaskId(taskId);
            pdDTO.setUpdatedBy(loginUm);
            personDisabilityDao.updateEffective(pdDTO);
        }

        if (peopleHurtVO.getDeathVO() != null) {
            PersonDeathVO personDeathVO = peopleHurtVO.getDeathVO();

            checkTime(personDeathVO.getDeathDate(), "死亡", reportNo, tempCaseTimes, channelProcessId);

            PersonDeathDTO personDeathDTO = new PersonDeathDTO();
            BeanUtils.copyProperties(personDeathVO, personDeathDTO);
            personDeathDTO.setIdAhcsChannelProcess(channelProcessId);
            personDeathDTO.setTaskId(taskId);
            personDeathDTO.setStatus(status);
            personDeathDTO.setUpdatedBy(loginUm);

            personDeathDao.updateEffective(personDeathDTO);

            savePersonDeath(reportNo, caseTimes, loginUm, personDeathDTO);
        } else {

            PersonDeathDTO personDeathDTO = new PersonDeathDTO();
            personDeathDTO.setIdAhcsChannelProcess(channelProcessId);
            personDeathDTO.setTaskId(taskId);
            personDeathDTO.setUpdatedBy(loginUm);
            personDeathDao.updateEffective(personDeathDTO);
        }

        if (peopleHurtVO.getBigDiseaseVO() != null) {
            BigDiseaseVO bigDiseaseVO = peopleHurtVO.getBigDiseaseVO();
            BigDiseaseDTO bigDiseaseDTO = new BigDiseaseDTO();
            BeanUtils.copyProperties(bigDiseaseVO, bigDiseaseDTO);

            BigDiseaseDTO bigDisease = bigDiseaseDao.getBigDisease(channelProcessId, taskId, null);
            if (bigDisease != null) {
                bigDiseaseDetailDao.removeBigDiseaseDetail(bigDisease.getBigDiseaseId());

                BigDiseaseDTO bdDTO = new BigDiseaseDTO();
                bdDTO.setIdAhcsChannelProcess(channelProcessId);
                bdDTO.setTaskId(taskId);
                bdDTO.setUpdatedBy(loginUm);
                bigDiseaseDao.updateEffective(bdDTO);
            }
            bigDiseaseDTO.setIdAhcsChannelProcess(channelProcessId);
            bigDiseaseDTO.setTaskId(taskId);
            bigDiseaseDTO.setStatus(status);

            saveBigDisease(reportNo, caseTimes, loginUm, bigDiseaseDTO, peopleHurtVO.getBigDiseaseDetailCode());
        } else {
            BigDiseaseDTO bigDisease = bigDiseaseDao.getBigDisease(channelProcessId, taskId, null);
            if (bigDisease != null) {
                bigDiseaseDetailDao.removeBigDiseaseDetail(bigDisease.getBigDiseaseId());

                BigDiseaseDTO bdDTO = new BigDiseaseDTO();
                bdDTO.setIdAhcsChannelProcess(channelProcessId);
                bdDTO.setTaskId(taskId);
                bdDTO.setUpdatedBy(loginUm);
                bigDiseaseDao.updateEffective(bdDTO);
            }
        }

        if (peopleHurtVO.getDisabilityAppraisalVO() != null) {
            List<DisabilityAppraisalVO> disabilityAppraisalVOList = peopleHurtVO.getDisabilityAppraisalVO();
            disabilityAppraisalVOList.forEach(disabilityAppraisalVO -> {
                disabilityAppraisalVO.setIdAhcsChannelProcess(channelProcessId);
                disabilityAppraisalVO.setStatus(status);
                saveDisabilityAppraisal(reportNo, caseTimes, loginUm, disabilityAppraisalVO);
            });
        } else {
            disabilityAppraisalService.removeDisabilityAppraisalByIdAhcsChannelProcess(channelProcessId);
        }

        if (peopleHurtVO.getHospitalVO() != null) {
            PersonHospitalVO personHospitalVO = peopleHurtVO.getHospitalVO();
            personHospitalVO.setIdAhcsChannelProcess(channelProcessId);
            personHospitalVO.setTaskId(taskId);
            personHospitalVO.setStatus(status);

            savePersonHospital(reportNo, caseTimes, loginUm, personHospitalVO);
        } else {
            personHospitalService.removePersonHospitalByIdAhcsChannelProcess(channelProcessId, taskId);
        }

        if (peopleHurtVO.getObjectExVO() != null) {
            PersonObjectExVO personObjectExVO = peopleHurtVO.getObjectExVO();
            personObjectExVO.setIdAhcsChannelProcess(channelProcessId);
            personObjectExVO.setStatus(status);
            PersonObjectExDTO existsObjectExDTO = personObjectExService.getPersonOtherInfoByIdChannelProcess(channelProcessId);
            if (existsObjectExDTO != null) {
                personObjectExService.removePersonOtherInfo(channelProcessId);
            }
            savePersonObjectEx(reportNo, caseTimes, loginUm, personObjectExVO);
        } else {
            personObjectExService.removePersonOtherInfo(channelProcessId);
        }

        if (peopleHurtVO.getPersonBenefitVO() != null) {
            boolean saveBenefitFlag = false;
            if (ListUtils.isNotEmpty(subCaseList)) {
                for (String subClass : subCaseList) {
                    if(StringUtils.isNotEmpty(subClass)) {
                        if (InsuredApplyTypeEnum.HOSPITAL_ALLOWANCE.getType().contains(subClass)
                                || InsuredApplyTypeEnum.EMERGENCY_MEDICINE_ALLOWANCE.getType().contains(subClass)) {
                            saveBenefitFlag = true;
                            break;
                        }
                    }
                }
            }
            if (!saveBenefitFlag) {
                peopleHurtVO.getPersonBenefitVO().setPersonBenefitList(null);
            }
            savePersonBenefit(peopleHurtVO.getPersonBenefitVO(), reportNo, caseTimes, channelProcessId, loginUm, taskId, status);
        } else {
            personBenefitService.removePersonBenefit(reportNo, caseTimes, taskId, channelProcessId);
        }
        if (peopleHurtVO.getPersonOtherLossVO() != null) {
            savePersonOtherLoss(peopleHurtVO.getPersonOtherLossVO(), reportNo, caseTimes, channelProcessId, loginUm, taskId, status);
        } else {

        }
    }


    private void checkTime(Date date, String type, String reportNo, String caseTimes, String idAhcsChannelProcess) throws GlobalBusinessException {
        if (date != null) {
            if (new Date().compareTo(date) < 0 && type.equals("死亡")) {
                throw new GlobalBusinessException("死亡时间不得超过当前时间");
            }

            if (new Date().compareTo(date) < 0) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "当前时间必须大于等于" + type + "时间");
            }

            if ("死亡".equals(type)) {
                return;
            }

            String taskId = "report1";

            PersonAccidentDTO personAccidentDTO = null;

            personAccidentDTO = personAccidentDao.getPersonAccidentInfo(reportNo, caseTimes, taskId, ChecklossConst.STATUS_TMP_SUBMIT);

            if (personAccidentDTO == null) {
                return;
            } else {
                Date accidentTime = personAccidentDTO.getAccidentTime();
                if (accidentTime == null) {
                    throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "accidentTime");
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

                    try {
                        date = sdf.parse(sdf.format(date));
                        accidentTime = sdf.parse(sdf.format(accidentTime));
                    } catch (ParseException e) {
                        throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "时间转换异常");
                    }

                    if (date.compareTo(accidentTime) < 0) {
                        throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, type + "时间必须大于等于出险时间");
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyNoPeopleHurtVO(String reportNo, int caseTimes, String loginUm, NoPeopleHurtVO noPeopleHurtVO) {
        /*String channelId = noPeopleHurtVO.getIdAhcsChannelProcess();
        String taskCode = noPeopleHurtVO.getTaskId();
        String status = noPeopleHurtVO.getStatus();*/
        PetInjureDTO petInjures = petInjureMapper.getPetInjures(reportNo, caseTimes);
        if (petInjures != null){
            petInjureMapper.delPetInjures(reportNo, caseTimes);
            petInjureExMapper.delPetInjureExById(petInjures.getIdClmsPetInjure());
            petTreatmentDetailMapper.delPetTreatmentDetailById(petInjures.getIdClmsPetInjure());
            clmsPetLossMapper.delClmsPetLoss(reportNo, caseTimes);
        }
        clmsCargoInfoService.deleteByCondition(reportNo, String.valueOf(caseTimes));
        clmsItemLossService.deleteByCondition(reportNo, caseTimes);
        clmsAllowanceInfoService.deleteByCondition(reportNo, caseTimes);
        clmsCashLossInfoService.deleteByCondition(reportNo, caseTimes);
        clmsRescueInfoService.deleteByCondition(reportNo, caseTimes);
        clmsSubstanceLossInfoService.deleteByCondition(reportNo, caseTimes);
        propLossService.deleteByCondition(reportNo, caseTimes);
        clmsTravelDelayInfoService.deleteByCondition(reportNo, caseTimes);
        clmsPersonalInjuryDeathInfoService.deleteByCondition(reportNo, caseTimes, noPeopleHurtVO.getTaskId());
        clmsPropertyLossInfoService.deleteByCondition(reportNo, caseTimes);
        clmsLegalLiabilityClassInfoService.deleteByCondition(reportNo, caseTimes);

        //给付津贴
        List<ClmsAllowanceInfoDTO> clmsAllowanceInfoDTO = noPeopleHurtVO.getClmsAllowanceInfoDTO();
        if (!CollectionUtils.isEmpty(clmsAllowanceInfoDTO)) {
            ClmsAllowanceInfo clmsAllowanceInfo = ClmsAllowanceInfo.builder().build();
            clmsAllowanceInfoDTO.forEach(allowanceInfoDTO -> {
                BeanUtils.copyProperties(allowanceInfoDTO, clmsAllowanceInfo);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String startTimeStr = Optional.ofNullable(allowanceInfoDTO.getStartTime())
                        .map(sdf::format).orElse("");
                String endTimeStr = Optional.ofNullable(allowanceInfoDTO.getEndTime())
                        .map(sdf::format).orElse("");
                Integer days = allowanceInfoDTO.getDays();
                String timeRange = startTimeStr + "-" + endTimeStr + ",day:" + days;
                clmsAllowanceInfo.setDatePeriod(timeRange);
                clmsAllowanceInfoService.insert(clmsAllowanceInfo);
            });
        }

        //现金损失信息
        List<ClmsCashLossInfoDTO> clmsCashLossInfoDTO = noPeopleHurtVO.getClmsCashLossInfoDTO();
        if (!CollectionUtils.isEmpty(clmsCashLossInfoDTO)) {
            ClmsCashLossInfo clmsCashLossInfo = ClmsCashLossInfo.builder().build();
            clmsCashLossInfoDTO.forEach(cashLossInfoDTO -> {
                BeanUtils.copyProperties(cashLossInfoDTO, clmsCashLossInfo);
                clmsCashLossInfoService.insert(clmsCashLossInfo);
            });
        }

        //救援信息
        List<ClmsRescueInfoDTO> clmsRescueInfoDTO = noPeopleHurtVO.getClmsRescueInfoDTO();
        if (!CollectionUtils.isEmpty(clmsRescueInfoDTO)) {
            ClmsRescueInfo clmsRescueInfo = ClmsRescueInfo.builder().build();
            clmsRescueInfoDTO.forEach(rescueInfoDTO -> {
            BeanUtils.copyProperties(rescueInfoDTO, clmsRescueInfo);
            clmsRescueInfoService.insert(clmsRescueInfo);
            });
        }

        //物质损失信息
        /*List<ClmsSubstanceLossInfoDTO> substanceLossInfoDTOList = noPeopleHurtVO.getSubstanceLossInfoDTOList();
        if (!CollectionUtils.isEmpty(substanceLossInfoDTOList)) {
            List<ClmsSubstanceLossInfo> substanceLossInfos =  new ArrayList<>();
            for (ClmsSubstanceLossInfoDTO substanceLossInfoDTO : substanceLossInfoDTOList) {
                ClmsSubstanceLossInfo clmsSubstanceLossInfo = ClmsSubstanceLossInfo.builder().build();
                BeanUtils.copyProperties(substanceLossInfoDTO, clmsSubstanceLossInfo);
                clmsSubstanceLossInfo.setLossReason(substanceLossInfoDTO.getLossName());
                clmsSubstanceLossInfo.setLossName(substanceLossInfoDTO.getLossName());
                substanceLossInfos.add(clmsSubstanceLossInfo);
            }
            clmsSubstanceLossInfoService.insertBatch(substanceLossInfos);
        }*/

        //企业财产损失信息
        /*PropLossDTO propLossDTO = noPeopleHurtVO.getPropLossDTO();
        if (null != propLossDTO && !CollectionUtils.isEmpty(propLossDTO.getPropDetailLossDTOList())) {
            propLossService.insert(propLossDTO);
        }*/

        //货运、企财、家财、工程、保证险信息
        List<ClmsItemLoss> clmsItemLossListVO = noPeopleHurtVO.getClmsItemLossVO();
        if (!CollectionUtils.isEmpty(clmsItemLossListVO)){
            clmsItemLossListVO.forEach(clmsItemLoss ->{
                clmsItemLoss.setCreatedBy(WebServletContext.getUserId());
                clmsItemLoss.setUpdatedBy(WebServletContext.getUserId());
                clmsItemLossService.saveClmsItemLoss(clmsItemLoss);
            });
        }

        //货运险信息
        ClmsCargoVO clmsCargoVO = noPeopleHurtVO.getClmsCargoVO();
        if (clmsCargoVO != null){
            clmsCargoInfoService.insert(clmsCargoVO);
        }

        //延误信息
        ClmsTravelDelayInfoDTO travelDelayInfoDTO = noPeopleHurtVO.getClmsTravelDelayInfoDTO();
        if (Objects.nonNull(travelDelayInfoDTO)) {
            ClmsTravelDelayInfo clmsTravelDelayInfo = ClmsTravelDelayInfo.builder().build();
            BeanUtils.copyProperties(travelDelayInfoDTO, clmsTravelDelayInfo);
            //出发地和目的地 前端传入为list<String>,后端保存为一个字段，需将list拼接为字符串
            setPlaceVaue(travelDelayInfoDTO, clmsTravelDelayInfo);
            clmsTravelDelayInfoService.insert(clmsTravelDelayInfo);
        }
        
        boolean employerInsurance = riskPropertyService.isEmployerInsurance(reportNo, null);
        if(employerInsurance) {
           	riskPropertyService.deleteCaseRiskProperty(reportNo, caseTimes, noPeopleHurtVO.getTaskId());
        }

        //人身伤亡信息
        ClmsPersonalInjuryDeathInfoDTO clmsPersonalInjuryDeathInfoDTO = noPeopleHurtVO.getClmsPersonalInjuryDeathInfoDTO();
        if (clmsPersonalInjuryDeathInfoDTO != null) {
            ClmsPersonalInjuryDeathInfo clmsPersonalInjuryDeathInfo = ClmsPersonalInjuryDeathInfo.builder().build();
                BeanUtils.copyProperties(clmsPersonalInjuryDeathInfoDTO, clmsPersonalInjuryDeathInfo);
                clmsPersonalInjuryDeathInfo.setClientNo(getClientNo(clmsPersonalInjuryDeathInfoDTO));
                clmsPersonalInjuryDeathInfo.setTaskId(noPeopleHurtVO.getTaskId());
                clmsPersonalInjuryDeathInfoService.insert(clmsPersonalInjuryDeathInfo);
            
			if (employerInsurance) {
				CaseRiskPropertyDTO reportCaseRiskProperty = new CaseRiskPropertyDTO();
				reportCaseRiskProperty.setIdCaseRiskProperty(UuidUtil.getUUID());
				reportCaseRiskProperty.setReportNo(reportNo);
				reportCaseRiskProperty.setCaseTimes(caseTimes);
				reportCaseRiskProperty.setPolicyNo(Optional.ofNullable(clmsPersonalInjuryDeathInfo.getPolicyNo()).orElse(""));
				reportCaseRiskProperty.setRiskGroupNo(clmsPersonalInjuryDeathInfo.getRiskGroupNo());
				reportCaseRiskProperty.setTaskId(clmsPersonalInjuryDeathInfo.getTaskId());
				reportCaseRiskProperty.setCertificateType(clmsPersonalInjuryDeathInfo.getInjuredCertificateType());
				reportCaseRiskProperty.setCertificateNo(clmsPersonalInjuryDeathInfo.getInjuredCertificateNo());
				reportCaseRiskProperty.setName(clmsPersonalInjuryDeathInfo.getInjuredName());
				reportCaseRiskProperty.setAge(clmsPersonalInjuryDeathInfo.getInjuredAge());
				reportCaseRiskProperty.setSex(clmsPersonalInjuryDeathInfo.getInjuredGender());
				reportCaseRiskProperty.setBirthDay(clmsPersonalInjuryDeathInfo.getInjuredBirthday());
				reportCaseRiskProperty.setCreatedBy(loginUm);
				reportCaseRiskProperty.setUpdatedBy(loginUm);
				riskPropertyService.saveCaseRiskProperty(reportCaseRiskProperty);
			}               
        }

        //第三者财产损失信息
        List<ClmsPropertyLossInfoDTO> clmsPropertyLossInfoDTO = noPeopleHurtVO.getClmsPropertyLossInfoDTO();
        if (!CollectionUtils.isEmpty(clmsPropertyLossInfoDTO)) {
            ClmsPropertyLossInfo clmsPropertyLossInfo = ClmsPropertyLossInfo.builder().build();
            clmsPropertyLossInfoDTO.forEach(clmsPropertyLossInfoDTO1 -> {
                BeanUtils.copyProperties(clmsPropertyLossInfoDTO1, clmsPropertyLossInfo);
                clmsPropertyLossInfo.setPropertyLossCause(clmsPropertyLossInfoDTO1.getPropertyLossProject());
                clmsPropertyLossInfoService.insert(clmsPropertyLossInfo);
            });
        }

        //法律责任及其他信息
        List<ClmsLegalLiabilityClassInfoDTO> clmsLegalLiabilityClassInfoDTO = noPeopleHurtVO.getClmsLegalLiabilityClassInfoDTO();
        if (!CollectionUtils.isEmpty(clmsLegalLiabilityClassInfoDTO)) {
            ClmsLegalLiabilityClassInfo clmsLegalLiabilityClassInfo = ClmsLegalLiabilityClassInfo.builder().build();
            clmsLegalLiabilityClassInfoDTO.forEach(clmsLegalLiabilityClassInfoDTO1 -> {
                BeanUtils.copyProperties(clmsLegalLiabilityClassInfoDTO1, clmsLegalLiabilityClassInfo);
                clmsLegalLiabilityClassInfoService.insert(clmsLegalLiabilityClassInfo);
            });
        }

        String channelId = noPeopleHurtVO.getIdAhcsChannelProcess();
        String taskCode = noPeopleHurtVO.getTaskId();
        String status = noPeopleHurtVO.getStatus();
        PetInjureVO petInjureVO = noPeopleHurtVO.getPetInjureVO();
        if(petInjureVO != null){
            petInjureVO.setTaskCode(taskCode);
            petInjureVO.setReportNo(reportNo);
            petInjureVO.setCaseTimes(caseTimes);
            petInjureVO.setUserId(loginUm);
            petInjureVO.setStatus(status);
            petInjureService.addPetInjure(petInjureVO);
        }

        FlightDelayVO flightDelayVO = noPeopleHurtVO.getFlightDelayVO();
        BaggageDelayVO baggageDelayVO = noPeopleHurtVO.getBaggageDelayVO();
        VehiclDelayOtherVO vehiclDelayOtherVO = noPeopleHurtVO.getVehiclDelayOtherVO();
        ExaminFailVO examinFailVO = noPeopleHurtVO.getExaminFailVO();
        TravelAlertVO travelAlertVO = noPeopleHurtVO.getTravelAlertVO();
        PropertyLossVO propertyLossVO = noPeopleHurtVO.getPropertyLossVO();
        List<OtherLossPptDTO> otherLossPptDTOs = noPeopleHurtVO.getOtherLossPptDTOs();
        List<OtherLossVO> otherLossVO = noPeopleHurtVO.getOtherLossVO();

        if (flightDelayVO != null) {
            flightDelayVO.setStatus(status);
            this.saveFlightDelay(flightDelayVO, reportNo, caseTimes, channelId, loginUm, taskCode, status);
        }

        if (baggageDelayVO != null) {
            baggageDelayVO.setStatus(status);
            this.saveBaggageDelay(baggageDelayVO, reportNo, caseTimes, channelId, loginUm, taskCode, status);
        } else {
            BaggageDelayDTO baggageDelayDTO = new BaggageDelayDTO();
            baggageDelayDTO.setReportNo(reportNo);
            baggageDelayDTO.setCaseTimes(caseTimes);
            baggageDelayDTO.setTaskCode(taskCode);
            baggageDelayDTO.setUpdatedBy(loginUm);
            baggageDelayDTO.setIdAhcsChannelProcess(channelId);
            baggageDelayService.updateEffective(baggageDelayDTO);
        }

        if (vehiclDelayOtherVO != null) {
            vehiclDelayOtherVO.setStatus(status);
            this.saveVehiclDelay(vehiclDelayOtherVO, reportNo, caseTimes, channelId, loginUm, taskCode, status);
        } else {
            VehiclDelayOtherDTO vehiclDelayOtherDTO = new VehiclDelayOtherDTO();
            vehiclDelayOtherDTO.setReportNo(reportNo);
            vehiclDelayOtherDTO.setCaseTimes(caseTimes);
            vehiclDelayOtherDTO.setTaskCode(taskCode);
            vehiclDelayOtherDTO.setIdAhcsChannelProcess(channelId);
            vehiclDelayOtherDTO.setUpdatedBy(loginUm);
            vehiclDelayOtherService.updateEffective(vehiclDelayOtherDTO);

        }

        if (examinFailVO != null) {
            examinFailVO.setStatus(status);
            this.saveExaminFailInfo(examinFailVO, reportNo, caseTimes, channelId, loginUm, taskCode, status);
        } else {
            examinFailService.removeExaminFail(reportNo, caseTimes, taskCode, channelId);
        }

        if (travelAlertVO != null) {
            travelAlertVO.setStatus(status);
            this.saveTravelAlterInfo(travelAlertVO, reportNo, caseTimes, channelId, loginUm, taskCode, status);
        } else {
            travelAlertService.removeTravelAlert(reportNo, caseTimes, taskCode, channelId);
        }

        if (propertyLossVO != null) {
            propertyLossVO.setStatus(status);
            this.savePropertyLossInfo(propertyLossVO, reportNo, caseTimes, channelId, loginUm, taskCode, status);
        } else {
            PropertyLossDTO propertyLossDTO = new PropertyLossDTO();
            propertyLossDTO.setReportNo(reportNo);
            propertyLossDTO.setCaseTimes(caseTimes);
            propertyLossDTO.setTaskCode(taskCode);
            propertyLossDTO.setIdAhcsChannelProcess(channelId);
            propertyLossDTO.setUpdatedBy(loginUm);
            propertyLossService.updateEffective(propertyLossDTO);
        }

        if (!CollectionUtils.isEmpty(otherLossVO)) {
            List<OtherLossDTO> otherLoss = otherLossMapper.getOtherLoss(reportNo, caseTimes, otherLossVO.get(0).getTaskCode(), otherLossVO.get(0).getIdAhcsChannelProcess());
            if (!CollectionUtils.isEmpty(otherLoss)) {
                otherLoss.forEach(existOtherLossDTO -> {
                    otherLossService.updateEffective(existOtherLossDTO);
                });
            }
            otherLossVO.forEach(otherLossVO1 -> {
                BeanUtils.copyProperties(otherLossVO1, otherLossVO);
                this.saveOtherLossDTOInfo(otherLossVO1, reportNo, caseTimes, channelId, loginUm, taskCode, status);
            });
        } else {
            OtherLossDTO otherLossDTO = new OtherLossDTO();
            otherLossDTO.setReportNo(reportNo);
            otherLossDTO.setCaseTimes(caseTimes);
            otherLossDTO.setTaskCode(taskCode);
            otherLossDTO.setIdAhcsChannelProcess(channelId);
            otherLossDTO.setUpdatedBy(loginUm);
            otherLossService.updateEffective(otherLossDTO);
        }

        otherLossPptService.removeOtherLossPptList(reportNo, caseTimes, taskCode);
        if (ListUtils.isNotEmpty(otherLossPptDTOs)) {
            this.saveOtherLossPpt(otherLossPptDTOs, reportNo, caseTimes, channelId, loginUm, taskCode, status);
        }

    }



    @Override
    public void saveVerifyConclusion(String loginUm, VerifyConclusionDTO verifyConclusionDTO) throws GlobalBusinessException {
        verifyConclusionDTO.setCreatedBy(loginUm);
        verifyConclusionDTO.setUpdatedBy(loginUm);

        verifyConclusiondao.updateEffective(verifyConclusionDTO);

        verifyConclusiondao.saveDuty(verifyConclusionDTO);
    }


    @Override
    public void savePersonAccident(String reportNo, int caseTimes, String loginUm, PersonAccidentDTO personAccidentDTO) throws GlobalBusinessException {
        personAccidentDao.removePersonAccident(personAccidentDTO.getReportNo(), caseTimes);
        personAccidentDTO.setReportNo(reportNo);
        personAccidentDTO.setCaseTimes(caseTimes);
        personAccidentDTO.setCreatedBy(loginUm);
        personAccidentDTO.setUpdatedBy(loginUm);
        personAccidentDao.savePersonAccident(personAccidentDTO);

    }

    @Override
    public void saveClassCase(String reportNo, int caseTimes, String loginUm, CaseClassDTO caseClassDTO) throws GlobalBusinessException {
        caseClassDao.removeCaseClass(caseClassDTO.getReportNo(), caseTimes,caseClassDTO.getTaskId(),caseClassDTO.getCaseSubClass());
        caseClassDTO.setReportNo(reportNo);
        caseClassDTO.setCaseTimes(caseTimes);
        caseClassDTO.setUpdatedBy(loginUm);
        caseClassDao.saveCaseClass(caseClassDTO);

    }

    @Override
    public void savePersonDisease(String reportNo, int caseTimes, String loginUm, PersonDiseaseDTO personDiseaseDTO) throws GlobalBusinessException {
        personDiseaseDTO.setReportNo(reportNo);
        personDiseaseDTO.setCaseTimes(caseTimes);
        personDiseaseDTO.setCreatedBy(loginUm);
        personDiseaseDTO.setUpdatedBy(loginUm);

        personDiseaseDao.savePersonDisease(personDiseaseDTO);
    }

    @Override
    public void savePersonDiagnose(String reportNo, int caseTimes, String loginUm, PersonDiagnoseDTO personDiagnoseDTO) throws GlobalBusinessException {
        personDiagnoseDTO.setReportNo(reportNo);
        personDiagnoseDTO.setCaseTimes(caseTimes);
        personDiagnoseDTO.setCreatedBy(loginUm);
        personDiagnoseDTO.setUpdatedBy(loginUm);
        if (!ConstValues.YES.equals(personDiagnoseDTO.getIsSurgical())) {
            personDiagnoseDTO.setSurgicalType("");
            personDiagnoseDTO.setSurgicalName("");
        } else {
            String surgicalCode = personDiagnoseDTO.getSurgicalCode();
            String surgicalName = personDiagnoseDTO.getSurgicalName();
            if (StringUtils.isNotEmpty(surgicalCode) && StringUtils.isEmptyStr(surgicalName)) {
                //根据报案号查询机构编码
                String orgTypeByReportNo = hospitalInfoService.getOrgTypeByReportNo(reportNo, NcbsConstant.OPERATION);
                String operationName = operationDefineMapper.getTherapyOperationByCode(surgicalCode, orgTypeByReportNo);
                personDiagnoseDTO.setSurgicalName(operationName);
            }
        }
        personDiagnoseDao.savePersonDiagnose(personDiagnoseDTO);
    }

    @Override
    public void savePersonDisability(String reportNo, int caseTimes, String loginUm, PersonDisabilityDTO personDisabilityDTO) throws GlobalBusinessException {
        personDisabilityDTO.setReportNo(reportNo);
        personDisabilityDTO.setCaseTimes(caseTimes);
        personDisabilityDTO.setCreatedBy(loginUm);
        personDisabilityDTO.setUpdatedBy(loginUm);
        personDisabilityDao.savePersonDisability(personDisabilityDTO);
    }

    @Override
    public void savePersonDeath(String reportNo, int caseTimes, String loginUm, PersonDeathDTO personDeathDTO) throws GlobalBusinessException {
        personDeathDTO.setReportNo(reportNo);
        personDeathDTO.setCaseTimes(caseTimes);
        personDeathDTO.setCreatedBy(loginUm);
        personDeathDTO.setUpdatedBy(loginUm);
        personDeathDao.savePersonDeath(personDeathDTO);
    }

    private void saveBigDisease(String reportNo, int caseTimes, String loginUm, BigDiseaseDTO bigDiseaseDTO, List<String> diseaseDetailCodes) throws GlobalBusinessException {
        String bigDiseaseId = this.addBigDisease(reportNo, caseTimes, loginUm, bigDiseaseDTO);

        if (ListUtils.isNotEmpty(diseaseDetailCodes)) {
            for (String diseaseDetailCode : diseaseDetailCodes) {
                BigDiseaseDetailDTO bigDiseaseDetailDTO = new BigDiseaseDetailDTO(bigDiseaseId, diseaseDetailCode);
                bigDiseaseDetailDTO.setCreatedBy(loginUm);
                bigDiseaseDetailDTO.setUpdatedBy(loginUm);
                bigDiseaseDetailDTO.setStatus(bigDiseaseDTO.getStatus());
                bigDiseaseDetailDao.saveBigDiseaseDetail(bigDiseaseDetailDTO);
            }
        }

    }

    @Override
    public String addBigDisease(String reportNo, int caseTimes, String loginUm, BigDiseaseDTO bigDiseaseDTO) throws GlobalBusinessException {
        String bigDiseaseId = UuidUtil.getUUID();
        String bigDiseaseCode = bigDiseaseDTO.getBigDiseaseCode();
        if (!ChecklossConst.BIG_DISEASE_CODE_ONE.equals(bigDiseaseCode) || !ChecklossConst.BIG_DISEASE_CODE_TWO.equals(bigDiseaseCode)) {
            bigDiseaseDTO.setPrimarySite("");
            bigDiseaseDTO.setPrimarySiteDesc("");
            bigDiseaseDTO.setPrimarySiteUnknown("");
        }
        bigDiseaseDTO.setBigDiseaseId(bigDiseaseId);
        bigDiseaseDTO.setReportNo(reportNo);
        bigDiseaseDTO.setCaseTimes(caseTimes);
        bigDiseaseDTO.setCreatedBy(loginUm);
        bigDiseaseDTO.setUpdatedBy(loginUm);
        bigDiseaseDao.saveBigDisease(bigDiseaseDTO);

        return bigDiseaseId;
    }

    private void saveDisabilityAppraisal(String reportNo, int caseTimes, String loginUm, DisabilityAppraisalVO disabilityAppraisalVO) {
        String idAhcsChannelProcess = disabilityAppraisalVO.getIdAhcsChannelProcess();
        String isAppraisal = disabilityAppraisalVO.getIsAppraisal();
        String status = disabilityAppraisalVO.getStatus();
        DisabilityAppraisalDTO disabilityAppraisalDTO = disabilityAppraisalService.getDisabilityAppraisalByIdAhcsChannelProcess(idAhcsChannelProcess);
        if (disabilityAppraisalDTO != null) {
            disabilityAppraisalDTO.setIsAppraisal(isAppraisal);
            disabilityAppraisalDTO.setStatus(status);
            disabilityAppraisalDTO.setUpdatedBy(loginUm);
            disabilityAppraisalService.modifyDisabilityAppraisal(disabilityAppraisalDTO);
        } else {
            disabilityAppraisalDTO = new DisabilityAppraisalDTO();
            disabilityAppraisalDTO.setCreatedBy(loginUm);
            disabilityAppraisalDTO.setUpdatedBy(loginUm);
            disabilityAppraisalDTO.setIdAhcsDisabilityAppraisal(UuidUtil.getUUID());
            disabilityAppraisalDTO.setReportNo(reportNo);
            disabilityAppraisalDTO.setCaseTimes(caseTimes);
            disabilityAppraisalDTO.setIsAppraisal(isAppraisal);
            disabilityAppraisalDTO.setIdAhcsChannelProcess(idAhcsChannelProcess);
            disabilityAppraisalDTO.setStatus(status);
            disabilityAppraisalService.addDisabilityAppraisal(disabilityAppraisalDTO);
        }
    }

    private void savePersonHospital(String reportNo, int caseTimes, String loginUm, PersonHospitalVO personHospitalVO) {
        List<PersonHospitalDTO> hospitals = personHospitalVO.getPersonHospitalList();
        String idAhcsChannelProcess = personHospitalVO.getIdAhcsChannelProcess();
        String taskId = personHospitalVO.getTaskId();
        String status = personHospitalVO.getStatus();
        for (PersonHospitalDTO hospital : hospitals) {
            if (!StringUtils.isEmptyStr(hospital.getTherapyType()) && ("MS_2201".equals(hospital.getMedicalStatus()) || "MS_2204".equals(hospital.getMedicalStatus()))) {
                hospital.setHospitalName("");
                hospital.setHospitalCode(null);
                hospital.setHospitalGrade(null);
                hospital.setTherapyType(null);
                hospital.setHospitalPropertyDes(null);
                hospital.setIsCooperation(null);
                hospital.setIsAppointedHospital(null);
                hospital.setIsSocialInsurance(null);
            }
            HospitalInfoVO hospitalCodeByName = new HospitalInfoVO();
            if (!StringUtils.isEmptyStr(hospital.getHospitalName()) && StringUtils.isEmptyStr(hospital.getHospitalCode())) {
                //根据报案号查询机构编码
                //根据机构编码确认机构是全国、北京(211)还是上海(231)
                String orgType = hospitalInfoService.getOrgTypeByReportNo(reportNo, NcbsConstant.HOSPITAL);
                hospitalCodeByName = hospitalInfoDao.getHospitalCodeByName(hospital.getHospitalName(), orgType);
                hospital.setHospitalCode(hospitalCodeByName != null ? hospitalCodeByName.getHospitalCode() : "");
            }
            hospital.setCreatedBy(loginUm);
            hospital.setUpdatedBy(loginUm);
            hospital.setIdAhcsPersonHospital(UuidUtil.getUUID());
            hospital.setReportNo(reportNo);
            hospital.setCaseTimes(caseTimes);
            hospital.setIdAhcsChannelProcess(idAhcsChannelProcess);
            hospital.setTaskId(taskId);
            hospital.setStatus(status);
        }
        personHospitalService.removePersonHospitalByIdAhcsChannelProcess(idAhcsChannelProcess, taskId);
        personHospitalService.addPersonHospitalList(hospitals);
    }

    @Override
    public void saveSurvey(String reportNo, int caseTimes, String loginUm, SurveyDTO surveyDTO) {
        SurveyDTO survey = surveyMapper.getSurvey(surveyDTO);
        surveyDTO.setCreatedBy(loginUm);
        surveyDTO.setUpdatedBy(loginUm);
        surveyDTO.setIdAhcsSurvey(UuidUtil.getUUID());
        surveyDTO.setSurveyUm(loginUm);
        if (survey != null) {
            surveyMapper.modifySurvey(surveyDTO);
        } else {
            surveyMapper.addSurvey(surveyDTO);
        }
    }

    @Override
    public void saveWholeCaseBase(String reportNo, int caseTimes, String indemnityConclusion, String indemnityModel, String loginUm) throws GlobalBusinessException {
        WholeCaseBaseDTO wholeCaseBaseDTO = new WholeCaseBaseDTO();
        wholeCaseBaseDTO.setReportNo(reportNo);
        wholeCaseBaseDTO.setCaseTimes(caseTimes);
        wholeCaseBaseDTO.setIndemnityConclusion(indemnityConclusion);
        if (StringUtils.isEmptyStr(indemnityModel)) {
            wholeCaseBaseDTO.setIndemnityModel("");
        } else {
            wholeCaseBaseDTO.setIndemnityModel(indemnityModel);
        }
        wholeCaseBaseDTO.setVerifyUm(loginUm);
        wholeCaseBaseDTO.setUpdatedBy(loginUm);
        LogUtil.info("报案号保存收单赔付结论入参报案号={},详细入参={}",reportNo,JsonUtils.toJsonString(wholeCaseBaseDTO));
        wholeCaseBaseService.modifyWholeCaseBase(wholeCaseBaseDTO);
    }

    @Override
    public PeopleHurtVO getPeopleHurtVO(String reportNo, int caseTimes, String channelProcessId, String taskId) throws GlobalBusinessException {
        PeopleHurtVO peopleHurtVO = new PeopleHurtVO();
        //意外信息
        peopleHurtVO.setAccidentVO(getPersonAccidentVO(channelProcessId, taskId, reportNo));
        //疾病信息
        peopleHurtVO.setDiseaseVO(getPersonDiseaseVO(channelProcessId, taskId));
        //诊断信息
        peopleHurtVO.setDiagnoseVO(getPersonDiagnoseVO(reportNo, channelProcessId, taskId));
        //残疾
        peopleHurtVO.setDisabilityVO(getPersonDisabilityVO(channelProcessId, taskId));
        //死亡信息
        peopleHurtVO.setDeathVO(getPersonDeathVO(channelProcessId, taskId));
        //重大疾病
        BigDiseaseVO bigDiseaseVO = getBigDiseaseVO(channelProcessId, taskId);
        peopleHurtVO.setBigDiseaseVO(bigDiseaseVO);

        if (null != bigDiseaseVO) {
            //重大疾病详细编码
            peopleHurtVO.setBigDiseaseDetailCode(getBigDiseaseDetailList(bigDiseaseVO.getBigDiseaseId()));
        }
        //伤残鉴定
        peopleHurtVO.setDisabilityAppraisalVO(getDisabilityAppraisalVO(channelProcessId));
        //医院信息
        peopleHurtVO.setHospitalVO(getPersonHospitalVO(reportNo, channelProcessId, taskId));
        //人员扩展信息表
        peopleHurtVO.setObjectExVO(getPersonObjectExVO(channelProcessId));
        //津贴信息表
        peopleHurtVO.setPersonBenefitVO(this.getPersonBenefit(reportNo, caseTimes, taskId, channelProcessId));
        //其他人伤信息
        peopleHurtVO.setPersonOtherLossVO(this.getPersonOtherLoss(reportNo, caseTimes, taskId, channelProcessId));

        return peopleHurtVO;
    }

    @Override
    public NoPeopleHurtVO getNoPeopleHurtVO(String reportNo, int caseTimes, String taskId, String channelProcessId) throws GlobalBusinessException {
        NoPeopleHurtVO noPeopleHurtVO = new NoPeopleHurtVO();
        List<PropertyLossReasonDTO> propertyLossReasonDTOList = commonService.getPropertyLossReason();
        //给付津贴
        List<ClmsAllowanceInfo> clmsAllowanceInfos = clmsAllowanceInfoService.queryByReportNo(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(clmsAllowanceInfos)) {
            List<ClmsAllowanceInfoDTO> allowanceInfoDTOs = new ArrayList<>();
            clmsAllowanceInfos.forEach(clmsAllowanceInfo -> {
                ClmsAllowanceInfoDTO allowanceInfoDTO = ClmsAllowanceInfoDTO.builder().build();
                BeanUtils.copyProperties(clmsAllowanceInfo, allowanceInfoDTO);
                allowanceInfoDTOs.add(allowanceInfoDTO);
            });
            noPeopleHurtVO.setClmsAllowanceInfoDTO(allowanceInfoDTOs);
        }
        //现金损失信息
        List<ClmsCashLossInfo> clmsCashLossInfos = clmsCashLossInfoService.queryByReportNo(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(clmsCashLossInfos)) {
            List<ClmsCashLossInfoDTO> cashLossInfoDTOs = new ArrayList<>();
            clmsCashLossInfos.forEach(cashLossInfo -> {
                ClmsCashLossInfoDTO cashLossInfoDTO = ClmsCashLossInfoDTO.builder().build();
                BeanUtils.copyProperties(cashLossInfo, cashLossInfoDTO);
                cashLossInfoDTOs.add(cashLossInfoDTO);
            });
            noPeopleHurtVO.setClmsCashLossInfoDTO(cashLossInfoDTOs);
        }
        //救援信息
        List<ClmsRescueInfo> clmsRescueInfos = clmsRescueInfoService.queryByReportNo(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(clmsRescueInfos)) {
            List<ClmsRescueInfoDTO> rescueInfoDTOs = new ArrayList<>();
            clmsRescueInfos.forEach(rescueInfo -> {
                ClmsRescueInfoDTO rescueInfoDTO = ClmsRescueInfoDTO.builder().build();
                BeanUtils.copyProperties(rescueInfo, rescueInfoDTO);
                rescueInfoDTOs.add(rescueInfoDTO);
            });
            noPeopleHurtVO.setClmsRescueInfoDTO(rescueInfoDTOs);
        }
        //物质损失信息 更改成list集合
        /*List<ClmsSubstanceLossInfo> substanceLossInfoList = clmsSubstanceLossInfoService.queryByReportNo(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(substanceLossInfoList)) {
            List<ClmsSubstanceLossInfoDTO> substanceLossInfoDTOList = new ArrayList<>();
            for (ClmsSubstanceLossInfo substanceLossInfo : substanceLossInfoList) {
                ClmsSubstanceLossInfoDTO substanceLossInfoDTO = ClmsSubstanceLossInfoDTO.builder().build();
                BeanUtils.copyProperties(substanceLossInfo, substanceLossInfoDTO);

                String oneClass = getPropertyLossReasonDTO(propertyLossReasonDTOList, substanceLossInfo.getLossReason());
                substanceLossInfoDTO.setPropertyLossCauseOneClass(oneClass);
                substanceLossInfoDTO.setPropertyLossCauseTwoClass(substanceLossInfo.getLossReason());

                substanceLossInfoDTOList.add(substanceLossInfoDTO);
            }
            noPeopleHurtVO.setSubstanceLossInfoDTOList(substanceLossInfoDTOList);
        }*/

        //企业财产损失信息
        /*PropLossDTO propLossDTO = propLossService.queryByReportNo(reportNo,caseTimes);
        if (Objects.nonNull(propLossDTO)) {
            noPeopleHurtVO.setPropLossDTO(propLossDTO);
        }*/

        //货运、企财、家财、工程、保证险信息
        List<ClmsItemLoss> clmsItemLosses = clmsItemLossService.getClmsItemLossService(reportNo, caseTimes);
        if (clmsItemLosses != null && clmsItemLosses.size() > 0){
            noPeopleHurtVO.setClmsItemLossVO(clmsItemLosses);
        }

        //货运险信息
        ClmsCargoVO clmsCargoVO = clmsCargoInfoService.getClmsCargoInfo(reportNo, String.valueOf(caseTimes));
        if (clmsCargoVO != null) {
            noPeopleHurtVO.setClmsCargoVO(clmsCargoVO);
        }

        //延误信息
        ClmsTravelDelayInfo travelDelayInfo = clmsTravelDelayInfoService.queryByReportNo(reportNo, caseTimes);
        if (Objects.nonNull(travelDelayInfo)) {
            ClmsTravelDelayInfoDTO travelDelayInfoDTO = ClmsTravelDelayInfoDTO.builder().build();
            BeanUtils.copyProperties(travelDelayInfo, travelDelayInfoDTO);
            //出发地点，到达地点存的是省市代码 用逗号分隔的字符串，此处要转换会List<String>类型给前端
            setPlaceData(travelDelayInfo, travelDelayInfoDTO);
            noPeopleHurtVO.setClmsTravelDelayInfoDTO(travelDelayInfoDTO);
        }
        //人身伤亡信息
        ClmsPersonalInjuryDeathInfo clmsPersonalInjuryDeathInfos = clmsPersonalInjuryDeathInfoService.queryByReportNo(reportNo, caseTimes);
        if (clmsPersonalInjuryDeathInfos != null) {
            ClmsPersonalInjuryDeathInfoDTO personalInjuryDeathInfoDTO = ClmsPersonalInjuryDeathInfoDTO.builder().build();
            BeanUtils.copyProperties(clmsPersonalInjuryDeathInfos, personalInjuryDeathInfoDTO);
            noPeopleHurtVO.setClmsPersonalInjuryDeathInfoDTO(personalInjuryDeathInfoDTO);
        }
        //第三者财产损失信息
        List<ClmsPropertyLossInfo> clmsPropertyLossInfos = clmsPropertyLossInfoService.queryByReportNo(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(clmsPropertyLossInfos)) {
            List<ClmsPropertyLossInfoDTO> clmsPropertyLossInfoDTOS = new ArrayList<>();
            for (ClmsPropertyLossInfo clmsPropertyLossInfo : clmsPropertyLossInfos){
                ClmsPropertyLossInfoDTO propertyLossInfoDTO = ClmsPropertyLossInfoDTO.builder().build();
                BeanUtils.copyProperties(clmsPropertyLossInfo, propertyLossInfoDTO);
                String oneClass = getPropertyLossReasonDTO(propertyLossReasonDTOList, clmsPropertyLossInfo.getPropertyLossCause());
                propertyLossInfoDTO.setPropertyLossCauseOneClass(oneClass);
                propertyLossInfoDTO.setPropertyLossCauseTwoClass(clmsPropertyLossInfo.getPropertyLossCause());
                clmsPropertyLossInfoDTOS.add(propertyLossInfoDTO);
                noPeopleHurtVO.setClmsPropertyLossInfoDTO(clmsPropertyLossInfoDTOS);
            }
        }
        //法律责任及其他信息
        List<ClmsLegalLiabilityClassInfo> clmsLegalLiabilityClassInfos = clmsLegalLiabilityClassInfoService.queryByReportNo(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(clmsLegalLiabilityClassInfos)) {
            List<ClmsLegalLiabilityClassInfoDTO> clmsLegalLiabilityClassInfoDTOS = new ArrayList<>();
            clmsLegalLiabilityClassInfos.forEach(clmsLegalLiabilityClassInfo -> {
                ClmsLegalLiabilityClassInfoDTO legalLiabilityClassInfoDTO = ClmsLegalLiabilityClassInfoDTO.builder().build();
                BeanUtils.copyProperties(clmsLegalLiabilityClassInfo, legalLiabilityClassInfoDTO);
                clmsLegalLiabilityClassInfoDTOS.add(legalLiabilityClassInfoDTO);
                noPeopleHurtVO.setClmsLegalLiabilityClassInfoDTO(clmsLegalLiabilityClassInfoDTOS);
            });
        }

        //非人伤本期不做
//        noPeopleHurtVO.setBaggageDelayVO(this.getBaggageDelay(reportNo, caseTimes, taskId, channelProcessId));
//        noPeopleHurtVO.setExaminFailVO(this.getExaminFail(reportNo, caseTimes, taskId, channelProcessId));
//        noPeopleHurtVO.setFlightDelayVO(this.getFlightDelay(reportNo, caseTimes, taskId, channelProcessId));
          noPeopleHurtVO.setOtherLossVO(this.getOtherLoss(reportNo, caseTimes));
//        noPeopleHurtVO.setPropertyLossVO(this.getPropertyLoss(reportNo, caseTimes, taskId, channelProcessId));
//        noPeopleHurtVO.setTravelAlertVO(this.getTravelAlert(reportNo, caseTimes, taskId, channelProcessId));
//        noPeopleHurtVO.setVehiclDelayOtherVO(this.getVehiclDelayOther(reportNo, caseTimes, taskId, channelProcessId));
//        noPeopleHurtVO.setOtherLossPptDTOs(this.getOtherLossPpt(reportNo, caseTimes, taskId));
        noPeopleHurtVO.setPetInjureVO(petInjureService.getPetInjure(new PetInjureVO(reportNo,caseTimes,taskId)));
        return noPeopleHurtVO;
    }


    private String getPropertyLossReasonDTO(List<PropertyLossReasonDTO> list, String twoClass) {
        for (PropertyLossReasonDTO dto : list) {
            for (PropertyLossDetailReasonDTO detailReasonDTO : dto.getLossDetailReasonDTOList()) {
                if (detailReasonDTO.getLossReasonDetTypeCode().equals(twoClass)) {
                    return dto.getLossReasonTypeCode();
                }
            }
        }
        return null;
    }

    @Transactional
    @Override
    public void initSurvey(String reportNo, String userId, String reportType, String lossObjectNo) {
        String taskId = TacheConstants.REPORT + reportType;
        List<String> caseSubClassList;
        int caseTimes = 1;

        ReportAccidentExEntity reportAccidentEx = reportService.getReportAccidentEx(reportNo);
        String medicalStatus = reportAccidentEx.getMedicalStatus();
        String insuredApplyStatus = reportAccidentEx.getInsuredApplyStatus();
        String insuredApplyType = reportAccidentEx.getInsuredApplyType();
        String therapyType = reportAccidentEx.getTherapyType();
        if (ChecklossConst.MEDICAL.equals(insuredApplyStatus) && StringUtils.isEmptyStr(therapyType)) {
            therapyType = ChecklossConst.AHCS_THERAPY_ONE;
        }
        String accidentType = reportAccidentEx.getAccidentType();
        String trafficAccidentType = reportAccidentEx.getTrafficAccidentType();
        String hospitalName = reportAccidentEx.getHospitalName();
        String insuredIdentity = reportAccidentEx.getInsuredIdentity();
        String hospitalCode = "";
        String bigDiseaseCode = reportAccidentEx.getBigDiseaseCode();
        String bigDiseaseName = reportAccidentEx.getBigDiseaseName();
        String diedDate = reportAccidentEx.getDiedCause();
        String injuryMechanism = "";
        BigDecimal hospitalDays = reportAccidentEx.getHospitalDays();

        List<ReportInfoExEntity> reportInfoExs = reportService.getReportInfoEx(reportNo);
        String caseClass = "";
        if (reportInfoExs != null && !reportInfoExs.isEmpty()) {
            LogUtil.audit("#reportNo={}, Array item type={}#", reportNo, reportInfoExs.get(0).getClass());
            ReportInfoExEntity object = reportInfoExs.get(0);
            if (object != null) {
                caseClass = object.getCaseClass();
                LogUtil.audit("#调用结报系统获取报案信息的案件大类#,获取传出参数caseClass=" + caseClass);
            }
        }
        if (!ChecklossConst.CASECLASS_PEOPLE_HURT.equals(caseClass)) {
            ReportAccidentTrafficEntity accidentTrafficEntity = reportService.getReportAccidentTrafficByReportNo(reportNo);
            if (accidentTrafficEntity != null) {
                trafficAccidentType = ChecklossConst.NO_PERSON_TRAFFIC_MAP.get(accidentTrafficEntity.getTransportation());
            }
        }

        ReportAccidentEntity reportAccident = reportService.getReportAccident(reportNo);
        String accidentCityCode = reportAccident.getAccidentCityCode();
        String overseasOccur = reportAccident.getOverseasOccur();
        String accidentArea = reportAccident.getAccidentArea();
        String provinceCode = reportAccident.getProvinceCode();
        String accidentCountyCode = reportAccident.getAccidentCountyCode();
        String accidentPlace = reportAccident.getAccidentPlace();
        String accidentDetail = reportAccident.getAccidentDetail();
        Date accidentDate = reportAccident.getAccidentDate();
        String overseaNationCode = reportAccident.getOverseaNationCode();
        LogUtil.audit("#调用结报系统获取出险地信息#,获取传出参数overseasOccur=" + overseasOccur + ",accidentCityCode=" + accidentCityCode);

        List<WholeCaseBaseEntity> wholeCaseBases = wholeCaseBaseService.getWholeCaseBase(reportNo);
        String isHugeAccident = "";
        String hugeAccidentCode = null;
        // String idHugeAccidentInfo = "";
        // String hugeAccidentType = "";

        if (wholeCaseBases != null) {
            WholeCaseBaseEntity wholeCaseBase = wholeCaseBases.get(0);
            isHugeAccident = wholeCaseBase.getIsHugeAccident();
            hugeAccidentCode = wholeCaseBase.getHugeAccidentCode();
            // idHugeAccidentInfo = wholeCaseBase.getHugeAccidentCode();
            // hugeAccidentType = wholeCaseBase.getHugeAccidentCode();
            LogUtil.audit("#从报案信息中获取重灾信息#,获取传出参数isHugeAccident=%s,hugeAccidentCode=%s", isHugeAccident, hugeAccidentCode);
        }

        String idAhcsChannelProcess = this.getChannelProcessId(reportNo, caseTimes, "", ChecklossConst.CASECLASS_PEOPLE_HURT, userId);

        String status = ChecklossConst.STATUS_TMP_SUBMIT;
        if (StringUtils.isNotEmpty(bigDiseaseName)) {
            BigDiseaseDTO bigDiseaseDTO = new BigDiseaseDTO();
            if (StringUtils.isNotEmpty(bigDiseaseCode)) {
                bigDiseaseDTO.setBigDiseaseCode(bigDiseaseCode);
            } else {
                bigDiseaseDTO.setBigDiseaseOther(bigDiseaseName);
            }
            bigDiseaseDTO.setIdAhcsChannelProcess(idAhcsChannelProcess);
            bigDiseaseDTO.setStatus(status);
            bigDiseaseDTO.setTaskId(taskId);
            this.addBigDisease(reportNo, caseTimes, userId, bigDiseaseDTO);
        }

        if (StringUtils.isNotEmpty(diedDate)) {
            PersonDeathDTO personDeathDTO = new PersonDeathDTO();
            personDeathDTO.setDeathDate(DateUtils.formatStringToDate(diedDate, DateUtils.FULL_DATE_STR));
            personDeathDTO.setIdAhcsChannelProcess(idAhcsChannelProcess);
            personDeathDTO.setTaskId(taskId);
            personDeathDTO.setStatus(status);
            this.savePersonDeath(reportNo, caseTimes, userId, personDeathDTO);
        }
        if (StringUtils.isNotEmpty(medicalStatus) || StringUtils.isNotEmpty(therapyType)) {
            PersonHospitalDTO personHospitalDTO = new PersonHospitalDTO();
            personHospitalDTO.setCreatedBy(userId);
            personHospitalDTO.setUpdatedBy(userId);
            personHospitalDTO.setReportNo(reportNo);
            personHospitalDTO.setCaseTimes(caseTimes);
            personHospitalDTO.setIdAhcsChannelProcess(idAhcsChannelProcess);
            personHospitalDTO.setMedicalStatus(medicalStatus);
            personHospitalDTO.setTaskId(taskId);
            personHospitalDTO.setTherapyType(therapyType);
            personHospitalDTO.setStatus(status);
            personHospitalDTO.setHospitalCode(hospitalCode);
            personHospitalDTO.setHospitalName(hospitalName);
            personHospitalDTO.setHospitalGrade("");
            personHospitalDTO.setIsCooperation("");
            personHospitalDTO.setIsSocialInsurance("");
            personHospitalDTO.setIsAppointedHospital("");
            personHospitalDTO.setHospitalPropertyDes("");
            List<PersonHospitalDTO> personHospitalDTOList = new ArrayList<>();
            personHospitalDTOList.add(personHospitalDTO);
            personHospitalService.addPersonHospitalList(personHospitalDTOList);
        }

        if (StringUtils.isNotEmpty(insuredApplyStatus)) {
            PersonAccidentDTO personAccidentDTO = new PersonAccidentDTO();
            personAccidentDTO.setIdAhcsChannelProcess(idAhcsChannelProcess);
            personAccidentDTO.setTaskId(taskId);
            personAccidentDTO.setOverseasOccur(overseasOccur);
            personAccidentDTO.setAccidentCityCode(accidentCityCode);
            personAccidentDTO.setInsuredApplyStatus(insuredApplyStatus);
            personAccidentDTO.setAccidentType(accidentType);
            personAccidentDTO.setTrafficTool(trafficAccidentType);
            personAccidentDTO.setAccidentArea(accidentArea);
            personAccidentDTO.setProvinceCode(provinceCode);
            personAccidentDTO.setAccidentCountyCode(accidentCountyCode);
            personAccidentDTO.setAccidentPlace(accidentPlace);
            personAccidentDTO.setStatus(status);
            personAccidentDTO.setAccidentDetail(accidentDetail);
            personAccidentDTO.setInjuryMechanism(injuryMechanism);
            // personAccidentDTO.setHugeAccidentType(hugeAccidentType);
            // personAccidentDTO.setIdHugeAccidentInfo(idHugeAccidentInfo);
            personAccidentDTO.setIsHugeAccident(isHugeAccident);
            personAccidentDTO.setHugeAccidentCode(hugeAccidentCode);
            personAccidentDTO.setInsuredIdentity(insuredIdentity);
            personAccidentDTO.setAccidentTime(accidentDate);
            personAccidentDTO.setPersonAccidentId(UuidUtil.getUUID());
            personAccidentDTO.setAccidentNation(overseaNationCode);
            this.savePersonAccident(reportNo, caseTimes, userId, personAccidentDTO);
        }
        if (hospitalDays != null) {
            personBenefitService.saveBenefitDays(hospitalDays, reportNo, 1, userId, taskId, status);
        }

        //非人伤
        if (caseClass.contains(ConstValues.CASECLASS_NO_PEOPLE_HURT_SX) || riskPropertyService.displayRiskProperty(reportNo, null)) {
            String idNpAhcsChannelProcess = this.getChannelProcessId(reportNo, 1, "", ConstValues.CASECLASS_NO_PEOPLE_HURT, userId);
            this.saveReportNoPeopleInfo(reportNo, 1, idNpAhcsChannelProcess, taskId, status, userId, accidentDate);
        }

//            Set<String> caseSubClassSet = new HashSet<>();
//            if (StringUtils.isNotEmpty(insuredApplyStatus)) {
//                accidentType = StringUtils.isNotEmpty(accidentType) ? accidentType : ChecklossConst.ACCIDENT_TYPE_YW;
//                therapyType = StringUtils.isNotEmpty(therapyType) ? therapyType : ChecklossConst.AHCS_THERAPY_ONE;
//
//                //事故类型不办空，且事故类型不为：疾病，则事故类型统一设为：意外
//                if (StringUtils.isNotEmpty(accidentType) && !ChecklossConst.ACCIDENT_TYPE_DISEASE.equals(accidentType)) {
//                    accidentType = ChecklossConst.ACCIDENT_TYPE_YW;
//                }
//                String[] insuredApplyStatuses = insuredApplyStatus.split("\\|");
//                for (String insuredApplyStatus1 : insuredApplyStatuses) {
//                    insuredApplyStatus1 = (ChecklossConst.INSURER_BENEFIT_RAPE.equals(insuredApplyStatus1)) ? ChecklossConst.INSURER_BENEFIT_AHCS : insuredApplyStatus1;
//                    List<CaseVO> caseVOList = new ArrayList<>();
//                    CaseVO caseVO = null;
//                    if (ChecklossConst.NEED_THREE_PARAM.contains(insuredApplyStatus1) && StringUtils.isNotEmpty(therapyType)) {
//                        String[] therapyTypes = therapyType.split("\\|");
//                        for (String therapyType1 : therapyTypes) {
//                            caseVO = new CaseVO();
//                            caseVO.setTherapyType(therapyType1);
//                            caseVO.setAccidentType(accidentType);
//                            caseVO.setInsuredApplyStatus(insuredApplyStatus1);
//                            caseVOList.add(caseVO);
//                        }
//                    } else {
//                        caseVO = new CaseVO();
//                        if (ChecklossConst.NEED_ACCIDENT_TYPE.contains(insuredApplyStatus1)) {
//                            caseVO.setAccidentType(accidentType);
//                        }
//                        caseVO.setInsuredApplyStatus(insuredApplyStatus1);
//                        caseVOList.add(caseVO);
//                    }
//                    List<String> caseSubClasses = new ArrayList<>();
//                    for (CaseVO caseVOParam : caseVOList) {
//                    }
//                    if (ListUtils.isEmptyList(caseSubClasses)) {
//                        if (ChecklossConst.PEOPLE_HURT_INSUREDAPPLYS_TATUS.contains(insuredApplyStatus1)) {
//                            caseSubClassSet.add(ConstValues.CASECLASS_OTHER_PEOPLE_HURT);
//                        } else {
//                            caseSubClassSet.add(ConstValues.CASECLASS_OTHER_NO_PEOPLE_HURT);
//                        }
//                        continue;
//                    }
//                    caseSubClassSet.addAll(caseSubClasses);
//                }
//            }
//
//            if (caseSubClassSet.size() == 0) {
//                if (caseClass.contains(ConstValues.CASECLASS_NO_PEOPLE_HURT)) {
//                    caseSubClassSet.add(ConstValues.CASECLASS_OTHER_NO_PEOPLE_HURT);
//                }
//                if (caseClass.contains(ConstValues.CASECLASS_PEOPLE_HURT)) {
//                    caseSubClassSet.add(ConstValues.CASECLASS_OTHER_PEOPLE_HURT);
//                }
//            }

        caseSubClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, taskId);
        //人伤报案caseSubClassList为空，非人伤报案已提前初始化
        if ((ListUtils.isEmptyList(caseSubClassList) || (ListUtils.isNotEmpty(caseSubClassList) && StringUtils.isEmptyStr(caseSubClassList.get(0))))) {
            if (StringUtils.isNotEmpty(insuredApplyType)) {
                List<String> caseSubClasses = new ArrayList<>();
                caseSubClasses.add(insuredApplyType);
                List<CaseClassDTO> caseSubClassDTO = new ArrayList<>();
                for (String caseSubClassStr : caseSubClasses) {
                    CaseClassDTO caseClassDTO = new CaseClassDTO();
                    caseClassDTO.setCreatedBy(userId);
                    caseClassDTO.setUpdatedBy(userId);
                    caseClassDTO.setTaskId(taskId);
                    caseClassDTO.setStatus(status);
                    caseClassDTO.setReportNo(reportNo);
                    caseClassDTO.setCaseTimes(caseTimes);
                    caseClassDTO.setCaseSubClass(caseSubClassStr);
                    caseClassDTO.setLossObjectNo(lossObjectNo);
                    caseSubClassDTO.add(caseClassDTO);
                }
                caseClassDao.addCaseClassList(caseSubClassDTO, caseTimes, userId);
            }
        }

    }
    /**
     * 保存非人伤信息
     */
    private void saveReportNoPeopleInfo(String reportNo, Integer caseTimes, String channelId, String taskCode, String status, String userId, Date accidentDate) {
        FlightDelayVO flightDelayVO = this.transToFlightDelayVO(reportNo);
        if (flightDelayVO != null) {
            this.saveFlightDelay(flightDelayVO, reportNo, caseTimes, channelId, userId, taskCode, status);
        }
        BaggageDelayVO baggageDelayVO = this.transToBaggageDelayVO(reportNo);
        if (baggageDelayVO != null) {
            this.saveBaggageDelay(baggageDelayVO, reportNo, caseTimes, channelId, userId, taskCode, status);
        }
        VehiclDelayOtherVO vehiclDelayOtherVO = this.transToVehiclDelayOtherVO(reportNo);
        if (vehiclDelayOtherVO != null) {
            this.saveVehiclDelay(vehiclDelayOtherVO, reportNo, caseTimes, channelId, userId, taskCode, status);
        }
        ExaminFailVO examinFailVO = this.transToExaminFailVO(reportNo);
        if (examinFailVO != null) {
            this.saveExaminFailInfo(examinFailVO, reportNo, caseTimes, channelId, userId, taskCode, status);
        }
        TravelAlertVO travelAlertVO = this.transToTravelAlertVO(reportNo);
        if (travelAlertVO != null) {
            this.saveTravelAlterInfo(travelAlertVO, reportNo, caseTimes, channelId, userId, taskCode, status);
        }
        PropertyLossVO propertyLossVO = this.transToPropertyLossVO(reportNo, accidentDate);
        if (propertyLossVO != null) {
            this.savePropertyLossInfo(propertyLossVO, reportNo, caseTimes, channelId, userId, taskCode, status);
        }
        OtherLossVO otherLossVO = this.transToOtherLossVO(reportNo);
        if (otherLossVO != null) {
            this.saveOtherLossDTOInfo(otherLossVO, reportNo, caseTimes, channelId, userId, taskCode, status);
        }

        savePetInjureInfo(reportNo);

    }

    private void savePetInjureInfo(String reportNo){
        ReportAccidentPetEntity petEntity = reportService.getReportAccidentPetByReportNo(reportNo);
        if(petEntity == null){
            return;
        }
        PetInjureVO petInjureVO = new PetInjureVO();
        petInjureVO.setReportNo(reportNo);
        petInjureVO.setCaseTimes(1);
        TypeAndAmountVO typeAndAmountVO = new TypeAndAmountVO();
        typeAndAmountVO.setLossAmount(petEntity.getCostEstimate());
        typeAndAmountVO.setType(petEntity.getPetInjuredType());
        List<TypeAndAmountVO> typeAndAmountList = new ArrayList<>();
        typeAndAmountList.add(typeAndAmountVO);
        petInjureVO.setTypeAndAmountList(typeAndAmountList);
        petInjureVO.setAccidentPet(petEntity.getAccidentPet());

        ReportAccidentEntity accidentEntity = reportService.getReportAccident(reportNo);
        if (accidentEntity != null && StringUtils.isNotEmpty(accidentEntity.getIdClmReportAccident())) {
            petInjureVO.setAccidentDate(accidentEntity.getAccidentDate());
            petInjureVO.setAccidentOverseas(accidentEntity.getOverseasOccur());
            petInjureVO.setAccidentProvinceCode(accidentEntity.getProvinceCode());
            petInjureVO.setAccidentCityCode(accidentEntity.getAccidentCityCode());
            petInjureVO.setAccidentCountryCode(accidentEntity.getAccidentCountyCode());
            petInjureVO.setAccidentContinentCode(accidentEntity.getOverseaNationCode());
            petInjureVO.setAccidentPlace(accidentEntity.getAccidentPlace());
        }

        petInjureVO.setTaskCode("report1");
        petInjureVO.setStatus(BaseConstant.STRING_1);
        petInjureService.addPetInjure(petInjureVO);
    }

    private FlightDelayVO transToFlightDelayVO(String reportNo) {
        ReportAccidentFlightEntity flightEntity = reportService.getReportAccidentFlightByReportNo(reportNo);
        if (flightEntity == null || StringUtils.isEmptyStr(flightEntity.getIdAhcsReportAccidentFlight())) {
            return null;
        }
        FlightDelayVO flightDelayVO = new FlightDelayVO();
        flightDelayVO.setOriginalFlightNo(flightEntity.getFlightNo());
        flightDelayVO.setRealFlightNo(flightEntity.getReplaceFlight());
        flightDelayVO.setTicketNo(flightEntity.getEleTicketNo());
        flightDelayVO.setDepartPlace(flightEntity.getDeparturePlace());
        flightDelayVO.setArrivalPlace(flightEntity.getDepartureAirport());
        flightDelayVO.setDelayDuration(BigDecimal.valueOf(flightEntity.getDelayTime()));
        flightDelayVO.setDelayDurationUnit("min");

        // 航班状态(多选)
        List<String> flightStatusList = new ArrayList<>();
        if (ConfigConstValues.YES.equals(flightEntity.getIsFlightLand())) {
            flightStatusList.add(FlightDelayConstValues.FLIGHT_STATUS_LAND);
        }
        if (ConfigConstValues.YES.equals(flightEntity.getIsFlightCancellation())) {
            flightStatusList.add(FlightDelayConstValues.FLIGHT_STATUS_CANCEL);
        }
        if (ConfigConstValues.YES.equals(flightEntity.getIsFlightDelay())) {
            flightStatusList.add(FlightDelayConstValues.FLIGHT_STATUS_DELAY);
        }
        if (ListUtils.isEmptyList(flightStatusList)) {
            flightStatusList.add(FlightDelayConstValues.FLIGHT_STATUS_DELAY);
        }
        flightDelayVO.setFlightStatusArr(flightStatusList.toArray(new String[] {}));
        return flightDelayVO;
    }

    private PersonAccidentVO getPersonAccidentVO(String idAhcsChannelProcess, String taskId, String reportNo) {
        PersonAccidentDTO personAccidentDTO = personAccidentDao.getPersonAccident(idAhcsChannelProcess, taskId, null);
        if (personAccidentDTO == null) {
            personAccidentDTO = new PersonAccidentDTO();
        }
        PersonAccidentVO personAccidentVO = new PersonAccidentVO();
        if (!StringUtils.isEmptyStr(personAccidentDTO.getProfessionCode())) {
            SubProfessionDefineDTO parentSub = professionDefineDao.getParentSub(personAccidentDTO.getProfessionCode());
            personAccidentDTO.setProfessionBigdCode(parentSub == null ? "" : parentSub.getParentCode());
        }
        BeanUtils.copyProperties(personAccidentDTO, personAccidentVO);

        personAccidentVO.setWhetherOutSideAccident(personAccidentVO.getOverseasOccur());
        personAccidentVO.setAccidentProvince(personAccidentVO.getProvinceCode());
        personAccidentVO.setAccidentCity(personAccidentDTO.getAccidentCityCode());
        personAccidentVO.setAccidentCounty(personAccidentVO.getAccidentCountyCode());

        personAccidentVO.setShowConstruction("Y");
//        List<String> showWorkInjuryTypeList = new ArrayList<>();
//        Map<String, Boolean> showResultMap = this.checkFromDetailFormulaListByReportNo(reportNo, "是否工伤", "是否因公致伤");
//        if (showResultMap.get("是否工伤") != null && showResultMap.get("是否工伤")) {
//            showWorkInjuryTypeList.add("01");
//        }
//        if (showResultMap.get("是否因公致伤") != null && showResultMap.get("是否因公致伤")) {
//            showWorkInjuryTypeList.add("02");
//        }
        String workInjuryTypes = personAccidentDTO.getWorkInjuryType();
        List<String> workInjuryTypeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(workInjuryTypes)) {
            String[] workInjuryTypeArr = workInjuryTypes.split(",");
            for (String workInjuryType : workInjuryTypeArr) {
                if (StringUtils.isNotEmpty(workInjuryType)) {
                    workInjuryTypeList.add(workInjuryType);
                }
            }
        }


        personAccidentVO.setWorkInjuryTypeList(workInjuryTypeList);
        personAccidentVO.setModuleCode(ChecklossConst.PERSON_ACCIDENT);

        if (StringUtils.isNotEmpty(personAccidentDTO.getPersonAccidentId())) {
            List<ThirdCarDTO> thirdCarList = thirdCarService.getThirdCarList(personAccidentDTO.getPersonAccidentId());
            if (ListUtils.isEmptyList(thirdCarList)) {
                thirdCarList = new ArrayList<ThirdCarDTO>();
                ThirdCarDTO thirdCarDTO = new ThirdCarDTO();
                thirdCarList.add(thirdCarDTO);
            }
            personAccidentVO.setThirdCarList(thirdCarList);
        }

        if (StringUtils.isNotEmpty(personAccidentVO.getHugeAccidentCode())) {
            HugeAccidentInfoDTO queryDTO = new HugeAccidentInfoDTO();
            queryDTO.setAccidentCode(personAccidentVO.getHugeAccidentCode());
            HugeAccidentInfoDTO accidentInfo = hugeAccidentInfoMapper.queryOneByCondition(queryDTO);
            if (Objects.nonNull(accidentInfo)) {
                personAccidentVO.setHugeAccidentName(accidentInfo.getAccidentName());
            }
        }
        return personAccidentVO;
    }

    private PersonDiseaseVO getPersonDiseaseVO(String idAhcsChannelProcess, String taskId) {
        PersonDiseaseDTO personDiseaseDTO = personDiseaseDao.getPersonDisease(idAhcsChannelProcess, taskId, "");
        if (personDiseaseDTO == null) {
            personDiseaseDTO = new PersonDiseaseDTO();
        }
        PersonDiseaseVO personDiseaseVO = new PersonDiseaseVO();
        BeanUtils.copyProperties(personDiseaseDTO, personDiseaseVO);
        personDiseaseVO.setModuleCode(ChecklossConst.PERSON_DISEASE);
        return personDiseaseVO;
    }

    private PersonDiagnoseVO getPersonDiagnoseVO(String reportNo, String idAhcsChannelProcess, String taskId) {
        List<PersonDiagnoseDTO> personDiagnoseDTOs = personDiagnoseDao.getPersonDiagnoseListById(idAhcsChannelProcess, taskId);

        if (personDiagnoseDTOs == null || personDiagnoseDTOs.isEmpty()) {
            personDiagnoseDTOs = new ArrayList<PersonDiagnoseDTO>();
            PersonDiagnoseDTO personDiagnoseDTO = new PersonDiagnoseDTO();
            personDiagnoseDTOs.add(personDiagnoseDTO);
        }
        personDiagnoseDTOs.forEach(personDiagnoseDTO -> {
            if (!StringUtils.isEmptyStr(personDiagnoseDTO.getDiagnosticTypologyCode())) {
                personDiagnoseDTO.setDiagnosticTypologyName(DiagnosticTypologyEnum.getName(personDiagnoseDTO.getDiagnosticTypologyCode()));
            }
        });
        PersonDiagnoseVO personDiagnoseVO = new PersonDiagnoseVO();
        personDiagnoseVO.setModuleCode(ChecklossConst.PERSON_DIAGNOSE);
        personDiagnoseVO.setDiagnoseDTOs(personDiagnoseDTOs);
        personDiagnoseVO.setShowFertility("N");
        return personDiagnoseVO;
    }

    private PersonDisabilityVO getPersonDisabilityVO(String idAhcsChannelProcess, String taskId) {
        List<PersonDisabilityDTO> personDisabilityDTOs = personDisabilityDao.getPersonDisabilityList(idAhcsChannelProcess, taskId, null);
        if (personDisabilityDTOs == null || personDisabilityDTOs.isEmpty()) {
            personDisabilityDTOs = new ArrayList<PersonDisabilityDTO>();
            PersonDisabilityDTO personDisabilityDTO = new PersonDisabilityDTO();
            personDisabilityDTOs.add(personDisabilityDTO);
        }
        PersonDisabilityVO personDisabilityVO = new PersonDisabilityVO();
        personDisabilityVO.setModuleCode(ChecklossConst.PERSON_DISABILITY);
        personDisabilityVO.setDisabilityDTOs(personDisabilityDTOs);
        return personDisabilityVO;
    }

    private PersonDeathVO getPersonDeathVO(String idAhcsChannelProcess, String taskId) {
        PersonDeathDTO personDeathDTO = personDeathDao.getPersonDeath(idAhcsChannelProcess, taskId, null);
        if (personDeathDTO == null) {
            personDeathDTO = new PersonDeathDTO();
        }
        PersonDeathVO personDeathVO = new PersonDeathVO();
        BeanUtils.copyProperties(personDeathDTO, personDeathVO);
        personDeathVO.setModuleCode(ChecklossConst.PERSON_DEATH);
        return personDeathVO;
    }

    private BigDiseaseVO getBigDiseaseVO(String idAhcsChannelProcess, String taskId) {
        BigDiseaseDTO bigDiseaseDTO = bigDiseaseDao.getBigDisease(idAhcsChannelProcess, taskId, null);
        if (bigDiseaseDTO == null) {
            bigDiseaseDTO = new BigDiseaseDTO();
        }
        BigDiseaseVO bigDiseaseVO = new BigDiseaseVO();
        BeanUtils.copyProperties(bigDiseaseDTO, bigDiseaseVO);
        bigDiseaseVO.setModuleCode(ChecklossConst.PERSON_BIG_DISEASE);
        return bigDiseaseVO;
    }

    private List<String> getBigDiseaseDetailList(String bigDiseaseId) {
        List<String> bigDiseaseDetailCodes = new ArrayList<String>();
        if (bigDiseaseId != null) {
            bigDiseaseDetailCodes = bigDiseaseDetailDao.getBigDiseaseDetailList(bigDiseaseId);
        }
        return bigDiseaseDetailCodes;
    }

    private List<DisabilityAppraisalVO> getDisabilityAppraisalVO(String idAhcsChannelProcess) {
        DisabilityAppraisalVO disabilityAppraisalVO = new DisabilityAppraisalVO();
        List<DisabilityAppraisalVO> disabilityAppraisalVOList = new ArrayList<DisabilityAppraisalVO>();
        List<DisabilityAppraisalDTO> isAppraisal = disabilityAppraisalService.getIsAppraisal(idAhcsChannelProcess);
        isAppraisal.forEach(disabilityAppraisalDTO -> {
            BeanUtils.copyProperties(disabilityAppraisalDTO, disabilityAppraisalVO);
            disabilityAppraisalVO.setIsAppraisal(disabilityAppraisalVO.getIsAppraisal());
            disabilityAppraisalVO.setModuleCode(ChecklossConst.PERSON_DISABILITY);
            disabilityAppraisalVOList.add(disabilityAppraisalVO);
        });
        return disabilityAppraisalVOList;
    }

    private PersonHospitalVO getPersonHospitalVO(String reportNo, String idAhcsChannelProcess, String taskId) throws GlobalBusinessException {
        PersonHospitalVO personHospitalVO = new PersonHospitalVO();
        List<PersonHospitalDTO> personHospitalDTOs = personHospitalService.getPersonHospitalByIdAhcsChannelProcess(idAhcsChannelProcess, taskId, "");
        if (personHospitalDTOs == null || personHospitalDTOs.isEmpty()) {
            personHospitalDTOs = new ArrayList<PersonHospitalDTO>();
            PersonHospitalDTO personHospitalDTO = new PersonHospitalDTO();
            personHospitalDTOs.add(personHospitalDTO);
        }
        personHospitalDTOs.forEach(hospital -> {
            if (!StringUtils.isEmptyStr(hospital.getTherapyType()) && (("MS_2201").equals(hospital.getMedicalStatus()) || ("MS_2204").equals(hospital.getMedicalStatus()))) {
                hospital.setHospitalName("");
                hospital.setHospitalCode(null);
                hospital.setHospitalGrade(null);
                hospital.setTherapyType(null);
                hospital.setHospitalPropertyDes(null);
                hospital.setIsCooperation(null);
                hospital.setIsAppointedHospital(null);
                hospital.setIsSocialInsurance(null);
            }
        });
        personHospitalVO.setPersonHospitalList(personHospitalDTOs);
        personHospitalVO.setModuleCode(ChecklossConst.PERSON_DIAGNOSE);
        return personHospitalVO;
    }

    @Override
    public SurveyVO getSurveyVO(String reportNo, int caseTimes, String taskId) {
        SurveyVO surveyVO = new SurveyVO();
        SurveyDTO setIdAhcsSu = new SurveyDTO();
        setIdAhcsSu.setReportNo(reportNo);
        setIdAhcsSu.setCaseTimes(caseTimes);
        setIdAhcsSu.setTaskId(taskId);
        SurveyDTO surveyDTO = surveyMapper.getSurvey(setIdAhcsSu);
        if (surveyDTO == null) {
            return surveyVO;
        }
        BeanUtils.copyProperties(surveyDTO, surveyVO);
        return surveyVO;
    }

    @Override
    public SurveyVO getSurveyVOByCaseType(String reportNo, int caseTimes, int afterRegistCase) {
        //立案之后不会修改,立案之前查 暂存 ，立案之后查提交
        SurveyVO surveyVO = new SurveyVO();
        SurveyDTO setIdAhcsSu = new SurveyDTO();
        setIdAhcsSu.setReportNo(reportNo);
        setIdAhcsSu.setCaseTimes(caseTimes);
        setIdAhcsSu.setTaskId("reportTrack");
        if (1 == afterRegistCase) {
            setIdAhcsSu.setStatus("1");
        } else {
            setIdAhcsSu.setStatus("0");
        }
        SurveyDTO surveyDTO = surveyMapper.getSurvey(setIdAhcsSu);
        if (surveyDTO == null) {
            return surveyVO;
        }
        BeanUtils.copyProperties(surveyDTO, surveyVO);
        return surveyVO;
    }


    private void savePersonObjectEx(String reportNo, Integer caseTimes, String loginUm, PersonObjectExVO objectExVO) {
        PersonObjectExDTO objectExDTO = new PersonObjectExDTO();
        BeanUtils.copyProperties(objectExVO, objectExDTO);
        objectExDTO.setCreatedBy(loginUm);
        objectExDTO.setUpdatedBy(loginUm);
        objectExDTO.setReportNo(reportNo);
        objectExDTO.setCaseTimes(caseTimes);
        personObjectExService.savePersonOtherInfo(objectExDTO);
    }

    private PersonObjectExVO getPersonObjectExVO(String channelProcessId) {
        PersonObjectExDTO objectExDto = personObjectExService.getPersonOtherInfoByIdChannelProcess(channelProcessId);
        if (objectExDto == null) {
            objectExDto = new PersonObjectExDTO();
        }
        PersonObjectExVO objectExVO = new PersonObjectExVO();
        BeanUtils.copyProperties(objectExDto, objectExVO);
        return objectExVO;

    }

    private BaggageDelayVO transToBaggageDelayVO(String reportNo) {
        ReportAccidentBaggageEntity baggageEntity = reportService.getReportAccidentBaggageByReportNo(reportNo);
        if (baggageEntity == null || StringUtils.isEmptyStr(baggageEntity.getIdAhcsReportAccidentBag())) {
            return null;
        }
        BaggageDelayVO baggageDelayVO = new BaggageDelayVO();
        baggageDelayVO.setFlightNo(baggageEntity.getFlightNo());
        Date planArrivalDate = baggageEntity.getPlanArrivalDate();
        Date signDate = baggageEntity.getSignDate();
        baggageDelayVO.setArrivalTime(planArrivalDate);
        baggageDelayVO.setSignInTime(signDate);
        String delayDuration = DateUtils.getCostTime(planArrivalDate, signDate);
        if (StringUtils.isNotEmpty(delayDuration)) {
            baggageDelayVO.setDelayDuration(new BigDecimal(delayDuration));
        }
        baggageDelayVO.setDelayDurationUnit("min");
        baggageDelayVO.setDelayDuration(new BigDecimal(baggageEntity.getDelayTime()));
        baggageDelayVO.setDepartPlace(baggageEntity.getDepartureAirport());
        baggageDelayVO.setArrivalPlace(baggageEntity.getDestinationAirport());
        return baggageDelayVO;
    }

    private VehiclDelayOtherVO transToVehiclDelayOtherVO(String reportNo) {
        ReportAccidentTrafficEntity trafficEntity = reportService.getReportAccidentTrafficByReportNo(reportNo);
        if (trafficEntity == null || StringUtils.isEmptyStr(trafficEntity.getIdAhcsReportAccidentTra())) {
            return null;
        }
        VehiclDelayOtherVO vehiclDelayOtherVO = new VehiclDelayOtherVO();
        String transportation = trafficEntity.getTransportation();
        String isTrafficDelay = trafficEntity.getIsTrafficDelay();
        String steamerDelayCase = trafficEntity.getSteamerDelayCase();
        if (StringUtils.isNotEmpty(transportation)) {
            switch (transportation) {
                case "1":
                    vehiclDelayOtherVO.setVehiclType(SettleConst.AHCS_VEHIC_TYPE_01);
                    break;
                case "2":
                    if ("N".equals(isTrafficDelay)) {
                        vehiclDelayOtherVO.setVehiclType(SettleConst.AHCS_VEHIC_TYPE_04);
                    } else {
                        vehiclDelayOtherVO.setVehiclType(SettleConst.AHCS_VEHIC_TYPE_02);
                    }
                    break;
                case "3":
                    vehiclDelayOtherVO.setVehiclType(SettleConst.AHCS_VEHIC_TYPE_03);
                    break;
                default:
                    break;
            }
        }

        if (StringUtils.isNotEmpty(steamerDelayCase)) {
            String[] steamerDelayCaseArr = steamerDelayCase.split("\\|");
            for (int i = 0; i < steamerDelayCaseArr.length; i++) {
                switch (steamerDelayCaseArr[i]) {
                    case "1":
                        steamerDelayCaseArr[i] = SettleConst.AHCS_CRUISES_DELAY_01;
                        break;
                    case "2":
                        steamerDelayCaseArr[i] = SettleConst.AHCS_CRUISES_DELAY_02;
                        break;
                    default:
                        break;
                }
            }
            vehiclDelayOtherVO.setCruisesDelayDescArr(steamerDelayCaseArr);
        }

        Date originalDepartureDate = trafficEntity.getOriginalDepartureDate();
        Date originalArrivalDate = trafficEntity.getOriginalArrivalDate();
        Date actualDepartureDate = trafficEntity.getActualDepartureDate();
        Date actualArrivalDate = trafficEntity.getActualArrivalDate();
        String originalDelay = DateUtils.getCostTime(originalDepartureDate, originalArrivalDate);
        String actualDelay = DateUtils.getCostTime(actualDepartureDate, actualArrivalDate);
        BigDecimal originalDelayD = null;
        BigDecimal actualDelayD = null;
        if (StringUtils.isNotEmpty(originalDelay)) {
            originalDelayD = new BigDecimal(originalDelay);
        }
        if (StringUtils.isNotEmpty(actualDelay)) {
            actualDelayD = new BigDecimal(actualDelay);
        }
        if (originalDelayD != null && actualDelayD != null) {
            vehiclDelayOtherVO.setDelayDuration(originalDelayD.compareTo(actualDelayD) > 0 ? originalDelayD : actualDelayD);
        } else {
            if (StringUtils.isNotEmpty(originalDelay)) {
                vehiclDelayOtherVO.setDelayDuration(originalDelayD);
            }
            if (StringUtils.isNotEmpty(actualDelay)) {
                vehiclDelayOtherVO.setDelayDuration(actualDelayD);
            }
        }
        vehiclDelayOtherVO.setDelayDuration(new BigDecimal(trafficEntity.getDelayTime()));
        vehiclDelayOtherVO.setOriginalDepartTime(originalDepartureDate);
        vehiclDelayOtherVO.setOriginalArrivalTime(originalArrivalDate);
        vehiclDelayOtherVO.setRealDepartTime(actualDepartureDate);
        vehiclDelayOtherVO.setRealArrivalTime(actualArrivalDate);

        vehiclDelayOtherVO.setDepartPlace(trafficEntity.getDeparturePlace());
        vehiclDelayOtherVO.setArrivalPlace(trafficEntity.getDestination());
        vehiclDelayOtherVO.setDelayDurationUnit("min");
        vehiclDelayOtherVO.setPortInfo(trafficEntity.getChangedPort());
        return vehiclDelayOtherVO;
    }

    private ExaminFailVO transToExaminFailVO(String reportNo) {
        ReportAccidentExamEntity examEntity = reportService.getReportAccidentExamByReportNo(reportNo);
        if (examEntity == null || StringUtils.isEmptyStr(examEntity.getIdAhcsReportAccidentExam())) {
            return null;
        }
        ExaminFailVO examinFailVO = null;
        String noPassType = examEntity.getNoPassType();
        if (StringUtils.isNotEmpty(noPassType)) {
            examinFailVO = new ExaminFailVO();
            Set<String> noPassTypeSet = new HashSet<>();
            String[] noPassTypeArr = noPassType.split("\\|");
            for (String type : noPassTypeArr) {
                if ("1".equals(type) || "2".equals(type)) {
                    noPassTypeSet.add(SettleConst.EXAMIN_01);
                } else {
                    noPassTypeSet.add(SettleConst.EXAMIN_02);
                }
            }
            examinFailVO.setFailTypeArr(noPassTypeSet.toArray(new String[]{}));
        }
        return examinFailVO;
    }

    private TravelAlertVO transToTravelAlertVO(String reportNo) {
        ReportAccidentTravelEntity travelEntity = reportService.getReportAccidentTravelByReportNo(reportNo);
        TravelAlertVO travelAlertVO = null;
        if (travelEntity != null && StringUtils.isNotEmpty(travelEntity.getIdAhcsReportAccidentTravel())) {
            travelAlertVO = new TravelAlertVO();
            String lossType = travelEntity.getChangeType();
            String alterReason = travelEntity.getChangeReason();
            BigDecimal lossAmount = travelEntity.getCostEstimate();
            if (StringUtils.isNotEmpty(lossType)) {
                String[] lossTypeArr = lossType.split("\\|");
                List<TravelAlertDTO> travelAlertlist = new ArrayList<>();
                for (String type : lossTypeArr) {
                    String accidentType = SettleConst.REPORT_TRAVEL_ALERT_MAP.get(type);
                    if (StringUtils.isNotEmpty(accidentType)) {
                        TravelAlertDTO travelAlertDTO = new TravelAlertDTO();
                        travelAlertDTO.setAccidentType(accidentType);
                        travelAlertDTO.setLossAmount(lossAmount);
                        travelAlertDTO.setAlterReason(alterReason);
                        travelAlertlist.add(travelAlertDTO);
                    }
                }
                if (ListUtils.isNotEmpty(travelAlertlist)) {
                    travelAlertVO.setTravelAlertlist(travelAlertlist);
                }
            }
            ReportAccidentEntity accidentEntity = reportService.getReportAccident(reportNo);
            if (accidentEntity != null && StringUtils.isNotEmpty(accidentEntity.getIdClmReportAccident())) {
                String overseasOccur = accidentEntity.getOverseasOccur();
                String provinceCode = accidentEntity.getProvinceCode();
                String accidentCityCode = accidentEntity.getAccidentCityCode();
                String accidentCountyCode = accidentEntity.getAccidentCountyCode();
                String accidentPlace = accidentEntity.getAccidentPlace();
                String accidentArea = accidentEntity.getAccidentArea();
                travelAlertVO.setAccidentOverseas(overseasOccur);
                travelAlertVO.setProvinceCode(provinceCode);
                travelAlertVO.setAccidentCityCode(accidentCityCode);
                travelAlertVO.setAccidentCountyCode(accidentCountyCode);
                travelAlertVO.setAccidentPlace(accidentPlace);
                travelAlertVO.setAccidentContinentCode(accidentArea);
            }
        }

        return travelAlertVO;
    }

    private PropertyLossVO transToPropertyLossVO(String reportNo, Date accidentDate) {
        ReportAccidentLossEntity lossEntity = reportService.getReportAccidentLossByReportNo(reportNo);

        PropertyLossVO propertyLossVO = null;
        if (lossEntity != null && StringUtils.isNotEmpty(lossEntity.getIdAhcsReportAccidentLoss())) {
            propertyLossVO = new PropertyLossVO();
            String lossType = lossEntity.getLossType();
            String alterReason = lossEntity.getAccidentReason();
            BigDecimal lossAmount = lossEntity.getCostEstimate();
            String accidentCauseCode = lossEntity.getAccidentReason();
            String otherAccidentCause = lossEntity.getAccidentReasonDesc();
            if (StringUtils.isNotEmpty(lossType)) {
                String[] lossTypeArr = lossType.split("\\|");
                List<PropertyLossDTO> propertyLossList = new ArrayList<>();
                for (String type : lossTypeArr) {
                    if (StringUtils.isNotEmpty(type)) {
                        PropertyLossDTO propertyLossDTO = new PropertyLossDTO();
                        propertyLossDTO.setAccidentType(type);
                        propertyLossDTO.setLossAmount(lossAmount);
                        propertyLossDTO.setAlterReason(alterReason);
                        propertyLossList.add(propertyLossDTO);
                    }
                }
                if (ListUtils.isNotEmpty(propertyLossList)) {
                    propertyLossVO.setAccidentDate(accidentDate);
                    propertyLossVO.setAccidentCauseCode(accidentCauseCode);
                    propertyLossVO.setOtherAccidentCause(otherAccidentCause);
                    propertyLossVO.setPropertyLossList(propertyLossList);
                }
            }
            ReportAccidentEntity accidentEntity = reportService.getReportAccident(reportNo);
            if (accidentEntity != null && StringUtils.isNotEmpty(accidentEntity.getIdClmReportAccident())) {
                String overseasOccur = accidentEntity.getOverseasOccur();
                String provinceCode = accidentEntity.getProvinceCode();
                String accidentCityCode = accidentEntity.getAccidentCityCode();
                String accidentCountyCode = accidentEntity.getAccidentCountyCode();
                String accidentPlace = accidentEntity.getAccidentPlace();
                String accidentArea = accidentEntity.getAccidentArea();
                propertyLossVO.setAccidentOverseas(overseasOccur);
                propertyLossVO.setProvinceCode(provinceCode);
                propertyLossVO.setAccidentCityCode(accidentCityCode);
                propertyLossVO.setAccidentCountyCode(accidentCountyCode);
                propertyLossVO.setAccidentPlace(accidentPlace);
                propertyLossVO.setAccidentContinentCode(accidentArea);
            }
        }

        return propertyLossVO;
    }

    private OtherLossVO transToOtherLossVO(String reportNo) {
        ReportAccidentOtherEntity otherEntity = reportService.getReportAccidentOtherByReportNo(reportNo);

        OtherLossVO otherLossVO = null;
        if (otherEntity != null && StringUtils.isNotEmpty(otherEntity.getIdAhcsReportAccidentOther())) {
            otherLossVO = new OtherLossVO();
            BigDecimal lossAmount = otherEntity.getCostEstimate();
            otherLossVO.setLossAmount(lossAmount);
            ReportAccidentEntity accidentEntity = reportService.getReportAccident(reportNo);
            if (accidentEntity != null && StringUtils.isNotEmpty(accidentEntity.getIdClmReportAccident())) {
                String overseasOccur = accidentEntity.getOverseasOccur();
                String provinceCode = accidentEntity.getProvinceCode();
                String accidentCityCode = accidentEntity.getAccidentCityCode();
                String accidentCountyCode = accidentEntity.getAccidentCountyCode();
                String accidentPlace = accidentEntity.getAccidentPlace();
                String accidentArea = accidentEntity.getAccidentArea();
                otherLossVO.setAccidentOverseas(overseasOccur);
                otherLossVO.setProvinceCode(provinceCode);
                otherLossVO.setAccidentCityCode(accidentCityCode);
                otherLossVO.setAccidentCountyCode(accidentCountyCode);
                otherLossVO.setAccidentPlace(accidentPlace);
                otherLossVO.setAccidentContinentCode(accidentArea);
            }
        }

        return otherLossVO;
    }

    private void saveFlightDelay(FlightDelayVO flightDelayVO, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskCode, String status) {
        FlightDelayDTO flightDelayDTO = new FlightDelayDTO();
        String[] flightStatusArr = flightDelayVO.getFlightStatusArr();
        StringBuffer flightStatus = new StringBuffer();
        if (flightStatusArr != null && flightStatusArr.length > 0) {
            for (String fstatus : flightStatusArr) {
                flightStatus.append(fstatus).append(",");
            }
            flightDelayVO.setFlightStatus(flightStatus.substring(0, flightStatus.length() - 1));
        } else {
            flightDelayVO.setFlightStatus("");
        }

        this.setFlightStatus(flightDelayVO);

        BeanUtils.copyProperties(flightDelayVO, flightDelayDTO);
        flightDelayDTO.setIdAhcsChannelProcess(channelId);
        flightDelayDTO.setTaskCode(taskCode);
        flightDelayDTO.setReportNo(reportNo);
        flightDelayDTO.setCaseTimes(caseTimes);
        flightDelayDTO.setCreatedBy(loginUm);
        flightDelayDTO.setUpdatedBy(loginUm);
        flightDelayDTO.setStatus(status);
        flightDelayService.saveFlighterDelay(flightDelayDTO);

    }

    private void setFlightStatus(FlightDelayVO flightDelayVO) {//todo
    }

    private void saveBaggageDelay(BaggageDelayVO baggageDelayVO, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskCode, String status) {
        BaggageDelayDTO baggageDelayDTO = new BaggageDelayDTO();
        BeanUtils.copyProperties(baggageDelayVO, baggageDelayDTO);
        baggageDelayDTO.setIdAhcsChannelProcess(channelId);
        baggageDelayDTO.setTaskCode(taskCode);
        baggageDelayDTO.setReportNo(reportNo);
        baggageDelayDTO.setCaseTimes(caseTimes);
        baggageDelayDTO.setCreatedBy(loginUm);
        baggageDelayDTO.setUpdatedBy(loginUm);
        baggageDelayDTO.setStatus(status);
        baggageDelayService.saveBaggageDelay(baggageDelayDTO);
    }

    private void saveVehiclDelay(VehiclDelayOtherVO vehiclDelayOtherVO, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskCode, String status) {
        VehiclDelayOtherDTO vehiclDelayOtherDTO = new VehiclDelayOtherDTO();
        BeanUtils.copyProperties(vehiclDelayOtherVO, vehiclDelayOtherDTO);
        vehiclDelayOtherDTO.setIdAhcsChannelProcess(channelId);
        vehiclDelayOtherDTO.setTaskCode(taskCode);
        vehiclDelayOtherDTO.setReportNo(reportNo);
        vehiclDelayOtherDTO.setCaseTimes(caseTimes);
        vehiclDelayOtherDTO.setCreatedBy(loginUm);
        vehiclDelayOtherDTO.setUpdatedBy(loginUm);
        vehiclDelayOtherDTO.setStatus(status);
        if (SettleConst.AHCS_VEHIC_TYPE_04.equals(vehiclDelayOtherDTO.getVehiclType())) {
            String[] cruisesDelayDescArr = vehiclDelayOtherVO.getCruisesDelayDescArr();
            if (cruisesDelayDescArr != null && cruisesDelayDescArr.length > 0) {
                StringBuffer cruisesDelayDesc = new StringBuffer();
                for (String desc : cruisesDelayDescArr) {
                    cruisesDelayDesc.append(desc).append(",");
                    vehiclDelayOtherDTO.setCruisesDelayDesc(cruisesDelayDesc.substring(0, cruisesDelayDesc.length() - 1));
                }
            } else {
                vehiclDelayOtherDTO.setCruisesDelayDesc("");
            }
        } else {
            vehiclDelayOtherDTO.setPortInfo(null);
            vehiclDelayOtherDTO.setCancelPortCount(null);
            vehiclDelayOtherDTO.setCruisesDelayDesc(null);
            vehiclDelayOtherDTO.setAlertDuration(null);
        }
        vehiclDelayOtherService.saveVehiclDelayOther(vehiclDelayOtherDTO);
    }

    private void saveExaminFailInfo(ExaminFailVO examinFailVO, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskCode, String status) {
        ExaminFailDTO examinFailDTO = new ExaminFailDTO();
        BeanUtils.copyProperties(examinFailVO, examinFailDTO);
        String[] failTypeArr = examinFailVO.getFailTypeArr();
        if (failTypeArr != null && failTypeArr.length > 0) {
            Boolean saveFailSubjectFlag = false;
            StringBuffer failTypeStr = new StringBuffer();
            for (String failType : failTypeArr) {
                if (!saveFailSubjectFlag && SettleConst.EXAMIN_01.equals(failType)) {
                    saveFailSubjectFlag = true;
                }
                failTypeStr.append(failType).append(",");
            }
            if (!saveFailSubjectFlag) {
                examinFailDTO.setFailSubject("");
            }
            examinFailDTO.setFailType(failTypeStr.substring(0, failTypeStr.length() - 1));
        } else {
            examinFailDTO.setFailType("");
        }
        examinFailDTO.setTaskCode(taskCode);
        examinFailDTO.setReportNo(reportNo);
        examinFailDTO.setCaseTimes(caseTimes);
        examinFailDTO.setIdAhcsChannelProcess(channelId);
        examinFailDTO.setCreatedBy(loginUm);
        examinFailDTO.setUpdatedBy(loginUm);
        examinFailDTO.setStatus(status);
        examinFailService.saveExaminFail(examinFailDTO);
    }

    private void saveTravelAlterInfo(TravelAlertVO travelAlertVO, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskCode, String status) {
        List<TravelAlertDTO> travelAlertList = travelAlertVO.getTravelAlertlist();
        if (ListUtils.isNotEmpty(travelAlertList)) {
            for (TravelAlertDTO travelAlertDTO : travelAlertList) {
                travelAlertDTO.setAccidentOverseas(travelAlertVO.getAccidentOverseas());
                if (ConstValues.ZERO_STR.equals(travelAlertVO.getAccidentOverseas())) {
                    travelAlertDTO.setProvinceCode(travelAlertVO.getProvinceCode());
                    travelAlertDTO.setAccidentCityCode(travelAlertVO.getAccidentCityCode());
                    travelAlertDTO.setAccidentCountyCode(travelAlertVO.getAccidentCountyCode());
                    travelAlertDTO.setAccidentContinentCode(null);
                } else {
                    travelAlertDTO.setProvinceCode(null);
                    travelAlertDTO.setAccidentCityCode(null);
                    travelAlertDTO.setAccidentCountyCode(null);
                    travelAlertDTO.setAccidentContinentCode(travelAlertVO.getAccidentContinentCode());
                }
                travelAlertDTO.setAccidentPlace(travelAlertVO.getAccidentPlace());
                travelAlertDTO.setTaskCode(taskCode);
                travelAlertDTO.setStatus(travelAlertVO.getStatus());
                travelAlertDTO.setReportNo(reportNo);
                travelAlertDTO.setCaseTimes(caseTimes);
                travelAlertDTO.setIdAhcsChannelProcess(channelId);
                travelAlertDTO.setCreatedBy(loginUm);
                travelAlertDTO.setUpdatedBy(loginUm);
                travelAlertDTO.setStatus(status);

                generateTravelAlertDTO(travelAlertVO, reportNo, caseTimes, loginUm, travelAlertDTO);

            }
        } else {
            travelAlertList = new ArrayList<>();
            TravelAlertDTO travelAlertDTO = new TravelAlertDTO();
            travelAlertDTO.setAccidentOverseas(travelAlertVO.getAccidentOverseas());
            if (ConstValues.ZERO_STR.equals(travelAlertVO.getAccidentOverseas())) {
                travelAlertDTO.setProvinceCode(travelAlertVO.getProvinceCode());
                travelAlertDTO.setAccidentCityCode(travelAlertVO.getAccidentCityCode());
                travelAlertDTO.setAccidentCountyCode(travelAlertVO.getAccidentCountyCode());
                travelAlertDTO.setAccidentContinentCode(null);
            } else {
                travelAlertDTO.setProvinceCode(null);
                travelAlertDTO.setAccidentCityCode(null);
                travelAlertDTO.setAccidentCountyCode(null);
                travelAlertDTO.setAccidentContinentCode(travelAlertVO.getAccidentContinentCode());
            }
            travelAlertDTO.setAccidentPlace(travelAlertVO.getAccidentPlace());
            travelAlertDTO.setTaskCode(taskCode);
            travelAlertDTO.setStatus(travelAlertVO.getStatus());
            travelAlertDTO.setReportNo(reportNo);
            travelAlertDTO.setCaseTimes(caseTimes);
            travelAlertDTO.setIdAhcsChannelProcess(channelId);
            travelAlertDTO.setCreatedBy(loginUm);
            travelAlertDTO.setUpdatedBy(loginUm);
            travelAlertDTO.setStatus(status);

            generateTravelAlertDTO(travelAlertVO, reportNo, caseTimes, loginUm, travelAlertDTO);

            travelAlertList.add(travelAlertDTO);
        }
        travelAlertService.saveTravelAlert(travelAlertList, reportNo, caseTimes, taskCode, channelId);

        saveTravelAlertInvoiceDTO(travelAlertVO, channelId, loginUm, reportNo, caseTimes);
    }

    private void saveTravelAlertInvoiceDTO(TravelAlertVO travelAlertVO, String channelId, String loginUm, String reportNo, Integer caseTimes) {
        if (ListUtils.isNotEmpty(travelAlertVO.getTravelAlertInvoiceList())) {
            List<TravelAlertInvoiceDTO> travelAlertInvoiceList = new ArrayList<>();
            TravelAlertInvoiceDTO travelAlertInvoiceDTO = null;
            for (TravelAlertInvoiceVO travelAlertInvoiceVO : travelAlertVO.getTravelAlertInvoiceList()) {
                travelAlertInvoiceDTO = new TravelAlertInvoiceDTO();
                BeanUtils.copyProperties(travelAlertInvoiceVO, travelAlertInvoiceDTO);
                travelAlertInvoiceDTO.setIdAhcsChannelProcess(channelId);
                travelAlertInvoiceDTO.setCreatedBy(loginUm);
                travelAlertInvoiceDTO.setUpdatedBy(loginUm);
                travelAlertInvoiceDTO.setReportNo(reportNo);
                travelAlertInvoiceDTO.setCaseTimes(caseTimes);
                travelAlertInvoiceList.add(travelAlertInvoiceDTO);
            }
            if (ListUtils.isNotEmpty(travelAlertInvoiceList)) {
                travelAlertInvoiceDao.deleTetravelAlertInvoiceForReportNo(reportNo, channelId);
                travelAlertInvoiceDao.saveTravelAlertInvoiceList(travelAlertInvoiceList);
            }
        }
    }

    private void generateTravelAlertDTO(TravelAlertVO travelAlertVO, String reportNo, Integer caseTimes, String loginUm, TravelAlertDTO travelAlertDTO) {
        travelAlertDTO.setDepartPlaceProvinceCode(travelAlertVO.getDepartPlaceProvinceCode());
        travelAlertDTO.setDepartPlaceCityCode(travelAlertVO.getDepartPlaceCityCode());
        travelAlertDTO.setDepartPlaceCountyCode(travelAlertVO.getDepartPlaceCountyCode());
        travelAlertDTO.setDepartPlacePlace(travelAlertVO.getDepartPlacePlace());
        travelAlertDTO.setArrivalPlaceProvinceCode(travelAlertVO.getArrivalPlaceProvinceCode());
        travelAlertDTO.setArrivalPlaceCityCode(travelAlertVO.getArrivalPlaceCityCode());
        travelAlertDTO.setArrivalPlaceCountyCode(travelAlertVO.getArrivalPlaceCountyCode());
        travelAlertDTO.setArrivalPlacePlace(travelAlertVO.getArrivalPlacePlace());
        travelAlertDTO.setdAccidentOverseas(travelAlertVO.getdAccidentOverseas());
        travelAlertDTO.setdAccidentContinentCode(travelAlertVO.getdAccidentContinentCode());
        travelAlertDTO.setdAccidentContinentPlace(travelAlertVO.getdAccidentContinentPlace());
        travelAlertDTO.setaAccidentOverseas(travelAlertVO.getaAccidentOverseas());
        travelAlertDTO.setaAccidentContinentCode(travelAlertVO.getaAccidentContinentCode());
        travelAlertDTO.setaAccidentContinentPlace(travelAlertVO.getaAccidentContinentPlace());
        travelAlertDTO.setOrderNo(travelAlertVO.getOrderNo());
        travelAlertDTO.setAccidentReason(travelAlertVO.getAccidentReason());
    }

    private void savePropertyLossInfo(PropertyLossVO propertyLossVO, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskCode, String status) {
        List<PropertyLossDTO> propertyLossList = propertyLossVO.getPropertyLossList();
        if (ListUtils.isNotEmpty(propertyLossList)) {
            for (PropertyLossDTO propertyLossDTO : propertyLossList) {
                propertyLossDTO.setAccidentOverseas(propertyLossVO.getAccidentOverseas());
                if (ConstValues.ZERO_STR.equals(propertyLossVO.getAccidentOverseas())) {
                    propertyLossDTO.setProvinceCode(propertyLossVO.getProvinceCode());
                    propertyLossDTO.setAccidentCityCode(propertyLossVO.getAccidentCityCode());
                    propertyLossDTO.setAccidentCountyCode(propertyLossVO.getAccidentCountyCode());
                    propertyLossDTO.setAccidentContinentCode(null);
                } else {
                    propertyLossDTO.setProvinceCode(null);
                    propertyLossDTO.setAccidentCityCode(null);
                    propertyLossDTO.setAccidentCountyCode(null);
                    propertyLossDTO.setAccidentContinentCode(propertyLossVO.getAccidentContinentCode());
                }
                propertyLossDTO.setProvinceCode(propertyLossVO.getProvinceCode());
                propertyLossDTO.setAccidentContinentCode(propertyLossVO.getAccidentContinentCode());
                propertyLossDTO.setAccidentCityCode(propertyLossVO.getAccidentCityCode());
                propertyLossDTO.setAccidentCountyCode(propertyLossVO.getAccidentCountyCode());
                propertyLossDTO.setAccidentPlace(propertyLossVO.getAccidentPlace());
                propertyLossDTO.setTaskCode(taskCode);
                propertyLossDTO.setStatus(propertyLossVO.getStatus());
                propertyLossDTO.setReportNo(reportNo);
                propertyLossDTO.setCaseTimes(caseTimes);
                propertyLossDTO.setIdAhcsChannelProcess(channelId);
                propertyLossDTO.setCreatedBy(loginUm);
                propertyLossDTO.setUpdatedBy(loginUm);
                propertyLossDTO.setStatus(status);
                propertyLossDTO.setThirdPropertyLossAmount(propertyLossVO.getThirdPropertyLossAmount());
                propertyLossDTO.setAccidentDate(propertyLossVO.getAccidentDate());
                propertyLossDTO.setAccidentCauseCode(propertyLossVO.getAccidentCauseCode());
                propertyLossDTO.setOtherAccidentCause(propertyLossVO.getOtherAccidentCause());
            }
        } else {
            propertyLossList = new ArrayList<>();
            PropertyLossDTO propertyLossDTO = new PropertyLossDTO();
            propertyLossDTO.setAccidentOverseas(propertyLossVO.getAccidentOverseas());
            if (ConstValues.ZERO_STR.equals(propertyLossVO.getAccidentOverseas())) {
                propertyLossDTO.setProvinceCode(propertyLossVO.getProvinceCode());
                propertyLossDTO.setAccidentCityCode(propertyLossVO.getAccidentCityCode());
                propertyLossDTO.setAccidentCountyCode(propertyLossVO.getAccidentCountyCode());
                propertyLossDTO.setAccidentContinentCode(null);
            } else {
                propertyLossDTO.setProvinceCode(null);
                propertyLossDTO.setAccidentCityCode(null);
                propertyLossDTO.setAccidentCountyCode(null);
                propertyLossDTO.setAccidentContinentCode(propertyLossVO.getAccidentContinentCode());
            }
            propertyLossDTO.setAccidentPlace(propertyLossVO.getAccidentPlace());
            propertyLossDTO.setTaskCode(taskCode);
            propertyLossDTO.setStatus(propertyLossVO.getStatus());
            propertyLossDTO.setReportNo(reportNo);
            propertyLossDTO.setCaseTimes(caseTimes);
            propertyLossDTO.setIdAhcsChannelProcess(channelId);
            propertyLossDTO.setCreatedBy(loginUm);
            propertyLossDTO.setUpdatedBy(loginUm);
            propertyLossDTO.setStatus(status);
            propertyLossDTO.setThirdPropertyLossAmount(propertyLossVO.getThirdPropertyLossAmount());
            propertyLossDTO.setAccidentDate(propertyLossVO.getAccidentDate());
            propertyLossDTO.setAccidentCauseCode(propertyLossVO.getAccidentCauseCode());
            propertyLossDTO.setOtherAccidentCause(propertyLossVO.getOtherAccidentCause());
            propertyLossList.add(propertyLossDTO);
        }
        propertyLossService.savePropertyLoss(propertyLossList, reportNo, caseTimes, taskCode, channelId);
    }

    private void saveOtherLossDTOInfo(OtherLossVO otherLossVO, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskCode, String status) {
        OtherLossDTO otherLossDTO = new OtherLossDTO();
        BeanUtils.copyProperties(otherLossVO, otherLossDTO);
        otherLossDTO.setTaskCode(taskCode);
        otherLossDTO.setReportNo(reportNo);
        otherLossDTO.setCaseTimes(caseTimes);
        otherLossDTO.setIdAhcsChannelProcess(channelId);
        otherLossDTO.setCreatedBy(loginUm);
        otherLossDTO.setUpdatedBy(loginUm);
        otherLossDTO.setStatus(status);
        otherLossDTO.setIdAhcsOtherLossDTO(UuidUtil.getUUID());
        if (ConstValues.ZERO_STR.equals(otherLossDTO.getAccidentOverseas())) {
            otherLossDTO.setAccidentContinentCode(null);
        } else {
            otherLossDTO.setProvinceCode(null);
            otherLossDTO.setAccidentCityCode(null);
            otherLossDTO.setAccidentCountyCode(null);
        }
        otherLossService.saveOtherLoss(otherLossDTO);
    }

    private BaggageDelayVO getBaggageDelay(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
        BaggageDelayVO baggageDelayVO = new BaggageDelayVO();
        BaggageDelayDTO baggageDelayDTO = baggageDelayService.getBaggageDelay(reportNo, caseTimes, null, taskCode, channelProcessId);
        if (baggageDelayDTO != null) {
            BeanUtils.copyProperties(baggageDelayDTO, baggageDelayVO);
        }
        if (StringUtils.isEmptyStr(baggageDelayVO.getDelayDurationUnit())) {
            baggageDelayVO.setDelayDurationUnit(ConstValues.TIME_UNIT_MIN);
        }
        baggageDelayVO.setModuleCode(ChecklossConst.BAGGAGE_DELAY);
        return baggageDelayVO;
    }

    private ExaminFailVO getExaminFail(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
        ExaminFailVO examinFailVO = new ExaminFailVO();
        ExaminFailDTO examinFailDTO = examinFailService.getExaminFail(reportNo, caseTimes, null, taskCode, channelProcessId);
        if (examinFailDTO != null) {
            BeanUtils.copyProperties(examinFailDTO, examinFailVO);
        }
        String failType = examinFailVO.getFailType();
        if (StringUtils.isNotEmpty(failType)) {
            examinFailVO.setFailTypeArr(failType.split(","));
        } else {
            examinFailVO.setFailTypeArr(new String[]{});
        }
        examinFailVO.setModuleCode(ChecklossConst.EXAMIN_FAIL);
        return examinFailVO;
    }

    private List<OtherLossVO> getOtherLoss(String reportNo, Integer caseTimes) {
        List<OtherLossDTO> otherLoss = otherLossService.getOtherLoss(reportNo, caseTimes);
        List<OtherLossVO> otherLossVOList = new ArrayList<>();
        otherLoss.forEach(otherLossDTO -> {
            OtherLossVO otherLossVO = new OtherLossVO();
            BeanUtils.copyProperties(otherLossDTO, otherLossVO);
            otherLossVOList.add(otherLossVO);
        });
        return otherLossVOList;
    }

    private PropertyLossVO getPropertyLoss(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
        PropertyLossVO propertyLossVO = new PropertyLossVO();
        List<String> accidentTypeList = new ArrayList<>();
        List<PropertyLossDTO> propertyLossList = propertyLossService.getPropertyLoss(reportNo, caseTimes, null, taskCode, channelProcessId);
        if (ListUtils.isEmptyList(propertyLossList)) {
            propertyLossList = new ArrayList<>();
        } else {
            PropertyLossDTO propertyLossDTO = propertyLossList.get(0);
            propertyLossVO.setAccidentOverseas(propertyLossDTO.getAccidentOverseas());
            propertyLossVO.setProvinceCode(propertyLossDTO.getProvinceCode());
            propertyLossVO.setAccidentCityCode(propertyLossDTO.getAccidentCityCode());
            propertyLossVO.setAccidentCountyCode(propertyLossDTO.getAccidentCountyCode());
            propertyLossVO.setAccidentContinentCode(propertyLossDTO.getAccidentContinentCode());
            propertyLossVO.setAccidentPlace(propertyLossDTO.getAccidentPlace());
            propertyLossVO.setThirdPropertyLossAmount(propertyLossDTO.getThirdPropertyLossAmount());
            propertyLossVO.setAccidentDate(propertyLossDTO.getAccidentDate());
            propertyLossVO.setAccidentCauseCode(propertyLossDTO.getAccidentCauseCode());
            propertyLossVO.setOtherAccidentCause(propertyLossDTO.getOtherAccidentCause());
            if (propertyLossList.size() == 1 && StringUtils.isEmptyStr(propertyLossDTO.getAccidentType())) {
                propertyLossList = new ArrayList<>();
            }

            List<PropertyLossDTO> propertyLossItemList = new ArrayList<>();
            for (PropertyLossDTO propertyLoss : propertyLossList) {
                String accidentType = propertyLoss.getAccidentType();
                if (StringUtils.isNotEmpty(accidentType)) {
                    accidentTypeList.add(accidentType);
                    PropertyLossDTO lossDTO = new PropertyLossDTO();
                    lossDTO.setUpdatedDate(null);
                    lossDTO.setCreatedDate(null);
                    lossDTO.setAccidentType(accidentType);
                    lossDTO.setLossAmount(propertyLoss.getLossAmount());
                    propertyLossItemList.add(lossDTO);
                }
            }
            propertyLossList = propertyLossItemList;
        }
        propertyLossVO.setAccidentTypeList(accidentTypeList);
        propertyLossVO.setPropertyLossList(propertyLossList);
        propertyLossVO.setModuleCode(ChecklossConst.PROPERTY_LOSS);
        propertyLossVO.setShowThirdProperty("Y");
//        String[] element1List = {"element1_065"};
//        String insuredApplyStatus = SettleConst.INSURED_CONFIG_PROPERTY_LOSS;
//        Integer num = policyDutyDetailDAO.getElementCode1ByReportNoAndAccidentType(reportNo, insuredApplyStatus, null, element1List);
//        if (num > 0) {
//        } else {
//            propertyLossVO.setShowThirdProperty("N");
//        }
        return propertyLossVO;
    }

    private TravelAlertVO getTravelAlert(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
        TravelAlertVO travelAlertVO = new TravelAlertVO();
        List<String> accidentTypeList = new ArrayList<>();
        List<TravelAlertDTO> travelAlertlist = travelAlertService.getTravelAlert(reportNo, caseTimes, null, taskCode, channelProcessId);
        if (ListUtils.isEmptyList(travelAlertlist)) {
            travelAlertlist = new ArrayList<>();
            List<TravelAlertInvoiceVO> travelAlertInvoiceVoList = new ArrayList<>();
            TravelAlertInvoiceVO travelAlertInvoiceVO = new TravelAlertInvoiceVO();
            travelAlertInvoiceVO.setReportNo(reportNo);
            travelAlertInvoiceVO.setInvoiceType("0");
            travelAlertInvoiceVoList.add(travelAlertInvoiceVO);
            travelAlertVO.setTravelAlertInvoiceList(travelAlertInvoiceVoList);
        } else {
            for (TravelAlertDTO travelAlertDTO : travelAlertlist) {
                if (StringUtils.isNotEmpty(travelAlertDTO.getAccidentType())) {
                    accidentTypeList.add(travelAlertDTO.getAccidentType());
                }
            }
            TravelAlertDTO travelAlertDTO = travelAlertlist.get(0);
            travelAlertVO.setAccidentOverseas(travelAlertDTO.getAccidentOverseas());
            travelAlertVO.setProvinceCode(travelAlertDTO.getProvinceCode());
            travelAlertVO.setAccidentCityCode(travelAlertDTO.getAccidentCityCode());
            travelAlertVO.setAccidentCountyCode(travelAlertDTO.getAccidentCountyCode());
            travelAlertVO.setAccidentContinentCode(travelAlertDTO.getAccidentContinentCode());
            travelAlertVO.setAccidentPlace(travelAlertDTO.getAccidentPlace());
            travelAlertVO.setdAccidentOverseas(travelAlertDTO.getdAccidentOverseas());
            travelAlertVO.setDepartPlaceProvinceCode(travelAlertDTO.getDepartPlaceProvinceCode());
            travelAlertVO.setDepartPlaceCityCode(travelAlertDTO.getDepartPlaceCityCode());
            travelAlertVO.setDepartPlaceCountyCode(travelAlertDTO.getDepartPlaceCountyCode());
            travelAlertVO.setDepartPlacePlace(travelAlertDTO.getDepartPlacePlace());
            travelAlertVO.setdAccidentContinentCode(travelAlertDTO.getdAccidentContinentCode());
            travelAlertVO.setdAccidentContinentPlace(travelAlertDTO.getdAccidentContinentPlace());
            travelAlertVO.setaAccidentOverseas(travelAlertDTO.getaAccidentOverseas());
            travelAlertVO.setArrivalPlaceProvinceCode(travelAlertDTO.getArrivalPlaceProvinceCode());
            travelAlertVO.setArrivalPlaceCityCode(travelAlertDTO.getArrivalPlaceCityCode());
            travelAlertVO.setArrivalPlaceCountyCode(travelAlertDTO.getArrivalPlaceCountyCode());
            travelAlertVO.setArrivalPlacePlace(travelAlertDTO.getArrivalPlacePlace());
            travelAlertVO.setaAccidentContinentCode(travelAlertDTO.getaAccidentContinentCode());
            travelAlertVO.setaAccidentContinentPlace(travelAlertDTO.getaAccidentContinentPlace());
            travelAlertVO.setOrderNo(travelAlertDTO.getOrderNo());
            travelAlertVO.setAccidentReason(travelAlertDTO.getAccidentReason());

            List<TravelAlertInvoiceDTO> travelAlertInvoiceList = travelAlertInvoiceDao.getTravelAlertInvoiceList(reportNo);
            List<TravelAlertInvoiceVO> travelAlertInvoiceVoList = new ArrayList<>();
            if (ListUtils.isNotEmpty(travelAlertInvoiceList)) {
                for (TravelAlertInvoiceDTO travelAlertInvoiceDTO : travelAlertInvoiceList) {
                    TravelAlertInvoiceVO travelAlertInvoiceVO = new TravelAlertInvoiceVO();
                    BeanUtils.copyProperties(travelAlertInvoiceDTO, travelAlertInvoiceVO);
                    travelAlertInvoiceVoList.add(travelAlertInvoiceVO);
                }
                travelAlertVO.setTravelAlertInvoiceList(travelAlertInvoiceVoList);
            } else {
                TravelAlertInvoiceVO travelAlertInvoiceVO = new TravelAlertInvoiceVO();
                travelAlertInvoiceVO.setReportNo(reportNo);
                travelAlertInvoiceVO.setInvoiceType("0");
                travelAlertInvoiceVoList.add(travelAlertInvoiceVO);
                travelAlertVO.setTravelAlertInvoiceList(travelAlertInvoiceVoList);
            }
            if (travelAlertlist.size() == 1 && StringUtils.isEmptyStr(travelAlertDTO.getAccidentType())) {
                travelAlertlist = new ArrayList<>();
            }
        }
        travelAlertVO.setAccidentTypeList(accidentTypeList);
        travelAlertVO.setTravelAlertlist(travelAlertlist);
        travelAlertVO.setModuleCode(ChecklossConst.TRAVEL_ALERT);

        return travelAlertVO;
    }

    private VehiclDelayOtherVO getVehiclDelayOther(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
        VehiclDelayOtherVO vehiclDelayOtherVO = new VehiclDelayOtherVO();
        VehiclDelayOtherDTO vehiclDelayOtherDTO = vehiclDelayOtherService.getVehiclDelayOther(reportNo, caseTimes, null, taskCode, channelProcessId);
        if (vehiclDelayOtherDTO != null) {
            BeanUtils.copyProperties(vehiclDelayOtherDTO, vehiclDelayOtherVO);
        }
        String cruisesDelayDesc = vehiclDelayOtherVO.getCruisesDelayDesc();
        if (StringUtils.isNotEmpty(cruisesDelayDesc)) {
            String[] cruisesDelayDescArr = cruisesDelayDesc.split(",");
            vehiclDelayOtherVO.setCruisesDelayDescArr(cruisesDelayDescArr);
        } else {
            vehiclDelayOtherVO.setCruisesDelayDescArr(new String[]{});
        }
        vehiclDelayOtherVO.setModuleCode(ChecklossConst.VEHICL_DELAY_OTHER);
        if (StringUtils.isEmptyStr(vehiclDelayOtherVO.getAlertDurationUnit())) {
            vehiclDelayOtherVO.setAlertDurationUnit(ConstValues.TIME_UNIT_MIN);
        }
        if (StringUtils.isEmptyStr(vehiclDelayOtherVO.getDelayDurationUnit())) {
            vehiclDelayOtherVO.setDelayDurationUnit(ConstValues.TIME_UNIT_MIN);
        }
        return vehiclDelayOtherVO;
    }

    private List<OtherLossPptDTO> getOtherLossPpt(String reportNo, Integer caseTimes, String taskCode) {
        List<OtherLossPptDTO> otherLossPptDTOs = otherLossPptService.getOtherLossPptListByReportNo(reportNo, caseTimes, null, taskCode);
        if (otherLossPptDTOs == null) {
            otherLossPptDTOs = new ArrayList<>();
        }
        for (OtherLossPptDTO otherLossPptDTO : otherLossPptDTOs) {
            otherLossPptDTO.setCreatedDate(null);
            otherLossPptDTO.setUpdatedDate(null);
        }
        return otherLossPptDTOs;
    }

    private void savePersonBenefit(PersonBenefitVO personBenefitVO, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskId, String status) {
        List<List<PersonBenefitDTO>> personBenefitList = personBenefitVO.getPersonBenefitList();
        personBenefitService.savePersonBenefit(personBenefitList, reportNo, caseTimes, channelId, loginUm, taskId, status);

    }

    @Override
    public void savePersonRescue(PersonRescueVO personRescueVO, String reportNo, Integer caseTimes, String loginUm, String taskId, String status) {
        List<PersonRescueDTO> personRescueList = personRescueVO.getPersonRescueList();
        if (ListUtils.isNotEmpty(personRescueList)) {
            Iterator<PersonRescueDTO> it = personRescueList.iterator();
            while (it.hasNext()) {
                PersonRescueDTO personRescueDTO = it.next();
                if (personRescueDTO.getAmount() == null || personRescueDTO.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                    it.remove();
                } else {
                    personRescueDTO.setCreatedBy(loginUm);
                    personRescueDTO.setUpdatedBy(loginUm);
                    personRescueDTO.setReportNo(reportNo);
                    personRescueDTO.setCaseTimes(caseTimes);
                    personRescueDTO.setTaskId(taskId);
                    personRescueDTO.setStatus(status);
                }
            }
        }
        personRescueService.savePersonRescue(personRescueList, reportNo, caseTimes, taskId);
    }

    private void savePersonOtherLoss(PersonOtherLossVO personOtherLossVO, String reportNo, Integer caseTimes, String idAhcsChannelProcess, String loginUm, String taskId, String status) {
        List<PersonOtherLossDTO> personOtherLossList = personOtherLossVO.getPersonOtherLossList();
        if (ListUtils.isNotEmpty(personOtherLossList)) {
            for (PersonOtherLossDTO personOtherLossDTO : personOtherLossList) {
                personOtherLossDTO.setCreatedBy(loginUm);
                personOtherLossDTO.setUpdatedBy(loginUm);
                personOtherLossDTO.setReportNo(reportNo);
                personOtherLossDTO.setIdAhcsChannelProcess(idAhcsChannelProcess);
                personOtherLossDTO.setCaseTimes(caseTimes);
                personOtherLossDTO.setTaskId(taskId);
                personOtherLossDTO.setStatus(status);
            }
        }
        personOtherLossService.savePersonOtherLoss(personOtherLossList, reportNo, caseTimes, taskId, idAhcsChannelProcess);
    }

    private void saveOtherLossPpt(List<OtherLossPptDTO> otherLossPptDTOs, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskCode, String status) {
        for (OtherLossPptDTO otherLossPptDTO : otherLossPptDTOs) {
            otherLossPptDTO.setReportNo(reportNo);
            otherLossPptDTO.setStatus(status);
            otherLossPptDTO.setTaskCode(taskCode);
            otherLossPptDTO.setIdOtherLossPpt(UuidUtil.getUUID());
        }
        otherLossPptService.addOtherLossPptList(otherLossPptDTOs, caseTimes, loginUm, channelId);
    }

    @SuppressWarnings("unchecked")
    private PersonBenefitVO getPersonBenefit(String reportNo, Integer caseTimes, String taskId, String channelProcessId) {
        PersonBenefitVO personBenefitVO = new PersonBenefitVO();
        Map<String, Object> retMap = personBenefitService.getPersonBenefit(reportNo, caseTimes, taskId, channelProcessId);
        if (retMap != null) {
            Object personBenefitListObj = retMap.get("personBenefitList");
            Object benefitTypes = retMap.get("benefitTypes");
            if (personBenefitListObj != null) {
                if (ListUtils.isEmptyList((List<List<PersonBenefitDTO>>) personBenefitListObj)) {
                    List<List<PersonBenefitDTO>> personBenefitList = new ArrayList<>();
                    personBenefitVO.setPersonBenefitList(personBenefitList);
                } else {
                    personBenefitVO.setPersonBenefitList((List<List<PersonBenefitDTO>>) personBenefitListObj);
                }
            }
            if (benefitTypes != null) {
                if (ListUtils.isNotEmpty((List<String>) benefitTypes)) {
                    personBenefitVO.setBenefitTypes((List<String>) benefitTypes);
                }
            }
        }
        personBenefitVO.setModuleCode(ChecklossConst.PERSON_BENEFIT);
        return personBenefitVO;
    }

    private PersonRescueVO getPersonRescue(String reportNo, Integer caseTimes, String taskId) {
        return personRescueService.getPersonRescueVO(reportNo, caseTimes, null, taskId);
    }

    private PersonOtherLossVO getPersonOtherLoss(String reportNo, Integer caseTimes, String taskId, String channelProcessId) throws GlobalBusinessException {
        PersonOtherLossVO personOtherLossVO = new PersonOtherLossVO();
        List<PersonOtherLossDTO> initList = new ArrayList<>();
        Map<String, PersonOtherLossDTO> personOtherLossMap = new HashMap<>();
        List<PersonOtherLossDTO> personOtherLossList = personOtherLossService.getPersonOtherLoss(reportNo, caseTimes, null, taskId, channelProcessId);
//        if (ListUtils.isNotEmpty(personOtherLossList)) {
//            for (PersonOtherLossDTO personOtherLossDTO : personOtherLossList) {
//                personOtherLossMap.put(personOtherLossDTO.getCaseType(), personOtherLossDTO);
//            }
//        }
        String[] strArray = {"AHCS_OL_CASE_TYPE"};
        List<CommonParameterTinyDTO> parameterList = commonParameterMapper.getCommonParameterList(strArray);
//        for (CommonParameterTinyDTO commonParameterTinyDTO : parameterList) {
//            PersonOtherLossDTO personOtherLossDTO = personOtherLossMap.get(commonParameterTinyDTO.getValueCode());
//            if (personOtherLossDTO != null) {
//                initList.add(personOtherLossDTO);
//            }
//        }
        // 修改过滤逻辑，使用 filter 和 anyMatch 来简化代码
        initList = personOtherLossList.stream()
                .filter(personOtherLossDTO -> parameterList.stream()
                        .anyMatch(param -> param.getValueCode().equals(personOtherLossDTO.getCaseType())))
                .collect(Collectors.toList());
        personOtherLossVO.setPersonOtherLossList(initList);
        personOtherLossVO.setModuleCode(ChecklossConst.PERSON_OTHER_LOSS);
        return personOtherLossVO;
    }

    @Override
    public String getChannelProcessId(String reportNo, Integer caseTimes, String partyNo, String channelType, String userId) {
        CaseInfoParameterDTO caseInfoParameter = new CaseInfoParameterDTO();
        caseInfoParameter.setReportNo(reportNo);
        caseInfoParameter.setPartyNo(partyNo);
        caseInfoParameter.setCaseTimes(caseTimes);
        caseInfoParameter.setChannelType(channelType);
        caseInfoParameter.setUserId(userId);
        return channelProcessService.getChannelProcessId(caseInfoParameter);
    }

    @Override
    public List<Map<String, String>> getAccidentTypeParameter(String reportNo, String collectionCode) throws GlobalBusinessException {
        List<Map<String, String>> paramList = new ArrayList<>();
        String insuredApplyStatus = SettleConst.AHCS_TRAVEL_ACC_TYPE.equals(collectionCode) ? SettleConst.INSURED_CONFIG_TRAVEL_CHANGE : SettleConst.INSURED_CONFIG_PROPERTY_LOSS;
        String[] element1Arr = {"", ""};
        List<CommonParameterTinyDTO> commonList = commonService.getCommonParameterList(new String[]{collectionCode});
        for (CommonParameterTinyDTO param : commonList) {
            String valueCode = param.getValueCode();
            Map<String, String> map = new HashMap<>();
            map.put("valueCode", param.getValueCode());
            map.put("valueChineseName", param.getValueChineseName());
            map.put("valueChineseAbbrName", param.getValueChineseAbbrName());
            map.put("showReason", "N");
            Set<String> mappingCodes = SettleConst.ACC_TYPE_MAP.get(collectionCode).get(valueCode);
            if (mappingCodes != null && mappingCodes.size() > 0) {
                String[] element2Arr = new String[mappingCodes.size()];
                mappingCodes.toArray(element2Arr);
                int existCount = policyDutyDetailDAO.getElementCode1ByReportNoAndAccidentType(reportNo, insuredApplyStatus, element2Arr, element1Arr);
                if (existCount > 0) {
                    map.put("showReason", "Y");
                }
            }
            paramList.add(map);
        }
        return paramList;
    }

    private PeopleHurtVO getPeopleHurtVOData(String reportNo, int caseTimes, String tacheName) throws GlobalBusinessException {
        PeopleHurtVO peopleHurtVO = new PeopleHurtVO();
        peopleHurtVO.setAccidentVO(getPersonAccidentDate(reportNo, caseTimes, tacheName));
        PersonDiseaseDTO personDiseaseDTO = personDiseaseDao.getPersonDiseaseByReportNo(reportNo, caseTimes, "", tacheName);
        if (personDiseaseDTO == null) {
            personDiseaseDTO = new PersonDiseaseDTO();
        }
        PersonDiseaseVO personDiseaseVO = new PersonDiseaseVO();
        BeanUtils.copyProperties(personDiseaseDTO, personDiseaseVO);
        personDiseaseVO.setModuleCode(ChecklossConst.PERSON_DISEASE);
        peopleHurtVO.setDiseaseVO(personDiseaseVO);
        List<PersonDiagnoseDTO> personDiagnoseDTOs = personDiagnoseDao.getPersonDiagnoseListByReportNo(reportNo, caseTimes, tacheName);
        if (personDiagnoseDTOs == null || personDiagnoseDTOs.isEmpty()) {
            personDiagnoseDTOs = new ArrayList<PersonDiagnoseDTO>();
            PersonDiagnoseDTO personDiagnoseDTO = new PersonDiagnoseDTO();
            personDiagnoseDTOs.add(personDiagnoseDTO);
        }
        PersonDiagnoseVO personDiagnoseVO = new PersonDiagnoseVO();
        personDiagnoseVO.setModuleCode(ChecklossConst.PERSON_DIAGNOSE);
        personDiagnoseVO.setDiagnoseDTOs(personDiagnoseDTOs);
        peopleHurtVO.setDiagnoseVO(personDiagnoseVO);
        List<PersonDisabilityDTO> personDisabilityDTOs = personDisabilityDao.getDisabilityListByReportNo(reportNo, caseTimes, "", tacheName);
        if (personDisabilityDTOs == null || personDisabilityDTOs.isEmpty()) {
            personDisabilityDTOs = new ArrayList<PersonDisabilityDTO>();
            PersonDisabilityDTO personDisabilityDTO = new PersonDisabilityDTO();
            personDisabilityDTOs.add(personDisabilityDTO);
        }
        PersonDisabilityVO personDisabilityVO = new PersonDisabilityVO();
        personDisabilityVO.setModuleCode(ChecklossConst.PERSON_DISABILITY);
        personDisabilityVO.setDisabilityDTOs(personDisabilityDTOs);
        peopleHurtVO.setDisabilityVO(personDisabilityVO);
        PersonDeathDTO personDeathDTO = personDeathDao.getPersonDeathByReportNo(reportNo, String.valueOf(caseTimes), tacheName, "");
        if (personDeathDTO == null) {
            personDeathDTO = new PersonDeathDTO();
        }
        PersonDeathVO personDeathVO = new PersonDeathVO();
        BeanUtils.copyProperties(personDeathDTO, personDeathVO);
        personDeathVO.setModuleCode(ChecklossConst.PERSON_DEATH);
        peopleHurtVO.setDeathVO(personDeathVO);
        BigDiseaseDTO bigDiseaseDTO = bigDiseaseDao.getBigDiseaseDTOByReportNo(reportNo, caseTimes, "", tacheName);
        if (bigDiseaseDTO == null) {
            bigDiseaseDTO = new BigDiseaseDTO();
        }
        BigDiseaseVO bigDiseaseVO = new BigDiseaseVO();
        BeanUtils.copyProperties(bigDiseaseDTO, bigDiseaseVO);
        bigDiseaseVO.setModuleCode(ChecklossConst.PERSON_BIG_DISEASE);
        peopleHurtVO.setBigDiseaseVO(bigDiseaseVO);
        if (null != bigDiseaseVO) {
            peopleHurtVO.setBigDiseaseDetailCode(getBigDiseaseDetailList(bigDiseaseVO.getBigDiseaseId()));
        }
        peopleHurtVO.setPersonBenefitVO(this.getPersonBenefit(reportNo, caseTimes, tacheName, null));
        peopleHurtVO.setPersonOtherLossVO(this.getPersonOtherLoss(reportNo, caseTimes, tacheName, null));
        return peopleHurtVO;
    }

    private NoPeopleHurtVO getNoPeopleHurtData(String reportNo, int caseTimes, String tacheName) {
        NoPeopleHurtVO noPeopleHurtVO = new NoPeopleHurtVO();
       /* noPeopleHurtVO.setBaggageDelayVO(this.getBaggageDelay(reportNo, caseTimes, tacheName, null));
        noPeopleHurtVO.setExaminFailVO(this.getExaminFail(reportNo, caseTimes, tacheName, null));
        noPeopleHurtVO.setFlightDelayVO(this.getFlightDelayData(reportNo, caseTimes, tacheName));
        noPeopleHurtVO.setOtherLossVO(this.getOtherLoss(reportNo, caseTimes, tacheName, null));
        noPeopleHurtVO.setPropertyLossVO(this.getPropertyLoss(reportNo, caseTimes, tacheName, null));
        noPeopleHurtVO.setTravelAlertVO(this.getTravelAlert(reportNo, caseTimes, tacheName, null));
        noPeopleHurtVO.setVehiclDelayOtherVO(this.getVehiclDelayOther(reportNo, caseTimes, tacheName, null));*/
        return noPeopleHurtVO;
    }

    private FlightDelayVO getFlightDelayData(String reportNo, Integer caseTimes, String taskCode) {
        FlightDelayVO flightDelayVO = new FlightDelayVO();
        String flightStatus = flightDelayVO.getFlightStatus();
        if (StringUtils.isNotEmpty(flightStatus)) {
            flightDelayVO.setFlightStatusArr(flightStatus.split(","));
        } else {
            flightDelayVO.setFlightStatusArr(new String[]{});
        }
        if (StringUtils.isEmptyStr(flightDelayVO.getDelayDurationUnit())) {
            flightDelayVO.setDelayDurationUnit(ConstValues.TIME_UNIT_MIN);
        }
        flightDelayVO.setModuleCode(ChecklossConst.FLIGHT_DELAY);
        return flightDelayVO;
    }

    private PersonAccidentVO getPersonAccidentDate(String reportNo, Integer caseTimes, String taskCode) {
        PersonAccidentDTO personAccidentDTO = personAccidentDao.getPersonAccidentInfo(reportNo, String.valueOf(caseTimes), taskCode, "");
        if (personAccidentDTO == null) {
            personAccidentDTO = new PersonAccidentDTO();
        }
        PersonAccidentVO personAccidentVO = new PersonAccidentVO();
        BeanUtils.copyProperties(personAccidentDTO, personAccidentVO);
        String workInjuryTypes = personAccidentDTO.getWorkInjuryType();
        List<String> workInjuryTypeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(workInjuryTypes)) {
            String[] workInjuryTypeArr = workInjuryTypes.split(",");
            for (String workInjuryType : workInjuryTypeArr) {
                if (StringUtils.isNotEmpty(workInjuryType)) {
                    workInjuryTypeList.add(workInjuryType);
                }
            }
        }
        personAccidentVO.setWorkInjuryTypeList(workInjuryTypeList);
        personAccidentVO.setModuleCode(ChecklossConst.PERSON_ACCIDENT);
        return personAccidentVO;
    }

    @Override
    public ResultAmountVO getTotalAmount(DutySurveyVO dutyVO) throws GlobalBusinessException {
        String reportNo = dutyVO.getReportNo();
        ResultAmountVO vo = caseClassDao.getTotalAmount(reportNo);
        return vo;

    }


    private String getConclusionKey(VerifyConclusionVO verifyConclusionVO) {
        String indemnityConclusionStr = verifyConclusionVO.getIndemnityConclusion();
        String indemnityModelStr = verifyConclusionVO.getIndemnityModel();
        String conclusionKey = "";
        if (ChecklossConst.CONCLUSION_VERIFY_PAY.equals(indemnityConclusionStr)) {
            if (ChecklossConst.CONCLUSION_PROTOCOL.equals(indemnityModelStr) || ChecklossConst.CONCLUSION_ACCOMMODATION.equals(indemnityModelStr)) {
                conclusionKey = indemnityConclusionStr + indemnityModelStr;
            } else {
                conclusionKey = indemnityConclusionStr;
            }
        }
        if (ChecklossConst.CONCLUSION_REFUSE_PAY.equals(indemnityConclusionStr)) {
            conclusionKey = indemnityConclusionStr;
        }
        return conclusionKey;
    }

    private void checkPolicyInfo(String reportNo) throws GlobalBusinessException {
        List<PolicyInfoVO> policyInfoVOList = policyInfoMapper.getPolicyStatus(reportNo);
        boolean isInValid = true;
        if (policyInfoVOList != null && policyInfoVOList.size() > 0) {
            for (PolicyInfoVO policyInfoVO : policyInfoVOList) {
                String policyStatus = policyInfoVO.getPolicyStatus();
                if (StringUtil.isNotEmpty(policyStatus)) {
                    if (!ChecklossConst.POLICY_STATUS_CANCEL.equals(policyStatus) && !ChecklossConst.POLICY_STATUS_CONTRACT_CANCEL.equals(policyStatus)
                            && !ChecklossConst.POLICY_STATUS_SURRENDER.equals(policyStatus)) {
                        isInValid = false;
                        break;
                    }
                }
            }
        }
        if (isInValid) {
            LogUtil.info("保单已退保或注销或契撤，不能发送核责/查勘或转实地查勘");
            throw new GlobalBusinessException(ErrorCode.Settle.POLICY_STATUS_INVALID);
        }
    }

    private void isValidSaveForDuty(String indemnityConclusion, String reportNo, Integer caseTimes) throws GlobalBusinessException {
        if (ChecklossConst.VERIFY_REFUSE.equals(indemnityConclusion)) {
            if (prePayMapper.getPrePayCount(reportNo, caseTimes) > 0 && paymentItemMapper.getPrePayCount(reportNo, caseTimes) > 0) {
                throw new GlobalBusinessException("当前案件有预赔记录！不能拒赔！");
            }
            if (!this.canRejectCase(reportNo, caseTimes)) {
                throw new GlobalBusinessException("重开案件不能拒赔！");
            }
        }
    }


    private void saveForSurvey(DutySurveyVO dutySurveyVO, String loginUm, String status) throws GlobalBusinessException {
        if (dutySurveyVO.getEstimatePolicyFormDTO() != null) {
            EstimatePolicyFormDTO estimatePolicyFormDTO = dutySurveyVO.getEstimatePolicyFormDTO();
            saveEstimatePolicy(estimatePolicyFormDTO, loginUm, dutySurveyVO.getStatus());
        }
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        String taskId = dutySurveyVO.getTaskId();
        SurveyVO surveyVO = dutySurveyVO.getSurveyVO();
        SurveyDTO surveyDTO = new SurveyDTO();
        if (surveyVO != null) {
            BeanUtils.copyProperties(surveyVO, surveyDTO);
        }
        surveyDTO.setTaskId(taskId);
        surveyDTO.setStatus(status);
        saveSurvey(reportNo, caseTimes, loginUm, surveyDTO);

    }

    private void saveEstimatePolicy(EstimatePolicyFormDTO estimatePolicyFormDTO, String loginUm, String status) throws GlobalBusinessException {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(loginUm);
        estimatePolicyFormDTO.setUpdatedBy(loginUm);
        if (registerCaseService.isExistRegisterRecord(estimatePolicyFormDTO.getReportNo(), estimatePolicyFormDTO.getCaseTimes())) {
            LogUtil.audit("已立案,不再进行人工立案, reportNo=%s, caseTimes=%s", estimatePolicyFormDTO.getReportNo(), estimatePolicyFormDTO.getCaseTimes());
            return;
        }
        if (ChecklossConst.STATUS_TMP_SUBMIT.equals(status)) {
            String reportNo = estimatePolicyFormDTO.getReportNo();
            Integer caseTimes = estimatePolicyFormDTO.getCaseTimes();
            LogUtil.audit("#开始人工立案。。reportNo=%s", reportNo);
            EstimateUtil.handleEstimatePolicyFormDTO(estimatePolicyFormDTO, userDTO);
            estimateService.modifyEstimateDataList(estimatePolicyFormDTO);
            LogUtil.audit("#结束人工立案。。reportNo=%s", reportNo);
            AcceptRecordDTO acceptRecordDTO = new AcceptRecordDTO();
            String processStatus = null;
            if (acceptRecordDTO != null) {
                processStatus = ConfigConstValues.PROCESS_STATUS_PENDING_AUDIT;
            } else {
                processStatus = ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT;
            }
            LogUtil.audit("#查勘发送后改变案件状态#processStatus=%s,reportNo=%s,caseTimes=%s", processStatus, reportNo, caseTimes);
            caseProcessService.updateCaseProcess(reportNo, caseTimes, processStatus);
        } else {
            estimateService.addEstimateDataList(estimatePolicyFormDTO);
        }
    }

    public RunRoleResultVO saveForDuty(DutySurveyVO dutySurveyVO, String loginUm, String status, String channelProcessId, String conclusionKey) throws GlobalBusinessException {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        String taskId = dutySurveyVO.getTaskId();
        VerifyConclusionDTO vcDTO = new VerifyConclusionDTO();
        vcDTO.setReportNo(reportNo);
        vcDTO.setCaseTimes(caseTimes);
        vcDTO.setTaskCode(taskId);
        vcDTO.setUpdatedBy(loginUm);
        verifyConclusiondao.updateEffective(vcDTO);
        VerifyConclusionVO verifyConlusionVO = dutySurveyVO.getVerifyConclusionVO();
        List<DutyRejectDetailVO> dutyRejectDetailList = verifyConlusionVO.getDutyRejectDetailList();
        if (!ChecklossConst.VERIFY_REFUSE.equals(conclusionKey)) {
            dutyRejectDetailList = null;
            verifyConlusionVO.setRejectNotifyDate(null);
        }
        BigDecimal totalRejectAmount = dutyRejectDetailService.modifyDutyRejectDetailList(loginUm, reportNo, caseTimes, status, dutyRejectDetailList);
        VerifyConclusionDTO verifyConclusionDTO = new VerifyConclusionDTO();
        BeanUtils.copyProperties(verifyConlusionVO, verifyConclusionDTO);
        verifyConclusionDTO.setReportNo(reportNo);
        verifyConclusionDTO.setCaseTimes(caseTimes);
        verifyConclusionDTO.setAuditingUm(loginUm);
        verifyConclusionDTO.setStatus(status);
        verifyConclusionDTO.setTaskCode(taskId);
        verifyConclusionDTO.setRejectAmount(totalRejectAmount);
        verifyConclusionDTO.setDepartmentCode(WebServletContext.getDepartmentCode());
        String ahcsVerifyConclusionId = UuidUtil.getUUID();
        verifyConclusionDTO.setAhcsVerifyConclusionId(ahcsVerifyConclusionId);
        //存 核赔结论信息
        saveVerifyConclusion(loginUm, verifyConclusionDTO);
        //存 减损金额,收单没有减损金额
        if (verifyConclusionDTO.getReduceAmount() != null) {
            saveLossReduce(loginUm, verifyConclusionDTO);
        }
        //存拒赔审批,收单默认都是赔付
        if (BaseConstant.STRING_4.equals(verifyConlusionVO.getIndemnityConclusion())) {
            ClaimRejectionApprovalRecordEntity claimRejectionApprovalRecordEntity = new ClaimRejectionApprovalRecordEntity();
            claimRejectionApprovalRecordEntity.setAuditRemark(verifyConlusionVO.getAuditingCommont());
            claimRejectionApprovalRecordEntity.setReportNo(reportNo);
            claimRejectionApprovalRecordEntity.setCaseTimes(caseTimes);
            claimRejectionApprovalRecordEntity.setAhcsVerifyConclusionId(ahcsVerifyConclusionId);
            saveRejectApprovalRecord(claimRejectionApprovalRecordEntity, loginUm);
        }

        String preConclusionKey = "";
        if (BpmConstants.MANUAL_SETTLE.equals(taskId)) {
            WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
            if (wholeCaseBaseDTO != null) {
                VerifyConclusionVO preVerifyConclusion = new VerifyConclusionVO();
                preVerifyConclusion.setIndemnityConclusion(wholeCaseBaseDTO.getIndemnityConclusion());
                preVerifyConclusion.setIndemnityModel(wholeCaseBaseDTO.getIndemnityModel());
                preConclusionKey = this.getConclusionKey(preVerifyConclusion);
            }
        }
        saveWholeCaseBase(reportNo, caseTimes, verifyConlusionVO.getIndemnityConclusion(), verifyConlusionVO.getIndemnityModel(), loginUm);
        if (BpmConstants.CHECK_DUTY.equals(taskId) && ChecklossConst.STATUS_TMP_SUBMIT.equals(status) && ChecklossConst.IS_UPDATE_YES.equals(dutySurveyVO.getIsClearBillAmonut())) {
            medicalBillService.clearBillAmonutForHuge(reportNo, caseTimes, loginUm, null);
        }
        String endorsementRemark = verifyConlusionVO.getEndorsementRemark();
        preConclusionKey = (StringUtil.isEmpty(preConclusionKey)) ? "-1" : preConclusionKey;
        if (BpmConstants.CHECK_DUTY.equals(taskId) || ChecklossConst.VERIFY_REFUSE.equals(conclusionKey) || !preConclusionKey.equals(conclusionKey)) {
            endorsementService.deleteByPolicyPayId(reportNo, caseTimes);
        }
        if (!BpmConstants.MANUAL_SETTLE.equals(taskId) && ChecklossConst.STATUS_TMP_SUBMIT.equals(status) && !ChecklossConst.VERIFY_REFUSE.equals(conclusionKey)) {
            String nonSurveyStatement = dutySurveyVO.getNonSurveyStatement();
            if (StringUtils.isEmptyStr(nonSurveyStatement)) {
                RunRoleResultVO runRoleResultVO = new RunRoleResultVO();
                if (runRoleResultVO != null && runRoleResultVO.isRisk()) {
                    return runRoleResultVO;
                }
            } else {
                NonSurveyStatementDTO nonSurveyStatementDTO = new NonSurveyStatementDTO();
                nonSurveyStatementDTO.setUserId(loginUm);
                nonSurveyStatementDTO.setReportNo(reportNo);
                nonSurveyStatementDTO.setCaseTimes(caseTimes);
                nonSurveyStatementDTO.setNonSurveyStatement(nonSurveyStatement);
                nonSurveyStatementDTO.setTacheCode(taskId);
            }

        }
        if (ChecklossConst.STATUS_TMP_SUBMIT.equals(status)) {
            //收单方法中conclusionKey为1，所以实际不执行里面内容以及下面的内容
            this.updateCaseProcess(conclusionKey, verifyConclusionDTO, loginUm, endorsementRemark);
        } else if (ChecklossConst.VERIFY_REFUSE.equals(conclusionKey)) {
            this.saveEndorsementRemark(reportNo, caseTimes, loginUm, endorsementRemark);
        }
        return null;
    }

    public void saveRejectApprovalRecord(ClaimRejectionApprovalRecordEntity claimRejectionApprovalRecordEntity, String loginUm) {
        claimRejectionApprovalRecordEntity.setInitiatorUm(loginUm);
        claimRejectionApprovalRecordEntity.setUpdatedBy(loginUm);
        claimRejectionApprovalRecordEntityMapper.insert(claimRejectionApprovalRecordEntity);
    }

    @Override
    public void saveLossReduce(String loginUm, VerifyConclusionDTO verifyConclusionDTO) {
        LossReduceDTO lossReduceDTO = new LossReduceDTO();
        lossReduceDTO.setShowName("");
        lossReduceDTO.setLatestBatch("");
        lossReduceDTO.setSocialCreditCode("");
        lossReduceDTO.setCompanyName("");
        lossReduceDTO.setArchiveTime(new Date());
        lossReduceDTO.setReduceTypeName("");
        lossReduceDTO.setSurveyTypeName("");
        lossReduceDTO.setPolicyCerNo("");
        lossReduceDTO.setPolicyNo("");
        lossReduceDTO.setIdAhcsLossReduce(UuidUtil.getUUID());
        lossReduceDTO.setReportNo(verifyConclusionDTO.getReportNo());
        lossReduceDTO.setCaseTimes(String.valueOf(verifyConclusionDTO.getCaseTimes()));
        lossReduceDTO.setReduceProject("");
        lossReduceDTO.setReduceAmount(verifyConclusionDTO.getReduceAmount());
        lossReduceDTO.setReduceType("");
        lossReduceDTO.setSurveyType("");
        lossReduceDTO.setReduceByStr("");
        lossReduceDTO.setReduceBys(Lists.newArrayList());
        lossReduceDTO.setAdditionalDesc("");
        lossReduceDTO.setCheckDesc("");
        lossReduceDTO.setStatus(verifyConclusionDTO.getStatus());
        lossReduceDTO.setReduceProjectName("");
        lossReduceDTO.setUserId(loginUm);
        lossReduceDTO.setCreatedBy(loginUm);
        lossReduceDTO.setUpdatedBy(loginUm);
        lossReduceMapper.updateEffective(lossReduceDTO);
        lossReduceMapper.saveLossReduce(lossReduceDTO);
    }

    @Override
    public PeopleHurtVO getPeopleHurtVO(String reportNo, int caseTimes) throws GlobalBusinessException {
        List<ChannelProcessDTO> channelProcessList = channelProcessService.getChanelProcessIdList(reportNo, caseTimes);
        String taskId = caseClassDao.getCurrentTaskCodeFromCaseClass(reportNo, caseTimes, "");
        PeopleHurtVO peopleHurtVO = new PeopleHurtVO();
        if (channelProcessList != null && !channelProcessList.isEmpty()) {
            for (ChannelProcessDTO channelProcess : channelProcessList) {
                switch (channelProcess.getChannelType()) {
                    case ChecklossConst.CASECLASS_PEOPLE_HURT:
                        peopleHurtVO = getPeopleHurtVO(reportNo, caseTimes, channelProcess.getIdAhcsChannelProcess(), taskId);
                        peopleHurtVO.setChannelType(ChecklossConst.CASECLASS_PEOPLE_HURT);
                        peopleHurtVO.setIdAhcsChannelProcess(channelProcess.getIdAhcsChannelProcess());
                        break;
                    default:
                        break;
                }
            }
        }
        return peopleHurtVO;
    }

    @Override
    public void flowWorkFlow(MedicalDTO medicalDTO) {
        //组装数据
        DutySurveyVO dutyVO = new DutySurveyVO();
        dutyVO.setIsCompleteDuty("N");
        dutyVO.setIsSettle("N");
        dutyVO.setStatus("1");
        dutyVO.setReportNo(medicalDTO.getReportNo());
        dutyVO.setCaseTimes(1);
        dutyVO.setTaskId("checkDuty");
        VerifyConclusionVO verifyConclusionVO = new VerifyConclusionVO();
        verifyConclusionVO.setIndemnityModel("");
        verifyConclusionVO.setIndemnityConclusion("1");
        verifyConclusionVO.setReduceAmount(new BigDecimal(String.valueOf(BigDecimal.ZERO)));
        dutyVO.setVerifyConclusionVO(verifyConclusionVO);
        String userId = WebServletContext.getUserId();

        //是否完成收单
        if (!ChecklossConst.IS_COMPLATE_DUTY.equals(dutyVO.getIsCompleteDuty())) {
            RunRoleResultVO dutySurveyResultVO = this.saveDutySurvey(dutyVO, userId, dutyVO.getStatus());
            if (dutySurveyResultVO != null) {
                return;
            }
        }

        if ("N".equals(dutyVO.getIsSettle())) {
            //提交至工作流完成任务
            if (ChecklossConst.STATUS_TMP_SUBMIT.equals(dutyVO.getStatus())) {
                this.completeDutySurvey(dutyVO, userId);
            }
        }
    }


    private void updateCaseProcess(String indemnityConclusion, VerifyConclusionDTO verifyConclusionDTO, String loginUm, String endorsementRemark) throws GlobalBusinessException {
        String reportNo = verifyConclusionDTO.getReportNo();
        Integer caseTimes = verifyConclusionDTO.getCaseTimes();
        if (ChecklossConst.VERIFY_REFUSE.equals(indemnityConclusion)) {
            policyPayService.handlePolicyPaysByConclusion(reportNo, caseTimes, SettleConst.INDEMNITY_MODE_DENIED, false);
            List<FeeInfoDTO> feeInfoList = new ArrayList<>();// 费用
            if (ListUtils.isEmptyList(feeInfoList)) {
                this.saveEndorsementRemark(reportNo, caseTimes, loginUm, endorsementRemark);
                String conclusionCauseCode = verifyConclusionDTO.getConclusionCauseCode();
                String conclusionCause = verifyConclusionCauseMapper.getVerifyConclusionCause(conclusionCauseCode);
                this.deniedCase(reportNo, caseTimes, conclusionCause, loginUm, false, verifyConclusionDTO.getTaskCode());
            }

        }
    }

    private void deniedCase(String reportNo, Integer caseTimes, String conclusionCause, String loginUm, Boolean payFee, String tacheCode) throws GlobalBusinessException {
        this.modifyBatch(reportNo, caseTimes, loginUm);
        communicateBaseMapper.cancelCommunicateTask(reportNo, caseTimes, loginUm);
    }

    private void modifyBatch(String reportNo, Integer caseTimes, String loginUm) {
        BatchDTO Batch = batchService.getBatch(reportNo, caseTimes);
        if (null != Batch) {
            Batch.setSettleStatus(SettleConst.SETTLE_STATUS_DONE);
            Batch.setUpdatedBy(loginUm);
            Batch.setSettleUserUm(loginUm);
            batchService.updateBatch(Batch);
        }
    }

    public FlightDelayVO getFlightDelay(String reportNo, Integer caseTimes, String taskCode, String channelProcessId){

        FlightDelayDTO flightDelayDTO = flightDelayService.getFlighterDelay(reportNo, caseTimes, null, taskCode, channelProcessId);
        if (flightDelayDTO == null) {
            return null;
        }

        FlightDelayVO flightDelayVO = new FlightDelayVO();
        BeanUtils.copyProperties(flightDelayDTO, flightDelayVO);
        String flightStatus = flightDelayVO.getFlightStatus();
        if (StringUtils.isNotEmpty(flightStatus)) {
            flightDelayVO.setFlightStatusArr(flightStatus.split(","));
        } else {
            flightDelayVO.setFlightStatusArr(new String[] {});
        }

        if (StringUtils.isEmptyStr(flightDelayVO.getDelayDurationUnit())) {
            flightDelayVO.setDelayDurationUnit(ConstValues.TIME_UNIT_MIN);
        }

        flightDelayVO.setShowLossAmount(ConstValues.YES);
        flightDelayVO.setModuleCode(ChecklossConst.FLIGHT_DELAY);

        return flightDelayVO;
    }

//    private void refuseDocStart(String reportNo, Integer caseTimes, String loginUm) throws GlobalBusinessException {
//        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
//        caseProcessDTO.setUserId(loginUm);
//        caseProcessDTO.setReportNo(reportNo);
//        caseProcessDTO.setCaseTimes(caseTimes);
//        caseProcessDTO.setProcessStatus(ConfigConstValues.PROCESS_STATUS_CASE_CLOSED);
//        caseProcessService.updateCaseProcessDTO(caseProcessDTO);
//    }
//
//
//    private void claimFeeFinance(String reportNo, Integer caseTimes, String idClmBatch) {
//        LogUtil.audit("........拒赔下发费用任务.....参数report={},caseTimes={},idClmBatch={}", reportNo, caseTimes, idClmBatch);
//        DownFinanceReqData downFinanceReqData = new DownFinanceReqData();
//        downFinanceReqData.setCaseTimes(caseTimes.toString());
//        downFinanceReqData.setReportNo(reportNo);
//        downFinanceReqData.setClaimType(ConstValues.CLAIM_TYPE_PAY);
//        downFinanceReqData.setIdClmBatch(idClmBatch);
//        try {
////            rapeApiMockService.downFinance(downFinanceReqData);
//        } catch (Exception e) {
//
//        }
//    }

    /**
     * 转换设置出发地和目的地的值
     *
     * @param travelDelayInfoDTO
     * @param clmsTravelDelayInfo
     */
    private void setPlaceVaue(ClmsTravelDelayInfoDTO travelDelayInfoDTO, ClmsTravelDelayInfo clmsTravelDelayInfo) {
        //出发地
        if (CollectionUtil.isNotEmpty(travelDelayInfoDTO.getDepartPlace())) {
            List<String> departPlaces = travelDelayInfoDTO.getDepartPlace();
            StringBuffer dbuf = new StringBuffer();
            for (String str : departPlaces) {
                dbuf.append(str).append(Constants.SEPARATOR);
            }

            clmsTravelDelayInfo.setDepartPlace(dbuf.toString().substring(0, dbuf.toString().length() - 1));
        }
        if (CollectionUtil.isNotEmpty(travelDelayInfoDTO.getArrivalPlace())) {
            List<String> arrivalPlaces = travelDelayInfoDTO.getArrivalPlace();
            StringBuffer arrbuf = new StringBuffer();
            for (String str : arrivalPlaces) {
                arrbuf.append(str).append(Constants.SEPARATOR);
            }
            clmsTravelDelayInfo.setArrivalPlace(arrbuf.toString().substring(0, arrbuf.toString().length() - 1));
        }

    }

    /**
     * 出发地，目的地转换处理
     * @param travelDelayInfo
     * @param travelDelayInfoDTO
     */
    private void setPlaceData(ClmsTravelDelayInfo travelDelayInfo, ClmsTravelDelayInfoDTO travelDelayInfoDTO) {
        //出发地
        String departPlace = travelDelayInfo.getDepartPlace();
        if (!StringUtils.isEmptyStr(departPlace)) {
            List<String> arrivalPlaceList = StringUtils.getListWithSeparator(departPlace, Constants.SEPARATOR);
            travelDelayInfoDTO.setDepartPlace(arrivalPlaceList);
        }
        //目的地
        String arrivalPlace = travelDelayInfo.getArrivalPlace();
        if (!StringUtils.isEmptyStr(arrivalPlace)) {
            List<String> arrivalPlaceList = StringUtils.getListWithSeparator(arrivalPlace, Constants.SEPARATOR);
            travelDelayInfoDTO.setArrivalPlace(arrivalPlaceList);
        }


    }

    private String getClientNo(ClmsPersonalInjuryDeathInfoDTO pidDTO) {
        if (StringUtils.isEmptyStr(pidDTO.getInjuredName())
                || StringUtils.isEmptyStr(pidDTO.getInjuredCertificateNo())
                || StringUtils.isEmptyStr(pidDTO.getInjuredCertificateType())) {
            throw new GlobalBusinessException("伤者姓名、证件号、证件类型不能为空");
        }

        try {
            return customerInfoService.getCustomerNo(pidDTO.getInjuredName(), BankAccountTypeEnum.BankAccountType_ONE.getCode(),
                    pidDTO.getInjuredCertificateType(), pidDTO.getInjuredCertificateNo());

        } catch (Exception e) {
            LogUtil.error("生成客户号失败:",e);
        }
        return "";
    }

    /**
     * 人伤信息
     * @param dutySurveyVO
     */
    @Override
    public PeopleHurtVO dealPeopleHurtInfo(DutySurveyVO dutySurveyVO, List<String> caseSubClassList) {
        String reportNo=dutySurveyVO.getReportNo();
        Integer caseTimes =dutySurveyVO.getCaseTimes();
        String loginUm = dutySurveyVO.getUserId();

        String channelProcessId;//通道号，区分人伤、非人伤
        PeopleHurtVO peopleHurtVO = dutySurveyVO.getPeopleHurtVO();
        if (peopleHurtVO != null) {
            peopleHurtVO.setTaskId(dutySurveyVO.getTaskId());
            peopleHurtVO.setStatus(dutySurveyVO.getStatus());
            channelProcessId = peopleHurtVO.getIdAhcsChannelProcess();
            if (StringUtils.isEmptyStr(channelProcessId)) {
                channelProcessId = this.getChannelProcessId(reportNo, caseTimes, dutySurveyVO.getPartyNo(), ConstValues.CASECLASS_PEOPLE_HURT, loginUm);
                peopleHurtVO.setIdAhcsChannelProcess(channelProcessId);
            }
            modifyPeopleHurtVO(reportNo, caseTimes, loginUm, peopleHurtVO, caseSubClassList);
        }
        return peopleHurtVO;

    }

    /**
     * 处理非人伤信息
     * @param dutySurveyVO
     * @return
     */
    @Override
    public NoPeopleHurtVO dealNoPeopleHurtInfo(DutySurveyVO dutySurveyVO,PeopleHurtVO peopleHurtVO) {
        String reportNo=dutySurveyVO.getReportNo();
        Integer caseTimes =dutySurveyVO.getCaseTimes();
        String  taskId=dutySurveyVO.getTaskId();
        String status= dutySurveyVO.getStatus();
        String loginUm = dutySurveyVO.getUserId();

        String channelProcessId;//通道号，区分人伤、非人伤
        NoPeopleHurtVO noPeopleHurtVO = dutySurveyVO.getNoPeopleHurtVO();
        if (Objects.nonNull(noPeopleHurtVO)) {
            noPeopleHurtVO.setTaskId(taskId);
            noPeopleHurtVO.setStatus(status);
            channelProcessId = noPeopleHurtVO.getIdAhcsChannelProcess();
            if (StringUtils.isEmptyStr(channelProcessId)) {
                channelProcessId = this.getChannelProcessId(reportNo, caseTimes, dutySurveyVO.getPartyNo(), ConstValues.CASECLASS_NO_PEOPLE_HURT, loginUm);
                noPeopleHurtVO.setIdAhcsChannelProcess(channelProcessId);
            }

            buildPetAccident(noPeopleHurtVO,peopleHurtVO);
            this.modifyNoPeopleHurtVO(reportNo, caseTimes, loginUm, noPeopleHurtVO);
        }
        return noPeopleHurtVO;
    }

    /**
     * 设置值
     * @param noPeopleHurtVO
     * @param peopleHurtVO
     */
    private void buildPetAccident(NoPeopleHurtVO noPeopleHurtVO,PeopleHurtVO peopleHurtVO){
        PetInjureVO petInjureVO = noPeopleHurtVO.getPetInjureVO();
        if(petInjureVO != null && peopleHurtVO != null && peopleHurtVO.getAccidentVO() != null){
            PersonAccidentVO accidentVO = peopleHurtVO.getAccidentVO();
            petInjureVO.setAccidentOverseas(accidentVO.getOverseasOccur());
            petInjureVO.setAccidentProvinceCode(accidentVO.getAccidentProvince());
            petInjureVO.setAccidentCityCode(accidentVO.getAccidentCity());
            petInjureVO.setAccidentCountryCode(accidentVO.getAccidentCounty());
            petInjureVO.setAccidentContinentCode(accidentVO.getAccidentNation());
            petInjureVO.setAccidentPlace(accidentVO.getAccidentPlace());
        }
    }



    @Async("asyncPool")
    public void initPolicyPay(String reportNo,Integer caseTimes){
        LogUtil.info("开始异步初始化理算reportNo={}",reportNo);
        policyPayService.initPolicyPayInfo(reportNo, caseTimes);
    }

    /**
     * 领款人信息校验
     * @param dutyVO
     */
    private void checkPaymentInfo(DutySurveyVO dutyVO) {
       Integer count= paymentInfoMapper.getPaymentEffInfoByReportNo(dutyVO.getReportNo());
       if(count<=0){
           throw new GlobalBusinessException("请录入领款人信息！");
       }
    }
    /**
     * 查询案件是否重开，判断是否可以拒赔
     * @param reportNo  报案号
     * @param caseTimes 赔付次数
     * @return boolean  true 表示允许拒赔；false 表示不允许
     */
    @Override
    public boolean canRejectCase(String reportNo, Integer caseTimes) {
        Integer verfiyCodeCount = wholeCaseBaseMapper.getverfiyCodeCount(reportNo,caseTimes);
        if(caseTimes>1 && verfiyCodeCount>0){
            return false;
        }
        return true;
    }
    public void saveDiagnosticInformation(DutySurveyVO dutySurveyVO) {
        String status = dutySurveyVO.getStatus();
        String userId = WebServletContext.getUserId();
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        dutySurveyVO.setUserId(userId);
        String taskId = dutySurveyVO.getTaskId();
        List<String> cscs = dutySurveyVO.getCaseSubClass();
        CaseClassDTO ccDTO = new CaseClassDTO();
        ccDTO.setReportNo(reportNo);
        ccDTO.setCaseTimes(caseTimes);
        ccDTO.setTaskId(taskId);
        ccDTO.setUpdatedBy(userId);
        caseClassDao.updateEffective(ccDTO);
        if (!CollectionUtils.isEmpty(cscs)) {
            List<CaseClassDTO> caseClassList = new ArrayList<>();
            Date date= new Date();
            for (String caseSubClass : cscs) {
                CaseClassDTO caseClassDTO = new CaseClassDTO();
                caseClassDTO.setReportNo(reportNo);
                caseClassDTO.setCaseTimes(caseTimes);
                caseClassDTO.setTaskId(taskId);
                caseClassDTO.setStatus(status);
                caseClassDTO.setCaseSubClass(caseSubClass);
                //加五秒存是为了保证和立案时的创建时间不在同一个点，线上同时调用立案接口和收单接口时 创建时间会一样

                caseClassDTO.setCreatedDate(DateUtil.offsetSecond(date,25));
                caseClassDTO.setUpdatedDate(DateUtil.offsetSecond(date,25));
                caseClassList.add(caseClassDTO);
            }
            LogUtil.info("date创建时间={}收单案件类别报案号={}，案件类别信息={}",DateUtils.parseToFormatString(date),reportNo, JsonUtils.toJsonString(caseClassList));
            caseClassDao.saveCaseClassList(caseClassList, caseTimes, userId,null);
        }

        String channelProcessId = null;
        PeopleHurtVO peopleHurtVO = dutySurveyVO.getPeopleHurtVO();
        if (peopleHurtVO != null) {
            peopleHurtVO.setTaskId(taskId);
            peopleHurtVO.setStatus(status);
            channelProcessId = peopleHurtVO.getIdAhcsChannelProcess();
            if (StringUtils.isEmptyStr(channelProcessId)) {
                channelProcessId = this.getChannelProcessId(reportNo, caseTimes, dutySurveyVO.getPartyNo(), ConstValues.CASECLASS_PEOPLE_HURT, userId);
                peopleHurtVO.setIdAhcsChannelProcess(channelProcessId);
            }
//            modifyPeopleHurtVO(reportNo, caseTimes, userId, peopleHurtVO, cscs);
            PersonDiagnoseVO personDiagnoseVO = peopleHurtVO.getDiagnoseVO();
            List<PersonDiagnoseDTO> diagnoses = personDiagnoseVO.getDiagnoseDTOs();

            PersonDiagnoseDTO pdDTO = new PersonDiagnoseDTO();
            pdDTO.setIdAhcsChannelProcess(channelProcessId);
            pdDTO.setTaskId(taskId);
            pdDTO.setUpdatedBy(userId);
            personDiagnoseDao.updateEffective(pdDTO);

            HashSet<String> repeatSet = new HashSet<>();
            if (!CollectionUtils.isEmpty(diagnoses)) {
                for (PersonDiagnoseDTO personDiagnoseDTO : diagnoses) {
                    if (StringUtils.isEmptyStr(personDiagnoseDTO.getDiagnoseCode()) && StringUtils.isEmptyStr(personDiagnoseDTO.getIsSurgical())
                            && StringUtils.isEmptyStr(personDiagnoseDTO.getSurgicalName())) {
                        continue;
                    }
                    if (repeatSet.contains(personDiagnoseDTO.getDiagnoseCode())) {
                        continue;
                    } else {
                        repeatSet.add(personDiagnoseDTO.getDiagnoseCode());
                    }
                    personDiagnoseDTO.setIdAhcsChannelProcess(channelProcessId);
                    personDiagnoseDTO.setTaskId(taskId);
                    personDiagnoseDTO.setStatus(status);

                    savePersonDiagnose(reportNo, caseTimes, userId, personDiagnoseDTO);
                }
            }

            List<PersonAccidentDTO> orgAccidentDTOs = personAccidentDao.getPersonAccidentListByIdChannel(reportNo, caseTimes, channelProcessId);
            if(orgAccidentDTOs!=null && !orgAccidentDTOs.isEmpty()){
                PersonAccidentDTO accidentDTO = orgAccidentDTOs.get(0);
                if(!taskId.equals(accidentDTO.getTaskId())) {
                    personAccidentDao.removePersonAccident(reportNo, caseTimes);
                    accidentDTO.setPersonAccidentId(UuidUtil.getUUID());
                    accidentDTO.setTaskId(taskId);
                    personAccidentDao.savePersonAccident(accidentDTO);
                }
            }
        }
    }

}
