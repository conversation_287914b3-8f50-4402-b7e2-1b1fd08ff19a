package com.paic.ncbs.claim.controller.who.investigate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeCollectionUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.api.FileUploadApiController;
import com.paic.ncbs.claim.controller.doc.DocAppFileUploadController;
import com.paic.ncbs.claim.controller.doc.DocumentController;
import com.paic.ncbs.claim.controller.doc.FileUploadController;
import com.paic.ncbs.claim.controller.doc.IOBSFileUploadController;
import com.paic.ncbs.claim.controller.mng.CommonParameterController;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentReqDTO;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentResDTO;
import com.paic.ncbs.claim.model.dto.doc.DocumentTypeDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessJsonDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateQueryDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateProcessMapper;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePage;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePageResult;
import com.paic.ncbs.claim.model.vo.fileupolad.DocumentTypeVO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.*;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimInfoToESVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.um.model.dto.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditPublicService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.um.config.UmProperties;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.sao.mesh.NcbsUmRequest;
import com.paic.ncbs.um.service.CacheService;
import com.paic.ncbs.um.util.Sha1SignUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "外部系统调查")
@RestController
@RequestMapping(value = "/public/app/investigateTaskAuditAction")
public class InvestigateTaskAuditPublicController {

    @Autowired
    private InvestigateTaskAuditPublicService investigateTaskAuditPublicService;

    @Autowired
    private FileUploadController fileUploadController;

    @Autowired
    private DocAppFileUploadController docAppFileUploadController;

    @Autowired
    private IOBSFileUploadController iobsFileUploadController;

    @Autowired
    private DocumentController documentController;

    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    private InvestigateTaskService investigateTaskService;

    @Autowired
    private InvestigateTaskController investigateTaskController;

    @Autowired
    private InvestigateProcessController investigateProcessController;

    @Autowired
    private FileCommonService fileCommonService;

    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private WholeCaseService wholeCaseService;

    @Autowired
    private NcbsUmRequest ncbsUmRequest;

    @Autowired
    private UmProperties umProperties;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    @Lazy
    private CacheService cacheService;

    @Autowired
    private CommonParameterController commonParameterController;

    @Autowired
    private InvestigateProcessMapper investigateProcessMapper;

    /**
     * 统一接口中转
     */
    @ApiOperation(value = "统一接口中转")
    @PostMapping(value = "/transfer", consumes = "application/json")
    @ResponseBody
    public ResponseResult<?> transferJson(@RequestBody InvestigateQueryDTO queryDTO,
                                          HttpServletRequest request) {
        if (queryDTO.getRequestData() == null) {
            throw new GlobalBusinessException("请求数据不能为空");
        }

        String interfaceName = queryDTO.getRequestData().getInterfaceName();
        if (interfaceName == null || interfaceName.trim().isEmpty()) {
            throw new GlobalBusinessException("接口名称不能为空");
        }

        // 用户设置
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        String userCode = requestData.getUserCode();
        String userName = requestData.getUserName();
        String departmentCode = ConfigConstValues.HQ_DEPARTMENT;
        try {
            List<SystemComInfoDTO> systemComInfoDTOS = cacheService.queryUserSystemComList(userCode);
            if (!(CollectionUtils.isEmpty(systemComInfoDTOS) || StringUtils.isEmptyStr(systemComInfoDTOS.get(0).getComCode()))){
                departmentCode = systemComInfoDTOS.get(0).getComCode();
            }
        } catch (NcbsException e) {
            LogUtil.error("外部用户获取用户权限机构异常", e);
        }

        if (StringUtils.isNotEmpty(userCode) && StringUtils.isNotEmpty(userName)) {
            UserInfoDTO userInfoDTO = new UserInfoDTO();
            userInfoDTO.setUserCode(userCode);
            userInfoDTO.setUserName(userName);
            userInfoDTO.setComCode(departmentCode);
            request.getSession().setAttribute(Constants.CURR_USER, userInfoDTO);
            request.getSession().setAttribute(Constants.CURR_COMCODE, departmentCode);
            MDC.put("userId", userCode);
        }

        try {
            java.lang.reflect.Method method = this.getClass().getMethod(interfaceName, InvestigateQueryDTO.class);
            return (ResponseResult<?>) method.invoke(this, queryDTO);
        } catch (NoSuchMethodException e) {
            throw new GlobalBusinessException("不支持的接口名称: " + interfaceName);
        } catch (GlobalBusinessException e) {
            throw e;
        } catch (Exception e) {
            LogUtil.error("#外部系统调查#接口中转异常#interfaceName={}", interfaceName, e);
            Throwable cause = e.getCause();
            if (cause instanceof GlobalBusinessException) {
                throw (GlobalBusinessException) cause;
            }
            throw new GlobalBusinessException("接口调用失败: " + e.getMessage());
        }
    }


    @ApiOperation(value = "查询调查任务列表")
    @PostMapping(value = "/getInvestigateTaskList")
    public ResponseResult<Object> getInvestigateTaskList(@RequestBody InvestigateQueryDTO queryDTO) throws Exception {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        if(StringUtils.isEmptyStr(requestData.getUserCode())){
            throw new GlobalBusinessException("用户id不能为空");
        }

        // 获取公估公司服务代码
        List<String> serverCode = null;
        try {
            // 查询供应商（公估公司）
            ResponseResult result = investigateService.getExternalDepartmentList(requestData.getUserCode());
            TpaSupplierInfoListVO tpaSupplierInfoListVO = (TpaSupplierInfoListVO) result.getData();
            if(tpaSupplierInfoListVO != null && !tpaSupplierInfoListVO.getSupplierInfoList().isEmpty()){
                TpaSupplierInfoListVO.SupplierInfo supplierInfo = tpaSupplierInfoListVO.getSupplierInfoList().get(0);
                // 查询服务代码
                TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
                List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
                serverCode = serverInfoList.stream()
                        .filter(serverInfo -> serverInfo.getSupplierCode().equals(supplierInfo.getSupplierCode()))
                        .map(ServerInfoVO::getServerCode)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            LogUtil.error("外部用户获取公估公司服务代码异常", e);
        }

        // 获取工作台任务列表
        List<WorkBenchTaskVO> workBenchTaskList = investigateTaskAuditPublicService.getInvestigateTaskList(serverCode);

        // 按任务类型分组
        Map<String, List<WorkBenchTaskVO>> filteredTaskList = workBenchTaskList.stream()
                .filter(vo -> BpmConstants.OC_MAJOR_INVESTIGATE.equals(vo.getTaskDefinitionBpmKey()))
                .collect(Collectors.groupingBy(WorkBenchTaskVO::getTaskDefinitionBpmKey));

        // 获取所有分组后的任务列表
        List<WorkBenchTaskVO> allTasks = filteredTaskList.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        allTasks.forEach(task -> {
            if (StringUtils.isNotEmpty(task.getCaseLength())) {
                try {
                    int totalHours = Integer.parseInt(task.getCaseLength());
                    String result = (totalHours / 24 > 0 ? totalHours / 24 + "天" : "") + (totalHours % 24) + "小时";
                    task.setCaseLength(result);
                } catch (NumberFormatException e) {
                    LogUtil.error("转换caseLength失败: {}", task.getCaseLength(), e);
                }
            }
            if (StringUtils.isNotEmpty(task.getCurrentLength())) {
                try {
                    task.setCurrentLength(task.getCurrentLength() + "天");
                } catch (Exception e) {
                    LogUtil.error("添加天单位失败: {}", task.getCurrentLength(), e);
                }
            }
        });

        return paginateList(allTasks, requestData);
    }


    /**
     * 查询调查任务详情
     */
    @ApiOperation("查询调查任务详情")
    @PostMapping(value = "/getIntegratedInvestigateInfo")
    public ResponseResult<Map<String, Object>> getIntegratedInvestigateInfo(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();

        if(StringUtils.isEmptyStr(requestData.getReportNo()) || StringUtils.isEmptyStr(requestData.getCaseTimes())){
            throw new GlobalBusinessException("报案号和案件次数不能为空");
        }
        if(StringUtils.isEmptyStr(requestData.getInvestigateId())){
            throw new GlobalBusinessException("调查ID不能为空");
        }

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取基础信息（客户基本信息、报案信息）
            Map<String, Object> basicInfo = investigateTaskAuditPublicService.getBasicInfo(requestData.getReportNo(), requestData.getCaseTimes(), requestData.getUserName());
            result.putAll(basicInfo);

            // 查询案件的历史调查信息
            List<InvestigateVO> historyInvestigateList = investigateService.getHistoryInvestigate(requestData.getReportNo(), requestData.getCaseTimes());
            result.put("historyInvestigateList", historyInvestigateList);

            // 本次提调信息
            InvestigateVO currentInvestigate = null;
            if (StringUtils.isNotEmpty(requestData.getInvestigateId())) {
                currentInvestigate = investigateService.getInvestigateById(requestData.getInvestigateId());
            }
            result.put("currentInvestigate", currentInvestigate);

            String investigateTaskId = investigateTaskService.getMajorTaskIdByInvestigateId(requestData.getInvestigateId());

            // 分配信息查询、调查任务处理
            List<InvestigateProcessVO> investigateProcessVOs = null;
            List<InvestigateProcessJsonDTO> investigateProcessJsonDTOs = new ArrayList<>();
            if (StringUtils.isNotEmpty(investigateTaskId)) {
                InvestigateTaskVO investigateTaskVO = investigateTaskService.getInvestigateTaskLinkedByTaskId(investigateTaskId);
                result.put("investigateTaskVO", investigateTaskVO);
                if (investigateTaskVO != null) {
                    // 根据调查定性类型处理返回不同的异常明细字段
                    if (InvestigateConstants.INVESTIGATE_QUALITATIVE_REFERENCE.equals(investigateTaskVO.getInvestigateQualitative())) {
                        investigateTaskVO.setAbnormalDetail1(investigateTaskVO.getAbnormalDetail());
                    } else if (InvestigateConstants.INVESTIGATE_QUALITATIVE_ABNORMAL.equals(investigateTaskVO.getInvestigateQualitative())) {
                        investigateTaskVO.setAbnormalDetail2(investigateTaskVO.getAbnormalDetail());
                    }
                    investigateProcessVOs = investigateTaskVO.getInvestigateProcessVOs();
                    // 转换为JSON DTO
                    if (!CollectionUtils.isEmpty(investigateProcessVOs)) {
                        for (InvestigateProcessVO vo : investigateProcessVOs) {
                            investigateProcessJsonDTOs.add(convertToInvestigateProcessJsonDTO(vo));
                        }
                    }
                }
                result.put("investigateProcessVOs", investigateProcessJsonDTOs); // JSON DTO调查记录
            }

            // 电子保单下载地址
            String previewUrl = investigateTaskAuditPublicService.getPolicyElectricPdfUrl(requestData.getReportNo(), requestData.getUserName());
            result.put("previewUrl", previewUrl);

            LogUtil.audit("#外部系统调查#整合查询接口#成功获取所有信息");

        } catch (Exception e) {
            LogUtil.error("#外部系统调查#整合查询接口#异常", e);
            throw new GlobalBusinessException("获取调查信息失败：" + e.getMessage());
        }

        return ResponseResult.success(result);
    }

    @ApiOperation(value = "提交调查审核任务")
    @PostMapping(value = "/finishTaskAudit")
    public ResponseResult<Object> finishTaskAudit(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        investigateTaskAuditPublicService.finishTaskAudit(requestData.getTaskAuditInfo());
        return ResponseResult.success();
    }

    @ApiOperation(value = "提交、暂存调查任务")
    @PostMapping(value = "/finishInvestigateTask")
    public ResponseResult<Object> finishInvestigateTask(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        InvestigateTaskDTO investigateTaskDTO = requestData.getInvestigateTaskDTO();
        List<InvestigateProcessJsonDTO> investigateProcessJsonDTOList = requestData.getInvestigateProcessJsonDTOs();
        String isStorage = requestData.getIsStorage();
        investigateTaskDTO.setIsStorage(isStorage);
        if (InvestigateConstants.INVESTIGATE_QUALITATIVE_REFERENCE.equals(investigateTaskDTO.getInvestigateQualitative())) {
            investigateTaskDTO.setAbnormalDetail(investigateTaskDTO.getAbnormalDetail1());
        } else if (InvestigateConstants.INVESTIGATE_QUALITATIVE_ABNORMAL.equals(investigateTaskDTO.getInvestigateQualitative())) {
            investigateTaskDTO.setAbnormalDetail(investigateTaskDTO.getAbnormalDetail2());
        }
        // 参数校验
        checkInvestigateTaskDTO(queryDTO);

        // 调查经过数据处理
        if (StringUtils.isNotEmpty(investigateTaskDTO.getIdAhcsInvestigateTask())) {
            investigateProcessMapper.deleteByInvestigateTaskId(investigateTaskDTO.getIdAhcsInvestigateTask());
        }
        
        // 处理JSON DTO转换为普通DTO
        if (!CollectionUtils.isEmpty(investigateProcessJsonDTOList)) {
            for (InvestigateProcessJsonDTO jsonDTO : investigateProcessJsonDTOList) {
                InvestigateProcessDTO processDTO = convertToInvestigateProcessDTO(jsonDTO);
                processDTO.setIdAhcsInvestigateTask(investigateTaskDTO.getIdAhcsInvestigateTask());
                investigateProcessController.saveOrUpdateInvestigateProcess(processDTO);
            }
        }

        // 调查数据提交、暂存处理
        investigateTaskController.finishInvestigateTask(investigateTaskDTO);
        return ResponseResult.success();
    }
    
    /**
     * 校验调查任务DTO参数
     */
    private void checkInvestigateTaskDTO(InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        InvestigateTaskDTO investigateTaskDTO = requestData.getInvestigateTaskDTO();
        String isStorage = requestData.getIsStorage();
        if (investigateTaskDTO == null || StringUtils.isEmptyStr(isStorage)) {
            throw new GlobalBusinessException("提交类型isStorage不能为空");
        }

        if (InvestigateConstants.VALIDATE_FLAG_NO.equals(isStorage)) {
            List<InvestigateProcessJsonDTO> investigateProcessJsonDTOList = requestData.getInvestigateProcessJsonDTOs();
            if (CollectionUtils.isEmpty(investigateProcessJsonDTOList)) {
                throw new GlobalBusinessException("调查记录不能为空");
            }

            if ((InvestigateConstants.INVESTIGATE_QUALITATIVE_REFERENCE.equals(investigateTaskDTO.getInvestigateQualitative()) ||
                    InvestigateConstants.INVESTIGATE_QUALITATIVE_ABNORMAL.equals(investigateTaskDTO.getInvestigateQualitative())) &&
                    StringUtils.isEmptyStr(investigateTaskDTO.getAbnormalDetail())) {
                throw new GlobalBusinessException("调查定性为" + investigateTaskDTO.getInvestigateQualitative() + "时，异常说明不能为空");
            }
            if (InvestigateConstants.VALIDATE_FLAG_YES.equals(investigateTaskDTO.getHasEvidence()) &&
                    StringUtils.isEmptyStr(investigateTaskDTO.getEvidenceDetail())) {
                throw new GlobalBusinessException("有无证据为是时，证据说明不能为空");
            }
        }
    }

    /**
     * 调查经过VO转JSON DTO
     */
    private InvestigateProcessJsonDTO convertToInvestigateProcessJsonDTO(InvestigateProcessVO investigateProcessVO) {
        InvestigateProcessJsonDTO jsonDTO = new InvestigateProcessJsonDTO();
        jsonDTO.setIdAhcsInvestigateProcess(investigateProcessVO.getIdAhcsInvestigateProcess());
        jsonDTO.setIdAhcsInvestigateTask(investigateProcessVO.getIdAhcsInvestigateTask());
        jsonDTO.setInvestigateWay(investigateProcessVO.getInvestigateWay());
        jsonDTO.setInvestigateDate(investigateProcessVO.getInvestigateDate());
        jsonDTO.setCustomerName(investigateProcessVO.getCustomerName());
        jsonDTO.setResultDescription(investigateProcessVO.getResultDescription());
        jsonDTO.setOtherInformation(investigateProcessVO.getOtherInformation());
        
        // 从otherInformation JSON字符串中提取额外字段
        if (StringUtils.isNotEmpty(investigateProcessVO.getOtherInformation())) {
            try {
                JSONObject otherInfoJson = JSON.parseObject(investigateProcessVO.getOtherInformation());
                if (otherInfoJson != null) {
                    jsonDTO.setDuties(otherInfoJson.getString("duties"));
                    jsonDTO.setOccupation(otherInfoJson.getString("occupation"));
                    jsonDTO.setVerifMethod(otherInfoJson.getString("verifMethod"));
                    jsonDTO.setVisitingLocation(otherInfoJson.getString("visitingLocation"));
                    jsonDTO.setVisitingPurpose(otherInfoJson.getString("visitingPurpose"));
                    jsonDTO.setVisitingObjects(otherInfoJson.getString("visitingObjects"));
                    jsonDTO.setTelephone(otherInfoJson.getString("telephone"));
                    jsonDTO.setHospitalName(otherInfoJson.getString("hospitalName"));
                    jsonDTO.setDiagnoseName(otherInfoJson.getString("diagnoseName"));
                    jsonDTO.setSiteSurvey(otherInfoJson.getString("siteSurvey"));
                    jsonDTO.setAccidentType(otherInfoJson.getString("accidentType"));
                    jsonDTO.setWitness(otherInfoJson.getString("witness"));
                    jsonDTO.setWorkUnit(otherInfoJson.getString("workUnit"));
                    jsonDTO.setSex(otherInfoJson.getString("sex"));
                    jsonDTO.setRelationship(otherInfoJson.getString("relationship"));
                    jsonDTO.setInsuranceCoverage(otherInfoJson.getString("insuranceCoverage"));
                    jsonDTO.setInsuranceTime(otherInfoJson.getString("insuranceTime"));
                    jsonDTO.setCoverage(otherInfoJson.getString("coverage"));
                }
            } catch (Exception e) {
                LogUtil.error("解析otherInformation JSON失败: {}", investigateProcessVO.getOtherInformation(), e);
            }
        }
        
        return jsonDTO;
    }

    /**
     * 调查经过JSON DTO转调查经过DTO
     */
    private InvestigateProcessDTO convertToInvestigateProcessDTO(InvestigateProcessJsonDTO jsonDTO) {
        InvestigateProcessDTO processDTO = new InvestigateProcessDTO();
        processDTO.setIdAhcsInvestigateProcess(jsonDTO.getIdAhcsInvestigateProcess());
        processDTO.setIdAhcsInvestigateTask(jsonDTO.getIdAhcsInvestigateTask());
        processDTO.setInvestigateWay(jsonDTO.getInvestigateWay());
        processDTO.setInvestigateDate(jsonDTO.getInvestigateDate());
        processDTO.setCustomerName(jsonDTO.getCustomerName());
        processDTO.setResultDescription(jsonDTO.getResultDescription());
        
        // 将额外字段转换为otherInformation JSON字符串（无论字段是否为空都赋值）
        JSONObject otherInfoJson = new JSONObject();
        otherInfoJson.put("duties", jsonDTO.getDuties() != null ? jsonDTO.getDuties() : "");
        otherInfoJson.put("occupation", jsonDTO.getOccupation() != null ? jsonDTO.getOccupation() : "");
        otherInfoJson.put("verifMethod", jsonDTO.getVerifMethod() != null ? jsonDTO.getVerifMethod() : "");
        otherInfoJson.put("visitingLocation", jsonDTO.getVisitingLocation() != null ? jsonDTO.getVisitingLocation() : "");
        otherInfoJson.put("visitingPurpose", jsonDTO.getVisitingPurpose() != null ? jsonDTO.getVisitingPurpose() : "");
        otherInfoJson.put("visitingObjects", jsonDTO.getVisitingObjects() != null ? jsonDTO.getVisitingObjects() : "");
        otherInfoJson.put("telephone", jsonDTO.getTelephone() != null ? jsonDTO.getTelephone() : "");
        otherInfoJson.put("hospitalName", jsonDTO.getHospitalName() != null ? jsonDTO.getHospitalName() : "");
        otherInfoJson.put("diagnoseName", jsonDTO.getDiagnoseName() != null ? jsonDTO.getDiagnoseName() : "");
        otherInfoJson.put("siteSurvey", jsonDTO.getSiteSurvey() != null ? jsonDTO.getSiteSurvey() : "");
        otherInfoJson.put("accidentType", jsonDTO.getAccidentType() != null ? jsonDTO.getAccidentType() : "");
        otherInfoJson.put("witness", jsonDTO.getWitness() != null ? jsonDTO.getWitness() : "");
        otherInfoJson.put("workUnit", jsonDTO.getWorkUnit() != null ? jsonDTO.getWorkUnit() : "");
        otherInfoJson.put("sex", jsonDTO.getSex() != null ? jsonDTO.getSex() : "");
        otherInfoJson.put("relationship", jsonDTO.getRelationship() != null ? jsonDTO.getRelationship() : "");
        otherInfoJson.put("insuranceCoverage", jsonDTO.getInsuranceCoverage() != null ? jsonDTO.getInsuranceCoverage() : "");
        otherInfoJson.put("insuranceTime", jsonDTO.getInsuranceTime() != null ? jsonDTO.getInsuranceTime() : "");
        otherInfoJson.put("coverage", jsonDTO.getCoverage() != null ? jsonDTO.getCoverage() : "");
        
        processDTO.setOtherInformation(otherInfoJson.toJSONString());

        return processDTO;
    }

    @ApiOperation(value = "保存或更新调查经过信息")
    @PostMapping(value = "/saveOrUpdateInvestigateProcess")
    public ResponseResult<Object> saveOrUpdateInvestigateProcess(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        InvestigateProcessDTO investigateProcessDTO = requestData.getInvestigateProcessDTO();

        if (investigateProcessDTO == null) {
            throw new GlobalBusinessException("调查经过数据不能为空");
        }

        investigateProcessController.saveOrUpdateInvestigateProcess(investigateProcessDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "删除调查经过信息")
    @PostMapping(value = "/deleteInvestigateProcess")
    public ResponseResult<Object> deleteInvestigateProcess(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        String idAhcsInvestigateProcess = requestData.getIdAhcsInvestigateProcess();

        if (StringUtils.isEmptyStr(idAhcsInvestigateProcess)) {
            throw new GlobalBusinessException("调查经过ID不能为空");
        }

        investigateProcessController.deleteInvestigateProcess(idAhcsInvestigateProcess);
        return ResponseResult.success();
    }

    /**
     * 查询历史案件列表
     */
    @ApiOperation(value = "查询历史案件列表")
    @PostMapping(value = "/getHistoryCaseListNew")
    public ResponseResult<Object> getHistoryCaseListNew(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();

        if(StringUtils.isEmptyStr(requestData.getReportNo()) || StringUtils.isEmptyStr(requestData.getCaseTimes())){
            throw new GlobalBusinessException("报案号和案件次数不能为空");
        }

        // 获取历史案件列表
        Pager pager = new Pager();
        pager.setPageIndex(requestData.getCurrentPage());
        pager.setPageRows(requestData.getPerPageSize());
        List<ClaimInfoToESVO> claimInfoToESVOs = reportCustomerInfoService.getHistoryCaseListNew(requestData.getReportNo(), requestData.getCaseTimes(), pager);

        // 处理案件列表数据
        processClaimInfoList(claimInfoToESVOs);

        Map<String, Object> result = new HashMap<>();
        result.put("list", claimInfoToESVOs);
        result.put("pager", pager);

        return ResponseResult.success(result);
    }

    /**
     * 处理案件列表数据
     */
    private void processClaimInfoList(List<ClaimInfoToESVO> claimInfoToESVOs) {
        if (claimInfoToESVOs == null || claimInfoToESVOs.isEmpty()) {
            return;
        }

        for (ClaimInfoToESVO claimInfo : claimInfoToESVOs) {
            // 案件状态码值转换
            if (StringUtils.isNotEmpty(claimInfo.getCaseStatus())) {
                // 案件状态映射
                Map<String, String> caseStatusMap = new HashMap<>();
                caseStatusMap.put("0", "已结案");
                caseStatusMap.put("1", "已报案");
                caseStatusMap.put("2", "已理算");

                String result = caseStatusMap.get(claimInfo.getCaseStatus());
                if (StringUtils.isNotEmpty(result)) {
                    claimInfo.setCaseStatus(result);
                }
            }

            // 案件性质码值转换
            if (StringUtils.isNotEmpty(claimInfo.getCaseNature())) {
                Map<String, String> caseNatureNCBSMap = new HashMap<>();
                caseNatureNCBSMap.put("1", "赔付");
                caseNatureNCBSMap.put("2", "零结");
                caseNatureNCBSMap.put("4", "拒赔");
                caseNatureNCBSMap.put("5", "立案注销");
                caseNatureNCBSMap.put("6", "报案注销");

                String ncbsResult = caseNatureNCBSMap.get(claimInfo.getCaseNature());
                if (StringUtils.isNotEmpty(ncbsResult)) {
                    claimInfo.setCaseNature(ncbsResult);
                }
            }

            // 备注信息拼接
            if (StringUtils.isNotEmpty(claimInfo.getProductType())) {
                String remark = null;
                if ("1".equals(claimInfo.getProductType())) {
                    remark= String.format("就诊医院: %s, 就诊疾病: %s, 出险人: %s",
                            claimInfo.getHospitalName() != null ? claimInfo.getHospitalName() : "-",
                            claimInfo.getDiagnoseCode() != null ? claimInfo.getDiagnoseCode() : "-",
                            claimInfo.getObjectName() != null ? claimInfo.getObjectName() : "-");
                } else {
                    remark= String.format("事故经过: %s",
                            claimInfo.getAccidentDetail() != null ? claimInfo.getAccidentDetail() : "-");
                }
                claimInfo.setRemark(remark);
            }
        }
    }

    /**
     * 查询所有单证文件
     */
    @ApiOperation("查询所有单证文件")
    @PostMapping(value = "/getDocumentList")
    public ResponseResult<Object> getDocumentList(@RequestBody InvestigateQueryDTO queryDTO) throws GlobalBusinessException {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        if(StringUtils.isEmptyStr(requestData.getReportNo()) || StringUtils.isEmptyStr(requestData.getCaseTimes())){
            throw new GlobalBusinessException("报案号和案件次数不能为空");
        }

        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setReportNo(requestData.getReportNo());
        fileInfoDTO.setCaseTimes(requestData.getCaseTimes());

        ResponseResult<List<FileInfoVO>> result = docAppFileUploadController.getDocumentList(fileInfoDTO);

        if (result != null && result.getData() != null) {
            // 构造包含documentList有值的DocumentTypeVO列表，并为每个文件对象设置流程类型和小类信息
            List<FileInfoDTO> documentList = new ArrayList<>();
            for (FileInfoVO fileInfoVO : result.getData()) {
                fileInfoVO.getSmallTypeList().stream()
                    .filter(documentTypeVO -> documentTypeVO.getDocumentList() != null &&
                        !documentTypeVO.getDocumentList().isEmpty())
                    .forEach(documentTypeVO -> {
                        // 为每个文件对象设置流程类型和小类信息
                        if (documentTypeVO.getDocumentList() != null) {
                            for (FileInfoDTO fileInfo : documentTypeVO.getDocumentList()) {
                                fileInfo.setFlowType(fileInfoVO.getFlowType());
                                fileInfo.setFlowName(fileInfoVO.getFlowName());
                                fileInfo.setSmallCode(documentTypeVO.getSmallCode());
                                fileInfo.setSmallName(documentTypeVO.getSmallName());
                                if (fileInfo.getFileUrl() != null) {
                                    fileInfo.setFileUrls(new String[]{fileInfo.getFileUrl()});
                                }
                                documentList.add(fileInfo);
                            }
                        }
                    });
            }

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("documentList", documentList);
            return ResponseResult.success(resultMap);
        }

        return ResponseResult.success();
    }

    /**
     * 查询单证文件地址
     */
    @ApiOperation("查询单证文件地址")
    @PostMapping(value = "/getIntranetIOBSDownloadUrl")
    public ResponseResult<Object> getIntranetIOBSDownloadUrl(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        LogUtil.audit("#外部系统调查#获取单证地址#fileId={}, fileName={}", requestData.getFileId(), requestData.getFileName());

        return iobsFileUploadController.getIntranetIOBSDownloadUrl(requestData.getFileId(), requestData.getFileName());
    }

    /**
     * 查询上传单证类型列表
     */
    @ApiOperation("查询上传单证类型列表")
    @PostMapping(value = "/getAllDocumentTypeList")
    public ResponseResult<Object> getAllDocumentTypeList(@RequestBody InvestigateQueryDTO queryDTO) throws GlobalBusinessException {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        LogUtil.audit("#外部系统调查#获取单证类型#");

        ResponseResult<Object> result = documentController.getAllDocumentTypeList(requestData.getFileInfo());
        List<DocumentTypeDTO> documentTypeVOList = (List<DocumentTypeDTO>) result.getData();
        List<DocumentTypeDTO> smallTypeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(documentTypeVOList)) {
            for (DocumentTypeDTO documentTypeDTO : documentTypeVOList) {
                if (!CollectionUtils.isEmpty(documentTypeDTO.getSmallTypeList())) {
                    smallTypeList.addAll(documentTypeDTO.getSmallTypeList());
                }
            }
        }

        return ResponseResult.success(smallTypeList);
    }

    @ApiOperation("上传单证")
    @PostMapping(value = "/uploadDocument")
    @ResponseBody
    public ResponseResult<Object> uploadDocument(@RequestBody InvestigateQueryDTO queryDTO) throws GlobalBusinessException {
        try {
            fileUploadController.uploadDocument(queryDTO.getFileInfoDTO(),queryDTO.getRequest());
        } catch (Exception e) {
            LogUtil.error("上传单证异常", e);
        }
        return ResponseResult.success();
    }

    /**
     * 查询电子保单地址
     */
    @ApiOperation("查询电子保单地址")
    @PostMapping(value = "/getPolicyElectricPdfUrl")
    public ResponseResult<String> getPolicyElectricPdfUrl(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        LogUtil.audit("#外部系统调查#电子保单下载#policyNo={}", requestData.getPolicyNo());

        try {
            String previewUrl = null;
            try {
                //查询电子保单预览URL
                String policyDocumentId = ocasMapper.getPolicyDocumentId(requestData.getPolicyNo());
                UserInfoDTO userDTO = WebServletContext.getUser(); // 外部系统用户
                previewUrl = fileCommonService.getPreviewUrl(policyDocumentId, userDTO.getUserName());
            } catch (Exception e) {
                LogUtil.error("保单号：{}获取预览失败，失败原因：{}", requestData.getPolicyNo(), e.getMessage());
            }

            return ResponseResult.success(previewUrl);

        } catch (Exception e) {
            LogUtil.error("#外部系统调查#电子保单下载#异常", e);
            throw new GlobalBusinessException("获取电子保单下载地址失败：" + e.getMessage());
        }
    }

    /**
     * 查询整案案件列表
     */
    @ApiOperation("查询整案案件列表")
    @PostMapping(value = "/getHistoryCaseList")
    public ResponseResult<Object> getHistoryCaseList(@RequestBody InvestigateQueryDTO queryDTO) {
        try {
            InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
            if(requestData == null || StringUtils.isEmptyStr(requestData.getReportNo())){
                throw new GlobalBusinessException("报案号不能为空");
            }
//            String userCode = requestData.getUserCode();
//            if (StringUtils.isEmptyStr(requestData.getReportNo())) {
//                throw new GlobalBusinessException("报案号不能为空");
//            }
//            String systemCode = umProperties.getSystemCode();
//            QueryUserViewRequestDTO queryUserViewRequestDTO = new QueryUserViewRequestDTO();
//            queryUserViewRequestDTO.setAccount(userCode);
//            queryUserViewRequestDTO.setSystemId(systemCode);
//            queryUserViewRequestDTO.setGatewayName("solomon");
//
//            String nonce = RandomStringUtils.randomAlphanumeric(32);
//            String timestamp = String.valueOf(System.currentTimeMillis());
//            String token = umProperties.getToken();
//            String clientCode = umProperties.getClientCode();
//            String signature = Sha1SignUtil.generateSign(token, timestamp, nonce).toLowerCase();
//
//            //登录人权限
//            LogUtil.info("queryUserInfoSx queryUserViewRequestDTO : {} ,加签信息nonce：{},timestamp:{},token:{},clientCode:{}" , JSON.toJSONString(queryUserViewRequestDTO), nonce, timestamp, token, clientCode);
//            QueryUserViewResponseDTO queryUserViewResponseDTO = ncbsUmRequest.queryUserView(queryUserViewRequestDTO, timestamp, nonce, signature, clientCode);
//            LogUtil.info("queryUserInfoSx queryUserViewResponseDTO : {} " , JSON.toJSONString(queryUserViewResponseDTO));

            // 查询案件列表
            WholeCaseVO wholeCaseVO = new WholeCaseVO();
            wholeCaseVO.setReportNo(requestData.getReportNo());
            wholeCaseVO.setCaseTimes(requestData.getCaseTimes());

            WholeCasePageResult caseListResult = wholeCaseService.getHistoryCaseList(wholeCaseVO);
            List<WholeCaseVO> caseList = null;
            if (caseListResult != null) {
                caseList = caseListResult.getList();
            }

//            if (RapeCollectionUtils.isEmpty(caseList)) {
//                if (queryUserViewResponseDTO != null && queryUserViewResponseDTO.getResult() != null) {
//                    List<RoleDTO> roles = queryUserViewResponseDTO.getResult().getRoles();
//                    if (RapeCollectionUtils.isNotEmpty(roles)) {
//                        boolean hasTPARole = roles.stream()
//                                .anyMatch(role -> "1854".equals(role.getRoleId()) || "1853".equals(role.getRoleId()));
//                        // TPA角色只能查看自己的案件
//                        if (hasTPARole) {
//                            if (caseList != null) {
//                                caseList = caseList.stream()
//                                        .filter(caseVO -> userCode.equals(caseVO.getAssigner()))
//                                        .collect(Collectors.toList());
//                            }
//                        }
//                    }
//                }
//            }

            return paginateList(caseList, requestData);
        } catch (GlobalBusinessException e) {
            LogUtil.error("#外部系统调查#查询整案案件列表#业务异常", e);
            return ResponseResult.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            LogUtil.error("#外部系统调查#查询整案案件列表#系统异常", e);
            return ResponseResult.fail("SYSTEM_ERROR", "系统异常：" + e.getMessage());
        }
    }

    /**
     * 查询整案案件详情
     */
    @ApiOperation("查询整案案件详情")
    @PostMapping(value = "/getCaseInfo")
    public ResponseResult<Map<String, Object>> getCaseInfo(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        if (StringUtils.isEmptyStr(requestData.getReportNo())) {
            throw new GlobalBusinessException("报案号不能为空");
        }
        Map<String, Object> result = new HashMap<>();
        try {
            // 获取基础信息（客户基本信息、报案信息）
            Map<String, Object> basicInfo = investigateTaskAuditPublicService.getBasicInfo(requestData.getReportNo(), requestData.getCaseTimes(), requestData.getUserName());
            result.putAll(basicInfo);

            // 整案信息
            WholeCaseBaseDTO wholeCaseBase = wholeCaseBaseService.getWholeCaseBase(requestData.getReportNo(), requestData.getCaseTimes());
            result.put("wholeCaseBase", wholeCaseBase);

            // 电子保单下载地址
            String previewUrl = investigateTaskAuditPublicService.getPolicyElectricPdfUrl(requestData.getReportNo(), requestData.getUserName());
            result.put("previewUrl", previewUrl);

        } catch (Exception e) {
            LogUtil.error("#外部系统调查#查询整案案件详情#异常", e);
            throw new GlobalBusinessException("查询整案案件详情失败：" + e.getMessage());
        }
        return ResponseResult.success(result);
    }

    /**
     * 获取公共下拉数据列表
     */
    @ApiOperation(value = "获取公共下拉数据列表")
    @PostMapping(value = "/getDataDictList")
    public ResponseResult<Object> getDataDictList(@RequestBody InvestigateQueryDTO queryDTO) {
        InvestigateQueryDTO.RequestData requestData = queryDTO.getRequestData();
        
        if (StringUtils.isEmptyStr(requestData.getCollectionCodeList())) {
            throw new GlobalBusinessException("参数类型码数组不能为空");
        }
        String[] collectionCodeArray = requestData.getCollectionCodeList().split(",");

        ResponseResult<Map<String, List<CommonParameterTinyDTO>>> result =
            commonParameterController.getDataDictList(collectionCodeArray);
        
        return ResponseResult.success(result.getData());
    }

    /**
     * 返回queryDTO中的jsonStr字段
     * @param queryDTO 包含jsonStr字段的查询对象
     * @return queryDTO中的jsonStr字段内容
     */
    @ApiOperation(value = "返回queryDTO中的jsonStr字段")
    @PostMapping(value = "/echoJson")
    public ResponseResult<Object> echoJson(@RequestBody InvestigateQueryDTO queryDTO) {
        return ResponseResult.success(queryDTO.getRequestData().getJsonStr());
    }

    /**
     * 公共分页方法
     */
    private <T> ResponseResult<Object> paginateList(List<T> dataList, InvestigateQueryDTO.RequestData requestData) {
        if (RapeCollectionUtils.isEmpty(dataList)) {
            Map<String, Object> result = new HashMap<>();
            result.put("list", dataList);
            result.put("pager", new Pager(1, 10, 0));
            return ResponseResult.success(result);
        }

        // 获取分页参数，使用新的字段，如果为空则使用默认值
        int currentPage = requestData.getCurrentPage() != null ? requestData.getCurrentPage() : 1;
        int perPageSize = requestData.getPerPageSize() != null ? requestData.getPerPageSize() : 10;

        // 创建分页对象
        Pager pager = new Pager(currentPage, perPageSize, dataList.size());

        // 计算分页范围
        int fromIndex = (currentPage - 1) * perPageSize;
        int toIndex = Math.min(fromIndex + perPageSize, dataList.size());

        // 对list进行物理分页
        List<T> paginatedList = dataList.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("list", paginatedList);
        result.put("pager", pager);

        return ResponseResult.success(result);
    }

}