package com.paic.ncbs.claim.controller.mng;


import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.fileupolad.SilkBagParamVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "锦囊信息相关")
@RestController
@RequestMapping(value = "/mng/app/SilkBagAction")
public class SilkBagController extends BaseController {

    @ApiOperation("显示锦囊建议")
    @PostMapping(value = "/showSilkBag")
    public ResponseResult<String> showSilkBag(HttpServletRequest request, @RequestBody SilkBagParamVO bagParamVO) throws GlobalBusinessException {
        bagParamVO.setUserId(WebServletContext.getUserId());
        return ResponseResult.success("");
    }


}
