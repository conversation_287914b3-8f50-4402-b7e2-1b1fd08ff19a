package com.paic.ncbs.claim.service.user;

import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;

import java.util.List;

public interface DepartmentService {

    DepartmentDTO queryDepartmentInfoByDeptCode(String departmentCode);

    List<DepartmentDTO> getDepartmentList();


    /**
     * 获取最上级机构，如果上级机构是总部，返回总部；如果上级机构是分公司，则返回具体分公司
     * @return
     */
    String getTopDepartment(String deptCode);

}
