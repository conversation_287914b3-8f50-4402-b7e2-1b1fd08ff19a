package com.paic.ncbs.claim.dao.entity.qualitychecke;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("clms_quality_record")
public class ClmsQualityRecord {
    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 质检任务id
     */
    @TableField("quality_id")
    private String qualityId;

    /**
     * 处理人
     */
    @TableField("handler")
    private String handler;

    /**
     * 处理时间
     */
    @TableField("process_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime processTime;

    /**
     * 环节（0-质检/1-审核）
     */
    @TableField("operate_type")
    private String node;

    /**
     * 轨迹审批意见
     */
    @TableField("locus_idea")
    private String locusIdea;

    /**
     * 轨迹详细意见
     */
    @TableField("locus_detail")
    private String locusDetail;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Short caseTimes;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sysUtime;
}
