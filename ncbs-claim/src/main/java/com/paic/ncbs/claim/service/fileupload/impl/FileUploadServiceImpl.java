package com.paic.ncbs.claim.service.fileupload.impl;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.constant.ReportConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.controller.doc.FileUploadController;
import com.paic.ncbs.claim.dao.mapper.doc.DocumentMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.api.DocumentListDTO;
import com.paic.ncbs.claim.model.dto.api.QueryDocumentInfoDTO;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentReqDTO;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentResDTO;
import com.paic.ncbs.claim.model.dto.doc.DocumentTypeDTO;
import com.paic.ncbs.claim.model.dto.fileupload.*;
import com.paic.ncbs.claim.model.vo.doc.DocSmallTypeVO;
import com.paic.ncbs.claim.model.vo.doc.FileDocumentVO;
import com.paic.ncbs.claim.model.vo.fileupolad.DocumentTypeVO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.fileupload.DocumentTypeService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.service.fileupload.FileViewRecordService;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service("fileUploadService")
@RefreshScope
public class FileUploadServiceImpl implements FileUploadService {

    @Value("${iobs.enable}")
    private boolean iobs;

    @Autowired
    private FileInfoMapper fileInfoMapper;

    @Autowired
    private DocumentTypeService documentTypeService;

    @Autowired
    private FileViewRecordService fileViewRecordService;

    @Autowired
    private DocumentMapper documentDao;

    @Autowired
    WholeCaseService wholeCaseService;

    @Autowired
    CommonParameterService commonParameterService;

    @Autowired
    CaseProcessService caseProcessService;

    @Autowired
    TaskInfoService taskInfoService;

    @Autowired
    PaymentInfoService paymentInfoService;

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @Autowired
    FileUploadService fileUploadService;

    @Autowired
    private FileUploadController fileUploadController;

    // 外部上传创建人
    private static final String EXTERNAL_UPLOAD_CREATED_BY = "system";

    // 外部上传更新人
    private static final String EXTERNAL_UPLOAD_UPDATED_BY = "system";

    // 外部上传上传人员
    private static final String EXTERNAL_UPLOAD_UPLOADPERSONNEL = EXTERNAL_UPLOAD_CREATED_BY + FileUploadConstants.DOCUMENT_UPLOADPERSONNEL_SEPARATOR + EXTERNAL_UPLOAD_UPDATED_BY;


    @Override
    @Transactional
    public String addFilesInfo(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {

        List<FileInfoDTO> fileInfoDTOList = this.getFileGroupIdForDocument(fileInfoDTO);

        String documentGroupId = fileInfoDTO.getDocumentGroupId();

        List<FileDocumentVO> documentDTOList = this.getDocumentDTOList(fileInfoDTO);
        String id = fileInfoMapper.getDocumentItemId();
        documentDTOList.get(0).setDocumentGroupItemsId(id);
        fileInfoMapper.createDocument(documentDTOList.get(0));

        if (ListUtils.isEmptyList(fileInfoDTOList) && FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(fileInfoDTO.getFirstUpload())) {
            fileInfoDTO.setIdAhcsFileInfo(UuidUtil.getUUID());
            fileInfoMapper.addFileInfo(fileInfoDTO);
        }
        return documentGroupId;
    }

    @Override
    @Transactional
    public UploadDocumentResDTO addFilesInfo(UploadDocumentReqDTO uploadDocumentReqDTO) {
        // 1、组装（对外）上传单证的documentGroupId=“报案号
        String documentGroupId = uploadDocumentReqDTO.getReportNo();

        // 2、插入document表数据
        for (DocumentListDTO documentListDTO : uploadDocumentReqDTO.getDocumentList()) {
            // 这里的设值，可能需要修改:document表中DOCUMENT_SIZE还没有落值（建议是让渠道传，因为根据prd，渠道需要校验了文件大小不能超过5MB，所以他肯定是能拿到文件大小的）
            List<FileDocumentVO> documentDTOList = this.getDocumentDTOList(uploadDocumentReqDTO, documentListDTO, documentGroupId);
            String id = fileInfoMapper.getDocumentItemId();
            documentDTOList.get(0).setDocumentGroupItemsId(id);
            fileInfoMapper.apiCreateDocument(documentDTOList.get(0));
        }
        // 3、插入clms_file_info表
        UploadDocumentResDTO uploadDocumentResDTO = addFileInfo(uploadDocumentReqDTO, documentGroupId);
        LogUtil.audit("（对外）上传单证完成");
        if (org.apache.commons.lang3.StringUtils.equals("manualSettle", uploadDocumentReqDTO.getTaskCode())) {
            // 4、开始处理-客户补材完成后回调
            LogUtil.audit("开始处理-客户补材完成后回调");
            documentTypeService.customerSupplementsSuccess(uploadDocumentReqDTO.getReportNo(), uploadDocumentReqDTO.getCaseTimes(), null);
            LogUtil.audit("结束处理-客户补材完成后回调");
        }
        return uploadDocumentResDTO;
    }

    /**
     * 插入clms_file_info表
     * @param uploadDocumentReqDTO
     * @param documentGroupId
     * @return
     */
    private UploadDocumentResDTO addFileInfo(UploadDocumentReqDTO uploadDocumentReqDTO, String documentGroupId) {
        // 1、初始化出参
        UploadDocumentResDTO uploadDocumentResDTO = new UploadDocumentResDTO();
        uploadDocumentResDTO.setBatchNo(Long.toString(System.currentTimeMillis()));

        // 2、clms_file_info表防止重复插入（跟据条件查询CLMS_FILE_INFO表数据是否存在，存在无需重复插入CLMS_FILE_INFO表，直接返回）
        FileInfoDTO fileInfoDTOTmp = initFileInfoDTOTmp(uploadDocumentReqDTO, documentGroupId);
        List<FileInfoDTO> fileInfoDTOList = this.getFileGroupIdForDocument(fileInfoDTOTmp);
        uploadDocumentResDTO.setDocumentGroupId(fileInfoDTOTmp.getDocumentGroupId());
        if (CollectionUtils.isEmpty(fileInfoDTOList)) {
            fileInfoDTOTmp.setIdAhcsFileInfo(UuidUtil.getUUID());
            fileInfoMapper.addFileInfo(fileInfoDTOTmp);
            LogUtil.audit("保存文件成功,需插入CLMS_FILE_INFO表");
        } else {
            LogUtil.audit("保存文件成功,无需插入CLMS_FILE_INFO表");
        }
        uploadDocumentResDTO.setReportNo(uploadDocumentReqDTO.getReportNo());
        return uploadDocumentResDTO;
    }

    /**
     * 初始化FileInfoDTO入参
     * @param uploadDocumentReqDTO
     * @return
     */
    private FileInfoDTO initFileInfoDTOTmp(UploadDocumentReqDTO uploadDocumentReqDTO, String documentGroupId) {
        FileInfoDTO fileInfoDTOTmp = new FileInfoDTO();
        fileInfoDTOTmp.setReportNo(uploadDocumentReqDTO.getReportNo());
        fileInfoDTOTmp.setCaseTimes(uploadDocumentReqDTO.getCaseTimes());
        fileInfoDTOTmp.setCreatedBy(EXTERNAL_UPLOAD_CREATED_BY);
        fileInfoDTOTmp.setUpdatedBy(EXTERNAL_UPLOAD_UPDATED_BY);
        fileInfoDTOTmp.setDocumentGroupId(documentGroupId);
        fileInfoDTOTmp.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
        fileInfoDTOTmp.setFlowType(StringUtils.getFlowTypeByCaseTimes(uploadDocumentReqDTO.getCaseTimes()));
        return fileInfoDTOTmp;
    }

    public List<FileDocumentVO> getDocumentDTOList(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        List<FileDocumentVO> documentDTOList = new ArrayList<>();
        FileDocumentVO documentDTO = new FileDocumentVO();
        String userId = fileInfoDTO.getCreatedBy();
        String uploadPerson = fileInfoDTO.getUploadPersonnel();
        String fileName = fileInfoDTO.getFileName();
        String taskCode = fileInfoDTO.getTaskCode();
        Integer applyTimes = fileInfoDTO.getApplyTimes();
        StringBuilder property = new StringBuilder();
        if (!FileUploadConstants.FILE_TYPE_RECORD.equals(fileInfoDTO.getFileType())) {
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_SUPPLEMENT);
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
            property.append(fileInfoDTO.getSupplement());
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_BATCHNO);
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
            property.append(fileInfoDTO.getBatchNo());
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
            String bigCode = documentTypeService.getBigCodeBySmallCode(fileInfoDTO.getSmallCode());
            if (StringUtils.isEmptyStr(bigCode)) {
                LogUtil.info("#上传单证#细类代码错误reportNo=" + fileInfoDTO.getReportNo() + ",smallCode=" + fileInfoDTO.getSmallCode());
                throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_UPLOAD_FILE_PARAM);
            }
            documentDTO.setDocumentClass(bigCode);
            documentDTO.setDocumentType(fileInfoDTO.getSmallCode());
            documentDTO.setRemark(fileInfoDTO.getAhcsLv3Code());
        }
//        String recordNo = fileInfoDTO.getRecordNo();
//        setDocumentType(fileInfoDTO, documentDTO, recordNo);
        property.append(FileUploadConstants.DOCUMENT_PROPERTY_TASKCODE);
        property.append(FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
        property.append(taskCode);
        property.append(FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
        if (applyTimes != null) {
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_APPLYTIMES);
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
            property.append(applyTimes);
            property.append(FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
        }
        String documentProperty = property.toString();
        if (StringUtils.isNotEmpty(documentProperty)) {
            documentProperty = documentProperty.substring(0, documentProperty.length() - 1);
            documentDTO.setDocumentProperty(documentProperty);
        }
        documentDTO.setOriginName(fileName);
        documentDTO.setDocumentFormat(fileInfoDTO.getFileFormat());
        documentDTO.setDocumentName(fileName);
        documentDTO.setUploadPersonnel(uploadPerson);
        documentDTO.setCreatedBy(userId);
        documentDTO.setUpdatedBy(userId);
        if (fileInfoDTO.getFileSize() != 0L) {
            documentDTO.setDocumentSize(String.valueOf(fileInfoDTO.getFileSize()));
        }
        documentDTO.setDocumentSource(fileInfoDTO.getUploadSource());
        documentDTO.setUploadPath(fileInfoDTO.getFileId());
        documentDTO.setBucketName(fileInfoDTO.getBucketName());
        documentDTO.setDocumentStatus("Y");
        documentDTO.setDocumentGroupId(fileInfoDTO.getDocumentGroupId());
        documentDTOList.add(documentDTO);
        return documentDTOList;
    }

    public List<FileDocumentVO> getDocumentDTOList(UploadDocumentReqDTO fileInfoDTO, DocumentListDTO documentListDTO, String documentGroupId) throws GlobalBusinessException {
        List<FileDocumentVO> documentDTOList = new ArrayList<>();
        FileDocumentVO documentDTO = new FileDocumentVO();
        String fileName = documentListDTO.getFileName();
        String bigCode = documentTypeService.getBigCodeBySmallCode(documentListDTO.getSmallTypeCode());
        if (StringUtils.isEmptyStr(bigCode)) {
            LogUtil.info("#上传单证#细类代码错误reportNo=" + fileInfoDTO.getReportNo() + ",smallCode=" + documentListDTO.getSmallTypeCode());
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_UPLOAD_FILE_PARAM);
        }
        documentDTO.setDocumentClass(bigCode);
        documentDTO.setDocumentType(documentListDTO.getSmallTypeCode());
        documentDTO.setOriginName(fileName);
        documentDTO.setDocumentFormat(documentListDTO.getFileType());
        documentDTO.setDocumentName(fileName);
        documentDTO.setUploadPersonnel(EXTERNAL_UPLOAD_UPLOADPERSONNEL);
        documentDTO.setCreatedBy(EXTERNAL_UPLOAD_CREATED_BY);
        documentDTO.setUpdatedBy(EXTERNAL_UPLOAD_UPDATED_BY);
        documentDTO.setDocumentSize(documentListDTO.getFileSize());
        documentDTO.setUploadPath(documentListDTO.getFileUrl());
        documentDTO.setDocumentStatus("Y");
        documentDTO.setDocumentGroupId(documentGroupId);
        documentDTO.setDocumentSource("03");
        documentDTO.setNetworkFlag("intranet");
        documentDTOList.add(documentDTO);
        return documentDTOList;
    }

    private void setDocumentType(FileInfoDTO fileInfoDTO, FileDocumentVO documentDTO, String recordNo) {
        if (StringUtils.isNotEmpty(recordNo)) {
            if (FileUploadConstants.CONFIRM_CLAIM.equalsIgnoreCase(fileInfoDTO.getTaskCode())) {
                documentDTO.setDocumentType(FileUploadConstants.RECORD_TYPE_CONFIRM_CLAIM);
            } else {
                documentDTO.setDocumentType(FileUploadConstants.RECORD_TYPE_ZERO_APPLY);
            }
        }
    }

    @Override
    public List<FileInfoDTO> getFileGroupIdForDocument(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        return fileInfoMapper.getFileGroupId(fileInfoDTO);
    }

    @Override
    public List<FileInfoVO> getDocumentList(String userId, FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        List<FileDocumentGroupDTO> documentGroupDTOList = new ArrayList<>();
        List<FileInfoVO> fileInfoVOList = new ArrayList<>();
        List<FileInfoDTO> documentGroupId = this.getFileGroupIdForDocument(fileInfoDTO);
        LogUtil.audit("查看单证获取文件组列表成功 文件组列表为:" + JSONObject.toJSONString(documentGroupId));

        if (ListUtils.isNotEmpty(documentGroupId)) {
            documentGroupDTOList = getDocumentByGroupIdArray(documentGroupId, FileUploadConstants.ALL_FLAG);
        }
        if (ListUtils.isNotEmpty(documentGroupDTOList)) {
            for (FileDocumentGroupDTO documentGroupDTO : documentGroupDTOList) {
                String documentGroupType = documentGroupDTO.getDocumentGroupType();
                if (StringUtils.isEmptyStr(documentGroupType)) {
                    documentGroupType = "01";
                }
                String caseTimesStr = "1";
                if (documentGroupType.startsWith("0")) {
                    caseTimesStr = documentGroupType.substring(1);
                }
                List<DocumentTypeVO> documentTypeVOList = this.getDocumentTypeVOList(documentGroupDTO, userId, fileInfoDTO);
                FileInfoVO fileInfoVO = new FileInfoVO();
                fileInfoVO.setDocumentGroupId(documentGroupDTO.getDocumentGroupId());
                fileInfoVO.setFlowType(documentGroupType);
                fileInfoVO.setFlowName(FileUploadConstants.flowType.get(documentGroupType));
                this.orderDocument(documentTypeVOList);
                fileInfoVO.setSmallTypeList(documentTypeVOList);
                fileInfoVOList.add(fileInfoVO);
            }
        }

        Map<String, Set<String>> exemptMap = documentTypeService.getExemptDocumentByReportNo(fileInfoDTO.getReportNo());
        if (exemptMap != null && exemptMap.size() > 0) {
            List<DocumentTypeDTO> smallTypeList = documentTypeService.getDocumentSmallTypeList();
            if (ListUtils.isEmptyList(smallTypeList)) {
                LogUtil.info(FileUploadConstants.DOCUMENT_SMALLTYPE_IS_NULL);
                throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_DOCUMENT_EMPTY_TYPE);
            }
            Map<String, DocumentTypeDTO> smallMap = new HashMap<>();
            for (DocumentTypeDTO d : smallTypeList) {
                smallMap.put(d.getSmallCode(), d);
            }
            if (ListUtils.isEmptyList(fileInfoVOList)) {
                for (Map.Entry<String, Set<String>> entry : exemptMap.entrySet()) {
                    addExemptFlag(fileInfoVOList, entry.getKey(), entry.getValue(), smallMap);
                }

            } else {
                Map<String, FileInfoVO> fileInfoVOMap = new HashMap<>();
                for (FileInfoVO f : fileInfoVOList) {
                    fileInfoVOMap.put(f.getFlowType(), f);
                }
                for (Map.Entry<String, Set<String>> entry : exemptMap.entrySet()) {
                    if (fileInfoVOMap.get(entry.getKey()) == null) {
                        continue;
                    }
                    List<DocumentTypeVO> documentTypeVOList = fileInfoVOMap.get(entry.getKey()).getSmallTypeList();
                    String groupId = fileInfoVOMap.get(entry.getKey()).getDocumentGroupId();
                    if (ListUtils.isEmptyList(documentTypeVOList)) {
                        addExemptFlag(fileInfoVOList, entry.getKey(), entry.getValue(), smallMap);
                    } else {
                        modifyExemptFlag(fileInfoVOList, entry.getKey(), entry.getValue(), smallMap, documentTypeVOList, groupId, fileInfoVOMap);
                    }

                }
            }
        }

        if (ListUtils.isNotEmpty(documentGroupDTOList)) {
            FileInfoVO removedFileUploadVO = new FileInfoVO();
            removedFileUploadVO.setDocumentGroupId(FileUploadConstants.DOCUMENT_GROUP_ID_REMOVED);
            removedFileUploadVO.setFlowType(FileUploadConstants.DOCUMENT_GROUP_ID_REMOVED);
            removedFileUploadVO.setFlowName(FileUploadConstants.DOCUMENT_TYPE_REMOVED_NAME);
            List<DocumentTypeVO> removedDocumentTypeVO = this.getRemovedDocumentTypeVOList(documentGroupDTOList);
            this.orderDocument(removedDocumentTypeVO);
            removedFileUploadVO.setSmallTypeList(removedDocumentTypeVO);
            fileInfoVOList.add(removedFileUploadVO);
        }
        return fileInfoVOList;
    }

    @Override
    public String getDocumentGroupIdByDb(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        List<FileInfoDTO> documentGroupIdList = fileInfoMapper.getFileGroupId(fileInfoDTO);
        String documentGroupId = "";
        if (ListUtils.isNotEmpty(documentGroupIdList)) {
            documentGroupId = documentGroupIdList.get(0).getDocumentGroupId();
        }
        return documentGroupId;
    }

//    @Override
//    public List<FileInfoDTO> getCurrentApplyDocument(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
//        List<FileInfoDTO> fileInfoList = null;
//        FileDocumentGroupDTO documentGroupDTO = null;
//        String documentGroupId = fileInfoDTO.getDocumentGroupId();
//        if (StringUtils.isNotEmpty(documentGroupId)) {
//            documentGroupDTO = fileUploadSAOMock.getDocumentGroupInfo(FileUploadConstants.IMAGE_SERVERID, FileUploadConstants.SYSTEMID, documentGroupId, FileUploadConstants.INVALIDATION_FLAG);
//        }
//        List<FileDocumentDTO> documentDTOList = null;
//        if (documentGroupDTO != null) {
//            documentDTOList = documentGroupDTO.getDocuments();
//            LogUtil.audit(FileUploadConstants.LOG_GET_DOCUMENT_GROUPID + documentGroupId);
//        }
//
//        LogUtil.audit("重开或零注环节上传的单证：" + JSONObject.toJSONString(documentDTOList));
//        fileInfoList = new ArrayList<FileInfoDTO>();
//        List<String> taskCodeList = fileInfoDTO.getTaskCodeList();
//        String applyTimesStr = String.valueOf(fileInfoDTO.getApplyTimes());
//        for (FileDocumentDTO documentDTO : documentDTOList) {
//            String documentProperty = documentDTO.getDocumentProperty();
//            if (StringUtils.isEmptyStr(documentProperty)) {
//                continue;
//            }
//            Map<String, String> propertyMap = StringUtils.parseMessageVariables(documentProperty, FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
//            String taskCodeValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_TASKCODE);
//            String applyTimesValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_APPLYTIMES);
//            if (taskCodeList.contains(taskCodeValue) && applyTimesStr.equals(applyTimesValue)) {
//                FileInfoDTO fileInfo = new FileInfoDTO();
//                fileInfo.setFileUrl(documentDTO.getUrl());
//                fileInfo.setFileName(documentDTO.getOriginName());
//                fileInfoList.add(fileInfo);
//            }
//        }
//        LogUtil.audit("重开或零注环节已上传的单证：" + JSONObject.toJSONString(fileInfoList));
//        return fileInfoList;
//    }

//    @Override
//    public String getDocumentGroupId(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
//        if (StringUtils.isNotEmpty(fileInfoDTO.getDocumentGroupId())) {
//            return fileInfoDTO.getDocumentGroupId();
//        }
//        String reportNo = fileInfoDTO.getReportNo();
//        FileDocumentGroupDTO documentGroupDTO = new FileDocumentGroupDTO();
//        documentGroupDTO.setBusinessCode(reportNo);
//        documentGroupDTO.setDocumentGroupName(FileUploadConstants.DOCUMENT_GROUP_NAME);
//        documentGroupDTO.setDocumentGroupDesc(FileUploadConstants.DOCUMENT_GROUP_NAME_DESC);
//        documentGroupDTO.setDataSource(FileUploadConstants.DATA_TWO);
//        if (FileUploadConstants.FILE_TYPE_RECORD.equals(fileInfoDTO.getFileType())) {
//            documentGroupDTO.setDocumentGroupType(FileUploadConstants.DOCUMENT_GROUP_TYPE);
//        } else {
//            String flowType = fileInfoDTO.getFlowType();
//            documentGroupDTO.setDocumentGroupType(flowType);
//        }
//        documentGroupDTO.setCreatedBy(fileInfoDTO.getCreatedBy());
//        documentGroupDTO.setUpdatedBy(fileInfoDTO.getUpdatedBy());
//        String groupId = "";
//        return groupId;
//    }

//    @Override
//    public FileInfoVO getRecordListByTcRt(String documentGroupId, List<String> taskCodeList, Integer applyTimes, String recordType) throws GlobalBusinessException {
//        FileInfoVO fileInfoVO = null;
//        FileDocumentGroupDTO documentGroupDTO = fileUploadSAOMock.getDocumentGroupInfo(FileUploadConstants.IMAGE_SERVERID,
//                FileUploadConstants.SYSTEMID, documentGroupId, FileUploadConstants.INVALIDATION_FLAG);
//        if (documentGroupDTO != null && documentGroupDTO.getDocuments() != null) {
//            LogUtil.audit("根据环节号、录音类型查询录音列表（查询本次零注申请上传的录音） 文件组ID为：" + documentGroupDTO.getDocumentGroupId());
//            boolean isZeroCancel = taskCodeList.contains(TacheConstants.ZERO_CANCEL_APPLY) || taskCodeList.contains(TacheConstants.ZERO_CANCEL_AUDIT);
//            fileInfoVO = new FileInfoVO();
//            fileInfoVO.setDocumentGroupId(documentGroupId);
//            List<FileInfoDTO> fileInfoList = new ArrayList<FileInfoDTO>();
//            List<FileDocumentDTO> documentDTOList = documentGroupDTO.getDocuments();
//            for (FileDocumentDTO documentDTO : documentDTOList) {
//                String documentType = documentDTO.getDocumentType();
//                String documentProperty = documentDTO.getDocumentProperty();
//                boolean isRecordType = StringUtils.isEmptyStr(documentType) || !recordType.equals(documentType);
//                if (isRecordType || StringUtils.isEmptyStr(documentProperty)) {
//                    continue;
//                }
//                String storageType = documentDTO.getStorageType();
//                String bucketName = documentDTO.getBucketName();
//                if (isZeroCancel || FileUploadConstants.RECORD_TYPE_ZERO_APPLY.equals(recordType)) {
//                    FileInfoDTO fileInfoDTO = new FileInfoDTO();
//                    fileInfoDTO.setFileName(documentDTO.getOriginName());
//                    fileInfoDTO.setFileUrl(documentDTO.getUrl());
//                    fileInfoDTO.setFileId(documentDTO.getUploadPath());
//                    fileInfoDTO.setStorageType(storageType);
//                    fileInfoDTO.setBucketName(bucketName);
//                    try {
//                        fileInfoDTO.setUploadDate(DateUtils.parseToFormatString(documentDTO.getUpdatedDate(), FileUploadConstants.FORMAL_TIME_FORMAT));
//                    } catch (ParseException e) {
//
//                    }
//                    fileInfoList.add(fileInfoDTO);
//                } else {
//                    Map<String, String> propertyMap = StringUtils.parseMessageVariables(documentProperty, FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
//                    String taskCodeValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_TASKCODE);
//                    if (taskCodeList.contains(taskCodeValue)) {
//                        FileInfoDTO fileInfoDTO = new FileInfoDTO();
//                        fileInfoDTO.setFileName(documentDTO.getOriginName());
//                        fileInfoDTO.setFileUrl(documentDTO.getUrl());
//                        fileInfoDTO.setStorageType(storageType);
//                        fileInfoDTO.setBucketName(bucketName);
//                        try {
//                            fileInfoDTO.setUploadDate(DateUtils.parseToFormatString(documentDTO.getUpdatedDate(), FileUploadConstants.FORMAL_TIME_FORMAT));
//                        } catch (ParseException e) {
//                            e.printStackTrace();
//                        }
//                        fileInfoList.add(fileInfoDTO);
//                    }
//                }
//            }
//            int recordSize = fileInfoList.size();
//            LogUtil.audit("#本次零注申请上传的零注申请类型的录音数量为# recordSize=" + recordSize);
//            if (FileUploadConstants.RECORD_TYPE_ZERO_APPLY.equals(recordType) && recordSize > FileUploadConstants.RECORD_ZERO_APPLY_COUNT) {
//                this.sortList(fileInfoList);
//                fileInfoList = fileInfoList.subList(0, 3);
//                LogUtil.audit("#最新上传的3个零注申请类型的录音数量为# recordSize3=" + fileInfoList.size());
//            }
//            fileInfoVO.setFileInfoList(fileInfoList);
//        }
//        return fileInfoVO;
//    }
//
//    @Override
//    public int getZeroRecordCount(String documentGroupId, List<String> taskCodeList, Integer applyTimes) throws GlobalBusinessException {
//        int zeroRecordCount = 0;
//        String applyTimesStr = String.valueOf(applyTimes);
//        FileDocumentGroupDTO documentGroupDTO = fileUploadSAOMock.getDocumentGroupInfo(FileUploadConstants.IMAGE_SERVERID, FileUploadConstants.SYSTEMID, documentGroupId, FileUploadConstants.INVALIDATION_FLAG);
//        if (documentGroupDTO != null && documentGroupDTO.getDocuments() != null) {
//            LogUtil.audit("根据环节号、录音类型查询录音列表（查询本次零注申请上传的录音） 文件组ID为：" + documentGroupDTO.getDocumentGroupId());
//            int zeroApplyRecordCount = 0;
//            List<FileDocumentDTO> documentDTOList = documentGroupDTO.getDocuments();
//            for (FileDocumentDTO documentDTO : documentDTOList) {
//                String documentType = documentDTO.getDocumentType();
//                String documentProperty = documentDTO.getDocumentProperty();
//                String recordType = documentDTO.getDocumentType();
//                boolean isRecordType = StringUtils.isEmptyStr(documentType);
//                if (isRecordType || StringUtils.isEmptyStr(documentProperty)) {
//                    continue;
//                }
//                if (FileUploadConstants.RECORD_TYPE_ZERO_APPLY.equals(recordType)) {
//                    zeroApplyRecordCount++;
//                }
//                Map<String, String> propertyMap = StringUtils.parseMessageVariables(documentProperty, FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
//                String taskCodeValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_TASKCODE);
//                String applyTimesValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_APPLYTIMES);
//                if (taskCodeList.contains(taskCodeValue) && applyTimesStr.equals(applyTimesValue)) {
//                    zeroRecordCount++;
//                }
//            }
//            LogUtil.audit("根查询本次零注申请上传录音数量为zeroRecordCount=" + zeroRecordCount + ",zeroApplyRecordCount=" + zeroApplyRecordCount);
//            if (zeroRecordCount == 0) {
//                if (zeroApplyRecordCount >= 1) {
//                    zeroRecordCount = 0;
//                } else {
//                    zeroRecordCount = 1;
//                }
//            }
//        }
//        return zeroRecordCount;
//    }

//    @Override
//    public boolean validIsFileUploadDocument(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
//        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
//        String documentGroupId = this.getDocumentGroupIdByDb(fileInfoDTO);
//        if (TacheConstants.ZERO_CANCEL_APPLY.equals(fileInfoDTO.getTaskCode()) && StringUtils.isEmptyStr(documentGroupId)) {
//            return false;
//        }
//        FileDocumentGroupDTO documentGroupDTO = fileUploadSAOMock.getDocumentGroupInfo(FileUploadConstants.IMAGE_SERVERID, FileUploadConstants.SYSTEMID, documentGroupId, FileUploadConstants.INVALIDATION_FLAG);
//        List<FileDocumentDTO> documentDTOList = null;
//        if (documentGroupDTO != null) {
//            documentDTOList = documentGroupDTO.getDocuments();
//            LogUtil.audit(FileUploadConstants.LOG_GET_DOCUMENT_GROUPID + documentGroupDTO.getDocumentGroupId());
//        }
//
//        String applyTimes = String.valueOf(fileInfoDTO.getApplyTimes());
//        for (FileDocumentDTO documentDTO : documentDTOList) {
//            String documentProperty = documentDTO.getDocumentProperty();
//            if (StringUtils.isEmptyStr(documentProperty)) {
//                continue;
//            }
//            Map<String, String> propertyMap = StringUtils.parseMessageVariables(documentProperty, FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
//            String taskCodeValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_TASKCODE);
//            String applyTimesValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_APPLYTIMES);
//            if (fileInfoDTO.getTaskCodeList().contains(taskCodeValue) && applyTimes.equals(applyTimesValue)) {
//                return true;
//            }
//        }
//        return false;
//    }

//    @Override
//    public FileInfoVO getZeroRecordList(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
//        String documentGroupId = this.getDocumentGroupIdByDb(fileInfoDTO);
//        FileInfoVO fileInfoVO = new FileInfoVO();
//        fileInfoVO.setCaseTimes(fileInfoDTO.getCaseTimes());
//        fileInfoVO.setDocumentGroupId(documentGroupId);
//        List<FileInfoDTO> fileInfoList = new ArrayList<>();
//        FileDocumentGroupDTO documentGroupDTO = fileUploadSAOMock.getDocumentGroupInfo(FileUploadConstants.IMAGE_SERVERID, FileUploadConstants.SYSTEMID, documentGroupId, FileUploadConstants.INVALIDATION_FLAG);
//        if (documentGroupDTO != null && documentGroupDTO.getDocuments() != null) {
//            LogUtil.audit("获取已上传的录音列表 文件组ID为：" + documentGroupDTO.getDocumentGroupId());
//            Map<String, String> recordTypeMap = this.getRecordTypeMap();
//            List<FileDocumentDTO> documentDTOList = documentGroupDTO.getDocuments();
//            for (FileDocumentDTO documentDTO : documentDTOList) {
//                String recordType = documentDTO.getDocumentType();
//                String documentProperty = documentDTO.getDocumentProperty();
//                boolean isRecordType = StringUtils.isEmptyStr(recordType) || !FileUploadConstants.RECORD_TYPE_ZERO_APPLY.equals(recordType);
//                if (isRecordType || StringUtils.isEmptyStr(documentProperty)) {
//                    continue;
//                }
//                FileInfoDTO fileInfo = this.setRecordInfo(documentDTO);
//                try {
//                    fileInfo.setUploadDate(DateUtils.parseToFormatString(documentDTO.getUpdatedDate(), FileUploadConstants.FORMAL_TIME_FORMAT));
//                } catch (ParseException e) {
//
//                }
//                fileInfo.setRecordTypeName(recordTypeMap.get(recordType));
//                fileInfoList.add(fileInfo);
//            }
//            fileInfoVO.setFileInfoList(fileInfoList);
//        }
//        return fileInfoVO;
//    }
//
//    @Override
//    public boolean validIsRecordUpload(FileInfoDTO fileInfoDTO) {
//
//        LogUtil.audit("#校验本次（如零注）申请 是否已上传录音#入参#fileInfoDTO=" + fileInfoDTO);
//        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_RECORD);
//        fileInfoDTO.setRecordType(FileUploadConstants.RECORD_TYPE_ZERO_APPLY);
//
//
//        String documentGroupId = this.getDocumentGroupIdByDb(fileInfoDTO);
//
//        if (StringUtils.isEmptyStr(documentGroupId)) {
//            return false;
//        }
//        fileInfoDTO.setDocumentGroupId(documentGroupId);
//        FileDocumentGroupDTO documentGroupDTO = fileUploadSAOMock.getDocumentGroupInfo(FileUploadConstants.IMAGE_SERVERID, FileUploadConstants.SYSTEMID, fileInfoDTO.getDocumentGroupId(), FileUploadConstants.INVALIDATION_FLAG);
//
//        if (documentGroupDTO != null && documentGroupDTO.getDocuments() != null) {
//            LogUtil.audit("获取已上传的 录音列表 文件组ID为：" + documentGroupDTO.getDocumentGroupId());
//
//            String applyTimesStr = String.valueOf(fileInfoDTO.getApplyTimes());
//            List<FileDocumentDTO> documentDTOList = documentGroupDTO.getDocuments();
//
//            for (FileDocumentDTO documentDTO : documentDTOList) {
//                String documentType = documentDTO.getDocumentType();
//
//                String documentProperty = documentDTO.getDocumentProperty();
//                boolean isRecordType = StringUtils.isEmptyStr(documentType) || !fileInfoDTO.getRecordType().equals(documentType);
//                if (isRecordType || StringUtils.isEmptyStr(documentProperty)) {
//                    continue;
//                }
//
//                Map<String, String> propertyMap = StringUtils.parseMessageVariables(documentProperty, FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
//                String taskCodeValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_TASKCODE);
//                String applyTimesValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_APPLYTIMES);
//                if (fileInfoDTO.getTaskCodeList().contains(taskCodeValue) && applyTimesStr.equals(applyTimesValue)) {
//                    return true;
//                }
//
//            }
//        }
//
//        return false;
//    }

    @Override
    public String getSeq(){
        return fileInfoMapper.getSeq();
    }

//    @Override
//    public FileInfoVO getZeroBindRecordList(FileInfoDTO fileInfoDTO, List<String> fileIdList) {
//
//        FileInfoVO fileInfoVO = new FileInfoVO();
//
//        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_RECORD);
//        String documentGroupId = this.getDocumentGroupIdByDb(fileInfoDTO);
//        if (StringUtils.isEmptyStr(documentGroupId)) {
//            return fileInfoVO;
//        }
//
//        fileInfoVO.setCaseTimes(fileInfoDTO.getCaseTimes());
//        fileInfoVO.setDocumentGroupId(documentGroupId);
//        List<FileInfoDTO> fileInfoList = new ArrayList<FileInfoDTO>();
//
//
//        FileDocumentGroupDTO documentGroupDTO = fileUploadSAOMock.getDocumentGroupInfo(FileUploadConstants.IMAGE_SERVERID, FileUploadConstants.SYSTEMID, documentGroupId, FileUploadConstants.INVALIDATION_FLAG);
//        List<String> taskCodeList = fileInfoDTO.getTaskCodeList();
//        String applyTimesStr = String.valueOf(fileInfoDTO.getApplyTimes());
//
//        if (documentGroupDTO != null && documentGroupDTO.getDocuments() != null) {
//            LogUtil.audit("获取已上传的录音列表 文件组ID为：" + documentGroupDTO.getDocumentGroupId());
//            List<FileDocumentDTO> documentDTOList = documentGroupDTO.getDocuments();
//
//
//            for (FileDocumentDTO documentDTO : documentDTOList) {
//                String recordType = documentDTO.getDocumentType();
//                String documentProperty = documentDTO.getDocumentProperty();
//                String uploadPath = documentDTO.getUploadPath();
//                String storageType = documentDTO.getStorageType();
//                String bucketName = documentDTO.getBucketName();
//
//                if (StringUtils.isNotEmpty(recordType) && StringUtils.isNotEmpty(documentProperty)) {
//                    if (FileUploadConstants.RECORD_TYPE_ZERO_APPLY.equals(recordType)) {
//                        if (ListUtils.isNotEmpty(fileIdList) && fileIdList.contains(uploadPath)) {
//                            FileInfoDTO fileInfo = new FileInfoDTO();
//                            fileInfo.setFileName(documentDTO.getOriginName());
//                            fileInfo.setFileUrl(documentDTO.getUrl());
//                            fileInfo.setFileId(documentDTO.getUploadPath());
//                            fileInfo.setStorageType(storageType);
//                            fileInfo.setBucketName(bucketName);
//                            fileInfoList.add(fileInfo);
//                        } else {
//                            Map<String, String> propertyMap = StringUtils.parseMessageVariables(documentProperty, FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
//                            String taskCodeValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_TASKCODE);
//                            String applyTimesValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_APPLYTIMES);
//
//
//                            if (taskCodeList.contains(taskCodeValue) && applyTimesStr.equals(applyTimesValue)) {
//                                FileInfoDTO fileInfo = new FileInfoDTO();
//                                fileInfo.setFileName(documentDTO.getOriginName());
//                                fileInfo.setFileId(documentDTO.getUploadPath());
//                                fileInfo.setFileUrl(documentDTO.getUrl());
//                                fileInfo.setStorageType(storageType);
//                                fileInfo.setBucketName(bucketName);
//                                fileInfoList.add(fileInfo);
//                            }
//                        }
//                    }
//                }
//            }
//
//            fileInfoVO.setFileInfoList(fileInfoList);
//        }
//
//        return fileInfoVO;
//    }

    /**
     * partition by collection_code order by display_no
     *
     * @return
     * @throws GlobalBusinessException
     */
//    private Map<String, String> getRecordTypeMap() throws GlobalBusinessException {
//        Map<String, String> recordTypeMap = new HashMap<>();
//        String[] collectionCode = {FileUploadConstants.RECORD_TYPE_COLLECTION_CODE};
//        List<CommonParameterTinyDTO> recordTypeList = commonParameterService.getCommonParameterList(collectionCode);
//        for (CommonParameterTinyDTO commonParameter : recordTypeList) {
//            recordTypeMap.put(commonParameter.getValueCode(), commonParameter.getValueChineseName());
//        }
//        return recordTypeMap;
//    }

    /**
     * FileDocumentDTO 轉換 FileInfoDTO
     * @param documentDTO
     * @return
     */
    private FileInfoDTO setRecordInfo(FileDocumentDTO documentDTO) {
        FileInfoDTO fileInfo = new FileInfoDTO();
        fileInfo.setFileName(documentDTO.getOriginName());
        fileInfo.setFileUrl(documentDTO.getUrl());
        fileInfo.setUploadPersonnel(documentDTO.getUploadPersonnel());
        fileInfo.setDocumentGroupItemsId(documentDTO.getDocumentGroupItemsId());
        fileInfo.setFileId(documentDTO.getUploadPath());
        fileInfo.setStorageType(documentDTO.getStorageType());
        fileInfo.setBucketName(documentDTO.getBucketName());
        return fileInfo;
    }


    private void sortList(List<FileInfoDTO> fileInfoList) throws GlobalBusinessException {
        Collections.sort(fileInfoList, new Comparator<FileInfoDTO>() {
            @Override
            public int compare(FileInfoDTO o1, FileInfoDTO o2) {
                SimpleDateFormat format = new SimpleDateFormat(DateUtils.FULL_DATE_STR);
                try {
                    Date dt1 = format.parse(o1.getUploadDate());
                    Date dt2 = format.parse(o2.getUploadDate());
                    // return dt1.getTime()- dt2.getTime(); .....
                    if (dt1.getTime() < dt2.getTime()) {
                        return 1;
                    } else if (dt1.getTime() > dt2.getTime()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (ParseException e) {
                    LogUtil.error("按时间倒序排序时间转换异常", e);
                }
                return 0;
            }
        });
    }


    private void addExemptFlag(List<FileInfoVO> fileInfoVOList, String flowType, Set<String> exemptSet,
                               Map<String, DocumentTypeDTO> smallMap) throws GlobalBusinessException {
        List<DocumentTypeVO> documentTypeVOList = new ArrayList<>();
        List documentList = new ArrayList<>();
        for (String s : exemptSet) {
            DocumentTypeVO documentTypeVO = new DocumentTypeVO();
            documentTypeVO.setDocumentList(documentList);
            documentTypeVO.setSmallCode(s);
            documentTypeVO.setSmallOrder(smallMap.get(s).getSmallOrder());
            String smallName = smallMap.get(s).getSmallName() + "(免)";
            documentTypeVO.setExemptFlag("1");
            documentTypeVO.setSmallName(smallName);
            documentTypeVO.setDoucmentCount(0);
            documentTypeVOList.add(documentTypeVO);
        }

        FileInfoVO fileInfoVO = new FileInfoVO();
        fileInfoVO.setDocumentGroupId("");
        fileInfoVO.setFlowType(flowType);
        fileInfoVO.setFlowName(FileUploadConstants.flowType.get(flowType));
        this.orderDocument(documentTypeVOList);
        fileInfoVO.setSmallTypeList(documentTypeVOList);
        fileInfoVOList.add(fileInfoVO);
    }


    private void modifyExemptFlag(List<FileInfoVO> fileInfoVOList, String flowType, Set<String> exemptSet, Map<String,
            DocumentTypeDTO> smallMap, List<DocumentTypeVO> documentTypeVOList, String groupId, Map<String, FileInfoVO> fileInfoVOMap) throws GlobalBusinessException {
        Map<String, DocumentTypeVO> documentVOMap = new HashMap<>();
        for (DocumentTypeVO d : documentTypeVOList) {
            documentVOMap.put(d.getSmallCode(), d);
        }

        List<DocumentTypeVO> newDocumentTypeVOList = new ArrayList<>();
        List documentList = new ArrayList<>();
        for (String s : exemptSet) {
            if (documentVOMap.containsKey(s)) {
                DocumentTypeVO documentTypeVO = documentVOMap.get(s);
                String smallName = smallMap.get(s).getSmallName() + "(免)";
                documentTypeVO.setExemptFlag("1");
                documentTypeVO.setSmallName(smallName);
                if (documentTypeVO.getDoucmentCount() == 0) {
                    documentTypeVO.setDocumentList(documentList);
                }
                newDocumentTypeVOList.add(documentTypeVO);
                documentVOMap.remove(s);
            } else {
                DocumentTypeVO documentTypeVO = new DocumentTypeVO();
                documentTypeVO.setDocumentList(documentList);
                documentTypeVO.setSmallCode(s);
                documentTypeVO.setSmallOrder(smallMap.get(s).getSmallOrder());
                String smallName = smallMap.get(s).getSmallName() + "(免)";
                documentTypeVO.setExemptFlag("1");
                documentTypeVO.setSmallName(smallName);
                documentTypeVO.setDoucmentCount(0);
                newDocumentTypeVOList.add(documentTypeVO);
            }
        }

        if (documentVOMap.size() > 0) {
            for (Map.Entry<String, DocumentTypeVO> entry : documentVOMap.entrySet()) {
                newDocumentTypeVOList.add(entry.getValue());
            }
        }

        FileInfoVO fileInfoVO = new FileInfoVO();
        fileInfoVO.setDocumentGroupId(groupId);
        fileInfoVO.setFlowType(flowType);
        fileInfoVO.setFlowName(FileUploadConstants.flowType.get(flowType));
        this.orderDocument(newDocumentTypeVOList);
        fileInfoVO.setSmallTypeList(newDocumentTypeVOList);
        fileInfoVOMap.put(flowType, fileInfoVO);
        fileInfoVOList = fileInfoVOMap.values().stream().collect(Collectors.toList());
        Collections.sort(fileInfoVOList, new Comparator<FileInfoVO>() {
            @Override
            public int compare(FileInfoVO o1, FileInfoVO o2) {
                int comparaValue = StringUtils.strToInt(o1.getFlowType()) - StringUtils.strToInt(o2.getFlowType());
                if (comparaValue < 0) {
                    return -1;
                } else if (comparaValue > 0) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
    }

    public void orderDocument(List<DocumentTypeVO> documentTypeVOList) {
        if (ListUtils.isEmptyList(documentTypeVOList)) {
            return;
        }
        Collections.sort(documentTypeVOList, new Comparator<DocumentTypeVO>() {
            @Override
            public int compare(DocumentTypeVO o1, DocumentTypeVO o2) {
                int o1Order = Integer.parseInt(o1.getSmallOrder());
                int o2Order = Integer.parseInt(o2.getSmallOrder());
                if (o1Order < o2Order) {
                    return -1;
                } else if (o1Order > o2Order) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
    }


    /**
     * 调用 调用通天藤获取文档组 获取数据
     *
     * @param documentGroupId
     * @param flag
     * @return
     * @throws GlobalBusinessException
     */
    private List<FileDocumentGroupDTO> getDocumentByGroupIdArray(List<FileInfoDTO> documentGroupId, String flag) throws GlobalBusinessException {
        List<FileDocumentGroupDTO> documentGroupDTOList = new ArrayList<>();
        if (ListUtils.isEmptyList(documentGroupId)) {
            return documentGroupDTOList;
        }
        Map<String, String> flowTpyeMap = new HashMap<>();

        String groupId = documentGroupId.get(0).getDocumentGroupId();
        List<FileDocumentDTO> list = fileInfoMapper.queryDocumentByGroupId(groupId);
        if (iobs){
            Calendar nowTime = Calendar.getInstance();
            nowTime.add(Calendar.MINUTE,4);//cos文件url有效期5分钟，设置4分钟为扣除调用cos接口时间
            list = list.stream().peek(doc->{
                String iobsFileDownloadUrl = iobsFileUploadService.getPerpetualDownloadUrl(doc.getUrl(),doc.getDocumentName());
                doc.setUrl(iobsFileDownloadUrl);
                doc.setExpireTime(nowTime.getTime());
            }).collect(Collectors.toList());
        }
        FileDocumentGroupDTO group = new FileDocumentGroupDTO();
        group.setDocumentGroupId(groupId);
        group.setDocuments(list);
        documentGroupDTOList.add(group);
        if (ListUtils.isNotEmpty(documentGroupDTOList)) {
            for (FileDocumentGroupDTO groupDTO : documentGroupDTOList) {
                if (StringUtils.isEmptyStr(groupDTO.getDocumentGroupType())) {
                    groupDTO.setDocumentGroupType(flowTpyeMap.getOrDefault(groupDTO.getDocumentGroupId(), "01"));
                }
            }
        }
        return documentGroupDTOList;
    }


    @SuppressWarnings("unchecked")
    private List<FileDocumentDTO> getDocumentByDocumentGroupArray(List<FileDocumentGroupDTO> documentGroupDTOList) throws GlobalBusinessException {
        List<FileDocumentDTO> allDocumentList = new ArrayList<FileDocumentDTO>();
        if (ListUtils.isEmptyList(documentGroupDTOList)) {
            return allDocumentList;
        }


        for (FileDocumentGroupDTO documentGroupDTO : documentGroupDTOList) {
            List<FileDocumentDTO> documentDTOList = documentGroupDTO.getDocuments();
            if (ListUtils.isEmptyList(documentDTOList)) {

                continue;
            }

            allDocumentList.addAll(documentDTOList);
        }
        return allDocumentList;
    }


    private List<DocumentTypeVO> getRemovedDocumentTypeVOList(List<FileDocumentGroupDTO> documentGroupDTOList) throws GlobalBusinessException {

        if (ListUtils.isEmptyList(documentGroupDTOList)) {
            return new ArrayList<DocumentTypeVO>();
        }


        List<FileDocumentDTO> allDocumentList = this.getDocumentByDocumentGroupArray(documentGroupDTOList);

        if (ListUtils.isEmptyList(allDocumentList)) {

            return new ArrayList<DocumentTypeVO>();
        }


        List<DocumentTypeDTO> smallTypeList = documentTypeService.getDocumentSmallTypeList();
        if (ListUtils.isEmptyList(smallTypeList)) {
            LogUtil.info(FileUploadConstants.DOCUMENT_SMALLTYPE_IS_NULL);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_DOCUMENT_EMPTY_TYPE);
        }

        return this.getSmallTypeyDocument(smallTypeList, allDocumentList, FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N, null, null);
    }


    private List<DocumentTypeVO> getSmallTypeyDocument(List<DocumentTypeDTO> smallTypeList, List<FileDocumentDTO> allDocumentList, String flag, String userId, FileInfoDTO fileInfo) throws GlobalBusinessException {
        List<FileInfoDTO> documentList = null;
        List<FileInfoDTO> disableDocumentList = null;
        DocumentTypeVO documentTypeVO = null;
        FileInfoDTO fileInfoDTO = null;
        List<DocumentTypeVO> documentTypeVOList = new ArrayList<DocumentTypeVO>();
        List<FileInfoDTO> ahsDocument = new ArrayList<>();
        Map<String, DocumentTypeDTO> smallMap = smallTypeList.stream().collect(Collectors.toMap(DocumentTypeDTO::getSmallCode, e -> e, (k1, k2) -> k1));
        for (DocumentTypeDTO documentType : smallTypeList) {
            String smallCode = documentType.getSmallCode();
            String smallName = documentType.getSmallName();
            documentList = new ArrayList<FileInfoDTO>();
            disableDocumentList = new ArrayList<FileInfoDTO>();
            for (FileDocumentDTO documentDTO : allDocumentList) {

                String docSmallCode = documentDTO.getDocumentType();
                if (docSmallCode != null && docSmallCode.equals(smallCode)) {
                    String documentStatus = documentDTO.getDocumentStatus();
                    String url = documentDTO.getUrl();

                    String uploadDate = null;
                    try {
                        uploadDate = DateUtils.parseToFormatString(documentDTO.getCreatedDate(), FileUploadConstants.FORMAL_TIME_FORMAT);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    if (StringUtils.isNotEmpty(uploadDate) && uploadDate.indexOf(".") > 0) {
                        uploadDate = uploadDate.substring(0, uploadDate.indexOf("."));
                    }
                    if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(flag) &&
                            FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(documentStatus)) {
                        documentList.add(this.transferDocument(documentDTO, userId, flag));
                    } else if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N.equals(documentStatus)) {

                        fileInfoDTO = new FileInfoDTO();
                        fileInfoDTO.setSupplement(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
                        fileInfoDTO.setDocumentGroupItemsId(documentDTO.getDocumentGroupItemsId());

                        fileInfoDTO.setFileUrl(url);
                        fileInfoDTO.setUploadDate(uploadDate);
                        fileInfoDTO.setUploadPersonnel(documentDTO.getUploadPersonnel());
                        fileInfoDTO.setScanUpload(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
                        fileInfoDTO.setShare(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
                        if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(documentDTO.getRemark())) {
                            fileInfoDTO.setScanUpload(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y);
                        }
                        if (this.isShareDoc(documentDTO.getDocumentProperty())) {
                            fileInfoDTO.setShare(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y);
                        }
                        fileInfoDTO.setFileId(documentDTO.getUploadPath());
                        fileInfoDTO.setBucketName(documentDTO.getBucketName());
                        fileInfoDTO.setFileFormat(documentDTO.getDocumentFormat());
                        fileInfoDTO.setFileName(documentDTO.getOriginName());
                        fileInfoDTO.setExpireTime(documentDTO.getExpireTime());
                        disableDocumentList.add(fileInfoDTO);
                    }
                } else if (FileUploadConstants.SMALL_CODE_OTHER.equals(smallCode)) {

                    if (StringUtils.isEmptyStr(docSmallCode) || !smallMap.containsKey(docSmallCode)) {
                        String documentStatus = documentDTO.getDocumentStatus();
                        String url = documentDTO.getUrl();

                        String uploadDate = null;
                        try {
                            uploadDate = DateUtils.parseToFormatString(documentDTO.getCreatedDate(), FileUploadConstants.FORMAL_TIME_FORMAT);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        if (StringUtils.isNotEmpty(uploadDate) && uploadDate.indexOf(".") > 0) {
                            uploadDate = uploadDate.substring(0, uploadDate.indexOf("."));
                        }
                        if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(flag) &&
                                FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(documentStatus)) {
                            ahsDocument.add(this.transferDocument(documentDTO, userId, flag));
                        } else if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N.equals(documentStatus)) {
                            fileInfoDTO = new FileInfoDTO();
                            fileInfoDTO.setSupplement(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
                            fileInfoDTO.setDocumentGroupItemsId(documentDTO.getDocumentGroupItemsId());

                            fileInfoDTO.setFileUrl(url);
                            fileInfoDTO.setUploadDate(uploadDate);
                            fileInfoDTO.setUploadPersonnel(documentDTO.getUploadPersonnel());
                            fileInfoDTO.setScanUpload(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
                            fileInfoDTO.setShare(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
                            if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(documentDTO.getRemark())) {
                                fileInfoDTO.setScanUpload(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y);
                            }
                            if (this.isShareDoc(documentDTO.getDocumentProperty())) {
                                fileInfoDTO.setShare(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y);
                            }
                            fileInfoDTO.setFileId(documentDTO.getUploadPath());
                            fileInfoDTO.setBucketName(documentDTO.getBucketName());
                            fileInfoDTO.setFileFormat(documentDTO.getDocumentFormat());
                            fileInfoDTO.setFileName(documentDTO.getOriginName());
                            fileInfoDTO.setExpireTime(documentDTO.getExpireTime());
                            disableDocumentList.add(fileInfoDTO);
                        }
                    }
                }
            }
            documentTypeVO = new DocumentTypeVO();
            documentTypeVO.setSmallCode(smallCode);
            documentTypeVO.setSmallOrder(documentType.getSmallOrder());
            documentTypeVO.setSmallName(smallName);
            if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(flag)) {
                if (ListUtils.isNotEmpty(documentList)) {
                    Collections.sort(documentList, new DocComparator());
                }
                documentTypeVO.setDocumentList(documentList);
                int count = ListUtils.isNotEmpty(documentList) ? documentList.size() : FileUploadConstants.DOCUMENT_COUNT_ZERO;
                documentTypeVO.setDoucmentCount(count);
            } else {
                documentTypeVO.setDocumentList(disableDocumentList);
                int count = ListUtils.isNotEmpty(disableDocumentList) ? disableDocumentList.size() : FileUploadConstants.DOCUMENT_COUNT_ZERO;
                documentTypeVO.setDoucmentCount(count);
            }
            documentTypeVOList.add(documentTypeVO);
        }

        if (ListUtils.isNotEmpty(ahsDocument)) {
            for (DocumentTypeVO dv : documentTypeVOList) {
                if (FileUploadConstants.SMALL_CODE_OTHER.equals(dv.getSmallCode())) {
                    LogUtil.audit("未知的案件类型={}", JSONObject.toJSONString(ahsDocument));
                    List<FileInfoDTO> otherDoc = dv.getDocumentList();
                    otherDoc = otherDoc == null ? new ArrayList<>() : otherDoc;
                    otherDoc.addAll(ahsDocument);
                    dv.setDocumentList(otherDoc);
                    dv.setDoucmentCount(otherDoc.size());
                }
            }
        }

        return documentTypeVOList;
    }


    @SuppressWarnings("unchecked")
    private List<DocumentTypeVO> getDocumentTypeVOList(FileDocumentGroupDTO documentGroupDTO, String userId, FileInfoDTO fileInfoDTO) throws GlobalBusinessException {

        if (null == documentGroupDTO || ListUtils.isEmptyList(documentGroupDTO.getDocuments())) {
            return new ArrayList<DocumentTypeVO>();
        }
        List<FileDocumentDTO> documentDTOList = documentGroupDTO.getDocuments();


        List<DocumentTypeDTO> smallTypeList = documentTypeService.getDocumentSmallTypeList();
        if (ListUtils.isEmptyList(smallTypeList)) {
            LogUtil.info(FileUploadConstants.DOCUMENT_SMALLTYPE_IS_NULL);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_DOCUMENT_EMPTY_TYPE);
        }

        return this.getSmallTypeyDocument(smallTypeList, documentDTOList, FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y, userId, fileInfoDTO);
    }


    private boolean isUserView(FileDocumentDTO documentDTO, String userId) throws GlobalBusinessException {
        String documentProperty = documentDTO.getDocumentProperty();
        if (StringUtils.isEmptyStr(documentProperty)) {
            return true;
        }


        Map<String, String> propertyMap = StringUtils.parseMessageVariables(documentProperty, FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
        String supplementValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_SUPPLEMENT);


        if (StringUtils.isNotEmpty(supplementValue) && FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(supplementValue)) {
            FileViewRecordDTO fileViewRecordDTO = new FileViewRecordDTO();
            fileViewRecordDTO.setDocumentGroupItemsId(documentDTO.getDocumentGroupItemsId());
            fileViewRecordDTO.setUserId(userId);

            int viewCount = fileViewRecordService.getFileViewRecord(fileViewRecordDTO);

            if (viewCount < 1) {
                return false;
            }
        }

        return true;
    }


    private boolean isShareDoc(String property) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(property)) {
            return false;
        }


        Map<String, String> propertyMap = StringUtils.parseMessageVariables(property, FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
        String shareValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_SHARE);

        if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(shareValue)) {
            return true;
        }
        return false;
    }

    private FileInfoDTO transferDocument(FileDocumentDTO documentDTO, String userId, String flag) throws GlobalBusinessException {
        String documentStatus = documentDTO.getDocumentStatus();
        boolean isUserView = true;
        String url = documentDTO.getUrl();

        String uploadDate = null;
        try {
            uploadDate = DateUtils.parseToFormatString(documentDTO.getCreatedDate(), FileUploadConstants.FORMAL_TIME_FORMAT);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotEmpty(uploadDate) && uploadDate.indexOf(".") > 0) {
            uploadDate = uploadDate.substring(0, uploadDate.indexOf("."));
        }
        if (uploadDate == null) {
            uploadDate = FileUploadConstants.EMPTY_STRING;
        }
        String uploadPersonner = documentDTO.getUploadPersonnel();
        if (uploadPersonner == null) {
            uploadPersonner = FileUploadConstants.EMPTY_STRING;
        }

        FileInfoDTO fileInfoDTO = null;
        if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(flag) &&
                FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(documentStatus)) {
            fileInfoDTO = new FileInfoDTO();

            isUserView = isUserView(documentDTO, userId);
            if (!isUserView) {
                fileInfoDTO.setSupplement(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y);
            } else {
                fileInfoDTO.setSupplement(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
            }

            fileInfoDTO.setDocumentGroupItemsId(documentDTO.getDocumentGroupItemsId());

            fileInfoDTO.setFileUrl(url);
            fileInfoDTO.setUploadDate(uploadDate);
            fileInfoDTO.setUploadPersonnel(uploadPersonner);
            fileInfoDTO.setFileId(documentDTO.getUploadPath());
            fileInfoDTO.setExpireTime(documentDTO.getExpireTime());

            if (FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(documentDTO.getRemark())) {
                fileInfoDTO.setScanUpload(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y);

            } else {
                fileInfoDTO.setScanUpload(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
            }
            if (this.isShareDoc(documentDTO.getDocumentProperty())) {
                fileInfoDTO.setShare(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y);
            } else {
                fileInfoDTO.setShare(FileUploadConstants.DOCUMENT_UPLOAD_FLAG_N);
            }

            Map<String, String> propertyMap = StringUtils.parseMessageVariables(documentDTO.getDocumentProperty(), FileUploadConstants.DOCUMENT_PROPERTY_SEPARATOR);
            String batchNoValue = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_BATCHNO);
            if (StringUtils.isNotEmpty(batchNoValue)) {
                fileInfoDTO.setBatchNo(batchNoValue);
            }

            String clickOrd = propertyMap.get(FileUploadConstants.DOCUMENT_PROPERTY_CLICKORD);
            if (StringUtils.isNotEmpty(clickOrd)) {
                fileInfoDTO.setClickOrder(Integer.parseInt(clickOrd));
            } else {
                fileInfoDTO.setClickOrder(0);
            }
            fileInfoDTO.setAhcsLv3Code(documentDTO.getRemark());
            fileInfoDTO.setBucketName(documentDTO.getBucketName());
            fileInfoDTO.setFileFormat(documentDTO.getDocumentFormat());
            fileInfoDTO.setFileName(documentDTO.getOriginName());

        }
        return fileInfoDTO;
    }




    @Override
    public List<DocSmallTypeVO> getMissingDocNotExempt(String reportNo, Integer caseTimes, String alias, String userId) throws GlobalBusinessException {

        LogUtil.audit("#检查案件缺少单证#入参#reportNo=" + reportNo + ",caseTime=" + caseTimes + ",alias=" + alias + ",userId=" + userId);
        String caseType = wholeCaseService.getCaseTypeByReportNo(reportNo, caseTimes);
        if (ReportConstant.CASE_TYPE_FAST.equals(caseType)) {
            return null;
        }

        List<DocSmallTypeVO> requiredDocTypeList = new ArrayList<>();
        LogUtil.audit("#检查案件缺少单证#必传单证#reportNo=" + reportNo + ",caseTime=" + caseTimes + ",requiredDocTypeList=" + JSONObject.toJSONString(requiredDocTypeList));
        if (ListUtils.isEmptyList(requiredDocTypeList)) {
            return null;
        }

        List<DocumentTypeDTO> smallTypeList = documentTypeService.getDocumentSmallTypeList();
        if (ListUtils.isEmptyList(smallTypeList)) {
            LogUtil.info(FileUploadConstants.DOCUMENT_SMALLTYPE_IS_NULL);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_DOCUMENT_EMPTY_TYPE);
        }
        Map<String, DocumentTypeDTO> smallMap = new HashMap<>();
        for (DocumentTypeDTO d : smallTypeList) {
            smallMap.put(d.getSmallCode(), d);
        }

        List<SelfDocTypeDTO> selfSmallTypeList = documentTypeService.getSelfTypeByScene(FileUploadConstants.DOCUMENT_SCENE_ONLINE);
        if (ListUtils.isEmptyList(selfSmallTypeList)) {
            LogUtil.info(FileUploadConstants.DOCUMENT_SMALLTYPE_IS_NULL);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_DOCUMENT_EMPTY_TYPE);
        }
        Map<String, SelfDocTypeDTO> selfSmallMap = new HashMap<>();
        for (SelfDocTypeDTO s : selfSmallTypeList) {
            selfSmallMap.put(s.getSelfSmallCode(), s);
        }

        List<FileDocumentDTO> documentList = this.getDocumentListByReportNo(reportNo, null);
        List<DocSmallTypeVO> missingDocTypes = new ArrayList<>();
        if (ListUtils.isEmptyList(documentList)) {
            Set<String> smallSet = new HashSet<>();
            for (DocSmallTypeVO d : requiredDocTypeList) {
                String smallCode = d.getSmallCode();
                if (smallCode.length() == 6 && smallSet.add(smallCode)) {
                    DocumentTypeDTO smallTypeDTO = smallMap.get(smallCode);
                    d.setSmallCode(smallTypeDTO.getSmallCode());
                    d.setSmallName(smallTypeDTO.getSmallName());
                    missingDocTypes.add(d);
                } else {
                    String small = selfSmallMap.get(smallCode).getSmallTypeCode();
                    if (smallSet.add(small)) {
                        DocumentTypeDTO smallTypeDTO = smallMap.get(small);
                        d.setSmallCode(smallTypeDTO.getSmallCode());
                        d.setSmallName(smallTypeDTO.getSmallName());
                        missingDocTypes.add(d);
                    }
                }
            }
        } else {
            Set<String> uploadSmallCode = new HashSet<>();
            for (FileDocumentDTO d : documentList) {
                uploadSmallCode.add(d.getDocumentType());
            }

            Set<String> smallCodSet = new HashSet<>();
            LogUtil.audit("#检查案件缺少单证#已上传单证#reportNo=" + reportNo + ",caseTime=" + caseTimes +
                    ",uploadSmallCode=" + JSONObject.toJSONString(uploadSmallCode));
            for (DocSmallTypeVO d : requiredDocTypeList) {
                String smallCode = d.getSmallCode();
                if (smallCode.startsWith("s")) {
                    smallCode = selfSmallMap.get(smallCode).getSmallTypeCode();
                    DocumentTypeDTO smallTypeDTO = smallMap.get(smallCode);
                    d.setSmallCode(smallTypeDTO.getSmallCode());
                    d.setSmallName(smallTypeDTO.getSmallName());
                }

                if (!uploadSmallCode.contains(smallCode) && smallCodSet.add(smallCode)) {
                    d.setSmallName(smallMap.get(smallCode).getSmallName());
                    missingDocTypes.add(d);
                }
            }
        }
        if (ListUtils.isEmptyList(missingDocTypes)) {
            return missingDocTypes;
        }
        LogUtil.audit("#检查案件缺少单证#缺少单证#reportNo=" + reportNo + ",caseTime=" + caseTimes + ",requiredDocTypes=" + JSONObject.toJSONString(missingDocTypes));
        return missingDocTypes;
    }

    private List<DocSmallTypeVO> filtRequiredDocTypeBySilkBag(List<Map<String, String>> requiredDocTypes, List<String> exemptList) throws GlobalBusinessException {
        LogUtil.audit("#获取必传单证#过滤锦囊#入参#requiredDocList=" + JSONObject.toJSONString(requiredDocTypes) + ",exemptList=" + JSONObject.toJSONString(exemptList));
        if (ListUtils.isEmptyList(requiredDocTypes)) {
            return null;
        }
        List<DocSmallTypeVO> requiredDocList = new ArrayList<>();
        if (ListUtils.isEmptyList(exemptList)) {
            for (Map<String, String> m : requiredDocTypes) {
                DocSmallTypeVO docSmallType = new DocSmallTypeVO();
                docSmallType.setSmallCode(m.get(FileUploadConstants.DOC_TYPE_CODE));
                docSmallType.setSmallName(m.get(FileUploadConstants.DOC_TYPE_NAME));
                requiredDocList.add(docSmallType);
            }
        } else {
            for (Map<String, String> m : requiredDocTypes) {
                String smallCode = m.get(FileUploadConstants.DOC_TYPE_CODE);
                if (exemptList.contains(smallCode)) {
                    continue;
                }
                DocSmallTypeVO docSmallType = new DocSmallTypeVO();
                docSmallType.setSmallCode(smallCode);
                docSmallType.setSmallName(m.get(FileUploadConstants.DOC_TYPE_NAME));
                requiredDocList.add(docSmallType);
            }
        }
        LogUtil.audit("#获取必传单证#过滤锦囊#出参#requiredDocList=" + JSONObject.toJSONString(requiredDocList));
        return requiredDocList;
    }

    @Override
    public List<FileDocumentDTO> getDocumentListByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(reportNo)) {
            LogUtil.audit("文件参数reportNo(报案号)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "报案号");
        }
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setReportNo(reportNo);
        fileInfoDTO.setCaseTimes(caseTimes);
        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
        List<FileInfoDTO> groupIdList = this.getFileGroupIdForDocument(fileInfoDTO);
        List<FileDocumentGroupDTO> documentGroupDTOList = null;
        if (ListUtils.isNotEmpty(groupIdList)) {
            documentGroupDTOList = getDocumentByGroupIdArray(groupIdList, FileUploadConstants.INVALIDATION_FLAG);
        }
        List<FileDocumentDTO> documentList = new ArrayList<>();
        for (FileDocumentGroupDTO documentGroupDTO : documentGroupDTOList) {
            List<FileDocumentDTO> documents = documentGroupDTO.getDocuments();
            if (ListUtils.isEmptyList(documents)) {
                continue;
            }
            for (FileDocumentDTO document : documents) {
                String documentClass = document.getDocumentClass();
                if (StringUtils.isNotEmpty(documentClass)) {
                    if ("009".equals(documentClass)) {
                        String documentType = document.getDocumentType();
                        if (StringUtils.isNotEmpty(documentType)) {
                            if ("009002".equals(documentType)) {
                                String remark = document.getRemark();
                                if (StringUtils.isEmptyStr(remark)) {
                                    document.setRemark("009002001");
                                }
                            }
                        }
                    }
                }
            }
            documentList.addAll(documents);
        }
        return documentList;


    }

    private void validGetDocumentParams(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {

        if (StringUtils.isEmptyStr(fileInfoDTO.getReportNo())) {
            LogUtil.audit("文件参数reportNo(报案号)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "报案号");
        } else if (fileInfoDTO.getCaseTimes() == null) {
            LogUtil.audit("文件参数caseTimes(赔付次数)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "赔付次数");
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getSmallCode())) {
            LogUtil.audit("文件参数smallCode(细类代码)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "细类代码");
        }
    }

    @Override
    public boolean removeFileList(FileInfoVO fileInfoVO, String userId) throws GlobalBusinessException {
        boolean resultFlag = documentDao.disableDocumentByUm(fileInfoVO.getDocumentGroupItemsIdArr(), userId);
        if (!resultFlag) {
            LogUtil.audit("调用影像sdk接口(disableDocumentByUm)一次作废多个附件失败！ resultFlag=" + resultFlag);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_FILE_UPLOAD_DELETE_FILE_FAIL);
        }
        return resultFlag;
    }

    @Override
    public boolean modifyDocument(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
//        List<UpdateFileDocumentVO> documentDTOList = new ArrayList<>();
        String[] documentGroupItemsIdArr = fileInfoDTO.getDocumentGroupItemsIdArr();
        String smallCode = fileInfoDTO.getSmallCode();
        boolean resultFlag = false;
        if (documentGroupItemsIdArr != null && documentGroupItemsIdArr.length > 0) {
//            UpdateFileDocumentVO documentDTO = null;
//            for (int i = 0; i < documentGroupItemsIdArr.length; i++) {
//                documentDTO = new UpdateFileDocumentVO();
//                documentDTO.setDocumentGroupItemsId(documentGroupItemsIdArr[i]);
//                documentDTO.setDocumentType(smallCode);
//                documentDTOList.add(documentDTO);
//            }
            resultFlag = documentDao.updateUploadedDocumentInfos(smallCode,documentGroupItemsIdArr);
            if (!resultFlag) {
                LogUtil.info("(updateUploadedDocumentInfos)更改单证类别失败！ resultFlag=" + resultFlag);
                throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_FILE_MODIFY_DOCUMENTS);
            }
        }

        return resultFlag;
    }

    @Override
    public FileRealDownLoadAddressInfoDTO getDocumentsRealAddress(String fileId) {
        String fileDownloadUrl = iobsFileUploadService.getPerpetualDownloadUrl(fileId, null);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.MINUTE,4);
        StringBuilder logRecord = new StringBuilder();
        logRecord.append("请求cos获取单证真实下载地址，原地址：")
                .append(fileId)
                .append(", 真实地址:")
                .append(fileDownloadUrl)
                .append(", 过期时间")
                .append(sdf.format(nowTime.getTime()));
        LogUtil.info(logRecord.toString());
        return FileRealDownLoadAddressInfoDTO.builder()
                .fileRealAddress(fileDownloadUrl)
                .expireTime(nowTime.getTime()).build();
    }

    @Override
    public List<FileDocumentDTO> queryDocumentInfo(QueryDocumentInfoDTO queryDocumentInfoDto) {

        List<String> documentGroupIdList = fileInfoMapper.getDocumentGroupIdByReportNoAndCaseTimes(queryDocumentInfoDto.getReportNo(), queryDocumentInfoDto.getCaseTimes());
        List<FileDocumentDTO> result = new ArrayList<FileDocumentDTO>();
        if(ListUtils.isNotEmpty(documentGroupIdList)) {
            List<FileDocumentDTO> list = fileInfoMapper.queryDocumentByGroupId(documentGroupIdList.get(0));
            result.addAll(list);
        }
        return result;
    }

    @Override
    public List<DocumentListDTO> queryDocumenList(String reportNo, Integer caseTimes) {
        List<DocumentListDTO> documentList = Lists.newArrayList();
        List<String> documentGroupIdList = fileInfoMapper.getDocumentGroupIdByReportNoAndCaseTimes(reportNo, caseTimes);
        if (CollectionUtils.isNotEmpty(documentGroupIdList) && StringUtils.isNotEmpty(documentGroupIdList.get(0))) {
            List<FileDocumentDTO> list = fileInfoMapper.queryDocumentByGroupId(documentGroupIdList.get(0));
            for (FileDocumentDTO fileDocument : list) {
                DocumentListDTO document = new DocumentListDTO();
                document.setFileName(fileDocument.getDocumentName());
                document.setFileSize(fileDocument.getDocumentSize() == null ? null : fileDocument.getDocumentSize().toString());
                document.setFileType(fileDocument.getDocumentFormat());
                document.setFileUrl(fileDocument.getUrl());
                document.setSmallTypeCode(fileDocument.getDocumentType());
                documentList.add(document);
            }
        }
        return documentList;
    }
    @Override
    public List<FileDocumentDTO> queryDocumentList(String reportNo) {
        List<FileDocumentDTO> list = fileInfoMapper.queryDocumentByGroupId(reportNo);
        if (iobs){
            Calendar nowTime = Calendar.getInstance();
            nowTime.add(Calendar.MINUTE,4);//cos文件url有效期5分钟，设置4分钟为扣除调用cos接口时间
            list = list.stream().peek(doc->{
                String iobsFileDownloadUrl = iobsFileUploadService.getPerpetualDownloadUrl(doc.getUrl(),doc.getDocumentName());
                doc.setUrl(iobsFileDownloadUrl);
                doc.setExpireTime(nowTime.getTime());
            }).collect(Collectors.toList());
        }
        return list;
    }
    @Override
    public FileDocumentDTO queryDocumentListById(String documentGroupItemsId) {
        FileDocumentDTO fileDocument = documentDao.selectByPrimaryKey(documentGroupItemsId);
        if (iobs){
            Calendar nowTime = Calendar.getInstance();
            nowTime.add(Calendar.MINUTE,4);//cos文件url有效期5分钟，设置4分钟为扣除调用cos接口时间
            String iobsFileDownloadUrl = iobsFileUploadService.getPerpetualDownloadUrl(fileDocument.getUploadPath(),fileDocument.getDocumentName());
            fileDocument.setUrl(iobsFileDownloadUrl);
            fileDocument.setExpireTime(nowTime.getTime());
        }
        return fileDocument;
    }
    @Override
    public ResponseResult uploadDocument(byte[] file, String smallType, String reportNo, Integer caseTimes, String name, String id) throws UnsupportedEncodingException {
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setReportNo(reportNo);
        fileInfoDTO.setCaseTimes(caseTimes);
        fileInfoDTO.setDocumentGroupId(reportNo);
        fileInfoDTO.setSmallCode(smallType);
        fileInfoDTO.setFileContentBytes(file);
        fileInfoDTO.setFirstUpload("Y");
        fileInfoDTO.setTaskCode(BpmConstants.OC_SETTLE_REVIEW);

        LogUtil.audit("#上传单证#入参：reportNo=" + fileInfoDTO.getReportNo() + ",caseTimes=" + fileInfoDTO.getCaseTimes());
        String returnCode = FileUploadConstants.FAIL_CODE;
        //校验入参
        String returnMsg = "";
        String flowType = StringUtils.getFlowTypeByCaseTimes(fileInfoDTO.getCaseTimes());

        String uploadPersonnel = ConstValues.SYSTEM_UM + "-" + ConstValues.SYSTEM_UM;
        fileInfoDTO.setUploadPersonnel(uploadPersonnel);
        fileInfoDTO.setCreatedBy(ConstValues.SYSTEM_UM);
        fileInfoDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
        fileInfoDTO.setFlowType(flowType);
        try {
            String fileName = name;
            String fileFormat = fileName.substring(fileName.lastIndexOf('.') + 1);
            fileInfoDTO.setFileName(fileName);
            fileInfoDTO.setFileFormat(fileFormat);
            fileInfoDTO.setFileContentBytes(file);
            fileInfoDTO.setContentType(".pdf");
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(returnCode, "获取单证文件内容字节数组异常");
        }

        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
        if (StringUtils.isNotEmpty(returnMsg)) {
            return ResponseResult.fail(returnCode, returnMsg);
        }
        try {
            String seq = this.getSeq();
            fileInfoDTO.setRecordNo(seq);
            //神兵stg环境，文件需上传至IOBS
            String fileId = iobs ? fileUploadController.saveFilePlatform(fileInfoDTO) : fileUploadController.saveLocalTool(fileInfoDTO);
            LogUtil.audit("单证上传到文档服务器返回的 fileId=" + fileId);
            fileInfoDTO.setFileId(fileId);
            if (StringUtils.isEmptyStr(fileId)) {
                LogUtil.audit("fileId为空，单证上传到文档服务器返回的 fileId=" + fileId);
                returnMsg = "解析文件并保存至文档服务器异常";
                return ResponseResult.fail(returnCode, returnMsg);
            }
        } catch (Exception e) {
            LogUtil.error("上传单证异常", e);
            return ResponseResult.fail(returnCode, "上传单证异常");
        }
        this.addFilesInfoF(fileInfoDTO, id);
        return ResponseResult.success();
    }
    @Transactional
    public void addFilesInfoF(FileInfoDTO fileInfoDTO,String id) throws GlobalBusinessException {
        List<FileInfoDTO> fileInfoDTOList = this.getFileGroupIdForDocument(fileInfoDTO);
        documentDao.deleteById(id);
        List<FileDocumentVO> documentDTOList = this.getDocumentDTOList(fileInfoDTO);
        documentDTOList.get(0).setDocumentGroupItemsId(id);
        fileInfoMapper.createDocument(documentDTOList.get(0));

        if (ListUtils.isEmptyList(fileInfoDTOList) && FileUploadConstants.DOCUMENT_UPLOAD_FLAG_Y.equals(fileInfoDTO.getFirstUpload())) {
            fileInfoDTO.setIdAhcsFileInfo(UuidUtil.getUUID());
            fileInfoMapper.addFileInfo(fileInfoDTO);
        }
    }

    @Override
    public ResponseResult uploadDocument(byte[] file, String smallType,String reportNo,Integer caseTimes,String name,UserInfoDTO userInfoDTO) throws UnsupportedEncodingException {
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setReportNo(reportNo);
        fileInfoDTO.setCaseTimes(caseTimes);
        fileInfoDTO.setDocumentGroupId(reportNo);
        fileInfoDTO.setSmallCode(smallType);
        fileInfoDTO.setFileContentBytes(file);
        fileInfoDTO.setFirstUpload("Y");
        fileInfoDTO.setTaskCode(BpmConstants.OC_SETTLE_REVIEW);

        LogUtil.audit("#上传单证#入参：reportNo=" + fileInfoDTO.getReportNo() + ",caseTimes=" + fileInfoDTO.getCaseTimes());
        String returnCode = FileUploadConstants.FAIL_CODE;
        //校验入参
        String returnMsg = fileUploadController.validUploadDocumentParams(fileInfoDTO, userInfoDTO);
        if (StringUtils.isNotEmpty(returnMsg)) {
            return ResponseResult.fail(returnCode, returnMsg);
        }

        String flowType = StringUtils.getFlowTypeByCaseTimes(fileInfoDTO.getCaseTimes());
        String userId = userInfoDTO.getUserCode();
        String userName = userInfoDTO.getUserName();
        String uploadPersonnel = userName + "-" + userId;
        if (uploadPersonnel.getBytes("GBK").length > 50) {
            uploadPersonnel = userId;
        }
        fileInfoDTO.setUploadPersonnel(uploadPersonnel);
        fileInfoDTO.setCreatedBy(userId);
        fileInfoDTO.setUpdatedBy(userId);
        fileInfoDTO.setFlowType(flowType);
        try {
            String fileName = name;
            String fileFormat = fileName.substring(fileName.lastIndexOf('.') + 1);
            fileInfoDTO.setFileName(fileName);
            fileInfoDTO.setFileFormat(fileFormat);
            fileInfoDTO.setFileContentBytes(file);
            fileInfoDTO.setContentType(".pdf");
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(returnCode, "获取单证文件内容字节数组异常");
        }

        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
        //校验文件格式和大小
        returnMsg = fileUploadController.validDocumentFile(fileInfoDTO);
        if (StringUtils.isNotEmpty(returnMsg)) {
            return ResponseResult.fail(returnCode, returnMsg);
        }

        try {
            String seq = this.getSeq();
            fileInfoDTO.setRecordNo(seq);
            //神兵stg环境，文件需上传至IOBS
            String fileId = iobs ? fileUploadController.saveFilePlatform(fileInfoDTO) : fileUploadController.saveLocalTool(fileInfoDTO);
            LogUtil.audit("单证上传到文档服务器返回的 fileId=" + fileId);
            fileInfoDTO.setFileId(fileId);
            if (StringUtils.isEmptyStr(fileId)) {
                LogUtil.audit("fileId为空，单证上传到文档服务器返回的 fileId=" + fileId);
                returnMsg = "解析文件并保存至文档服务器异常";
                return ResponseResult.fail(returnCode, returnMsg);
            }
        } catch (Exception e) {
            LogUtil.error("上传单证异常", e);
            return ResponseResult.fail(returnCode, "上传单证异常");
        }
        return fileUploadController.addFilesInfo(fileInfoDTO,userName);
    }

    @Override
    public ResponseResult uploadDocumentByAmort(byte[] file, String smallType, String idRecoveryRecord, String fileName) throws UnsupportedEncodingException {
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setDocumentGroupId(idRecoveryRecord);
        fileInfoDTO.setFileContentBytes(file);
        fileInfoDTO.setFirstUpload("Y");

        LogUtil.audit("#上传单证#入参：reportNo=" + fileInfoDTO.getReportNo() + ",caseTimes=" + fileInfoDTO.getCaseTimes());
        UserInfoDTO userDTO = WebServletContext.getUser();
        String returnCode = FileUploadConstants.FAIL_CODE;
        String returnMsg="";

        String flowType = StringUtils.getFlowTypeByCaseTimes(fileInfoDTO.getCaseTimes());
        String userId = userDTO.getUserCode();
        String userName = userDTO.getUserName();
        String uploadPersonnel = userName + "-" + userId;
        if (uploadPersonnel.getBytes("GBK").length > 50) {
            uploadPersonnel = userId;
        }
        fileInfoDTO.setUploadPersonnel(uploadPersonnel);
        fileInfoDTO.setCreatedBy(userId);
        fileInfoDTO.setUpdatedBy(userId);
        fileInfoDTO.setFlowType(flowType);
        try {
            String fileFormat = fileName.substring(fileName.lastIndexOf('.') + 1);
            fileInfoDTO.setFileName(fileName);
            fileInfoDTO.setFileFormat(fileFormat);
            fileInfoDTO.setFileContentBytes(file);
            fileInfoDTO.setContentType(fileFormat);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(returnCode, "获取单证文件内容字节数组异常");
        }

        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
        //校验文件格式和大小
        returnMsg = fileUploadController.validDocumentFile(fileInfoDTO);
        if (StringUtils.isNotEmpty(returnMsg)) {
            return ResponseResult.fail(returnCode, returnMsg);
        }

        try {
            String seq = this.getSeq();
            fileInfoDTO.setRecordNo(seq);
            //神兵stg环境，文件需上传至IOBS
            String fileId = iobs ? fileUploadController.saveFilePlatform(fileInfoDTO) : fileUploadController.saveLocalTool(fileInfoDTO);
            LogUtil.audit("单证上传到文档服务器返回的 fileId=" + fileId);
            fileInfoDTO.setFileId(fileId);
            if (StringUtils.isEmptyStr(fileId)) {
                LogUtil.audit("fileId为空，单证上传到文档服务器返回的 fileId=" + fileId);
                returnMsg = "解析文件并保存至文档服务器异常";
                return ResponseResult.fail(returnCode, returnMsg);
            }
        } catch (Exception e) {
            LogUtil.error("上传单证异常", e);
            return ResponseResult.fail(returnCode, "上传单证异常");
        }
        return fileUploadController.addFilesInfo(fileInfoDTO,userName);
    }
}
