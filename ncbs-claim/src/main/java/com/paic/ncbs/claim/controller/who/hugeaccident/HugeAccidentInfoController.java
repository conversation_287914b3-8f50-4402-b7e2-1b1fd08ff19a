package com.paic.ncbs.claim.controller.who.hugeaccident;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.vo.accident.HugeAccidentInfoVO;
import com.paic.ncbs.claim.service.accident.HugeAccidentInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "重灾维护")
@RestController
@RequestMapping(value = "/who/app/hugeAccidentInfoAction")

public class HugeAccidentInfoController extends BaseController {

    @Autowired
    private HugeAccidentInfoService hugeAccidentInfoService;


    @ApiOperation("新增或更新重灾详细信息")
    @PostMapping(value = "/getHugeAccidentInfo")
    public ResponseResult<List<HugeAccidentInfoVO>> getHugeAccidentInfo(@RequestBody HugeAccidentInfoVO hugeAccidentInfo){
        LogUtil.audit("#重灾维护#插入或更新重灾详细信息, reportNo=%s, caseTimes=%s", hugeAccidentInfo.getReportNo(), hugeAccidentInfo.getCaseTimes());
        return ResponseResult.success(hugeAccidentInfoService.getHugeAccidentInfo(hugeAccidentInfo));
    }


}