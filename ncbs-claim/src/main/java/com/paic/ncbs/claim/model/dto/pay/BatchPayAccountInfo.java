package com.paic.ncbs.claim.model.dto.pay;

/**
 * 结算收款账号信息
 */
public class BatchPayAccountInfo {

    /**
     * 结算单号
     */
    private String settlementNo;

    /**
     * 收款账号
     */
    private String accountNo;

    /**
     * 收款户名
     */
    private String accountName;

    /**
     * 对公对私标志，0－企业，1－个人，不传默认为对公
     */
    private String bankPayType;

    /**
     * 收款方银行联行号
     */
    private String bankCode;

    /**
     * 收款方银行大类名称
     */
    private String bankName;

    /**
     * 收款方开户行名称：中国银行深圳支行
     * 对公对私标志为0-企业时，必填；对公对私标志为1-个人时，金额大于100万必填
     */
    private String brachBankName;

    public String getSettlementNo() {
        return settlementNo;
    }

    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBankPayType() {
        return bankPayType;
    }

    public void setBankPayType(String bankPayType) {
        this.bankPayType = bankPayType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBrachBankName() {
        return brachBankName;
    }

    public void setBrachBankName(String brachBankName) {
        this.brachBankName = brachBankName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

}
