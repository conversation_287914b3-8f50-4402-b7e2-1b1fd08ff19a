package com.paic.ncbs.claim.service.prepay.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.CollectPayApproachEnum;
import com.paic.ncbs.claim.common.enums.PaymentInfoTypeEnum;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.coinsurance.PaymentItemFee;
import com.paic.ncbs.claim.dao.mapper.casezero.CaseZeroCancelMapper;
import com.paic.ncbs.claim.dao.mapper.coinsurance.PaymentItemFeeMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.prepay.PrePayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.PlanTermContentDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.vo.ahcs.*;
import com.paic.ncbs.claim.model.vo.settle.MaxPayParam;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.ahcs.AhcsPolicyPlanDataService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.coinsurance.CoinsuranceService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.common.ResidueAmountService;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.duty.DutyAttrAsyncInitService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.fee.FeePayService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.prepay.PrePayService;
import com.paic.ncbs.claim.service.prepay.PrePayTransactionService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.service.verify.VerifyService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.SettleConst.CLAIM_TYPE_PRE_PAY;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Slf4j
@Service("prePayService")
public class PrePayServiceImpl implements PrePayService {

	/**
	 * 已申请
	 */
	private static final String PREPAY_APPLY = "1";
	/**
	 * 已审批
	 */
	private static final String PREPAY_APPROVE = "2";

	@Autowired
	private PrePayMapper prePayMapper;
	@Autowired
	private FeePayService feePayService;
	@Autowired
	private PolicyPayService policyPayService;
	@Autowired
	private CoinsureService coinsureService;
	@Autowired
	private PaymentInfoService paymentInfoService;
	@Autowired
	private PaymentItemService paymentItemService;
	@Autowired
	private CommonService commonService;
	@Autowired
	private PrePayTransactionService prePayTransactionService;
	@Autowired
	private WholeCaseBaseService wholeCaseBaseService;
	@Autowired
	private CaseZeroCancelMapper caseZeroCancelMapper;
	@Autowired
	private CacheService cacheService;
	@Autowired
	private DutyPayMapper dutyPayDao;
	@Autowired
	private EstimateService estimateService;
	@Autowired
	private EstimatePolicyMapper estimatePolicyDAO;
	@Autowired
	private MaxPayService maxPayService;
	@Autowired
	private DutyPayMapper dutyPayMapper;
	@Autowired
	private PolicyClaimCaseMapper policyClaimCaseMapper;
	@Autowired
	private UserInfoService userInfoService;
	@Autowired
	private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

	@Autowired
	private DutyAttrAsyncInitService dutyAttrAsyncInitService;

	@Autowired
	@Lazy
	private VerifyService verifyService;

	@Autowired
	private AhcsPolicyPlanDataService ahcsPolicyPlanDataService;

	@Autowired
	private PolicyPayMapper policyPayMapper;
	@Autowired
	private ResidueAmountService residueAmountService;
	@Autowired
	private DutyDetailPayMapper dutyDetailPayInfoDao;
	@Autowired
	private TaskInfoMapper taskInfoMapper;
	@Autowired
	private PermissionService permissionService;
	@Autowired
	private IOperationRecordService operationRecordService;
	@Autowired
	private PolicyInfoMapper policyInfoMapper;
	@Autowired
	private TaskInfoService taskInfoService;

	@Autowired
	private RiskPropertyService riskPropertyService;
	@Autowired
	private BpmService bpmService;
	@Autowired
	private NoticeService noticeService;
	@Autowired
	private PermissionUserMapper permissionUserMapper;
	@Autowired
	private PrintCoreService printCoreService;
	@Autowired
	private CoinsuranceService coinsuranceService;
	@Autowired
	private PaymentItemFeeMapper paymentItemFeeMapper;

	@Autowired
	private GlobalPolicyService globalPolicyService;

	private static Map<String,String> PRE_APPLY_TASK_NODE = new HashMap<>();
	static {
		PRE_APPLY_TASK_NODE.put(BpmConstants.OC_CHECK_DUTY,"2");
		PRE_APPLY_TASK_NODE.put(BpmConstants.OC_MANUAL_SETTLE,"3");
	}
	

	/**
	 * @Description: 判断是否可以发起预赔申请
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 * 返回类型  List<FeePayDTO>
	 */
	@Override
	public void isCanPrePayApply(String reportNo, Integer caseTimes) throws GlobalBusinessException {
		if(caseTimes>1){
			throw new GlobalBusinessException("重开案件不能发起预赔！");
		}

		PaymentInfoDTO param = new PaymentInfoDTO();
		param.setReportNo(reportNo);
		param.setCaseTimes(caseTimes);

		List<PaymentInfoDTO> paymentInfoList = paymentInfoService.getPaymentInfo(param);
		if(ListUtils.isEmptyList(paymentInfoList)){
			throw new GlobalBusinessException("请录入支付信息");
		}

		WholeCaseBaseDTO wholecase = wholeCaseBaseService.getWholeCaseBase2(reportNo,caseTimes);
		if(!ConstValues.YES.equals(wholecase.getIsRegister())){
			throw new GlobalBusinessException("未立案不能预赔");
		}

		if("0".equals(wholecase.getWholeCaseStatus())){
			throw new GlobalBusinessException("已结案不能预赔");
		}
		//校验当前流程是否有冲突 预赔 发起
		bpmService.processCheck(reportNo,BpmConstants.OC_PREPAY_REVIEW,BpmConstants.OPERATION_INITIATE);

		/* delete by zjtang 取消旧校验逻辑
		if(caseZeroCancelMapper.getZeroCancelApplyCount(reportNo,caseTimes) > 0){
			throw new GlobalBusinessException("已发起零注申请不能预赔");
		}

		//是否存在未完成的预赔申请
		Integer i = prePayMapper.getNoFinishPrePay(reportNo, caseTimes);
		if ( i > 0) {
			// 定义错误码提示：存在未完成预赔申请
			throw new GlobalBusinessException("已申请预赔未完成审批");
		}

		//校验当前案件是否存在除过主任务外其他未完成的流程,若存在，就不能发起预赔
		TaskInfoDTO taskInfoDTOCondition = new TaskInfoDTO();
		taskInfoDTOCondition.setCaseTimes(caseTimes);
		taskInfoDTOCondition.setReportNo(reportNo);
		List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getUndoTaskInfoList(taskInfoDTOCondition);
		if(ListUtils.isNotEmpty(taskInfoVOList) && taskInfoVOList.size() > 0) {
			throw new GlobalBusinessException("当前案件存在其他未处理任务，不能发起预赔！");
		}
		 */
	}

	/**
	 * 查询预赔次数。逻辑：已审批完成的预赔次数 + 1
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	public Integer getApprovedSubTimes(String reportNo, Integer caseTimes) {
		Integer subTimes = 0;
		if(prePayMapper.getApprovedSubTimes(reportNo,caseTimes) != null){
			subTimes = prePayMapper.getApprovedSubTimes(reportNo,caseTimes);
		}
		return subTimes + 1;
	}

		@Override
	public List<DutyPrepayInfoDTO> getDutyPrepayInfoList(DutyPrepayInfoDTO prepayInfoDTO)
			throws GlobalBusinessException {
		return prePayMapper.getDutyPrepayInfoList(prepayInfoDTO);
	}

	@Override
	public PrePayCaseVO getPrePayCaseList(String reportNo, Integer caseTimes,Integer subTimes) {
		List<PreDutyVO> preDutyList = prePayMapper.getPrePayDutyList(reportNo);
		if(ListUtils.isEmptyList(preDutyList)){
			return new PrePayCaseVO();
		}
		if(subTimes == null){
			subTimes = prePayMapper.getCurrentApprovedSubTimes(reportNo,caseTimes);
		}
		//设置已申请预赔金额
		BigDecimal prePayAmt = setApplyAmount(preDutyList,reportNo,caseTimes,subTimes);
		//设置最大给付额
		setMaxPayAmount(reportNo,caseTimes,preDutyList);

		/**
		 * 查询责任明细数据：预赔申请和预赔审批都调同一个方法：预赔申请初始化查询责任明细查询的是clms_policy_duty_detail表，这个表没有预赔金额，
		 * 预赔申请时责任明细数据落库到clms_policy_prepay_duty_detail表了
		 * 预赔审批 页面初始化查询也是要查询 责任明细 ，这个时候责任明显示应该是查询预赔申请录入的责任信息，所以这里要查询clms_policy_prepay_duty_detail表
		 */
		//1:查询报案任务节点，如果是预赔审批 就查询clms_policy_prepay_duty_detail
		List<ClmsPolicyPrepayDutyDetailDTO> prepayDutyDetailDTOList = new ArrayList<>();
		String processStatus=prePayMapper.getProcessStatus(reportNo,caseTimes);
		//预赔审批
		if(Objects.equals(CaseProcessStatus.PREPAY_REJECT.getCode(),processStatus)){
			prepayDutyDetailDTOList = prePayMapper.getApprovalPrePayDutyDetailList(reportNo,caseTimes,subTimes);
			log.info("prepayDutyDetailDTOList:{}",prepayDutyDetailDTOList);
		}else{
			//预赔申请
			prepayDutyDetailDTOList = prePayMapper.getPrePayDutyDetailList(reportNo);
		}

		Map<String,List<ClmsPolicyPrepayDutyDetailDTO>> dutyDetailMap = prepayDutyDetailDTOList.stream().collect(Collectors.groupingBy(ClmsPolicyPrepayDutyDetailDTO::getDutyCode));

		Map<String,String> coinsMap = coinsureService.getCoinsureDescByReportNo(reportNo);
		Map<String, List<CoinsureDTO>> coinsuranceMap = coinsureService.getCoinsureListByReportNo(reportNo);
		Map<String, List<CoinsureRecordDTO>> coinsuranceRecordMap = coinsureService.getCoinsureRecordByReportNo(reportNo);
		List<PrePolicyVO> policyList = new ArrayList<>();
		Map<String, List<PreDutyVO>> policyMap = preDutyList.stream().collect(Collectors.groupingBy(PreDutyVO::getPolicyNo));
		for (Map.Entry<String, List<PreDutyVO>> policyEntry : policyMap.entrySet()) {
			PrePolicyVO policyVO = new PrePolicyVO();
			BeanUtils.copyProperties(policyEntry.getValue().get(0),policyVO);
			Map<String, List<PreDutyVO>> planMap = policyEntry.getValue().stream().collect(Collectors.groupingBy(PreDutyVO::getPlanCode));
			List<PrePlanVO> planList = new ArrayList<>();
			for (Map.Entry<String, List<PreDutyVO>> planEntry : planMap.entrySet()) {
				PrePlanVO planVO = new PrePlanVO();
				List<PreDutyVO> preDutyVOS = planEntry.getValue();
				BeanUtils.copyProperties(preDutyVOS.get(0),planVO);
				Boolean isShareAmount = preDutyVOS.stream().anyMatch(PreDutyVO::getShareAmount);

				for (PreDutyVO preDutyVO : preDutyVOS) {
					//其他责任属性
					preDutyVO.setAttributes(dutyAttrAsyncInitService.getOtherDutyAttributeList(preDutyVO.getIdPolicyDuty()));
					preDutyVO.setPrepayDutyDetailDTOList(dutyDetailMap.get(preDutyVO.getDutyCode()));
					//查询责任历史赔付金额
					BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay("pre", reportNo, policyVO.getPolicyNo(), planVO.getPlanCode(),
							preDutyVO.getDutyCode(), preDutyVO.getDutyShareAmount(), preDutyVO.getShareDutyGroup(), isShareAmount);
					HistoryPayInfoDTO historyPayInfo = new HistoryPayInfoDTO();
					historyPayInfo.setPolicyNo(policyVO.getPolicyNo());
					historyPayInfo.setPlanCode(planVO.getPlanCode());
					historyPayInfo.setDutyCode(preDutyVO.getDutyCode());
					historyPayInfo.setDutyBaseAmount(preDutyVO.getDutyAmount());
					historyPayInfo.setDutyHistoryPay(dutyHistoryPay);
					BigDecimal dutyMaxPay = residueAmountService.getDutyMaxPay(historyPayInfo);
					//责任明细层级的剩余理赔金额计算
					BigDecimal  dutySum= calculateResidueAmount(preDutyVO,reportNo,policyVO.getPolicyNo(),planVO.getPlanCode(),historyPayInfo,dutyMaxPay, isShareAmount);
					if(preDutyVO.getDutyShareAmount()){
						//共享保额的剩余理赔金额等于基本保额减去责任历史赔付金额（历史赔付金额计算保函了预赔部分）
						preDutyVO.setDutyMaxPayAmount(preDutyVO.getDutyAmount().subtract(dutyHistoryPay));
					}else{
						//非共享保额的每个责任的剩余理赔金额等于 等于基本保额-明细历史预赔金额
						preDutyVO.setDutyMaxPayAmount(preDutyVO.getDutyAmount().subtract(dutySum));
					}
				}
				planVO.setPreDutyList(preDutyVOS);
				planList.add(planVO);
			}
			policyVO.setPrePlanList(planList);
			//查询小条款信息
			List<PlanTermContentDTO>  termDtoList = ahcsPolicyPlanDataService.getPlanTermContentInfo(reportNo);
			policyVO.setPlanTermContentDTOList(termDtoList);
			//特别约定
			List<SpecialPromiseDTO>  specialPromiseDTOList = policyPayMapper.getSpecialPromise(policyVO.getIdAhcsPolicyInfo());
			policyVO.setSpecialPromiseDTOList(specialPromiseDTOList);

			policyList.add(policyVO);
			policyVO.setCoinsuranceDesc(coinsMap.getOrDefault(policyVO.getPolicyNo(),""));
			policyVO.setCoinsuranceList(coinsuranceMap.get(policyVO.getPolicyNo()));

			if (coinsuranceRecordMap.containsKey(policyVO.getPolicyNo())) {
				policyVO.setIsFullPay(BaseConstant.STRING_1);
			}
		}
		PrePayCaseVO caseVO = new PrePayCaseVO();
		caseVO.setReportNo(reportNo);
		caseVO.setCaseTimes(caseTimes);
		caseVO.setPrePayAmount(prePayAmt);
		BigDecimal preFeeAmt = getPrePayFeeAmount(reportNo,caseTimes,subTimes);
		caseVO.setPreFeeAmount(preFeeAmt);
		caseVO.setPreTotalAmount(prePayAmt.add(preFeeAmt));
		caseVO.setPrePolicyList(policyList);
		//理算页面支付信息展示 点击金额时 根据预赔次数展示对应的明细数据
		if(Objects.equals(CaseProcessStatus.PENDING_SETTLE.getCode(),processStatus)){
			List<ClmsPolicyPrepayDutyDetailDTO> settleDTOList=prePayMapper.getAllApprovalPrePayDutyDetailList(reportNo,caseTimes);
			for (ClmsPolicyPrepayDutyDetailDTO dto : settleDTOList) {
				List<PreDutyVO> preDutyVOList= policyMap.get(dto.getPolicyNo());
				Map<String,List<PreDutyVO>> planMap= preDutyVOList.stream().collect(Collectors.groupingBy(PreDutyVO::getPlanCode));
				List<PreDutyVO> dutyplanList= planMap.get(dto.getPlanCode());
				Map<String,List<PreDutyVO>> dutyMap = dutyplanList.stream().collect(Collectors.groupingBy(PreDutyVO::getDutyCode));
				boolean isShareAmount = dutyMap.get(dto.getDutyCode()).get(0).getDutyShareAmount();
				String shareDutyGroup=dutyMap.get(dto.getDutyCode()).get(0).getShareDutyGroup();

				BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay("pre", reportNo, dto.getPolicyNo(), dto.getPlanCode(),
						dto.getDutyCode(), isShareAmount, shareDutyGroup, isShareAmount);

				dto.setResidueAmount(dto.getDutyDetailAmount().subtract(dutyHistoryPay));

				List<PreDutyVO> dutyVOList =policyMap.get(dto.getPolicyNo());
				//保单归属机构
				String departMenatName =dutyVOList.get(0).getDepartmentName();
				dto.setDepartmentName(departMenatName);
				dto.setDepartmentCode(dutyVOList.get(0).getDepartmentCode());
				//责任明细剩余理赔金额
				dto.setResidueAmount(dto.getDutyDetailAmount().subtract(dutyHistoryPay));

			}
			caseVO.setSettlePrepayDutyDetailDTOList(settleDTOList);
		}

		return caseVO;
	}

	/**
	 * 责任明细层级的剩余理赔金额计算
	 *
	 * @param preDutyVO
	 * @param reportNo
	 * @param policyNo
	 * @param planCode
	 * @param isShareAmount
	 */
	private BigDecimal calculateResidueAmount(PreDutyVO preDutyVO, String reportNo, String policyNo, String planCode,
											  HistoryPayInfoDTO historyPayInfo,BigDecimal dutyMaxPay, Boolean isShareAmount) {
		List<ClmsPolicyPrepayDutyDetailDTO> dutyDetailDTOList =	preDutyVO.getPrepayDutyDetailDTOList();
		//责任下的所有明细历史预赔总和
		BigDecimal dutyCodeAmount=BigDecimal.ZERO;
		for (ClmsPolicyPrepayDutyDetailDTO dto : dutyDetailDTOList) {
			String dutyCode = dto.getDutyCode();
			String dutyDetailCode = dto.getDutyDetailCode();
			//查询责任明细历史赔付金额
			BigDecimal dutyDetailHistoryPay = residueAmountService.getDutyDetailHistoryPay("pre", reportNo, policyNo, planCode, dutyCode, dutyDetailCode, isShareAmount);
			MaxPayParam param = new MaxPayParam();
			param.setSelectScene("pre");
			param.setReportNo(reportNo);
			param.setPolicyNo(policyNo);
			param.setPlanCode(planCode);
			param.setDutyCode(dutyCode);
			param.setDutyDetailCode(dutyDetailCode);
			//预赔历史金额
			BigDecimal preDutyDetailHistoryPay=dutyDetailPayInfoDao.getPrePayDutyDetailHistoryPayAmount(param);
			//历史理赔金额等于历史预赔金额+非预赔金额
			dutyDetailHistoryPay=dutyDetailHistoryPay.add(preDutyDetailHistoryPay);

			historyPayInfo.setDutyDetailCode(dutyDetailCode);
			historyPayInfo.setDutyDetailBaseAmount(dto.getDutyDetailAmount());
			historyPayInfo.setDutyDetailHistoryPay(dutyDetailHistoryPay);
			BigDecimal dutyDetailMaxPay = residueAmountService.getDutyDetailMaxPay(historyPayInfo);

			//责任明细剩余理赔金额不能大于责任剩余理赔金额
			if (dutyMaxPay.compareTo(dutyDetailMaxPay) < 0) {
				dto.setResidueAmount(dutyMaxPay);
			} else {
				dto.setResidueAmount(dutyDetailMaxPay);
			}
			//总的历史赔付金额 = 基本保额-剩余理赔金额
			dutyCodeAmount=historyPayInfo.getDutyDetailBaseAmount().subtract(dto.getResidueAmount());

		}
		return dutyCodeAmount;
	}

	private void setMaxPayAmount(String reportNo,Integer caseTimes,List<PreDutyVO> preDutyList){
		List<EstimatePolicyDTO> estimatePolicyDTOList = estimatePolicyDAO.getByReportNoAndCaseTimes(reportNo, caseTimes);
		if (!CollectionUtils.isEmpty(estimatePolicyDTOList)) {
			estimateService.clearEstimateDutyRecordList(estimatePolicyDTOList, BaseConstant.STRING_02);
			maxPayService.initEstPoliciesPayMaxPay(estimatePolicyDTOList, null);
			estimatePolicyDTOList.forEach(policy->{
				List<EstimatePlanDTO> planPayDTOS = policy.getEstimatePlanList();
				if(ListUtils.isEmptyList(planPayDTOS)){
					return;
				}
				planPayDTOS.forEach(plan->{
					List<EstimateDutyRecordDTO> dutyPayDTOS = plan.getEstimateDutyRecordList();
					dutyPayDTOS.forEach(duty-> preDutyList.forEach(duty2->{
						if (duty.getPolicyNo().equals(duty2.getPolicyNo()) && duty.getPlanCode().equals(duty2.getPlanCode())
								&& duty.getDutyCode().equals(duty2.getDutyCode())){
							duty2.setDutyMaxPayAmount(duty.getDutyMaxPay());
							duty2.setShareAmount(duty.getIsShareAmount());
							duty2.setDutyShareAmount(duty.getIsDutyShareAmount());
							duty2.setShareDutyGroup(duty.getShareDutyGroup());
						}
					}));
				});
			});
		}
	}
	
	private BigDecimal setApplyAmount(List<PreDutyVO> preDutyList,String reportNo,Integer caseTimes,Integer subTimes){
		BigDecimal prePayAmt = BigDecimal.ZERO;
		List<PreDutyVO> preApplyDutyList = prePayMapper.getPrePayApplyDutyList(reportNo,caseTimes,subTimes);
		if(ListUtils.isEmptyList(preApplyDutyList)){
			return prePayAmt;
		}
		Map<String,BigDecimal> preApplyMap = preApplyDutyList.stream().collect(Collectors.toMap(k->k.getPolicyNo()+k.getPlanCode()+k.getDutyCode(),v -> BigDecimalUtils.nvl(v.getDutyPreAmount(),"0")));
		for (PreDutyVO vo : preDutyList) {
			vo.setDutyPreAmount(preApplyMap.get(vo.getPolicyNo()+vo.getPlanCode()+vo.getDutyCode()));
		}
		for (PreDutyVO vo : preApplyDutyList) {
			if(vo.getDutyPreAmount() == null){
				continue;
			}
			prePayAmt = prePayAmt.add(vo.getDutyPreAmount());
		}
		return prePayAmt;
	}

	@Override
	public void savePrePayApply(PrePayCaseVO prePayCaseVO) {
		if (riskPropertyService.displayRiskProperty(prePayCaseVO.getReportNo(), null)) {
			throw new GlobalBusinessException("责任险案件不支持预赔");
		}

		//校验能否发起预赔
		isCanPrePayApply(prePayCaseVO.getReportNo(),prePayCaseVO.getCaseTimes());
		if(!PRE_APPLY_TASK_NODE.containsKey(prePayCaseVO.getTaskNode())){
			throw new GlobalBusinessException("请在收单或理算发起预赔");
		}
		// 预赔总金额和联金额的校验
		checkEstimateAmount(prePayCaseVO);
		//费用关联责任校验
		checkFeeDutyCode(prePayCaseVO);
		//校验剩余理赔金额
		checkMaxPay(prePayCaseVO.getReportNo(),prePayCaseVO.getPrePolicyList());

		String userId = WebServletContext.getUserId();
		prePayCaseVO.setIdAhcsPrepayInfo(UuidUtil.getUUID());
		//获取预赔次数
		Integer subTimes = getApprovedSubTimes(prePayCaseVO.getReportNo(),prePayCaseVO.getCaseTimes());

		prePayCaseVO.setSubTimes(subTimes);

		//构建责任预赔信息
		List<DutyPrepayInfoDTO> dutyPrepayInfoList = buildDutyPrePayList(prePayCaseVO,userId,subTimes);

		//构建保单费用信息，为后面生成费用支付项做准备
		buildPolicyFeeList(prePayCaseVO,subTimes);

		if(!BigDecimalUtils.compareBigDecimalPlus(prePayCaseVO.getPreTotalAmount(),BigDecimal.ZERO)){
			throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"预赔总金额需大于0");
		}

		//构建预赔申请表信息
		PrePayInfoDTO prePayInfoDTO = buildPrePayInfoDTO(prePayCaseVO,userId);
		prePayInfoDTO.setSubTimes(subTimes);

		checkPrePayPaymentInfo(prePayCaseVO);

		//构建赔款支付项
		List<PaymentItemDTO> paymentItemList = buildPaymemtItem(prePayCaseVO,userId,subTimes);

		//构建费用支付项
		paymentItemList.addAll(buildFeePaymemtItem(prePayCaseVO,userId,subTimes));
		//主共保案件需拆分费用支付项子表
		Map<String, List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(prePayCaseVO.getReportNo());
		List<PaymentItemFee> paymentItemFees = new ArrayList<>();
		if(coinsureService.isMainCoinsureFlag(prePayCaseVO.getReportNo())) {
			List<FeePayDTO> feePayDTOS = getPrePayFee(prePayCaseVO.getReportNo(),prePayCaseVO.getCaseTimes()).getNormalFee();
			paymentItemFees = feePayService.FeeItemSplitByCoins(paymentItemList, coinsMap, feePayDTOS);
		}

		//开启事务写预赔申请相关表
		prePayTransactionService.savePrePayApplyInfo(prePayCaseVO,prePayInfoDTO,dutyPrepayInfoList,paymentItemList,paymentItemFees);

	}

	private void checkFeeDutyCode(PrePayCaseVO prePayCaseVO) {
		List<PrePolicyVO> prePolicyVOList = prePayCaseVO.getPrePolicyList();
		String reportNo = prePayCaseVO.getReportNo();
		Integer caseTimes = prePayCaseVO.getCaseTimes();
		if(prePolicyVOList!=null && prePolicyVOList.size()>0){
			//获取有赔款金额的责任编码
			List<String> dutyCodes = prePolicyVOList.stream()
					.flatMap(prePolicyVo -> prePolicyVo.getPrePlanList().stream())
					.flatMap(prePlanVO -> prePlanVO.getPreDutyList().stream())
					.filter(preDutyVO -> BigDecimal.ZERO
							.compareTo(ObjectUtil.isNotEmpty(preDutyVO.getDutyPreAmount())
									? preDutyVO.getDutyPreAmount()
									: BigDecimal.ZERO)<0)
					.map(PreDutyVO::getDutyCode).collect(Collectors.toList());
			commonService.checkFeeDutyCode(reportNo,caseTimes,dutyCodes,SettleConst.CLAIM_TYPE_PRE_PAY);
		}
	}

	private void checkEstimateAmount(PrePayCaseVO prePayCaseVO) {
		// 1、先查詢是否有已預賠的金額
		List<PreDutyVO> preDutyList = prePayMapper.getPrePayDutyList(prePayCaseVO.getReportNo());
		List<PreDutyVO> dutyPreList = prePayMapper.getDutyPrepaySum(prePayCaseVO.getReportNo(), prePayCaseVO.getCaseTimes());
		BigDecimal prePayAmt = BigDecimal.ZERO;
		if (ListUtils.isNotEmpty(dutyPreList)) {
			Map<String, PreDutyVO> dutyPrePayMap = dutyPreList.stream().collect(Collectors.toMap(k ->k.getPolicyNo()+k.getPlanCode()+k.getDutyCode(),dto->dto));
			for (PreDutyVO vo : preDutyList) {
				vo.setDutyPreAmount(dutyPrePayMap.getOrDefault(vo.getPolicyNo()+vo.getPlanCode()+vo.getDutyCode(),new PreDutyVO()).getDutyPreAmount());
				if(vo.getDutyPreAmount() != null){
					prePayAmt = prePayAmt.add(vo.getDutyPreAmount());
				}
			}
		}
		//
		BigDecimal preTotalAmount = prePayAmt.add(prePayCaseVO.getPreTotalAmount());
		// 通过单号查询立案金额
		BigDecimal estimateAmount = dutyPayDao.getEstimateAmount(prePayCaseVO.getReportNo(),prePayCaseVO.getCaseTimes());
		if (preTotalAmount.compareTo(estimateAmount)>0) {
			throw new GlobalBusinessException("累计预赔金额已经超过立案金额，请核实！");
		}
	}

	void checkMaxPay(String reportNo, List<PrePolicyVO> prePolicyList){
		if(ListUtils.isEmptyList(prePolicyList)){
			return;
		}
		List<PolicyClaimCaseDTO> policyCaseList = policyClaimCaseMapper.getInsuredCodeByReportNo(reportNo);
		Map<String,String> insuredCodeMap = policyCaseList.stream().collect(Collectors.toMap(PolicyClaimCaseDTO::getPolicyNo,PolicyClaimCaseDTO::getInsuredCode));
		for (PrePolicyVO policy : prePolicyList) {
			if(ListUtils.isEmptyList(policy.getPrePlanList())){
				continue;
			}
			for (PrePlanVO plan : policy.getPrePlanList()) {
				if(ListUtils.isEmptyList(plan.getPreDutyList())){
					continue;
				}
				for (PreDutyVO duty : plan.getPreDutyList()) {
					BigDecimal dutyPreAmt = duty.getDutyPreAmount();
					if(dutyPreAmt == null || BigDecimal.ZERO.equals(dutyPreAmt)){
						continue;
					}
					BigDecimal shareAmount = null;
					if(StringUtils.isNotEmpty(duty.getShareDutyGroup())){
						shareAmount = getShareAmount(duty.getShareDutyGroup(),plan.getPreDutyList());
					}
					if(!checkPlanDutyMaxPay(policy.getPolicyNo(),plan.getPlanCode(),insuredCodeMap.get(policy.getPolicyNo()),duty,shareAmount)){
						throw new GlobalBusinessException(duty.getDutyName()+"("+duty.getDutyCode()+")预赔金额不能大于剩余理赔金额");
					}
				}
			}
		}
	}

	private BigDecimal getShareAmount(String shareGroup,List<PreDutyVO> preDutyList){
		Set<String> shareSet = new HashSet<>(Arrays.asList(shareGroup.split(",")));
		BigDecimal shareAmount = BigDecimal.ZERO;
		for (PreDutyVO duty : preDutyList) {
			if(duty.getDutyPreAmount() != null && shareSet.contains(duty.getDutyCode())){
				shareAmount = shareAmount.add(duty.getDutyPreAmount());
			}
		}
		return shareAmount;
	}

	private boolean checkPlanDutyMaxPay(String policyNo,String planCode,String insuredCode,PreDutyVO duty,BigDecimal shareAmt){
		MaxPayParam maxPayParam = new MaxPayParam();
		maxPayParam.setPolicyNo(policyNo);
		maxPayParam.setPlanCode(planCode);
		maxPayParam.setDutyCode(duty.getDutyCode());

		if(!duty.getShareAmount()){
			//不是标的共享
			maxPayParam.setInsuredCode(insuredCode);
		}

		if(StringUtils.isNotEmpty(duty.getShareDutyGroup())){
			//责任共享
			maxPayParam.setDutyCode(null);
			maxPayParam.setDutyCodeList(Arrays.asList(duty.getShareDutyGroup().split(",")));
		}
		LogUtil.audit("maxPayParam={}",JSON.toJSONString(maxPayParam));
		BigDecimal hisAmt = BigDecimalUtils.nvl(dutyPayMapper.getDutyHistoryAmount(maxPayParam),"0");
		BigDecimal baseAmt = BigDecimalUtils.nvl(duty.getDutyAmount(),"0");
		BigDecimal maxPay = baseAmt.subtract(hisAmt);
		LogUtil.audit("policyNo={},plan={},duty={},",policyNo,planCode,duty.getDutyCode());
		LogUtil.audit("基本amt={},已赔amt={},预赔amt={}",baseAmt,hisAmt,duty.getDutyPreAmount());

		if(shareAmt != null && shareAmt.compareTo(BigDecimal.ZERO) > 0 && shareAmt.compareTo(maxPay) > 0){
			throw new GlobalBusinessException("责任编码为:["+duty.getShareDutyGroup()+"]预赔赔款金额之和["+shareAmt+"]不能大于剩余理赔金额["+maxPay+"]");
		}
		if(BigDecimalUtils.compareBigDecimalPlusOrEqual(maxPay,duty.getDutyPreAmount())){
			return true;
		}
		return false;
	}

	@SneakyThrows
	private PrePayInfoDTO buildPrePayInfoDTO(PrePayCaseVO prePayCaseVO, String userId){
		PrePayInfoDTO payInfoDTO = new PrePayInfoDTO();
		payInfoDTO.setCreatedBy(userId);
		payInfoDTO.setUpdatedBy(userId);
		payInfoDTO.setPrePayInfoId(prePayCaseVO.getIdAhcsPrepayInfo());
		payInfoDTO.setReportNo(prePayCaseVO.getReportNo());
		payInfoDTO.setCaseTimes(prePayCaseVO.getCaseTimes());
		payInfoDTO.setPrePayAmount(prePayCaseVO.getPreTotalAmount());
		payInfoDTO.setApplyAmount(prePayCaseVO.getPrePayAmount());
		payInfoDTO.setPrePayCause(prePayCaseVO.getPrePayCause());
		payInfoDTO.setStatus(PREPAY_APPLY);
		payInfoDTO.setApplyBy(userId);
		payInfoDTO.setApplyName(Optional.ofNullable(userInfoService.getUserNameById(userId)).orElse("SYSTEM"));
		payInfoDTO.setApplyRemark(prePayCaseVO.getApplyRemark());
		payInfoDTO.setPrepayBigType(PRE_APPLY_TASK_NODE.get(prePayCaseVO.getTaskNode()));
		payInfoDTO.setTaskDefinitionBpmKey(prePayCaseVO.getTaskNode());
		prePayCaseVO.setIdAhcsPrepayInfo(payInfoDTO.getPrePayInfoId());
		return payInfoDTO;
	}

	private List<DutyPrepayInfoDTO> buildDutyPrePayList(PrePayCaseVO prePayCaseVO ,String userId,Integer subTimes){
		List<DutyPrepayInfoDTO> resultList = new ArrayList<>();
		BigDecimal prePayAmount = BigDecimal.ZERO;

		for(PrePolicyVO policy : prePayCaseVO.getPrePolicyList()){
			BigDecimal policyPayAmount = BigDecimal.ZERO;

			for (PrePlanVO plan : policy.getPrePlanList()) {
				for (PreDutyVO duty : plan.getPreDutyList()) {
					policyPayAmount = policyPayAmount.add(BigDecimalUtils.nvl(duty.getDutyPreAmount(),0));
					DutyPrepayInfoDTO dutyPrepayInfoDTO = new DutyPrepayInfoDTO();
					dutyPrepayInfoDTO.setCreatedBy(userId);
					dutyPrepayInfoDTO.setUpdatedBy(userId);
					dutyPrepayInfoDTO.setIdAhcsDutyPrepayInfo(UuidUtil.getUUID());
					dutyPrepayInfoDTO.setReportNo(prePayCaseVO.getReportNo());
					dutyPrepayInfoDTO.setCaseTimes(prePayCaseVO.getCaseTimes());
					dutyPrepayInfoDTO.setSubTimes(subTimes);
					dutyPrepayInfoDTO.setPolicyNo(policy.getPolicyNo());
					dutyPrepayInfoDTO.setPlanCode(plan.getPlanCode());
					dutyPrepayInfoDTO.setPlanName(plan.getPlanName());
					dutyPrepayInfoDTO.setDutyCode(duty.getDutyCode());
					dutyPrepayInfoDTO.setDutyPayAmount(duty.getDutyPreAmount());
					dutyPrepayInfoDTO.setIdAhcsPrepayInfo(prePayCaseVO.getIdAhcsPrepayInfo());
					dutyPrepayInfoDTO.setCaseNo(policy.getCaseNo());
					dutyPrepayInfoDTO.setPolicyPrepayDutyDetailDTOList(duty.getPrepayDutyDetailDTOList());
					resultList.add(dutyPrepayInfoDTO);
				}
			}
			policy.setPolicyPayAmount(policyPayAmount);
			prePayAmount = prePayAmount.add(policyPayAmount);

		}
		prePayCaseVO.setPrePayAmount(prePayAmount);
		return resultList;
	}

	private void buildPolicyFeeList(PrePayCaseVO prePayCaseVO ,Integer subTimes){
		List<PrePaymentVO> policyFeeList = feePayService.getPolicyPrePayFeeList(prePayCaseVO.getReportNo(),prePayCaseVO.getCaseTimes(),subTimes);
		BigDecimal preFeeAmount = null;
		if(ListUtils.isEmptyList(policyFeeList)){
			prePayCaseVO.setPrePaymentFeeList(new ArrayList<>());
		}else{
			prePayCaseVO.setPrePaymentFeeList(policyFeeList);
			preFeeAmount = policyFeeList.stream().map(PrePaymentVO::getPaymentAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
		}
		preFeeAmount = preFeeAmount == null ? BigDecimal.ZERO:preFeeAmount;
		prePayCaseVO.setPreFeeAmount(preFeeAmount);
		prePayCaseVO.setPreTotalAmount(prePayCaseVO.getPrePayAmount().add(preFeeAmount));

	}

	private List<PaymentItemDTO> buildPaymemtItem(PrePayCaseVO prePayCaseVO ,String userId,Integer subTimes){
		if(!BigDecimalUtils.compareBigDecimalPlus(prePayCaseVO.getPrePayAmount(),BigDecimal.ZERO)){
			return new ArrayList<>();
		}
		List<PaymentItemDTO> resultList = new ArrayList<>();

		//赔款生成支付项
		// if(prePayCaseVO.getPaymentInfoList().size() > 1){
			//多个账号按前端传入生成
			List<PrePaymentVO> payList = Optional.ofNullable(prePayCaseVO.getPrePaymentList()).orElse(new ArrayList<>());

			List<String> distinctList = payList.stream().map(k -> k.getPolicyNo()+k.getIdClmPaymentInfo()).distinct().collect(Collectors.toList());
			if(payList.size() != distinctList.size()){
				throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"赔款支付信息重复，请合并同一保单同一领款人的金额");
			}
			Map<String,List<PrePolicyVO>> policyMap = prePayCaseVO.getPrePolicyList().stream().collect(Collectors.groupingBy(PrePolicyVO::getPolicyNo));
			Map<String,String> compensateNoMap = getCompensateNoMap(payList,policyMap);
			BigDecimal payAmout = BigDecimal.ZERO;
			Map<String,PaymentInfoDTO> paymentInfoMap = prePayCaseVO.getPaymentInfoList().stream().collect(Collectors.toMap(PaymentInfoDTO::getIdClmPaymentInfo,dto -> dto));
			for (PrePaymentVO vo : payList) {
				if(vo.getPaymentAmount() == null){
					vo.setPaymentAmount(BigDecimal.ZERO);
				}
				payAmout = payAmout.add(vo.getPaymentAmount());

				PaymentInfoDTO paymentInfoDTO = paymentInfoMap.get(vo.getIdClmPaymentInfo());
				if(null == paymentInfoDTO){
					LogUtil.audit("支付信息ID不存在id="+vo.getIdClmPaymentInfo());
					throw new GlobalBusinessException("赔款支付信息错误,请重新选择");
				}
				PaymentItemDTO itemDTO = new PaymentItemDTO();
				BeanUtils.copyProperties(paymentInfoDTO,itemDTO);
				itemDTO.setCreatedBy(userId);
				itemDTO.setUpdatedBy(userId);
				itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
				itemDTO.setPolicyNo(vo.getPolicyNo());
				itemDTO.setCaseNo(vo.getCaseNo());
				itemDTO.setReportNo(prePayCaseVO.getReportNo());
				itemDTO.setCaseTimes(prePayCaseVO.getCaseTimes());
				itemDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
				itemDTO.setSubTimes(subTimes);
				itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PREPAY);
				itemDTO.setIdClmPaymentInfo(paymentInfoDTO.getIdClmPaymentInfo());
				itemDTO.setCollectPaySign(SettleConst.COLLECTION);
				itemDTO.setPaymentAmount(vo.getPaymentAmount());
				itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);
				itemDTO.setCollectPayApproach(CollectPayApproachEnum.REAL_TIME_PAYMENT.getType());
				itemDTO.setMergeSign(SettleConst.NOT_MERGE);
				itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
				itemDTO.setIsCoinsure(ConstValues.NO);
				itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
				itemDTO.setCompensateNo(compensateNoMap.get(vo.getPolicyNo()));
				itemDTO.setCoinsuranceMark(vo.getCoinsuranceMark());
				itemDTO.setAcceptInsuranceFlag(vo.getAcceptInsuranceFlag());
				itemDTO.setCoinsuranceCompanyCode(vo.getCoinsuranceCompanyCode());
				itemDTO.setCoinsuranceCompanyName(vo.getCoinsuranceCompanyCode());
				itemDTO.setCoinsuranceRatio(vo.getCoinsuranceRatio());
				itemDTO.setIsFullPay(vo.getIsFullPay());
				itemDTO.setCoinsuranceActualAmount(vo.getCoinsuranceActualAmount());
				itemDTO.setChgCoinsuranceActualAmount(vo.getCoinsuranceActualAmount());
				itemDTO.setChgPaidAmount(vo.getCoinsuranceActualAmount());
				resultList.add(itemDTO);
			}

			if(!BigDecimalUtils.isEqual(payAmout,prePayCaseVO.getPrePayAmount())){
				LogUtil.audit("赔款支付金额={}，预赔赔款合计金额={}",payAmout,prePayCaseVO.getPrePayAmount());
				throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"赔款支付金额与预赔赔款合计金额不等，请重新分配");
			}
		/*} else{
			//单个账号根据预赔保单生成
			PaymentInfoDTO paymentInfoDTO = prePayCaseVO.getPaymentInfoList().get(0);
			for(PrePolicyVO policy : prePayCaseVO.getPrePolicyList()){
				if(!BigDecimalUtils.compareBigDecimalPlus(policy.getPolicyPayAmount(),BigDecimal.ZERO)){
					continue;
				}
				PaymentItemDTO itemDTO = new PaymentItemDTO();
				BeanUtils.copyProperties(paymentInfoDTO,itemDTO);
				itemDTO.setCreatedBy(userId);
				itemDTO.setUpdatedBy(userId);
				itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
				itemDTO.setPolicyNo(policy.getPolicyNo());
				itemDTO.setCaseNo(policy.getCaseNo());
				itemDTO.setReportNo(prePayCaseVO.getReportNo());
				itemDTO.setCaseTimes(prePayCaseVO.getCaseTimes());
				itemDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
				itemDTO.setSubTimes(subTimes);
				itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PREPAY);
				itemDTO.setIdClmPaymentInfo(paymentInfoDTO.getIdClmPaymentInfo());
				itemDTO.setCollectPaySign(SettleConst.COLLECTION);
				itemDTO.setPaymentAmount(policy.getPolicyPayAmount());
				itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);
				itemDTO.setCollectPayApproach(CollectPayApproachEnum.REAL_TIME_PAYMENT.getType());
				itemDTO.setMergeSign(SettleConst.NOT_MERGE);
				itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
				itemDTO.setIsCoinsure(ConstValues.NO);
				itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
				itemDTO.setCompensateNo(ConstValues.YES+commonService.generateNo( NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO,policy.getDepartmentCode()));
				resultList.add(itemDTO);
			}
		}*/

		//分摊共保支付项
		//resultList.addAll(buildCoinsurePaymemtItem(prePayCaseVO,userId,subTimes));
		resultList.addAll(buildCoinsurePaymemtItem(prePayCaseVO.getReportNo(), resultList));
		return resultList;
	}

	private List<PaymentItemDTO> buildFeePaymemtItem(PrePayCaseVO prePayCaseVO ,String userId,Integer subTimes){
		if(!BigDecimalUtils.compareBigDecimalPlus(prePayCaseVO.getPreFeeAmount(),BigDecimal.ZERO)){
			return new ArrayList<>();
		}
		Boolean isMainCoinsFlag = coinsureService.isMainCoinsureFlag(prePayCaseVO.getReportNo());
		List<PaymentItemDTO> resultList = new ArrayList<>();
		Map<String,List<PrePolicyVO>> policyMap = prePayCaseVO.getPrePolicyList().stream().collect(Collectors.groupingBy(PrePolicyVO::getPolicyNo));
		LogUtil.audit("policyMap={}",JSON.toJSONString(policyMap));
		List<PrePaymentVO> prePaymentFeeList = Optional.ofNullable(prePayCaseVO.getPrePaymentFeeList()).orElse(new ArrayList<>());
		LogUtil.audit("prePeymentFeeList={}",JSON.toJSONString(prePayCaseVO.getPrePaymentFeeList()));
		Map<String,String> compensateNoMap = getCompensateNoMap(prePaymentFeeList,policyMap);
		Map<String, List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(prePayCaseVO.getReportNo());

		//费用生成支付项
		if(prePayCaseVO.getPaymentFeeList().size() > 1){
			//多个账号按前端传入生成

			BigDecimal feeAmout = BigDecimal.ZERO;
			Map<String,PaymentInfoDTO> paymentInfoMap = prePayCaseVO.getPaymentFeeList().stream().collect(Collectors.toMap(PaymentInfoDTO::getIdClmPaymentInfo,dto -> dto));
			for (PrePaymentVO vo : prePaymentFeeList) {
				feeAmout = feeAmout.add(vo.getPaymentAmount());
				PaymentInfoDTO paymentInfoDTO = paymentInfoMap.get(vo.getIdClmPaymentInfo());
				PaymentItemDTO itemDTO = new PaymentItemDTO();
				BeanUtils.copyProperties(paymentInfoDTO,itemDTO);
				itemDTO.setCreatedBy(userId);
				itemDTO.setUpdatedBy(userId);
				itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
				itemDTO.setPolicyNo(vo.getPolicyNo());
				itemDTO.setCaseNo(vo.getCaseNo());
				itemDTO.setReportNo(prePayCaseVO.getReportNo());
				itemDTO.setCaseTimes(prePayCaseVO.getCaseTimes());
				itemDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
				itemDTO.setSubTimes(subTimes);
				itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_FEE_PREPAY);
				itemDTO.setIdClmPaymentInfo(paymentInfoDTO.getIdClmPaymentInfo());
				itemDTO.setCollectPaySign(SettleConst.COLLECTION);
				itemDTO.setPaymentAmount(vo.getPaymentAmount());
				itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);
				itemDTO.setCollectPayApproach(CollectPayApproachEnum.REAL_TIME_PAYMENT.getType());
				itemDTO.setMergeSign(SettleConst.NOT_MERGE);
				itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
				itemDTO.setIsCoinsure(ConstValues.NO);
				itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
				itemDTO.setCompensateNo(compensateNoMap.get(vo.getPolicyNo()));
				itemDTO.setFeeType(vo.getFeeType());
				itemDTO.setIsFullPay(vo.getIsFullPay());
				itemDTO.setIdAhcsFeePay(vo.getIdAhcsFeePay());

				List<CoinsureDTO> coinsureList = coinsMap.get(vo.getPolicyNo());
				if (CollectionUtils.isNotEmpty(coinsureList)) {
					CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
					if (Objects.nonNull(coinsureDTO)) {
						itemDTO.setCoinsuranceMark(BaseConstant.STRING_1);
						itemDTO.setAcceptInsuranceFlag(coinsureDTO.getAcceptInsuranceFlag());
						itemDTO.setCoinsuranceCompanyCode(coinsureDTO.getReinsureCompanyCode());
						itemDTO.setCoinsuranceCompanyName(coinsureDTO.getReinsureCompanyName());
						itemDTO.setCoinsuranceRatio(coinsureDTO.getReinsureScale());
						BigDecimal coinsuranceActualAmount = itemDTO.getPaymentAmount();
						if("1".equals(itemDTO.getIsFullPay()) && isMainCoinsFlag){
							coinsuranceActualAmount = itemDTO.getPaymentAmount().multiply(coinsureDTO.getReinsureScale().divide(new BigDecimal(100))).setScale(2);
						}
						itemDTO.setCoinsuranceActualAmount(coinsuranceActualAmount);
						itemDTO.setChgCoinsuranceActualAmount(coinsuranceActualAmount);
						itemDTO.setChgPaidAmount(coinsuranceActualAmount);
					}
				}

				resultList.add(itemDTO);
			}

			if(!BigDecimalUtils.isEqual(feeAmout,prePayCaseVO.getPreFeeAmount())){
				LogUtil.audit("支付费用总金额={}，录入费用总金额={}",feeAmout,prePayCaseVO.getPrePayAmount());
				throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"支付费用总金额与录入费用总金额不等，请重新分配");
			}
		}
		else{
			//单个账号根据预赔保单生成
			PaymentInfoDTO paymentInfoDTO = prePayCaseVO.getPaymentFeeList().get(0);
			for(PrePaymentVO vo : prePaymentFeeList){
				PaymentItemDTO itemDTO = new PaymentItemDTO();
				BeanUtils.copyProperties(paymentInfoDTO,itemDTO);
				itemDTO.setCreatedBy(userId);
				itemDTO.setUpdatedBy(userId);
				itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
				itemDTO.setPolicyNo(vo.getPolicyNo());
				itemDTO.setCaseNo(vo.getCaseNo());
				itemDTO.setReportNo(prePayCaseVO.getReportNo());
				itemDTO.setCaseTimes(prePayCaseVO.getCaseTimes());
				itemDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
				itemDTO.setSubTimes(subTimes);
				itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_FEE_PREPAY);
				itemDTO.setIdClmPaymentInfo(paymentInfoDTO.getIdClmPaymentInfo());
				itemDTO.setCollectPaySign(SettleConst.COLLECTION);
				itemDTO.setPaymentAmount(vo.getPaymentAmount());
				itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);
				itemDTO.setCollectPayApproach(CollectPayApproachEnum.REAL_TIME_PAYMENT.getType());
				itemDTO.setMergeSign(SettleConst.NOT_MERGE);
				itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
				itemDTO.setIsCoinsure(ConstValues.NO);
				itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
				itemDTO.setCompensateNo(compensateNoMap.get(vo.getPolicyNo()));
				itemDTO.setFeeType(vo.getFeeType());
				itemDTO.setIsFullPay(vo.getIsFullPay());
				itemDTO.setIdAhcsFeePay(vo.getIdAhcsFeePay());

				List<CoinsureDTO> coinsureList = coinsMap.get(vo.getPolicyNo());
				if (CollectionUtils.isNotEmpty(coinsureList)) {
					CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
					if (Objects.nonNull(coinsureDTO)) {
						itemDTO.setCoinsuranceMark(BaseConstant.STRING_1);
						itemDTO.setAcceptInsuranceFlag(coinsureDTO.getAcceptInsuranceFlag());
						itemDTO.setCoinsuranceCompanyCode(coinsureDTO.getReinsureCompanyCode());
						itemDTO.setCoinsuranceCompanyName(coinsureDTO.getReinsureCompanyName());
						itemDTO.setCoinsuranceRatio(coinsureDTO.getReinsureScale());
						BigDecimal coinsuranceActualAmount = itemDTO.getPaymentAmount();
						if("1".equals(itemDTO.getIsFullPay()) && isMainCoinsFlag){
							coinsuranceActualAmount = itemDTO.getPaymentAmount().multiply(coinsureDTO.getReinsureScale().divide(new BigDecimal(100))).setScale(2);
						}
						itemDTO.setCoinsuranceActualAmount(coinsuranceActualAmount);
						itemDTO.setChgCoinsuranceActualAmount(coinsuranceActualAmount);
						itemDTO.setChgPaidAmount(coinsuranceActualAmount);
					}
				}

				resultList.add(itemDTO);
			}
		}
		return resultList;
	}

	private Map<String,String> getCompensateNoMap(List<PrePaymentVO>prePaymentList,Map<String, List<PrePolicyVO>> policyMap){
		Map<String,List<PrePaymentVO>> prePayMap = prePaymentList.stream().collect(Collectors.groupingBy(PrePaymentVO::getPolicyNo));
		LogUtil.audit("prePayMap={},policyMap={}",JSON.toJSONString(prePayMap),JSON.toJSONString(policyMap));
		Map<String,String> compensateNoMap = new HashMap<>();
		prePayMap.forEach((k,v)->{
			compensateNoMap.put(k,ConstValues.YES+commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO,
					policyMap.get(k).get(0).getDepartmentCode()));
			});
		return compensateNoMap;
	}

	private void checkPrePayPaymentInfo(PrePayCaseVO prePayCaseVO){
		PaymentInfoDTO param = new PaymentInfoDTO();
		param.setReportNo(prePayCaseVO.getReportNo());
		param.setCaseTimes(prePayCaseVO.getCaseTimes());
		List<PaymentInfoDTO> paymentInfoList = paymentInfoService.getPaymentInfo(param);
		if(ListUtils.isEmptyList(paymentInfoList)){
			throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"请录入支付信息");
		}
		//赔款支付信息
		if(BigDecimalUtils.compareBigDecimalPlus(prePayCaseVO.getPrePayAmount(),BigDecimal.ZERO)){
			List<PaymentInfoDTO> payList = paymentInfoList.stream().filter(dto -> PaymentInfoTypeEnum.INDEMNITY.getType().equals(
					dto.getPaymentUsage())).collect(Collectors.toList());
			if(ListUtils.isEmptyList(payList)){
				throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"请录入赔款支付信息");
			}

			if(payList.size() > 1){
				if(ListUtils.isEmptyList(prePayCaseVO.getPrePaymentList())){
					throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"请分配赔款支付信息");
				}else {
					Set<String> payIdSet = payList.stream().map(PaymentInfoDTO::getIdClmPaymentInfo).collect(Collectors.toSet());
					for (PrePaymentVO vo : prePayCaseVO.getPrePaymentList()) {
						if(payIdSet.add(vo.getIdClmPaymentInfo())){
							LogUtil.audit("赔款支付信息id不存在payIdSet="+JSON.toJSONString(payIdSet)+",id="+vo.getIdClmPaymentInfo());
							throw new GlobalBusinessException("赔款支付信息错误,请重新选择");
						}
						if(StringUtils.isEmptyStr(vo.getPolicyNo())){
							throw new GlobalBusinessException("赔款支付信息错误,保单号不能为空");
						}
					}

					checkPrePayAmountIsEqual(prePayCaseVO.getPrePolicyList(),prePayCaseVO.getPrePaymentList());
				}
			}
			prePayCaseVO.setPaymentInfoList(payList);
		}

		//费用支付信息
		if(BigDecimalUtils.compareBigDecimalPlus(prePayCaseVO.getPreFeeAmount(),BigDecimal.ZERO)){
			List<PaymentInfoDTO> feeList = paymentInfoList.stream().filter(dto -> PaymentInfoTypeEnum.COST.getType().equals(
					dto.getPaymentUsage())).collect(Collectors.toList());
			if(ListUtils.isEmptyList(feeList)){
				throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"请录入费用支付信息");
			}
			if(feeList.size() > 1){
				if(ListUtils.isEmptyList(prePayCaseVO.getPrePaymentFeeList())){
					throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"请分配费用支付信息");
				}else {
					Set<String> payIdSet = feeList.stream().map(PaymentInfoDTO::getIdClmPaymentInfo).collect(Collectors.toSet());
					for (PrePaymentVO vo : prePayCaseVO.getPrePaymentFeeList()) {
						if(payIdSet.add(vo.getIdClmPaymentInfo())){
							LogUtil.audit("费用支付信息id不存在payIdSet="+JSON.toJSONString(payIdSet)+",id="+vo.getIdClmPaymentInfo());
							throw new GlobalBusinessException("费用支付信息错误,请重新选择");
						}
					}
				}
			}
			prePayCaseVO.setPaymentFeeList(feeList);
		}

	}

	private List<PaymentItemDTO> buildCoinsurePaymemtItem(String reportNo, List<PaymentItemDTO> paymentItemList) {
		List<PaymentItemDTO> resultList = new ArrayList<>();
		Map<String, List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(reportNo);
		if(CollectionUtils.isEmpty(paymentItemList) || coinsMap.isEmpty()){
			return resultList;
		}

		Map<String, List<CoinsureRecordDTO>> coinsureRecordMap = coinsureService.getCoinsureRecordByReportNo(reportNo);
		for (PaymentItemDTO paymentItem : paymentItemList) {
			List<CoinsureDTO> coinsureList = coinsMap.get(paymentItem.getPolicyNo());
			if (CollectionUtils.isEmpty(coinsureList)) {
				continue;
			}

//			if (coinsureRecordMap.containsKey(paymentItem.getPolicyNo()) && !BaseConstant.STRING_1.equals(paymentItem.getIsFullPay())) {
//				throw new GlobalBusinessException(String.format("当前案件该保单已经做过全额给付，不能再选择非全额给付，保单号：%s", paymentItem.getPolicyNo()));
//			}

			if (Objects.isNull(paymentItem.getCoinsuranceActualAmount())) {
				throw new GlobalBusinessException(String.format("共保实付金额不能为空，保单号：%s, 户名：%s", paymentItem.getPolicyNo(), paymentItem.getClientName()));
			}

			CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
			// 从共没有代付
			if (Objects.isNull(coinsureDTO) || BaseConstant.STRING_2.equals(coinsureDTO.getCoinsuranceType())) {
				if (!BaseConstant.STRING_1.equals(paymentItem.getIsFullPay())) {
					throw new GlobalBusinessException(String.format("从共全额给付不能选择否，保单号：%s, 户名：%s", paymentItem.getPolicyNo(), paymentItem.getClientName()));
				}

				if (paymentItem.getCoinsuranceActualAmount().compareTo(paymentItem.getPaymentAmount()) != 0) {
					throw new GlobalBusinessException(String.format("我司承担金额错误，保单号：%s, 户名：%s", paymentItem.getPolicyNo(), paymentItem.getClientName()));
				}
				continue;
			}

			coinsureList = coinsureList.stream().sorted(Comparator.comparing(CoinsureDTO::getCompanyFlag)).collect(Collectors.toList());
			BigDecimal paymentAmount = paymentItem.getPaymentAmount();
			BigDecimal sumAmount = BigDecimal.ZERO;
			for (int i = 0; i < coinsureList.size(); i++) {
				CoinsureDTO coins = coinsureList.get(i);
				BigDecimal coinsuranceActualAmount;

				if (i == coinsureList.size() - 1) {
					coinsuranceActualAmount = paymentAmount.subtract(sumAmount);
					if (coinsuranceActualAmount.compareTo(paymentItem.getCoinsuranceActualAmount()) != 0) {
						log.info("我司承担金额错误, 保单号: {}, 户名: {}, 入参金额: {}, 计算金额: {}", paymentItem.getPolicyNo(), paymentItem.getClientName(), paymentItem.getCoinsuranceActualAmount(), coinsuranceActualAmount);
						throw new GlobalBusinessException(String.format("我司承担金额错误，保单号：%s, 户名：%s", paymentItem.getPolicyNo(), paymentItem.getClientName()));
					}
				} else {
					coinsuranceActualAmount = paymentAmount.multiply(nvl(coins.getReinsureScale(), 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
					sumAmount = sumAmount.add(coinsuranceActualAmount);
				}

				if (i < coinsureList.size() - 1 && "1".equals(paymentItem.getIsFullPay())) {
					PaymentItemDTO itemDTO = new PaymentItemDTO();
					BeanUtils.copyProperties(paymentItem, itemDTO);
					itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
					itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REINSURE_PAY);
					itemDTO.setPaymentAmount(coinsuranceActualAmount);
					itemDTO.setCoinsuranceMark(BaseConstant.STRING_1);
					itemDTO.setAcceptInsuranceFlag(coins.getAcceptInsuranceFlag());
					itemDTO.setCoinsuranceCompanyCode(coins.getReinsureCompanyCode());
					itemDTO.setCoinsuranceCompanyName(coins.getReinsureCompanyName());
					itemDTO.setCoinsuranceRatio(coins.getReinsureScale());
					itemDTO.setCoinsuranceActualAmount(coinsuranceActualAmount);
					itemDTO.setIsCoinsure(ConstValues.YES);
					resultList.add(itemDTO);
				}
			}
		}
		return resultList;
	}

	/*private List<PaymentItemDTO> buildCoinsurePaymemtItem(PrePayCaseVO prePayCaseVO ,String userId,Integer subTimes){
		Map<String,List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(prePayCaseVO.getReportNo());
		if(coinsMap.size() < 1){
			return new ArrayList<>();
		}
		LogUtil.audit("预赔共保coninsMap={}",JSON.toJSONString(coinsMap));
		LogUtil.audit("预赔保单prePolicyList={}",JSON.toJSONString(prePayCaseVO.getPrePolicyList()));
		List<PaymentItemDTO> resultList = new ArrayList<>();
		for(PrePolicyVO policy : prePayCaseVO.getPrePolicyList()){
			if(!BigDecimalUtils.compareBigDecimalPlus(policy.getPolicyPayAmount(),BigDecimal.ZERO)){
				continue;
			}
			List<CoinsureDTO> coinsList = coinsMap.getOrDefault(policy.getPolicyNo(),new ArrayList<>());
			if(coinsList.size() < 1){
				continue;
			}
			coinsList = coinsList.stream().filter(coin -> !"1".equals(coin.getAcceptInsuranceFlag())).collect(Collectors.toList());
			//共保总比例
			BigDecimal coinTotalPer = BigDecimal.ZERO;
			for (CoinsureDTO coinsureDTO : coinsList) {
				if(coinsureDTO.getReinsureScale() == null){
					coinsureDTO.setReinsureScale(BigDecimal.ZERO);
				}
				coinTotalPer = coinTotalPer.add(coinsureDTO.getReinsureScale());
			}
			//共保总金额
			BigDecimal coinTotalAmt = BigDecimalUtils.percentage(policy.getPolicyPayAmount(),coinTotalPer);
			BigDecimal addAmt = BigDecimal.ZERO;
			int size = coinsList.size();
			for(int i=0;i<size;i++){
				CoinsureDTO coins = coinsList.get(i);
				BigDecimal coinAmt = BigDecimal.ZERO;
				if(size == 1){
					coinAmt = BigDecimalUtils.percentage(policy.getPolicyPayAmount(),coins.getReinsureScale());
				}else{
					if(i < size -1 ){
						coinAmt = BigDecimalUtils.percentage(policy.getPolicyPayAmount(),coins.getReinsureScale());
						addAmt = addAmt.add(coinAmt);
					}else{
						coinAmt = coinTotalAmt.subtract(addAmt);
					}
				}

				PaymentItemDTO itemDTO = new PaymentItemDTO();
				itemDTO.setClientName(coins.getReinsureCompanyName());
				if(itemDTO.getClientName() == null){
					itemDTO.setClientName(coins.getReinsureCompanyCode());
				}
				itemDTO.setCreatedBy(userId);
				itemDTO.setUpdatedBy(userId);
				itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
				itemDTO.setPolicyNo(policy.getPolicyNo());
				itemDTO.setCaseNo(policy.getCaseNo());
				itemDTO.setReportNo(prePayCaseVO.getReportNo());
				itemDTO.setCaseTimes(prePayCaseVO.getCaseTimes());
				itemDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
				itemDTO.setSubTimes(subTimes);
				itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REINSURE_PAY);
				itemDTO.setIdClmPaymentInfo(coins.getReinsureCompanyCode());
				itemDTO.setCollectPaySign(SettleConst.COLLECT_SIGN);
				itemDTO.setPaymentAmount(coinAmt);
				itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);
				itemDTO.setCollectPayApproach(CollectPayApproachEnum.REAL_TIME_PAYMENT.getType());
				itemDTO.setMergeSign(SettleConst.NOT_MERGE);
				itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
				itemDTO.setIsCoinsure(ConstValues.YES);
				itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
				itemDTO.setCompensateNo(ConstValues.YES+commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO, policy.getDepartmentCode()));
				resultList.add(itemDTO);
			}
		}
		return resultList;
	}*/

	@Override
	@Transactional
	public void sendPrePayApprove(PrePayInfoDTO prePayInfoDTO,List<String> msgList) {
		Integer subTimes = prePayMapper.getCurrentApprovedSubTimes(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes());
		LogUtil.audit("subTimes={}",subTimes);
		String userId = WebServletContext.getUserId();
		Boolean flag = true;

		PreApproveVO preApproveVO = null;
		prePayInfoDTO.setPrepayBigType(prePayMapper.getPreBigType(prePayInfoDTO.getPrePayInfoId()));
		boolean agree = ConstValues.AUDIT_AGREE_CODE.equals(prePayInfoDTO.getVerifyOptions());
		if(agree){
			String taskId = taskInfoMapper.getLatestTaskId(prePayInfoDTO.getReportNo(),
					prePayInfoDTO.getCaseTimes(),
					BpmConstants.OC_PREPAY_REVIEW);
			flag = checkPermission(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),taskId);
			//预赔审批权限不够插入预赔表
			if(!flag){
				savePrePayInfo(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),userId);
				msgList.add("提交成功，待上级审批人继续处理");
				LogUtil.audit("预赔审批权限不足！");
			}
			//同意 写pay表
			preApproveVO = savePolicyPlanDutyPay(prePayInfoDTO,subTimes,userId);
		}else{
			if(!ConstValues.AUDIT_DISAGREE_CODE.equals(prePayInfoDTO.getVerifyOptions())){
				throw new GlobalBusinessException("请选择同意或不同意");
			}
			//不同意 删除支付项、删除费用
			preApproveVO = new PreApproveVO();
			//预赔审批不同意添加消息提醒
			NoticesDTO noticesDTO = new NoticesDTO();
			noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
			noticesDTO.setNoticeSubClass(BpmConstants.NSC_PREPAY_REVIEW);
			noticesDTO.setReportNo(prePayInfoDTO.getReportNo());
			noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
			noticesDTO.setCaseTimes(prePayInfoDTO.getCaseTimes());
			TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(prePayInfoDTO.getReportNo(), prePayInfoDTO.getCaseTimes(), BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);
			if (taskInfoDTO!=null){
				noticeService.saveNotices(noticesDTO,taskInfoDTO.getAssigner());
			}

		}

		prePayInfoDTO.setUpdatedBy(userId);
		prePayInfoDTO.setApproverBy(userId);
		UserInfoDTO u = null;
		try {
			u = Optional.ofNullable(cacheService.queryUserInfo(userId)).orElse(new UserInfoDTO());
		} catch (Exception e) {
			u = new UserInfoDTO();
		}
		prePayInfoDTO.setApproverName(u.getUserName());
//		prePayInfoDTO.setPrepayBigType(prePayMapper.getPreBigType(prePayInfoDTO.getPrePayInfoId()));
		preApproveVO.setSubTimes(subTimes);
		preApproveVO.setPrePayInfoDTO(prePayInfoDTO);

		prePayTransactionService.savePrePayApprove(preApproveVO,flag);

		if(agree){
			//权限满足
			if(flag){
				//避免异步获取不到事务内更新的数据，在本次事务提交之后，再生成共保摊回通知书和摊回记录
				TransactionSynchronizationManager.registerSynchronization(
						new TransactionSynchronization() {
							@Override
							public void afterCommit() {
								//生成共保通知书并上传影像 异步,生成共保摊回记录
								UserInfoDTO userInfoDTO = WebServletContext.getUser();
								printCoreService.saveCoinsFileAsync(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),CLAIM_TYPE_PRE_PAY,userInfoDTO,prePayInfoDTO.getSubTimes());
								coinsuranceService.addRecoveryInfo(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),CLAIM_TYPE_PRE_PAY,subTimes);;
							}
						}
				);
				//  预赔送收付费 同意的情况下 preApproveVO肯定有数据
				sendPaySys(preApproveVO);
				//预赔调用gloabl理算回流
				globalPolicyService.sendReturnSettleToGlobal(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),"1",true);
				if("3".equals(prePayInfoDTO.getPrepayBigType())){
					//重新理算
					policyPayService.reCalculateSettleAmount(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes());
				}
				//打印共保通知书
//				printCoreService.saveCoinsFileAsync(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),"2");
	//			paymentItemService.updateCompensateNoPaymentItem(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),"2");
				// 同步第三方系统老核心
	//			mqProducerPreCompensateService.syncProducerPreCompensateLink(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes());
			} else {
				savePrePayTaskInfo(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),prePayInfoDTO.getSelectedUserId());
			}
		}

	}
	@Override
	public Boolean checkPermission(String reportNo,Integer caseTimes,String taskId) {
		Boolean flag = false;
		if(StringUtils.isEmptyStr(taskId)
			|| StringUtils.isEmptyStr(reportNo)
				|| caseTimes == null){
			LogUtil.audit("校验权限参数错误！reportNo:{},caseTimes:{},taskId:{}",reportNo,caseTimes,taskId);
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		LogUtil.audit("当前任务id={}",taskId);
		TaskInfoDTO taskDto = taskInfoService.getTaskDtoByTaskId(taskId);
		if(taskDto == null){
			LogUtil.audit("taskId查任务为空");
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		String userId = WebServletContext.getUserId();
		String taskDefinitionBpmKey = taskDto.getTaskDefinitionBpmKey();
		String typeCode = null;
		if(BpmConstants.OC_REGISTER_REVIEW.equals(taskDefinitionBpmKey)
				|| BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskDefinitionBpmKey)){
			typeCode = Constants.PERMISSION_REGIST;
		} else {
			typeCode = Constants.PERMISSION_VERIFY;
		}
		Integer userGrade = permissionService.getUserGrade(typeCode,taskDto.getDepartmentCode(),userId);
		if(userGrade == null){
			throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"你未配置案件机构的核赔权限");
		}
		if(taskDto.getTaskGrade() == null){
			throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"案件预赔权限为空");
		}
		if(userGrade >= taskDto.getTaskGrade()){
			flag = true;
		}
		return flag;
	}
	private void savePrePayTaskInfo(String reportNo,Integer caseTimes,String selectedUserId) {
		String taskId = taskInfoMapper.getLatestTaskId(reportNo,
				caseTimes,BpmConstants.OC_PREPAY_REVIEW);
		LogUtil.audit("预赔审批当前任务id={}",taskId);
		String userId = WebServletContext.getUserId();
		String managerUserId = null;
		String managerUserName = null;
		if(!StringUtils.isEmptyStr(selectedUserId)){
			String [] parts = selectedUserId.split("-",2);
			managerUserId = parts[0];
			managerUserName = parts[1];
		}
		TaskInfoDTO taskDto = taskInfoService.getTaskDtoByTaskId(taskId);
		if(taskDto == null){
			LogUtil.audit("taskId查任务为空");
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		Integer userGrade = permissionService.getUserGrade(Constants.PERMISSION_VERIFY,taskDto.getDepartmentCode(),userId);
		if(userGrade < taskDto.getTaskGrade()){
			//权限不满足，继续找高一个级别的人处理
			String comCode = permissionUserMapper.getComCodeByUserId(managerUserId);
			userGrade++;
			TaskInfoDTO startTask = new TaskInfoDTO();
			BeanUtils.copyProperties(taskDto,startTask);
			startTask.setTaskId(UuidUtil.getUUID());
			startTask.setTaskDefinitionBpmKey(BpmConstants.OC_PREPAY_REVIEW);
			startTask.setAssigneeTime(new Date());
			startTask.setStatus(BpmConstants.TASK_STATUS_PENDING);
			startTask.setCreatedBy(userId);
			startTask.setUpdatedBy(userId);
			startTask.setAssigner(managerUserId);
			startTask.setAssigneeName(managerUserName);
			startTask.setApplyer(taskDto.getAssigner());
			startTask.setApplyerName(taskDto.getAssigneeName());
			startTask.setPreTaskId(taskDto.getTaskId());
			startTask.setAuditGrade(userGrade);
			startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
			startTask.setDepartmentCode(comCode);
			taskInfoMapper.addTaskInfo(startTask);

			//操作记录
			operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_PREPAY_REVIEW
					, "同意,", " 待上级审批人继续处理");
		}
	}

	private void savePrePayInfo(String reportNo,Integer caseTimes,String userId) {
		//查询本次处理中的预赔信息
		PrePayInfoDTO prePayInfoDTO1 = prePayMapper.getPrePayWaitApprove(reportNo,caseTimes);

		PrePayCaseVO prePayCaseVO = new PrePayCaseVO();
		prePayCaseVO.setIdAhcsPrepayInfo(UuidUtil.getUUID());
		prePayCaseVO.setReportNo(reportNo);
		prePayCaseVO.setCaseTimes(caseTimes);
		prePayCaseVO.setPreTotalAmount(prePayInfoDTO1.getPrePayAmount());
		prePayCaseVO.setPrePayAmount(prePayInfoDTO1.getPrePayAmount());
		prePayCaseVO.setPrePayCause(prePayInfoDTO1.getPrePayCause());
		prePayCaseVO.setApplyRemark(prePayInfoDTO1.getApplyRemark());
		PrePayInfoDTO prePayInfoDTONew = buildPrePayInfoDTO(prePayCaseVO,userId);
		prePayInfoDTONew.setPrepayBigType(prePayInfoDTO1.getPrepayBigType());
		prePayInfoDTONew.setTaskDefinitionBpmKey(prePayInfoDTO1.getTaskDefinitionBpmKey());
		prePayInfoDTONew.setSubTimes(prePayInfoDTO1.getSubTimes());
		prePayMapper.savePrePayInfo(prePayInfoDTONew);
	}

	/**
	 * 调用拆分 逻辑把支付项拆分到险种责任
	 * 组装送收费的数据并且送收费
	 * @param preApproveVO
	 */
	private void sendPaySys(PreApproveVO preApproveVO) {
		LogUtil.info("initNoSttlePaymentItem1");
		PrePayInfoDTO prePayInfoDTO = preApproveVO.getPrePayInfoDTO();
		Integer subTimes = preApproveVO.getSubTimes();
		// 查理预赔的赔付金额
		List<PolicyPayDTO> policyPayDTOS;
		if (CollectionUtils.isEmpty(preApproveVO.getAddPolicyPayList())){
			policyPayDTOS = preApproveVO.getUpdatePolicyPayList();
		} else {
			policyPayDTOS = preApproveVO.getAddPolicyPayList();
		}

		PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
		paymentItemDTO.setReportNo(prePayInfoDTO.getReportNo());
		paymentItemDTO.setCaseTimes(prePayInfoDTO.getCaseTimes());
		paymentItemDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
		paymentItemDTO.setSubTimes(subTimes);
		List<PaymentItemDTO> allItems = paymentItemService.getPaymentItem(paymentItemDTO);
		//将费用支付项目替换为子表支付项
		Map<String,List<PaymentItemDTO>> feeItemMap = payInfoNoticeThirdPartyCoreSAO.getPaymentItemAndFeeItem(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),Constants.PAYMENT_ITEM_STATUS_10);
		allItems.stream()
				.filter(item->item.getPaymentType().contains("J")).collect(Collectors.toList())
				.forEach(fee ->{
					List<PaymentItemDTO> feeItemList = feeItemMap.get(fee.getIdClmPaymentItem());
					if(feeItemList!=null && feeItemList.size()>0){
						allItems.remove(fee);
						allItems.addAll(feeItemList);
					}
				});
		if (CollectionUtils.isEmpty(allItems)){
			return;
		}
		// 责任拆分 很重要的逻辑 后续应该还要调整
		verifyService.splitPlanAndDuty(policyPayDTOS,allItems);
		payInfoNoticeThirdPartyCoreSAO.noticePayment(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(), null, false, true);
	}


	@Override
	public List<PrePayInfoDTO> getHistoryPrePayApprove(String reportNo, Integer caseTimes) {
		List<PrePayInfoDTO> prelist = prePayMapper.getHistoryPrePayApprove(reportNo,caseTimes);
		if(ListUtils.isEmptyList(prelist)){
			return new ArrayList<>();
		}
		prelist.forEach(dto ->{
			dto.setVerifyName(ConstValues.AUDIT_AGREE_CODE.equals(dto.getVerifyOptions()) ? EndCaseConstValues.AUDIT_AGREE_NAME:EndCaseConstValues.AUDIT_DISAGREE_NAME);
            if(StringUtils.isNotEmpty(dto.getApplyName())){
            	dto.setApplyName(dto.getApplyBy()+"-"+dto.getApplyName());
			}else{
				dto.setApplyName(dto.getApplyBy());
			}
			if(StringUtils.isNotEmpty(dto.getApproverName())){
				dto.setApproverName(dto.getApproverBy()+"-"+dto.getApproverName());
			}else{
				dto.setApproverName(dto.getApproverBy());
			}
			if(dto.getPrePayAmount()==null){
				dto.setPrePayAmount(BigDecimal.ZERO);
			}
			if(dto.getApplyAmount() == null){
				dto.setApplyAmount(BigDecimal.ZERO);
			}
			dto.setApplyFeeAmount(dto.getPrePayAmount().subtract(dto.getApplyAmount()));
		});
		return prelist;
	}

	@Override
	public PrePayInfoDTO getPrePayWaitApprove(String reportNo, Integer caseTimes) {
		return prePayMapper.getPrePayWaitApprove(reportNo,caseTimes);
	}

	private PreApproveVO savePolicyPlanDutyPay(PrePayInfoDTO prePayInfoDTO,Integer subTimes,String userId){
		final String reportNo = prePayInfoDTO.getReportNo();
		final Integer caseTimes = prePayInfoDTO.getCaseTimes();

		DutyPrepayInfoDTO param = new DutyPrepayInfoDTO();
		param.setReportNo(reportNo);
		param.setCaseTimes(caseTimes);
		param.setSubTimes(subTimes);
		List<DutyPrepayInfoDTO> dutyPrepayInfoList = getDutyPrepayInfoList(param);
		if (ListUtils.isEmptyList(dutyPrepayInfoList)) {
			LogUtil.audit("dutyPrepayInfoList={}",JSON.toJSONString(dutyPrepayInfoList));
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		Map<String, DutyPrepayInfoDTO> dutyPrePayMap = dutyPrepayInfoList.stream().collect(Collectors.toMap(k ->k.getPolicyNo()+k.getPlanCode()+k.getDutyCode(),dto->dto));

		List<PolicyPayDTO> policyPayList = policyPayService.getPolicyPayListByReportNoAndCaseTimes(reportNo, caseTimes);
		Map<String, PolicyPayDTO> policyPayMap = new HashMap<>();
		if (ListUtils.isNotEmpty(policyPayList)) {
			policyPayMap = policyPayList.stream().collect(Collectors.toMap(k->k.getPolicyNo(),dto->dto));
		}

		PrePayCaseVO caseVO = getPrePayCaseList(reportNo,caseTimes,subTimes);
		if(ListUtils.isEmptyList(caseVO.getPrePolicyList())){
			LogUtil.audit("prePolicyList={}",JSON.toJSONString(caseVO.getPrePolicyList()));
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		FeePayDTO feePayDTO = new FeePayDTO();
		feePayDTO.setReportNo(reportNo);
		feePayDTO.setCaseTimes(caseTimes);
		feePayDTO.setSubTimes(subTimes);
		List<FeeInfoDTO> feeInfoList = Optional.ofNullable(feePayService.getPrePayFeeAmountByParam(feePayDTO)).orElse(new ArrayList<>());
		Map<String,BigDecimal> feeMap = feeInfoList.stream().collect(Collectors.toMap(k->k.getPolicyNo(),v->v.getFeeAmount()));
		List<DutyPayDTO> dutyPayList = new ArrayList<>();
		List<PlanPayDTO> planPayList = new ArrayList<>();
		List<PolicyPayDTO> updatePolicyPayList = new ArrayList<>();
		List<PolicyPayDTO> addPolicyPayList = new ArrayList<>();
		for (PrePolicyVO policy : caseVO.getPrePolicyList()) {
			List<PlanPayDTO> prePlanList = new ArrayList<>();
			BigDecimal policyPreAmt = BigDecimal.ZERO;
			for (PrePlanVO plan : policy.getPrePlanList()) {
				List<DutyPayDTO> preDutyList = new ArrayList<>();
				BigDecimal planPreAmt = BigDecimal.ZERO;
				for (PreDutyVO duty : plan.getPreDutyList()) {
					BigDecimal dutyPreAmt = dutyPrePayMap.get(policy.getPolicyNo()+plan.getPlanCode()+duty.getDutyCode()).getDutyPayAmount();
					dutyPreAmt = dutyPreAmt == null ? BigDecimal.ZERO : dutyPreAmt;
					DutyPayDTO dutyPayDTO = new DutyPayDTO();
					dutyPayDTO.setIdAhcsDutyPay(UuidUtil.getUUID());
					dutyPayDTO.setSettleAmount(dutyPreAmt);
					dutyPayDTO.setCaseNo(policy.getCaseNo());
					dutyPayDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
					dutyPayDTO.setDutyCode(duty.getDutyCode());
					dutyPayDTO.setCaseTimes(caseTimes);
					dutyPayDTO.setSubTimes(subTimes);
					dutyPayDTO.setCreatedBy(userId);
					dutyPayDTO.setUpdatedBy(userId);
					dutyPayDTO.setPlanCode(plan.getPlanCode());
					dutyPayDTO.setPolicyNo(policy.getPolicyNo());
					dutyPayList.add(dutyPayDTO);
					preDutyList.add(dutyPayDTO);
					planPreAmt = planPreAmt.add(dutyPreAmt);

				}
				PlanPayDTO planPayDTO = new PlanPayDTO();
				planPayDTO.setIdAhcsPlanPay(UuidUtil.getUUID());
				planPayDTO.setCaseNo(policy.getCaseNo());
				planPayDTO.setCaseTimes(caseTimes);
				planPayDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
				planPayDTO.setCreatedBy(userId);
				planPayDTO.setUpdatedBy(userId);
				planPayDTO.setPlanCode(plan.getPlanCode());
				planPayDTO.setSubTimes(subTimes);
				planPayDTO.setSettleAmount(planPreAmt);
				planPayDTO.setDutyPayArr(preDutyList);
				policyPreAmt = policyPreAmt.add(planPreAmt);
				planPayList.add(planPayDTO);
				prePlanList.add(planPayDTO);
			}
			PolicyPayDTO policyPayDTO = new PolicyPayDTO();
			if(policyPayMap.containsKey(policy.getPolicyNo())){
				//更新
				policyPayDTO = policyPayMap.get(policy.getPolicyNo());
				policyPayDTO.setUpdatedBy(userId);
				updatePolicyPayList.add(policyPayDTO);
			}else{
				policyPayDTO.setIdAhcsPolicyPay(UuidUtil.getUUID());
				policyPayDTO.setCreatedBy(userId);
				policyPayDTO.setUpdatedBy(userId);
				policyPayDTO.setCaseNo(policy.getCaseNo());
				policyPayDTO.setCaseTimes(caseTimes);
				policyPayDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
				policyPayDTO.setPolicyNo(policy.getPolicyNo());
				policyPayDTO.setReportNo(reportNo);
				addPolicyPayList.add(policyPayDTO);
			}
			policyPayDTO.setPlanPayArr(prePlanList);
			BigDecimal policyPreFeeAmt = feeMap.getOrDefault(policy.getPolicyNo(),BigDecimal.ZERO);
			policyPayDTO.setSettleAmount(policyPreAmt);
			policyPayDTO.setPolicyPrePay(policyPreAmt.add(BigDecimalUtils.nvl(policyPayDTO.getPolicyPrePay(), 0)));
			policyPayDTO.setPolicyPreFee(policyPreFeeAmt.add(BigDecimalUtils.nvl(policyPayDTO.getPolicyPreFee(), 0)));
			policyPayDTO.setPolicySumPay(policyPreAmt.add(policyPreFeeAmt).add(BigDecimalUtils.nvl(policyPayDTO.getPolicySumPay(), 0)));
			
		}
		PreApproveVO vo = new PreApproveVO();
		vo.setPrePayInfoDTO(prePayInfoDTO);
		vo.setDutyPayList(dutyPayList);
		vo.setPlanPayList(planPayList);
		vo.setAddPolicyPayList(addPolicyPayList);
		vo.setUpdatePolicyPayList(updatePolicyPayList);
		vo.setSubTimes(subTimes);
		return vo;
	}

	@Override
	public boolean hasPrePayHistory(String reportNo, Integer caseTimes) {
		return prePayMapper.getPrePayHistory(reportNo,caseTimes) > 0;
	}

	@Override
	public FeeBigVO getPrePayFee(String reportNo, Integer caseTimes) {
		FeeBigVO vo = new FeeBigVO();
		vo.setReportNo(reportNo);
		vo.setCaseTimes(caseTimes);

		List<FeePayDTO> policyList = feePayService.getPolicyDepartment(reportNo, caseTimes);
		if(ListUtils.isEmptyList(policyList)){
			vo.setNormalFee(new ArrayList<>());
			return vo;
		}
		vo.setNormalFee(policyList);
		Integer subTimes = getApprovedSubTimes(reportNo,caseTimes);
		FeePayDTO param = new FeePayDTO();
		param.setReportNo(reportNo);
		param.setCaseTimes(caseTimes);
		param.setClaimType(CLAIM_TYPE_PRE_PAY);
		param.setSubTimes(subTimes);
		List<FeeInfoDTO> feeList = feePayService.getFeePayByParam(param);

		if(ListUtils.isNotEmpty(feeList)){
			Map<String,List<FeeInfoDTO>> feeMap = feeList.stream().collect(Collectors.groupingBy(FeeInfoDTO :: getPolicyNo));
			policyList.forEach(dto -> dto.setFeeInfos(feeMap.getOrDefault(dto.getPolicyNo(),new ArrayList<>())) );
			List<String> feeIdList = feeList.stream().map(FeeInfoDTO::getIdAhcsFeePay).collect(Collectors.toList());
			List<InvoiceInfoDTO> invoiceList = feePayService.getInvoiceByFeeIds(feeIdList);
			if(ListUtils.isNotEmpty(invoiceList)){
				Map<String,List<InvoiceInfoDTO>> invoiceMap = invoiceList.stream().collect(Collectors.groupingBy(InvoiceInfoDTO::getIdAhcsFeePay));
				feeList.forEach(fee ->{
					List<InvoiceInfoDTO> invoices =	invoiceMap.get(fee.getIdAhcsFeePay());
					if(ListUtils.isNotEmpty(invoices)){
						fee.setInvoiceInfo(invoices.get(0));
					}else{
						fee.setInvoiceInfo(new InvoiceInfoDTO());
					}
				});
			}
		}
		if (ListUtils.isNotEmpty(policyList)) {
			//设置共保描述
			Map<String,String> coinsMap = coinsureService.getCoinsureDescByReportNo(reportNo);
			policyList.forEach(dto -> dto.setCoinsuranceDesc(coinsMap.get(dto.getPolicyNo())));
		}

		return vo;
	}

	@Override
	public void savePrePayFee(FeeBigVO feeBig) {
		final String reportNo = feeBig.getReportNo();
		final Integer caseTimes = feeBig.getCaseTimes();
		final String userId = WebServletContext.getUserId();
		Integer subTimes = getApprovedSubTimes(reportNo,caseTimes);
		if(ListUtils.isEmptyList(feeBig.getNormalFee())){
			prePayTransactionService.delPrePayFeeInfo(reportNo,caseTimes,userId,subTimes);
			return;
		}
		PaymentInfoDTO param = new PaymentInfoDTO();
		param.setReportNo(reportNo);
		param.setCaseTimes(caseTimes);
		param.setPaymentUsage(PaymentInfoTypeEnum.COST.getType());
		List<PaymentInfoDTO> paymentInfoList = paymentInfoService.getPaymentInfo(param);
		Set<String> payInfoSet = null;
		if(ListUtils.isEmptyList(paymentInfoList)){
			throw new GlobalBusinessException("请录入费用支付信息");
		}else{
			payInfoSet = paymentInfoList.stream().map(PaymentInfoDTO::getIdClmPaymentInfo).collect(Collectors.toSet());
		}

		List<FeeInfoDTO> feePayList = checkPrePayFee(feeBig,payInfoSet);

		List<InvoiceInfoDTO> invoiceList = new ArrayList<>();
		feePayList.forEach(fee ->{
			fee.setCreatedBy(userId);
			fee.setUpdatedBy(userId);
			fee.setIdAhcsFeePay(UuidUtil.getUUID());
			fee.setReportNo(reportNo);
			fee.setCaseTimes(caseTimes);
			fee.setReportNo(reportNo);
			fee.setCaseTimes(caseTimes);
			fee.setClaimType(CLAIM_TYPE_PRE_PAY);
			fee.setSubTimes(subTimes);
			InvoiceInfoDTO invoice = fee.getInvoiceInfo();
			if(null != invoice ){
				invoice.setIdAhcsInvoiceInfo(UuidUtil.getUUID());
				invoice.setIdAhcsFeePay(fee.getIdAhcsFeePay());
				invoice.setCreatedBy(userId);
				invoice.setUpdatedBy(userId);
				invoice.setIsModifiedFlag("0");
				invoiceList.add(invoice);
			}
		});

		prePayTransactionService.savePrePayFeeInfo(reportNo,caseTimes,userId,subTimes,feePayList,invoiceList);
	}

	private List<FeeInfoDTO> checkPrePayFee(FeeBigVO feeBig,Set<String> payInfoSet){
		List<FeeInfoDTO> resultList = new ArrayList<>();
		Set<String> feeRepeatSet = new HashSet<>();
		for(FeePayDTO policy:feeBig.getNormalFee()){
			List<FeeInfoDTO> feeList = policy.getFeeInfos();
			if(ListUtils.isEmptyList(feeList)){
				continue;
			}
			for(FeeInfoDTO fee:feeList){
				if(StringUtils.isEmptyStr(fee.getFeeType()) || fee.getFeeAmount()==null
						|| !BigDecimalUtils.compareBigDecimalPlus(fee.getFeeAmount(),BigDecimal.ZERO)){
					continue;
				}
				if(!feeRepeatSet.add(policy.getPolicyNo()+fee.getIdClmPaymentInfo()+fee.getFeeType())){
					throw new GlobalBusinessException("同一保单同一支付信息不能有重复的费用类型");
				}
				if(StringUtils.isEmptyStr(fee.getIdClmPaymentInfo())){
					throw new GlobalBusinessException("保单号"+policy.getPolicyNo()+"支付信息不能为空，请重新选择");
				}
				if(payInfoSet.add(fee.getIdClmPaymentInfo())){
					LogUtil.audit("payInfoSet={},paymentInfoid={}",JSON.toJSONString(payInfoSet),fee.getIdClmPaymentInfo());
					throw new GlobalBusinessException("支付信息错误，请重新选择");
				}

				resultList.add(fee);
				fee.setPolicyNo(policy.getPolicyNo());
				fee.setCaseNo(policy.getCaseNo());
				InvoiceInfoDTO invoice = fee.getInvoiceInfo();
				if(invoice != null){
					if(!"000".equals(invoice.getInvoiceType())){
						if(StringUtils.isEmptyStr(invoice.getInvoiceNo()) || StringUtils.isEmptyStr(invoice.getInvoiceCode()) ||  StringUtils.isEmptyStr(invoice.getInvoiceType())){
							throw new GlobalBusinessException("发票类型或号码或代码不能为空");
						}
					}
				}
			}
		}
		if(ListUtils.isEmptyList(resultList)){
			throw new GlobalBusinessException("请录入预赔费用");
		}
		return resultList;
	}

	@Override
	public List<PaymentItemDTO> getPrePaymentItem(PaymentItemDTO paymentItemDTO) {
		String reportNo = paymentItemDTO.getReportNo();
		Integer caseTimes = paymentItemDTO.getCaseTimes();
		paymentItemDTO.setSubTimes(getApprovedSubTimes(reportNo,caseTimes));
		paymentItemDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
		List<PaymentItemDTO> itemList = paymentItemService.getPrePaymentItem(paymentItemDTO);
		if(ListUtils.isEmptyList(itemList)){
			return new ArrayList<>();
		}
		itemList.forEach(dto->{dto.setPaymentTypeName(PaymentTypeEnum.getName(dto.getPaymentType()));
			if("0".equals(dto.getCollectPaySign())){
				dto.setCollectPaySignName("收款");
			}else{
				dto.setCollectPaySignName("付款");
			}
		});
		return itemList;
	}

	private BigDecimal getPrePayFeeAmount(String reportNo,Integer caseTimes,Integer subTimes){
		FeePayDTO feePayDTO = new FeePayDTO();
		feePayDTO.setReportNo(reportNo);
		feePayDTO.setCaseTimes(caseTimes);
		feePayDTO.setSubTimes(subTimes);
		List<FeeInfoDTO> feeInfoList = Optional.ofNullable(feePayService.getPrePayFeeAmountByParam(feePayDTO)).orElse(new ArrayList<>());
		if(ListUtils.isEmptyList(feeInfoList)){
			return BigDecimal.ZERO;
		}
		BigDecimal preFeeAmout = BigDecimal.ZERO;
		for (FeeInfoDTO dto : feeInfoList) {
			preFeeAmout = preFeeAmout.add(dto.getFeeAmount());
		}
		return preFeeAmout;
	}

	private void checkPrePayAmountIsEqual(List<PrePolicyVO> prePolicyList,List<PrePaymentVO> prePaymentList){
		List<PrePolicyVO> prePolicyFilterList = prePolicyList.stream().filter(vo ->
				BigDecimalUtils.compareBigDecimalPlus(vo.getPolicyPayAmount(),BigDecimal.ZERO)).collect(Collectors.toList());

		Map<String,BigDecimal> policyPayMap = prePaymentList.stream().filter(vo ->
				BigDecimalUtils.compareBigDecimalPlus(vo.getPaymentAmount(),BigDecimal.ZERO))
				.collect(Collectors.groupingBy(PrePaymentVO::getPolicyNo,
						Collectors.collectingAndThen(Collectors.mapping(PrePaymentVO::getPaymentAmount,
						Collectors.reducing(BigDecimal::add)),Optional::get)));

		for (PrePolicyVO vo : prePolicyFilterList) {
			BigDecimal paymentAmt = policyPayMap.get(vo.getPolicyNo());
			if(paymentAmt == null || paymentAmt.compareTo(vo.getPolicyPayAmount()) !=0 ){
				LogUtil.audit("payAmt={},policyAmt={}",paymentAmt,vo.getPolicyPayAmount());
				throw new GlobalBusinessException("保单号"+vo.getPolicyNo()+"赔款支付信息金额不正确");
			}
		}

	}

	@Override
	public Integer getPrePayApplyCount(String reportNo, Integer caseTimes) {
		return prePayMapper.getPrePayApplyCount(reportNo,caseTimes);
	}

	@Override
	public PrePayCaseVO getPrePaySumList(String reportNo, Integer caseTimes) {
		List<PreDutyVO> preDutyList = prePayMapper.getPrePayDutyList(reportNo);
		if(ListUtils.isEmptyList(preDutyList)){
			return new PrePayCaseVO();
		}

		//设置最大给付额
		setMaxPayAmount(reportNo,caseTimes,preDutyList);

		List<PreDutyVO> dutyPreList = prePayMapper.getDutyPrepaySum(reportNo,caseTimes);
		Map<String, PreDutyVO> dutyPrePayMap = new HashMap<>();
		BigDecimal prePayAmt = BigDecimal.ZERO;
		if (ListUtils.isNotEmpty(dutyPreList)) {
			dutyPrePayMap = dutyPreList.stream().collect(Collectors.toMap(k ->k.getPolicyNo()+k.getPlanCode()+k.getDutyCode(),dto->dto));
			for (PreDutyVO vo : preDutyList) {
				vo.setDutyPreAmount(dutyPrePayMap.getOrDefault(vo.getPolicyNo()+vo.getPlanCode()+vo.getDutyCode(),new PreDutyVO()).getDutyPreAmount());
				if(vo.getDutyPreAmount() != null){
					prePayAmt = prePayAmt.add(vo.getDutyPreAmount());
				}
			}
		}

		List<PrePolicyVO> feePreList = prePayMapper.getFeePrepaySum(reportNo,caseTimes);
		Map<String, BigDecimal> feePreMap = new HashMap<>();
		BigDecimal preFeeAmt = BigDecimal.ZERO;
		if (ListUtils.isNotEmpty(feePreList)) {
			feePreMap = feePreList.stream().collect(Collectors.toMap(k ->k.getPolicyNo(),v->v.getPolicyFeeAmount()));
			preFeeAmt = feePreList.stream().map(PrePolicyVO::getPolicyFeeAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
		}

		List<PrePolicyVO> policyList = new ArrayList<>();
		Map<String, List<PreDutyVO>> policyMap = preDutyList.stream().collect(Collectors.groupingBy(PreDutyVO::getPolicyNo));
		for (Map.Entry<String, List<PreDutyVO>> policyEntry : policyMap.entrySet()) {
			PrePolicyVO policyVO = new PrePolicyVO();
			BeanUtils.copyProperties(policyEntry.getValue().get(0),policyVO);
			Map<String, List<PreDutyVO>> planMap = policyEntry.getValue().stream().collect(Collectors.groupingBy(PreDutyVO::getPlanCode));
			List<PrePlanVO> planList = new ArrayList<>();
			for (Map.Entry<String, List<PreDutyVO>> planEntry : planMap.entrySet()) {
				PrePlanVO planVO = new PrePlanVO();
				BeanUtils.copyProperties(planEntry.getValue().get(0),planVO);
				planVO.setPreDutyList(planEntry.getValue());
				planList.add(planVO);
			}
			policyVO.setPrePlanList(planList);
			policyVO.setPolicyFeeAmount(feePreMap.get(policyVO.getPolicyNo()));
			policyList.add(policyVO);
		}
		PrePayCaseVO caseVO = new PrePayCaseVO();
		caseVO.setPrePolicyList(policyList);
		prePayAmt = prePayAmt == null ? BigDecimal.ZERO:prePayAmt;
		preFeeAmt = preFeeAmt == null ? BigDecimal.ZERO:preFeeAmt;
		caseVO.setPrePayAmount(prePayAmt);
		caseVO.setPreFeeAmount(preFeeAmt);
		caseVO.setPreTotalAmount(prePayAmt.add(preFeeAmt));
		return caseVO;

	}

	/**
	 * 预赔申请记录按钮飘红
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	@Override
	public Integer getPrePayCount(String reportNo, Integer caseTimes) {
		return prePayMapper.getPrePayTotals(reportNo,caseTimes);
	}
}
