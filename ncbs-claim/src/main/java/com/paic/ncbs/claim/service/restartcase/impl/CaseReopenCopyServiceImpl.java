package com.paic.ncbs.claim.service.restartcase.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ReportConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.EstimateTypeEnum;
import com.paic.ncbs.claim.common.util.EstimateUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AntiMoneyLaunderingMapper;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.ClmsAmlCompanyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.*;
import com.paic.ncbs.claim.dao.mapper.duty.BigDiseaseMapper;
import com.paic.ncbs.claim.dao.mapper.dynamic.DynamicFieldResultMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseProcessMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseRegisterApplyMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseExMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.*;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.*;
import com.paic.ncbs.claim.dao.mapper.other.SurveyMapper;
import com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentDutyMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.report.LinkManMapper;
import com.paic.ncbs.claim.dao.mapper.report.RegisterCaseLogMapper;
import com.paic.ncbs.claim.dao.mapper.restartcase.RestartCaseRecordMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DiagnoseHospitalBillAssociationMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillDetailMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillReduceDetailMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillSpecialMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;
import com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO;
import com.paic.ncbs.claim.model.dto.duty.LossEstimationDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.model.dto.pay.PaymentPlanDutyDTO;
import com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.service.duty.LossEstimationService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.restartcase.CaseReopenCopyService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.trace.ClmsTraceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Slf4j
@Service
public class CaseReopenCopyServiceImpl implements CaseReopenCopyService {

    @Autowired
    private CaseBaseMapper caseBaseMapper;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private WholeCaseBaseExMapper wholeCaseBaseExMapper;
    @Autowired
    private BigDiseaseMapper bigDiseaseMapper;
    @Autowired
    private MedicalBillInfoMapper billInfoMapper;
    @Autowired
    private MedicalBillDetailMapper billDetailMapper;
    @Autowired
    private MedicalBillReduceDetailMapper billReduceDetailMapper;
    @Autowired
    private MedicalBillSpecialMapper billSpecialMapper;
    @Autowired
    DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    @Autowired
    private CaseClassMapper caseClassMapper;
    @Autowired
    private CaseProcessMapper caseProcessMapper;
    @Autowired
    private CaseRegisterApplyMapper caseRegisterApplyMapper;
    @Autowired
    private ChannelProcessMapper channelProcessMapper;
    @Autowired
    private DisabilityAppraisalMapper disabilityAppraisalMapper;
    @Autowired
    private LinkManMapper linkManMapper;
    @Autowired
    private LossReduceMapper lossReduceMapper;
    @Autowired
    private OtherLossMapper otherLossMapper;
    @Autowired
    private PaymentInfoMapper paymentInfoMapper;
    @Autowired
    private PersonAccidentMapper personAccidentMapper;
    @Autowired
    private PersonBenefitMapper personBenefitMapper;
    @Autowired
    private PersonDeathMapper personDeathMapper;
    @Autowired
    private PersonDiagnoseMapper personDiagnoseMapper;
    @Autowired
    private PersonDisabilityMapper personDisabilityMapper;
    @Autowired
    private PersonDiseaseMapper personDiseaseMapper;
    @Autowired
    private PersonHospitalMapper personHospitalMapper;
    @Autowired
    private PersonObjectExMapper personObjectExMapper;
    @Autowired
    private PersonOtherLossMapper personOtherLossMapper;
    @Autowired
    private RegisterCaseLogMapper registerCaseLogMapper;
    @Autowired
    private SurveyMapper surveyMapper;
    @Autowired
    private ClmsEstimateRecordMapper estimateRecordMapper;
    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;
    @Autowired
    private EstimatePlanMapper estimatePlanMapper;
    @Autowired
    private EstimateDutyMapper estimateDutyMapper;
    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordMapper;
    @Autowired
    private AntiMoneyLaunderingMapper antiMoneyLaunderingMapper;
    @Autowired
    private RestartCaseRecordMapper restartCaseRecordMapper;
    @Autowired
    private ClmsAllowanceInfoMapper clmsAllowanceInfoMapper;
    @Autowired
    private ClmsCashLossInfoMapper clmsCashLossInfoMapper;
    @Autowired
    private ClmsLegalLiabilityClassInfoMapper clmsLegalLiabilityClassInfoMapper;
    @Autowired
    private ClmsPersonalInjuryDeathInfoMapper clmsPersonalInjuryDeathInfoMapper;
    @Autowired
    private ClmsPropertyLossInfoMapper clmsPropertyLossInfoMapper;
    @Autowired
    private ClmsRescueInfoMapper clmsRescueInfoMapper;
    @Autowired
    private ClmsSubstanceLossInfoMapper clmsSubstanceLossInfoMapper;
    @Autowired
    private PropLossMapper propLossMapper;
    @Autowired
    private PropDetailLossMapper propDetailLossMapper;
    @Autowired
    private ClmsTravelDelayInfoMapper clmsTravelDelayInfoMapper;

    @Autowired
    private DiagnoseHospitalBillAssociationMapper diagnoseHospitalBillAssociationMapper;

    @Autowired
    private FileInfoMapper fileInfoMapper;

    @Autowired
    private ClmsAmlCompanyInfoMapper amlCompanyInfoMapper;

    @Autowired
    private ClmsPaymentDutyMapper paymentDutyMapper;

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private EstimateDutyHistoryMapper estimateDutyHistoryMapper;
    @Autowired
    private LossEstimationService lossEstimationService;
    @Autowired
    private EstimateChangeMapper estimateChangeMapper;

    @Autowired
    private ClmsTraceService clmsTraceService;

    @Autowired
    private DynamicFieldResultMapper dynamicFieldResultMapper;

    @Transactional
    @Override
    public void copyForCaseReopen(String reportNo, Integer caseTimes, String idClmRestartCaseRecord) {
        Integer reopenCaseTimes = caseTimes + 1;
        WholeCaseBaseEntity wholeCaseBase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, reopenCaseTimes);
        if (Objects.nonNull(wholeCaseBase)) {
            throw new GlobalBusinessException("案件重开，数据已拷贝！");
        }

        String userId = MDC.get(BaseConstant.USER_ID);
        CaseReopenCopyDTO dto = new CaseReopenCopyDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setReopenCaseTimes(reopenCaseTimes);
        dto.setUserId(userId);
        dto.setCaseStatus(ReportConstant.INIT_CASE_STATUS);
        dto.setProcessStatus(CaseProcessStatus.PENDING_SETTLE.getCode());
        //dto.setIdClmWholeCaseBase(UuidUtil.getUUID());
        //dto.setIdClmChannelProcess(UuidUtil.getUUID());
        dto.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
        // 目前只有人伤
        List<ChannelProcessDTO> channelProcessIds = channelProcessMapper.getChannelProcessIds(reportNo, caseTimes);
        if (channelProcessIds!= null && channelProcessIds.size() > 0){
            for (ChannelProcessDTO channelProcessDTO : channelProcessIds) {
                dto.setIdClmWholeCaseBase(UuidUtil.getUUID());
                dto.setIdClmChannelProcess(UuidUtil.getUUID());
                dto.setChannelType(channelProcessDTO.getChannelType());
                // 支持非人伤
                channelProcessMapper.copyForCaseReopen(dto);
                if (channelProcessDTO.getChannelType().equals(ChecklossConst.CASECLASS_PEOPLE_HURT)){
                    //人伤
                    personAccidentMapper.copyForCaseReopen(dto);
                    personDiseaseMapper.copyForCaseReopen(dto);
                    personDiagnoseMapper.copyForCaseReopen(dto);
                    personDisabilityMapper.copyForCaseReopen(dto);
                    personDeathMapper.copyForCaseReopen(dto);
                    bigDiseaseMapper.copyForCaseReopen(dto);
                    disabilityAppraisalMapper.copyForCaseReopen(dto);
                    personHospitalMapper.copyForCaseReopen(dto);
                    personObjectExMapper.copyForCaseReopen(dto);
                    personBenefitMapper.copyForCaseReopen(dto);
                    personOtherLossMapper.copyForCaseReopen(dto);
                }else if (channelProcessDTO.getChannelType().equals(ChecklossConst.CASECLASS_NO_PEOPLE_HURT)){
                    //非人伤
                    clmsAllowanceInfoMapper.copyForCaseReopen(dto);
                    clmsCashLossInfoMapper.copyForCaseReopen(dto);
                    clmsRescueInfoMapper.copyForCaseReopen(dto);
                    clmsSubstanceLossInfoMapper.copyForCaseReopen(dto);
                    propLossMapper.copyForCaseReopen(dto);
                    clmsTravelDelayInfoMapper.copyForCaseReopen(dto);
                    clmsPersonalInjuryDeathInfoMapper.copyForCaseReopen(dto);
                    clmsPropertyLossInfoMapper.copyForCaseReopen(dto);
                    clmsLegalLiabilityClassInfoMapper.copyForCaseReopen(dto);
                    otherLossMapper.copyForCaseReopen(dto);
                    propDetailLossMapper.copyForCaseReopen(dto);
                }
            }
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("ReportReopenCopyServiceImpl.copyForReportReopen start, dto={}", JSON.toJSONString(dto));

        dynamicFieldResultMapper.copyForCaseReopen(dto);
        caseBaseMapper.copyForCaseReopen(dto);
        wholeCaseBaseMapper.copyForCaseReopen(dto);
        wholeCaseBaseExMapper.copyForCaseReopen(dto);
        caseProcessMapper.copyForCaseReopen(dto);
        caseRegisterApplyMapper.copyForCaseReopen(dto);
        linkManMapper.copyForCaseReopen(dto);
        lossReduceMapper.copyForCaseReopen(dto);
        paymentInfoMapper.copyForCaseReopen(dto);
        registerCaseLogMapper.copyForCaseReopen(dto);
        surveyMapper.copyForCaseReopen(dto);
        antiMoneyLaunderingMapper.copyForCaseReopen(dto);
        fileInfoMapper.copyForCaseReopen(dto);

        Integer amlCompanyInfoCount = amlCompanyInfoMapper.getAmlCompanyInfoCount(reportNo, caseTimes);
        if (amlCompanyInfoCount != null && amlCompanyInfoCount > 0){
            amlCompanyInfoMapper.copyForCaseReopen(dto);
        }

        // 从重开记录中获取案件小类
        RestartCaseRecordEntity restartCaseRecord = restartCaseRecordMapper.selectByPrimaryKey(idClmRestartCaseRecord);
        if (Objects.nonNull(restartCaseRecord)){
            dto.setRestartAmount(restartCaseRecord.getRestartAmount());
            estimateRecordMapper.copyForCaseReopen(dto);
        }
        if (Objects.nonNull(restartCaseRecord) && StringUtils.isNotBlank(restartCaseRecord.getCaseKind())) {
            String taskId = caseClassMapper.getCurrentTaskCodeFromCaseClass(reportNo, caseTimes, null);

            List<CaseClassDTO> caseClassList = Lists.newArrayList();
            for (String caseSubClass : restartCaseRecord.getCaseKind().split(",")) {
                CaseClassDTO caseClassDTO = new CaseClassDTO();
                caseClassDTO.setReportNo(reportNo);
                caseClassDTO.setCaseTimes(reopenCaseTimes);
                caseClassDTO.setCaseSubClass(caseSubClass);
                caseClassDTO.setTaskId(taskId);
                caseClassDTO.setStatus(ChecklossConst.STATUS_TMP_SUBMIT);
                caseClassList.add(caseClassDTO);
            }
            caseClassMapper.addCaseClassList(caseClassList, reopenCaseTimes, userId);
        }

        // 拷贝账单相关表
        List<String> idBillInfoList = billInfoMapper.getIdBillInfoList(dto.getReportNo(), dto.getCaseTimes());
        if (CollectionUtils.isNotEmpty(idBillInfoList)) {
            //老的billInfo表主键id与新的id做映射
            Map<String, String> oldBillIdAndNewBillIdMap = new HashMap<>();
            List<BillCopyDTO> billCopyList = Lists.newArrayList();
            for (String idAhcsBillInfo : idBillInfoList) {
                BillCopyDTO billCopy = new BillCopyDTO();
                String newIdAhcsBillInfo = UuidUtil.getUUID();
                billCopy.setIdAhcsBillInfo(idAhcsBillInfo);
                billCopy.setReopenIdAhcsBillInfo(newIdAhcsBillInfo);
                billCopy.setReopenCaseTimes(reopenCaseTimes);
                billCopy.setUserId(userId);
                billCopy.setIdClmChannelProcess(dto.getIdClmChannelProcess());
                billCopyList.add(billCopy);
                oldBillIdAndNewBillIdMap.put(idAhcsBillInfo, newIdAhcsBillInfo);
            }
            billInfoMapper.copyForCaseReopen(billCopyList);
            billDetailMapper.copyForCaseReopen(billCopyList);
            billReduceDetailMapper.copyForCaseReopen(billCopyList);
            billSpecialMapper.copyForCaseReopen(billCopyList);

            copyForCaseReopenDiagnoseHospitalBillAssociation(userId, idBillInfoList, oldBillIdAndNewBillIdMap);
        }

        // 拷贝保单预估相关表
        List<EstimatePolicyInfoDTO> estimatePolicyList = estimatePolicyMapper.getEstimateDataByReportNo(dto.getReportNo(), dto.getCaseTimes());
        List<String> caseNoList = Lists.newArrayList();
        Map<String, EstimatePolicyInfoDTO> policyMap = Maps.newHashMap();
        for (EstimatePolicyInfoDTO policyDTO : estimatePolicyList) {
            caseNoList.add(policyDTO.getCaseNo());
            policyMap.put(policyDTO.getIdAhcsEstimatePolicy(), policyDTO);
            policyDTO.setReopenEstimatePolicyId(UuidUtil.getUUID());
            policyDTO.setReopenUserId(userId);
            policyDTO.setReopenCaseTimes(reopenCaseTimes);
        }
        estimatePolicyMapper.copyForCaseReopen(estimatePolicyList);

        List<EstimatePlanDTO> estimatePlanList = estimatePlanMapper.getListByPolicyId(Lists.newArrayList(policyMap.keySet()));
        Map<String, EstimatePlanDTO> planMap = Maps.newHashMap();
        for (EstimatePlanDTO planDTO : estimatePlanList) {
            planMap.put(planDTO.getIdAhcsEstimatePlan(), planDTO);
            planDTO.setReopenEstimatePlanId(UuidUtil.getUUID());
            planDTO.setReopenUserId(userId);
            planDTO.setReopenCaseTimes(reopenCaseTimes);
            planDTO.setIdAhcsEstimatePolicy(policyMap.get(planDTO.getIdAhcsEstimatePolicy()).getReopenEstimatePolicyId());
        }
        estimatePlanMapper.copyForCaseReopen(estimatePlanList);

        List<EstimateDutyDTO> estimateDutyList = estimateDutyMapper.getDutyListByIdPlanList(Lists.newArrayList(planMap.keySet()));
        for (EstimateDutyDTO dutyDTO : estimateDutyList) {
            dutyDTO.setReopenEstimateDutyId(UuidUtil.getUUID());
            dutyDTO.setReopenUserId(userId);
            dutyDTO.setReopenCaseTimes(reopenCaseTimes);
            dutyDTO.setIdAhcsEstimatePlan(planMap.get(dutyDTO.getIdAhcsEstimatePlan()).getReopenEstimatePlanId());
        }
        estimateDutyMapper.copyForCaseReopen(estimateDutyList);

        dto.setCaseNoList(caseNoList);
        //如果是重开，查询上一次结案的已决责任金额
        List<PaymentPlanDutyDTO> paymentDutyDTOList = paymentDutyMapper.getPaymentDutyByReportNo(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(paymentDutyDTOList)) {
            estimateDutyRecordMapper.copyForCaseReopen(dto);
        }else {
            copyForDutyRecord(reportNo, caseTimes, paymentDutyDTOList);
        }
        //estimateDutyRecordMapper.copyForCaseReopen(dto);
        //人伤跟踪任务业务数据copy数据
        clmsTraceService.copyPersonTrace(dto);
        log.info("ReportReopenCopyServiceImpl.copyForReportReopen end, useTime={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    private void copyForDutyRecord(String reportNo, Integer caseTimes,List<PaymentPlanDutyDTO> paymentDutyDTOList) {
        Map<String, BigDecimal> policyPaySumMap = new HashMap<>();
        Map<String, BigDecimal> policyFeeSumMap = new HashMap<>();
        List<EstimateChangeDTO> estimateChangeList = new ArrayList<>();
        //计算变化量 = 估损 - 已决赔付金额
        Map<String, BigDecimal> oldPlanDutyCodeAmountMap = new HashMap<>();
        for (PaymentPlanDutyDTO paymentPlanDutyDTO: paymentDutyDTOList){
            if (paymentPlanDutyDTO.getPaymentType().equals("1J")){
                //目前不计算费用变化量
                continue;
            }
            String key = paymentPlanDutyDTO.getPlanCode() + paymentPlanDutyDTO.getDutyCode();
            if (oldPlanDutyCodeAmountMap.containsKey(key)){
                oldPlanDutyCodeAmountMap.put(key, oldPlanDutyCodeAmountMap.get(key).add(paymentPlanDutyDTO.getDutyPayAmount()));
            }else {
                oldPlanDutyCodeAmountMap.put(key, paymentPlanDutyDTO.getDutyPayAmount());
            }
        }
        //筛选出我司共保比例
        Map<String,BigDecimal> ssCoinsRateMap = estimateService.getCoinsRateMap(reportNo);
        // 获取再保的保单，获取主共从共等数据
        Map<String, List<CoinsureDTO>> coinsuranceMap = coinsureService.getCoinsureListByReportNo(reportNo);

        List<EstimateDutyRecordDTO> recordDTOList = estimateDutyRecordMapper.getRecordsOfRegisterCaseByReportNo(reportNo, caseTimes,"05");
        EstimateDutyRecordDTO newEstimateDutyRecord;
        List<EstimateDutyRecordDTO> dutyRecordReopenDTOList = new ArrayList<>();
        List<EstimateDutyHistoryDTO> estimateDutyHistoryDTOList = new ArrayList<>();
        String uuid = UuidUtil.getUUID();
        for (EstimateDutyRecordDTO recordDTO :recordDTOList) {
            newEstimateDutyRecord = new EstimateDutyRecordDTO();
            EstimateDutyHistoryDTO estimateDutyHistoryDTO = new EstimateDutyHistoryDTO();
            BeanUtils.copyProperties(recordDTO, newEstimateDutyRecord);
            BigDecimal coinsRate = ssCoinsRateMap.get(recordDTO.getPolicyNo());
            BigDecimal estimateAmount = (recordDTO.getEstimateAmount() == null) ? new BigDecimal(0) : recordDTO.getEstimateAmount();
            //上次已赔付客户金额总数
            BigDecimal oldAmount = oldPlanDutyCodeAmountMap.get(recordDTO.getPlanCode() + recordDTO.getDutyCode());
            newEstimateDutyRecord.setChgPayValue(estimateAmount.subtract(oldAmount == null ? new BigDecimal(0) : oldAmount));
            if (coinsRate != null) {
                newEstimateDutyRecord.setSsCoinsRate(coinsRate);
                //0:内部联保(我司主联)、1:外部联保(我司主承)、2:外部联保(我司非主承)
                String coinsuranceType = null;
                List<CoinsureDTO> coinsureDTOList = coinsuranceMap.get(recordDTO.getPolicyNo());
                if (CollectionUtils.isNotEmpty(coinsureDTOList)) {
                    coinsuranceType = coinsureDTOList.get(0).getCoinsuranceType();
                }
                if ("0".equals(coinsuranceType) || "1".equals(coinsuranceType)){
                    //这种情况才进行共保比例金额计算
                    //重开-我司共保赔付金额
                    BigDecimal coinsPayValue = estimateAmount.multiply(nvl(coinsRate, 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                    newEstimateDutyRecord.setSsCoinsPayValue(coinsPayValue);
                    //我司本次变化量 = 本(次估损金额 - 上次已决金额) * 共保比例
                    BigDecimal ssCoinsChgPayValue = newEstimateDutyRecord.getChgPayValue()
                            .multiply(nvl(coinsRate, 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                    newEstimateDutyRecord.setSsCoinsChgPayValue(ssCoinsChgPayValue);
                }else {
                    newEstimateDutyRecord.setSsCoinsPayValue(estimateAmount);
                    newEstimateDutyRecord.setSsCoinsChgPayValue(newEstimateDutyRecord.getChgPayValue());
                }
            }
            newEstimateDutyRecord.setEstimateType(EstimateTypeEnum.REGISTER_PENDING.getType());
            newEstimateDutyRecord.setArchiveTime(new Date());
            newEstimateDutyRecord.setCaseTimes(caseTimes+1);
            dutyRecordReopenDTOList.add(newEstimateDutyRecord);

            BeanUtils.copyProperties(newEstimateDutyRecord, estimateDutyHistoryDTO);
            estimateDutyHistoryDTO.setDutyEstimateAmount(newEstimateDutyRecord.getEstimateAmount());
            estimateDutyHistoryDTO.setIdAhcsEstimatDutyHistory(UuidUtil.getUUID());
            estimateDutyHistoryDTO.setIdFlagHistoryChange(uuid);
            estimateDutyHistoryDTOList.add(estimateDutyHistoryDTO);
            EstimateUtil.calPolicyPayAndFeeSumAmount(policyPaySumMap, policyFeeSumMap, newEstimateDutyRecord);
        }
        estimateDutyRecordMapper.addEstimateDutyRecordList(dutyRecordReopenDTOList);
        //重开审批通过新增未决历史信息数据

        for (Map.Entry<String, BigDecimal> entry : policyPaySumMap.entrySet()) {
            EstimateChangeDTO estimateChangeDTO = new EstimateChangeDTO();
            estimateChangeDTO.setReportNo(reportNo);
            estimateChangeDTO.setCaseTimes(caseTimes+1);
            estimateChangeDTO.setPolicyNo(entry.getKey());
            estimateChangeDTO.setReason(null);//首次为空
            estimateChangeDTO.setMaxPayAmount(null);
            estimateChangeDTO.setSumDutyPayAmount(entry.getValue());
            estimateChangeDTO.setSumDutyFeeAmount(policyFeeSumMap.get(entry.getKey()));
            BigDecimal changeAmount = policyFeeSumMap.get(entry.getKey())==null
                    ?new BigDecimal(0): policyFeeSumMap.get(entry.getKey());
            estimateChangeDTO.setChangeAmount(entry.getValue().add(changeAmount));
            estimateChangeDTO.setIdclmsEstimateChange(UuidUtil.getUUID());
            estimateChangeDTO.setIdFlagHistoryChange(uuid);
            estimateChangeDTO.setCreatedBy(WebServletContext.getUserId());
            estimateChangeDTO.setUpdatedBy(WebServletContext.getUserId());
            estimateChangeDTO.setChangeDate(new Date());
            estimateChangeDTO.setUserId(ConstValues.SYSTEM);
            estimateChangeList.add(estimateChangeDTO);
        }
        estimateChangeMapper.addEstimateChangeList(estimateChangeList);
        estimateDutyHistoryMapper.addHistoryRecord(estimateDutyHistoryDTOList);
        //保存
        List<LossEstimationDTO> lossEstimationDTOs = lossEstimationService.getLastLossEstimationVOList(reportNo, caseTimes);
        for(LossEstimationDTO lossEstimationDTO : lossEstimationDTOs){
            lossEstimationDTO.setId(UuidUtil.getUUID());
            lossEstimationDTO.setCaseTimes(caseTimes+1);
            lossEstimationDTO.setCreatedBy(WebServletContext.getUserId());
            lossEstimationDTO.setUpdatedBy(WebServletContext.getUserId());
            lossEstimationDTO.setTaskId(uuid);
            lossEstimationService.saveLossEstimation(lossEstimationDTO);
        }
    }

    private void copyForCaseReopenDiagnoseHospitalBillAssociation(String userId, List<String> idBillInfoList, Map<String, String> oldBillIdAndNewBillIdMap) {
        //组装新的重开数据
        List<DiagnoseHospitalBillAssociationDTO> diagnoseCopyList = Lists.newArrayList();
        //根据billInfo的主键id查询诊断医院票据信息关联表
        List<DiagnoseHospitalBillAssociationDTO> diagnoseList = diagnoseHospitalBillAssociationMapper.getDiagnoseHospitalBillAssociationList(idBillInfoList);
        diagnoseList.stream().forEach(diagnoseDTO -> {
            diagnoseDTO.setIdClmsBillInfo(oldBillIdAndNewBillIdMap.get(diagnoseDTO.getIdClmsBillInfo()));
            diagnoseDTO.setUserId(userId);
            diagnoseDTO.setIdClmsDiagnoseHospitalBillAssociation(UuidUtil.getUUID());
            diagnoseCopyList.add(diagnoseDTO);
        });
        if (CollectionUtils.isNotEmpty(diagnoseCopyList)){
            diagnoseHospitalBillAssociationMapper.copyForCaseReopen(diagnoseCopyList);
        }
    }
}
