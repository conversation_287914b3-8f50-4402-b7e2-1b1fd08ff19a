package com.paic.ncbs.claim.controller.pay;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.pay.impl.BatchPayServiceImpl;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "批量支付中心")
@RestController
@Slf4j
@RequestMapping("/public/pay/batchPay")
public class BatchPayController extends BaseController {

    @Autowired
    private BatchPayServiceImpl batchPayServiceImpl;
    @Autowired
    private PaymentItemService paymentItemService;

    /**
     * 获取批量支付打包查询页面的机构列表
     * @param
     */
    @GetMapping(value = "/getSelectDepartmentList")
    public ResponseResult<Object> getSelectDepartmentList(){
        LogUtil.audit("获取批量支付打包查询页面的机构列表");
        try{
            List<DepartmentVO> departmentVOList =
                    batchPayServiceImpl.getSelectDepartmentList();
            return ResponseResult.success(departmentVOList);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }


    /**
     * 批量支付查询接口
     * @param dto
     */
    @PostMapping("/queryPaymentDetails")
    public ResponseResult<Object> queryPaymentDetails(@RequestBody BatchPaymentDetailDTO dto){
        LogUtil.audit("批量支付，查询条件请求入参{}",JSON.toJSONString(dto));
        try{
            return ResponseResult.success(batchPayServiceImpl.queryPaymentDetails(dto));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    /**
     * 批量支付打包接口
     * @param dto
     */
    @PostMapping("/packagePayment")
    public ResponseResult<Object> packagePayment(@RequestBody BatchPackagePaymentDTO dto){
        LogUtil.audit("批量打包请求入参{}",JSON.toJSONString(dto));
        try{
            return ResponseResult.success(batchPayServiceImpl.packagePayment(dto));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    /**
     * 查询已打包的数据
     * @param dto
     */
    @PostMapping("/queryBatchPayDetails")
    public ResponseResult<Object> queryBatchPayDetails(@RequestBody BatchPaymentDTO dto){
        LogUtil.audit("查询已打包列表请求入参{}",JSON.toJSONString(dto));
        try{
            return ResponseResult.success(batchPayServiceImpl.queryBatchPayDetails(dto));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    /**
     * 发起支付
     * @param dto
     */
    @PostMapping("/sendPayment")
    public ResponseResult sendPayment(@RequestBody BatchPaymentDTO dto){
        LogUtil.audit("发起支付请求入参{}",JSON.toJSONString(dto));
        try{
            batchPayServiceImpl.sendPayment(dto);
            return ResponseResult.success();
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    /**
     * 解包
     * @param dto
     */
    @PostMapping("/unpackPayment")
    public ResponseResult unpackPayment(@RequestBody BatchPaymentDTO dto){
        LogUtil.audit("解包请求入参{}",JSON.toJSONString(dto));
        try{
            batchPayServiceImpl.unpackPayment(dto);
            return ResponseResult.success();
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    /**
     * 批量支付详情页主信息
     * @param dto
     */
    @PostMapping("/queryMergePaymentByBatchNo")
    public ResponseResult<Object> queryMergePaymentByBatchNo(@RequestBody BatchPaymentDTO dto){
        LogUtil.audit("查询详情主信息请求入参{}",JSON.toJSONString(dto));
        try{
            return ResponseResult.success(batchPayServiceImpl.queryMergePaymentByBatchNo(dto));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    /**
     * 批量支付详情列表
     * @param dto
     */
    @PostMapping("/queryPaymentListByBatchNo")
    public ResponseResult<Object> queryPaymentListByBatchNo(@RequestBody BatchPaymentDetailDTO dto){
        LogUtil.audit("批量支付详情列表，查询条件请求入参{}",JSON.toJSONString(dto));
        try{
            return ResponseResult.success(batchPayServiceImpl.queryPaymentListByBatchNo(dto));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @PostMapping("/updateBatchPaymentInfo")
    public ResponseResult updateBatchPaymentInfo(@RequestBody BatchPaymentDTO dto){
        LogUtil.audit("修改请求入参{}",JSON.toJSONString(dto));
        try{
            batchPayServiceImpl.updateBatchPaymentInfo(dto);
            return ResponseResult.success();
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @PostMapping("/queryBatchPaymentInfo")
    public ResponseResult queryBatchPaymentInfo(@RequestBody BatchPaymentDetailDTO dto){
        LogUtil.audit("查询支付请求入参{}",JSON.toJSONString(dto));
        try{
            return ResponseResult.success(batchPayServiceImpl.queryBatchPaymentInfo(dto));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

}