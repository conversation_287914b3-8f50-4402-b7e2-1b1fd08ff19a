package com.paic.ncbs.claim.model.vo.ahcs;

import java.math.BigDecimal;

public class PrePaymentVO {
    private String policyNo;
    private String caseNo;
    private String idClmPaymentInfo;
    private String feeType;
    private BigDecimal paymentAmount;

    /**
     * 共保标志 0非共保1共保
     */
    private String coinsuranceMark;

    /**
     * 是否主承保 0否1是
     */
    private String acceptInsuranceFlag;

    /**
     * 共保公司编码
     */
    private String coinsuranceCompanyCode;

    /**
     * 共保公司名称
     */
    private String coinsuranceCompanyName;

    /**
     * 共保比例
     */
    private BigDecimal coinsuranceRatio;

    /**
     * 是否全额给付 0否1是
     */
    private String isFullPay;

    /**
     * 共保实付金额
     */
    private BigDecimal coinsuranceActualAmount;

    private String idAhcsFeePay;

    private String dutyCode;

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getIdClmPaymentInfo() {
        return idClmPaymentInfo;
    }

    public void setIdClmPaymentInfo(String idClmPaymentInfo) {
        this.idClmPaymentInfo = idClmPaymentInfo;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getCoinsuranceMark() {
        return coinsuranceMark;
    }

    public void setCoinsuranceMark(String coinsuranceMark) {
        this.coinsuranceMark = coinsuranceMark;
    }

    public String getAcceptInsuranceFlag() {
        return acceptInsuranceFlag;
    }

    public void setAcceptInsuranceFlag(String acceptInsuranceFlag) {
        this.acceptInsuranceFlag = acceptInsuranceFlag;
    }

    public String getCoinsuranceCompanyCode() {
        return coinsuranceCompanyCode;
    }

    public void setCoinsuranceCompanyCode(String coinsuranceCompanyCode) {
        this.coinsuranceCompanyCode = coinsuranceCompanyCode;
    }

    public String getCoinsuranceCompanyName() {
        return coinsuranceCompanyName;
    }

    public void setCoinsuranceCompanyName(String coinsuranceCompanyName) {
        this.coinsuranceCompanyName = coinsuranceCompanyName;
    }

    public BigDecimal getCoinsuranceRatio() {
        return coinsuranceRatio;
    }

    public void setCoinsuranceRatio(BigDecimal coinsuranceRatio) {
        this.coinsuranceRatio = coinsuranceRatio;
    }

    public String getIsFullPay() {
        return isFullPay;
    }

    public void setIsFullPay(String isFullPay) {
        this.isFullPay = isFullPay;
    }

    public BigDecimal getCoinsuranceActualAmount() {
        return coinsuranceActualAmount;
    }

    public void setCoinsuranceActualAmount(BigDecimal coinsuranceActualAmount) {
        this.coinsuranceActualAmount = coinsuranceActualAmount;
    }

    public String getIdAhcsFeePay() {
        return idAhcsFeePay;
    }

    public void setIdAhcsFeePay(String idAhcsFeePay) {
        this.idAhcsFeePay = idAhcsFeePay;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }
}
