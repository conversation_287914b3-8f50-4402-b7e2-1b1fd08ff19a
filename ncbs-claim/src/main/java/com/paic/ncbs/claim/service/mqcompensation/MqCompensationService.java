package com.paic.ncbs.claim.service.mqcompensation;

import com.paic.ncbs.claim.dao.entity.mq.MqMessageRecordEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-06-12 9:57
 */
public interface MqCompensationService {

    List<MqMessageRecordEntity> getFailureRecordList();

    void updateMqSendStatus(MqMessageRecordEntity mqMessageRecordEntity, boolean isSuccess);

    void addMqCompensation(String topic, String messageBody, Integer resendCount);
}
