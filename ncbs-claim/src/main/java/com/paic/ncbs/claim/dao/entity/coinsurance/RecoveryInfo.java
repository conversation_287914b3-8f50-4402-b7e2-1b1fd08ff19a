package com.paic.ncbs.claim.dao.entity.coinsurance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 共保摊回记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Getter
@Setter
@TableName("clms_recovery_info")
public class RecoveryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_coins_info", type = IdType.INPUT)
    private String idCoinsInfo;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 共保公司编码
     */
    @TableField("coins_company_code")
    private String coinsCompanyCode;

    /**
     * 共保公司名称
     */
    @TableField("coins_company_name")
    private String coinsCompanyName;

    /**
     * 是否我司
     */
    @TableField("company_flag")
    private String companyFlag;

    /**
     * 是否主席
     */
    @TableField("main_flag")
    private String mainFlag;

    /**
     * 共保比例
     */
    @TableField("coins_rate")
    private BigDecimal coinsRate;

    /**
     * 币别
     */
    @TableField("currency")
    private String currency;

    /**
     * 支付项目：赔款/费用
     */
    @TableField("pay_item")
    private String payItem;

    /**
     * 共保分摊金额
     */
    @TableField("coins_amount")
    private BigDecimal coinsAmount;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 摊回状态
     */
    @TableField("recovery_flag")
    private String recoveryFlag;

    /**
     * 已摊回金额
     */
    @TableField("recovery_amount")
    private BigDecimal recoveryAmount;

    /**
     * 摊回时间
     */
    @TableField("recovery_date")
    private LocalDateTime recoveryDate;

    /**
     * 摊回批次号
     */
    @TableField("recovery_batch_no")
    private String recoveryBatchNo;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 最新修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;

    @TableField("sub_times")
    private Integer subTimes;
}
