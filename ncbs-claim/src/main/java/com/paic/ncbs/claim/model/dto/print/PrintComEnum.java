package com.paic.ncbs.claim.model.dto.print;


import cn.hutool.core.util.ObjectUtil;

import java.util.HashMap;
import java.util.Map;

public enum PrintComEnum {
    SHENZHEN("808","招商银行股份有限公司上海自贸实验区分行","755914493610506","三星财产保险（中国）有限公司深圳分公司"),
    JANGSHU("785","招商银行股份有限公司上海自贸实验区分行","510902929910801","三星财产保险（中国）有限公司江苏分公司"),
    TIANJING("814","招商银行股份有限公司上海自贸实验区分行","121951208210908","三星财产保险（中国）有限公司天津分公司"),
    QINGDAO("793","招商银行股份有限公司上海自贸实验区分行","121951208110701","三星财产保险（中国）有限公司青岛分公司"),
    SHANXI("802","招商银行股份有限公司上海自贸实验区分行","***************","三星财产保险（中国）有限公司陕西分公司"),
    BEIJING("776","招商银行股份有限公司上海自贸实验区分行","***************","三星财产保险（中国）有限公司北京分公司"),
    SHANGHAI("1","招商银行股份有限公司上海自贸实验区分行","***************","三星财产保险（中国）有限公司");

    private final String comCode;
    private final String bankName;
    private final String bankAccount;
    private final String payee;

    PrintComEnum(String comCode,String bankName,String bankAccount,String payee){
        this.comCode = comCode;
        this.bankAccount=bankAccount;
        this.bankName=bankName;
        this.payee=payee;
    }

    private static final Map<String,PrintComEnum> COM_ENUM_MAP = new HashMap<>();

    static {
        for(PrintComEnum printComEnum:PrintComEnum.values()){
            COM_ENUM_MAP.put(printComEnum.getComCode(),printComEnum);
        }
    }

    public static PrintComEnum getByCode(String comCode){
        PrintComEnum printComEnum = COM_ENUM_MAP.get(comCode);
        if(ObjectUtil.isEmpty(printComEnum)){
            printComEnum = PrintComEnum.getByCode("1");
        }
        return printComEnum;
    }

    public String getComCode(){
        return comCode;
    }
    public String getBankName(){
        return bankName;
    }
    public String getBankAccount(){
        return bankAccount;
    }
    public String getPayee(){
        return payee;
    }

}
