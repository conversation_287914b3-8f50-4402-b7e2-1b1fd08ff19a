package com.paic.ncbs.claim.model.dto.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.page.Pager;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 批量支付明细
 */
public class BatchPaymentDetailDTO {

    /**
     * 报案来源
     */
    private String reportMode;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 机构编码
     */
    private String departmentAbbrCode;

    /**
     * 机构名称
     */
    private String departmentAbbrName;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 支付用途
     */
    private String paymentType;

    /**
     * 支付状态
     */
    private String mergePaymentStatus;

    /**
     * 金额
     */
    private BigDecimal paymentAmount;

    /**
     * 收款人
     */
    private String clientName;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String sysCtime;

    /**
     * 帐号类型:个人帐号=1,公司帐号=0
     */
    private String bankAccountAttribute;

    /**
     * 领款方式
     */
    private String payType;

    /**
     * 结算方式
     */
    private String collectPayApproach;

    /**
     * 客户开户银行码
     */
    private String clientBankCode;;

    /**
     * 客户开户银行
     */
    private String clientBankName;

    /**
     * 开户行明细码
     */
    private String bankDetailCode;

    /**
     * 开户行明细
     */
    private String bankDetail;

    /**
     * 收款人账号
     */
    private String clientBankAccount;

    /**
     * 结案时间-开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endCaseDateStart;

    /**
     * 结案时间-结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endCaseDateEnd;

    /**
     * 分页
     */
    private Pager pager;

    public String getReportMode() {
        return reportMode;
    }

    public void setReportMode(String reportMode) {
        this.reportMode = reportMode;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getDepartmentAbbrCode() {
        return departmentAbbrCode;
    }

    public void setDepartmentAbbrCode(String departmentAbbrCode) {
        this.departmentAbbrCode = departmentAbbrCode;
    }

    public String getDepartmentAbbrName() {
        return departmentAbbrName;
    }

    public void setDepartmentAbbrName(String departmentAbbrName) {
        this.departmentAbbrName = departmentAbbrName;
    }

    public String getPaySerialNo() {
        return paySerialNo;
    }

    public void setPaySerialNo(String paySerialNo) {
        this.paySerialNo = paySerialNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getMergePaymentStatus() {
        return mergePaymentStatus;
    }

    public void setMergePaymentStatus(String mergePaymentStatus) {
        this.mergePaymentStatus = mergePaymentStatus;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(String sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getBankAccountAttribute() {
        return bankAccountAttribute;
    }

    public void setBankAccountAttribute(String bankAccountAttribute) {
        this.bankAccountAttribute = bankAccountAttribute;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getClientBankCode() {
        return clientBankCode;
    }

    public void setClientBankCode(String clientBankCode) {
        this.clientBankCode = clientBankCode;
    }

    public String getCollectPayApproach() {
        return collectPayApproach;
    }

    public void setCollectPayApproach(String collectPayApproach) {
        this.collectPayApproach = collectPayApproach;
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName;
    }

    public String getBankDetailCode() {
        return bankDetailCode;
    }

    public void setBankDetailCode(String bankDetailCode) {
        this.bankDetailCode = bankDetailCode;
    }

    public String getBankDetail() {
        return bankDetail;
    }

    public void setBankDetail(String bankDetail) {
        this.bankDetail = bankDetail;
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount;
    }

    public Date getEndCaseDateStart() {
        return endCaseDateStart;
    }

    public void setEndCaseDateStart(Date endCaseDateStart) {
        this.endCaseDateStart = endCaseDateStart;
    }

    public Date getEndCaseDateEnd() {
        return endCaseDateEnd;
    }

    public void setEndCaseDateEnd(Date endCaseDateEnd) {
        this.endCaseDateEnd = endCaseDateEnd;
    }

    public Pager getPager() {
        return pager;
    }

    public void setPager(Pager pager) {
        this.pager = pager;
    }

}
