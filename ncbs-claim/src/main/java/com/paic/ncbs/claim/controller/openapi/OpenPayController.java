package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.openapi.FeeInvoiceBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.MergePaymentBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentDTO;
import com.paic.ncbs.claim.model.vo.openapi.ResponseResultVo;
import com.paic.ncbs.claim.service.openapi.OpenPayService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@Slf4j
@RestController
@RequestMapping("/public/pay")
public class OpenPayController extends BaseController {

    @Autowired
    private OpenPayService openPayService;

    /**
     * 收付费回调支付结果
     * @param dtos
     * @return
     */
    @ApiOperation("接收支付结果")
    @PostMapping(value = "/paymentBackResult")
    public ResponseResult<Object> handlePayBack(@RequestBody List<PaymentBackResultDTO> dtos) {
        openPayService.handlePayBack(dtos);
        return ResponseResult.success();
    }

    @ApiOperation("支付信息修改")
    @PostMapping(value = "/updatePayment")
    public ResponseResult<Object> updatePayment(@RequestBody @Validated PaymentDTO dto) {
        openPayService.updatePayment(dto);
        return ResponseResult.success();
    }

    @ApiOperation("进项税费用发票退回")
    @PostMapping(value = "/feeInvoiceBackResult")
    public ResponseResult<Object> feeInvoiceBackResult(@RequestBody @Validated FeeInvoiceBackResultDTO dto) {
        openPayService.feeInvoiceBackResult(dto);
        return ResponseResult.success();
    }

    @ApiOperation("接收批量支付合并支付结果")
    @PostMapping(value = "/mergePaymentBackResult")
    public ResponseResult<Object> mergePaymentBackResult(@RequestBody MergePaymentBackResultDTO dto) {
        openPayService.mergePaymentBackResult(dto);
        return ResponseResult.success();
    }
    /**
     * 其他费用结算通知接口-N01
     * 通知是否核销成功
     */
    @ApiOperation("接收其他费用结算通知结果")
    @PostMapping(value = "/otherFeeSettlementResult")
    public ResponseResultVo otherFeeSettlementResult(@RequestBody MergePaymentBackResultDTO dtos) {
        ResponseResultVo responseResult = new ResponseResultVo();
        try {
            openPayService.otherFeeSettlementResult(dtos);
            responseResult.setResponseCode(GlobalResultStatus.SUCCESS.getCode());
            responseResult.setResponseMsg(GlobalResultStatus.SUCCESS.getMsg());
        }catch (Exception e){
            log.error("其他费用结算结果处理异常",e);
            responseResult.setResponseCode(GlobalResultStatus.FAIL.getCode());
            responseResult.setResponseMsg(e.getMessage());
        }
        return responseResult;
    }

    /**
     * 其他费用结算通知接口-N01
     * 通知是否核销成功
     */
    @ApiOperation("接收共保摊回结算通知结果")
    @PostMapping(value = "/otherFeeSettlementResultCoins")
    public ResponseResultVo otherFeeSettlementResultCoins(@RequestBody MergePaymentBackResultDTO dtos) {
        ResponseResultVo responseResult = new ResponseResultVo();
        try {
            openPayService.otherFeeSettlementResultCoins(dtos);
            responseResult.setResponseCode(GlobalResultStatus.SUCCESS.getCode());
            responseResult.setResponseMsg(GlobalResultStatus.SUCCESS.getMsg());
        }catch (Exception e){
            log.error("其他费用结算结果处理异常",e);
            responseResult.setResponseCode(GlobalResultStatus.FAIL.getCode());
            responseResult.setResponseMsg(e.getMessage());
        }
        return responseResult;
    }

    @ApiOperation("共保合并支付补推接口")
    @GetMapping(value = "/coinsMergePaymentReturn")
    public ResponseResult<Object> coinsMergePaymentReturn(@RequestParam("batchNo") String batchNo) {
        openPayService.coinsMergePaymentReturn(batchNo);
        return ResponseResult.success();
    }
}
