package com.paic.ncbs.claim.model.dto.pay;

import java.math.BigDecimal;

/**
 * 批量支付操作记录表DTO
 */
public class BatchPayOperationRecordDTO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 删除标记
     */
    private String delFlag;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 节点
     */
    private String operationNode;

    /**
     * 收款人
     */
    private String payeeName;

    /**
     * 收款账号
     */
    private String payeeBankAccount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private BigDecimal sysCtime;
    /**
     * 修改时间
     */
    private String sysUtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getOperationNode() {
        return operationNode;
    }

    public void setOperationNode(String operationNode) {
        this.operationNode = operationNode;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getPayeeBankAccount() {
        return payeeBankAccount;
    }

    public void setPayeeBankAccount(String payeeBankAccount) {
        this.payeeBankAccount = payeeBankAccount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public BigDecimal getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(BigDecimal sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(String sysUtime) {
        this.sysUtime = sysUtime;
    }

}
