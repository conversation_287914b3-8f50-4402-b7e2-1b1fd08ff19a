package com.paic.ncbs.claim.dao.mapper.coinsurance;

import com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmortizationVo;
import com.paic.ncbs.claim.dao.entity.coinsurance.CoinsSearchVo;
import com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 共保摊回记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface RecoveryInfoMapper extends BaseMapper<RecoveryInfo> {

    void insertList(@Param("recoveryInfoList") List<RecoveryInfo> recoveryInfoList);

    List<CoinsAmortizationVo> selectCoinsList(CoinsSearchVo coinsSearchVo);

    List<CoinsAmortizationVo> selectByIdList(List<String> idList);

    void updateList(@Param("coinsAmortizationVoList") List<CoinsAmortizationVo> coinsAmortizationVoList,
                    @Param("idRecoveryRecord") String idRecoveryRecord);

    List<RecoveryInfo> selectByIdRecovery(@Param("idRecoveryRecord") String idRecoveryRecord);

    void updateByRecovery(@Param("idRecoveryRecord") String batchNo,@Param("paymentStatus") String paymentStatus);

    List<CoinsAmortizationVo> selectAmortByRecordId(@Param("idRecoveryRecord") String idRecoveryRecord);
}
