package com.paic.ncbs.claim.service.instalment.impl;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.vo.instalment.PaymentItemDownFinanceVO;
import com.paic.ncbs.claim.model.vo.instalment.PaymentItemEditableVO;
import com.paic.ncbs.claim.service.instalment.InstalmentService;
import com.paic.ncbs.claim.common.constant.InstalmentConst;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.instalment.InstalmentFailDTO;
import com.paic.ncbs.claim.model.vo.instalment.PaymentInfoEditableVO;
import com.paic.ncbs.claim.service.instalment.InstalmentFailService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service("instalmentService")
@Transactional
public class InstalmentServiceImpl implements InstalmentService {

    @Autowired
    public InstalmentFailService instalmentFailService;

    @Autowired
    public PaymentInfoService paymentInfoService;

    @Override
    public boolean checkIfCaseContainsInstalment(String reportNo, Integer caseTimes) {
        LogUtil.audit("开始检查案件是否开过通知单reportNo={}", reportNo);
        List<InstalmentFailDTO> list = instalmentFailService.findByReportNoAndCaseTimes(reportNo, caseTimes);
        boolean hasGenerated = list.stream().anyMatch(i -> Objects.nonNull(i.getQrCodeUrl()));
        LogUtil.audit("案件是否开过通知单结果={}#reportNo={}", hasGenerated, reportNo);
        return hasGenerated;
    }

    @Override
    public List<PaymentItemEditableVO> isAllowChangePaymentItem(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        List<PaymentItemDownFinanceVO> paymentItemDownFinanceList = findDownFinancedPaymentItem(reportNo, caseTimes);
        boolean anyDownFinance = paymentItemDownFinanceList.stream().anyMatch(PaymentItemDownFinanceVO::getDownFinanced);
        if (anyDownFinance) {
            return paymentItemDownFinanceList.stream().map(tmp -> {
                PaymentItemEditableVO paymentItemEditable = new PaymentItemEditableVO();
                paymentItemEditable.setAllowDelete(Boolean.FALSE);
                paymentItemEditable.setAllowModify(!tmp.getDownFinanced());
                paymentItemEditable.setIdClmPaymentItem(tmp.getPaymentItem().getIdClmPaymentItem());
                return paymentItemEditable;
            }).collect(Collectors.toList());
        } else {
            return paymentItemDownFinanceList.stream().map(tmp -> {
                PaymentItemEditableVO paymentItemEditable = new PaymentItemEditableVO();
                paymentItemEditable.setAllowModify(Boolean.TRUE);
                paymentItemEditable.setAllowDelete(Boolean.TRUE);
                paymentItemEditable.setIdClmPaymentItem(tmp.getPaymentItem().getIdClmPaymentItem());
                return paymentItemEditable;
            }).collect(Collectors.toList());
        }
    }
    @Override
    public List<PaymentInfoEditableVO> isAllowChangePaymentInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        List<PaymentItemDownFinanceVO> paymentItemDownFinanceList = findDownFinancedPaymentItem(reportNo, caseTimes);
        List<PaymentItemDownFinanceVO> downFinancedItem = paymentItemDownFinanceList.stream().filter(PaymentItemDownFinanceVO::getDownFinanced).collect(Collectors.toList());
        PaymentInfoDTO param = new PaymentInfoDTO();
        param.setReportNo(reportNo);
        param.setCaseTimes(caseTimes);
        List<PaymentItemComData> paymentInfoList = paymentInfoService.getPaymentInfoList(param);
        return paymentInfoList.stream().map(info -> {
            PaymentInfoEditableVO editable = new PaymentInfoEditableVO();
            editable.setIdClmPaymentInfo(info.getIdClmPaymentInfo());
            boolean isDownFinanced = downFinancedItem.stream().anyMatch(item -> info.getIdClmPaymentInfo().equals(item.getPaymentItem().getIdClmPaymentInfo()));
            editable.setAllowDelete(!isDownFinanced);
            editable.setAllowModify(!isDownFinanced);
            return editable;
        }).collect(Collectors.toList());
    }


    @Override
    public List<PaymentItemDownFinanceVO> findDownFinancedPaymentItem(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        LogUtil.audit("开始查找下发支付的支付项#report={}", reportNo);
        List<PaymentItemComData> paymentItems = null;
        List<PaymentItemDownFinanceVO> paymentItemDownFinanceList = new ArrayList<>();
        if (!isGoThroughNewProcess()) {
            LogUtil.audit("开关未打开#reportNo={}", reportNo);
            return Collections.emptyList();
        }

        boolean iGenerateNotice = checkIfCaseGenerateNotice(reportNo, caseTimes);
        if (!iGenerateNotice) {
            LogUtil.audit("案件未开过通知单reportNo={}", reportNo);
            return Collections.emptyList();
        }
        paymentItems = instalmentFailService.findAllPaymentItem(reportNo, caseTimes);

        if (CollectionUtils.isEmpty(paymentItems)) {
            LogUtil.audit("获取的支付项数量为空reportNo={}", reportNo);
            return Collections.emptyList();
        }

        for (PaymentItemComData paymentItem : paymentItems) {
            LogUtil.audit("保单号={}支付项状态为={}", paymentItem.getPolicyNo(), paymentItem.getPaymentItemStatus());
            PaymentItemDownFinanceVO tmp = new PaymentItemDownFinanceVO();
            tmp.setPaymentItem(paymentItem);
            tmp.setDownFinanced(Boolean.FALSE);

            if (isPaymentItemDownFinance(paymentItem)) {
                tmp.setDownFinanced(Boolean.TRUE);
            }
            paymentItemDownFinanceList.add(tmp);
        }
        return paymentItemDownFinanceList;
    }

    public boolean checkIfCaseGenerateNotice(String reportNo, Integer caseTimes) {
        LogUtil.audit("开始检查案件是否开过通知单reportNo={}", reportNo);
        List<InstalmentFailDTO> list = instalmentFailService.findByReportNoAndCaseTimes(reportNo, caseTimes);
        boolean hasGenerated = list.stream().anyMatch(i -> Objects.nonNull(i.getQrCodeUrl()));
        LogUtil.audit("案件是否开过通知单结果={}#reportNo={}", hasGenerated, reportNo);
        return hasGenerated;
    }

    public boolean isGoThroughNewProcess() {

        LogUtil.audit("先行下发支付开关已关闭");
        return false;
    }

    private boolean isPaymentItemDownFinance(PaymentItemComData paymentItem) {
        return Stream.of(InstalmentConst.PAYMENT_TYPE_SHARED_LIST).noneMatch(paymentType -> paymentType.equalsIgnoreCase(paymentItem.getPaymentType()))
                && !SettleConst.CLAIM_TYPE_PRE_PAY.equals(paymentItem.getClaimType()) && !InstalmentConst.PAYMENT_ITEM_STATUS_TEMPORARY.equals(paymentItem.getPaymentItemStatus());
    }

}
