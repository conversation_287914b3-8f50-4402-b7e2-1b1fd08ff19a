package com.paic.ncbs.claim.controller.pet;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.pet.PetHospitalDTO;
import com.paic.ncbs.claim.model.vo.pet.PetHospitalVO;
import com.paic.ncbs.claim.model.vo.pet.PetInjureVO;
import com.paic.ncbs.claim.service.pet.PetHospitalService;
import com.paic.ncbs.claim.service.pet.PetInjureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Api(tags = "宠物医院")
@RestController
@RequestMapping("/pet/petHospitalAction")
public class PetHospitalController {

	@Autowired
	private PetHospitalService petHospitalService;

	@ApiOperation("保存医院")
	@PostMapping("/addPetHospitalList")
	public ResponseResult addPetHospitalList(@RequestBody List<PetHospitalDTO> petHospitalList){
		petHospitalService.addPetHospitalList(petHospitalList);
		return ResponseResult.success();
	}

	@ApiOperation("获取宠物医院")
	@PostMapping("/getPetHospitalList")
	public ResponseResult<List<PetHospitalVO>> getPetHospitalList(@RequestBody PetHospitalDTO petHospitalDTO) {
		petHospitalDTO.setCountryCode(null);
		List<PetHospitalDTO> petHospitalList = petHospitalService.getPetHospitalList(petHospitalDTO);
		List<PetHospitalVO> petHospitalVOList = new ArrayList<>();
		for (PetHospitalDTO dto : petHospitalList) {
			petHospitalVOList.add(new PetHospitalVO(dto));
		}
		return ResponseResult.success(petHospitalVOList);
	}

}

