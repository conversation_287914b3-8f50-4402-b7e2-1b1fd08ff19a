package com.paic.ncbs.claim.dao.mapper.coinsurance;

import com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmortizationVo;
import com.paic.ncbs.claim.dao.entity.coinsurance.CoinsInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.coinsurance.CoinsSearchVo;

import java.util.List;

/**
 * <p>
 * 共保摊回记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface CoinsInfoMapper extends BaseMapper<CoinsInfo> {

    void insertList(List<CoinsInfo> coinsInfoList);

    List<CoinsAmortizationVo> selectCoinsList(CoinsSearchVo coinsSearchVo);

    List<CoinsAmortizationVo> selectByIdList(List<String> idList);
}
