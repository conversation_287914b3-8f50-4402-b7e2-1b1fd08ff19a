package com.paic.ncbs.claim.dao.entity.qualitychecke;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 案件质检信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Getter
@Setter
@TableName("clms_quality_info")
public class ClmsQualityInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField("id")
    private String id;

    /**
     * 序号
     */
    @TableField("serial_no")
    private String serialNo;

    /**
     * 报案号
     */
    @ExcelProperty("报案号")
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Short caseTimes;

    /**
     * 立案号
     */
    @TableField("regist_no")
    private String registNo;

    /**
     * 处理机构
     */
    @TableField("handle_com")
    private String handleCom;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 结案日期
     */
    @TableField("close_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime closeDate;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 案件性质
     */
    @TableField("case_nature")
    private String caseNature;

    /**
     * 出险人
     */
    @TableField("insured_person")
    private String insuredPerson;

    /**
     * 案件金额
     */
    @TableField("case_amount")
    private BigDecimal caseAmount;

    /**
     * 立案人员
     */
    @TableField("case_creator")
    private String caseCreator;

    /**
     * 调查跟进人员
     */
    @TableField("investigator")
    private String investigator;

    /**
     * 理算人员
     */
    @TableField("adjuster")
    private String adjuster;

    /**
     * 核赔人员
     */
    @TableField("claim_approver")
    private String claimApprover;

    /**
     * 承保机构
     */
    @TableField("com_code")
    private String comCode;

    /**
     * 理赔机构
     */
    @TableField("claim_org")
    private String claimOrg;

    /**
     * 质检序号
     */
    @TableField("qiser_no")
    private String qiserNo;

    /**
     * 质检发起人
     */
    @TableField("qinitiator")
    private String qinitiator;

    /**
     * 质检发起时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime startTime;

    /**
     * 质检结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime endTime;

    /**
     * 质检人
     */
    @TableField("qinspector")
    private String qinspector;

    /**
     * 质检结果
     */
    @TableField("quality_result")
    private String qualityResult;

    /**
     * 质检是否结束(0-未结束,1-已结束)
     */
    @TableField("is_end")
    private String isEnd;

    /**
     * 赔付金额/估损金额
     */
    @TableField("payment_amount")
    private BigDecimal paymentAmount;

    /**
     * 任务状态(0:未处理，1:处理中、2:处理结束)
     */
    @TableField("task_status")
    private String taskStatus;

    /**
     * 批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 导入时间
     */
    @TableField("import_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime importTime;

    /**
     * 质检意见详情说明
     */
    @TableField("opinion_detail")
    private String opinionDetail;

    /**
     * 作业人归属（tpa）
     */
    @TableField("operator_tpa")
    private String operatorTpa;

    /**
     * 处理时间
     */
    @TableField("process_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime processTime;

    /**
     * 处理人
     */
    @ExcelProperty("质检人员工号")
    @TableField("handler")
    private String handler;

    /**
     * 环节（0-质检/1-质检审核）
     */
    @TableField("node")
    private String node;

    /**
     * 轨迹审批意见
     */
    @TableField("locus_idea")
    private String locusIdea;

    /**
     * 轨迹详细意见
     */
    @TableField("locus_detail")
    private String locusDetail;

    /**
     * 审批意见:0-通过 1-退回； 2-移交审批
     */
    @TableField("approval_opinion")
    private String approvalOpinion;

    /**
     * 审批详情意见
     */
    @TableField("approval_desc")
    private String approvalDesc;

    /**
     * 移交审批人
     */
    @TableField("atransfer_person")
    private String atransferPerson;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sysUtime;

    /**
     * 质检标准
     */
    @TableField("qualityType")
    private String qualityType;

    /**
     * 是否global案件0-否，1-是
     */
    @TableField("isglobal")
    private String isglobal;

}
