package com.paic.ncbs.claim.controller.who;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/app/caseProcessAction")
public class CaseProcessController extends BaseController {

	@Autowired
	private CaseProcessService caseProcessService;

	
	@RequestMapping(value = "/isDeletePaymentItem", method = RequestMethod.POST)
	public ResponseResult<String> isDeletePaymentItem(CaseProcessDTO caseProcess) {

		LogUtil.audit("报案号={},进入判断是否可修改支付项接口", caseProcess.getReportNo());

		return ResponseResult.success(caseProcessService.isDeletePaymentItem(caseProcess));

	}

}
