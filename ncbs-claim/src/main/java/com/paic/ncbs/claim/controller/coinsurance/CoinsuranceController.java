package com.paic.ncbs.claim.controller.coinsurance;

import com.github.pagehelper.PageInfo;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.entity.coinsurance.AmortSearcherVo;
import com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmorDetailVo;
import com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmortizationVo;
import com.paic.ncbs.claim.dao.entity.coinsurance.CoinsSearchVo;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.coinsurance.CoinsuranceService;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/coinsurance")
@Api(tags = {"共保"})
@Slf4j
@RefreshScope
public class CoinsuranceController {
    @Autowired
    private CoinsuranceService coinsuranceService;
    @Autowired
    private PrintCoreService printCoreService;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

    //计算共保摊回数据
    @GetMapping("/settleCoinsuranceList")
    public ResponseResult<List<CoinsAmortizationVo>> settleCoinsuranceList(@RequestParam("reportNo") String reportNo,
                                                                           @RequestParam("caseTimes") Integer caseTimes,
                                                                           @RequestParam("payAmount") BigDecimal payAmount,
                                                                           @RequestParam("payType") String payType){
//        return ResponseResult.success(coinsuranceService.settleCoinsuranceList(settleCoinsVo));
        return ResponseResult.success(coinsuranceService.settleCoinsuranceList(reportNo,caseTimes,payAmount,payType));
    }

    //共保摊回通知书打印
    @GetMapping("/coinsNoticePrint")
    public void coinsNoticePrint(@RequestParam("reportNo") String reportNo,
                                   @RequestParam("caseTimes") Integer caseTimes,
                                   @RequestParam("claimType") String claimType,
                                 @RequestParam("subTimes") Integer subTimes) {
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        printCoreService.saveCoinsFileAsync(reportNo,caseTimes,claimType,userInfoDTO,subTimes);
//        coinsuranceService.addRecoveryInfo(reportNo,caseTimes,claimType,null);
    }

    //查询共保摊回记录
    @PostMapping("/getCoinsAmortizationList")
    public ResponseResult<PageInfo<CoinsAmortizationVo>> getCoinsAmortizationList(@RequestBody CoinsSearchVo coinsSearchVo){
        return ResponseResult.success(coinsuranceService.getCoinsAmortizationList(coinsSearchVo));
    }

    //查询共保摊回详情
    @PostMapping("/getCoinsAmorDetail")
    public CoinsAmorDetailVo getCoinsAmorDetail(@RequestBody AmortSearcherVo amortSearcherVo){
        return coinsuranceService.getCoinsAmorDetail(amortSearcherVo);
    }

    //共保摊回提交前校验
    @PostMapping("/checkAmor")
    public ResponseResult checkAmor(@RequestBody CoinsAmorDetailVo coinsAmorDetailVo){
        return coinsuranceService.checkAmor(coinsAmorDetailVo);
    }

    //共保摊回提交
    @PostMapping("/submitAmor")
    public ResponseResult submitAmor(@RequestBody CoinsAmorDetailVo coinsAmorDetailVo){
        return coinsuranceService.submitAmor(coinsAmorDetailVo);
    }

    //查询共保摊回详情
    @GetMapping("/sendPayment")
    public void sendPayment(@RequestParam("reportNo") String reportNo,@RequestParam("caseTimes") Integer caseTimes){
        payInfoNoticeThirdPartyCoreSAO.noticePayment(reportNo,caseTimes,null,false,false);
    }
}
