package com.paic.ncbs.claim.service.secondunderwriting;

import com.paic.ncbs.claim.dao.entity.clms.ClmsPolicyHistoryUwInfoEntity;
import com.paic.ncbs.claim.model.dto.secondunderwriting.ClmsPolicyHistoryUwInfoDTO;

import java.util.List;

/**
 * 理赔保单历史保结信息(ClmsPolicyHistoryUwInfoEntity)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-28 15:28:08
 */
public interface ClmsPolicyHistoryUwInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsPolicyHistoryUwInfoEntity queryById(String id);

    /**
     * 新增数据
     *
     * @param clmsPolicyHistoryUwInfoEntity 实例对象
     * @return 实例对象
     */
    ClmsPolicyHistoryUwInfoEntity insert(ClmsPolicyHistoryUwInfoEntity clmsPolicyHistoryUwInfoEntity);

    /**
     * 批量插入
     * @param list
     */
    void saveBatch(List<ClmsPolicyHistoryUwInfoDTO> list,String reportNo);

    /**
     * 修改数据
     *
     * @param clmsPolicyHistoryUwInfoEntity 实例对象
     * @return 实例对象
     */
    ClmsPolicyHistoryUwInfoEntity update(ClmsPolicyHistoryUwInfoEntity clmsPolicyHistoryUwInfoEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    /**
     * 保单历史核保信息
     * @param reportNo
     * @return
     */
    List<ClmsPolicyHistoryUwInfoDTO> getClmsPolicyHistoryUwInfoList(String reportNo);


    /**
     * 根据报案号查询保单理赔历史核保信息
     * @param policyNoList
     */
   void saveClaimPolicyUwInfo(List<String> policyNoList);
}
