package com.paic.ncbs.claim.dao.mapper.pay;

import com.paic.ncbs.claim.model.dto.pay.BatchPayOperationRecordDTO;
import com.paic.ncbs.claim.model.vo.pay.BatchPayOperationRecordVO;

import java.util.List;

/**
 * 批量支付操作记录表(clm_batch_pay_operation_record)数据库访问层
 */
public interface ClmBatchPayOperationRecordMapper {

    /**
     * 通过batchNo查询操作记录
     *
     * @param batchNo 批次号
     * @return list
     */
    List<BatchPayOperationRecordVO> queryOperationRecordByBatchNo(String batchNo);

    int addClmBatchPayOperationRecord(BatchPayOperationRecordDTO batchPayOperationRecordDTO);

}

