package com.paic.ncbs.claim.dao.mapper.pay;

import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryInfo;
import com.paic.ncbs.claim.dao.entity.coinsurance.SettleCoinsReqVo;
import com.paic.ncbs.claim.model.dto.other.AsynchronousCompensationJobExtDTO;
import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.vo.pay.BatchPaymentDetailVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@MapperScan
public interface PaymentItemMapper {

    void addPaymentItem(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getPaymentItem(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getPaymentItemsByIds(List<String> paymentItemIds);

    List<PaymentItemDTO> getHisPaymentItem(PaymentItemDTO paymentItemDTO);

    void updatePaymentItem(PaymentItemDTO paymentItemDTO);

    void delPaymentItem(PaymentItemDTO paymentItemDTO);

    void addPaymentItemList(@Param("paymentItemList") List<PaymentItemDTO> paymentItemList);

    void updatePaymentItemStatus(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getPrePaymentItem(PaymentItemDTO paymentItemDTO);

    String getCompensateNoByPayment(PaymentItemDTO itemDTO);

    BigDecimal getSumAdvancePay(PaymentItemDTO itemDTO);

    BigDecimal getSumPrePay(PaymentItemDTO itemDTO);

    BigDecimal getSumBeforePrePay(PaymentItemDTO itemDTO);

    int getPaymentItemCount(PaymentItemDTO itemDTO);

    int getNoPayItemByCompensateNo(@Param("compensateNo") String compensateNo);

    int getNoPayItemByReportNo(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes);

    void insertJobInfo(AsynchronousCompensationJobExtDTO asynchronousCompensationJobDTO);

    /**
     *  根据报案号和赔付次数（不等于当次的次数）
     * @param paymentItemDTO 入参
     * @return 出参
     */
    List<PaymentItemDTO> getPaymentItemByReportNo(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getPaymentItemPayByReportNoAndPayType(PaymentItemDTO paymentItemDTO);

    BigDecimal getLastPaymentAmount(PaymentItemDTO paymentItemDTO);

    /**
     * 查询监管所需赔付中间表数据
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<CompensationIntermediateData> getCompensationIntermediateData(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 查询没有客户号的数据进行补充
     * @return
     */
    List<PaymentItemDTO> queryCustomerNoToSupplementedFromItem();

    /**
     * 批量更新
     * @param paymentItemList
     */
    void batchUpdate(@Param("paymentItemList") List<PaymentItemDTO> paymentItemList);

    String getPayDateByReportNo(String reportNo);

    /**
     * 获取指令支付信息
     */
    PaymentItemDTO getOrderPayMentInfo(String reportNo,Integer caseTimes);

    int getPrePayCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 查询超过配置天数还未通知支付的数据
     * @param updatedDate
     * @return
     */
    List<PaymentItemDTO> getBatchOrderPayDataInfo(Date updatedDate);

   BigDecimal getSettleAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * TPA全包查询支付项目信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<PaymentItemComData> getTPAPaymentItemDTO(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void updatePaymentItemVoucherUrl(PaymentItemDTO paymentItemDTO);
    List<PaymentItemDTO> getAllFeeByPolicyNo(PaymentItemDTO paymentItemDTO);
    List<PaymentItemDTO> getNewEstimateFeeByPolicyNo(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getNewPaymentByPolicyNo(PaymentItemDTO paymentItemDTO);
    List<PaymentItemDTO> getNewEstimatePaymentByPolicyNo(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getPaymentItemList(PaymentItemDTO paymentItemDTO);

    void updatePaymentItemByInfo(PaymentInfoDTO paymentInfoDTO);

    void delPaymentItemByInfo(@Param("idClmPaymentInfo") String idClmPaymentInfo);

    int isPay(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes);

    List<PaymentItemDTO> getMergePayAccountList(@Param("yesterday") Date yesterday);

    BatchPaymentMainInfo getPaymentMainInfo(@Param("productCode") String productCode,
                                            @Param("clientBankAccount") String clientBankAccount,
                                            @Param("yesterday") Date yesterday);

    List<BatchPaymentDetailInfo> getMergePaymentList(@Param("productCode") String productCode,
                                                     @Param("clientBankAccount") String clientBankAccount,
                                                     @Param("yesterday") Date yesterday);
    List<String> getMergePayCompensateId(@Param("failDate") Date failDate);
    void updateMergePaymentId(@Param("list") List<String> list);

    int addClmsMergePayment(MergePaymentDTO mergePaymentDTO);

    void updatePaymentItemId(@Param("id") Integer id, @Param("list") List<BatchPaymentDetailInfo> list);

    void updateBatchPaymentItemId(@Param("id") Integer id, @Param("list") List<String> list);

    void updateMergePaymentItemStatus(@Param("batchNo") String batchNo, @Param("mergePaymentStatus") String mergePaymentStatus);

    void updateMergePaymentStatus(@Param("batchNo") String batchNo, @Param("mergePaymentStatus") String mergePaymentStatus);

    void updateMergeSettlementStatus(@Param("batchNo") String batchNo, @Param("settlementStatus") String settlementStatus, @Param("errorMsg") String errorMsg);

    List<MergePaymentVO> getPackagePaymentAccountList(@Param("list") List<String> list);

    /**
     * @Description: 查询费用发票号码
     * @param list
     * @return
     * 返回类型  InvoiceInfoDTO
     */
    List<InvoiceInfoDTO> getInvoiceNo(@Param("list") List<String> list);

    List<BatchPaymentDetailInfo> getPackagePaymentList(@Param("clientBankAccount") String clientBankAccount,
                                                       @Param("list") List<String> list);

    BatchPaymentMainInfo getPackagePaymentMainInfo(@Param("list") List<String> list);

    MergePaymentVO queryMergePaymentByBatchNo(@Param("batchNo") String batchNo);

    List<BatchPaymentDetailInfo> getPackagePaymentListByBatchNo(@Param("batchNo") String batchNo);

    BigDecimal getSumPreFeeByReportNo(PaymentItemDTO itemDTO);

    List<PaymentItemDTO> getPaymentItemNoDraft(PaymentItemDTO paymentItemDTO);
    /**
     * 根据批次号更新支付项目状态
     * @param batchNo
     * @param paymentItemStatus
     */
    void updatePaymentItemStatusByBatchNo(@Param("batchNo") String batchNo, @Param("paymentItemStatus") String paymentItemStatus);
    /**
     * 删除支付项、支付条款及责任
     * @param idClmPaymentItem
     * @param paymentItemStatus
     */
    void delPaymentPlanAndDutyById(@Param("idClmPaymentItem") String idClmPaymentItem, @Param("paymentItemStatus") String paymentItemStatus);
    /**
     * 获取所有支付项（包含追偿）
     * @param paymentItemDTO
     * @return
     */
    List<PaymentItemDTO> getAllPaymentItem(PaymentItemDTO paymentItemDTO);

    SettleCoinsReqVo getCoinsSettleFeeVo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes
            , @Param("claimType") String claimType, @Param("subTimes")Integer subTimes);

    SettleCoinsReqVo getCoinsSettleAmountVo(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes
            ,@Param("claimType") String claimType, @Param("subTimes")Integer subTimes);

    List<PaymentItemDTO> selectByRecoveryInfo(@Param("recoveryInfoList") List<RecoveryInfo> recoveryInfoList);

    void updateItemStatusByRecovery(@Param("recoveryInfoList") List<RecoveryInfo> recoveryInfoList,@Param("paymentStatus") String paymentStatus);

    PaymentItemDTO selectByItemId(@Param("idClmPaymentItem") String idClmPaymentItem);

    void updatePaymentItemIdByItem(@Param("id") int id,@Param("idClmPaymentItem") String idClmPaymentItem);

    List<PaymentItemDTO> getPaymentItemFee(PaymentItemDTO paymentItemDTO);

    void updateOtherPaymentStatus(@Param("idClmPaymentItem") String idClmPaymentItem,@Param("paymentStatus") String paymentStatus);

    List<PaymentItemDTO> getBackMergePaymentItem(@Param("batchNo") String batchNo);

    /**
     * 批量支付根据批次号查询 clm_payment_item id
     * @param batchNo
     * @return
     */
    List<String> getIdByBatchNo(@Param("batchNo") String batchNo);


    /**
     * 更新理赔合并支付表 clms_merge_payment
     * @param dto
     * @return
     */
    void updateMergePaymentInfo(BatchPaymentDTO dto);

    PaymentItemDTO getAddressInfo(@Param("batchNo") String batchNo);

    List<BatchPaymentDetailVO> queryBatchPaymentResult(BatchPaymentDetailDTO dto);

    /**
     * 获取历史费用之和
     */
    BigDecimal getHisFeeByReport(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}
