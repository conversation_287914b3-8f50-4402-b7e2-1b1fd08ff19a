package com.paic.ncbs.claim.controller.pet;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.pet.IChongReportVO;
import com.paic.ncbs.claim.model.vo.report.TelReportResponseVO;
import com.paic.ncbs.claim.service.pet.PetReportService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 宠物险报案
 */
@Api(tags = "宠物险报案信息")
@RestController
@RequestMapping("/public/petReportAction")
public class PetReportController {
    
    @Autowired
    private PetReportService petReportService;

    /**
     *  宠物险报案接口
     * @param iChongReportVO
     * @return
     */
    @PostMapping(value = "/savePetReport")
    public ResponseResult<TelReportResponseVO> savePetReport(@RequestBody IChongReportVO iChongReportVO) {
        return ResponseResult.success(petReportService.savePetReport(iChongReportVO));
    }

}