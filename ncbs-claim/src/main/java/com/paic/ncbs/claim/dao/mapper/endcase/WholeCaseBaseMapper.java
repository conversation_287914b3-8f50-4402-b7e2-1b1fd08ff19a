package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.model.dto.copypolicy.GlobalSettleDto;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface WholeCaseBaseMapper extends BaseDao<WholeCaseBaseEntity> {

    List<WholeCaseBaseEntity> getWholeCaseBaseByReport(String report);

    WholeCaseBaseDTO getWholeCaseBase(String reportNo, int caseTimes);

    WholeCaseBaseEntity getWholeCaseBaseByReportNoAndCaseTimes(@Param("reportNo") String reportNo,
                                                               @Param("caseTimes") Integer caseTimes);

    List<String> getClaimStatusByReportNos(@Param("reportNos") List<String> reportNos);

    // duty 方法名已更改  getWholeCaseBase ->  getWholeCaseBase2
    public WholeCaseBaseDTO getWholeCaseBase2(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    String getCaseTypeCode(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public void modifyHugeAccident(WholeCaseBaseDTO wholeCaseBaseDTO);

    public void modifyWholeCaseBase(WholeCaseBaseDTO wholeCaseBaseDTO);

    Date getReportDate(String reportNo);

    String getWholeCaseStatus(String reportNo, Integer caseTimes);

    WholeCaseBaseDTO getHugeInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void modifyIsSelfHelp(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("isSelfHelp") String isSelfHelp, @Param("userId") String userId);

    WholeCaseBaseDTO getWholeCaseIndemnityStatus(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    WholeCaseBaseDTO getWholeCaseIndemnityStatusZt(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


    void modifyWholeCaseEndCaseNo( @Param("endCaseNo")  String endCaseNo,@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void insertList(@Param("list") List<WholeCaseBaseEntity> list);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    /**
     * 查询赔付次数
     * @param reportNo
     * @return
     */
    Integer getCaseTimes(String reportNo);

    WholeCaseBaseDTO getCusWholeCaseInfo(String reportNo);

    /**
     * 查询待送收付案件列表
     * @return
     */
    List<WholeCaseBaseDTO> getWholeCaseBaseByPendPay();

    /**
     * 更新材料齐全时间
     * @param caseBaseDTO
     */
    void updateDocFullDate(WholeCaseBaseDTO caseBaseDTO);

    Integer getverfiyCodeCount(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes);
    /**
     * 修改案件是否人伤跟踪
     *
     * @param isPersonTrace 1-是，0-否
     */
    void updateIsPersonTrace(String reportNo, Integer caseTimes, String isPersonTrace);

    /**
     * 修改案件状态
     */
    void updateCaseStatus(WholeCaseBaseDTO caseBaseDTO);

    GlobalSettleDto selectGlobalSettle(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes);
}