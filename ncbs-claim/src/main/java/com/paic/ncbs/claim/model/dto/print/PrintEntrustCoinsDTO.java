package com.paic.ncbs.claim.model.dto.print;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PrintEntrustCoinsDTO {
    private String printType; // 0 费用， 1 赔款
    private String coinsComName;// 共保公司名称
    private String coinsName;//  被保险人名称
    private String productName;//  险种名称
    private String policyNo;//  保单号
    private String reportNo;//  报案号
    private String accidentDate;//  事故日期
    private String totalAmount;//  总额 赔款（总额），费用（总额，专票时不含税金额，普票时含税金额）
    private String reinsureScale;//  共保份额
    private String amount;//  赔款（份额）
    private String payee;//  收款人
    private String bankAccount;//  账号
    private String bankName;//  收款行
    private String updateBy;//  提交人
    private String email;//  邮箱
    private String printDate;//  生成日期 yyyy年mm月dd日
    private String fileId;
    private Integer caseTimes;
    private String departmentCode;
    private String claimType;//1赔付 2预赔
}
