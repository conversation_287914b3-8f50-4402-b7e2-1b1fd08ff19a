package com.paic.ncbs.claim.model.dto.copypolicy;

import lombok.Data;

@Data
public class GlobalSettleDto {
    private String fkRc00RcivYear;//报案号
    private Long fkRc00RcivSeqNum;//报案号
    private String fkRcivDate;//立案号
    private Long fkRcivSeqNum;//立案号
    private Long fkAgrmSeqNum;//立案号
    private String terCls;//结案类型（1 Payment Closing（支付结案）5 Claim Withdrawal(理赔注销） 6 “0” Claim Amount （0结案））
    private String notCoverableCode;//结案类型不为1时必传（结案类型为5时: 1:Error Acceptance （错误报案）2: Double Acceptance （重复报案）3: Wrong Policy Acceptance （错误保单）4: Wrong Confirmation（错误报案信息）结案类型为6时:1 :Claim Abandonment（客户放弃索赔）2: Not Our Fault （非理赔原因0结案）3: Under Deductible （小于免赔额））
    private ClaimDetailCasualty claimDetailCasualty;//人伤 团意险必传
    private ClaimDetailLiability claimDetailLiability;//责任，雇主必传
    private GlobalPaymentInfo paymentInfo;
    private GlobalRecipientInfo recipientInfo;
    private BankInfo bankInfo;

}
