package com.paic.ncbs.claim.service.pay;

import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.model.vo.pay.*;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;

import java.util.Date;
import java.util.List;

public interface BatchPayService {

    /**
     * 获取批量支付打包查询页面的机构列表
     * @param
     */
    List<DepartmentVO> getSelectDepartmentList();

    /**
     * 批量支付明细查询
     * @param dto
     */
    BatchPaymentDetailResult queryPaymentDetails(BatchPaymentDetailDTO dto);

    /**
     * 批量打包
     * @param dto
     */
    BatchPackagePaymentVO packagePayment(BatchPackagePaymentDTO dto);

    /**
     * 查询已打包的数据
     * @param dto
     */
    BatchPaymentResult queryBatchPayDetails(BatchPaymentDTO dto);

    /**
     * 发起支付
     * @param dto
     */
    void sendPayment(BatchPaymentDTO dto);

    /**
     * 解包
     * @param dto
     */
    void unpackPayment(BatchPaymentDTO dto);

    /**
     * 查询详情信息
     * @param dto
     */
    BatchPaymentVO queryMergePaymentByBatchNo(BatchPaymentDTO dto);

    /**
     * 批量支付详情列表
     * @param dto
     */
    BatchPaymentDetailResult queryPaymentListByBatchNo(BatchPaymentDetailDTO dto);

    /**
     * 修改收款人信息
     * @param dto
     */
    void updateBatchPaymentInfo(BatchPaymentDTO dto);


    BatchPaymentDetailResult queryBatchPaymentInfo(BatchPaymentDetailDTO dto);

}
