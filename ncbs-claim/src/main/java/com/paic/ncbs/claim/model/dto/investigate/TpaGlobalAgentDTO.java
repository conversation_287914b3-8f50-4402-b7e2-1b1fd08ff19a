package com.paic.ncbs.claim.model.dto.investigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("查询中台调查结构")
public class TpaGlobalAgentDTO {
    @ApiModelProperty("请求时间：1698303245480")
    private String requestTime;
    @ApiModelProperty("请求序列号")
    private String requestId;
    @ApiModelProperty("请求类型")
    private String requestType;
    @ApiModelProperty("请求方code")
    private String companyId;
    @ApiModelProperty("接口查询条件参数")
    private TpaRequestData requestData;


    public TpaRequestData createTpaRequestData(){
        return new TpaRequestData();
    }

    @Data
    public class TpaRequestData{
        @ApiModelProperty("供应商代码")
        private String supplierCode;
        @ApiModelProperty("供应商名称")
        private String supplierName;
        @ApiModelProperty("供应商类型")
        private String supplierTypeCode;
        @ApiModelProperty("客户ID")
        private String customId;
        @ApiModelProperty("服务类型代码")
        private String serverTypeCode;
        @ApiModelProperty("外部公估公司作业账号")
        private String externalOperator; // 传值时，supplierTypeCode传01
    }
}
