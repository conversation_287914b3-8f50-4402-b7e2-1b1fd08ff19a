package com.paic.ncbs.claim.service.secondunderwriting;

import com.paic.ncbs.claim.model.vo.senconduw.ClmsPolicyCalculateAgreeDTO;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsPolicySurrenderInfoDTO;
import com.paic.ncbs.claim.model.vo.senconduw.SurrenderCheckVO;

/**
 * 理赔保单解约信息表(ClmsPolicySurrenderInfoEntity)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-03 14:49:01
 */
public interface ClmsPolicySurrenderInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param reportNo
     * @return 实例对象
     */
    ClmsPolicySurrenderInfoDTO getSurrenderInfo(String reportNo,Integer caseTimes,String operateType);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    void saveData(ClmsPolicySurrenderInfoDTO dto);


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    /**
      *
      * @Description 查询是否有解约记录
      * <AUTHOR>
      * @Date 2023/11/6 11:07
      **/
    Integer getSurrenderCount(String reportNo, Integer caseTimes);

    ClmsPolicyCalculateAgreeDTO calculateAgree(String policyNo, String surrenderDate);

    SurrenderCheckVO checkIsSurrender(String reportNo, Integer caseTimes);


    /**
     * 查询当前报案号的保单号下其他报案号信息
     * 理赔在途
     *
     * @param policyNo
     * @param reportNo
     * @return
     */
    String getClaimingInfo(String policyNo, String reportNo);

    /**
      *
      * @Description 理赔调用批改传递
      * <AUTHOR>
      * @Date 2023/11/24 16:30
      **/
    void applyPolicySurrender(String reportNo, Integer caseTimes);

    /**
     * 解约 校验是否有在途案件
     * @param reportNo
     * @param caseTimes
     */
    void checkSurrenderIsOtherCase(String reportNo, Integer caseTimes);
}
