package com.paic.ncbs.claim.dao.entity.coinsurance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 共保摊回主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Getter
@Setter
@TableName("clms_recovery_record")
public class RecoveryRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_recovery_record", type = IdType.INPUT)
    private String idRecoveryRecord;

    /**
     * 总摊回金额
     */
    @TableField("sum_amor_amount")
    private BigDecimal sumAmorAmount;

    /**
     * 总实收金额
     */
    @TableField("sum_receipts_amount")
    private BigDecimal sumReceiptsAmount;

    /**
     * 应摊回金额与实收金额不一致原因
     */
    @TableField("amount_difference_reason")
    private String amountDifferenceReason;

    /**
     * 共保公司与打款方不一致原因
     */
    @TableField("company_mismatch_reason")
    private String companyMismatchReason;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 最新修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;

    private String batchNo;
}
