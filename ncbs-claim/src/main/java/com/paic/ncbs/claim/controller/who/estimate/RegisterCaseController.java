package com.paic.ncbs.claim.controller.who.estimate;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(tags = "立案")
@RestController
@RequestMapping("/who/app/registerCaseAction")
public class RegisterCaseController extends BaseController {

    @Autowired
    private RegisterCaseService registerCaseService;

    @ApiOperation(value = "判断案件是否立案")
    @ResponseBody
    @RequestMapping(value = "/isRegister/{reportNo}/{caseTimes}", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<Map<String, Object>> isRegister(@PathVariable("reportNo") String reportNo,
                                                          @PathVariable("caseTimes") Integer caseTimes) {
        return ResponseResult.success(registerCaseService.isRegister(reportNo, caseTimes));

    }

}
