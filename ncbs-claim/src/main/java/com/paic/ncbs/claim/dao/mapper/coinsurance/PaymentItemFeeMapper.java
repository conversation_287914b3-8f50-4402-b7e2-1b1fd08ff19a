package com.paic.ncbs.claim.dao.mapper.coinsurance;

import com.paic.ncbs.claim.dao.entity.coinsurance.PaymentItemFee;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryInfo;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 支付项目费用子表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface PaymentItemFeeMapper extends BaseMapper<PaymentItemFee> {

    void insertList(@Param("paymentItemFeeList") List<PaymentItemFee> paymentItemFees);

    List<PaymentItemDTO> getPaymentItemAndFeeItem(@Param("reportNo") String reportNo,
                                                  @Param("caseTimes") Integer caseTimes,
                                                  @Param("paymentItemStatus") String paymentItemStatus);

    void delPaymentItemFee(PaymentItemDTO paymentItemDTO);

    void updateItemStatusByRecovery(@Param("recoveryInfoList") List<RecoveryInfo> recoveryInfoList,@Param("paymentStatus") String paymentStatus);

    List<PaymentItemDTO> getHisPaymentItemFee(PaymentItemDTO paymentItemDTO);

    PaymentItemFee selectByPaySerialNo(@Param("IdPaymentItemFee") String paySerialNo);

    void updatePaymentItemFee(PaymentItemDTO paymentItem);

    List<PaymentItemDTO> getAllPaymentItem(PaymentItemDTO paymentItemDTO);

    void updateMergePaymentStatus(@Param("batchNo") String batchNo,@Param("paymentStatus") String paymentStatus);

    PaymentItemFee getPaymentItemFeeById(@Param("batchNo") String batchNo);

    PaymentItemFee getPaymentItemFeeByPaymentId(@Param("idClmPaymentItem") String idClmPaymentItem);

    void updateOtherPaymentStatus(@Param("idClmPaymentItem") String idClmPaymentItem,@Param("paymentStatus") String paymentStatus);
}
