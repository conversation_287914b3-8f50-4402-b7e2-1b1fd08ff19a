package com.paic.ncbs.claim.model.dto.pay;

import lombok.Data;

@Data
public class PayAccountInfo {
    private String settlementNo;//结算单号
    private String accountNo;//收款账号
    private String accountName;//收款户名
    private String bankPayType;//对公对私标志，0－企业，1－个人，不传默认为对公
    private String bankCode;//收款方银行联行号
    private String bankName;//收款方银行大类名称
    private String brachBankName;//收款方开户行名称：中国银行深圳支行对公对私标志为0-企业时，必填；对公对私标志为1-个人时，金额大于100万必填

}
