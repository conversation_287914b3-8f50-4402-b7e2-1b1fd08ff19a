package com.paic.ncbs.claim.service.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsAllowanceInfo;

import java.util.List;

/**
 * 给付津贴信息表(ClmsAllowanceInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:45
 */
public interface ClmsAllowanceInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsAllowanceInfo queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    List<ClmsAllowanceInfo> queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsAllowanceInfo 实例对象
     * @return 实例对象
     */
    ClmsAllowanceInfo insert(ClmsAllowanceInfo clmsAllowanceInfo);

    /**
     * 修改数据
     *
     * @param clmsAllowanceInfo 实例对象
     * @return 实例对象
     */
    ClmsAllowanceInfo update(ClmsAllowanceInfo clmsAllowanceInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    void deleteByCondition(String reportNo, int caseTimes);

}
