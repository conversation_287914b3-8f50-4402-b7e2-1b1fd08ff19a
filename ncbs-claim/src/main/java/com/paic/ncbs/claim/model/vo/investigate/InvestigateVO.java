package com.paic.ncbs.claim.model.vo.investigate;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


@SuppressWarnings("serial")
public class InvestigateVO extends InvestigateDTO {

    private String initiatorUmName;

	private String initiatorName;

    private String primaryInvestigatorUmName;

    private String investigateDepartmentName;

    private String sendBackMan;

    private String sendBackReason;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sendBackTime;
	

	private String investigateQualitativeName;


    private List<InvestigateAdditionalVO> additionals;
    

    private InvestigateEvaluateVO investigateEvaluateVO;
	
    
    public String getInvestigateQualitativeName() {
		return investigateQualitativeName;
	}

	public void setInvestigateQualitativeName(String investigateQualitativeName) {
		this.investigateQualitativeName = investigateQualitativeName;
	}

	public InvestigateEvaluateVO getInvestigateEvaluateVO() {
		return investigateEvaluateVO;
	}

	public void setInvestigateEvaluateVO(InvestigateEvaluateVO investigateEvaluateVO) {
		this.investigateEvaluateVO = investigateEvaluateVO;
	}

	public List<InvestigateAdditionalVO> getAdditionals() {
		return additionals;
	}

	public void setAdditionals(List<InvestigateAdditionalVO> additionals) {
		this.additionals = additionals;
	}

	public String getSendBackMan() {
		return sendBackMan;
	}

	public void setSendBackMan(String sendBackMan) {
		this.sendBackMan = sendBackMan;
	}

	public String getSendBackReason() {
		return sendBackReason;
	}

	public void setSendBackReason(String sendBackReason) {
		this.sendBackReason = sendBackReason;
	}

	public String getInitiatorUmName() {
		return initiatorUmName;
	}

	public void setInitiatorUmName(String initiatorUmName) {
		this.initiatorUmName = initiatorUmName;
	}

	public String getPrimaryInvestigatorUmName() {
		return primaryInvestigatorUmName;
	}

	public void setPrimaryInvestigatorUmName(String primaryInvestigatorUmName) {
		this.primaryInvestigatorUmName = primaryInvestigatorUmName;
	}

	public String getInvestigateDepartmentName() {
		return investigateDepartmentName;
	}

	public void setInvestigateDepartmentName(String investigateDepartmentName) {
		this.investigateDepartmentName = investigateDepartmentName;
	}

	public String getInitiatorName() {
		return initiatorName;
	}

	public void setInitiatorName(String initiatorName) {
		this.initiatorName = initiatorName;
	}

	public Date getSendBackTime() {
		return sendBackTime;
	}

	public void setSendBackTime(Date sendBackTime) {
		this.sendBackTime = sendBackTime;
	}
}