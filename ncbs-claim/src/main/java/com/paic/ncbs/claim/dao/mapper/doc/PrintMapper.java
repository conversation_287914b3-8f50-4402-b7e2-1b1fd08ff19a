package com.paic.ncbs.claim.dao.mapper.doc;

import com.paic.ncbs.claim.model.dto.doc.PrintRecordDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.SettleBatchInfoDTO;
import com.paic.ncbs.claim.model.vo.doc.PrintBillInfoVO;
import com.paic.ncbs.claim.model.vo.doc.PrintCaseInfoVO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyPayInfoVO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyPayVO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@MapperScan
public interface PrintMapper {

//    public List<PrinterDetailDTO> getPrinterInfoByDepartmentCode(@Param("departmentCode") String departmentCode);
//
//    public PrintStamperInfoDTO getStampInfoByDepartmentCode(@Param("departmentCode") String departmentCode);
//
//    public List<PrintDutyPayInfoVO> getDutyPayInfoVO(@Param("reportNo") String reportNo);
//
//    public PrintCaseInfoVO getCaseInfoVO(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
//    public List<PrintRecordDTO> getPrintRecordByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("channel") String channel);
//
//    public List<PolicyClaimCaseDTO> getInsuredCodeByReportNo(@Param("reportNo") String reportNo, @Param("certificateNo") String certificateNo);
//
//
//    public PrintCaseInfoVO getIntegraionCaseInfoVO(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public CaseZeroCancelDTO getCaseZeroCancelInfo(@Param("reportNo") String reportNo,
                                                   @Param("caseTimes") Integer caseTimes, @Param("applyType") Integer applyType);

    public List<String> getPolicyNoListByReportNo(@Param("reportNo") String reportNo);


    public String getHolderNameByPolicyNo(@Param("policyNo") String policyNo, @Param("reportNo") String reportNo, @Param("policyCerNo") String policyCerNo);

    /***********newcode*********************/

    public List<PrintDutyPayInfoVO> getDutyPayInfo(@Param("reportNo") String reportNo);

    public List<WholeCaseVO> getHistoryCase(WholeCaseVO wholeCase);

    public BigDecimal getSumPay(String reportNo,Integer caseTimes);

//    Date getReportDateByReportNo(String reportNo);

    public Date getAccidentDateByReportNo(String reportNo);

    public String getInsuredNameByReportNo(String reportNo);
//

    public PrintCaseInfoVO getPrintCaseInfoVO(String reportNo, Integer caseTimes);

    public List<PrintDutyPayVO> getPrintClaimInfo(String reportNo);

    public EndorsementDTO getEndorsementInfo(@Param("reportNo") String reportNo,
                                             @Param("caseTimes") Integer caseTimes);

    public SettleBatchInfoDTO getSettleAmounts(String reportNo, Integer caseTimes);

    public List<PrintBillInfoVO> getPrintBillInfoVO(String reportNo, Integer caseTimes);

    public List<PaymentItemComData> requestPaymentItemDTOList(String reportNo, Integer caseTimes);

    public int getPrintCount(String reportNo, Integer caseTimes);

    public VerifyConclusionDTO getRefuseInfo(String reportNo, Integer caseTimes);

    public WholeCaseBaseDTO getWholeCaseIndemnityStatus(String reportNo, Integer caseTimes);
    public void addPrintRecord(PrintRecordDTO printRecordDTO);

    List<WholeCaseVO> getHistoryCaseByReportNo(@Param("reportNo") String reportNo, @Param("departmentCodes") List<String> departmentCodes);

    List<WholeCaseVO> getHistoryCaseByPolicyCaseNo(@Param("policyNo") String policyNo,@Param("caseNo") String caseNo,@Param("batchNo") String batchNo, @Param("departmentCodes") List<String> departmentCodes);

    List<WholeCaseVO> getHistoryCaseByBatchNo(@Param("batchNo") String batchNo, @Param("departmentCodes") List<String> departmentCodes);

    void saveClaimNoticeFileId(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("fileId") String fileId);
    void updateCalculationId(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("calculationId") String calculationId);
    void saveCommissionFileId(PrintEntrustDTO printEntrustDTO);

    /**
     * 更新委托表的file_id
     * @param printEntrustDTO 包含fileId和idEntrust的参数对象
     */
    void updateEntrustmentFileId(PrintEntrustDTO printEntrustDTO);

    /**
     * 检查ID是否存在于调查表中
     * @param id 要检查的ID
     * @return 存在记录数
     */
    int existsInInvestigateTable(@Param("id") String id);

    /**
     * 检查ID是否存在于委托表中
     * @param id 要检查的ID
     * @return 存在记录数
     */
    int existsInEntrustTable(@Param("id") String id);

    String findFileId(String reportNo, Integer caseTimes);

    String findCommissionFileId(PrintEntrustDTO printEntrustDTO);

    String findEntrustmentFileId(PrintEntrustDTO printEntrustDTO);

    String selectParentDepartmentCodeByReportNo(@Param("reportNo") String reportNo);

    List<WholeCaseVO> findFileIdList(String reportNo);

    public BigDecimal getPaySum(String reportNo,Integer caseTimes);

    String findCalculationId(String reportNo, Integer caseTimes);
}
