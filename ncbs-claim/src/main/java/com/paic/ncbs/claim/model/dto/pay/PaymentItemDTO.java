package com.paic.ncbs.claim.model.dto.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.convert.BeanConvert;
import com.paic.ncbs.claim.model.vo.pay.PaymentItemVO;
import com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt;
import com.paic.ncbs.claim.replevy.vo.ClmsRelatedActualReceiptVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PaymentItemDTO {
    private String createdBy;
    private Date createdDate;
    private String updatedBy;
    private Date updatedDate;
    private String idClmPaymentItem;
    private String policyNo;
    private String reportNo;
    private String caseNo;
    private String idClmBatch;
    private Integer caseTimes;
    // 报案号 + 赔付次数（历史支付信息展示）
    private String reportCaseTimesNo;
    private String claimType;
    private String organizeCode;
    private Integer subTimes;
    private String paymentType;
    private String idClmPaymentInfo;
    private String collectPaySign;
    private BigDecimal paymentAmount;
    private String paymentCurrencyCode;
    private String clientName;
    private String clientCertificateType;
    private String clientCertificateNo;
    private String clientBankCode;
    private String clientBankName;
    private String clientBankAccount;
    private String clientMobile;
    private String clientType;
    private String provinceName;
    private String cityName;
    private String regionCode;
    private String collectPayApproach;
    private String bankAccountAttribute;
    private String mergeSign;
    private String paymentItemStatus;
    private List<String> paymentItemStatusList;
    private Date effectiveDate;
    private Date invalidateDate;
    private String remark;
    private String isCoinsure;
    private String migrateFrom;
    private String collectPayNo;
    private String hisIdClmPaymentNotice;
    private String idClmSubrogationSettle;
    private BigDecimal exchangeRate;
    private BigDecimal convertAmount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date archiveDate;
    private String extendInfo;
    private String bankDetail ;
    private String modifyInfo ;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date createDate ;
    //序号
    private  Integer  serialNo ;
    // 第三方系统id
    private String financialId ;
    // 计算书号
    private String compensateNo ;
    // 支付日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date payDate ;
    // 支付退回日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date payBackDate ;
    private String paymentTypeName;
    private String collectPaySignName;
    private String feeType;
    private String clientRelation;
    private String bankDetailCode;
    /**
     * 机构类型
     */
    private String agencyType;
    /**
     * 客户号
     */
    private String customerNo;

    /**
     * 公司证件类型/企业证件类型
     */
    private String companyCardType;

    /**
     * 领款方式为微信零钱/美团点评的时候必传
     */
    private String openId;

    /**
     * 领款方式：1 微信零钱 2银行转账 3 美团点评
     */
    private String payType;

    /**
     * 共保标志 0非共保1共保
     */
    private String coinsuranceMark;

    /**
     * 是否主承保 0否1是
     */
    private String acceptInsuranceFlag;

    /**
     * 共保公司编码
     */
    private String coinsuranceCompanyCode;

    /**
     * 共保公司名称
     */
    private String coinsuranceCompanyName;

    /**
     * 共保比例
     */
    private BigDecimal coinsuranceRatio;

    /**
     * 是否全额给付 0否1是
     */
    private String isFullPay;

    /**
     * 共保实付金额
     */
    private BigDecimal coinsuranceActualAmount;

    /**
     * 财务支付金额
     */
    private BigDecimal financePaymentAmount;

    /**
     * 支付凭证地址
     */
    private String paymentVoucherUrl;

    /**
     * 整案状态
     */
    private String wholeCaseStatus;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 退运险合并支付，送收付ID
     */
    private String id;

    /**
     *批次号，用于批量核销
     */
    private String batchNo;
    /**
     *支付金额(赔款领款人信息显示)
     */
    private BigDecimal chgPaymentAmount;
    /**
     *关联实收
     */
    private List<ClmsRelatedActualReceiptVo> relatedActualReceiptVoList;
    /**
     *关联实收
     */
    private List<ClmsRelatedActualReceipt> relatedActualReceiptList;
    /**
     *是否新数据
     */
    private String isNew;

    /**
     *我司承担金额变化量
     */
    private BigDecimal chgCoinsuranceActualAmount;

    /**
     *实付金额变化量
     */
    private BigDecimal chgPaidAmount;

    private String idClmPaymentItemFee;

    private BigDecimal coinsuranceActualTax;
    private BigDecimal coinsuranceActualNoTax;

    private String idAhcsFeePay;

    public PaymentItemVO convertToVO(){
        PaymentItemDTOConvert convert = new PaymentItemDTOConvert();
        return convert.convert(this);
    }

    private static class PaymentItemDTOConvert implements BeanConvert<PaymentItemDTO, PaymentItemVO>{

        @Override
        public PaymentItemVO convert(PaymentItemDTO paymentItemDTO) {
            PaymentItemVO paymentItemVO = new PaymentItemVO();
            BeanUtils.copyProperties(paymentItemDTO,paymentItemVO);
            paymentItemVO.setPayDate(paymentItemDTO.getPayDate());
            return paymentItemVO;
        }
    }
}
