package com.paic.ncbs.claim.service.settle.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPresonMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeDetailMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonDisabilityMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PlanPayMapper;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.vo.openapi.CompositePolicyVO;
import com.paic.ncbs.claim.model.vo.settle.DutyBillLimitInfoVO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.common.ClmsGetPolicyMonthInfoService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.service.duty.DutyAttrAsyncInitService;
import com.paic.ncbs.claim.service.duty.DutyPayService;
import com.paic.ncbs.claim.service.settle.AutoSettleService;
import com.paic.ncbs.claim.service.settle.PlanPayService;
import com.paic.ncbs.claim.service.settle.SettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ClmsDutyDetailBillSettleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RefreshScope
@Service("planPayService")
public class PlanPayServiceImpl implements PlanPayService {

    @Autowired
    private AhcsCommonService ahcsCommonService;

    @Autowired
    private PlanPayMapper planPayDao;
    @Autowired
    private DutyPayService dutyPayService;

    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;

    @Autowired
    private PersonDisabilityMapper personDisabilityMapper;

    @Autowired
    private DutyAttrAsyncInitService dutyAttrAsyncInitService;

    @Autowired
    private AutoSettleService autoSettleService;

    @Autowired
    private CaseClassMapper caseClassDao;

    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;
    @Autowired
    private AhcsInsuredPresonMapper ahcsInsuredPresonMapper;

    @Autowired
    private AhcsPolicyPlanMapper ahcsPolicyPlanMapper;
    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;

    @Autowired
    private ClmsGetPolicyMonthInfoService clmsGetPolicyMonthInfoService;
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    @Autowired
    private ClmsDutyDetailBillSettleService clmsDutyDetailBillSettleService;

    @Override
    public List<PlanPayDTO> getByPolicy(PolicyPayDTO policy) {
        //List<String> caseClassList = caseClassDao.getCaseClassList(policy.getReportNo(), policy.getCaseTimes(), BpmConstants.CHECK_DUTY);
        String policyNo = policy.getPolicyNo();
        //查询责任明细发票理算信息
        List<ClmsDutyDetailBillSettleDTO> detailBillSettleLists= getDutyDetailBillInfo(policy);
        List<PlanPayDTO> plans = planPayDao.getByPolicy(policy);
        if (ListUtils.isNotEmpty(plans)) {
            for (PlanPayDTO plan : plans) {
                plan.setPolicyNo(policyNo);
                List<DutyPayDTO> dutyList = plan.getDutyPayArr();
                for (DutyPayDTO duty : dutyList){
                    //其他责任属性
                    duty.setAttributes(dutyAttrAsyncInitService.getOtherDutyAttributeList(duty.getIdCopyDuty()));
                    List<DutyDetailPayDTO> details = duty.getDutyDetailPayArr();
                    for (DutyDetailPayDTO detail : details){
                        String dutyDetailType = detail.getDutyDetailType();
                        if(Objects.equals("04",dutyDetailType) && CollectionUtil.isNotEmpty(detailBillSettleLists)){
                            List<ClmsDutyDetailBillSettleDTO> detailBillSettlelist = getDetailBillSettleInfo(detailBillSettleLists,plan.getPlanCode(),duty.getDutyCode(),detail.getDutyDetailCode());
                            detail.setDetailBillSettleList(detailBillSettlelist);
                        }
                        //责任明细合理费用设置
                        List<ClmsDutyDetailBillSettleDTO> deailBIllList = detailBillSettleLists.stream().filter(cDTO ->Objects.equals(detail.getPlanCode(),cDTO.getPlanCode()) && Objects.equals(detail.getDutyCode(),cDTO.getDutyCode()) && Objects.equals(detail.getDutyDetailCode(),cDTO.getDutyDetailCode())).collect(Collectors.toList());
                        setDetailValue(detail,deailBIllList);
                        //detail.setIsDisplay(autoSettleService.isDutyMatchCaseClass(caseClassList,dutyDetailType));
                        /**
                         * 2024-07-10 思佳要求所有责任不论案件类型是否与责任明细责任分类匹配，一律放开 不用置灰
                         */
                        detail.setIsDisplay(BaseConstant.TRUE);
                        //伤残等级
                        if (SettleConst.DETAIL_TYPE_DISABILITY.equals(detail.getDutyDetailType())){
                            detail.setDisabilityGrade(getDisabilityGrade(policy.getReportNo(),policy.getCaseTimes()));
                        }
                    }
                    DutyBillLimitInfoDTO dutyBillLimitInfoDTO = new DutyBillLimitInfoDTO();
                    dutyBillLimitInfoDTO.setPolicyNo(policyNo);
                    dutyBillLimitInfoDTO.setDutyCode(duty.getDutyCode());
                    dutyBillLimitInfoDTO.setPlanCode(plan.getPlanCode());
                    dutyBillLimitInfoDTO.setReportNo(policy.getReportNo());
                    dutyBillLimitInfoDTO.setCaseTimes(policy.getCaseTimes());
                    List<DutyBillLimitInfoVO> dutyLimitPayList = dutyBillLimitInfoMapper.getDutyLimitPayList(dutyBillLimitInfoDTO);
                    duty.setDutyLimitPayList(dutyLimitPayList);
                }
                plan.setDutyPayArr(dutyList.stream().sorted(Comparator.comparing(DutyPayDTO::getShareDutyGroup, Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList()));
            }
        }
        return plans;
    }

    @Override
    public void reQueryDutyDetail(PolicyPayDTO policy) {
        ReportCustomerInfoEntity entity = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(policy.getReportNo());
        //查询被保险人是否有社保
        List<AhcsInsuredPresonEntity> persionList = ahcsInsuredPresonMapper.getAhcsInsuredPersionByClientNo(entity.getClientNo());
        String isSociaSecurity="";
        if(CollectionUtil.isNotEmpty(persionList)){
            isSociaSecurity=persionList.get(0).getIsSociaSecurity();
        }
        String productPackage= ahcsPolicyPlanMapper.getPolicyProductPackage(policy.getPolicyNo());

        LogUtil.audit("-重新结算理算费用-报案号：{}",policy.getReportNo());
        String policyNo = policy.getPolicyNo();
        String indemnityMode = policy.getIndemnityMode();
        PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(policyNo,null);
        //合同月
        List<PolicyMonthDto> monthDtoList = clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),policyDto.getPolicyEndDate());
        policy.setMonthDtoList(monthDtoList);
        List<PlanPayDTO> plans = planPayDao.getByPolicy(policy);
        if (ListUtils.isNotEmpty(plans)) {
            for (PlanPayDTO plan : plans) {
                plan.setPolicyNo(policyNo);
                List<DutyPayDTO> dutyList = plan.getDutyPayArr();
                for (DutyPayDTO duty : dutyList){
                    duty.setPolicyNo(policyNo);
                    List<DutyDetailPayDTO> details = duty.getDutyDetailPayArr();
                    for (DutyDetailPayDTO detail : details){
                        detail.setPolicyNo(policyNo);
                        detail.setReportNo(policy.getReportNo());
                        detail.setCaseTimes(policy.getCaseTimes());
                        detail.setIsSociaSecurity(isSociaSecurity);//社保标识，理算时区分社保无社保取赔付比列
                        detail.setProductPackage(productPackage);//方案编码
                        detail.setProductCode(policy.getProductCode());//产品编码
                        detail.setInsuranceBeginTime(policyDto.getPolicyStartDate());
                        detail.setInsuranceEndTime(policyDto.getPolicyEndDate());
                        detail.setSettleAmount(null);
                        //修改收单时可能会变更账单类型，需重新获取
                        if (SettleConst.DETAIL_TYPE_MEDICAL.equals(detail.getDutyDetailType())){
                            detail.setReasonableAmountType(getBillClass(policy.getReportNo(),policy.getCaseTimes()));
                        }
                        //修改收单时，可能会变更伤残等级
                        if(SettleConst.DETAIL_TYPE_DISABILITY.equals(detail.getDutyDetailType())){
                            detail.setDisabilityGrade(getDisabilityGrade(policy.getReportNo(),policy.getCaseTimes()));
                        }
                        detail.setMonthDtoList(monthDtoList);
                        //修改收单时，可能会变更赔付模式
                        detail.setIndemnityMode(indemnityMode);
                    }
                    //重新获取责任属性
                    dutyAttrAsyncInitService.getAttrByDutyDetailType(duty,policy.getReportNo(),policy.getCaseTimes());
                }
            }
            policy.setPlanPayArr(plans);
        }
    }

    private String getBillClass(String reportNo,int caseTimes){
        String therapyType = null;
        List<String> therapyTypeList = medicalBillInfoMapper.getBillClassByReportNo(reportNo,caseTimes);
        if (therapyTypeList.size() > 1){
            therapyType = BaseConstant.STRING_3;
        }else if(therapyTypeList.size() == 1 && ChecklossConst.AHCS_THERAPY_ONE.equals(therapyTypeList.get(0))){
            therapyType = BaseConstant.STRING_2;
        }else if (therapyTypeList.size() == 1 && ChecklossConst.AHCS_THERAPY_TWO.equals(therapyTypeList.get(0))){
            therapyType = BaseConstant.STRING_1;
        }
        return therapyType;
    }

    private String getDisabilityGrade(String reportNo,int caseTimes){
        PersonDisabilityDTO personDisability = new PersonDisabilityDTO();
        personDisability.setReportNo(reportNo);
        personDisability.setCaseTimes(caseTimes);
        personDisability.setTaskId(BpmConstants.CHECK_DUTY);
        List<PersonDisabilityDTO> disableList = personDisabilityMapper.getList(personDisability);
        String disabilityGrade = "";
        if (!disableList.isEmpty()) {
            disabilityGrade = disableList
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(d -> d.getDisabilityGrade() != null && d.getDisabilityGrade().matches("\\d+"))
                    .min(Comparator.comparingInt(d -> Integer.parseInt(d.getDisabilityGrade())))
                    .map(PersonDisabilityDTO::getDisabilityGrade)
                    .orElse("");
        }

        return disabilityGrade;
    }

    @Transactional
    @Override
    public void insertPlanPayList(List<PlanPayDTO> planPayArr) {
        if (!planPayArr.isEmpty()) {
            ahcsCommonService.batchHandlerTransactionalWithArgs(PlanPayMapper.class, planPayArr,
                    ListUtils.GROUP_NUM, "insertPlanPayInfo");
        }
    }
    @Override
    public void deleteByBatchId(String idAhcsBatch,String claimType) {
        planPayDao.deleteByBatchId(idAhcsBatch,claimType);
    }

    @Override
    public void batchModify(List<PlanPayDTO> planPayArr) {
        if (planPayArr != null && !planPayArr.isEmpty()) {
            for (PlanPayDTO planPayInfo : planPayArr) {
                //批量更新赔付责任信息
                dutyPayService.modifyUpdate(planPayInfo.getDutyPayArr());
            }
            //批量更新险种赔付信息
            if (!CollectionUtils.isEmpty(planPayArr)) {
                for (PlanPayDTO planPayDTO : planPayArr) {
                    planPayDTO.setUpdatedBy(WebServletContext.getUserId());
                    planPayDao.updatePlanPayInfoList(planPayDTO);

                }
            }
            /*ahcsCommonService.batchHandlerTransactionalWithArgs(PlanPayMapper.class, planPayArr,
                    ListUtils.GROUP_NUM, "updatePlanPayInfoList");*/
        }
    }

    /**
     * 查询责任明细发票理算信息
     * @param policy
     */
    private List<ClmsDutyDetailBillSettleDTO> getDutyDetailBillInfo(PolicyPayDTO policy) {
        List<ClmsDutyDetailBillSettleDTO> list =  clmsDutyDetailBillSettleService.getAllInfoByReportNo(policy);
        return list;
    }

    private List<ClmsDutyDetailBillSettleDTO> getDetailBillSettleInfo(List<ClmsDutyDetailBillSettleDTO> detailBillSettleLists, String planCode, String dutyCode, String dutyDetailCode) {
        List<ClmsDutyDetailBillSettleDTO> list= detailBillSettleLists.stream().filter(clmsDutyDetailBillSettleDTO -> Objects.equals(clmsDutyDetailBillSettleDTO.getPlanCode(),planCode) && Objects.equals(clmsDutyDetailBillSettleDTO.getDutyCode(),dutyCode) && Objects.equals(clmsDutyDetailBillSettleDTO.getDutyDetailCode(),dutyDetailCode)).collect(Collectors.toList());
        setModifyFlag(list);
        List<ClmsDutyDetailBillSettleDTO> sortedlist = list.stream().sorted((t1,t2)->t1.getBillDate().compareTo(t2.getBillDate())).collect(Collectors.toList());
        return sortedlist;
    }

    private void setModifyFlag(List<ClmsDutyDetailBillSettleDTO> list) {
        for (ClmsDutyDetailBillSettleDTO dto :list) {
            dto.setCanModifyFlag("Y");
        }
    }
    private void setDetailValue(DutyDetailPayDTO detail, List<ClmsDutyDetailBillSettleDTO> deailBIllList) {
         BigDecimal remitAmount=BigDecimal.ZERO;
         BigDecimal reasonableAmount=BigDecimal.ZERO;;
         for (ClmsDutyDetailBillSettleDTO dto:deailBIllList) {
             if(Objects.equals("0",dto.getSettleType())){
                 reasonableAmount = reasonableAmount.add(Objects.isNull(dto.getReasonableAmount()) ? BigDecimal.ZERO : dto.getReasonableAmount());
                 remitAmount = remitAmount.add(Objects.isNull(dto.getRemitAmount()) ? BigDecimal.ZERO : dto.getRemitAmount());
             }

         }
         detail.setReasonableAmount(reasonableAmount);
         detail.setRemitAmount(remitAmount);
    }

}
