package com.paic.ncbs.claim.service.investigate.impl;

import com.paic.ncbs.claim.controller.report.QueryReportController;
import com.paic.ncbs.claim.controller.who.WholeCaseController;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditPublicMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditPublicDTO;
import com.paic.ncbs.claim.model.dto.report.GetReportBaseInfoSearchVO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditPublicService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditService;
import com.paic.ncbs.file.service.FileCommonService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class InvestigateTaskAuditPublicServiceImpl implements InvestigateTaskAuditPublicService {

    @Autowired
    private InvestigateTaskAuditPublicMapper investigateTaskAuditPublicMapper;

    @Autowired
    private InvestigateTaskAuditService investigateTaskAuditService;

    @Autowired
    private QueryReportController queryReportController;

    @Autowired
    private WholeCaseController wholeCaseController;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private FileCommonService fileCommonService;

    @Override
    public List<WorkBenchTaskVO> getInvestigateTaskList(List<String> investigateDepartment) {
        return investigateTaskAuditPublicMapper.getInvestigateTaskList(investigateDepartment);
    }

    @Override
    public void finishTaskAudit(InvestigateTaskAuditPublicDTO taskAuditDTO) {
        // 转换为原始DTO，调用现有服务
        InvestigateTaskAuditPublicDTO originalDTO = new InvestigateTaskAuditPublicDTO();
        BeanUtils.copyProperties(taskAuditDTO, originalDTO);

        // 使用外部审核人作为用户ID
        String externalAuditor = taskAuditDTO.getExternalAuditor();
        if (externalAuditor == null || externalAuditor.trim().isEmpty()) {
            throw new IllegalArgumentException("外部审核人不能为空");
        }

        // 调用现有的审核服务
        investigateTaskAuditService.finishTaskAudit(taskAuditDTO, externalAuditor);
    }

    /**
     * 获取客户基本信息
     */
    public ReportCustomerInfoVO getCustomerInfo(String reportNo) {
        return queryReportController.getCustomerInfoByReportNo(reportNo).getData();
    }

    /**
     * 获取报案信息
     */
    public ReportBaseInfoResData getReportBaseInfo(String reportNo, Integer caseTimes) {
        GetReportBaseInfoSearchVO searchVO = new GetReportBaseInfoSearchVO();
        searchVO.setReportNo(reportNo);
        searchVO.setCaseTimes(caseTimes);
        return wholeCaseController.getReportBaseInfo(searchVO).getData();
    }

    /**
     * 获取电子保单下载地址
     */
    public String getPolicyElectricPdfUrl(String reportNo, String userName) {
        try {
            String policyNo = ahcsPolicyInfoMapper.getOnePolicyNoByReportNo(reportNo);
            String policyDocumentId = ocasMapper.getPolicyDocumentId(policyNo);
            return fileCommonService.getPreviewUrl(policyDocumentId, userName);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取基础信息（客户基本信息、报案信息、电子保单下载地址）
     */
    @Override
    public Map<String, Object> getBasicInfo(String reportNo, Integer caseTimes, String userName) {
        Map<String, Object> result = new java.util.HashMap<>();

        // 客户基本信息
        ReportCustomerInfoVO customerInfo = getCustomerInfo(reportNo);
        if (customerInfo != null && customerInfo.getIsSociaSecurity() != null) {
            String isSociaSecurity = customerInfo.getIsSociaSecurity();
            if ("1".equals(isSociaSecurity)) {
                customerInfo.setIsSociaSecurity("无社保");
            } else if ("2".equals(isSociaSecurity)) {
                customerInfo.setIsSociaSecurity("有社保");
            }
        }
        result.put("customerInfo", customerInfo);

        // 报案信息
        ReportBaseInfoResData reportBaseInfo = getReportBaseInfo(reportNo, caseTimes);
        result.put("reportBaseInfo", reportBaseInfo);
        if (reportBaseInfo != null) {
            result.put("linkManList", reportBaseInfo.getLinkManList());
        }

        return result;
    }
}