package com.paic.ncbs.claim.service.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsPersonalInjuryDeathInfo;

import java.util.List;

/**
 * 人身伤亡信息表(ClmsPersonalInjuryDeathInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:48
 */
public interface ClmsPersonalInjuryDeathInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsPersonalInjuryDeathInfo queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    ClmsPersonalInjuryDeathInfo queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsPersonalInjuryDeathInfo 实例对象
     * @return 实例对象
     */
    ClmsPersonalInjuryDeathInfo insert(ClmsPersonalInjuryDeathInfo clmsPersonalInjuryDeathInfo);

    /**
     * 修改数据
     *
     * @param clmsPersonalInjuryDeathInfo 实例对象
     * @return 实例对象
     */
    ClmsPersonalInjuryDeathInfo update(ClmsPersonalInjuryDeathInfo clmsPersonalInjuryDeathInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    void deleteByCondition(String reportNo, int caseTimes, String taskId);

}
