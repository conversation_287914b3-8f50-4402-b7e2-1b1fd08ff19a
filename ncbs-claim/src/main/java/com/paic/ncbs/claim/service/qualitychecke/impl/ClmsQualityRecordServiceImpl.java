package com.paic.ncbs.claim.service.qualitychecke.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo;
import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityRecord;
import com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityRecordMapper;
import com.paic.ncbs.claim.service.qualitychecke.ClmsQualityRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class ClmsQualityRecordServiceImpl extends ServiceImpl<ClmsQualityRecordMapper, ClmsQualityRecord> implements ClmsQualityRecordService {
    @Override
    public List<ClmsQualityRecord> selectByReportNoAndCaseTimes(String reportNo, Short caseTimes, String id) {
        return baseMapper.selectByReportNoAndCaseTimes(reportNo, caseTimes, id);
    }

    @Override
    public int insertrecord(ClmsQualityInfo clmsQualityInfo) {
        ClmsQualityRecord clmsQualityRecord = new ClmsQualityRecord();
        clmsQualityRecord.setQualityId(clmsQualityInfo.getId());
        clmsQualityRecord.setCaseTimes(clmsQualityInfo.getCaseTimes());
        clmsQualityRecord.setReportNo(clmsQualityInfo.getReportNo());
        clmsQualityRecord.setProcessTime(LocalDateTime.now());
        clmsQualityRecord.setNode(clmsQualityInfo.getNode());
        clmsQualityRecord.setHandler(WebServletContext.getUserId()+"-"+WebServletContext.getUserName());
        clmsQualityRecord.setCreatedBy(WebServletContext.getUserId());
        clmsQualityRecord.setUpdatedBy(WebServletContext.getUserId());
        clmsQualityRecord.setId(UuidUtil.getUUID());
        if(StringUtils.isNotEmpty(clmsQualityInfo.getNode())&&"1".equals(clmsQualityInfo.getNode())){
            if("0".equals(clmsQualityInfo.getApprovalOpinion())){
                clmsQualityRecord.setLocusIdea("通过");
            }else if("1".equals(clmsQualityInfo.getApprovalOpinion())){
                clmsQualityRecord.setLocusIdea("退回");
            }else if("2".equals(clmsQualityInfo.getApprovalOpinion())){
                clmsQualityRecord.setLocusIdea("移交审批");
            }
            clmsQualityRecord.setLocusDetail(clmsQualityInfo.getApprovalDesc());
        }else if (StringUtils.isNotEmpty(clmsQualityInfo.getNode())&&"0".equals(clmsQualityInfo.getNode())){
            clmsQualityRecord.setLocusDetail(clmsQualityInfo.getOpinionDetail());
        }

        return baseMapper.insertrecord(clmsQualityRecord);
    }
}
