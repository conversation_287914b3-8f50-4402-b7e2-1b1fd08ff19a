package com.paic.ncbs.claim.model.dto.copypolicy;

import lombok.Data;

import java.util.List;

@Data
public class ClaimDetailLiability {
    private String liabilityCls;//损失类型（1:产品2:一般）
    private String genlLiabType;//具体损失类型 1 人伤 2 物损
    private String causeLoss1;//损失原因大类 （5.0详见损失原因大类说明）
    private String countryCode;//具体国家类型（详见国家类型说明） CN000
    private String lossDescription;//损失描述
    private String addressDetail;//出险详细地址
    private String item1;//受损项目类型 （详见受损项目类型说明）
    private ClaimDetailPersonInjuryInfo claimDetailPersonInjuryInfo;
    private List<ClaimDetailLossObjectInfo> claimDetailLossObjectInfoList;
}
