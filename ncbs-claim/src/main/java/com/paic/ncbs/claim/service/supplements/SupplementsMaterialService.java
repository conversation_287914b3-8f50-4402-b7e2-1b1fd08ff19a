package com.paic.ncbs.claim.service.supplements;

import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;

import java.util.List;

/**
 * 客户补材任务记录信息服务
 */
public interface SupplementsMaterialService {

    /**
     * 根据ID查询补材任务信息
     * @param id
     * @return
     */
    SupplementsMaterialDTO selectByPrimaryKey(String id);

    /**
     * 保存
     * @param dto
     */
    void saveData(SupplementsMaterialDTO dto);

    /**
     * 更新数据
     * @param dto
     */
    void updateData(SupplementsMaterialDTO dto);

    List<SupplementsMaterialDTO> getSupplementsMaterial(String reportNo, Integer caseTimes);

    /**
     * 超期未处理的任务数据
     * @param days 天数
     * @return
     */
    List<SupplementsMaterialDTO> getServiceData(Integer days);



    /**
     * 更新数据
     * @param dto
     */
    void updateServiceData(SupplementsMaterialDTO dto);


    /**
     * 查询客户补材任务记录
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<SupplementsMaterialDTO> getCustomerSupplements(String reportNo,Integer caseTimes);

    /**
     *  获取补材任务短信通知列表
     * @param smsType
     * @param days
     * @return
     */
    List<SupplementsMaterialDTO> getSmsNotificationTask(String smsType,Integer days);
}
