package com.paic.ncbs.claim.service.openapi.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.MapUtil;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.enums.DiagnoseTypeEnum;
import com.paic.ncbs.claim.common.enums.TreatCondition;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.EstimateUtil;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.config.PackageTypeDutyConfig;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.*;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.openapi.ClmsTpaRequestRegisterDTO;
import com.paic.ncbs.claim.model.dto.report.DutyEstimateLossDTO;
import com.paic.ncbs.claim.model.dto.settle.HistoryPayInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.*;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.ClaimWorkFlowService;
import com.paic.ncbs.claim.service.common.ResidueAmountService;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.endcase.CaseClassService;
import com.paic.ncbs.claim.service.estimate.ClmsTpaRequestRegisterService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.openapi.OnlineRegisterService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * TPA立案接口实现
 */
@Slf4j
@Service
public class OnlineRegisterServiceImpl implements OnlineRegisterService {

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private DutySurveyService dutySurveyService;

    @Autowired
    private CaseClassService caseClassService;


    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private RegisterCaseService registerCaseService;

    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private  PersonAccidentMapper personAccidentMapper;

    @Autowired
    private ClmsTpaRequestRegisterService clmsTpaRequestRegisterService;
    @Autowired
    private ClaimWorkFlowService claimWorkFlowService;

    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private PackageTypeDutyConfig packageTypeDutyConfig;

    @Autowired
    private ResidueAmountService residueAmountService;

    @Autowired
    private DutyPayMapper dutyPayMapper;

    @Autowired
    private BpmService bpmService;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    /**
     * tpa立案
     * @param dutySurveyVO
     */
    @Override
    @Transactional
    public void register(DutySurveyVO dutySurveyVO) {
        log.info("tpa平台接入立案接口报案号={},入参={}",dutySurveyVO.getReportNo(), JsonUtils.toJsonString(dutySurveyVO));
        //1入参校验
        checkAndSetValue(dutySurveyVO);

        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        String status = dutySurveyVO.getStatus();
        String loginUm = "system";
        dutySurveyVO.setUserId(loginUm);
        String taskId = dutySurveyVO.getTaskId();
        LogUtil.audit("#保存报案跟踪数据,reportNo=%s,taskId=%s,status=%s", reportNo, taskId, status);

        /**
         *  2：组装立案信息（新建报案的时候已经落地的数据信息）:
         *  TPA只传入一个预估金额，经会议和业务确认，目前只有一个险种一个责任，TPA只传入一个预估金额（页面对应的立案赔款金额）没有理赔费用金额，核心将
         *  预估金额映射到责任上，目前不考虑多责任的情况
         */
        EstimatePolicyFormDTO estimatePolicyFormDTO = estimateService.getEstimateDataByTache(reportNo, caseTimes, EstimateUtil.ESTIMATE_TYPE_REGISTRATION, null);
        estimatePolicyFormDTO.setEstimateLossAmount(dutySurveyVO.getEstimatePolicyFormDTO().getEstimateLossAmount());
        estimatePolicyFormDTO.setDutyEstimateLossList(dutySurveyVO.getEstimatePolicyFormDTO().getDutyEstimateLossList());
        estimatePolicyFormDTO.setAllBillAmount(dutySurveyVO.getEstimatePolicyFormDTO().getAllBillAmount());
       //映射立案金额到责任
        setEstimatePolicyFormDTOValue(estimatePolicyFormDTO);
        dutySurveyVO.setEstimatePolicyFormDTO(estimatePolicyFormDTO);

        //立案信息更新
        estimateService.dealEstimateInfo(dutySurveyVO);

        //将之前保存的案件类别先设置为“失效”状态
        List<String> caseSubClassList= caseClassService.saveTPAClassData(dutySurveyVO);

       //区分人伤、非人伤
        PeopleHurtVO peopleHurtVO = dutySurveyVO.getPeopleHurtVO();
        setPersonAccident(dutySurveyVO);
        if (peopleHurtVO != null) {
            peopleHurtVO = dutySurveyService.dealPeopleHurtInfo(dutySurveyVO,caseSubClassList);
        }
        //非人伤
        NoPeopleHurtVO noPeopleHurtVO = dutySurveyVO.getNoPeopleHurtVO();
        if (Objects.nonNull(noPeopleHurtVO)) {
            //非人伤 9月版本
            dutySurveyService.dealNoPeopleHurtInfo(dutySurveyVO,peopleHurtVO);
        }
        this.addReportTrack(dutySurveyVO, loginUm, status);

        //更新风险标识到clms_report_info表
        ReportInfoEntity entity =new ReportInfoEntity();
        entity.setRiskFlag(dutySurveyVO.getRiskFlag());
        entity.setRiskRemark(dutySurveyVO.getRiskRemark());
        entity.setReportNo(dutySurveyVO.getReportNo());
        reportInfoMapper.updateReportRiskFlag(entity);

        if (dutySurveyVO.getPersonRescueVO() != null) {
            dutySurveyService.savePersonRescue(dutySurveyVO.getPersonRescueVO(), reportNo, caseTimes, loginUm, taskId, status);
        }
        //立案前校验
        ResponseResult resultVO = registerCaseService.registerCaseCheck(estimatePolicyFormDTO.getReportNo(), estimatePolicyFormDTO.getCaseTimes());
        if(!ErrorCode.RegisterCase.CAN_APPLY_REGISTER_CASE.equals(resultVO.getData())){
            throw new GlobalBusinessException(resultVO.getCode(), resultVO.getMsg());
        }
        //TPA立案成功记录保存
        ClmsTpaRequestRegisterDTO registerDTO =new ClmsTpaRequestRegisterDTO();
        registerDTO.setReportNo(reportNo);
        registerDTO.setCaseTimes(caseTimes);
        clmsTpaRequestRegisterService.save(registerDTO);
        //立案数据保存及立案工作流
        estimateService.saveEstimateByReportTrack(estimatePolicyFormDTO, loginUm);

    }

    /**
     * 工作流处理
     * @param reportNo
     * @param caseTimes
     */
    @Override
    @Transactional
    public CaseRegisterApplyDTO dealWorkFlow(String reportNo, Integer caseTimes) {
        //TPA过来的数据 立案完成后直接工作流流转到收单
        //完成报案跟踪工作流 原先的事件改成直接调用创建任务和完成任务
        claimWorkFlowService.dealWorkFlowData(reportNo,caseTimes);
        //记录报案跟踪提交是否成功
        clmsTpaRequestRegisterService.update(reportNo,caseTimes);

        //把立案数据id返回给TPA()告诉TPA立案成功=核心立案保存+报案跟踪提交
        CaseRegisterApplyDTO dto = registerCaseService.getLastCaseRegisterApplyDTO(reportNo,caseTimes);
        //global保单立案，估损回流
        globalPolicyService.sendReturnRegisterAndEstimateLoss(reportNo,caseTimes);

        return dto;


    }

    /**
     * 设置事故信息
     * @param dutySurveyVO
     */
    private void setPersonAccident(DutySurveyVO dutySurveyVO) {
        PeopleHurtVO peopleHurtVO =dutySurveyVO.getPeopleHurtVO();
        //查询事故地址相关信息：tPA报案时传了，立案时只穿案件类别信息
        PersonAccidentDTO dto= personAccidentMapper.getPersonAccidentByReportNo(dutySurveyVO.getReportNo(),"report1",dutySurveyVO.getCaseTimes());
        PersonAccidentVO personAccidentVo = new PersonAccidentVO();
        BeanUtils.copyProperties(dto,personAccidentVo);
        personAccidentVo.setIdAhcsChannelProcess("");
        personAccidentVo.setPersonAccidentId("");
        personAccidentVo.setTaskId(peopleHurtVO.getAccidentVO().getTaskId());
        personAccidentVo.setInjuryMechanism(peopleHurtVO.getAccidentVO().getInjuryMechanism());
        personAccidentVo.setProfessionCode(peopleHurtVO.getAccidentVO().getProfessionCode());
        personAccidentVo.setProfessionBigdCode(peopleHurtVO.getAccidentVO().getProfessionBigdCode());
        personAccidentVo.setSubProfessionCode(peopleHurtVO.getAccidentVO().getSubProfessionCode());
        personAccidentVo.setDetailPlace(peopleHurtVO.getAccidentVO().getDetailPlace());
        personAccidentVo.setDetailPlaceDesc(peopleHurtVO.getAccidentVO().getDetailPlaceDesc());
        personAccidentVo.setInjuryMechanismDesc(peopleHurtVO.getAccidentVO().getInjuryMechanismDesc());

        personAccidentVo.setAccidentProvince(dto.getProvinceCode());
        personAccidentVo.setAccidentCity(dto.getAccidentCityCode());
        personAccidentVo.setAccidentCounty(dto.getAccidentCountyCode());
        dutySurveyVO.getPeopleHurtVO().setAccidentVO(personAccidentVo);
    }

    /**
     * 设置责任的立案金额
     * @param estimatePolicyFormDTO
     */
    private void setEstimatePolicyFormDTOValue(EstimatePolicyFormDTO estimatePolicyFormDTO) {
        List<EstimatePolicyDTO> dtoList = estimatePolicyFormDTO.getEstimatePolicyList();
        List<DutyEstimateLossDTO> dutyEstimateLossList = estimatePolicyFormDTO.getDutyEstimateLossList();
        for (EstimatePolicyDTO dto : dtoList) {
            //查询方案
            String packageType = policyInfoMapper.getPackageType(dto.getReportNo(),dto.getPolicyNo());
            Map<String,String> defaultDutyMap = packageTypeDutyConfig.getBillAmountDefaultDutyMap();
            for (EstimatePlanDTO planDto :dto.getEstimatePlanList()) {
                for (EstimateDutyRecordDTO dutyDTO : planDto.getEstimateDutyRecordList()) {
                    String planCode = planDto.getPlanCode();
                    String dutyCode = dutyDTO.getDutyCode();
                    //查询剩余保额，并将剩余保额和预估金额进行比较，如果剩余保额大于预估金额，则将预估金额设置为预估金额，否则设置为剩余保额
                    BigDecimal resdueAmount = this.getResdueAmount(estimatePolicyFormDTO.getReportNo(),planCode,dutyCode);
                    for(DutyEstimateLossDTO dutyEstimateLoss:dutyEstimateLossList) {
                        if(dutyEstimateLossList.size() ==1 && StringUtil.isBlank(dutyEstimateLoss.getDutyCode())) {
                            //如果配置了
                            if(MapUtil.isNotEmpty(defaultDutyMap) && defaultDutyMap.containsKey(packageType)){
                                if(defaultDutyMap.get(packageType).contains(dutyDTO.getDutyCode())) {
                                    if(estimatePolicyFormDTO.getAllBillAmount().compareTo(resdueAmount)>0){
                                        LogUtil.audit("报案号：{}，责任：{}的剩余保额{}小于估损金额：{}",estimatePolicyFormDTO.getReportNo(),dutyDTO.getDutyCode(),resdueAmount,estimatePolicyFormDTO.getAllBillAmount());
                                       dutyDTO.setEstimateAmount(resdueAmount);
                                    } else {
                                        dutyDTO.setEstimateAmount(estimatePolicyFormDTO.getAllBillAmount());
                                    }
                                }
                            } else {
                                if(dutyEstimateLoss.getEstimateLossAmount().compareTo(resdueAmount) > 0) {
                                    LogUtil.audit("报案号：{}，责任：{}的剩余保额{}小于估损金额：{}",estimatePolicyFormDTO.getReportNo(),dutyDTO.getDutyCode(),resdueAmount,estimatePolicyFormDTO.getEstimateLossAmount());
                                    dutyDTO.setEstimateAmount(resdueAmount);
                                } else {
                                    dutyDTO.setEstimateAmount(dutyEstimateLoss.getEstimateLossAmount());
                                }
                            }
                        } else if(dutyDTO.getDutyCode().equals(dutyEstimateLoss.getDutyCode())) {
                            if(dutyEstimateLoss.getEstimateLossAmount().compareTo(resdueAmount) > 0) {
                                LogUtil.audit("报案号：{}，责任：{}的剩余保额{}小于估损金额：{}",estimatePolicyFormDTO.getReportNo(),dutyDTO.getDutyCode(),resdueAmount,estimatePolicyFormDTO.getEstimateLossAmount());
                                dutyDTO.setEstimateAmount(resdueAmount);
                            } else {
                                dutyDTO.setEstimateAmount(dutyEstimateLoss.getEstimateLossAmount());
                            }
                        }
                    }
//                    dutyDTO.setEstimateAmount(estimatePolicyFormDTO.getEstimateLossAmount());
                }
            }
        }
    }

    /**
     * 入参校验及设置必要数据
     * @param dutySurveyVO
     */
    private void checkAndSetValue(DutySurveyVO dutySurveyVO) {
        //1:首先判断当前报案号是否在报案跟踪环节
        if(StringUtils.isEmptyStr(dutySurveyVO.getReportNo())){
            throw new GlobalBusinessException("报案号不能为空");
        }

        //查询是否核心已经操作过报案数据，如果做过则 阻断，如果核心操作过报案数据（流程状态发生变化即被操作过）clms_task_info表报案号赔付次数下有多条
        //数据
        Integer count = taskInfoMapper.getTpaTaskInfo(dutySurveyVO.getReportNo(),dutySurveyVO.getCaseTimes());
        if(count>1){
            throw new GlobalBusinessException("任务已在处理中.....请耐心等待！");
        }

        if(dutySurveyVO.getCaseTimes()==0){
            dutySurveyVO.setCaseTimes(1);
        }
        if(!Objects.equals(dutySurveyVO.getStatus(),"0")){
            throw new GlobalBusinessException("立案接口状态status传值为0");
        }
        dutySurveyVO.setTaskId("reportTrack");
        dutySurveyVO.setStatus("1");//线上报案不会有0-暂存，所以设置为1-发送
        if(ObjectUtil.isEmpty(dutySurveyVO.getMigrateFrom())){
            dutySurveyVO.setMigrateFrom("na");
        }
        if(CollectionUtils.isEmpty(dutySurveyVO.getCaseClass())){
            throw new GlobalBusinessException("案件类别caseClass不能为空");
        }
        if(CollectionUtils.isEmpty(dutySurveyVO.getCaseSubClass())){
            throw new GlobalBusinessException("案件类别细类列表caseSubClass不能为空");
        }
        if(ObjectUtil.isEmpty(dutySurveyVO.getEstimatePolicyFormDTO())){
            throw new GlobalBusinessException("立案信息不能为空");
        }
        if(ObjectUtil.isEmpty(dutySurveyVO.getSurveyVO())||ObjectUtil.isEmpty(dutySurveyVO.getSurveyVO().getPhoneSurveyDetail())){
            throw new GlobalBusinessException("报案查勘说明不能为空");
        }
        if(ObjectUtil.isEmpty(dutySurveyVO.getPeopleHurtVO())){
            throw new GlobalBusinessException("案件类别信息不能为空");
        }
        //人伤信息校验
        checkPeopleHurtInfo(dutySurveyVO.getPeopleHurtVO());

        //校验当前流程是否有冲突
        bpmService.processCheck(dutySurveyVO.getReportNo(), BpmConstants.OC_REPORT_TRACK,BpmConstants.OPERATION_SUBMIT);

    }

    /**
     * 人伤信息校验
     * @param peopleHurtVO
     */
    private void checkPeopleHurtInfo(PeopleHurtVO peopleHurtVO) {
        PersonHospitalVO hospitalVO = peopleHurtVO.getHospitalVO();
        if(ObjectUtil.isEmpty(hospitalVO)||CollectionUtils.isEmpty(hospitalVO.getPersonHospitalList())){
            throw new GlobalBusinessException("医院信息不能为空");
        }
        List<PersonHospitalDTO> hospitalDTOS = hospitalVO.getPersonHospitalList();
        if(ObjectUtil.isEmpty(hospitalDTOS.get(0).getMedicalStatus())){
            throw new GlobalBusinessException("治疗状态不能为空");
        }

        String  medicalStatus =hospitalDTOS.get(0).getMedicalStatus();
        if(Objects.equals(TreatCondition.TREATMENING.getType(),medicalStatus)||Objects.equals(TreatCondition.TREATMEN_ED.getType(),medicalStatus)){
            if(ObjectUtil.isEmpty(hospitalDTOS.get(0).getTherapyType())){
                throw new GlobalBusinessException("治疗类型不能为空");
            }
            if(ObjectUtil.isEmpty(hospitalDTOS.get(0).getHospitalName())){
                throw new GlobalBusinessException("医院名称不能为空");
            }
        }
        if(ObjectUtil.isEmpty(peopleHurtVO.getChannelType())){
            peopleHurtVO.setChannelType("1");
        }
        checkDiagnose(peopleHurtVO);
    }

    /**
     * 诊断信息校验
     * @param peopleHurtVO
     */
    private void checkDiagnose(PeopleHurtVO peopleHurtVO) {
        PersonDiagnoseVO personDiagnoseVO =peopleHurtVO.getDiagnoseVO();
        List<PersonDiagnoseDTO> dtoList=new ArrayList<>();
        if(Objects.isNull(personDiagnoseVO)||CollectionUtils.isEmpty(personDiagnoseVO.getDiagnoseDTOs())){
           if(CollectionUtils.isEmpty(peopleHurtVO.getAccidentCausesDetails())){
               throw new GlobalBusinessException("没有病例不存在诊断信息的情况必须传AccidentCausesDetail，不能同时为空");
           }
        }
        if(Objects.nonNull(personDiagnoseVO)&& !CollectionUtils.isEmpty(personDiagnoseVO.getDiagnoseDTOs())){
            for (PersonDiagnoseDTO pdto : personDiagnoseVO.getDiagnoseDTOs()) {
                if(ObjectUtil.isEmpty(pdto.getDiagnoseCode())){
                    throw new GlobalBusinessException("诊断信息Code不能为空");
                }
                if(ObjectUtil.isEmpty(pdto.getDiagnoseName())){
                    throw new GlobalBusinessException("诊断信息名称不能为空");
                }
                if(ObjectUtil.isEmpty(pdto.getDiagnosticTypologyCode())){
                    throw new GlobalBusinessException("诊断类型不能为空");
                }
            }
            dtoList.addAll(personDiagnoseVO.getDiagnoseDTOs());
        }
        if(!CollectionUtils.isEmpty(peopleHurtVO.getAccidentCausesDetails())){
            List<PersonDiagnoseDTO> diagnoseDTOS = new ArrayList<>();
            for (String  code :peopleHurtVO.getAccidentCausesDetails()) {
                PersonDiagnoseDTO personDiagnoseDTO =new PersonDiagnoseDTO();
                personDiagnoseDTO.setDiagnoseCode(code);
                personDiagnoseDTO.setDiagnoseName(DiagnoseTypeEnum.getName(code));
                //默认西医
                personDiagnoseDTO.setDiagnosticTypologyCode("01");
                personDiagnoseDTO.setIsSurgical("N");//默认非手术
                diagnoseDTOS.add(personDiagnoseDTO);
            }
            dtoList.addAll(diagnoseDTOS);
            peopleHurtVO.getDiagnoseVO().setDiagnoseDTOs(dtoList);
        }





    }
    public void addReportTrack(DutySurveyVO dutySurveyVO, String loginUm, String status)  {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();

        String taskId = dutySurveyVO.getTaskId();
        SurveyVO surveyVO = dutySurveyVO.getSurveyVO();
        SurveyDTO surveyDTO = new SurveyDTO();
        surveyVO.setReportNo(reportNo) ;
        surveyVO.setCaseTimes(caseTimes);
        if (surveyVO != null) {
            BeanUtils.copyProperties(surveyVO, surveyDTO);
        }
        surveyDTO.setTaskId(taskId);
        surveyDTO.setStatus(status);
        dutySurveyService.saveSurvey(reportNo, caseTimes, loginUm, surveyDTO);

    }

    /**
     * 剩余理赔额
     * @return
     */
    private BigDecimal getResdueAmount(String reportNo, String planCode, String dutyCode) {
        //查询是否共享保额信息
        DutyPayDTO duty = dutyPayMapper.getdutyShareInfo(reportNo,planCode,dutyCode);
        boolean isShareAmount=false;
        boolean isDutyShareAmount=false;
        if(Objects.isNull(duty)){
            log.info("查询责任信息失败！报案号={},险种编码={}，责任编码={}",reportNo,planCode,dutyCode);
            throw new RuntimeException( "查询责任信息失败！");
        }
        if(Objects.equals("1",duty.getIsShareAmount())){
            isShareAmount=true;
        }
        if (StringUtils.isNotEmpty(duty.getShareDutyGroup())){
            isDutyShareAmount = true;
        }
        BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay(null, reportNo, duty.getPolicyNo(), planCode,
                dutyCode, duty.getIsDutyShareAmount(), duty.getShareDutyGroup(), isShareAmount);
        HistoryPayInfoDTO historyPayInfo = new HistoryPayInfoDTO();
        historyPayInfo.setPolicyNo(duty.getPolicyNo());
        historyPayInfo.setPlanCode(planCode);
        historyPayInfo.setDutyCode(dutyCode);
        historyPayInfo.setDutyBaseAmount(duty.getBaseAmountPay());
        historyPayInfo.setDutyHistoryPay(dutyHistoryPay);

        BigDecimal dutyMaxPay = residueAmountService.getDutyMaxPay(historyPayInfo);
        return  dutyMaxPay;
    }
}
