package com.paic.ncbs.claim.service.estimate.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.ClaimRejectionApprovalRecordEntityMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.*;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.report.RegisterAmountRelMapper;
import com.paic.ncbs.claim.dao.mapper.restartcase.RestartCaseRecordMapper;
import com.paic.ncbs.claim.dao.mapper.settle.CoinsureInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO;
import com.paic.ncbs.claim.model.dto.report.RegisterAmountRelDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.dto.user.UserDTO;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeApplyVO;
import com.paic.ncbs.claim.model.vo.openapi.PolicyVO;
import com.paic.ncbs.claim.model.vo.policy.EstimatePolicyVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyDutyDetailVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyDutyVO;
import com.paic.ncbs.claim.model.vo.user.PermissionUserVO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.ClaimQueryDataSourceService;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.EndCaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.indicator.CaseIndicatorService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.prepay.PrePayService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicySurrenderInfoService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PolicyClaimCaseService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.service.waitingcenter.WaitingCenterService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.ConfigConstValues.AUDIT_NOTNEED;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Slf4j
@Transactional
@Service("estimateService")
public class EstimateServiceImpl implements EstimateService {

    @Autowired
    private AhcsCommonService ahcsCommonService;

    @Autowired
    @Lazy
    PolicyPayService policyPayService;

    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordDao;

    @Autowired
    private EstimatePolicyMapper estimatePolicyDAO;

    @Autowired
    private EstimateDutyMapper estimateDutyDAO;

    @Autowired
    private MaxPayService maxPayService;

    @Autowired
    @Lazy
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private RegisterCaseService registerCaseService;

    @Autowired
    @Lazy
    private EndCaseService endCaseService;

    @Autowired
    @Lazy
    private CaseProcessService caseProcessService;

    @Autowired
    private ClmsEstimateRecordService clmsEstimateRecordService;

    @Autowired
    private PolicyClaimCaseService policyClaimCaseService;

    @Autowired
    private EstimatePlanMapper estimatePlanDAO;

    @Autowired
    private EstimateDutyHistoryMapper estimateDutyHistoryDao;

    @Autowired
    private RegisterAmountRelMapper registerAmountRelMapper;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private PrePayService prePayService;

    @Autowired
    private ClaimRejectionApprovalRecordEntityMapper claimRejectionApprovalRecordEntityMapper ;

    @Autowired
    private CommonService commonService ;
    @Autowired
    private CoinsureService coinsureService ;
    @Autowired
    private PrintService printService;
    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    @Lazy
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;

    @Autowired
    private ReinsuranceService reinsuranceService;

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper ;

    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private EstimateIntermediateDataMapper estimateIntermediateDataMapper;
    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private ClmsPolicySurrenderInfoService clmsPolicySurrenderInfoService;

    @Autowired
    private ClaimQueryDataSourceService claimQueryDataSourceService;

    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;
    @Autowired
    private SmsInfoService smsInfoService;
    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private EstimateDutyHistoryMapper estimateDutyHistoryMapper;
    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordMapper;

    @Autowired
    private EstimateChangeMapper estimateChangeMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper ;

    @Autowired
    private DepartmentDefineService departmentDefineService;

    @Autowired
    private CaseBaseService caseBaseService;
    @Value("${tpa.account}")
    private String tpaAccountId;

    @Autowired
    private CoinsureInfoMapper coinsureInfoMapper;

    @Autowired
    private EstimateChangeService estimateChangeService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private RestartCaseRecordMapper restartCaseRecordMapper;

    @Autowired
    private CaseIndicatorService caseIndicatorService;

    @Autowired
    private PermissionUserMapper permissionUserMapper;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private PaymentItemMapper paymentItemMapper;

    @Autowired
    private WaitingCenterService unSettleApproveWaitingCenterServiceImpl;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    @Autowired
    private PrintCoreService printCoreService;

    @Override
    public void modifyEstimateDataList(EstimatePolicyFormDTO estimatePolicyFormDTO) throws GlobalBusinessException {
        String reportNo = estimatePolicyFormDTO.getReportNo();
        Integer caseTimes = estimatePolicyFormDTO.getCaseTimes();
        LogUtil.audit("#人工立案#进入人工立案了 --------caseTimes=%s,ReportNo=%s", caseTimes, reportNo);
        EstimateUtil.validEstimateDataList(estimatePolicyFormDTO);
        if (!EstimateUtil.ESTIMATE_TYPE_REGISTRATION.equals(estimatePolicyFormDTO.getEstimateType())) {
            modifyEstimateDutyRecord(estimatePolicyFormDTO);
            estimatePolicyFormDTO.setEstimateType(EstimateUtil.ESTIMATE_TYPE_TRACK);
            asyncHandleEstimateData(estimatePolicyFormDTO);
            return;
        }
        boolean hasRecore = false;
        LogUtil.audit("#人工立案#系统内该报案号下是否存在大案呈报记录hasRecore=%s,ReportNo=%s", hasRecore, reportNo);
        if (!hasRecore) {
            modifyEstimateDutyRecord(estimatePolicyFormDTO);
        }
        asyncHandleEstimateData(estimatePolicyFormDTO);
        EstimatePolicyDTO estimatePolicyDTO = estimatePolicyFormDTO.getEstimatePolicyList().get(0);
        getWholeCaseBase(estimatePolicyDTO, estimatePolicyFormDTO.getUpdatedBy());
    }

    @Override
    public void addEstimateDataList(EstimatePolicyFormDTO newEPF) throws GlobalBusinessException {
        this.modifyEstimateDutyRecord(newEPF);
    }

    @Override
    public List<EstimatePolicyDTO> getEstimateList(String reportNo, Integer caseTimes) {
        return estimatePolicyDAO.getEstimatePolicyList(reportNo, caseTimes);
    }

    @Override
    public EstimatePolicyFormDTO getEstimateDataList(EstimatePolicyFormDTO estimatePolicyForm) throws GlobalBusinessException {

        String reportNo = estimatePolicyForm.getReportNo();
        Integer caseTimes = estimatePolicyForm.getCaseTimes();
        String estimateType = estimatePolicyForm.getEstimateType();

        LogUtil.audit("#电话查勘#人工立案查询立案信息 reportNo ={},caseTimes={},estimateType={}", reportNo, caseTimes, estimateType);
        //获取ahcs_estimate_policy意键险保单预估列表
        List<EstimatePolicyDTO> estimatePolicyDTOList = estimatePolicyDAO.getByReportNoAndCaseTimes(reportNo, caseTimes);

        EstimatePolicyFormDTO estimatePolicyFormDTO = new EstimatePolicyFormDTO();
        if (ListUtils.isEmptyList(estimatePolicyDTOList)) {
            LogUtil.audit("#查询立案数据为空, reportNo = {},caseTimes = {},estimateType={}", reportNo, caseTimes, estimateType);
            return estimatePolicyFormDTO;
        }
        estimatePolicyFormDTO.setReportNo(reportNo);
        estimatePolicyFormDTO.setCaseTimes(caseTimes);

        if (!EstimateUtil.ESTIMATE_TYPE_REGISTRATION.equals(estimateType)) {
            getNewEstimatePolicyList(estimatePolicyFormDTO, estimatePolicyDTOList, EstimateUtil.ESTIMATE_TYPE_TRACK, null);
            estimatePolicyFormDTO.setEstimateType(EstimateUtil.ESTIMATE_TYPE_TRACK);
            return estimatePolicyFormDTO;
        }

        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);

        if (!EstimateUtil.YES_REGISTR.equals(wholeCaseBaseDTO.getIsRegister())) {
            LogUtil.audit("#电话查勘#人工立案查询立案信息，还没有进行过人工立案发送,没有立过案,reportNo=%s", reportNo);
            estimatePolicyFormDTO.setIsAmend(EstimateConstValues.YES_AMEND);
        } else {
            LogUtil.audit("#电话查勘#人工立案查询立案信息，进行过人工立案发送,reportNo=%s", reportNo);
            estimatePolicyFormDTO.setIsAmend(EstimateConstValues.NO_AMEND);
            estimatePolicyFormDTO.setRegisterUm(wholeCaseBaseDTO.getRegisterUm());
            estimatePolicyFormDTO.setRegisterName("admin");
            estimatePolicyFormDTO.setRegisterDate(wholeCaseBaseDTO.getRegisterDate());
        }
        getNewEstimatePolicyList(estimatePolicyFormDTO, estimatePolicyDTOList, EstimateUtil.ESTIMATE_TYPE_REGISTRATION, null);
        estimatePolicyFormDTO.setEstimateType(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        return estimatePolicyFormDTO;
    }

    @Override
    public EstimatePolicyFormDTO getRegiterEstimateDataList(EstimatePolicyFormDTO estimatePolicyForm)
            throws GlobalBusinessException {
        EstimatePolicyFormDTO estimatePolicyFormDTO = new EstimatePolicyFormDTO();

        String reportNo = estimatePolicyForm.getReportNo();
        Integer caseTimes = estimatePolicyForm.getCaseTimes();
        LogUtil.audit("#历史案件页面查询已立案信息# reportNo=" + reportNo + ",caseTimes=" + caseTimes);
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
        if (null == wholeCaseBaseDTO || !EstimateUtil.YES_REGISTR.equals(wholeCaseBaseDTO.getIsRegister())) {
            return estimatePolicyFormDTO;
        }

        return getEstimateData(estimatePolicyForm);

    }

    @Override
    public void clearEstimateDutyRecordList(List<EstimatePolicyDTO> estimatePolicyDTOList, String estimateType) {
        for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyDTOList) {
            for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                Iterator<EstimateDutyRecordDTO> it = estimatePlanDTO.getEstimateDutyRecordList().iterator();
                while (it.hasNext()) {
                    EstimateDutyRecordDTO recordDto = it.next();
                    if (!estimateType.equals(recordDto.getEstimateType())) {
                        it.remove();
                    }
                }
            }
        }
    }

    @Override
    public boolean checkExists(String reportNo, Integer caseTimes, String estimateType) {
        Integer count = estimatePolicyDAO.checkExists(reportNo, caseTimes, estimateType);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Transactional
    @Override
    public RegistSaveDTO saveEstimateByReportTrack(EstimatePolicyFormDTO estimatePolicyFormDTO, String loginUm) {
        String reportNo = estimatePolicyFormDTO.getReportNo();
        Integer caseTimes = estimatePolicyFormDTO.getCaseTimes();
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(loginUm);
        estimatePolicyFormDTO.setUpdatedBy(loginUm);

        TaskInfoDTO taskInfo = taskInfoService.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_REPORT_TRACK);
        if (Objects.isNull(taskInfo) || !BaseConstant.STRING_0.equals(taskInfo.getStatus())) {
            throw new GlobalBusinessException("任务不存在或挂起中，无法提交！");
        }

        try {
            riskPropertyService.getEstRiskPropertyPlan(estimatePolicyFormDTO.getEstimatePolicyList());
        }catch (Exception e){
            LogUtil.info("获取立案标的险种异常，不影响原有流程",e);
        }
        //获取责任未决记录表的 仲裁费、 诉讼费、 公估费等金额总和
        BigDecimal newEstimateAmountSum = EstimateUtil.getEstimateAmountSumByDutyRecord(estimatePolicyFormDTO.getEstimatePolicyList());

        if (newEstimateAmountSum.doubleValue() < 0) {
            throw new GlobalBusinessException(ErrorCode.RegisterCase.HAS_AMOUNT_RECORD_DATA);
        }
        LogUtil.audit("#开始报案跟踪立案,reportNo={},caseTimes={}", reportNo, caseTimes);
        CaseRegisterApplyDTO lastApplyInfo = registerCaseService.getLastCaseRegisterApplyDTO(reportNo, caseTimes);
        int applyTimes = 1;
        if (lastApplyInfo != null) {
            applyTimes = lastApplyInfo.getApplyTimes() + 1;
        }
        CaseRegisterApplyDTO caseRegisterApplyDTO = new CaseRegisterApplyDTO();
        caseRegisterApplyDTO.setCreatedBy(loginUm);
        caseRegisterApplyDTO.setUpdatedBy(loginUm);
        String idAhcsRegisterApply = UuidUtil.getUUID();
        caseRegisterApplyDTO.setIdAhcsCaseRegisterApply(idAhcsRegisterApply);
        caseRegisterApplyDTO.setReportNo(reportNo);
        caseRegisterApplyDTO.setCaseTimes(caseTimes);
        caseRegisterApplyDTO.setApplyUm(loginUm);
        caseRegisterApplyDTO.setApplyTimes(applyTimes);
        caseRegisterApplyDTO.setStatus("1");
        caseRegisterApplyDTO.setRegisterAmount(newEstimateAmountSum);


        this.saveManualRegisterData(estimatePolicyFormDTO, idAhcsRegisterApply);
        LogUtil.audit("立案申请, reportNo={}, #立案金额={}", reportNo, newEstimateAmountSum);

        if(!taskInfoService.hasNotFinishTaskByTaskKey(reportNo,caseTimes,BpmConstants.OC_REGISTER_REVIEW,null)){
            bpmService.suspendOrActiveTask_oc(reportNo,caseTimes,BpmConstants.OC_REPORT_TRACK,true);
//            bpmService.startProcess_oc(reportNo,caseTimes,BpmConstants.OC_REGISTER_REVIEW);
            LogUtil.audit("立案申请保存申请信息,reportNo={},caseTimes={}", reportNo, caseTimes);
            registerCaseService.addCaseRegisterApplyDTO(caseRegisterApplyDTO); // TODO 可能添加了两条一样的数据
            //生成立案审批任务
            startRegistReview(reportNo,caseTimes,newEstimateAmountSum,estimatePolicyFormDTO.getRegistryApproved());

        }else{
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG,"立案审核未完成");
        }
        LogUtil.audit("立案申请起立案审批流程结束,reportNo={},caseTimes={}", reportNo, caseTimes);

        caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.REGISTRATION_WAIT_APPROVING.getCode());

        RegistSaveDTO registSaveDTO = new RegistSaveDTO();
        //立案审批后 记录状态 如果是TPA线上数据 告诉TPA中台，不是线上数据 就不通知
        registSaveDTO.setProcessStatus(CaseProcessStatus.REGISTRATION_WAIT_APPROVING.getCode());
        //操作记录
        operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_REGISTER_REVIEW, "发起", null);
        //获取当前用户立案审批的权限等级:案件的估损金额(赔款+费用)大于等于当前处理人的审核权限的上限金额，需要审核，否则不需要审批
        boolean isNeedAudit = false;
        if (StringUtils.isNotEmpty(loginUm)) {
            if (!("system".equals(loginUm) || ConstValues.SYSTEM.equals(loginUm))) {
                PermissionUserVO permissionUserVO = new PermissionUserVO();
                permissionUserVO .setUserId(loginUm);
                permissionUserVO.setTypeCode(Constants.PERMISSION_REGIST);
                permissionUserVO.setComCode(WebServletContext.getDepartmentCode());
                List<PermissionUserDTO> permissionUserList = permissionUserMapper.getPermissionUserList(permissionUserVO);
                if (!CollectionUtils.isEmpty(permissionUserList)) {
                    PermissionUserDTO permissionUserDTO = permissionUserList.get(0);
                    if (permissionUserDTO.getMaxAmount().compareTo(newEstimateAmountSum) < 0) {
                        isNeedAudit = true;
                    }
                }else {
                    isNeedAudit = true;
                }
            }
        } else {
            isNeedAudit = true;
        }
        if (!isNeedAudit) {
            CaseRegisterApplyVO caseRegisterApplyVO = new CaseRegisterApplyVO();
            BeanUtils.copyProperties(caseRegisterApplyDTO, caseRegisterApplyVO);
            caseRegisterApplyVO.setAuditOpinion(ConstValues.AUDIT_AGREE_CODE);
            caseRegisterApplyVO.setStatus(AUDIT_NOTNEED);
            List<String> msgList = new ArrayList<>();
            sendRegisterAudit(caseRegisterApplyVO, msgList, true);
            registSaveDTO.setAutoRegisterAudit(BaseConstant.STRING_1);
        } else {
            registSaveDTO.setAutoRegisterAudit(BaseConstant.STRING_0);
        }
        return registSaveDTO;
    }

    private void startRegistReview(String reportNo,Integer caseTimes,BigDecimal registTotalAmount,String registryApproved){
        String departmentCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        String userId = userInfoDTO.getUserCode();
        String managerUserId = null;
        String managerUserName = null;
        if(!StringUtils.isEmptyStr(registryApproved)){
            String [] parts = registryApproved.split("-",2);
            managerUserId = parts[0];
            managerUserName = parts[1];
        }
        String comCode = permissionUserMapper.getComCodeByUserId(managerUserId);
        TaskInfoDTO startTask = new TaskInfoDTO();
        startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
        startTask.setTaskId(UuidUtil.getUUID());
        startTask.setReportNo(reportNo);
        startTask.setCaseTimes(caseTimes);
        startTask.setTaskDefinitionBpmKey(BpmConstants.OC_REGISTER_REVIEW);
        startTask.setAssigneeTime(new Date());
        startTask.setStatus(BpmConstants.TASK_STATUS_PENDING);
        startTask.setCreatedBy(userId);
        startTask.setUpdatedBy(userId);
        startTask.setAssigner(managerUserId);
        startTask.setAssigneeName(managerUserName);
        startTask.setApplyer(userId);
        startTask.setApplyerName(userInfoDTO.getUserName());
        //立案金额+立案费用
        Integer taskGrade = permissionService.getPermissionGrade(Constants.PERMISSION_REGIST, ConfigConstValues.HQ_DEPARTMENT,registTotalAmount);
        if(taskGrade == null){
            //查不到等级，默认给2级
            LogUtil.audit("使用默认案件等级");
            taskGrade = 5;
        }

        PermissionUserDTO permissionUser = permissionService.getLatestGrade(Constants.PERMISSION_REGIST,departmentCode,taskGrade);
        Integer auditGrade = permissionUser.getGrade();
        if(auditGrade == null){
            LogUtil.audit("使用默认审批等级");
            auditGrade = taskGrade;
        }
        startTask.setTaskGrade(taskGrade);
        startTask.setAuditGrade(auditGrade);
        if(StringUtils.isEmptyStr(comCode)){
            startTask.setDepartmentCode(permissionUser.getComCode());
        } else {
            startTask.setDepartmentCode(comCode);
        }
        taskInfoMapper.addTaskInfo(startTask);
    }

    /**
     * 立案未决入库
     * @param
     * @param loginUm
     * @param newEstimateAmountSum
     */
    private void addClmsEstimateRecord(String reportNo,Integer caseTimes, String loginUm, BigDecimal newEstimateAmountSum) {
        ClmsEstimateRecord record = new ClmsEstimateRecord();
        record.setIdClmsEstimateRecord(UuidUtil.getUUID());
        record.setReportNo(reportNo);
        Integer caseTimess = Integer.parseInt(caseTimes.toString());
        record.setCaseTimes(caseTimess);
        record.setEstimateType(EstimateTypeEnum.REGISTER_PENDING.getType());
        record.setEstimateAmount(newEstimateAmountSum);
        record.setEffectiveTime(new Date());
        record.setRecordUserId(loginUm);
        record.setCreatedBy(loginUm);
        record.setCreatedDate(new Date());
        record.setUpdatedBy(loginUm);
        record.setUpdatedDate(new Date());
        clmsEstimateRecordService.addEstimateRecord(record);
    }

    @Override
    public void asyncHandleEstimateData(EstimatePolicyFormDTO estimatePolicyFormDTO) throws GlobalBusinessException {
        Map<String, String> taskParam = new HashMap<String, String>();
        taskParam.put(ConfigConstValues.KEY_REPORT_NO, estimatePolicyFormDTO.getReportNo());
        taskParam.put(ConfigConstValues.KEY_CASE_TIMES, String.valueOf(estimatePolicyFormDTO.getCaseTimes()));
        taskParam.put(ConfigConstValues.KEY_UPDATEBY, estimatePolicyFormDTO.getUpdatedBy());
        taskParam.put(ConfigConstValues.KEY_ESTIMATE_TYPE, estimatePolicyFormDTO.getEstimateType());
        LogUtil.audit("....开始创建人工立案异步任务.....参数 =" + JSON.toJSONString(taskParam));
    }

    @Override
    public BigDecimal getAmount(String reportNo, String caseNo, Integer caseTimes) {
        return estimatePolicyDAO.getAmount(reportNo, caseNo, caseTimes);
    }

    @Override
    public List<EstimateDutyDTO> getEstimateDutyDTOList(List<String> caseNoList, Integer caseTimes) {
        return estimateDutyDAO.getEstimateDutyDTOList(caseNoList, caseTimes);
    }

    @Override
    public List<EstimatePolicySumDTO> getEstimatePolicySum(String reportNo, Integer caseTimes) {
        return estimatePolicyDAO.getEstimatePolicySum(reportNo, caseTimes);
    }

    /**
     * 根据reportNo、caseTimes、未决类型查询预估保单表单dto
     * @param reportNo
     * @param caseTimes
     * @param tache
     * @return
     * @throws GlobalBusinessException
     */
    @Override
    public EstimatePolicyFormDTO getEstimateDataByTache(String reportNo, Integer caseTimes, String tache, String scene) {
        LogUtil.audit("通过环节查询未决数据，reportNo={}, caseTimes={}, tache={}", reportNo, caseTimes, tache);
        //根据reportNo, caseTimes获取意键险保单预估列表    ahcs_estimate_policy意键险保单预估
        List<EstimatePolicyDTO> estimatePolicyDTOList = estimatePolicyDAO.getByReportNoAndCaseTimes(reportNo, caseTimes);
//        log.info("未决查询结果：reportNo:{}，result:{}", reportNo, JsonUtils.toJsonString(estimatePolicyDTOList));
        EstimatePolicyFormDTO estimatePolicyFormDTO = new EstimatePolicyFormDTO();
        if (ListUtils.isEmptyList(estimatePolicyDTOList)) {
            LogUtil.audit("#查询数据为空, reportNo = {},caseTimes = {},estimateType={}", reportNo, caseTimes, tache);
            return estimatePolicyFormDTO;
        }
        if(ListUtils.isEmptyList(estimatePolicyDTOList.get(0).getEstimatePlanList())){
            throw new GlobalBusinessException("立案险种为空");
        }
        estimatePolicyFormDTO.setReportNo(reportNo);
        estimatePolicyFormDTO.setCaseTimes(caseTimes);
        getNewEstimatePolicyList(estimatePolicyFormDTO, estimatePolicyDTOList, tache, scene);
        estimatePolicyFormDTO.setEstimateType(tache);
        //设置共保描述
        Map<String,String> coinsMap = coinsureService.getCoinsureDescByReportNo(reportNo);
        if(coinsMap.size() > 0){
            for (EstimatePolicyDTO dto : estimatePolicyDTOList) {
                dto.setCoinsuranceDesc(coinsMap.getOrDefault(dto.getPolicyNo(),""));
            }
        }

        return estimatePolicyFormDTO;
    }

    /**
     * 根据保单号获取保单明细
     * @param policyNo
     * @param caseTimes
     * @param report
     * @param tache
     * @return
     * @throws GlobalBusinessException
     */
    @Override
    public EstimatePolicyFormDTO getEstimateDataByPolicy(String policyNo, Integer caseTimes, String tache,String report) throws GlobalBusinessException {
        LogUtil.audit("通过环节查询未决数据，reportNo={}, caseTimes={}, tache={}", policyNo, caseTimes, tache);
        //policyNo, caseTimes获取意键险保单预估列表    ahcs_estimate_policy意键险保单预估
        List<EstimatePolicyDTO> estimatePolicyDTOList = estimatePolicyDAO.getEstimateDataByPolicy(policyNo, caseTimes,report);
        EstimatePolicyFormDTO estimatePolicyFormDTO = new EstimatePolicyFormDTO();
        if (ListUtils.isEmptyList(estimatePolicyDTOList)) {
            return estimatePolicyFormDTO;
        }
        estimatePolicyFormDTO.setCaseTimes(caseTimes);
        getNewEstimatePolicyList(estimatePolicyFormDTO, estimatePolicyDTOList, tache, "copy");
        estimatePolicyFormDTO.setEstimateType(tache);
        return estimatePolicyFormDTO;
    }

    @Override
    public void initPolicyClaimCaseData(String reportNo, Integer caseTimes, String userId) {
        List<PolicyClaimCaseDTO> policyClaimCaseDTOList = new ArrayList<>();

        List<EstimatePolicyDTO> estimatePolicyDTOList = getPolicyCopy(reportNo, caseTimes);

        if (ListUtils.isEmptyList(estimatePolicyDTOList)) {
            LogUtil.audit("#预估立案#初始化立案信息#承接抄单的数据为空,reportNo={" + reportNo + "}" + ",caseTimes={" + caseTimes + "}");
            return;
        }
        List<PolicyClaimCaseDTO> resultpolicyClaimCaseList = policyClaimCaseService
                .getPolicyClaimCaseListByReportNo(reportNo);
        for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyDTOList) {

            if (!EstimateUtil.isContainPolicyClaimCaseDTOList(estimatePolicyDTO, resultpolicyClaimCaseList)) {
                policyClaimCaseDTOList.add(getPolicyClaimCaseDTO(estimatePolicyDTO, userId));
            }
        }

        LogUtil.audit("报案号={}", reportNo);

        if (ListUtils.isNotEmpty(policyClaimCaseDTOList)) {
            policyClaimCaseService.addBatchPolicyClaimCase(policyClaimCaseDTOList);
        }
    }

    @Override
    public List<EstimatePolicyDTO> getPolicyCopy(String reportNo, Integer caseTimes) {
        //通过报案号，查询抄单数据，关联AHCS_POLICY_INFO,AHCS_POLICY_PLAN,AHCS_POLICY_DUTY
        List<EstimatePolicyDTO> estimatePolicys = estimatePolicyDAO.getEstimatePolicyFromCopy(reportNo, caseTimes);
        //抄单数据去重：不同险种的相同责任，取保额大的
        if(riskPropertyService.displayRiskProperty(reportNo,null)){
            riskPropertyService.setEstRiskPropertyPlan(estimatePolicys);
            return estimatePolicys;
        }
        removeDutyRepeatDate(estimatePolicys);
        return estimatePolicys;
    }

    @Override
    public void addPolicyCopyData(List<EstimatePolicyDTO> estimatePolicyDTOList) {
        List<EstimatePolicyDTO> policyList = new ArrayList<EstimatePolicyDTO>();
        List<EstimatePlanDTO> planList = new ArrayList<EstimatePlanDTO>();
        List<EstimateDutyDTO> dutyList = new ArrayList<EstimateDutyDTO>();
        List<String> caseNoList = new ArrayList<String>();
        String reportNo = estimatePolicyDTOList.get(0).getReportNo();
        Integer caseTimes = estimatePolicyDTOList.get(0).getCaseTimes();
        LogUtil.audit("#遍历保单进行赋值,reportNo={},caseTimes={}#", reportNo, caseTimes);

        for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyDTOList) {
            String policyNo = estimatePolicyDTO.getPolicyNo();
            String caseNo = estimatePolicyDTO.getCaseNo();
            caseNoList.add(caseNo);
            BigDecimal planEstimateCount = null;
            BigDecimal policyDirectClaimFee = null;
            List<EstimatePlanDTO> estimatePlanDTOList = estimatePolicyDTO.getEstimatePlanList();
            String policyId = UuidUtil.getUUID();
            for (EstimatePlanDTO estimatePlanDTO : estimatePlanDTOList) {
                BigDecimal planDirectClaimFee = null;
                String planId = UuidUtil.getUUID();
                String planCode = estimatePlanDTO.getPlanCode();
                List<EstimateDutyDTO> estimateDutyDTOList = estimatePlanDTO.getEstimateDutyList();
                BigDecimal dutyEstimateCount = null;
                for (EstimateDutyDTO estimateDutyDTO : estimateDutyDTOList) {
                    String dutyCode = estimateDutyDTO.getDutyCode();
                    EstimateDutyRecordDTO estimateDutyRecordDTO = estimateDutyRecordDao.getDutyRecordDTO(policyNo,
                            caseTimes, caseNo, planCode, dutyCode);
                    if (estimateDutyRecordDTO != null) {
                        estimateDutyDTO = EstimateUtil.covertToEstimateDutyDTO(estimateDutyDTO, estimateDutyRecordDTO);
                        estimateDutyDTO.setIdAhcsEstimatePlan(planId);
                        if (estimateDutyDTO.getEstimateAmount() != null) {
                            if (dutyEstimateCount == null) {
                                dutyEstimateCount = BigDecimal.ZERO;
                            }
                            dutyEstimateCount = dutyEstimateCount.add(estimateDutyDTO.getEstimateAmount());
                            if (null != estimateDutyDTO.getVerifyAppraiseFee()) {
                                if (planDirectClaimFee == null) {
                                    planDirectClaimFee = BigDecimal.ZERO;
                                }
                                planDirectClaimFee = BigDecimalUtils.sum(estimateDutyDTO.getVerifyAppraiseFee(),
                                        planDirectClaimFee);
                            }
                        }
                        dutyList.add(estimateDutyDTO);
                    }
                }
                if (dutyEstimateCount != null) {
                    if (planEstimateCount == null) {
                        planEstimateCount = BigDecimal.ZERO;
                    }
                    estimatePlanDTO.setEstimateAmount(dutyEstimateCount);
                    planEstimateCount = planEstimateCount.add(dutyEstimateCount);
                }
                if (null != planDirectClaimFee) {
                    estimatePlanDTO.setVerifyAppraiseFee(planDirectClaimFee);
                    if (policyDirectClaimFee == null) {
                        policyDirectClaimFee = BigDecimal.ZERO;
                    }
                    policyDirectClaimFee = BigDecimalUtils.sum(policyDirectClaimFee, planDirectClaimFee);
                }
                estimatePlanDTO.setCreatedBy(ConstValues.SYSTEM_UM);
                estimatePlanDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
                estimatePlanDTO.setPolicyNo(policyNo);
                estimatePlanDTO.setCaseTimes(caseTimes);
                estimatePlanDTO.setCaseNo(caseNo);
                estimatePlanDTO.setIdAhcsEstimatePlan(planId);
                estimatePlanDTO.setIdAhcsEstimatePolicy(policyId);
                planList.add(estimatePlanDTO);
            }
            estimatePolicyDTO.setVerifyAppraiseFee(policyDirectClaimFee);
            estimatePolicyDTO.setEstimateAmount(planEstimateCount);
            estimatePolicyDTO.setCreatedBy(ConstValues.SYSTEM_UM);
            estimatePolicyDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
            estimatePolicyDTO.setIdAhcsEstimatePolicy(policyId);
            policyList.add(estimatePolicyDTO);
        }

        LogUtil.audit("#往ahcs_estimate_policy表插入数据,reportNo={},caseTimes={}#", reportNo, caseTimes);
        ahcsCommonService.batchProcess(estimatePolicyDAO::addBatchEstimatePolicy, policyList);
        policyList.clear();

        LogUtil.audit("#往ahcs_estimate_plan表插入数据,reportNo={},caseTimes={}#", reportNo, caseTimes);
        ahcsCommonService.batchProcess(estimatePlanDAO::addBatchEstimatePlan, planList);

        planList.clear();

        LogUtil.audit("#往ahcs_estimate_duty表插入数据,reportNo={},caseTimes={}#", reportNo, caseTimes);
        List<List<EstimateDutyDTO>> dutyDTOS = ListUtils.getListByGroup(dutyList, 20);
        for (List<EstimateDutyDTO> dutyDTOList : dutyDTOS) {
            estimateDutyDAO.addBatchEstimateDuty(dutyDTOList);
        }
        dutyList.clear();

        if (caseTimes > 1) {
            int lastCaseTimes = caseTimes - 1;
            LogUtil.audit("#重开案件,要对ahcs_estimate_duty_history这个赔案号上一次赔付次数未失效的数据失效掉,reportNo={},caseTimes={}#", reportNo,
                    caseTimes);

            ahcsCommonService.batchProcess(estimateDutyHistoryDao::updateHistoryRecord, caseNoList, lastCaseTimes);

        }
        dutyList = estimateDutyDAO.getEstimateDutyDTOList(caseNoList, caseTimes);
        List<EstimateDutyHistoryDTO> estimateDutyHistoryDTOList = EstimateUtil.convertToHistoryDTO(dutyList,
                ConstValues.SYSTEM_UM, null);
        LogUtil.audit("#往ahcs_estimate_duty_history表插入数据,reportNo={},caseTimes={}#", reportNo, caseTimes);
        ahcsCommonService.batchProcess(estimateDutyHistoryDao::addHistoryRecord, estimateDutyHistoryDTOList);

    }

    @Override
    public List<EstimatePolicyDTO> getByReportNoAndCaseTimes(String reportNo, Integer caseTimes) {
        return estimatePolicyDAO.getByReportNoAndCaseTimes(reportNo, caseTimes);
    }

    @Override
    public void initEstimateData(EstimatePolicyFormDTO epf) {
        String reportNo = epf.getReportNo();
        Integer caseTimes = epf.getCaseTimes();

        List<EstimatePolicyDTO> estimatePolicyDTOList = estimatePolicyDAO.getByReportNoAndCaseTimes(reportNo, caseTimes);

        if (ListUtils.isEmptyList(estimatePolicyDTOList)) {
            LogUtil.audit("#查询立案数据为空,自动预估数据为空！  reportNo = {},caseTimes = {},estimateType={}", reportNo,
                    reportNo, epf.getEstimateType());
            return;
        }
        //未决类型（01、预估未决，02.立案未决）
        if (EstimateUtil.ESTIMATE_TYPE_TRACK.equals(epf.getEstimateType())) {
            clearEstimateDutyRecordList(estimatePolicyDTOList, EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        }

        List<EstimateDutyRecordDTO> list = getEstimateDutyRecordList(reportNo, caseTimes, estimatePolicyDTOList, epf.getEstimateType());
        ahcsCommonService.batchProcess(estimateDutyRecordDao::addEstimateDutyRecordList, list);
    }

    @Override
    public String getIdAhcsEstimateDutyRecord(String reportNo, Integer caseTimes, String estimateType) {

        return estimatePolicyDAO.getIdAhcsEstimateDutyRecord(reportNo, caseTimes, estimateType);

    }

    @Override
    public BigDecimal getLatestRegisterAmount(String reportNo, Integer caseTimes, String estimateType) {
        return estimatePolicyDAO.getLatestRegisterAmount(reportNo, caseTimes, estimateType);
    }

    @Override
    public BigDecimal getRegisterAmount(String reportNo, Integer caseTimes) {
        return estimatePolicyDAO.getRegisterAmount(reportNo, caseTimes);
    }

    /**
     * 抄单数据去重：不同险种的相同责任，取保额大的
     */
    private void removeDutyRepeatDate(List<EstimatePolicyDTO> estimatePolicys) {
        try {
            if (CollectionUtils.isEmpty(estimatePolicys)) {
                return;
            }
            for (EstimatePolicyDTO estimatePolicy : estimatePolicys) {
                for (EstimatePlanDTO estimatePlan : estimatePolicy.getEstimatePlanList()) {
                    Map<String, EstimateDutyDTO> dutyMap = new LinkedHashMap<>();
                    for (EstimateDutyDTO estimateDuty : estimatePlan.getEstimateDutyList()) {
                        String dutyCode = estimateDuty.getDutyCode();
                        EstimateDutyDTO dutyDto = dutyMap.get(dutyCode);
                        if (dutyDto == null || BigDecimalUtils.nvl(estimateDuty.getBaseAmountPay(), 0)
                                .compareTo(BigDecimalUtils.nvl(dutyDto.getBaseAmountPay(), 0)) > 0) {
                            dutyMap.put(dutyCode, estimateDuty);
                        }
                    }
                    List<EstimateDutyDTO> dutys = new ArrayList<>();
                    dutys.addAll(dutyMap.values());
                    estimatePlan.setEstimateDutyList(dutys);
                }
            }
        } catch (Exception e) {
            LogUtil.error("去除抄单重复数据异常", e);
        }
    }

    private PolicyClaimCaseDTO getPolicyClaimCaseDTO(EstimatePolicyDTO estimatePolicyDTO, String userId) {
        PolicyClaimCaseDTO policyClaimCaseDTO = new PolicyClaimCaseDTO();
        policyClaimCaseDTO.setCaseNo(estimatePolicyDTO.getCaseNo());
        policyClaimCaseDTO.setDepartmentCode(estimatePolicyDTO.getDepartmentCode());
        policyClaimCaseDTO.setDepartmentName(estimatePolicyDTO.getDepartmentName());
        policyClaimCaseDTO.setPartyNo("1");
        policyClaimCaseDTO.setPolicyNo(estimatePolicyDTO.getPolicyNo());
        policyClaimCaseDTO.setReportNo(estimatePolicyDTO.getReportNo());
        policyClaimCaseDTO.setSubpolicyNo(estimatePolicyDTO.getSubpolicyNo());
        policyClaimCaseDTO.setInsuredCode(estimatePolicyDTO.getInsuredCode());
        policyClaimCaseDTO.setName(estimatePolicyDTO.getName());
        policyClaimCaseDTO.setCreatedBy(userId);
        policyClaimCaseDTO.setUpdatedBy(userId);
        return policyClaimCaseDTO;
    }

    public void saveManualRegisterData(EstimatePolicyFormDTO estimatePolicyFormDTO, String idAhcsRegisterApply) throws GlobalBusinessException {
        String reportNo = estimatePolicyFormDTO.getReportNo();
        Integer caseTimes = estimatePolicyFormDTO.getCaseTimes();
        EstimateUtil.validEstimateDataList(estimatePolicyFormDTO);

        LogUtil.audit("报案跟踪人工立案更新立案数据, reportNo={}, caseTimes={}", reportNo, caseTimes);
        this.handleDutyRecordData(estimatePolicyFormDTO, idAhcsRegisterApply);
        EstimatePolicyDTO estimatePolicyDTO = estimatePolicyFormDTO.getEstimatePolicyList().get(0);
        getWholeCaseBaseNotApproval(estimatePolicyDTO, estimatePolicyFormDTO.getUpdatedBy());

    }

    public void getWholeCaseBase(EstimatePolicyDTO estimatePolicyDTO, String updatedBy) throws GlobalBusinessException {
        WholeCaseBaseDTO tempWholecaseDTO = wholeCaseBaseService.getWholeCaseBase(estimatePolicyDTO.getReportNo(),
                estimatePolicyDTO.getCaseTimes());
        if (EstimateUtil.YES_REGISTR.equals(tempWholecaseDTO.getIsRegister())) {
            LogUtil.audit("#已自动立案，不再进行人工立案#");
            return;
        }
        WholeCaseBaseDTO wholeCaseBaseDTO = new WholeCaseBaseDTO();
        wholeCaseBaseDTO.setRegisterUm(updatedBy);
        wholeCaseBaseDTO.setIsRegister(EstimateUtil.YES_REGISTR);
//        wholeCaseBaseDTO.setRegisterDate(new Date());
        wholeCaseBaseDTO.setUpdatedBy(updatedBy);
        wholeCaseBaseDTO.setReportNo(estimatePolicyDTO.getReportNo());
        wholeCaseBaseDTO.setCaseTimes(estimatePolicyDTO.getCaseTimes());
        wholeCaseBaseDTO.setRegistNo(commonService.generateNo( NoConstants.REGIST_NO, VoucherTypeEnum.CASE_NO,WebServletContext.getDepartmentCode()));
        wholeCaseBaseService.modifyWholeCaseBase(wholeCaseBaseDTO);
        endCaseService.batchModifyCaseBaseDTO(wholeCaseBaseDTO);
        AcceptRecordDTO acceptRecordDTO = new AcceptRecordDTO();
        String processStatus = null;
        if (null != acceptRecordDTO) {
            processStatus = ConfigConstValues.PROCESS_STATUS_PENDING_AUDIT;
        } else {
            processStatus = ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT;
        }
        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
        caseProcessDTO.setReportNo(estimatePolicyDTO.getReportNo());
        caseProcessDTO.setCaseTimes(estimatePolicyDTO.getCaseTimes());
        caseProcessDTO.setUpdatedBy(updatedBy);
        caseProcessDTO.setProcessStatus(processStatus);
        caseProcessDTO.setRegisterDeptCode(WebServletContext.getComCode());
        caseProcessService.updateCaseRegisterDept(caseProcessDTO);
    }


    public void getWholeCaseBaseNotApproval(EstimatePolicyDTO estimatePolicyDTO, String updatedBy) throws GlobalBusinessException {
        WholeCaseBaseDTO tempWholecaseDTO = wholeCaseBaseService.getWholeCaseBase(estimatePolicyDTO.getReportNo(),
                estimatePolicyDTO.getCaseTimes());
        if (EstimateUtil.YES_REGISTR.equals(tempWholecaseDTO.getIsRegister())) {
            LogUtil.audit("#已自动立案，不再进行人工立案#");
            return;
        }
        WholeCaseBaseDTO wholeCaseBaseDTO = new WholeCaseBaseDTO();
        wholeCaseBaseDTO.setRegisterUm(updatedBy);
        wholeCaseBaseDTO.setIsRegister(EstimateUtil.NO_REGISTR);
//        wholeCaseBaseDTO.setRegisterDate(new Date());
        wholeCaseBaseDTO.setUpdatedBy(updatedBy);
        wholeCaseBaseDTO.setReportNo(estimatePolicyDTO.getReportNo());
        wholeCaseBaseDTO.setCaseTimes(estimatePolicyDTO.getCaseTimes());
        wholeCaseBaseDTO.setRegistNo(commonService.generateNo( NoConstants.REGIST_NO, VoucherTypeEnum.CASE_NO,WebServletContext.getDepartmentCode()));
        wholeCaseBaseService.modifyWholeCaseBase(wholeCaseBaseDTO);
        endCaseService.batchModifyCaseBaseDTO(wholeCaseBaseDTO);
        AcceptRecordDTO acceptRecordDTO = new AcceptRecordDTO();
        String processStatus = null;
        if (null != acceptRecordDTO) {
            processStatus = ConfigConstValues.PROCESS_STATUS_PENDING_AUDIT;
        } else {
            processStatus = ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT;
        }
        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
        caseProcessDTO.setReportNo(estimatePolicyDTO.getReportNo());
        caseProcessDTO.setCaseTimes(estimatePolicyDTO.getCaseTimes());
        caseProcessDTO.setUpdatedBy(updatedBy);
        caseProcessDTO.setProcessStatus(processStatus);
        caseProcessDTO.setRegisterDeptCode(WebServletContext.getComCode());
        caseProcessService.updateCaseRegisterDept(caseProcessDTO);
    }

    private void handleDutyRecordData(EstimatePolicyFormDTO estimatePolicyFormDTO, String idAhcsRegisterApply) {
        List<RegisterAmountRelDTO> registerAmountRelDTOS = new ArrayList<>();
        List<EstimateDutyRecordDTO> recordDTOList = new ArrayList<>();
        List<String> oldIdList = new ArrayList<>();
        EstimateDutyRecordDTO newRecordDTO;
        RegisterAmountRelDTO registerAmountRelDTO = null;
        List<EstimatePlanDTO> estimatePlanList = new ArrayList<>();
        Set<String> caseNoSet = new HashSet<>();
        //定义 history 表和 change 关系键
        String idFlagHistoryChange = UuidUtil.getUUID();
        Map<String, BigDecimal> policyPaySumMap = new HashMap<>();
        Map<String, BigDecimal> policyFeeSumMap = new HashMap<>();
        List<EstimateChangeDTO> estimateChangeList = new ArrayList<>();
        EstimateChangeDTO estimateChangeDTO;

        Map<String,BigDecimal> coinsRateMap = getCoinsRateMap(estimatePolicyFormDTO.getReportNo());
        // 获取再保的保单，获取主共从共等数据
        Map<String, List<CoinsureDTO>> coinsuranceMap = coinsureService.getCoinsureListByReportNo(estimatePolicyFormDTO.getReportNo());
        boolean isRiskProperty = false; // 判断是否责任险
        for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyFormDTO.getEstimatePolicyList()) {
        	isRiskProperty = riskPropertyService.displayRiskProperty(estimatePolicyFormDTO.getReportNo(), estimatePolicyDTO.getPolicyNo());
        	if(isRiskProperty) {
	            CaseBaseEntity caseBase = caseBaseService.getCaseBaseInfo(estimatePolicyFormDTO.getReportNo(), estimatePolicyDTO.getPolicyNo(), estimatePolicyFormDTO.getCaseTimes());
	            if(caseBase != null) {
	            	caseBaseService.updateRiskGroup(caseBase.getIdClmCaseBase(),estimatePolicyDTO.getRiskGroupNo(),estimatePolicyDTO.getRiskGroupName());
	            }
        	}
            for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
               // if(StringUtils.isNotEmpty(estimatePlanDTO.getIdPlyRiskProperty())){
            	if(isRiskProperty) {
                    estimatePlanDTO.setIdAhcsEstimatePolicy(estimatePolicyDTO.getIdAhcsEstimatePolicy());
                    estimatePlanList.add(estimatePlanDTO);
                    caseNoSet.add(estimatePlanDTO.getCaseNo());
                }
                for (EstimateDutyRecordDTO estimateDutyRecordDTO : estimatePlanDTO.getEstimateDutyRecordList()) {
                    newRecordDTO = new EstimateDutyRecordDTO();
                    oldIdList.add(estimateDutyRecordDTO.getIdAhcsEstimateDutyRecord());
                    BeanUtils.copyProperties(estimateDutyRecordDTO, newRecordDTO);
                    newRecordDTO.setIdAhcsEstimateDutyRecord(UuidUtil.getUUID());
                    newRecordDTO.setEstimateType(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
                    newRecordDTO.setTaskId(BpmConstants.REPORT_TRACK);
                    newRecordDTO.setChgPayValue(estimateDutyRecordDTO.getEstimateAmount()==null?new BigDecimal(0):estimateDutyRecordDTO.getEstimateAmount());
                    BigDecimal coinsRate = coinsRateMap.get(estimatePolicyDTO.getPolicyNo());
                    if (coinsRate != null) {
                        newRecordDTO.setSsCoinsRate(coinsRate);
                        //0:内部联保(我司主联)、1:外部联保(我司主承)、2:外部联保(我司非主承)
                        String coinsuranceType = null;
                        List<CoinsureDTO> coinsureDTOList = coinsuranceMap.get(estimatePolicyDTO.getPolicyNo());
                        if (!CollectionUtils.isEmpty(coinsureDTOList)) {
                            coinsuranceType = coinsureDTOList.get(0).getCoinsuranceType();
                        }
                        if ("0".equals(coinsuranceType) || "1".equals(coinsuranceType)){
                            BigDecimal coinsChgPayValue = newRecordDTO.getChgPayValue().multiply(nvl(coinsRate, 0))
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                            newRecordDTO.setSsCoinsPayValue(coinsChgPayValue);
                            newRecordDTO.setSsCoinsChgPayValue(coinsChgPayValue);
                        } else {
                            newRecordDTO.setSsCoinsPayValue(newRecordDTO.getChgPayValue());
                            newRecordDTO.setSsCoinsChgPayValue(newRecordDTO.getChgPayValue());
                        }

                    }
                    recordDTOList.add(newRecordDTO);
                    registerAmountRelDTO = new RegisterAmountRelDTO();
                    registerAmountRelDTO.setCreatedBy(newRecordDTO.getUpdatedBy());
                    registerAmountRelDTO.setUpdatedBy(newRecordDTO.getUpdatedBy());
                    registerAmountRelDTO.setIdRegisterCaseApply(idAhcsRegisterApply);
                    registerAmountRelDTO.setIdEstimateDutyRecord(newRecordDTO.getIdAhcsEstimateDutyRecord());
                    registerAmountRelDTO.setIdAhcsRegisterAmountRel(UuidUtil.getUUID());
                    registerAmountRelDTOS.add(registerAmountRelDTO);
                    EstimateUtil.calPolicyPayAndFeeSumAmount(policyPaySumMap, policyFeeSumMap, estimateDutyRecordDTO);
                }
            }
            estimateChangeDTO = new EstimateChangeDTO();
            estimateChangeDTO.setReportNo(estimatePolicyFormDTO.getReportNo());
            estimateChangeDTO.setCaseTimes(estimatePolicyFormDTO.getCaseTimes());
            estimateChangeDTO.setPolicyNo(estimatePolicyDTO.getPolicyNo());
            estimateChangeDTO.setReason(null);//首次为空
            estimateChangeDTO.setMaxPayAmount(null);
            estimateChangeDTO.setSumDutyPayAmount(policyPaySumMap.get(estimatePolicyDTO.getPolicyNo()));
            estimateChangeDTO.setSumDutyFeeAmount(policyFeeSumMap.get(estimatePolicyDTO.getPolicyNo()));
            BigDecimal changeAmount = policyFeeSumMap.get(estimatePolicyDTO.getPolicyNo())==null
                    ?new BigDecimal(0): policyFeeSumMap.get(estimatePolicyDTO.getPolicyNo());
            String uuid = UuidUtil.getUUID();
            estimateChangeDTO.setChangeAmount(policyPaySumMap.get(estimatePolicyDTO.getPolicyNo()).add(changeAmount));
            estimateChangeDTO.setIdclmsEstimateChange(uuid);
            estimateChangeDTO.setIdFlagHistoryChange(idFlagHistoryChange);
            estimateChangeDTO.setCreatedBy(WebServletContext.getUserId());
            estimateChangeDTO.setUpdatedBy(WebServletContext.getUserId());
            estimateChangeDTO.setChangeDate(new Date());
            estimateChangeDTO.setUserId(WebServletContext.getUserId());
            estimateChangeList.add(estimateChangeDTO);
        }
        if(oldIdList.size() > 0){
            estimateDutyRecordDao.updateEffectiveByIds(oldIdList, estimatePolicyFormDTO.getUpdatedBy());
        }

        if(estimatePlanList.size() > 0){
            Integer caseTimes = estimatePolicyFormDTO.getCaseTimes();
            if(caseNoSet.size() > 0){
                List<String> caseNoList = new ArrayList<>(caseNoSet);
                estimateDutyRecordDao.updateEffectiveByCaseNo(caseNoList,caseTimes);
                estimatePlanDAO.delEstimatePlanByCaseNo(caseNoList,caseTimes);
                estimateDutyDAO.delEstimateDutyByCaseNo(caseNoList,caseTimes);
            }
            saveEstimatePlan(null, estimatePlanList, EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        }
        estimateDutyRecordDao.addDutyRecordList(recordDTOList);

        registerAmountRelMapper.addRegisterAmountRel(registerAmountRelDTOS);


        //插入duty_history
        saveDutyHistory(idFlagHistoryChange, recordDTOList);
        //插入change表
        //先将change表数据要置为失效
        estimateChangeMapper.deleteEstimateChange(estimateChangeList.get(0));
        estimateChangeMapper.addEstimateChangeList(estimateChangeList);
    }

    @Override
    public Map<String,BigDecimal> getCoinsRateMap(String reportNo) {
        Map<String,BigDecimal> coinsRateMap = new HashMap<>();
        List<PolicyVO> policyInfoList = policyInfoMapper.getOpenPolicyInfoList(reportNo);
        if (CollectionUtils.isEmpty(policyInfoList)) {
            return coinsRateMap;
        }
        /*Map<String,String> coinsMarkMap = new HashMap<>();
        for (PolicyVO vo: policyInfoList){
            coinsMarkMap.put(vo.getPolicyNo(), vo.getCoinsuranceMark());
        }*/
        List<CoinsureDTO> coinsureList = coinsureInfoMapper.getCoinsureListByReportNo(reportNo);
        if (CollectionUtils.isEmpty(coinsureList)) {
            return coinsRateMap;
        }
        //过滤得到我司共保比例
        coinsRateMap = coinsureList.stream().filter(t -> t.getCompanyFlag().equals("1")).collect(Collectors.toMap(e -> e.getPolicyNo(), CoinsureDTO::getReinsureScale));
        for (PolicyVO policyInfo : policyInfoList) {
            if (!BaseConstant.STRING_1.equals(policyInfo.getCoinsuranceMark())) {
                coinsRateMap.remove(policyInfo.getPolicyNo());
            }
        }
        return coinsRateMap;
    }

    private String saveDutyHistory(String idFlagHistoryChange, List<EstimateDutyRecordDTO> recordDTOList) {
        //查询 duty_record表数据后插入history和change(原因为空)，再修改的这次也插入history和change(原因为本次修改原因)
        List<EstimateDutyHistoryDTO> historyDTOList = new ArrayList<>();
        recordDTOList.stream().forEach(recordDTO->{
            String uuid = UuidUtil.getUUID();
            EstimateDutyHistoryDTO historyDTO = new EstimateDutyHistoryDTO();
            BeanUtils.copyProperties(recordDTO,historyDTO);
            historyDTO.setDutyEstimateAmount(recordDTO.getEstimateAmount());
            historyDTO.setEffectiveTime(recordDTO.getArchiveTime());//duty_record归档时间作为生效时间
            historyDTO.setIdAhcsEstimatDutyHistory(uuid);
            historyDTO.setIdFlagHistoryChange(idFlagHistoryChange);
            historyDTOList.add(historyDTO);
        });
        estimateDutyHistoryMapper.addHistoryRecord(historyDTOList);
        return idFlagHistoryChange;
    }

    private void saveEstimatePlan(String estimatePoliyId, List<EstimatePlanDTO> estimatePlanList, String estimateType){
        List<EstimateDutyDTO> dutyDTOList = new ArrayList<>();
        EstimateDutyDTO dutyDTO;
        List<EstimatePlanDTO> addEstimatePlanList = new ArrayList<>();
        String userId = WebServletContext.getUserId();
        for (EstimatePlanDTO planDTO : estimatePlanList) {
        	if(!StrUtil.isEmpty(estimatePoliyId))
        		planDTO.setIdAhcsEstimatePolicy(estimatePoliyId);
            planDTO.setCreatedBy(userId);
            planDTO.setUpdatedBy(userId);
            if(StringUtils.isEmptyStr(planDTO.getIdAhcsEstimatePlan())){
                planDTO.setIdAhcsEstimatePlan(UuidUtil.getUUID());
            }
            addEstimatePlanList.add(planDTO);
            for (EstimateDutyRecordDTO dutyRecordDTO : planDTO.getEstimateDutyRecordList()) {
                dutyDTO = new EstimateDutyDTO();
                BeanUtils.copyProperties(dutyRecordDTO, dutyDTO);
                dutyDTO.setIdAhcsEstimatePlan(planDTO.getIdAhcsEstimatePlan());
                dutyDTO.setIdAhcsEstimateDuty(UuidUtil.getUUID());
                dutyDTO.setEstimateType(estimateType);
                dutyDTOList.add(dutyDTO);
            }
        }

        if(ListUtils.isNotEmpty(addEstimatePlanList)){
            estimatePlanDAO.addBatchEstimatePlan(addEstimatePlanList);
        }
        estimateDutyDAO.addBatchEstimateDuty(dutyDTOList);
    }

    private void getNewEstimatePolicyList(EstimatePolicyFormDTO estimatePolicyFormDTO, List<EstimatePolicyDTO> estimatePolicyDTOList,
                                          String estimateType, String scene)
            throws GlobalBusinessException {
        Long startTime = System.currentTimeMillis();
        clearEstimateDutyRecordList(estimatePolicyDTOList, estimateType);
        maxPayService.initEstPoliciesPayMaxPay(estimatePolicyDTOList, scene);
        Long endTimes = System.currentTimeMillis();
        LogUtil.audit("最大给付额结束用时------------------------:" + (endTimes - startTime));
        estimatePolicyFormDTO.setEstimatePolicyList(estimatePolicyDTOList);
    }

    private EstimatePolicyFormDTO getTrackData(EstimatePolicyFormDTO estimatePolicyForm)
            throws GlobalBusinessException {
        EstimatePolicyFormDTO estimatePolicyFormDTO;
        estimatePolicyForm.setEstimateType(EstimateUtil.ESTIMATE_TYPE_TRACK);
        estimatePolicyFormDTO = getEstimateDataList(estimatePolicyForm);

        List<EstimatePolicyDTO> policyList = new ArrayList<>();
        for (EstimatePolicyDTO policy : estimatePolicyFormDTO.getEstimatePolicyList()) {
            if (null != policy.getEstimateAmount()) {
                policyList.add(policy);
            }
        }

        if (ListUtils.isNotEmpty(policyList)) {
            estimatePolicyFormDTO.setEstimatePolicyList(policyList);
        }
        estimatePolicyFormDTO.setEstimateType(EstimateUtil.ESTIMATE_TYPE_TRACK);
        return estimatePolicyFormDTO;
    }

    private EstimatePolicyFormDTO getEstimateData(EstimatePolicyFormDTO estimatePolicyForm)
            throws GlobalBusinessException {
        EstimatePolicyFormDTO estimatePolicyFormDTO;
        estimatePolicyForm.setEstimateType(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        estimatePolicyFormDTO = getEstimateDataList(estimatePolicyForm);
        if (null == estimatePolicyFormDTO || ListUtils.isEmptyList(estimatePolicyFormDTO.getEstimatePolicyList())) {
            return estimatePolicyFormDTO;
        }

        List<EstimatePolicyDTO> policyList = new ArrayList<>();
        for (EstimatePolicyDTO policy : estimatePolicyFormDTO.getEstimatePolicyList()) {
            if (null != policy.getEstimateAmount() && BigDecimalUtils.isGreaterZero(policy.getEstimateAmount())) {
                policyList.add(policy);
            }
        }

        if (ListUtils.isNotEmpty(policyList)) {
            estimatePolicyFormDTO.setEstimatePolicyList(policyList);
        }
        estimatePolicyFormDTO.setEstimateType(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);

        return estimatePolicyFormDTO;
    }

    private void modifyEstimateDutyRecord(EstimatePolicyFormDTO estimatePolicyFormDTO) {
        Date updatedDate = new Date();
        LogUtil.audit("#人工立案# 批量修改责任record,ReportNo=%s", estimatePolicyFormDTO.getReportNo());

        try {
            riskPropertyService.getEstRiskPropertyPlan(estimatePolicyFormDTO.getEstimatePolicyList());
        }catch (Exception e){
            LogUtil.info("获取立案标的险种异常，不影响原有流程",e);
        }

//        List<EstimateDutyRecordDTO> estimateDutyRecordList = new ArrayList<>();
        CaseBaseEntity caseBase = null;
    	boolean isChangeRiskGroup = false; // 判断是否改变方案
        for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyFormDTO.getEstimatePolicyList()) {
        	caseBase = caseBaseService.getCaseBaseInfo(estimatePolicyFormDTO.getReportNo(), estimatePolicyDTO.getPolicyNo(), estimatePolicyFormDTO.getCaseTimes());
		    if(caseBase == null) {
		    	continue;
		    }
		    isChangeRiskGroup = !StrUtil.equals(StrUtil.trimToNull(caseBase.getRiskGroupNo()),StrUtil.trimToNull(estimatePolicyDTO.getRiskGroupNo()));
        	if(isChangeRiskGroup) {
        		caseBaseService.updateRiskGroup(caseBase.getIdClmCaseBase(),estimatePolicyDTO.getRiskGroupNo(),estimatePolicyDTO.getRiskGroupName());
                estimateDutyRecordDao.updateEffectiveByCaseNo(ListUtil.of(caseBase.getCaseNo()),estimatePolicyFormDTO.getCaseTimes());
                estimatePlanDAO.delEstimatePlanByCaseNo(ListUtil.of(caseBase.getCaseNo()),estimatePolicyFormDTO.getCaseTimes());
                estimateDutyDAO.delEstimateDutyByCaseNo(ListUtil.of(caseBase.getCaseNo()),estimatePolicyFormDTO.getCaseTimes());
        		saveEstimatePlan(estimatePolicyDTO.getIdAhcsEstimatePolicy(), estimatePolicyDTO.getEstimatePlanList(), EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        	}
	        for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
	             for (EstimateDutyRecordDTO estimateDutyRecordDTO : estimatePlanDTO.getEstimateDutyRecordList()) {
	                 estimateDutyRecordDTO.setUpdatedBy(estimatePolicyFormDTO.getUpdatedBy());
	                 estimateDutyRecordDTO.setUpdatedDate(updatedDate);
	                 estimateDutyRecordDTO.setEstimateType(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
	                 estimateDutyRecordDTO.setTaskId(TacheConstants.REPORT_TRACK);
	                 // estimateDutyRecordList.add(estimateDutyRecordDTO);
	                 if(isChangeRiskGroup) {
	                	 estimateDutyRecordDTO.setIdAhcsEstimateDutyRecord(UuidUtil.getUUID());
	                	 estimateDutyRecordDao.addDutyRecordList(ListUtil.of(estimateDutyRecordDTO));
	                 }else {
	                	 estimateDutyRecordDao.modifyBatchEstimateDutyRecord(estimateDutyRecordDTO);
	                 }
	            }
	        }

        }
//        for (EstimateDutyRecordDTO estimateDutyRecordDTO : estimateDutyRecordList) {
//            estimateDutyRecordDao.modifyBatchEstimateDutyRecord(estimateDutyRecordDTO);
//        }
    }

    private List<EstimateDutyRecordDTO> getEstimateDutyRecordList(String reportNo, Integer caseTimes, List<EstimatePolicyDTO> epList, String estimateType) {

        List<EstimateDutyRecordDTO> dutyRecordList = new ArrayList<>();
        Date archiveTime = new Date();
        Map<String,BigDecimal> coinsRateMap = getCoinsRateMap(reportNo);
        // 获取再保的保单，获取主共从共等数据
        Map<String, List<CoinsureDTO>> coinsuranceMap = coinsureService.getCoinsureListByReportNo(reportNo);
        for (EstimatePolicyDTO estimatePolicyDTO : epList) {
            for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                for (EstimateDutyRecordDTO recordDTO : estimatePlanDTO.getEstimateDutyRecordList()) {
                    recordDTO.setEstimateType(estimateType);
                    if (EstimateUtil.ESTIMATE_TYPE_TRACK.equals(estimateType)) {
                        recordDTO.setTaskId(TacheConstants.TEL_ESTIAMTE_TRACK);
                    } else {
                        String taskId = TacheConstants.REPORT_TRACK;
                        boolean isNewProcess = caseProcessService.getIsNewProcess(reportNo, caseTimes);
                        if (!isNewProcess) {
                            taskId = TacheConstants.TEL_SURVEY;
                        }
                        recordDTO.setTaskId(taskId);
                        LogUtil.audit("#初始化立案数据环节为#reportNo=" + reportNo + ",isNewProcess=" + isNewProcess + ",taskId=" + taskId);
                    }
                    recordDTO.setChgPayValue(recordDTO.getEstimateAmount()==null?new BigDecimal(0):recordDTO.getEstimateAmount());
                    recordDTO.setArchiveTime(archiveTime);
                    BigDecimal coinsRate = coinsRateMap.get(estimatePolicyDTO.getPolicyNo());
                    if (coinsRate != null) {
                        recordDTO.setSsCoinsRate(coinsRate);
                        //0:内部联保(我司主联)、1:外部联保(我司主承)、2:外部联保(我司非主承)
                        String coinsuranceType = null;
                        List<CoinsureDTO> coinsureDTOList = coinsuranceMap.get(estimatePolicyDTO.getPolicyNo());
                        if (!CollectionUtils.isEmpty(coinsureDTOList)) {
                            coinsuranceType = coinsureDTOList.get(0).getCoinsuranceType();
                        }
                        if ("0".equals(coinsuranceType) || "1".equals(coinsuranceType)){
                            BigDecimal coinsPayValue = recordDTO.getChgPayValue().multiply(nvl(coinsRate, 0))
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                            recordDTO.setSsCoinsPayValue(coinsPayValue);
                            recordDTO.setSsCoinsChgPayValue(coinsPayValue);
                        } else {
                            recordDTO.setSsCoinsPayValue(recordDTO.getChgPayValue());
                            recordDTO.setSsCoinsChgPayValue(recordDTO.getChgPayValue());
                        }

                    }
                    dutyRecordList.add(recordDTO);
                }
            }
        }

        return dutyRecordList;
    }

    @Override
    public void sendRegisterAudit(CaseRegisterApplyVO caseRegisterApplyVO, List<String> msgList, boolean isAutoRegister) throws GlobalBusinessException {
        LogUtil.audit("立案审批发送reportNo={},taskId={}",caseRegisterApplyVO.getReportNo(),caseRegisterApplyVO.getTaskId());
        LogUtil.audit("立案审批获取用户, WebServletContext.getUserId()={}",WebServletContext.getUserId());
        String  loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM :WebServletContext.getUserId() ;
        String auditOpinion = caseRegisterApplyVO.getAuditOpinion();
        String reportNo = caseRegisterApplyVO.getReportNo();
        Integer caseTimes = caseRegisterApplyVO.getCaseTimes();
        CaseRegisterApplyDTO applyInfo = registerCaseService.getLastCaseRegisterApplyDTO(reportNo, caseTimes);
        caseRegisterApplyVO.setIdAhcsCaseRegisterApply(applyInfo.getIdAhcsCaseRegisterApply());
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
        caseRegisterApplyVO.setAuditUm(loginUm);
        registerCaseService.saveRegisterAuditInfo(caseRegisterApplyVO);

        boolean pass = true;
        boolean agree = ConstValues.AUDIT_AGREE_CODE.equals(auditOpinion);
        String taskIdNew = UuidUtil.getUUID();
        if(agree){
            LogUtil.audit("立案审批同意, reportNo={}, caseTimes={}", reportNo, caseTimes);
            if (!isAutoRegister){
                pass = checkRegistPremission(reportNo,caseRegisterApplyVO.getTaskId(),
                        loginUm,applyInfo.getRegisterAmount(),
                        BpmConstants.OC_REGISTER_REVIEW, caseRegisterApplyVO.getSelectedUserId(), taskIdNew);
            }
            LogUtil.audit("立案审批权限是否满足={}",pass);
            String tips = "";
            if(pass){
                LogUtil.audit("立案审批完成");
                msgList.add("立案审批完成，案件已立案");
                if((!ConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister()) && ConstValues.SYSTEM_UM.equals(wholeCaseBaseDTO.getRegisterUm()))) {
                    LogUtil.audit("还没有自动立案，更新立案金额信息, reportNo={}, caseTimes={}", reportNo, caseTimes);
                    wholeCaseBaseService.saveCaseRegisterInfo(reportNo, caseTimes, loginUm);
                }
                //查询案件是否已分配给TPA用户操作，如果已分配给TPA用户，TPA会告诉核心 并将TPA的用户告诉核心 更新到报案跟踪节点的ASSIGNER字段 以TPA开头
               TaskInfoDTO tdto=  taskInfoMapper.getTaskIsTPA(reportNo,caseTimes,BpmConstants.OC_REPORT_TRACK);
                List<String> accountList = Arrays.stream(tpaAccountId.split(",")).collect(Collectors.toList());
//               if(Objects.equals("TPA",tdto.getAssigner().substring(0,3))){
                if (accountList.contains(tdto.getAssigner()) || accountList.contains(tdto.getAssigner().substring(0, 3))) {
                    log.info("TPA的数据，立案人设置为TPA的用户" + tdto.getAssigner());
                    //设置立案人未TPA的用户
                    wholeCaseBaseDTO.setRegisterUm(tdto.getAssigner());
                } else {
                    wholeCaseBaseDTO.setRegisterUm(loginUm);
                }

                wholeCaseBaseDTO.setRegisterDate(new Date());
                wholeCaseBaseDTO.setIsRegister(EstimateUtil.YES_REGISTR);
                // 立案未决入库
                this.addClmsEstimateRecord(reportNo,caseTimes,loginUm,applyInfo.getRegisterAmount());
                //发送大案邮件(立案)
//                sendRegisterMajorCaseMail(reportNo,caseTimes);

                //立案审批后插入监管估损中间表
                LogUtil.audit("立案审批后插入监管估损中间表");
                saveEstimateIntermediateData(loginUm, reportNo, caseTimes, EstimateTypeEnum.REGISTER_PENDING.getType());
                LogUtil.audit("立案审批后插入监管估损中间表完成");

                LogUtil.audit("开始同步立案信息");
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        LogUtil.audit("claim sendMQ start------------------------");
                        // 调用渠道mq 同步立案信息
                        SyncCaseStatusDto dto = new SyncCaseStatusDto();
                        dto.setReportNo(reportNo);
                        dto.setCaseTimes(caseTimes);
                        dto.setCaseStatus(SyncCaseStatusEnum.REGISTER);
                        mqProducerSyncCaseStatusService.syncCaseStatus(dto);

                        // 立案，发送再保
                        RepayCalDTO repayCalDTO = new RepayCalDTO();
                        repayCalDTO.setReportNo(reportNo);
                        repayCalDTO.setCaseTimes(caseTimes);
                        repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.REGISTER);
                        repayCalDTO.setLoginUm(loginUm);
                        reinsuranceService.sendReinsurance(repayCalDTO);
                    }
                });

//                mqProducerClaimService.syncClaimLink(reportNo,caseTimes);
                //更新未决历史信息日期（金额大于10万的案件需要走审批，未决历史信息的日期是立案日期，更新成立案审批日期）
                //根据reportNo查询caseNo
                EstimateDutyRecordDTO estimateDutyRecordDTO = new EstimateDutyRecordDTO();
                estimateDutyRecordDTO = estimateDutyRecordMapper.getCaseNoByReportNo(reportNo);
                String caseNo = estimateDutyRecordDTO.getCaseNo();
                //更新责任未决记录表
                estimateDutyRecordMapper.updateEstimateDateByCaseNo(caseNo);
                //更新历史责任未决表
                estimateDutyHistoryMapper.updateEstimateDateByCaseNo(caseNo);
                //更新未决修正表
                estimateChangeMapper.updateEstimateDateByReportNo(reportNo);
            }else{
                wholeCaseBaseDTO.setIsRegister(EstimateUtil.NO_REGISTR);
                msgList.add("提交成功，待上级审批人继续处理");
                CaseRegisterApplyDTO caseRegisterApplyDTO = new CaseRegisterApplyDTO();
                caseRegisterApplyDTO.setCreatedBy(loginUm);
                caseRegisterApplyDTO.setUpdatedBy(loginUm);
                caseRegisterApplyDTO.setIdAhcsCaseRegisterApply(UuidUtil.getUUID());
                caseRegisterApplyDTO.setReportNo(reportNo);
                caseRegisterApplyDTO.setCaseTimes(caseTimes);
                caseRegisterApplyDTO.setApplyUm(loginUm);
                caseRegisterApplyDTO.setApplyTimes(applyInfo.getApplyTimes());//同一次申请不加1
                caseRegisterApplyDTO.setStatus("1");
                caseRegisterApplyDTO.setRegisterAmount(applyInfo.getRegisterAmount());
                registerCaseService.addCaseRegisterApplyDTO(caseRegisterApplyDTO);
                tips = " 待上级审批人继续处理";
            }
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REGISTER_REVIEW, "通过", StringUtils.isEmptyStr(caseRegisterApplyVO.getAuditRemark()) ? tips : caseRegisterApplyVO.getAuditRemark().concat(tips), loginUm);
        }else{
            LogUtil.audit("立案审批不同意, reportNo={}, caseTimes={}", reportNo, caseTimes);
            wholeCaseBaseDTO.setRegisterUm(loginUm);
            wholeCaseBaseDTO.setIsRegister(EstimateUtil.NO_REGISTR);
//            wholeCaseBaseDTO.setRegisterDate(new Date());
            wholeCaseBaseDTO.setUpdatedBy(WebServletContext.getUserId());
            wholeCaseBaseDTO.setReportNo(reportNo);
            wholeCaseBaseDTO.setCaseTimes(caseTimes);
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REGISTER_REVIEW, "不通过", caseRegisterApplyVO.getAuditRemark(), loginUm);
            //未决审批中的立案审批不同意时添加消息提醒
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_PENDING);
            noticesDTO.setReportNo(reportNo);
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(caseTimes);
            TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(reportNo, caseTimes, BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);
            if (taskInfoDTO!=null){
                noticeService.saveNotices(noticesDTO,taskInfoDTO.getAssigner());
            }

        }

        wholeCaseBaseService.modifyWholeCaseBase(wholeCaseBaseDTO);
        // 计算立案时效
        caseIndicatorService.calCaseRegIndicator(reportNo, caseTimes);
        bpmService.completeTask_oc(reportNo,caseTimes,BpmConstants.OC_REGISTER_REVIEW, caseRegisterApplyVO.getTaskId());

        if(!agree || pass){
            bpmService.suspendOrActiveTask_oc(reportNo,caseTimes,BpmConstants.OC_REPORT_TRACK,false);
        }
        if(agree && !pass){
            caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.REGISTRATION_WAIT_APPROVING.getCode());
        } else {
            caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.PENDING_REGISTER.getCode());
        }
        // 立案审批同步对接办事中心
        if(!isAutoRegister) {
            if(agree && !pass){
                unSettleApproveWaitingCenterServiceImpl.createInstance(taskIdNew);
            } else {
                // 人工审批时
                unSettleApproveWaitingCenterServiceImpl.createInstance(caseRegisterApplyVO.getTaskId());
            }
        }
        //global回流 立案，估损
        if(agree && pass){
            globalPolicyService.sendReturnRegisterAndEstimateLoss(reportNo,caseTimes);
        }
    }

    @Override
    public void saveEstimateIntermediateData(String loginUm, String reportNo, Integer caseTimes, String type) {
        /*EstimatePolicyFormDTO result = getEstimateDataByTache(reportNo, caseTimes, type, null);
        if (Objects.isNull(result)){
            return;
        }

        riskPropertyService.getEstRiskPropertyPlan(result.getEstimatePolicyList());

        //监管估损中间数据-赔付：P1，费用：P2
        try {
            List<EstimateIntermediateData> estimateIntermediateDataList = new ArrayList<>();
            for (EstimatePolicyDTO estimatePolicyDTO : result.getEstimatePolicyList()) {
                //查出以前估损表责任数据
                Map<String, EstimateIntermediateData> map = getEstimateIntermediateDataMap(estimatePolicyDTO);

                for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                    for (EstimateDutyRecordDTO estimateDutyRecordDTO : estimatePlanDTO.getEstimateDutyRecordList()) {
                        //组装估损赔款数据
                        assemblyEstimateIntermediateData(estimateDutyRecordDTO, reportNo, map, estimateDutyRecordDTO.getEstimateAmount(), estimateIntermediateDataList, caseTimes, "P1");
                        //组装估损费用数据
                        assemblyEstimateIntermediateData(estimateDutyRecordDTO, reportNo, map, estimateDutyRecordDTO.getExecuteFee(), estimateIntermediateDataList, caseTimes, "P2");
                    }
                }
            }
            estimateIntermediateDataMapper.batchInsert(estimateIntermediateDataList);
        }catch (Exception e){
            log.error("保存监管中间表处理异常",e);
        }*/
    }

    /**
     * 立案信息
     * @param dutySurveyVO
     */
    @Override
    public void dealEstimateInfo(DutySurveyVO dutySurveyVO) {
        boolean isRegister = registerCaseService.isExistRegisterRecord(dutySurveyVO.getReportNo(), dutySurveyVO.getCaseTimes());
        if (!isRegister) {
            EstimatePolicyFormDTO estimatePolicyFormDTO = dutySurveyVO.getEstimatePolicyFormDTO();
            estimatePolicyFormDTO.setUpdatedBy(dutySurveyVO.getUserId());
            this.addEstimateDataList(estimatePolicyFormDTO);
        }
    }

    @Override
    public List<EstimateChangePolicyFormDTO> getEstimateHistoryList(EstimateChangePolicyFormDTO estimateChangePolicyFormDTO) {
        List<EstimateChangePolicyFormDTO> resultList = new LinkedList<>();

        String reportNo = estimateChangePolicyFormDTO.getReportNo();
        Integer caseTimes = estimateChangePolicyFormDTO.getCaseTimes();
        if (!registerCaseService.isExistRegisterRecord(reportNo, caseTimes)){
            return resultList;
        }
        String departmentName = getDepartmentName(reportNo);
        LogUtil.audit("未决历史信息查询 --------caseTimes=%s,ReportNo=%s", caseTimes, reportNo);
        //1 查询 duty_history 表，条件 保单号、案件号、估损类型02
        List<EstimateDutyHistoryDTO> historyDTOS = estimateDutyHistoryMapper.getEstimateDutyHistoryList(reportNo, caseTimes);
        //2 查询 duty_record 表最近数据，条件 保单号、案件号、估损类型02 取一条
        /*List<EstimateDutyRecordDTO> dutyRecordDTOS = estimateDutyRecordMapper.getRecordsOfRegisterCaseByReportNo(reportNo
                , caseTimes,EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        if (CollectionUtils.isEmpty(historyDTOS)){
            throw new GlobalBusinessException("未决初始化数据查询为空");
        }*/

        //3 组装上面history表和duty_record表数据
        //组装修正change表主键与history对象关联关系
        Map<String , List<EstimateDutyHistoryDTO>> idFlagAndHistoryListMap = new HashMap<>();
        Map<String, List<EstimateDutyHistoryDTO>> idFlagPolicyNoAndHistoryListMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(historyDTOS)){
            historyDTOS.stream().forEach(historyDTO ->{
                String idFlagHistory = historyDTO.getIdFlagHistoryChange();
                if (StringUtils.isNotEmpty(idFlagHistory)) {
                    List<EstimateDutyHistoryDTO> list = new ArrayList<>();
                    if (idFlagAndHistoryListMap.containsKey(idFlagHistory)) {
                        list = idFlagAndHistoryListMap.get(idFlagHistory);
                    }
                    list.add(historyDTO);
                    idFlagAndHistoryListMap.put(idFlagHistory, list);

                    String idFlagPolicyNoKey = historyDTO.getIdFlagHistoryChange() + historyDTO.getPolicyNo();
                    List<EstimateDutyHistoryDTO> tempList = new ArrayList<>();
                    if (idFlagPolicyNoAndHistoryListMap.containsKey(idFlagPolicyNoKey)) {
                        tempList = idFlagPolicyNoAndHistoryListMap.get(idFlagPolicyNoKey);
                    }
                    tempList.add(historyDTO);
                    idFlagPolicyNoAndHistoryListMap.put(idFlagPolicyNoKey, tempList);
                }
            });
        }
        //根据报案号查询保单险种对应的名字
        List<EstimateChangePlanDTO> policyPlanInfoList = estimateDutyHistoryMapper.getEstimatePlanCodeList(reportNo,caseTimes);
        Map<String, String> policyPlanNameMap = new HashMap<>();
        policyPlanInfoList.stream().forEach(info ->{
            policyPlanNameMap.put(info.getPolicyNo()+info.getPlanCode(),info.getPlanName()+"("+info.getPlanCode()+")");
        });

        // 查change表
        List<EstimateChangeDTO> changeDTOS = estimateChangeMapper.getAllEstimateChangeList(reportNo, caseTimes);
        Map<String, List<EstimateChangeDTO>> idFlagAndChangeMap = new LinkedHashMap<>();
        //以前没有到责任层级的修正你历史收集
        List<EstimateChangeDTO> oldChangeDTOList = new LinkedList<>();
        if(!CollectionUtils.isEmpty(changeDTOS)){
            //记录每次更改id标识和对应的保单list
            changeDTOS.stream().forEach(changeDTO->{
                String idFlag = changeDTO.getIdFlagHistoryChange();
                if (StringUtils.isNotEmpty(idFlag)){
                    List<EstimateChangeDTO> list = new ArrayList<>();
                    if (idFlagAndChangeMap.containsKey(idFlag)){
                        list = idFlagAndChangeMap.get(idFlag);
                    }
                    list.add(changeDTO);
                    idFlagAndChangeMap.put(idFlag, list);
                }else {
                    oldChangeDTOList.add(changeDTO);
                }
            });
        }

        //组装每次修改保单信息,把duty_record表数据返回条件：历史修改无数据，如果有数据就会出现这条重复
       /* if (CollectionUtils.isEmpty(historyDTOS)) {
            EstimateChangePolicyFormDTO newFormDTO = getFormDTO(dutyRecordDTOS, policyPlanNameMap, departmentName, registerName, idFlagAndChangeMap.size());
            resultList.add(newFormDTO);
        }*/

        //组装history数据
        int count = idFlagAndChangeMap.size() + oldChangeDTOList.size();
        for (Map.Entry<String, List<EstimateChangeDTO>> entry : idFlagAndChangeMap.entrySet()) {
            String idFlag = entry.getKey();
            List<EstimateChangeDTO> changeList = entry.getValue();
            EstimateChangePolicyFormDTO formDTO = new EstimateChangePolicyFormDTO();
            List<EstimateDutyHistoryDTO> historyDTOList = idFlagAndHistoryListMap.get(idFlag);
            List<EstimateChangePolicyDTO> estimatePolicyList = new ArrayList<>();
            //遍历保单号change
            for (EstimateChangeDTO changeDTO : changeList){
                String policyNo = changeDTO.getPolicyNo();
                String idFlagPolicyNoKey = idFlag + policyNo;
                List<EstimateDutyHistoryDTO> dutyHistoryDTOS = idFlagPolicyNoAndHistoryListMap.get(idFlagPolicyNoKey);
                //组装当前保单下的险种、责任信息
                Map<String, List<EstimateDutyRecordDTO>> planDutyListMap = new HashMap<>();
                Map<String,String> tmpRiskGroupNo = new HashMap<>();
                Map<String,String> tmpRiskGroupName = new HashMap<>();
                dutyHistoryDTOS.stream().forEach(result ->{
                    EstimateDutyRecordDTO dutyRecordDTO = new EstimateDutyRecordDTO();
                    dutyRecordDTO.setEstimateAmount(result.getDutyEstimateAmount());
                    dutyRecordDTO.setEstimateType("02");
                    dutyRecordDTO.setDutyCode(result.getDutyCode());
                    dutyRecordDTO.setDutyName(result.getDutyName());
                    dutyRecordDTO.setPolicyNo(result.getPolicyNo());
                    dutyRecordDTO.setCaseNo(result.getCaseNo());
                    dutyRecordDTO.setBaseAmountPay(result.getBaseAmountPay());
                    dutyRecordDTO.setPlanCode(result.getPlanCode());
                    dutyRecordDTO.setArbitrageFee(result.getArbitrageFee()==null?null:result.getArbitrageFee());
                    dutyRecordDTO.setLawsuitFee(result.getLawsuitFee()==null?null:result.getLawsuitFee());
                    dutyRecordDTO.setCommonEstimateFee(result.getCommonEstimateFee()==null?null:result.getCommonEstimateFee());
                    dutyRecordDTO.setLawyerFee(result.getLawyerFee()==null?null:result.getLawyerFee());
                    dutyRecordDTO.setExecuteFee(result.getExecuteFee()==null?null:result.getExecuteFee());
                    dutyRecordDTO.setVerifyAppraiseFee(result.getVerifyAppraiseFee()==null?null:result.getVerifyAppraiseFee());
                    dutyRecordDTO.setInquireFee(result.getInquireFee()==null?null:result.getInquireFee());
                    dutyRecordDTO.setOtherFee(result.getOtherFee()==null?null:result.getOtherFee());
                    dutyRecordDTO.setSpecialSurveyFee(result.getSpecialSurveyFee());
                    //组装每个险种层下的责任
                    List<EstimateDutyRecordDTO> tempPlanDutyList = new ArrayList<>();
                    String key = result.getPolicyNo() + result.getPlanCode();
                    if (planDutyListMap.containsKey(key)){
                        tempPlanDutyList = planDutyListMap.get(key);
                    }
                    tempPlanDutyList.add(dutyRecordDTO);
                    planDutyListMap.put(key,tempPlanDutyList);
                    tmpRiskGroupNo.put(result.getPolicyNo(), result.getRiskGroupNo());
                    tmpRiskGroupName.put(result.getPolicyNo(), result.getRiskGroupName());
                });
                LogUtil.audit("组装责任: {}",JSONObject.toJSONString(historyDTOList));

                //组装险种

                List<EstimateChangePlanDTO> estimatePlanList = new ArrayList<>();
                planDutyListMap.forEach((key, value) -> {
                    EstimateChangePlanDTO planDTO = new EstimateChangePlanDTO();
                    planDTO.setPlanCode(key);
                    planDTO.setPlanName(policyPlanNameMap.get(key));
                    planDTO.setEstimateDutyRecordList(value);
                    estimatePlanList.add(planDTO);
                });
                //组装保单
                EstimateChangePolicyDTO policyDTO = new EstimateChangePolicyDTO();
                policyDTO.setEstimatePlanList(estimatePlanList);
                policyDTO.setCaseDepartment(departmentName);
                policyDTO.setPolicyNo(policyNo);
                if(!StrUtil.isEmpty(tmpRiskGroupNo.get(policyNo))) {
					policyDTO.setRiskGroupList(ListUtil.of(EstimateChangeRiskGroupDTO.builder()
							.riskGroupNo(tmpRiskGroupNo.get(policyNo)).riskGroupName(tmpRiskGroupName.get(policyNo))
							.estimatePlanList(estimatePlanList).build()));
                }else {
                	policyDTO.setEstimatePlanList(estimatePlanList);
                }
                estimatePolicyList.add(policyDTO);
                //组装每次修改保单信息
                formDTO.setReportNo(changeDTO.getReportNo());
                formDTO.setCaseTimes(changeDTO.getCaseTimes());
                formDTO.setIdEstimateChangeApply(changeDTO.getIdEstimateChangeApply());
                formDTO.setSortNum(count);
                count -- ;
                formDTO.setEstimateChangeReason(changeDTO.getReason());
                String userName = Optional.ofNullable(userInfoService.getUserNameById(changeDTO.getUserId())).orElse(null);
                formDTO.setRegisterName(userName);
                formDTO.setRegisterDate(changeDTO.getChangeDate());
                formDTO.setSumDutyPayAmount(changeDTO.getSumDutyPayAmount());
                formDTO.setSumDutyFeeAmount(changeDTO.getSumDutyFeeAmount());
                formDTO.setEstimatePolicyList(estimatePolicyList);
                formDTO.setIdFlagHistoryChange(changeDTO.getIdFlagHistoryChange());
                resultList.add(formDTO);
            }
        }
        //如果历史保单层级修改有记录，组装结果
        EstimateChangePolicyFormDTO formDTO;
        List<EstimateChangePolicyDTO> estimatePolicyList;
        List<EstimateChangePlanDTO> estimatePlanList;
        List<EstimateDutyRecordDTO> assembleDutyList;
        if(!CollectionUtils.isEmpty(oldChangeDTOList)){
            for (EstimateChangeDTO changeDTO : oldChangeDTOList){
                formDTO = new EstimateChangePolicyFormDTO();
                //需要构建责任层级页面才能展示
                EstimateDutyRecordDTO dutyRecordDTO = new EstimateDutyRecordDTO();
                dutyRecordDTO.setEstimateAmount(changeDTO.getChangeAmount());
                assembleDutyList = new ArrayList<>();
                assembleDutyList.add(dutyRecordDTO);
                //组装险种
                estimatePlanList = new ArrayList<>();
                EstimateChangePlanDTO planDTO = new EstimateChangePlanDTO();
                planDTO.setEstimateDutyRecordList(assembleDutyList);
                planDTO.setPlanName("");
                estimatePlanList.add(planDTO);
                //组装保单
                estimatePolicyList = new LinkedList<>();
                EstimateChangePolicyDTO policyDTO = new EstimateChangePolicyDTO();
                policyDTO.setCaseDepartment(departmentName);
                policyDTO.setPolicyNo(changeDTO.getPolicyNo());
                policyDTO.setEstimatePlanList(estimatePlanList);
                estimatePolicyList.add(policyDTO);
                //组装每次修改保单信息
                formDTO.setReportNo(changeDTO.getReportNo());
                formDTO.setCaseTimes(changeDTO.getCaseTimes());
                formDTO.setIdEstimateChangeApply(changeDTO.getIdEstimateChangeApply());
                formDTO.setSortNum(count);
                count -- ;
                formDTO.setEstimateChangeReason(changeDTO.getReason());
                String userName = Optional.ofNullable(userInfoService.getUserNameById(changeDTO.getUserId())).orElse(null);
                formDTO.setRegisterName(userName);
                formDTO.setRegisterDate(changeDTO.getChangeDate());
                formDTO.setSumDutyPayAmount(changeDTO.getChangeAmount());
                formDTO.setEstimatePolicyList(estimatePolicyList);
                formDTO.setIdFlagHistoryChange(changeDTO.getIdFlagHistoryChange());
                resultList.add(formDTO);
            }
        }

       /* List<RestartCaseRecordEntity> restartCaseRecordList = restartCaseRecordMapper.getRestartList(reportNo);
        if(!restartCaseRecordList.isEmpty()){
            for (RestartCaseRecordEntity restartCaseRecordEntity : restartCaseRecordList) {
                //审批意见为0-同意时才展示
                if(restartCaseRecordEntity.getApprovalOpinions() != null
                        && restartCaseRecordEntity.getApprovalOpinions() == 0){
                    EstimateDutyRecordDTO estimateDutyRecordDTO = estimateDutyRecordMapper.getCaseNoByReportNo(reportNo);
                    String caseNo = estimateDutyRecordDTO.getCaseNo();
                    //查询案件重开的未决信息
                    List<EstimateChangePolicyFormDTO> restartEstimateDutyList = estimateDutyHistoryMapper.getRestartEstimateDutyList(caseNo,restartCaseRecordEntity.getCaseTimes());
                    if(!restartEstimateDutyList.isEmpty()){
                        for(EstimateChangePolicyFormDTO estimateChangeDTO: restartEstimateDutyList){
                            resultList.add(estimateChangeDTO);
                        }
                    }
                }
            }
        }*/
        try {
            if(!resultList.isEmpty()){
                for(EstimateChangePolicyFormDTO estimateChangeDTO: resultList){
                    List<EstimateChangeApplyVO> estimateChangeApplyVOList = estimateChangeService.getEstimateChangeApplyById(estimateChangeDTO.getIdEstimateChangeApply());
                    if(!estimateChangeApplyVOList.isEmpty()){
                        estimateChangeDTO.setEstimateChangeApplyVOList(estimateChangeApplyVOList);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        resultList.sort(Comparator.comparing(EstimateChangePolicyFormDTO::getRegisterDate));

        return resultList;
    }

    private String getDepartmentName(String reportNo) {
        return ahcsPolicyInfoMapper.getDepartmentNameByReportNo(reportNo);
    }

    /**
     * 组装新的中间表数据
     * @param estimateDutyRecordDTO
     * @param reportNo
     * @param map
     * @param amount
     * @param estimateIntermediateDataList
     * @param caseTimes
     * @param estimateFlag
     */
    private static void assemblyEstimateIntermediateData(EstimateDutyRecordDTO estimateDutyRecordDTO, String reportNo, Map<String, EstimateIntermediateData> map
            , BigDecimal amount, List<EstimateIntermediateData> estimateIntermediateDataList, Integer caseTimes, String estimateFlag) {
        String  loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM :WebServletContext.getUserId() ;
        //取出上一次估损赔款数据
        StringBuilder payKey = new StringBuilder();
        payKey.append(estimateDutyRecordDTO.getPolicyNo()).append("|")
                .append(reportNo).append("|")
                .append(estimateDutyRecordDTO.getPlanCode()).append("|")
                .append(estimateDutyRecordDTO.getDutyCode()).append("|")
                .append(estimateFlag);
        EstimateIntermediateData oldPayEstimateIntermediateData = map.get(payKey.toString());

        BigDecimal oldPayAmount = (ObjectUtils.isEmpty(oldPayEstimateIntermediateData)|| oldPayEstimateIntermediateData.getEstimateAmount()==null)
                ? new BigDecimal(0) : oldPayEstimateIntermediateData.getEstimateAmount();
        BigDecimal estimatePayAmountChange = new BigDecimal(0);
        BigDecimal estimateAmount = new BigDecimal(0);
        amount = (amount == null) ? new BigDecimal(0) : amount;
        if ("P1".equals(estimateFlag)){
            //如果是赔款，计算本次与上一次的估损变化量
            estimatePayAmountChange = amount.subtract(oldPayAmount);
            estimateAmount = amount;
        }
        if ("P2".equals(estimateFlag)){
            //如果是费用,变化量就是本次金额
            estimatePayAmountChange = (estimateDutyRecordDTO.getEstimateAmount()==null) ? new BigDecimal(0) : estimateDutyRecordDTO.getEstimateAmount();
            //费用总金额 = 上次总金额 + 本次金额
            estimateAmount = oldPayAmount.add(estimatePayAmountChange);
        }
        estimateIntermediateDataList.add(EstimateIntermediateData.builder()
                        .id(UuidUtil.getUUID())
                        .policyNo(estimateDutyRecordDTO.getPolicyNo())
                        .reportNo(reportNo)
                        .caseNo(estimateDutyRecordDTO.getCaseNo())
                        .caseTimes(caseTimes)
                        .planCode(estimateDutyRecordDTO.getPlanCode())
                        .dutyCode(estimateDutyRecordDTO.getDutyCode())
                        .estimateFlag(estimateFlag)
                        .estimateAmount(estimateAmount)
                        .estimateAmountChange(estimatePayAmountChange)
                        .estimateDate(estimateDutyRecordDTO.getCreatedDate())
                        .createdBy(loginUm)
                        .updatedBy(loginUm)
                        .build());
    }

    /**
     * 查出以前估损表责任数据
     * @param estimatePolicyDTO
     * @return
     */
    private Map<String, EstimateIntermediateData> getEstimateIntermediateDataMap(EstimatePolicyDTO estimatePolicyDTO) {
        List<EstimateIntermediateData> policyList = estimateIntermediateDataMapper.queryListByCondition(
                                                        EstimateIntermediateData.builder()
                                                        .reportNo(estimatePolicyDTO.getReportNo())
                                                        .build());
        Map<String, EstimateIntermediateData> map = new HashMap<>();
        if (CollectionUtils.isEmpty(policyList)){
            return map;
        }
        //获取每个责任下最新的估损数据
        StringBuilder key ;
        for (EstimateIntermediateData data : policyList){
            key = new StringBuilder();
            key.append(data.getPolicyNo()).append("|")
                    .append(data.getReportNo()).append("|")
                    .append(data.getPlanCode()).append("|")
                    .append(data.getDutyCode()).append("|")
                    .append(data.getEstimateFlag());
            if (map.containsKey(key.toString())){
                EstimateIntermediateData temp = map.get(key.toString());
                //获取最新一条数据
                if(temp.getUpdatedDate().compareTo(data.getUpdatedDate()) < 0){
                    map.put(key.toString(),data);
                }
            }else {
                map.put(key.toString(),data);
            }
        }
        return map;
    }

    @Override
    public boolean checkRegistPremission(String reportNo, String taskId, String userId,BigDecimal estimateAmount, String bpmKey, String selectedUserId, String taskIdNew){
        LogUtil.audit("当前任务id={}",taskId);
        TaskInfoDTO taskDto = taskInfoService.getTaskDtoByTaskId(taskId);
        if(taskDto == null){
            LogUtil.audit("taskId查任务为空");
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        if(taskDto.getTaskGrade() == null){
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"案件立案审批权限为空");
        }
        //获取当前用户估损审批的权限等级:案件的估损金额(赔款+费用)大于等于当前处理人的审核权限的上限金额，需要审核，否则不需要审批
        Integer userGrade = taskDto.getTaskGrade();
        boolean isNeedAudit = false;
        if (StringUtils.isNotEmpty(userId)) {
            if (!("system".equals(userId) || ConstValues.SYSTEM.equals(userId))) {
                PermissionUserVO permissionUserVO = new PermissionUserVO();
                permissionUserVO .setUserId(userId);
                permissionUserVO.setTypeCode(Constants.PERMISSION_REGIST);
                permissionUserVO.setComCode(WebServletContext.getDepartmentCode());
                List<PermissionUserDTO> permissionUserList = permissionUserMapper.getPermissionUserList(permissionUserVO);
                if (!CollectionUtils.isEmpty(permissionUserList)) {
                    PermissionUserDTO permissionUserDTO = permissionUserList.get(0);
                    if (permissionUserDTO.getMaxAmount().compareTo(estimateAmount) < 0) {
                        isNeedAudit = true;
                        userGrade = permissionUserDTO.getGrade();
                    }
                } else {
                    throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"您未配置案件机构的立案审批权限，请联系管理员配置权限！");
                }
            }
        } else {
            isNeedAudit = true;
        }
        if(!isNeedAudit){
            //权限满足
            return true;
        }else{
            if(!BpmConstants.OC_REGISTER_REVIEW.equals(bpmKey)){
                return false;
            }
            //权限不满足，继续找高一个级别的人处理
            String departmentCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
            userGrade++;
            PermissionUserDTO permissionUser = permissionService.getLatestGrade(Constants.PERMISSION_REGIST,departmentCode,userGrade);
            String managerUserId = null;
            String managerUserName = null;
            if(!StringUtils.isEmptyStr(selectedUserId)){
                String [] parts = selectedUserId.split("-",2);
                managerUserId = parts[0];
                managerUserName = parts[1];
            }
            String comCode = permissionUserMapper.getComCodeByUserId(managerUserId);
            TaskInfoDTO startTask = new TaskInfoDTO();
            BeanUtils.copyProperties(taskDto,startTask);
            startTask.setTaskId(taskIdNew);
            startTask.setTaskDefinitionBpmKey(BpmConstants.OC_REGISTER_REVIEW);
            startTask.setAssigneeTime(new Date());
            startTask.setStatus(BpmConstants.TASK_STATUS_PENDING);
            startTask.setCreatedBy(userId);
            startTask.setUpdatedBy(userId);
            startTask.setAssigner(managerUserId);
            startTask.setAssigneeName(managerUserName);
            startTask.setApplyer(taskDto.getAssigner());
            startTask.setApplyerName(taskDto.getAssigneeName());
            startTask.setPreTaskId(taskDto.getTaskId());
            startTask.setAuditGrade(userGrade);
            startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
            startTask.setDepartmentCode(comCode);
            taskInfoMapper.addTaskInfo(startTask);
            return false;
        }
    }

    @Override
    public List<PolicyDutyVO> getEstimateByPolicyNo(EstimatePolicyVO estimatePolicyVO) throws GlobalBusinessException {
        String reportNo = estimatePolicyVO.getReportNo();
        Integer caseTimes = estimatePolicyVO.getCaseTimes();
        String policyNo = estimatePolicyVO.getPolicyNo();
        String policyCerNo = estimatePolicyVO.getPolicyCerNo();
        List<PolicyPayDTO> copyPolicyPays = policyPayService.selectFromPolicyCopy(reportNo, caseTimes);
        LogUtil.audit("get copyPolicyPays end");
        removePolicyPayByPolicyNo(copyPolicyPays, policyNo, policyCerNo);
        setPolicyPays(copyPolicyPays);
        maxPayService.initPoliciesPayMaxPay(copyPolicyPays, null);
        LogUtil.audit("查询立案信息从预估立案表中查询:reportNo:{},caseTimes:{},policyNo:{},policyCerNo:{}"
                ,reportNo,caseTimes,policyNo,policyCerNo);
        List<EstimatePolicyDTO> estimatePolicys = getEstimatePolicyDTO(reportNo, caseTimes, policyNo, policyCerNo);
        return getPolicyDutys(copyPolicyPays, estimatePolicys);
    }

    private void removePolicyPayByPolicyNo(List<PolicyPayDTO> copyPolicyPays, String policyNo, String policyCerNo) {
        if (CollectionUtils.isEmpty(copyPolicyPays) || StringUtil.isEmpty(policyNo)) {
            return;
        }
        LogUtil.audit("policyNo:{},policyCerNo:{}",policyNo,policyCerNo);
        for (int i = copyPolicyPays.size() - 1; i >= 0; i--) {
            PolicyPayDTO estimatePolicy = copyPolicyPays.get(i);
            LogUtil.audit("estimatePolicy PolicyNo:{}",estimatePolicy.getPolicyNo());
            if (!policyNo.equals(estimatePolicy.getPolicyNo())) {
                copyPolicyPays.remove(i);
            }
        }
    }


    private void setPolicyPays(List<PolicyPayDTO> copyPolicyPays) {
        for (PolicyPayDTO policyPay : copyPolicyPays) {
            policyPay.setIdAhcsPolicyPay(UuidUtil.getUUID());
            for (PlanPayDTO planPayItem : policyPay.getPlanPayArr()) {
                planPayItem.setIdAhcsPlanPay(UuidUtil.getUUID());
                planPayItem.setPolicyNo(policyPay.getPolicyNo());
                planPayItem.setCaseNo(policyPay.getCaseNo());
                planPayItem.setCaseTimes(policyPay.getCaseTimes());
                for (DutyPayDTO dutyPayItem : planPayItem.getDutyPayArr()) {
                    dutyPayItem.setPolicyNo(policyPay.getPolicyNo());
                    dutyPayItem.setPlanCode(planPayItem.getPlanCode());
                    dutyPayItem.setCaseNo(policyPay.getCaseNo());
                    dutyPayItem.setCaseTimes(policyPay.getCaseTimes());
                    for (DutyDetailPayDTO dutyDetailPayItem : dutyPayItem.getDutyDetailPayArr()) {
                        dutyDetailPayItem.setPolicyNo(policyPay.getPolicyNo());
                        dutyDetailPayItem.setPlanCode(planPayItem.getPlanCode());
                        dutyDetailPayItem.setDutyCode(dutyPayItem.getDutyCode());
                        dutyDetailPayItem.setCaseNo(policyPay.getCaseNo());
                        dutyDetailPayItem.setCaseTimes(policyPay.getCaseTimes());
                        dutyDetailPayItem.setReportNo(policyPay.getReportNo());
                    }
                }
            }
        }
    }

    private List<EstimatePolicyDTO> getEstimatePolicyDTO(String reportNo, Integer caseTimes, String policyNo,
                                                         String policyCerNo) {
        List<EstimatePolicyDTO> estimatePolicys = estimatePolicyDAO.getByReportNoAndCaseTimes(reportNo, caseTimes);
        if (ListUtils.isEmptyList(estimatePolicys)) {

            return new ArrayList<>();
        }
        clearEstimateDutyRecordList(estimatePolicys, EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        removeEstimateByPolicyNo(estimatePolicys, policyNo, policyCerNo);
        return estimatePolicys;
    }

    private void removeEstimateByPolicyNo(List<EstimatePolicyDTO> estimatePolicys, String policyNo,
                                          String policyCerNo) {
        if (CollectionUtils.isEmpty(estimatePolicys) || StringUtil.isEmpty(policyNo)) {
            return;
        }
        for (int i = estimatePolicys.size() - 1; i >= 0; i--) {
            EstimatePolicyDTO estimatePolicy = estimatePolicys.get(i);
            if (!policyNo.equals(estimatePolicy.getPolicyNo())
                    || !StringUtils.isEqualStr(policyCerNo, estimatePolicy.getPolicyCerNo())) {
                estimatePolicys.remove(i);
            }
        }
    }


    private List<PolicyDutyVO> getPolicyDutys(List<PolicyPayDTO> copyPolicyPays,
                                              List<EstimatePolicyDTO> estimatePolicys) {
        List<PolicyDutyVO> policyDutys = new ArrayList<>();
        for (PolicyPayDTO policyPay : copyPolicyPays) {
            for (PlanPayDTO plan : policyPay.getPlanPayArr()) {
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    PolicyDutyVO policyDuty = new PolicyDutyVO();
                    policyDuty.setPolicyNo(policyPay.getPolicyNo());
                    policyDuty.setPlanCode(plan.getPlanCode());
                    policyDuty.setPlanName(plan.getPlanName());
                    policyDuty.setGroupCode(plan.getGroupCode());
                    policyDuty.setDutyName(duty.getDutyName());
                    policyDuty.setDutyDesc(duty.getDutyDesc());
                    policyDuty.setDutyCode(duty.getDutyCode());
                    policyDuty.setDutyMaxPay(duty.getMaxAmountPay());
                    policyDuty.setDutyAmount(duty.getBaseAmountPay());
                    policyDuty.setEstimateAmount(getEstimateAmount(estimatePolicys, duty));
                    List<PolicyDutyDetailVO> policyDutyDetails = new ArrayList<>();
                    for (DutyDetailPayDTO dutyDetail : duty.getDutyDetailPayArr()) {
                        PolicyDutyDetailVO policyDutyDetail = new PolicyDutyDetailVO();
                        policyDutyDetail.setDetailMaxPay(dutyDetail.getMaxAmountPay());
                        policyDutyDetail.setDutyAmount(dutyDetail.getBaseAmountPay());
                        policyDutyDetail.setDutyDetailCode(dutyDetail.getDutyDetailCode());
                        policyDutyDetail.setDutyDetailName(dutyDetail.getDutyDetailName());
                        policyDutyDetail.setIsMergePolicyDuty(dutyDetail.getIsMergePolicyDuty());
                        policyDutyDetails.add(policyDutyDetail);
                    }
                    policyDuty.setAhcsPolicyDutyDetail(policyDutyDetails);
                    policyDutys.add(policyDuty);
                }
            }
        }
        return policyDutys;
    }


    private BigDecimal getEstimateAmount(List<EstimatePolicyDTO> estimatePolicys, DutyPayDTO duty) {
        BigDecimal coShare = null;  		BigDecimal estimateAmount = null;
        for (EstimatePolicyDTO estimatePolicy : estimatePolicys) {
            coShare = estimatePolicy.getCoShare();
            if (!duty.getPolicyNo().equals(estimatePolicy.getPolicyNo())) {
                continue;
            }
            for (EstimatePlanDTO estimatePlan : estimatePolicy.getEstimatePlanList()) {
                if (!duty.getPlanCode().equals(estimatePlan.getPlanCode())) {
                    continue;
                }
                for (EstimateDutyRecordDTO estimateDutyRecord : estimatePlan.getEstimateDutyRecordList()) {
                    if (!duty.getDutyCode().equals(estimateDutyRecord.getDutyCode())) {
                        continue;
                    }
                    estimateAmount = estimateDutyRecord.getEstimateAmount();
                    if (estimateAmount != null) {
                        if (BigDecimalUtils.isEqual(estimateAmount, BigDecimal.ZERO)) {
                            return estimateAmount;
                        }
                        return estimateAmount.multiply(coShare == null ? new BigDecimal(1) : coShare).setScale(2,
                                BigDecimal.ROUND_HALF_UP);
                    } else {
                        return estimateDutyRecord.getEstimateAmount();
                    }
                }
            }
        }
        return null;
    }

    @Override
    public List<EstimatePolicyDTO> getEstimatePolicies(String reportNo, Integer caseTimes, Integer subTimes) throws GlobalBusinessException {
        Long startTime = System.currentTimeMillis();

        // 查询保单、险种、责任
        List<EstimatePolicyDTO> estimatePolicyList = estimatePolicyDAO.getEstimatePolicyList(reportNo, caseTimes);

        if (estimatePolicyList == null || estimatePolicyList.isEmpty()) {
            LogUtil.info("预估信息不存在" + reportNo);
            return new ArrayList<>();
        }

        // 获取预赔次数
        if (subTimes == null) {
            subTimes = prePayService.getApprovedSubTimes(reportNo, caseTimes);
        }

        // 设置最大给付额
        maxPayService.initEstPoliciesPayMaxPay(estimatePolicyList, null);

        EstimatePolicyDTO estimatePolicy = null;
        String key = null;
        // 获取预赔详情信息，给预估对象设置预赔赔款金额
        Map<String, DutyPrepayInfoDTO> dutyPrepayInfoMap = getDutyPrepayInfoMap(reportNo, caseTimes, subTimes);

        Iterator<EstimatePolicyDTO> iterator = estimatePolicyList.iterator();
        while (iterator.hasNext()) {
            estimatePolicy = iterator.next();
            for (EstimatePlanDTO estimatePlanDTO : estimatePolicy.getEstimatePlanList()) {
                for (EstimateDutyDTO estimateDutyDTO : estimatePlanDTO.getEstimateDutyList()) {
                    key = estimatePolicy.getCaseNo() + "_" + estimatePlanDTO.getPlanCode() + "_"
                            + estimateDutyDTO.getDutyCode();
                    // 设置预赔赔款金额
                    estimateDutyDTO.setPrePayAmount(null == dutyPrepayInfoMap.get(key) ? BigDecimal.ZERO
                            : dutyPrepayInfoMap.get(key).getDutyPayAmount());
                }
            }
        }
        LogUtil.info(reportNo + "查询预估和预赔信息耗时" + (System.currentTimeMillis() - startTime));
        return estimatePolicyList;
    }

    private Map<String, DutyPrepayInfoDTO> getDutyPrepayInfoMap(String reportNo, Integer caseTimes, Integer subTimes)
            throws GlobalBusinessException {
        DutyPrepayInfoDTO dto = new DutyPrepayInfoDTO();
        dto.setReportNo(reportNo);
        dto.setSubTimes(subTimes);
        dto.setCaseTimes(caseTimes);
        List<DutyPrepayInfoDTO> dutyPrepayInfoList = prePayService.getDutyPrepayInfoList(dto);

        Map<String, DutyPrepayInfoDTO> map = new HashMap<String, DutyPrepayInfoDTO>();
        if (ListUtils.isEmptyList(dutyPrepayInfoList)) {
            return map;
        }
        String key = null;
        for (DutyPrepayInfoDTO dutyPrepayInfoDTO : dutyPrepayInfoList) {
            key = dutyPrepayInfoDTO.getCaseNo() + "_" + dutyPrepayInfoDTO.getPlanCode() + "_"
                    + dutyPrepayInfoDTO.getDutyCode();

            map.put(key, dutyPrepayInfoDTO);
        }

        return map;
    }

    @Override
    public void addBatchEstimatePolicy(List<EstimatePolicyDTO> policyList) {
        ahcsCommonService.batchProcess(estimatePolicyDAO::addBatchEstimatePolicy, policyList);
        List<EstimatePlanDTO> planList = new ArrayList<EstimatePlanDTO>();
        List<EstimateDutyDTO> dutyList = new ArrayList<EstimateDutyDTO>();
        List<String> caseNoList = new ArrayList<String>();
        for (EstimatePolicyDTO policyDTO : policyList) {
            planList.addAll(policyDTO.getEstimatePlanList());
            for (EstimatePlanDTO planDTO : policyDTO.getEstimatePlanList()) {
                dutyList.addAll(planDTO.getEstimateDutyList());
            }
        }
        ahcsCommonService.batchProcess(estimatePlanDAO::addBatchEstimatePlan, planList);
        List<List<EstimateDutyDTO>> dutyDTOS = ListUtils.getListByGroup(dutyList, 20);
        for (List<EstimateDutyDTO> dutyDTOList : dutyDTOS) {
            estimateDutyDAO.addBatchEstimateDuty(dutyDTOList);
        }
        Integer caseTimes = policyList.get(0).getCaseTimes();
        String reportNo = policyList.get(0).getReportNo();
        if (caseTimes > 1) {
            int lastCaseTimes = caseTimes - 1;
            LogUtil.audit("#批量报案案件,赔付次数为2的,要对ahcs_estimate_duty_history这个赔案号上一次赔付次数未失效的数据失效掉,reportNo={},caseTimes={}#",
                    reportNo, caseTimes);
            ahcsCommonService.batchHandlerTransactionalWithArgs(EstimateDutyHistoryMapper.class, caseNoList, 40,
                    "updateHistoryRecord", lastCaseTimes);

        }
        List<EstimateDutyHistoryDTO> estimateDutyHistoryDTOList = EstimateUtil.convertToHistoryDTO(dutyList,
                ConstValues.SYSTEM_UM,null);
        LogUtil.audit("#往ahcs_estimate_duty_history表插入数据,reportNo={},caseTimes={}#", reportNo, caseTimes);
        List<List<EstimateDutyHistoryDTO>> estimateDutyHistoryDTOS = ListUtils
                .getListByGroup(estimateDutyHistoryDTOList, 20);
        for (List<EstimateDutyHistoryDTO> estimateDutyHistoryList : estimateDutyHistoryDTOS) {
            estimateDutyHistoryDao.addHistoryRecord(estimateDutyHistoryList);
        }
    }

    @Override
    @Transactional
    public void sendRejectAudit(ClaimRejectionApprovalRecordEntity claimRejectionApprovalRecordEntity, String reportNo, Integer caseTimes) {
        if (StringUtils.isEmptyStr(claimRejectionApprovalRecordEntity.getIdAhcsCaseRegisterApply())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "拒赔主键不能为空");
        }

        String userId = WebServletContext.getUserId();
        claimRejectionApprovalRecordEntity.setUpdatedBy(userId);
        claimRejectionApprovalRecordEntity.setAuditUm(userId);
        bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_REJECT_REVIEW);
        //拒赔审批同意
        if(BaseConstant.STRING_1.equals(claimRejectionApprovalRecordEntity.getAuditOpinion())){
            if(estimateChangeService.checkEstimateChangePending(reportNo, caseTimes)){
                throw new GlobalBusinessException("当前案件存在审批中的未决修正，不能拒赔审批！");
            }
            //拒赔审批时，该报案流程之前所有流程更新为已处理
            bpmService.closeAll_oc(reportNo,caseTimes);
            clmsPolicySurrenderInfoService.checkSurrenderIsOtherCase(reportNo,caseTimes);
            clmsPolicySurrenderInfoService.applyPolicySurrender(reportNo,caseTimes);
            caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.CASE_CLOSED.getCode());
            claimRejectionApprovalRecordEntity.setAuditOpinion(BaseConstant.STRING_1);
            //删除赔付表数据、赔款支付项数据、批单
            policyPayService.deletePolicyPays(reportNo,caseTimes);

            String endCseNo =commonService.generateNo( NoConstants.END_CASE_NO, VoucherTypeEnum.END_CASE_NO,WebServletContext.getDepartmentCode()) ;
            wholeCaseBaseMapper.modifyWholeCaseEndCaseNo(endCseNo,reportNo, caseTimes);

            // 零结和拒赔 判断是否有费用需要支付
            paymentItemService.checkAndPayFee(reportNo,caseTimes);
            printService.sendPrintCore(reportNo,caseTimes);
//            mqProducerClaimCancelService.syncProducerClaimCancelLink(reportNo,caseTimes);

            //操作记录
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_REJECT_REVIEW, "通过", claimRejectionApprovalRecordEntity.getAuditRemark());

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 拒赔，回写渠道已结案
                    SyncCaseStatusDto dto = new SyncCaseStatusDto();
                    dto.setReportNo(reportNo);
                    dto.setCaseTimes(caseTimes);
                    dto.setCaseStatus(SyncCaseStatusEnum.ENDCASE);
                    dto.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_REFUSE);
                    mqProducerSyncCaseStatusService.syncCaseStatus(dto);

                    // 拒赔，发送再保
                    RepayCalDTO repayCalDTO = new RepayCalDTO();
                    repayCalDTO.setReportNo(reportNo);
                    repayCalDTO.setCaseTimes(caseTimes);
                    repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
                    repayCalDTO.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_REFUSE);
                    reinsuranceService.sendReinsurance(repayCalDTO);

                    //重开案件无赔款不发短信
                    Boolean flag = true;
                    if(caseTimes > 1){
                        Integer count = paymentItemMapper.isPay(reportNo,caseTimes);
                        if(count == 0){
                            flag = false;
                        }
                    }
                    if(flag) {
                        // 拒赔审核同意后关闭案件发送短信通知
                        String requestId = MDC.get(BaseConstant.REQUEST_ID);
                        smsInfoService.sendBusinessSmsAsync(SmsTypeEnum.REJECT_REVIEW, reportNo, caseTimes,
                                claimRejectionApprovalRecordEntity.getIdAhcsCaseRegisterApply(), requestId);
                    }
                }
            });
        }else {
            claimRejectionApprovalRecordEntity.setAuditOpinion(BaseConstant.STRING_2);
            // zjtang 最新逻辑已无挂起状态，且主流程只会有一个流程未处理的情况，故调整查询逻辑为查询挂起的主流程改为查询未处理的主流程
            TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(reportNo, caseTimes,BpmConstants.SUPEND_USE, BaseConstant.STRING_2);
            if (null == taskInfoDTO){
                throw new GlobalBusinessException("获取拒赔流程异常");
            }
            bpmService.newSuspendOrActiveTask_oc(reportNo,caseTimes,taskInfoDTO.getTaskDefinitionBpmKey(),false);
            if (taskInfoDTO.getTaskDefinitionBpmKey().equals(BpmConstants.OC_MANUAL_SETTLE)){
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.PENDING_SETTLE.getCode());
            }else {
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.PENDING_ACCEPT.getCode());
            }
            //操作记录
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_REJECT_REVIEW, "不通过", claimRejectionApprovalRecordEntity.getAuditRemark());
            //拒赔审批不同意添加消息提醒
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_EREJECT_RVIEW);
            noticesDTO.setReportNo(reportNo);
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(caseTimes);
            TaskInfoDTO taskInfoDTO1 = taskInfoService.ownNewSuspendProcess(reportNo, caseTimes, BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);
            if (taskInfoDTO1!=null){
                noticeService.saveNotices(noticesDTO,taskInfoDTO1.getAssigner());
            }
        }

        claimRejectionApprovalRecordEntityMapper.updateByPrimaryKey(claimRejectionApprovalRecordEntity);
    }

    @Override
    public EstimatePolicyFormDTO getEstimateDataByType(String reportNo, Integer caseTimes, String tache) throws GlobalBusinessException {
        LogUtil.audit("通过环节查询未决数据，reportNo={}, caseTimes={}, tache={}", reportNo, caseTimes, tache);
        //根据reportNo, caseTimes获取意键险保单预估列表    ahcs_estimate_policy意键险保单预估
        List<EstimatePolicyDTO> estimatePolicyDTOList = estimatePolicyDAO.getByReportNoAndCaseTimesForRestartCase(reportNo, caseTimes);
        EstimatePolicyFormDTO estimatePolicyFormDTO = new EstimatePolicyFormDTO();
        if (ListUtils.isEmptyList(estimatePolicyDTOList)) {
            LogUtil.audit("#查询数据为空, reportNo = {},caseTimes = {},estimateType={}", reportNo, caseTimes, tache);
            return estimatePolicyFormDTO;
        }
        estimatePolicyFormDTO.setReportNo(reportNo);
        estimatePolicyFormDTO.setCaseTimes(caseTimes);
        getNewEstimatePolicyList(estimatePolicyFormDTO, estimatePolicyDTOList, tache, null);
        estimatePolicyFormDTO.setEstimateType(tache);
        //设置共保描述
        Map<String,String> coinsMap = coinsureService.getCoinsureDescByReportNo(reportNo);
        if(coinsMap.size() > 0){
            for (EstimatePolicyDTO dto : estimatePolicyDTOList) {
                dto.setCoinsuranceDesc(coinsMap.getOrDefault(dto.getPolicyNo(),""));
            }
        }
        return estimatePolicyFormDTO;
    }
//
//    private void sendRegisterMajorCaseMail(String reportNo,Integer caseTimes){
//        //发送大案邮件(立案)
//        try {
//            BigDecimal registerAmount = clmsEstimateRecordService.getEstimateRecordAmount(reportNo,caseTimes);
//            String scope = MailScopeEnum.getRegistType(registerAmount);
//
//            BigDecimal estimateLosssAmt = clmsEstimateRecordService.getEstimateLossAmount(reportNo,caseTimes);
//            String lossScope = MailScopeEnum.getRegistType(estimateLosssAmt);
//            if(registerAmount != null && registerAmount.equals(lossScope)){
//                //报案估损金额与立案金额同一区间，不发送
//                LogUtil.audit("报案估损金额与立案金额同一区间reportNo="+reportNo+",regAmt="+registerAmount+",lossAmt="+estimateLosssAmt);
//                return;
//            }
//
//            if(StringUtils.isNotEmpty(scope)){
//                String deptCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
//                //取二级机构
//                deptCode = departmentDefineService.getLevel2DeptCode(deptCode);
//                Map<String,String> receiverMap = receiverConfigService.getMajorCaseReceiverList(deptCode, MailNodeEnum.REGIST.getType(),scope);
//                String receiver = receiverMap.get("receivers");
//                if(StringUtils.isNotEmpty(receiver)){
//                    //收件人不为空发邮件
//                    mailSendService.sendMajorCaseMail(reportNo,receiver,receiverMap.get("copyers"));
//                }
//            }
//        }catch (Exception e){
//
//        }
//
//    }
}
