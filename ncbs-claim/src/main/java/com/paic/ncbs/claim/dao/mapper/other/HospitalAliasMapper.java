package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.model.dto.other.HospitalAliasInfoDTO;
import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.model.vo.other.HospitalAliasInfoVO;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

@MapperScan
public interface HospitalAliasMapper {

    /**
     * 查询医院别名信息
     * @param hospitalAliasInfoDTO
     * @return
     */
    public List<HospitalAliasInfoVO> getHospitalAliasList(HospitalAliasInfoDTO hospitalAliasInfoDTO);

    /**
     * 删除医院别名信息
     * @param hospitalAliasInfoDTO
     * @return
     */
    void deleteHospitalInfo(HospitalAliasInfoDTO hospitalAliasInfoDTO);

    /**
     * 新增医院别名信息
     * @param dtoList
     * @return
     */
    void batchAddHospitalAlias(List<HospitalAliasInfoDTO> dtoList);

    /**
     * 查询已存在的医院别名信息
     * @param dtoList
     * @return
     */
    public List<Map<String,String>> selectExistingAliases(List<HospitalAliasInfoDTO> dtoList);


}
