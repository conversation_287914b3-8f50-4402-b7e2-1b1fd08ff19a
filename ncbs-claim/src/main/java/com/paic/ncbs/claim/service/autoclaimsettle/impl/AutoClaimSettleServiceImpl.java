package com.paic.ncbs.claim.service.autoclaimsettle.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.EndCaseConstValues;
import com.paic.ncbs.claim.common.enums.RuleTypeEnums;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.settle.EndorsementMapper;
import com.paic.ncbs.claim.model.dto.autoclaimsettle.ClaimRuleResultDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;
import com.paic.ncbs.claim.service.autoclaimsettle.AutoClaimSettleService;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.rule.AutoRuleRecordService;
import com.paic.ncbs.claim.service.rule.AutoRuleService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.settle.SettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 自动理赔服务
 */
@Slf4j
@Service
public class AutoClaimSettleServiceImpl implements AutoClaimSettleService {

    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private SettleService settleService;
    @Autowired
    private CaseZeroCancelService caseZeroCancelService;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private EndorsementMapper endorsementMapper;
    @Autowired
    private AutoRuleService autoRuleService;
    @Autowired
    private AutoRuleRecordService autoRuleRecordService;
    @Override
    public void autoSettleSubmit(DutySurveyVO dutyVO) {
        String reportNo=dutyVO.getReportNo();
        Integer caseTimes=dutyVO.getCaseTimes();
        log.info("收单提交后判断是否可以自动理算提交逻辑报案号={}",reportNo);
        log.info("开始异步初核责reportNo={}",reportNo);
        //调用自动核责，判断是否可以自动理算提交：isAutoPass返回true 表示可以自动理算后提交调用自动核赔
        ClaimRuleResultDTO claimRuleResultDTO=  autoRuleService.executeSettleRule(reportNo,caseTimes);
        log.info("报案号={}，异步核责返回结果={}",reportNo, JsonUtils.toJsonString(claimRuleResultDTO));
        if ("Y".equals(dutyVO.getIsSettle())||dutyVO.getCaseTimes()>1){
            //理算退回收单的数据 跑完核责后 数据还是流转到人工理算岗位
            log.info("报案号={}-{}，理算退回收单提交或者重开提交。",reportNo,caseTimes);
            return;
        }

        if(claimRuleResultDTO.isAutoPass()){
            //自动核责通过的数据 进行自动理算
            log.info("异步自动核责通过报案号={}",reportNo);
            //开始自动理算金额
            List<PolicyPayDTO> policyPayDTOList = policyPayService.initPolicyPayInfo(reportNo, caseTimes);
            //判断是否能自动提交理算到核赔
            if(dealpolicyData(policyPayDTOList,reportNo)){
                log.info("报案号={},发票在等待期或者0理算金额需要手动理算提交",reportNo);
                autoRuleRecordService.updateRuleResult(reportNo,caseTimes, BpmConstants.OC_MANUAL_SETTLE, ConstValues.NO);
                return;
            }
            //组装自动理算提交的参数
            SettlesFormVO settlesFormVO=new SettlesFormVO();
            List<PaymentItemComData> paymentItems= paymentItemMapper.getTPAPaymentItemDTO(reportNo, caseTimes);
            EndorsementDTO endorsement = endorsementMapper.selectByReportNoAndCaseTime(reportNo, caseTimes);
            settlesFormVO.setReportNo(reportNo);
            settlesFormVO.setCaseTimes(caseTimes);
            settlesFormVO.setPaymentItemArr(paymentItems);
            settlesFormVO.setPolicyPayArr(policyPayDTOList);
            settlesFormVO.setEndorInfo(endorsement);
            settlesFormVO.setSettleAutoSubmit("Y");//自动 理算提交
            // 理算后自动提交：理算自动提交
            settleService.settle(settlesFormVO);
            autoRuleRecordService.updateRuleResult(reportNo,caseTimes, BpmConstants.OC_MANUAL_SETTLE, ConstValues.YES);
        }else{
            // 自核责不通过的 看是否需要做零注
            log.info("自动核责不通过报案号={}",reportNo);
            // false表示不走零注流程 true表示走零注流程
            if(claimRuleResultDTO.isAutoZeroCancel()){
                log.info("自动零注释审批开始={}",reportNo);
                String zeroCancelType=claimRuleResultDTO.getZeroCancelType();
                String zeroCancelReason=claimRuleResultDTO.getZeroCancelReason();
                caseZeroCancelService.autoCaseZeroCancelProcess(reportNo, caseTimes,
                        EndCaseConstValues.APPLY_TYPE_CANCEL, zeroCancelType, zeroCancelReason, true);
            }
        }

    }

    /**
     * 判断 数据要不要自动提交
     * false:不需要自动提交，数据到理算岗 业务人员自己手动点理算提交
     * true：自动提交
     *
     * @param policyPayDTOList
     * @param reportNo
     */
    private boolean dealpolicyData(List<PolicyPayDTO> policyPayDTOList, String reportNo) {

        if(CollectionUtils.isEmpty(policyPayDTOList)){
            return  false;
        }
        BigDecimal payAmount= BigDecimal.ZERO;//理算金额
        for(PolicyPayDTO policy: policyPayDTOList){
            List<PlanPayDTO> planPayDTOList = policy.getPlanPayArr();
            for (PlanPayDTO plan : planPayDTOList) {
                List<DutyPayDTO> dutyPayDTOS = plan.getDutyPayArr();
                for (DutyPayDTO duty : dutyPayDTOS) {
                    List<DutyDetailPayDTO> detailPayDTOS =  duty.getDutyDetailPayArr();
                    for (DutyDetailPayDTO detail : detailPayDTOS) {
                        payAmount=payAmount.add(BigDecimalUtils.nvl(detail.getAutoSettleAmount(),"0"));
                        if (Objects.equals("Y", detail.getWaitFlag())) {
                            boolean isexsits = autoRuleRecordService.ruleDetailExist(reportNo, policy.getCaseTimes(),
                                    BpmConstants.OC_MANUAL_SETTLE, RuleTypeEnums.RULE_TYPE_000008.getCode());
                            if (!isexsits) {
                                autoRuleRecordService.addRuleDetailRecord(reportNo, policy.getCaseTimes(),
                                        BpmConstants.OC_MANUAL_SETTLE, RuleTypeEnums.RULE_TYPE_000008.getCode(),
                                        RuleTypeEnums.RULE_TYPE_000008.getName());
                            }
                            log.info("发票在等待期不自动理算提交报案号={}", reportNo);
                            return true;
                        }
                    }
                }
            }
        }
//        if(payAmount.compareTo(BigDecimal.ZERO)==0){
//            //理算金额为0不自动理算提交
//            log.info("理算金额为0不自动理算提交报案号={}",reportNo);
//            return true;
//        }
        return false;

    }
}
