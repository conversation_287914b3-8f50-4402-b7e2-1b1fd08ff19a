package com.paic.ncbs.claim.service.openapi.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPresonMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyHolderMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.doc.DocumentTypeMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.prepay.PrePayMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DiagnoseHospitalBillAssociationMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.verify.VerifyMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.convert.ReportQueryResConvert;
import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;
import com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.endcase.CustomerReprotInfoDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.openapi.*;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.report.*;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PreDutyVO;
import com.paic.ncbs.claim.model.vo.duty.PeopleHurtVO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.openapi.*;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.checkloss.ChannelProcessService;
import com.paic.ncbs.claim.service.checkloss.PersonHospitalService;
import com.paic.ncbs.claim.service.common.ClaimCommonQueryFileInfoService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.fileupload.DocumentTypeService;
import com.paic.ncbs.claim.service.openapi.OpenReportService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.report.LinkManService;
import com.paic.ncbs.claim.service.report.ReportAccidentExService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.settle.CustomerService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import com.paic.ncbs.claim.service.settle.PlanPayService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OpenReportServiceImpl implements OpenReportService {

    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private ReportAccidentExService reportAccidentExService;
    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private EstimateChangeService estimateChangeService;
    @Autowired
    private DutySurveyService dutySurveyService;
    @Autowired
    private LinkManService linkManService;
    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private PersonAccidentMapper personAccidentMapper;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private CaseClassMapper caseClassMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private FileInfoMapper fileInfoMapper;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private AhcsInsuredPresonMapper ahcsInsuredPresonMapper;

    @Autowired
    private WholeCaseService wholeCaseService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private ChannelProcessService channelProcessService;

    @Autowired
    private PersonHospitalService personHospitalService;

    @Autowired
    private PolicyPayMapper policyPayMapper;

    @Resource(name = "planPayService")
    private PlanPayService planPayService;

    @Autowired
    DocumentTypeMapper documentTypeMapper;

    @Autowired
    ClaimCommonQueryFileInfoService claimCommonQueryFileInfoService;
    @Autowired
    private PolicyClaimCaseMapper policyClaimCaseMapper;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private AhcsPolicyHolderMapper ahcsPolicyHolderMapper;

    @Autowired
    private VerifyMapper verifyDao;

    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;
    @Autowired
    private MedicalBillService medicalBillService;
    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    private CaseZeroCancelService caseZeroCancelService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private DocumentTypeService documentTypeService;
    @Autowired
    private DiagnoseHospitalBillAssociationMapper diagnoseHospitalBillAssociationMapper;
    @Autowired
    private PrePayMapper prePayMapper;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

    @Override
    public OpenReportInfoVO getReportInfo(String reportNo, Integer caseTimes) {
        OpenReportInfoVO vo = new OpenReportInfoVO();

        // 案件信息
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        if (Objects.nonNull(reportInfo)) {
            ReportInfoVO reportInfoVO = new ReportInfoVO();
            reportInfoVO.setReportNo(reportInfo.getReportNo());
            reportInfoVO.setCaseTimes(caseTimes);
            reportInfoVO.setReportTime(reportInfo.getReportDate());
            reportInfoVO.setReportType(reportInfo.getReportType());
            reportInfoVO.setReportSubMode(reportInfo.getReportSubMode());

            ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
            if (Objects.nonNull(reportAccident)) {
                reportInfoVO.setAccidentDate(reportAccident.getAccidentDate());
            }

            ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
            if (Objects.nonNull(reportAccidentEx)) {
                reportInfoVO.setAccidentType(reportAccidentEx.getAccidentType());
                reportInfoVO.setInsuredApplyStatus(reportAccidentEx.getInsuredApplyStatus());
                reportInfoVO.setInsuredApplyType(reportAccidentEx.getInsuredApplyType());
            }

            PersonAccidentDTO accidentDTO = personAccidentMapper.getPersonAccidentByReportNo(reportNo, null, caseTimes);
            if (Objects.nonNull(accidentDTO)) {
                reportInfoVO.setWhetherOutSideAccident(accidentDTO.getOverseasOccur());
                reportInfoVO.setAccidentProvince(accidentDTO.getProvinceCode());
                reportInfoVO.setAccidentCity(accidentDTO.getAccidentCityCode());
                reportInfoVO.setAccidentCounty(accidentDTO.getAccidentCountyCode());
                reportInfoVO.setAccidentArea(accidentDTO.getAccidentArea());
                reportInfoVO.setAccidentNation(accidentDTO.getAccidentNation());
                reportInfoVO.setAccidentPlace(accidentDTO.getAccidentPlace());
            }

            //查询整案信息表
            WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
            if (Objects.nonNull(wholeCase)) {
                reportInfoVO.setIsHugeAccident(wholeCase.getIsHugeAccident());
                reportInfoVO.setRegisterDate(wholeCase.getRegisterDate());
                reportInfoVO.setEndCaseDate(wholeCase.getEndCaseDate());
                reportInfoVO.setIndemnityConclusion(wholeCase.getIndemnityConclusion());
            }

            List<ReportInfoExEntity> reportInfoExEntityList = reportInfoExMapper.getReportInfoEx(reportNo);
            if (CollectionUtils.isNotEmpty(reportInfoExEntityList)) {
                ReportInfoExEntity reportInfoExEntity = reportInfoExEntityList.get(0);
                reportInfoVO.setResidenceProvince(reportInfoExEntity.getResidenceProvince());
                reportInfoVO.setResidenceCity(reportInfoExEntity.getResidenceCity());
                reportInfoVO.setResidenceDistrict(reportInfoExEntity.getResidenceDistrict());
                reportInfoVO.setResidenceAddress(reportInfoExEntity.getResidenceAddress());
                reportInfoVO.setClaimDealWay(reportInfoExEntity.getClaimDealWay());
            }

            String taskId = null;
            String currStatus = caseProcessService.getCaseProcessStatus(reportNo, caseTimes);
            reportInfoVO.setCaseStatus(currStatus);
            if (StringUtils.isNotEmpty(currStatus)) {
                if (currStatus.startsWith(CaseProcessStatus.PENDING_REGISTER.getCode())) {
                    taskId = "report1";
                } else if (currStatus.startsWith(CaseProcessStatus.PENDING_ACCEPT.getCode())) {
                    taskId = "reportTrack";
                } else {
                    taskId = "checkDuty";
                }
            }
            if (StringUtils.isNotEmpty(taskId)) {
                List<String> reportTypeList = caseClassMapper.getCaseClassList(reportNo, caseTimes, taskId);
                reportInfoVO.setReportTypeList(reportTypeList);
            }

            vo.setReportInfo(reportInfoVO);
        }

        // 被保险人相关信息
        ReportCustomerInfoEntity customerInfo = customerService.getReportCustomerInfoByReportNo(reportNo);
        if (Objects.nonNull(customerInfo)) {
            CustomerInfoVO customerInfoVO = new CustomerInfoVO();
            customerInfoVO.setClientNo(customerInfo.getClientNo());
            customerInfoVO.setClientName(customerInfo.getName());
            customerInfoVO.setSexCode(customerInfo.getSexCode());
            customerInfoVO.setCertificateType(customerInfo.getCertificateType());
            customerInfoVO.setCertificateNo(customerInfo.getCertificateNo());
            customerInfoVO.setBirthday(customerInfo.getBirthday());
            vo.setCustomerInfo(customerInfoVO);
        }

        // 立案信息
        List<CaseBaseDTO> caseBaseDTOList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
        if (CollectionUtils.isNotEmpty(caseBaseDTOList)) {
            List<EstimateChangeDTO> estimateList = estimateChangeService.getPolicyRegisterAmount(reportNo, caseTimes);
            Map<String, BigDecimal> policyAmoutMap = estimateList.stream().collect(Collectors.toMap(
                    EstimateChangeDTO::getPolicyNo, EstimateChangeDTO::getRegisterAmount));

            List<ClaimInfoVO> claimInfoList = Lists.newArrayList();
            for (CaseBaseDTO caseBaseDTO : caseBaseDTOList) {
                ClaimInfoVO claimInfoVO = new ClaimInfoVO();
                claimInfoVO.setClaimNo(caseBaseDTO.getRegistNo());
                claimInfoVO.setPolicyNo(caseBaseDTO.getPolicyNo());
                claimInfoVO.setClaimAmount(policyAmoutMap.get(caseBaseDTO.getPolicyNo()));
                claimInfoList.add(claimInfoVO);
            }
            vo.setClaimInfoList(claimInfoList);
        }

        // 人伤相关信息
        PeopleHurtVO peopleHurtVO = dutySurveyService.getPeopleHurtVO(reportNo, caseTimes);
        vo.setPeopleHurtVO(peopleHurtVO);

        // 联系人列表（包含申请人信息）
        List<LinkManEntity> linkManList = linkManService.getLinkMans(reportNo, caseTimes);
        if (CollectionUtils.isNotEmpty(linkManList)) {
            List<LinkManDTO> linkManDTOList = Lists.newArrayList();
            for (LinkManEntity linkMan : linkManList) {
                LinkManDTO linkManDTO = new LinkManDTO();
                linkManDTO.setApplicantPerson(linkMan.getApplicantPerson());
                linkManDTO.setApplicantType(linkMan.getApplicantType());
                linkManDTO.setCertificateType(linkMan.getCertificateType());
                linkManDTO.setCertificateNo(linkMan.getCertificateNo());
                linkManDTO.setLinkManRelation(linkMan.getLinkManRelation());
                linkManDTO.setLinkManRelationName(RelationType.getName(linkMan.getLinkManRelation()));
                linkManDTO.setLinkManTelephone(linkMan.getLinkManTelephone());
                linkManDTO.setLinkManName(linkMan.getLinkManName());
                linkManDTO.setIsReport(linkMan.getIsReport());
                linkManDTO.setSendMessage(linkMan.getSendMessage());
                linkManDTOList.add(linkManDTO);
            }
            vo.setLinkManList(linkManDTOList);
        }

        // 报案号关联保单信息
        List<PolicyVO> policyInfoList = policyInfoMapper.getOpenPolicyInfoList(reportNo);
        vo.setPolicyInfoDTOList(policyInfoList);

        // 保单赔付信息
        List<PolicyPayDTO> policyPayList = policyPayService.getByReportNo(reportNo, caseTimes);
        vo.setPolicyPayList(policyPayList);

        // 领款信息
        List<PaymentVO> paymentList = getPaymentList(reportNo, caseTimes);
        vo.setPaymentList(paymentList);

        // 单证多媒体材料
        List<String> documentGroupIdList = fileInfoMapper.getDocumentGroupIdByReportNoAndCaseTimes(reportNo, caseTimes);
        if (CollectionUtils.isNotEmpty(documentGroupIdList) && StringUtils.isNotEmpty(documentGroupIdList.get(0))) {
            List<FileDocumentDTO> list = fileInfoMapper.queryDocumentByGroupId(documentGroupIdList.get(0));
            if (CollectionUtils.isNotEmpty(list)) {
                List<MediaVO> mediaVOList = Lists.newArrayList();
                for (FileDocumentDTO documentDTO : list) {
                    MediaVO mediaVO = new MediaVO();
                    mediaVO.setSmallTypeCode(documentDTO.getDocumentType());
                    mediaVO.setMediaUrl(documentDTO.getUrl());
                    mediaVOList.add(mediaVO);
                }
                vo.setMediaList(mediaVOList);
            }
        }
        //医疗账单信息
        List<MedicalBillInfoVO> medicalBillInfoVOS = medicalBillService.getMedicalBillInfoForPrint(reportNo, caseTimes);
        if (CollectionUtils.isNotEmpty(medicalBillInfoVOS)) {
            for (MedicalBillInfoVO medicalBillInfoVO : medicalBillInfoVOS) {
                List<DiagnoseHospitalBillAssociationDTO> diagnoseHospitalBillAssociationList =
                        diagnoseHospitalBillAssociationMapper.getDiagnoseHospitalBillAssociation(medicalBillInfoVO.getIdAhcsBillInfo());
                medicalBillInfoVO.setDiagnoseHospitalBillAssociationList(diagnoseHospitalBillAssociationList);
            }
        }
        vo.setMedicalBillInfoList(medicalBillInfoVOS);
        return vo;
    }

    @Override
    public List<PaymentVO> getPaymentList(String reportNo, Integer caseTimes) {
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        List<PaymentItemDTO> paymentItemList = paymentItemMapper.getPrePaymentItem(paymentItemDTO);

        List<PaymentVO> voList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(paymentItemList)) {
            for (PaymentItemDTO dto : paymentItemList) {
                // 剔除掉费用的
                if (!(PaymentTypeEnum.PAY.getType().equals(dto.getPaymentType()) || PaymentTypeEnum.PRE_PAY.getType().equals(dto.getPaymentType()))) {
                    continue;
                }
                PaymentVO vo = new PaymentVO();
                vo.setPayAmount(dto.getPaymentAmount());
                vo.setPolicyNo(dto.getPolicyNo());
                vo.setClientBankAccount(dto.getClientBankAccount());
                vo.setClientBankCode(dto.getClientBankCode());
                vo.setClientBankName(dto.getClientBankName());
                vo.setProvinceCode(dto.getProvinceName());
                vo.setCityCode(dto.getCityName());
                vo.setRegionCode(dto.getRegionCode());
                vo.setClientName(dto.getClientName());
                vo.setPayTime(dto.getPayDate());
                vo.setCollectPayApproach(dto.getCollectPayApproach());
                vo.setCustomerPickUp("2");//默认值非指令支付
                //判断结算方式是否时指令支付
                if(Objects.equals(CollectPayApproachEnum.ORDER_PAYMENT.getType(), vo.getCollectPayApproach())){
                    vo.setCustomerPickUp("1");//指令支付
                }
                vo.setPayType(dto.getPayType());
                vo.setClientRelation(dto.getClientRelation());
                vo.setBankAccountAttribute(dto.getBankAccountAttribute());
                vo.setClientCertificateNo(dto.getClientCertificateNo());
                vo.setClientCertificateType(dto.getClientCertificateType());
                vo.setPaySerialNo(dto.getIdClmPaymentItem());
                voList.add(vo);
            }
        } else {
            PaymentInfoDTO param = new PaymentInfoDTO();
            param.setReportNo(reportNo);
            param.setCaseTimes(caseTimes);
            List<PaymentInfoDTO> paymentInfoList = paymentInfoService.getPaymentInfo(param);
            if(CollectionUtils.isNotEmpty(paymentInfoList)) {
                for (PaymentInfoDTO dto : paymentInfoList) {
                    PaymentVO vo = new PaymentVO();
//                    vo.setPayAmount(dto.getPaymentAmount());
//                    vo.setPolicyNo(dto.getPolicyNo());
                    vo.setClientBankAccount(dto.getClientBankAccount());
                    vo.setClientBankCode(dto.getClientBankCode());
                    vo.setClientBankName(dto.getClientBankName());
                    vo.setProvinceCode(dto.getProvinceName());
                    vo.setCityCode(dto.getCityName());
                    vo.setRegionCode(dto.getRegionCode());
                    vo.setClientName(dto.getClientName());
//                    vo.setPayTime(dto.getPayDate());
                    vo.setCollectPayApproach(dto.getCollectPayApproach());
                    vo.setCustomerPickUp("2");//默认值非指令支付
                    //判断结算方式是否时指令支付
                    if(Objects.equals(CollectPayApproachEnum.ORDER_PAYMENT.getType(), vo.getCollectPayApproach())){
                        vo.setCustomerPickUp("1");//指令支付
                    }
                    vo.setPayType(dto.getPayType());
                    vo.setClientRelation(dto.getClientRelation());
                    vo.setBankAccountAttribute(dto.getBankAccountAttribute());
                    vo.setClientCertificateNo(dto.getClientCertificateNo());
                    vo.setClientCertificateType(dto.getClientCertificateType());
                    voList.add(vo);
                }
            }
        }
        return voList;
    }

    /**
     * 保单历史理赔案件信息
     * @param requestDto
     * @return
     */
    @Override
    public List<PolicyClaimHistoryReportInfoDTO> getPolicyHistoryClaimInfo(QueryClaimReportRequestDTO requestDto) {
        checkData(requestDto);
        QueryClaimReportRequestVo requestVo = new QueryClaimReportRequestVo();
        BeanUtils.copyProperties(requestDto,requestVo);
        List<ClaimHistoryReportInfoVO> claimHistoryReportInfoVos = reportInfoMapper.getCusPolicyHistoryClaimInfo(requestVo);

        if(CollectionUtils.isEmpty(claimHistoryReportInfoVos)){
            return new ArrayList<>();
        }
        List<PolicyClaimHistoryReportInfoDTO> reportInfoDTOList = BeanUtil.copyToList(claimHistoryReportInfoVos, PolicyClaimHistoryReportInfoDTO.class);
        //产品名称查询处理，多个产品名称逗号分隔
        if(CollectionUtils.isNotEmpty(reportInfoDTOList)){
            for (PolicyClaimHistoryReportInfoDTO dto : reportInfoDTOList) {
                //查询结案时间
                WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseMapper.getCusWholeCaseInfo(dto.getReportNo());
                //已结案的查询赔付时间 和设置结案时间
                if(Objects.equals(wholeCaseBaseDTO.getWholeCaseStatus(),WholeCaseStatusEnum.WHOLE_CASE_STATUS_ZERO.getCode())){
                  String payDate =  paymentItemMapper.getPayDateByReportNo(dto.getReportNo());
                  dto.setLastTimePayDate(payDate);
                  dto.setEndCaseDate(DateUtils.dateFormat(wholeCaseBaseDTO.getEndCaseDate(),DateUtils.FULL_DATE_STR));
                  //已赔付金额
                  BigDecimal payFee =  reportInfoMapper.getSumPayFee(dto.getReportNo());
                  dto.setPayAmount(payFee);
                }
                //查询报案人姓名和报案日期
                ReportInfoEntity entity =  reportInfoMapper.getReportInfo(dto.getReportNo());
                dto.setReportDate(DateUtils.dateFormat(entity.getReportDate(),DateUtils.FULL_DATE_STR));
                dto.setReporterName(entity.getReporterName());
                // 查询产品代码，产品名称
                List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(dto.getReportNo());
                if (CollectionUtils.isNotEmpty(policyInfoDTOList)) {
                    String productCodes =
                            policyInfoDTOList.stream().map(PolicyInfoDTO::getProductCode).distinct().collect(Collectors.joining(Constants.SEPARATOR));
                    String productNames =
                            policyInfoDTOList.stream().map(PolicyInfoDTO::getProductName).distinct().collect(Collectors.joining(Constants.SEPARATOR));
                    dto.setProductCode(productCodes);
                    dto.setProductName(productNames);
                }
                // 查询投保人证件号码
                List<AhcsPolicyHolderEntity> policyHolderEntityList =
                        ahcsPolicyHolderMapper.getInfoByReportNo(dto.getReportNo());
                if (CollectionUtils.isNotEmpty(policyHolderEntityList)) {
                    String certificateNos =
                            policyHolderEntityList.stream().map(AhcsPolicyHolderEntity::getCertificateNo).distinct().collect(Collectors.joining(Constants.SEPARATOR));
                    dto.setHolderCertificateNo(certificateNos);
                }

                //查询流程状态：
                String processStatus = reportInfoMapper.getProcessStatus(dto.getReportNo());
                String caseStatusName=CaseProcessStatus.getName(processStatus);
                dto.setCaseStatus(caseStatusName);
            }
        }
        return reportInfoDTOList;
    }

    /**
     * 入参校验
     */
    private void checkData(QueryClaimReportRequestDTO requestDto) {
        if(StringUtils.isEmptyStr(requestDto.getReportNo()) && StringUtils.isEmptyStr(requestDto.getCaseNo())
                &&StringUtils.isEmptyStr(requestDto.getPolicyNo())
                && StringUtils.isEmptyStr(requestDto.getCertificateNo())){
            throw new GlobalBusinessException("报案号，赔案号，保单号，证件号不能同时为空！");
        }
        if(StringUtils.isNotEmpty(requestDto.getCertificateNo()) && StringUtils.isEmptyStr(requestDto.getCertificateType())){
            throw new GlobalBusinessException("录入证件号时证件类型必填");
        }
    }

    /**
     * 案件详情信息查询
     * @param reportNo
     * @return
     */
    @Override
    public ClaimDetailedInfoDTO getReportDetailInfo(String reportNo) {
        RapeCheckUtil.checkParamEmpty(reportNo, "报案号");
        //先通过报案号查询赔付次数，默认取赔付次数最大的（最新的）一笔数据
        Integer caseTimes = wholeCaseBaseMapper.getCaseTimes(reportNo);
        //1客户基本信息查询
        ClaimDetailedInfoDTO resultDto = new ClaimDetailedInfoDTO();
        long cusTimesStart = System.currentTimeMillis();
        CustomerBaseInfoDTO cusDto = getCustomerInfo(reportNo);

        log.info("客户基本信息查询耗时={}毫秒",System.currentTimeMillis()-cusTimesStart);
        resultDto.setCustomerBaseInfo(cusDto);
        //2:案件信息查询
        long caseTimesStart = System.currentTimeMillis();
        CaseInfoDTO caseInfo = getReportBaseInfo(reportNo,caseTimes);

        log.info("案件信息查询耗时={}毫秒",System.currentTimeMillis()-caseTimesStart);
        resultDto.setCaseInfo(caseInfo);
        //3整案信息查询
        long wholeTimesStart = System.currentTimeMillis();
        WholeCaseInfoDTO wholeCaseInfoDTO = getWholeCaseInfo(reportNo,caseTimes);
        if(Objects.equals("1",wholeCaseInfoDTO.getCaseClass())){
            //置空原因：客服新建报案还未录入报案跟踪 这个是空值，此处调用的查询逻辑是复用之前的逻辑，
            //之前的逻辑会先给个默认值“1”，然后在查询结果，根据查询结果做转换
            //因为客服新建报案还未报案跟踪没有这个值，但此处会返回一个默认值1，所以置空
            wholeCaseInfoDTO.setCaseClass(""); //
        }

        log.info("整案信息查询耗时={}毫秒",System.currentTimeMillis()-wholeTimesStart);
        resultDto.setWholeCaseInfo(wholeCaseInfoDTO);
        //4事故医院信息
        long hostptilTimesStart = System.currentTimeMillis();
        AccidentInfoDTO accidentInfoDTO = getHospitalInfo(reportNo,caseTimes);
        log.info("事故医院信息查询耗时={}毫秒",System.currentTimeMillis()-hostptilTimesStart);
        resultDto.setAccidentInfo(accidentInfoDTO);
        //5理算信息查询
        long settleTimesStart = System.currentTimeMillis();
        List<SettleInfoDTO>  settleInfoList = getSettleInfo(reportNo,caseTimes);
        log.info("理算信息查询耗时={}毫秒",System.currentTimeMillis()-settleTimesStart);
        resultDto.setSettleInfoList(settleInfoList);
        //查询单证信息列表
        long doucTimesStart = System.currentTimeMillis();
        List<DocumnetInfoDTO> documnetInfoList = getDocumentInfoList(reportNo,caseTimes);
        log.info("查询单证信息列表耗时={}毫秒",System.currentTimeMillis()-doucTimesStart);
        resultDto.setDocumnetInfoList(documnetInfoList);
        //查询补材信息列表
        long supplementsMaterialTimeStart = System.currentTimeMillis();
        List<SupplementsMaterialDTO> customerSupplements = documentTypeService.getCustomerSupplements(reportNo, caseTimes);
        resultDto.setCustomerSupplements(customerSupplements);
        log.info("补材信息查询耗时={}毫秒",System.currentTimeMillis()-supplementsMaterialTimeStart);

        //领款信息查询
        long paymentTimesStart = System.currentTimeMillis();
        List<PaymentVO> paymentList = getPaymentListTOCustServ(reportNo, caseTimes);
        resultDto.setPaymentList(paymentList);
        log.info("领款信息查询耗时={}毫秒",System.currentTimeMillis()-paymentTimesStart);

        //医疗单据信息查询
        long medicalBillTimeStart = System.currentTimeMillis();
        List<MedicalBillInfoVO> medicalBillInfoVOS = medicalBillService.getMedicalBillInfoForPrint(reportNo, caseTimes);
        for(MedicalBillInfoVO medicalBillInfoVO : medicalBillInfoVOS) {
            //增加诊断信息，从clms_diagnose_hospital_bill_association表获取 可能多条
            List<DiagnoseHospitalBillAssociationDTO> list =
                    diagnoseHospitalBillAssociationMapper.getDiagnoseHospitalBillAssociation(medicalBillInfoVO.getIdAhcsBillInfo());
            String diagnoseName= list.stream().map(DiagnoseHospitalBillAssociationDTO::getDiagnoseName).distinct().collect(Collectors.joining(","));
            medicalBillInfoVO.setDiagnoseName(diagnoseName);
        }
        resultDto.setMedicalBillInfoList(medicalBillInfoVOS);
        log.info("医疗单据信息查询耗时={}毫秒",System.currentTimeMillis()-medicalBillTimeStart);
        return resultDto;
    }

    /**
     * 查询单证信息列表
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private List<DocumnetInfoDTO> getDocumentInfoList(String reportNo, Integer caseTimes) {
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setReportNo(reportNo);
        fileInfoDTO.setCaseTimes(caseTimes);
        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
        log.info("客服查询案件详情-单证信息报案号={},赔付次数={},文件类型={}", reportNo,caseTimes,fileInfoDTO.getFileType());
        List<FileInfoDTO> fileInfoDTOList = documentTypeMapper.getDocumentListInfo(fileInfoDTO);
        log.info("客服查询案件详情-单证信息查询结果={},长度大小={}",JsonUtils.toJsonString(fileInfoDTOList),fileInfoDTOList.size());
        if(CollectionUtils.isEmpty(fileInfoDTOList)){
            return  null;
        }
        List<DocumnetInfoDTO> documnetInfoDTOList =new ArrayList<>();
        FileInfoDTO fileInfoDTOParams = new FileInfoDTO();
        fileInfoDTOParams.setReportNo(reportNo);
        for (FileInfoDTO file : fileInfoDTOList) {
            fileInfoDTOParams.setFileId(file.getFileId());
            FileInfoDTO fileDto = claimCommonQueryFileInfoService.getFileInfo(fileInfoDTOParams);
            log.info("客服查询案件详情-单证信息结果={}", JsonUtils.toJsonString(fileDto));
            DocumnetInfoDTO documnetInfoDTO = new DocumnetInfoDTO();
            documnetInfoDTO.setFileFormat(file.getFileFormat());
            documnetInfoDTO.setFileName(fileDto.getFileName());
            documnetInfoDTO.setFileId(file.getFileId());
            documnetInfoDTO.setFileUrl(fileDto.getFileUrl());
            documnetInfoDTO.setUploadDate(file.getUploadDate());
            documnetInfoDTOList.add(documnetInfoDTO);
        }

        return documnetInfoDTOList;
    }

    /**
     * 查询理算信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private List<SettleInfoDTO> getSettleInfo(String reportNo, Integer caseTimes) {
        List<SettleInfoDTO> settleInfoDTOList=null;
        List<PolicyPayDTO> policyList = policyPayMapper.selectByReportNo(reportNo, caseTimes);
        if(CollectionUtils.isEmpty(policyList)){
           return  settleInfoDTOList;
        }
        List<PreDutyVO> dutyPreList = prePayMapper.getDutyPrepaySum(reportNo,caseTimes);
        Map<String, PreDutyVO> dutyPrePayMap = new HashMap<>();
        if (ListUtils.isNotEmpty(dutyPreList)) {
            dutyPrePayMap = dutyPreList.stream().collect(Collectors.toMap(k ->k.getPolicyNo()+k.getPlanCode()+k.getDutyCode(),dto->dto));
        }
        for (PolicyPayDTO policy : policyList) {
            List<PlanPayDTO>  planPayDTOList = planPayService.getByPolicy(policy);
            if(CollectionUtils.isEmpty(planPayDTOList)){
                continue;
            }
            if(Objects.isNull(settleInfoDTOList)){
                settleInfoDTOList=new ArrayList<>();
            }
            for (PlanPayDTO dto : planPayDTOList) {
                List<DutyPayDTO> dutyList = dto.getDutyPayArr();
                if(CollectionUtil.isNotEmpty(dutyList)){
                    for (DutyPayDTO duty : dutyList) {
                        SettleInfoDTO settleInfoDTO = new SettleInfoDTO();
                        //保单归属机构
                        settleInfoDTO.setComCode(policy.getDepartmentChineseName());
                        //保单号
                        settleInfoDTO.setPolicyNo(dto.getPolicyNo());
                        //险种名称
                        settleInfoDTO.setPlanName(dto.getPlanName());
                        settleInfoDTO.setPlanCode(dto.getPlanCode());
                        //险种编码
                        settleInfoDTO.setDutyName(duty.getDutyName());
                        settleInfoDTO.setDutyCode(duty.getDutyCode());
                        //基本保额
                        settleInfoDTO.setBaseAmountPay(duty.getBaseAmountPay());
                        //理算金额
                        settleInfoDTO.setSettleAmount(duty.getSettleAmount());
                        //理算依据
                        settleInfoDTO.setSettleReason(duty.getSettleReason());
                        //预赔金额
                        settleInfoDTO.setPrePayAmount(BigDecimal.ZERO);
                        if (MapUtil.isNotEmpty(dutyPrePayMap)) {
                            PreDutyVO preDutyVO = dutyPrePayMap.getOrDefault(dto.getPolicyNo() + dto.getPlanCode() + duty.getDutyCode(), new PreDutyVO());
                            if (preDutyVO.getDutyPreAmount() != null) {
                                settleInfoDTO.setPrePayAmount(preDutyVO.getDutyPreAmount());
//                            }
                            }
                        }
                        settleInfoDTOList.add(settleInfoDTO);
                    }
                }
            }

        }



        return settleInfoDTOList;
    }

    /**
     * 事故医院信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private AccidentInfoDTO getHospitalInfo(String reportNo, Integer caseTimes) {
        String taskId = caseClassMapper.getCurrentTaskCodeFromCaseClass(reportNo, caseTimes,"");
        String idAhcsChannelProcess = channelProcessService.getChannelProcessId(reportNo, caseTimes);
        AccidentInfoDTO accidentInfoDTO = null;
        log.info("客服查询案件详情-事故医院信息taskId={},通道任务={}",taskId,idAhcsChannelProcess);
        String status ="";
        if(BpmConstants.MANUAL_SETTLE.equals(taskId) || BpmConstants.CHECK_DUTY.equals(taskId)){
            status = ChecklossConst.STATUS_TMP_SUBMIT;
        }else{
            return null;
        }
        List<PersonHospitalDTO>  personHospitalDTOs = personHospitalService.getPersonHospitals(idAhcsChannelProcess, taskId,status);
        log.info("客服查询案件详情-事故医院信息查询结果={}",JsonUtils.toJsonString(personHospitalDTOs));
        if(CollectionUtils.isNotEmpty(personHospitalDTOs) && Objects.nonNull(personHospitalDTOs.get(0))){
            accidentInfoDTO=new AccidentInfoDTO();
            log.info("客服查询案件详情-事故医院信息查询结果shuju={}",JsonUtils.toJsonString(personHospitalDTOs.get(0)));
            accidentInfoDTO.setHospitalName(personHospitalDTOs.get(0).getHospitalName());
            accidentInfoDTO.setHospitalgrade(personHospitalDTOs.get(0).getHospitalGrade());
            accidentInfoDTO.setHospitalProperty(personHospitalDTOs.get(0).getHospitalPropertyDes());
            accidentInfoDTO.setTreadType(personHospitalDTOs.get(0).getTherapyType());
            accidentInfoDTO.setIsCooperation(personHospitalDTOs.get(0).getIsCooperation());
            accidentInfoDTO.setTreatStatus(personHospitalDTOs.get(0).getMedicalStatus());
        }

        return accidentInfoDTO;

    }

    /**
     * 整案信息查询
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private WholeCaseInfoDTO getWholeCaseInfo(String reportNo, Integer caseTimes) {
        WholeCaseBaseDTO wholeCaseBase = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
        WholeCaseInfoDTO caseInfoDTO = new WholeCaseInfoDTO();
        caseInfoDTO.setCaseClass(wholeCaseBase.getCaseClass());
        if(ObjectUtil.isNotNull(wholeCaseBase.getRegisterDate())){
            caseInfoDTO.setRegisterDate(DateUtils.dateFormat(wholeCaseBase.getRegisterDate(),DateUtils.FULL_DATE_STR));
        }
        if(ObjectUtil.isNotNull(wholeCaseBase.getDocumentFullDate())){
            caseInfoDTO.setDocumentFullDate(DateUtils.dateFormat(wholeCaseBase.getDocumentFullDate(),DateUtils.FULL_DATE_STR));
        }
        caseInfoDTO.setDepartmentName(wholeCaseBase.getDepartmentName());
        if(ObjectUtil.isNotNull(wholeCaseBase.getEndCaseDate())){
            caseInfoDTO.setEndCaseDate(DateUtils.dateFormat(wholeCaseBase.getEndCaseDate(),DateUtils.FULL_DATE_STR));
        }
        caseInfoDTO.setVerifyConclusion(wholeCaseBase.getVerifyConclusion());
        return caseInfoDTO;
    }

    /**
     * 查询案件基本信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private CaseInfoDTO getReportBaseInfo(String reportNo, Integer caseTimes) {
        ReportBaseInfoResData resData= wholeCaseService.getReportBaseInfo(reportNo,caseTimes);
        CaseInfoDTO caseInfoDTO = new CaseInfoDTO();
        if(Objects.isNull(resData)){
            return  caseInfoDTO;
        }
        if(ObjectUtil.isNotNull(resData.getReportDate())){
            caseInfoDTO.setReportDate(DateUtils.dateFormat(resData.getReportDate(),DateUtils.FULL_DATE_STR));
        }

        caseInfoDTO.setReporterName(resData.getReporterName());
        caseInfoDTO.setReporterType(resData.getRelationWithReport());
        caseInfoDTO.setReportWay(resData.getReportMode());
        caseInfoDTO.setIsRepeatReport(resData.getIsRepeatReport());
        if(CollectionUtils.isNotEmpty(resData.getRepeatReportNoList())){
            String message ="该客户在临近事故日期/临近报案日期内已有报案记录，报案号为"+ com.paic.ncbs.claim.common.util.StringUtils.getMergeString(resData.getRepeatReportNoList(),Constants.SEPARATOR)+"请注意核实是否为重复报案！";
            caseInfoDTO.setRepeatReportInfo(message);
        }
        caseInfoDTO.setReportPhone(resData.getReporterCallNo());
        caseInfoDTO.setIsSpecialReport(resData.getHugeAccident());
        if(ObjectUtil.isNotNull(resData.getAccidentDate())){
            caseInfoDTO.setAccidentDate(DateUtils.dateFormat(resData.getAccidentDate(),DateUtils.FULL_DATE_STR));
        }

        caseInfoDTO.setAccidentPlace(resData.getAccidentPlace());
        caseInfoDTO.setAccidentReason(resData.getAccidentCauseLevel1()+"-"+resData.getAccidentCauseLevel2());
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
        if (Objects.nonNull(reportAccident)) {
            caseInfoDTO.setAccidentDetail(reportAccident.getAccidentDetail());
        }
        caseInfoDTO.setAssigner(taskInfoMapper.getAssigner(reportNo,caseTimes));
        return caseInfoDTO;
    }

    /**
     * 查询客户信息
     * @param reportNo
     */
    private CustomerBaseInfoDTO getCustomerInfo(String reportNo) {
        ReportCustomerInfoEntity customerInfo = customerService.getReportCustomerInfoByReportNo(reportNo);
        CustomerBaseInfoDTO customerBaseInfoDTO = new CustomerBaseInfoDTO();
        if(Objects.isNull(customerInfo)){
            return customerBaseInfoDTO;
        }
        customerBaseInfoDTO.setInsuredName(customerInfo.getName());
        if(ObjectUtil.isNotNull(customerInfo.getBirthday())){
            //customerBaseInfoDTO.setBirthDay(DateUtils.dateFormat(customerInfo.getBirthday(),DateUtils.FULL_DATE_STR));
            int age = DateUtil.age(customerInfo.getBirthday(),new Date());
            customerBaseInfoDTO.setBirthDay(Integer.toString(age));
        }

        customerBaseInfoDTO.setCertifcateNo(customerInfo.getCertificateNo());
        customerBaseInfoDTO.setCertificateType(customerInfo.getCertificateType());
        customerBaseInfoDTO.setCustomerType(customerInfo.getClientType());
        customerBaseInfoDTO.setCustomerClass(customerInfo.getClientCategoty());
        customerBaseInfoDTO.setRemark(customerInfo.getRemark());
        List<AhcsInsuredPresonEntity> persion = ahcsInsuredPresonMapper.getAhcsInsuredPersionByClientNo(customerInfo.getClientNo());
        //  是否社保: 2为社保， 0或空为非社保
        if (CollectionUtil.isEmpty(persion)) {
            customerBaseInfoDTO.setIsSociaSecurity(null);
        } else {
            customerBaseInfoDTO.setIsSociaSecurity(persion.get(0).getIsSociaSecurity());
        }

        customerBaseInfoDTO.setReportNo(reportNo);
        //理赔核心这个层级没有保单号，因客户系统需要，微信群沟通确认，有多个保单用都好分割
        //根据报案号，当前被保险人客户号查询保单号
        List<String> policyNoList = policyClaimCaseMapper.getInsuredPolicyNo(reportNo,customerInfo.getClientNo());
        log.info("客服系统查询案件详情policyNoList={}", JsonUtils.toJsonString(policyNoList));
        String policynos = com.paic.ncbs.claim.common.util.StringUtils.getMergeString(policyNoList,Constants.SEPARATOR);
        log.info("客服系统查询案件详情保单好多个逗号分割={}", policynos);
        customerBaseInfoDTO.setPolicyNo(policynos);

        return  customerBaseInfoDTO;
    }

    @Override
    public ReportQueryResVO queryCaseInfoList(ReportQueryReqDTO req) {
        // 组装返回数据
        List<ReportQueryInfoDTO> reportQueryList = new ArrayList<>();
        // List<String> typeList = StringUtils.getListWithSeparator(req.getType(), Constants.SEPARATOR);
        List<String> typeList = req.getTypes();
        if (CollectionUtils.isEmpty(typeList) || typeList.contains("1")) {
            // 查询未决数据
            List<ReportQueryInfoDTO> unSettledList = reportInfoMapper.queryReportInfoUnsettled(req);
            if (CollectionUtils.isNotEmpty(unSettledList)) {
                reportQueryList.addAll(unSettledList);
            }
        }

        if (CollectionUtils.isEmpty(typeList) || typeList.contains("2")) {
            // 查询已结案（有赔付）
            List<ReportQueryInfoDTO> settledList = reportInfoMapper.queryReportInfoSettled(req);
            if (CollectionUtils.isNotEmpty(settledList)) {
                Set<String> matchKeys =
                        reportQueryList.stream().map(ReportQueryInfoDTO::getKey).collect(Collectors.toSet());
                settledList.forEach(item -> {
                    if (!matchKeys.contains(item.getKey())) {
                        reportQueryList.add(item);
                        matchKeys.add(item.getKey());
                    }
                });
            }
        }

        if (CollectionUtils.isEmpty(typeList) || typeList.contains("3")) {
            // 零结 拒赔 零赔付
            List<ReportQueryInfoDTO> settledList = reportInfoMapper.queryReportInfoZeroCannel(req);
            if (CollectionUtils.isNotEmpty(settledList)) {
                Set<String> matchKeys =
                        reportQueryList.stream().map(ReportQueryInfoDTO::getKey).collect(Collectors.toSet());
                settledList.forEach(item -> {
                    if (!matchKeys.contains(item.getKey())) {
                        reportQueryList.add(item);
                        matchKeys.add(item.getKey());
                    }
                });
            }
        }

        List<ReportQueryInfoResVO> reportInfoList =
                reportQueryList.stream().map(item -> ReportQueryResConvert.INSTANCE.to(item)).collect(Collectors.toList());
        ReportQueryResVO result = new ReportQueryResVO();
        result.setStatus(CollectionUtils.isNotEmpty(reportInfoList) ? "1" : "0");
        result.setReportInfoList(reportInfoList);
        return result;
    }

    @Override
    public ReportQueryResVO queryReport(ReportQueryReqDTO req) {
        //接口增加案件本次抵扣免赔额
        ReportQueryResVO result = new ReportQueryResVO();
        //查询未决数据
        List<ReportQueryInfoDTO> unSettledList = reportInfoMapper.listReportQueryInfoUnsettled(req);
        //查询已决数据
        List<ReportQueryInfoDTO> settledList = reportInfoMapper.listReportQueryInfoSettled(req);
        //组装返回数据
        List<ReportQueryInfoDTO> reportQueryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unSettledList)) {
            reportQueryList.addAll(unSettledList);
        }
        if (CollectionUtils.isNotEmpty(settledList)) {
            Set<String> matchKeys = reportQueryList.stream().map(ReportQueryInfoDTO::getKey).collect(Collectors.toSet());
            settledList.forEach(item -> {
                if (!matchKeys.contains(item.getKey())) {
                    //增加免赔额和案件赔付金额
                    EndCaseInfo endCaseInfo = reportInfoMapper.getPayAmountAndRemitAmount(item.getReportNo());
                    item.setSettleAmount(endCaseInfo.getSettleAmount());
                    item.setRemitAmount(endCaseInfo.getRemitAmount());





                    reportQueryList.add(item);
                    matchKeys.add(item.getKey());
                }
            });
        }
        List<ReportQueryInfoResVO> reportInfoList = reportQueryList.stream().map(item -> {
            ReportQueryInfoResVO vo = ReportQueryResConvert.INSTANCE.to(item);
            vo.setSettleAmount(item.getSettleAmount());
            vo.setRemitAmount(item.getRemitAmount());

            return vo;
        }).collect(Collectors.toList());        result.setStatus(org.apache.commons.collections.CollectionUtils.isNotEmpty(reportInfoList) ? "1" : "0");
        result.setReportInfoList(reportInfoList);
        return result;
    }

    @Override
    public ReportQueryResVO queryReport4Severe(ReportQueryReqDTO req) {
        req.setPlanCodes(null);
        ReportQueryResVO result = new ReportQueryResVO();
        //查询已决数据
        List<ReportQueryInfoDTO> settledList = reportInfoMapper.listReportQueryInfoSettled(req);
        //组装返回数据
        List<ReportQueryInfoDTO> reportQueryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settledList)) {
            Set<String> matchKeys = reportQueryList.stream().map(ReportQueryInfoDTO::getKey).collect(Collectors.toSet());
            settledList.forEach(item -> {
                if (!matchKeys.contains(item.getKey())) {
                    reportQueryList.add(item);
                    matchKeys.add(item.getKey());
                }
            });
        }
        List<ReportQueryInfoResVO> reportInfoList = reportQueryList.stream().map(item -> ReportQueryResConvert.INSTANCE.to(item)).collect(Collectors.toList());
        result.setStatus(org.apache.commons.collections.CollectionUtils.isNotEmpty(reportInfoList) ? "1" : "0");
        result.setReportInfoList(reportInfoList);
        return result;
    }

    @Override
    public ClaimStatusResponseVo queryClaimStatusTPA(TpaClaimStatusRequestDto tpaClaimStatusRequestDto) {
        ClaimStatusRequestVo claimStatusRequestVo = tpaClaimStatusRequestDto.getRequestData();
        Integer caseTimes = claimStatusRequestVo.getCaseTimes();
        String registNo = claimStatusRequestVo.getRegistNo();

        if(StringUtils.isEmptyStr(registNo)){
            throw new GlobalBusinessException("报案号不能为空");
        }
        if(StringUtils.isEmptyStr(caseTimes)){
            throw new GlobalBusinessException("赔付次数不能为空");
        }
        ClaimStatusResponseVo claimStatusResponseVo = new ClaimStatusResponseVo();
        claimStatusResponseVo.setRegistNo(registNo);
        String processStatusCode = caseProcessService.getCaseProcessStatus(registNo, caseTimes);
        String processStatusName = CaseProcessStatus.getName(processStatusCode);
        //案件节点
        claimStatusResponseVo.setClaimStatusCode(processStatusCode);
        claimStatusResponseVo.setClaimStatusName(processStatusName);
        //核赔审批信息 取最新一条
        List<VerifyDTO> verifyList = verifyDao.getVerifyList(registNo,caseTimes);
        //已结案 审核状态 01通过  99案件处理中 还未审核 02案件注销/不通过
        if(CaseProcessStatus.CASE_CLOSED.getCode().equals(processStatusCode)){
            //需考虑零结
            BigDecimal payFee =  policyPayService.getSumPayFee(registNo, caseTimes);
            claimStatusResponseVo.setUwResult("01");
            if(BigDecimal.ZERO.compareTo(payFee) == 0) {
                CaseZeroCancelDTO caseZeroCancelDTO = caseZeroCancelService.getLastZeroCancelInfo(registNo, caseTimes);
                if(Objects.nonNull(caseZeroCancelDTO)) {
                    claimStatusResponseVo.setUwDate(caseZeroCancelDTO.getVerifyDate());
                    claimStatusResponseVo.setReason(caseZeroCancelDTO.getVerifyRemark());
                }
            } else if(org.apache.commons.collections.CollectionUtils.isNotEmpty(verifyList)){
                //取最新一条
                VerifyDTO verifyDTO = verifyList.get(0);
                claimStatusResponseVo.setUwDate(verifyDTO.getVerifyDate());
                claimStatusResponseVo.setReason(verifyDTO.getVerifyOpinion());
            }
            claimStatusResponseVo.setSumPayAmt(payFee.toString());
        }else if(CaseProcessStatus.CASE_CANCELLED.getCode().equals(processStatusCode)){
            //案件已注销
            claimStatusResponseVo.setUwResult("02");
            claimStatusResponseVo.setReason("案件已注销");
        }else if(CaseProcessStatus.PENDING_SETTLE.getCode().equals(processStatusCode) && CollectionUtils.isNotEmpty(verifyList)){
            claimStatusResponseVo.setUwResult("02");
            VerifyDTO verifyDTO = verifyList.get(0);
            claimStatusResponseVo.setUwDate(verifyDTO.getVerifyDate());
            claimStatusResponseVo.setReason(verifyDTO.getVerifyOpinion());

        }else {
            //待审核
            claimStatusResponseVo.setUwResult("99");
        }
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseMapper.getWholeCaseBase2(registNo,caseTimes);
        claimStatusResponseVo.setRegisterDate(wholeCaseBaseDTO.getRegisterDate());
        claimStatusResponseVo.setEndCaseDate(wholeCaseBaseDTO.getEndCaseDate());
        claimStatusResponseVo.setIndemnityConclusion(wholeCaseBaseDTO.getIndemnityConclusion());

        return claimStatusResponseVo;
    }

    @Override
    public ReportQueryResVO getQueryCusReportInfo(ReportQueryReqDTO req) {
        checkInputData(req);
        ReportQueryResVO vo=new ReportQueryResVO();
        //查询报案记录
        List<CustomerReprotInfoDTO> csReportInfoList =  reportCustomerInfoMapper.getHistoryReportInfo(req);
        if(CollectionUtils.isNotEmpty(csReportInfoList)){
            for (CustomerReprotInfoDTO dto : csReportInfoList) {
                dto.setCaseStatusName(WholeCaseStatusEnum.getName(dto.getCaseStatus()));
                dto.setIndemnityConclusionName(IndemnityConclusionEnum.getName(dto.getIndemnityConclusion()));
            }
        }
        vo.setCusReportInfoList(csReportInfoList);

        if(CollectionUtil.isNotEmpty(req.getPlanCodes())){
            if(Objects.isNull(req.getProductCode())){
                throw new GlobalBusinessException("产品编码必填");
            }
            List<String> lists= reportCustomerInfoMapper.getCustomerHistoryPlanInfo(req);
            vo.setCusPlanReprotList(lists);
        }


        return vo;
    }

    @Override
    public Object getHistoryCaseList(ReportQueryReqDTO queryVO) {
        //根据客户信息查询案件
        WholeCaseVO wholeCase =new WholeCaseVO();
        wholeCase.setCertificateNo(queryVO.getCertificateNo());
        wholeCase.setCertificateType(queryVO.getCertificateType());
        wholeCase.setName(queryVO.getName());
        wholeCase.setClientNo(queryVO.getClientNo());
        wholeCase.setPolicyNo(queryVO.getPolicyNo());
        wholeCase.setReportNo(queryVO.getReportNo());
        List<HistoryCaseDTO> reportInfos = reportInfoService.getHistoryCaseNew(wholeCase);
        //查询案件对应的信息
        HistoryClaimInfoVO historyClaimInfoVO = new HistoryClaimInfoVO();
        List<OpenReportInfoVO> historyClaimInfoList = new ArrayList<>();
        reportInfos.forEach(item -> {
            OpenReportInfoVO reportInfo = this.getReportInfo(item.getReportNo(), Integer.valueOf(item.getCaseTimes()));
            if(reportInfo!=null && ListUtils.isNotEmpty(reportInfo.getPolicyPayList())) {
                reportInfo.getPolicyPayList().forEach(policyPay -> {
                    if(null!= policyPay && ListUtils.isNotEmpty(policyPay.getPlanPayArr())) {
                        policyPay.getPlanPayArr().forEach(planPay -> {
                            if(null != planPay && ListUtils.isNotEmpty(planPay.getDutyPayArr())) {
                                planPay.getDutyPayArr().forEach(dutyPay -> {
                                    if(null != dutyPay && ListUtils.isNotEmpty(dutyPay.getDutyDetailPayArr())) {
                                        dutyPay.getDutyDetailPayArr().forEach(dutyDetailPay -> {
                                            if(null!=dutyDetailPay && null !=dutyDetailPay.getAutoSettleAmount() && null== dutyDetailPay.getSettleAmount() && dutyDetailPay.getAutoSettleAmount().compareTo(BigDecimal.ZERO)>=0) {
                                                dutyDetailPay.setSettleAmount(dutyDetailPay.getAutoSettleAmount());
                                            }
                                        });
                                    }
                                } );
                            }
                        });
                    }

                });
            }

            historyClaimInfoList.add(reportInfo);
        });
        historyClaimInfoVO.setHistoryClaimInfoList(historyClaimInfoList);
        return historyClaimInfoVO;
    }

    /**
     * 参数校验
     * @param req
     */
    private void checkInputData(ReportQueryReqDTO req) {
        if(StringUtils.isEmptyStr(req.getCertificateNo())){
            throw new GlobalBusinessException("证件号必填");
        }
        if(StringUtils.isEmptyStr(req.getCertificateType())){
            throw new GlobalBusinessException("录入证件号时证件类型必填");
        }
        if(StringUtils.isEmptyStr(req.getName())){
            throw new GlobalBusinessException("姓名必填");
        }
    }

    /**
     * 给客服系统查询支付信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    public List<PaymentVO> getPaymentListTOCustServ(String reportNo, Integer caseTimes) {
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        //paymentItemDTO.setCaseTimes(caseTimes);
        List<String> paymentItemStatusList = new ArrayList<>();
        paymentItemStatusList.add(Constants.PAYMENT_ITEM_STATUS_11);
        paymentItemStatusList.add(Constants.PAYMENT_ITEM_STATUS_70);
        paymentItemStatusList.add(Constants.PAYMENT_ITEM_STATUS_71);
        paymentItemStatusList.add(Constants.PAYMENT_ITEM_STATUS_80);
        paymentItemDTO.setPaymentItemStatusList(paymentItemStatusList);
        List<PaymentItemDTO> paymentItemList = paymentItemMapper.getPaymentItemList(paymentItemDTO);
        List<PaymentVO> voList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(paymentItemList)) {
            for (PaymentItemDTO dto : paymentItemList) {
                // 剔除掉费用的
                if (!(PaymentTypeEnum.PAY.getType().equals(dto.getPaymentType()) || PaymentTypeEnum.PRE_PAY.getType().equals(dto.getPaymentType()))) {
                    continue;
                }
                PaymentVO vo = new PaymentVO();
                vo.setPolicyNo(dto.getPolicyNo());
                vo.setClientBankAccount(dto.getClientBankAccount());
                vo.setClientBankCode(dto.getClientBankCode());
                vo.setClientBankName(dto.getClientBankName());
                vo.setProvinceCode(dto.getProvinceName());
                vo.setCityCode(dto.getCityName());
                vo.setRegionCode(dto.getRegionCode());
                vo.setClientName(dto.getClientName());
                vo.setPayTime(dto.getPayDate());
                vo.setCollectPayApproach(dto.getCollectPayApproach());
                vo.setCustomerPickUp("2");//默认值非指令支付
                //判断结算方式是否时指令支付
                if(Objects.equals(CollectPayApproachEnum.ORDER_PAYMENT.getType(), vo.getCollectPayApproach())){
                    vo.setCustomerPickUp("1");//指令支付
                }
                vo.setPayType(dto.getPayType());
                vo.setClientRelation(dto.getClientRelation());
                vo.setBankAccountAttribute(dto.getBankAccountAttribute());
                vo.setClientCertificateNo(dto.getClientCertificateNo());
                vo.setClientCertificateType(dto.getClientCertificateType());
                vo.setPaymentItemStatus(dto.getPaymentItemStatus());
                vo.setFinancePaymentAmount(dto.getFinancePaymentAmount());
                if (Constants.PAYMENT_ITEM_STATUS_70.equals(dto.getPaymentItemStatus()) || Constants.PAYMENT_ITEM_STATUS_71.equals(dto.getPaymentItemStatus())) {
                    vo.setPaymentFailReason(dto.getExtendInfo());
                }
                if (Constants.PAYMENT_ITEM_STATUS_80.equals(dto.getPaymentItemStatus())) {
                    String cosUrl = payInfoNoticeThirdPartyCoreSAO.queryPaymenVoucherUrl(dto);
                    vo.setPaymentVoucherUrl(cosUrl);
                }
                voList.add(vo);
            }
        } else {
            PaymentInfoDTO param = new PaymentInfoDTO();
            param.setReportNo(reportNo);
            param.setCaseTimes(caseTimes);
            param.setPaymentUsage("P1");
            List<PaymentInfoDTO> paymentInfoList = paymentInfoService.getPaymentInfo(param);
            if(CollectionUtils.isNotEmpty(paymentInfoList)) {
                for (PaymentInfoDTO dto : paymentInfoList) {
                    PaymentVO vo = new PaymentVO();
                    vo.setClientBankAccount(dto.getClientBankAccount());
                    vo.setClientBankCode(dto.getClientBankCode());
                    vo.setClientBankName(dto.getClientBankName());
                    vo.setProvinceCode(dto.getProvinceName());
                    vo.setCityCode(dto.getCityName());
                    vo.setRegionCode(dto.getRegionCode());
                    vo.setClientName(dto.getClientName());
                    vo.setCollectPayApproach(dto.getCollectPayApproach());
                    vo.setCustomerPickUp("2");//默认值非指令支付
                    //判断结算方式是否时指令支付
                    if(Objects.equals(CollectPayApproachEnum.ORDER_PAYMENT.getType(), vo.getCollectPayApproach())){
                        vo.setCustomerPickUp("1");//指令支付
                    }
                    vo.setPayType(dto.getPayType());
                    vo.setClientRelation(dto.getClientRelation());
                    vo.setBankAccountAttribute(dto.getBankAccountAttribute());
                    vo.setClientCertificateNo(dto.getClientCertificateNo());
                    vo.setClientCertificateType(dto.getClientCertificateType());
                    voList.add(vo);
                }
            }
        }
        return voList;
    }
}
