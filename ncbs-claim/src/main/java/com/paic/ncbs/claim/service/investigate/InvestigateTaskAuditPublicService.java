package com.paic.ncbs.claim.service.investigate;

import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditPublicDTO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;

import java.util.List;
import java.util.Map;

public interface InvestigateTaskAuditPublicService {

    /**
     * 查询调查任务列表
     * @param investigateDepartment 调查部门
     * @return 任务列表
     */
    List<WorkBenchTaskVO> getInvestigateTaskList(List<String> investigateDepartment);

    /**
     * 完成调查任务审核
     * @param taskAuditDTO 审核信息
     */
    void finishTaskAudit(InvestigateTaskAuditPublicDTO taskAuditDTO);

    Map<String, Object> getBasicInfo(String reportNo, Integer caseTimes, String userName);

    String getPolicyElectricPdfUrl(String reportNo, String userName);
}