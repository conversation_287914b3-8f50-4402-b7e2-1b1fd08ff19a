package com.paic.ncbs.claim.dao.mapper.user;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.um.model.dto.SystemComInfoDTO;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

public interface DepartmentDefineMapper extends BaseDao<DepartmentDefineEntity> {

    DepartmentDefineEntity selectByPrimaryKey(Serializable departmentCode);

    List<DepartmentDefineEntity> getSubDepartment(String departmentCode);

    DepartmentDefineEntity getDepartmentInfo(String departmentCode);


    public DepartmentDTO queryDepartmentInfoByDeptCode(String deptCode);


    List<DepartmentVO> getSelectDepartmentList();

    public List<DepartmentDefineEntity> querySameLevelDepartmentListByDeptCode(String deptCode);

    public List<DepartmentDefineEntity> queryDepartmentListByLevel(int level);

    public DepartmentDefineEntity queryParentDepartmentInfoByDeptCode(String deptCode);


    public List<DepartmentDefineEntity> getDepartmentByDepartmentCode(String departmentCode);

    List<DepartmentDTO> queryChildDepartmentRecursion(@Param("deptCode")String deptCode);

    List<DepartmentDTO> queryParentDepartmentRecursion(@Param("deptCode")String deptCode);

    DepartmentDTO getDepartmentL2ByCode(@Param("departmentCode") String departmentCode);

    List<DepartmentDTO> getDepartmentList();

    DepartmentDTO getLevel2DeptByLevel3(@Param("departmentCode") String departmentCode);

    /**
     *
     * @Description: 根据机构编号查询所有子机构列表（包含本身）
     */
    List<DepartmentDTO> queryChildDepartments(@Param("departmentCode") String departmentCode);

    String queryDepartmentNameByDeptCode(@Param("deptCode") String deptCode);

    /**
     * 获取某个机构对应的二级机构代码
     * @param deptCode
     * @return
     */
    String getLevel2DeptCode(@Param("deptCode") String deptCode);

    List<String> getChildCodeList(@Param("deptCodeList") List<String> deptCodeList);

    List<SystemComInfoDTO> filterLevelDeptCode(@Param("deptCode") String deptCode,@Param("sysComList") List<SystemComInfoDTO> sysComList);

    DepartmentDTO getTopDepartment (@Param("deptCode") String deptCode);

}