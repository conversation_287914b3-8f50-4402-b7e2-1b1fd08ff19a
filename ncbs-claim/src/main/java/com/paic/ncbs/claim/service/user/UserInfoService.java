package com.paic.ncbs.claim.service.user;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;

import java.util.List;

public interface UserInfoService {

    List<DepartmentDTO> getLevel2DeptListExcludeUser(String userId) throws GlobalBusinessException;

    List<DepartmentDTO> getUserLevel2HeadDeptList(String userId) throws GlobalBusinessException;

    List<DepartmentDTO> getUserDepartmentList(String userId) throws GlobalBusinessException;

    String getUserNameById(String userId);

    UserInfoDTO getUserInfoDTO(String userCode);

    String getUserTopDepartment(String userId) throws GlobalBusinessException;

}
