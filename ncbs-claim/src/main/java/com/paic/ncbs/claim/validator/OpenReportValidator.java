package com.paic.ncbs.claim.validator;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.openapi.DutyPayByCustomReqDTO;
import com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class OpenReportValidator {

    public static void checkListDutyPayByCustom(DutyPayByCustomReqDTO req) {
        if (req == null) {
            throw new GlobalBusinessException("请求信息不能为空！");
        }
        if (StringUtils.isEmpty(req.getClientNo())
                && !(StringUtils.isNotEmpty(req.getName()) && StringUtils.isNotEmpty(req.getCertificateNo()) && StringUtils.isNotEmpty(req.getCertificateType()))) {
            throw new GlobalBusinessException("客户号或者人员三要素（名称、证件类型、证件号码二者必传其一！");
        }
    }

    public static void checkQueryReport(ReportQueryReqDTO req) {
        if (req == null) {
            throw new GlobalBusinessException("请求信息不能为空！");
        }
        if (StringUtils.isEmpty(req.getClientNo())
                && !(StringUtils.isNotEmpty(req.getName()) && StringUtils.isNotEmpty(req.getCertificateNo()) && StringUtils.isNotEmpty(req.getCertificateType()))
                && StringUtils.isEmpty(req.getPolicyNo())) {
            throw new GlobalBusinessException("客户号、人员三要素（名称、证件类型、证件号码）、保单号三者必传其一！");
        }
    }

    public static void checkQueryCase(ReportQueryReqDTO req) {
        if (req == null) {
            throw new GlobalBusinessException("请求信息不能为空！");
        }
        if (CollectionUtils.isEmpty(req.getCertificateNos())
                && StringUtils.isEmpty(req.getPolicyNo())) {
            throw new GlobalBusinessException("人员证件号码、保单号必传其一！");
        }
        if (CollectionUtils.isNotEmpty(req.getCertificateNos()) && req.getCertificateNos().size() > 2000) {
            throw new GlobalBusinessException("同时查询证件号码太多！");
        }
    }

}
