package com.paic.ncbs.claim.service.secondunderwriting.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.UwConclusionTypeEnum;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.clms.ClmsPolicyHistoryUwInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsPolicyHistoryUwInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.secondunderwriting.ClmsPolicyHistoryUwInfoDTO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimESPolicyInfoVO;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicyHistoryUwInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 理赔保单历史保结信息(ClmsPolicyHistoryUwInfoEntity)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-28 15:28:08
 */
@Slf4j
@Service("clmsPolicyHistoryUwInfoService")
public class ClmsPolicyHistoryUwInfoServiceImpl implements ClmsPolicyHistoryUwInfoService {
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private ClmsPolicyHistoryUwInfoMapper clmsPolicyHistoryUwInfoMapper;
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;
    @Autowired
    private GlobalPolicyService globalPolicyService;


    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsPolicyHistoryUwInfoEntity queryById(String id) {
        return this.clmsPolicyHistoryUwInfoMapper.queryById(id);
    }

    /**
     * 新增数据
     *
     * @param clmsPolicyHistoryUwInfoEntity 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsPolicyHistoryUwInfoEntity insert(ClmsPolicyHistoryUwInfoEntity clmsPolicyHistoryUwInfoEntity) {
        this.clmsPolicyHistoryUwInfoMapper.insert(clmsPolicyHistoryUwInfoEntity);
        return clmsPolicyHistoryUwInfoEntity;
    }

    @Override
    public void saveBatch(List<ClmsPolicyHistoryUwInfoDTO> list,String reportNo) {
        if(CollectionUtil.isEmpty(list)){
            throw new GlobalBusinessException("抄单保存核保信息批量保存参数不能为空！");
        }

        List<ClmsPolicyHistoryUwInfoEntity> entityList = new ArrayList<>();
        for (ClmsPolicyHistoryUwInfoDTO dto : list) {
            dto.setReportNo(reportNo);
            checkSetInputParams(dto,entityList);
        }
        log.info("抄单核保信息保存={}",JsonUtils.toJsonString(entityList));
        if(entityList.size()>=1){
            clmsPolicyHistoryUwInfoMapper.insertBatch(entityList);
        }

    }

    /**
     * 修改数据
     *
     * @param clmsPolicyHistoryUwInfoEntity 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsPolicyHistoryUwInfoEntity update(ClmsPolicyHistoryUwInfoEntity clmsPolicyHistoryUwInfoEntity) {
        this.clmsPolicyHistoryUwInfoMapper.update(clmsPolicyHistoryUwInfoEntity);
        return this.queryById(clmsPolicyHistoryUwInfoEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsPolicyHistoryUwInfoMapper.deleteById(id) > 0;
    }

    /**
     * 查询保单历史核保信息
     * @param reportNo
     * @return
     */
    @Override
    public List<ClmsPolicyHistoryUwInfoDTO> getClmsPolicyHistoryUwInfoList(String reportNo) {
        List<String> policyNoList = ahcsPolicyInfoMapper.getPolicyNoByReportNo(reportNo);
        String policyNo = "";
        if(policyNoList.isEmpty()){
            return null;
        } else {
            policyNo = policyNoList.get(0);
            Map<String, String> plyBaseInfo = new HashMap<>();
            //todo tzj 查询承保表，需适配global调整
            if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                //global
                plyBaseInfo = ahcsPolicyInfoMapper.getPlyBaseInfo(reportNo,policyNo);
            }else{
                plyBaseInfo = ocasMapper.getPlyBaseInfo(policyNo);
            }
            if(ObjectUtil.isEmpty(plyBaseInfo)){
                return null;
            }
            String productClass = plyBaseInfo.get("productClass");
            //意外险和健康险不显示核保结论
            if(!("02".equals(productClass) || "03".equals(productClass))){
                return null;
            }
        }
        //查询个人的核保结论
        List<ClmsPolicyHistoryUwInfoEntity> entityList =  clmsPolicyHistoryUwInfoMapper.getClmsPolicyHistoryUwInfoList(reportNo);
        //如果个人的核保结论为空，则查询该保单+方案下的核保结论
//        if(CollectionUtil.isEmpty(entityList)){
//            //根据报案号查询方案
//            List<ClaimESPolicyInfoVO> claimESPolicyInfoVOList = taskInfoMapper.getClaimESPolicyInfoVOList(reportNo);
//            if(!claimESPolicyInfoVOList.isEmpty()){
//                String schemeName = claimESPolicyInfoVOList.get(0).getSchemeName();
//                entityList = clmsPolicyHistoryUwInfoMapper.getPolicyNoUwConclusionInfo(policyNo,schemeName);
//            }
//        }
        if(CollectionUtil.isEmpty(entityList)){
            return null;
        }
        List<ClmsPolicyHistoryUwInfoDTO> returnDtoList = BeanUtil.copyToList(entityList,ClmsPolicyHistoryUwInfoDTO.class);
        //核保结论代码转换为汉字给前端展示
        for (ClmsPolicyHistoryUwInfoDTO dto : returnDtoList) {
            dto.setUwConclusion(UwConclusionTypeEnum.getName(dto.getUwConclusion()));
        }
        return returnDtoList;
    }
    //入参必填校验及相关值设置
    private void checkSetInputParams(ClmsPolicyHistoryUwInfoDTO dto,List<ClmsPolicyHistoryUwInfoEntity> entityList) {
//        String policyNo=dto.getPolicyNo();
//        String message= Constants.COPY_POLICY_UW_ERROR_MESS;
////        if(StrUtil.isEmpty(policyNo)){
//            throw new GlobalBusinessException("抄单数据核保信息保单号不能为空！");
//        }
//        if(StrUtil.isEmpty(dto.getBusinessType())){
//            throw new GlobalBusinessException(message+policyNo+"个团属性不能为空！");
//        }
//        if(StrUtil.isEmpty(dto.getProductName())){
//            throw new GlobalBusinessException(message+policyNo+"产品名称不能为空！");
//        }
//        if(StrUtil.isEmpty(dto.getSchemeName())){
//            throw new GlobalBusinessException(message+policyNo+"方案名称不能为空！");
//        }
//        if(StrUtil.isEmpty(dto.getPlanStatus())){
//            throw new GlobalBusinessException(message+policyNo+"险种状态不能为空！");
//        }
//        if(StrUtil.isEmpty(dto.getUwConclusion())){
//            throw new GlobalBusinessException(message+policyNo+"核保结论不能为空！");
//        }
//        if(Objects.isNull(dto.getUwCompleteDate())){
//            throw new GlobalBusinessException(message+policyNo+"核保完成时间不能为空！");
//        }
//        if(Objects.isNull(dto.getInsuranceBeginDate())){
//            throw new GlobalBusinessException(message+policyNo+"保单起期不能为空！");
//        }
//        if(Objects.isNull(dto.getInsuranceEndDate())){
//            throw new GlobalBusinessException(message+policyNo+"保单止期不能为空！");
//        }
        ClmsPolicyHistoryUwInfoEntity entity = new ClmsPolicyHistoryUwInfoEntity();
        BeanUtils.copyProperties(dto,entity);
        entity.setId(UuidUtil.getUUID());
        entity.setCreatedBy(WebServletContext.getUserId());
        entity.setUpdatedBy(WebServletContext.getUserId());
        Date date = new Date();
        entity.setCreatedDate(date);
        entity.setUpdatedDate(date);
        entityList.add(entity);
    }

    /**
     * 处理保全报案号下的保单理赔核保历史信息
     */
    public void saveClaimPolicyUwInfo(List<String> policyNoList){
        if(CollectionUtil.isEmpty(policyNoList)){
            return;
        }
        //查询当前报案号，赔付次数下的保单的理赔已结案的单子的二核信息
        List<ClmsPolicyHistoryUwInfoEntity> clmsUwInfoList = clmsPolicyHistoryUwInfoMapper.getPlanUwPlanConclusionInfo(policyNoList);
        log.info("入参列表={}，UW下的保单历史核保信息={}", JsonUtils.toJsonString(policyNoList),JsonUtils.toJsonString(clmsUwInfoList));
        if(CollectionUtil.isEmpty(clmsUwInfoList)){
           return;
        }
        Date date = new Date();
        ReportCustomerInfoEntity reportCustomerInfoEntity
                = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(clmsUwInfoList.get(0).getReportNo());
        for (ClmsPolicyHistoryUwInfoEntity ent : clmsUwInfoList) {
            ent.setId(UuidUtil.getUUID());
            ent.setCreatedBy(WebServletContext.getUserId());
            ent.setUpdatedBy(WebServletContext.getUserId());
            ent.setCreatedDate(date);
            ent.setUpdatedDate(date);
            ent.setInsuredName(reportCustomerInfoEntity.getName());
            ent.setClientNo(reportCustomerInfoEntity.getClientNo());
        }
        clmsPolicyHistoryUwInfoMapper.insertBatch(clmsUwInfoList);

    }
}
