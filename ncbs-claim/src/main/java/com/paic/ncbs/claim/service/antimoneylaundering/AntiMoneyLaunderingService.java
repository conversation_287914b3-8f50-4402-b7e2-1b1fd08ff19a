package com.paic.ncbs.claim.service.antimoneylaundering;

import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;

import javax.servlet.http.HttpServletRequest;
/**
 * @author:QIANKEGONG513
 * date:2023/5/18
 * version:v0.0.1
 */
public interface AntiMoneyLaunderingService {
    /**
     * 根据ReportNo,clientName,clientCertificateNo,clientCertificateType查询反洗钱信息
     * @param clmsAntiMoneyLaunderingInfoDto 入参
     * @return ClmsAntiMoneyLaunderingInfoDto 出参
     */
    ClmsAntiMoneyLaunderingInfoDto getClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);
    /**
     * 保存反洗钱信息
     * @param clmsAntiMoneyLaunderingInfoDto 入参
     * @param
     */
    void addClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);

    /**
     * 删除反洗钱信息
     * @param clmsAntiMoneyLaunderingInfoDto 入参
     */
    void deleteClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);

    /**
     * 根据报案号，赔付次数，客户号删除反洗钱信息
     */
    void deleteClmsAmlInfoByCustomerNo(ClmsAntiMoneyLaunderingInfoDto parmsDto);


    /**
     * 根据姓名，证件类型，证件号查询反洗钱信息
     * @param parmsDto
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto   getAmlByNameAndCardTypeAndCardNo(ClmsAntiMoneyLaunderingInfoDto parmsDto);

    /**
     * 根据客户号查询反洗钱信息
     * @param parmsDto
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto getAmlByCustomerNo(ClmsAntiMoneyLaunderingInfoDto parmsDto);
}
