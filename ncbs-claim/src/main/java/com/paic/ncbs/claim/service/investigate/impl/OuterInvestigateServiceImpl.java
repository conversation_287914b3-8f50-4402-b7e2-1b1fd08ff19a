package com.paic.ncbs.claim.service.investigate.impl;

import java.util.Date;

import com.paic.ncbs.claim.service.common.IOperationRecordService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.RedisKeyConstants;
import com.paic.ncbs.claim.common.constant.investigate.InvestigateConstants;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateProcessMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentReqDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.model.dto.investigate.OuterInvestigateResultDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.service.investigate.InvestigateProcessService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.investigate.OuterInvestigateService;

import cn.hutool.core.util.StrUtil;

@Service
public class OuterInvestigateServiceImpl implements OuterInvestigateService{
	
	@Autowired
	private InvestigateProcessService investigateProcessService;
	
    @Autowired
    private FileUploadService fileUploadService;
    
    @Autowired
    private InvestigateService investigateService;
    
	@Autowired
	private InvestigateTaskMapper investigateTaskDao;
	
	@Autowired
	private InvestigateTaskAuditMapper investigateTaskAuditDao;
	
	@Autowired
	private InvestigateMapper investigateDao;
	
	@Autowired
    BpmService bpmService;
	
	@Autowired
	private CaseProcessService caseProcessService ;
	
    @Autowired
    private RedissonClient redissonClient;
    
    @Autowired
    private TaskInfoMapper taskInfoMapper;

	@Autowired
	private IOperationRecordService operationRecordService;

	@Override
    @Transactional(rollbackFor = Exception.class)
	public int finishTask(OuterInvestigateResultDTO outerInvestigateResult) {		
		
        String investigateId = outerInvestigateResult.getIdAhcsInvestigate();
        if(StrUtil.isEmptyIfStr(investigateId)) {
        	return 0;
        }		
		RLock lock = redissonClient.getLock(RedisKeyConstants.getBatchCloseCaseLock(investigateId));
        try {
            if (lock.tryLock()) {
            	
				InvestigateTaskDTO investigateTask = investigateTaskDao.getMajorInvestigateTaskByInvestigateId(investigateId);
				if(investigateTask == null || InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_AUDIT.equals(investigateTask.getTaskStatus()) || InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_FINISH.equals(investigateTask.getTaskStatus())) {
					return 0;
				}		
				
	          	TaskInfoDTO taskInfo = taskInfoMapper.getTaskDtoByTaskId(investigateTask.getIdAhcsInvestigateTask());
            	if(taskInfo == null || !BpmConstants.INVESTIGATE_PLATFORM.equals(taskInfo.getAssigner())) {
            		return 0;
				}
				
				boolean isFinish = this.checkIsFinishTask(investigateId);
				if (isFinish) {
					InvestigateDTO investigate = new InvestigateDTO();
					investigate.setIdAhcsInvestigate(investigateId);
					investigate.setUpdatedBy(investigateTask.getInvestigatorUm());
			        if (outerInvestigateResult.getIsHasAdjustingFee().equals("N")){
			        	outerInvestigateResult.setCommonEstimateFee(null);
			        }        
			        investigateService.modifyInvestigateForFee(investigateId, outerInvestigateResult.getIsHasAdjustingFee(),outerInvestigateResult.getCommonEstimateFee());
					investigate.setInvestigateStatus(InvestigateConstants.AHCS_INVESTIGATE_STATUS_AUDIT);
					investigateDao.modifyInvestigate(investigate);		
					
					investigateTask.setAbnormalDetail(outerInvestigateResult.getAbnormalDetail());
					investigateTask.setInvestigateConclusion(outerInvestigateResult.getInvestigateConclusion());
					investigateTask.setInvestigateQualitative(outerInvestigateResult.getInvestigateQualitative());
					investigateTask.setHasEvidence(outerInvestigateResult.getHasEvidence());
					investigateTask.setEvidenceDetail(outerInvestigateResult.getEvidenceDetail());					
					investigateTask.setTaskStatus(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_AUDIT);
					investigateTask.setFinishDate(new Date());
					investigateTask.setUpdatedBy(investigateTask.getInvestigatorUm());
					investigateTaskDao.modifyInvestigateTask(investigateTask);			
					
					InvestigateTaskAuditDTO investigateTaskAudit = new InvestigateTaskAuditDTO();
					investigateTaskAudit.setIdAhcsInvestigateTaskAudit(UuidUtil.getUUID());
					investigateTaskAudit.setIdAhcsInvestigateTask(investigateTask.getIdAhcsInvestigateTask());
					investigateTaskAudit.setInitiatorUm(investigateTask.getInvestigatorUm());
					investigateTaskAudit.setReviewUserUm(investigateTask.getDispatchUm());
					investigateTaskAudit.setCreatedBy(investigateTask.getInvestigatorUm());
					investigateTaskAudit.setUpdatedBy(investigateTask.getInvestigatorUm());
					investigateTaskAuditDao.addTaskAudit(investigateTaskAudit);
				
					bpmService.completeTask_oc(investigateTask.getReportNo(),investigateTask.getCaseTimes(),BpmConstants.OC_MAJOR_INVESTIGATE);
					//操作记录
					operationRecordService.insertOperationRecordByLabour(investigateTask.getReportNo(), BpmConstants.OC_MAJOR_INVESTIGATE, "回复", null);

					//操作记录
					operationRecordService.insertOperationRecordByLabour(investigateTask.getReportNo(), BpmConstants.OC_INVESTIGATE_REVIEW, "发起", null);
					bpmService.startProcessOc(investigateTask.getReportNo(),investigateTask.getCaseTimes(),BpmConstants.OC_INVESTIGATE_REVIEW,investigateTaskAudit.getIdAhcsInvestigateTaskAudit(),null,null);

					caseProcessService.updateCaseProcess(investigateTask.getReportNo(), investigateTask.getCaseTimes(),	CaseProcessStatus.INVESTIGATION_REVIEW.getCode());
					
					if(outerInvestigateResult.getInvestigateProcessList() != null) {
				        for (InvestigateProcessDTO investigateProcess : outerInvestigateResult.getInvestigateProcessList()) {
				           	investigateProcess.setCreatedBy(investigateTask.getInvestigatorUm());
				        	investigateProcess.setUpdatedBy(investigateTask.getInvestigatorUm());
				        	investigateProcess.setIdAhcsInvestigateTask(investigateTask.getIdAhcsInvestigateTask());				
				        	investigateProcess.setIdAhcsInvestigateProcess(UuidUtil.getUUID());
				            investigateProcessService.addInvestigateProcess(investigateProcess);
				        }
					}
					

					if(outerInvestigateResult.getDocumentList() != null) {
						UploadDocumentReqDTO uploadDocumentReq = new UploadDocumentReqDTO();
						uploadDocumentReq.setReportNo(investigateTask.getReportNo());
						uploadDocumentReq.setCaseTimes(investigateTask.getCaseTimes());
						uploadDocumentReq.setDocumentList(outerInvestigateResult.getDocumentList());
						fileUploadService.addFilesInfo(uploadDocumentReq);
					}
					
					return 1;
					
				} else {
					throw new GlobalBusinessException(ErrorCode.Investigate.HAS_INVESTIGATION_UNFINISHED);
				}
				
	        } else {
	            throw new GlobalBusinessException("该调查任务正在处理中！");
	        }            
        } 
        finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        
	}
	
	private boolean checkIsFinishTask(String investigateId) {
		int count = investigateTaskDao.getCountUnfinishedTaskByIdAhcsInvestigate(investigateId);
		if (count > 1) {
			return false;
		}
		int count2 = investigateTaskDao.getCountNlAuditingByIdAhcsInvestigate(investigateId);
		return count2 <= 0;
	}

}
