package com.paic.ncbs.claim.controller.doc;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileRealDownLoadAddressInfoDTO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "文件上传")
@RestController
@RequestMapping("/doc/app/fileUploadAction")
public class DocAppFileUploadController extends BaseController {


    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @ApiOperation("查看单证需要获取文件组列表")
    @RequestMapping(value = "/getDocumentList", method = RequestMethod.POST)
    public ResponseResult<List<FileInfoVO>> getDocumentList(@RequestBody FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
        UserInfoDTO userDTO = WebServletContext.getUser();
        String userId = userDTO.getUserCode();
        LogUtil.audit("查看单证reportNo={}",fileInfoDTO.getReportNo());
        // 获取文件列表
        List<FileInfoVO> fileUploadVOList = fileUploadService.getDocumentList(userId, fileInfoDTO);
        //替换https
//        if(ListUtils.isNotEmpty(fileUploadVOList)){
//            fileUploadVOList.forEach( a ->{
//                a.getSmallTypeList().forEach( b->{
//                  if(ListUtils.isNotEmpty(b.getDocumentList())) {
//                      b.getDocumentList().forEach( c ->{
//                          String url = c.getFileUrl();
//                          if(url != null && url.contains("http")){
//                              c.setFileUrl(url.replace("http","https"));
//                          }
//                      });
//                  }
//                });
//            });
//        }
        return ResponseResult.success(fileUploadVOList);
    }

    @ApiOperation("删除单证需要获取文件组列表")
    @PostMapping(value = "/removeDocumentList")
    public ResponseResult removeDocumentList(@RequestBody FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        UserInfoDTO userDTO = WebServletContext.getUser();
        this.validRemoveDocumentParams(fileInfoDTO, userDTO);
        LogUtil.audit("删除单证reportNo={}",fileInfoDTO.getReportNo());
        String wholeCaseStatus = wholeCaseBaseService.getWholeCaseStatus(fileInfoDTO.getReportNo(), fileInfoDTO.getCaseTimes());
        if (FileUploadConstants.CASE_STATUS_END.equals(wholeCaseStatus)) {
            LogUtil.audit("已结案的案件不能删除单证！ wholeCaseStatus=" + wholeCaseStatus);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_FILE_DELETE_CASE_END);
        }
        String updatedBy = userDTO.getUserName() + FileUploadConstants.DOCUMENT_UPLOADPERSONNEL_LEFT
                + userDTO.getUserCode() + FileUploadConstants.DOCUMENT_UPLOADPERSONNEL_RIGHT;
        LogUtil.audit("#微服务删除单证#删除人：" + updatedBy);
        String[] documentItemsIdArr = fileInfoDTO.getDocumentGroupItemsIdArr();
        if (documentItemsIdArr != null && documentItemsIdArr.length > 0) {
            FileInfoVO fileInfoVO = new FileInfoVO();
            fileInfoVO.setReportNo(fileInfoDTO.getReportNo());
            fileInfoVO.setCaseTimes(fileInfoDTO.getCaseTimes());
            fileInfoVO.setDocumentGroupItemsIdArr(documentItemsIdArr);
            fileUploadService.removeFileList(fileInfoVO, updatedBy);
        }

        return ResponseResult.success();
    }

    private void validRemoveDocumentParams(FileInfoDTO fileInfoDTO, UserInfoDTO userDTO) throws GlobalBusinessException {
        this.validFileParams(fileInfoDTO.getReportNo(), fileInfoDTO.getCaseTimes());
        String[] documentGroupItemsIdArr = fileInfoDTO.getDocumentGroupItemsIdArr();
        if ((null == documentGroupItemsIdArr || documentGroupItemsIdArr.length == 0)) {
            LogUtil.audit("文件参数(文件id数组)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "单证id数组");
        } else if (userDTO == null || userDTO.getUserCode() == null) {
            LogUtil.audit("用户信息不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "用户信息");
        }
    }

    @ApiOperation("修改单证需要获取文件组列表")
    @PostMapping(value = "/modifyDocumentList")
    public ResponseResult modifyDocumentList(@RequestBody FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        this.validModifyDocumentParams(fileInfoDTO);
        LogUtil.audit("更改单证类型reportNo={}",fileInfoDTO.getReportNo());
        String wholeCaseStatus = wholeCaseBaseService.getWholeCaseStatus(fileInfoDTO.getReportNo(), fileInfoDTO.getCaseTimes());
        if (FileUploadConstants.CASE_STATUS_END.equals(wholeCaseStatus)) {
            LogUtil.audit("已结案的案件不能更改！ wholeCaseStatus=" + wholeCaseStatus);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_FILE_MODIFY_CASE_END);
        }
        fileUploadService.modifyDocument(fileInfoDTO);
        return ResponseResult.success();
    }

    private void validModifyDocumentParams(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        this.validFileParams(fileInfoDTO.getReportNo(), fileInfoDTO.getCaseTimes());
        String[] documentGroupItemsIdArr = fileInfoDTO.getDocumentGroupItemsIdArr();
        if ((null == documentGroupItemsIdArr || documentGroupItemsIdArr.length == 0)) {
            LogUtil.audit("文件参数(文件id数组)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "单证id数组");
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getSmallCode())) {
            LogUtil.audit("参数smallCode(细类代码)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "细类代码");
        }
    }


    @ApiOperation("获取单证的GroupId")
    @RequestMapping(value = "/getDocumentGroupId", method = RequestMethod.POST)
    public ResponseResult<FileInfoDTO> getDocumentGroupId( @RequestBody FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        FileInfoDTO fileInfo = new FileInfoDTO();
        fileInfo.setDocumentGroupId(fileInfoDTO.getReportNo());
        fileInfo.setFirstUpload("Y");
        return ResponseResult.success(fileInfo);
    }

    @ApiOperation("获取单证真实访问地址")
    @GetMapping(value = "/getDocumentsRealAddress")
    public ResponseResult<FileRealDownLoadAddressInfoDTO> getDocumentsRealAddress(@ApiParam("原始单证地址key") @RequestParam("fileId")  String fileId) throws GlobalBusinessException {
        return ResponseResult.success(fileUploadService.getDocumentsRealAddress(fileId));
    }


    private void validFileParams(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(reportNo)) {
            LogUtil.audit("文件参数reportNo(报案号)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "报案号");
        } else if (caseTimes == null) {
            LogUtil.audit("文件参数caseTimes(赔付次数)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "赔付次数");
        }
    }


}
