package com.paic.ncbs.claim.service.antimoneylaundering;

import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;

/**
 * 反洗钱信息处理
 */
public interface ClmsAmlInfoService {
    /**
     * 处理反洗钱信息
     * @param dto
     */
    void saveAmlInfo(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 查询反洗钱
     * @param dto
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto  getClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 删除反洗器
     */
   void deleteClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto dto);


    /**
     * 根据姓名，证件类型 证件号查询反洗钱信息
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto  getAmlByNameAndCardTypeAndCardNo(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 查询反洗钱
     * @param dto
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto getClmsAntiMoneyLaunderingInfoNew(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 根据客户号查询反洗钱信息
     * @param dto
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto getAmlByCustomerNo(ClmsAntiMoneyLaunderingInfoDto dto);
}
