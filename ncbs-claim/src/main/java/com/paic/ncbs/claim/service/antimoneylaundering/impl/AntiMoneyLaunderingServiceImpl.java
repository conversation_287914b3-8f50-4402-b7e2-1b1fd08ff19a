package com.paic.ncbs.claim.service.antimoneylaundering.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AntiMoneyLaunderingMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.service.antimoneylaundering.AntiMoneyLaunderingService;
import com.paic.ncbs.claim.service.customer.CustomerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author:QIANKEGONG513
 * date:2023/5/18
 * version:v0.0.1
 */
@Slf4j
@Service("AntiMoneyLaunderingService")
public class AntiMoneyLaunderingServiceImpl implements AntiMoneyLaunderingService {

    @Autowired
    AntiMoneyLaunderingMapper antiMoneyLaunderingMapper;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Override
    public ClmsAntiMoneyLaunderingInfoDto getClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto) {

        return antiMoneyLaunderingMapper.getClmsAntiMoneyLaunderingInfo(clmsAntiMoneyLaunderingInfoDto);
    }

    @Override
    public void addClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto cdto) {

        String userCode = WebServletContext.getUserId();
        cdto.setCreatedBy(userCode);
        cdto.setUpdatedBy(userCode);

        if(StrUtil.isNotEmpty(cdto.getIdClmsAntiMoneyLaunderingInfo())){
            ClmsAntiMoneyLaunderingInfoDto oldDto =  getClmsAntiMoneyLaunderingInfoById(cdto.getIdClmsAntiMoneyLaunderingInfo());
            if(ObjectUtil.isEmpty(oldDto)){
                throw new GlobalBusinessException("更新的反洗钱信息不存请核实");
            }
            //判断 领款人姓名，证件类型，证件号 是否和元数据一致，如果一致 则不用调用crm获取客户
            if(!Objects.equals(cdto.getClientName(),oldDto.getClientName())
                    || !Objects.equals(cdto.getClientCertificateType(),oldDto.getClientCertificateType())
                    || !Objects.equals(cdto.getClientCertificateNo(),oldDto.getClientCertificateNo())){
                String customerNo = customerInfoService.getCustomerNo(cdto.getClientName(),cdto.getBankAccountAttribute()
                        ,cdto.getClientCertificateType(),cdto.getClientCertificateNo());
                cdto.setCustomerNo(customerNo);
            }
            BeanUtils.copyProperties(cdto,oldDto);
            antiMoneyLaunderingMapper.updateClmsAntiMoneyLaunderingInfoById(oldDto);

        }else{
            //调用crm接口获取客户号
            String customerNo = customerInfoService.getCustomerNo(cdto.getClientName(),cdto.getBankAccountAttribute()
                    ,cdto.getClientCertificateType(),cdto.getClientCertificateNo());
            //新增
            cdto.setCustomerNo(customerNo);
            antiMoneyLaunderingMapper.addClmsAntiMoneyLaunderingInfo(cdto);
        }

    }

    /**
     * 根据个人反洗钱信息表主键查询
     * @param idClmsAntiMoneyLaunderingInfo
     */
    private ClmsAntiMoneyLaunderingInfoDto getClmsAntiMoneyLaunderingInfoById(String idClmsAntiMoneyLaunderingInfo) {
       return  antiMoneyLaunderingMapper.getClmsAntiMoneyLaunderingInfoById(idClmsAntiMoneyLaunderingInfo);
    }

    @Override
    public void deleteClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto) {
        antiMoneyLaunderingMapper.deleteClmsAntiMoneyLaunderingInfo(clmsAntiMoneyLaunderingInfoDto);
    }

    /**
     * 根据报案号，赔付次数，客户号 删除个人反洗钱信息
     * @param parmsDto
     */
    @Override
    public void deleteClmsAmlInfoByCustomerNo(ClmsAntiMoneyLaunderingInfoDto parmsDto) {
        antiMoneyLaunderingMapper.deleteClmsAmlInfoByCustomerNo(parmsDto);
    }

    /**
     * 根据姓名，证件类型，证件号查询反洗钱信息
     * @param parmsDto
     * @return
     */
    @Override
    public ClmsAntiMoneyLaunderingInfoDto getAmlByNameAndCardTypeAndCardNo(ClmsAntiMoneyLaunderingInfoDto parmsDto) {
        return antiMoneyLaunderingMapper.getAmlByNameAndCardTypeAndCardNo(parmsDto);
    }

    @Override
    public ClmsAntiMoneyLaunderingInfoDto getAmlByCustomerNo(ClmsAntiMoneyLaunderingInfoDto parmsDto) {
        return antiMoneyLaunderingMapper.getAmlByCustomerNo(parmsDto);
    }
}
