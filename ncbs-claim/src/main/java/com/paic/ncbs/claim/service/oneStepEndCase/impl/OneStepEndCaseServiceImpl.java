package com.paic.ncbs.claim.service.oneStepEndCase.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDiseaseVO;
import com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.oneStepEndCase.*;
import com.paic.ncbs.claim.model.dto.openapi.*;
import com.paic.ncbs.claim.model.dto.report.DutyEstimateLossDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillReduceDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.*;
import com.paic.ncbs.claim.model.vo.oneStepEndCase.OneStepEndCaseResponse;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.model.vo.report.OnlineReportResponseVO;
import com.paic.ncbs.claim.model.vo.report.OnlineReportVO;
import com.paic.ncbs.claim.service.ahcs.AhcsPolicyDutyService;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.oneStepEndCase.OneStepEndCaseService;
import com.paic.ncbs.claim.service.openapi.OnlineRegisterService;
import com.paic.ncbs.claim.service.openapi.ReceiveTpaAssignmentService;
import com.paic.ncbs.claim.service.openapi.ReceiveTpaSettleService;
import com.paic.ncbs.claim.service.report.OnlineReportService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.report.ReportInfoExService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.*;

@Service
public class OneStepEndCaseServiceImpl implements OneStepEndCaseService {

    @Autowired
    private OnlineReportService onlineReportService;
    @Autowired
    private OnlineRegisterService onlineRegisterService;
    @Autowired
    private MedicalBillService medicalBillService;
    @Autowired
    private ReceiveTpaSettleService receiveTpaSettleService;
    @Autowired
    private ReceiveTpaAssignmentService receiveTpaAssignmentService;
    @Autowired
    private AhcsPolicyDutyService policyDutyService;
    @Autowired
    private ReportInfoExService reportInfoExService;
    @Autowired
    private RegisterCaseService registerCaseService;
    @Autowired
    private CaseZeroCancelService caseZeroCancelService;


    @Transactional
    @Override
    public OneStepEndCaseResponse oneStepEndCase(OneStepEndCaseDto oneStepEndCaseDto) throws Exception {
        //重复报案校验
        String acceptanceNo = oneStepEndCaseDto.getAcceptanceNumber();
        OneStepEndCaseResponse oldReportResponse = checkReportRepeat(acceptanceNo);
        if(Objects.nonNull(oldReportResponse)) {
            return oldReportResponse;
        }
        OneStepEndCaseResponse oneStepEndCaseResponse = new OneStepEndCaseResponse();
        //组装报案数据-报案
        OnlineReportVO onlineReportVO = new OnlineReportVO();
        buildReportVO(oneStepEndCaseDto, onlineReportVO);

        OnlineReportResponseVO onlineReportResponseVO = onlineReportService.saveReport(onlineReportVO);
        String reportNo = onlineReportResponseVO.getReportNo();
        String policyNo = oneStepEndCaseDto.getReportBaseInfoDto().getPolicyNo();
        LogUtil.info("受理号：{}，报案已完成，报案号：{}", acceptanceNo, reportNo);

        TpaAssignmentDTO tpaAssignmentDTO = new TpaAssignmentDTO();
        tpaAssignmentDTO.setCaseTimes(1);
        tpaAssignmentDTO.setReportNo(reportNo);
        tpaAssignmentDTO.setTpaCom(Constants.SYSTEM_USER);
        tpaAssignmentDTO.setTpaComName(Constants.SYSTEM_USER);
        receiveTpaAssignmentService.receive(tpaAssignmentDTO);
        //组装立案数据-立案
        List<MedicalBillDto> medicalBillDTOList = oneStepEndCaseDto.getLossAssessmentInfoDto().getPeopleHurtVO().getMedicalBillDTOList();
        DutySurveyVO dutySurveyVO = new DutySurveyVO();
        MedicalDTO medicalDTO = new MedicalDTO();
        buildDutySurveyVO(oneStepEndCaseDto, medicalBillDTOList, dutySurveyVO,medicalDTO);
        dutySurveyVO.setReportNo(reportNo);
        onlineRegisterService.register(dutySurveyVO);
        CaseRegisterApplyDTO registerApplyDTO = onlineRegisterService.dealWorkFlow(reportNo, 1);
        LogUtil.info("受理号：{}，立案已完成，立案号返回：{}", acceptanceNo, JSON.toJSONString(registerApplyDTO));
        //组装收单数据-收单
//        MedicalDTO medicalDTO = new MedicalDTO();
//        buildMedicalDTO(medicalBillDTOList, medicalDTO);
        medicalDTO.setReportNo(reportNo);
        DutySurveyVO dutyVO = medicalBillService.addOuterMedicalBill(medicalDTO);
        LogUtil.info("受理号：{}，收单已完成，收单号返回：{}", acceptanceNo, JSON.toJSONString(dutyVO));
        //判断走零注，还是正常赔付
        SettleInfoDto settleInfoDto = oneStepEndCaseDto.getSettleInfoDto();
        if ("1".equals(settleInfoDto.getCaseConclusion())) {
            //组装理算数据-理算
            ReceiveTpaSettleDTO settleDTO = new ReceiveTpaSettleDTO();
            buildSettleDTO(oneStepEndCaseDto, reportNo, policyNo, settleDTO);
            receiveTpaSettleService.dealTpaSettle(settleDTO);
            LogUtil.info("受理号：{}，理算已完成。", acceptanceNo);
        } else {
            //保存零注申请信息
            CaseZeroCancelDTO caseZeroCancelDTO = new CaseZeroCancelDTO();
            caseZeroCancelDTO.setReportNo(reportNo);
            caseZeroCancelDTO.setCaseTimes(1);
            caseZeroCancelDTO.setStatus("1");
            if ("2".equals(settleInfoDto.getCaseConclusion())) {
                caseZeroCancelDTO.setApplyType("1"); // 零结
            } else {
                caseZeroCancelDTO.setApplyType("2"); // 注销
            }
            caseZeroCancelDTO.setReasonCode(settleInfoDto.getConclusionDetailCode());
            caseZeroCancelDTO.setApplyReasonDetails(settleInfoDto.getConclusionDetailMsg());
            caseZeroCancelDTO.setApplyDate(new Date());
            caseZeroCancelDTO.setReduceAmount(BigDecimal.ZERO);
            if(StringUtils.isNotEmpty(onlineReportVO.getReportSubMode()) && onlineReportVO.getReportSubMode().equals("9-05")) {
                caseZeroCancelDTO.setVerifyPass(true);
            }
            caseZeroCancelService.saveCaseZeroCancelApply(caseZeroCancelDTO);
            LogUtil.info("受理号：{}，零注已完成，报案号：{}", acceptanceNo, reportNo);
        }
        //返回
        oneStepEndCaseResponse.setReportNo(reportNo);
        oneStepEndCaseResponse.setCaseTimes(1);
        oneStepEndCaseResponse.setRegisterNo(registerApplyDTO.getIdAhcsCaseRegisterApply());
        return oneStepEndCaseResponse;
    }

    private void buildSettleDTO(OneStepEndCaseDto oneStepEndCaseDto, String reportNo, String policyNo, ReceiveTpaSettleDTO settleDTO) {
        SettleInfoDto settleInfoDto = oneStepEndCaseDto.getSettleInfoDto();
        settleDTO.setReportNo(reportNo);
        settleDTO.setCaseTimes(1);
        //封装支付信息
        List<TpaPaymentItemDTO> paymentItemList = new ArrayList<>();
        TpaPaymentItemDTO paymentItemDTO = new TpaPaymentItemDTO();
        paymentItemDTO.setPaymentAmount(String.valueOf(settleInfoDto.getSettleAmount()));
        paymentItemDTO.setPaymentUsage("P1");
        paymentItemDTO.setPolicyNo(policyNo);
        paymentItemList.add(paymentItemDTO);
        settleDTO.setPaymentItemList(paymentItemList);
        //封装险种、责任、责任明细等信息
        List<TpaPolicyDTO> policyList = new ArrayList<>();
        TpaPolicyDTO tpaPolicyDTO = new TpaPolicyDTO();
        tpaPolicyDTO.setPolicyNo(policyNo);
        tpaPolicyDTO.setSettleAmount(String.valueOf(settleInfoDto.getSettleAmount()));
        List<TpaPlanDTO> planPayList = new ArrayList<>();
        for (PlanPayDto planPayDto : settleInfoDto.getPlanPayDtoList()) {
            TpaPlanDTO tpaPlanDTO = new TpaPlanDTO();
            BigDecimal planAmount = BigDecimal.ZERO;
            List<TpaDutyDTO> dutyPayList = new ArrayList<>();
            for (DutyPayDto dutyPayDto : planPayDto.getDutyPayDtoList()) {
                TpaDutyDTO tpaDutyDTO = new TpaDutyDTO();
                tpaDutyDTO.setDutyCode(dutyPayDto.getDutyCode());
                tpaDutyDTO.setSettleReason(dutyPayDto.getSettleReason());
                BigDecimal dutyPayAmount = BigDecimal.ZERO;
                List<TpaDutyDetailDTO> dutyDetailPayList = new ArrayList<>();
                for (DutyDetailPayDto dutyDetailPayDto : dutyPayDto.getDutyDetailPayList()) {
                    TpaDutyDetailDTO tpaDutyDetailDTO = new TpaDutyDetailDTO();
                    tpaDutyDetailDTO.setDutyDetailCode(dutyDetailPayDto.getDutyDetailCode());
                    tpaDutyDetailDTO.setAutoSettleAmount(String.valueOf(dutyDetailPayDto.getAutoSettleAmount()));
                    dutyPayAmount = dutyPayAmount.add(dutyDetailPayDto.getAutoSettleAmount());
                    dutyDetailPayList.add(tpaDutyDetailDTO);
                }
                tpaDutyDTO.setSettleAmount(String.valueOf(dutyPayAmount));
                tpaDutyDTO.setDutyDetailPayList(dutyDetailPayList);

                if (ListUtils.isNotEmpty(dutyPayDto.getDutyBillLimitInfoList())) {
                    List<DutyBillLimitInfo> tpaDutyBillLimitInfoDTOList = new ArrayList<>();
                    for (DutyBillLimitInfoDto dutyBillLimitInfoDto : dutyPayDto.getDutyBillLimitInfoList()) {
                        DutyBillLimitInfo tpaDutyBillLimitInfo = new DutyBillLimitInfo();
                        tpaDutyBillLimitInfo.setBillDate(dutyBillLimitInfoDto.getBillDate());
                        tpaDutyBillLimitInfo.setLimitType(dutyBillLimitInfoDto.getLimitType());
                        tpaDutyBillLimitInfo.setSettleClaimAmount(String.valueOf(dutyBillLimitInfoDto.getSettleClaimAmount()));
                        tpaDutyBillLimitInfoDTOList.add(tpaDutyBillLimitInfo);
                    }
                    tpaDutyDTO.setDutyBillLimitInfoList(tpaDutyBillLimitInfoDTOList);
                }
                planAmount = planAmount.add(dutyPayAmount);
                dutyPayList.add(tpaDutyDTO);
            }
            String planCode = policyDutyService.getPlanCodeByDuty(reportNo, policyNo, dutyPayList.get(0).getDutyCode());
            tpaPlanDTO.setPlanCode(planCode);
            tpaPlanDTO.setDutyPayList(dutyPayList);
            tpaPlanDTO.setSettleAmount(String.valueOf(planAmount));
            planPayList.add(tpaPlanDTO);
        }
        tpaPolicyDTO.setPlanPayList(planPayList);
        policyList.add(tpaPolicyDTO);
        policyList = mergeDutyPayByPlanCode(policyList);
        settleDTO.setPolicyList(policyList);
        settleDTO.setIsAutoVerify(true);
    }

    private List<TpaPolicyDTO> mergeDutyPayByPlanCode(List<TpaPolicyDTO> policyList) {
        // 合并planPayList中险种相同的元素
        List<TpaPolicyDTO> mergedPolicyList = new ArrayList<>();
        for (TpaPolicyDTO policy : policyList) {
            BigDecimal sumSettleAmount = new BigDecimal(0);
            List<TpaPlanDTO>  planDTOList = new ArrayList<>();
            Map<String, List<TpaPlanDTO>> planMap = policy.getPlanPayList().stream().collect(Collectors.groupingBy(TpaPlanDTO::getPlanCode));
            for(Map.Entry<String, List<TpaPlanDTO>> planEntry : planMap.entrySet()) {
                List<TpaDutyDTO>  dutyPayList = new ArrayList<>();
                BigDecimal settleAmount = new BigDecimal(0);
                for (TpaPlanDTO plan : planEntry.getValue()) {
                    settleAmount = settleAmount.add(new BigDecimal(plan.getSettleAmount()));
                    sumSettleAmount = sumSettleAmount.add(new BigDecimal(plan.getSettleAmount()));
                    dutyPayList.addAll(plan.getDutyPayList());
                }
                TpaPlanDTO tpaPlanDTO = new TpaPlanDTO();
                tpaPlanDTO.setPlanCode(planEntry.getKey());
                tpaPlanDTO.setSettleAmount(String.valueOf(settleAmount));
                tpaPlanDTO.setDutyPayList(dutyPayList);
                planDTOList.add(tpaPlanDTO);
            }
            policy.setSettleAmount(String.valueOf(sumSettleAmount));
            policy.setPlanPayList(planDTOList);
            mergedPolicyList.add(policy);
            /*boolean merged = false;
            for (TpaPolicyDTO mergedPolicy : mergedPolicyList) {
                if (mergedPolicy.getPlanPayList().stream().anyMatch(mergedPlan -> mergedPlan.getPlanCode().equals(policy.getPlanPayList().get(0).getPlanCode()))) {
                    policy.getPlanPayList().forEach(plan -> {
                        Optional<TpaPlanDTO> optionalMergedPlan = mergedPolicy.getPlanPayList().stream()
                                .filter(mergedPlan -> mergedPlan.getPlanCode().equals(plan.getPlanCode())).findFirst();
                        if (optionalMergedPlan.isPresent()) {
                            TpaPlanDTO mergedPlan = optionalMergedPlan.get();
                            plan.getDutyPayList().forEach(duty -> {
                                Optional<TpaDutyDTO> optionalMergedDuty = mergedPlan.getDutyPayList().stream()
                                        .filter(mergedDuty -> mergedDuty.getDutyCode().equals(duty.getDutyCode())).findFirst();
                                if (optionalMergedDuty.isPresent()) {
                                    TpaDutyDTO mergedDuty = optionalMergedDuty.get();
                                    BigDecimal newAmount = new BigDecimal(mergedDuty.getSettleAmount()).add(new BigDecimal(duty.getSettleAmount()));
                                    mergedDuty.setSettleAmount(String.valueOf(newAmount));
                                    mergedDuty.getDutyDetailPayList().addAll(duty.getDutyDetailPayList());
                                    if (ListUtils.isNotEmpty(duty.getDutyBillLimitInfoList())) {
                                        if (ListUtils.isNotEmpty(mergedDuty.getDutyBillLimitInfoList())) {
                                            mergedDuty.getDutyBillLimitInfoList().addAll(duty.getDutyBillLimitInfoList());
                                        } else {
                                            mergedDuty.setDutyBillLimitInfoList(duty.getDutyBillLimitInfoList());
                                        }
                                    }
                                } else {
                                    mergedPlan.getDutyPayList().add(duty);
                                }
                            });
                            BigDecimal planNewAmount = new BigDecimal(mergedPlan.getSettleAmount()).add(new BigDecimal(plan.getSettleAmount()));
                            mergedPlan.setSettleAmount(String.valueOf(planNewAmount));
                        } else {
                            mergedPolicy.getPlanPayList().add(plan);
                        }
                    });
                    merged = true;
                }
            }
            if (!merged) {
                mergedPolicyList.add(policy);
            }*/
        }
        return mergedPolicyList;
    }

    private void buildDutySurveyVO(OneStepEndCaseDto oneStepEndCaseDto, List<MedicalBillDto> medicalBillDTOList, DutySurveyVO dutySurveyVO,MedicalDTO medicalDTO) {
        SurveyVO surveyVO = new SurveyVO();
        surveyVO.setPhoneSurveyDetail("报案");
        dutySurveyVO.setSurveyVO(surveyVO);
        LossAssessmentInfoDto lossAssessmentInfoDto = oneStepEndCaseDto.getLossAssessmentInfoDto();
        BeanUtils.copyProperties(lossAssessmentInfoDto, dutySurveyVO);
        dutySurveyVO.setCaseTimes(1);
        dutySurveyVO.setStatus("0");
        dutySurveyVO.setTaskId("reportTrack");
        dutySurveyVO.setMigrateFrom("na");
        PeopleHurtVO peopleHurtVO = new PeopleHurtVO();
        BeanUtils.copyProperties(lossAssessmentInfoDto.getPeopleHurtVO(), peopleHurtVO);
        PersonAccidentVO personAccidentVO = new PersonAccidentVO();
        BeanUtils.copyProperties(lossAssessmentInfoDto.getPeopleHurtVO().getAccidentVO(), personAccidentVO);
        peopleHurtVO.setAccidentVO(personAccidentVO);
        PersonDiseaseVO personDiseaseVO = new PersonDiseaseVO();
        BeanUtils.copyProperties(lossAssessmentInfoDto.getPeopleHurtVO().getDiseaseVO(), personDiseaseVO);
        peopleHurtVO.setDiseaseVO(personDiseaseVO);
        PersonDiagnoseVO personDiagnoseVO = new PersonDiagnoseVO();
        List<PersonDiagnoseDTO> diagnoseDTOs = new ArrayList<>();
        for (DiagnoseDto diagnoseDTO : lossAssessmentInfoDto.getPeopleHurtVO().getDiagnoseDTOList()) {
            PersonDiagnoseDTO personDiagnoseDTO = new PersonDiagnoseDTO();
            BeanUtils.copyProperties(diagnoseDTO, personDiagnoseDTO);
            diagnoseDTOs.add(personDiagnoseDTO);
        }
        personDiagnoseVO.setDiagnoseDTOs(diagnoseDTOs);
        peopleHurtVO.setDiagnoseVO(personDiagnoseVO);
        List<PersonHospitalDTO> personHospitalDTOs = new ArrayList<>();
//        List<MedicalBillDto> medicalBillDTOList = oneStepEndCaseDto.getLossAssessmentInfoDto().getPeopleHurtVO().getMedicalBillDTOList();
        List<MedicalInfoDTO> medicalInfos = new ArrayList<>();
        for (MedicalBillDto medicalBillDto : medicalBillDTOList) {
            PersonHospitalDTO personHospitalDTO = new PersonHospitalDTO();
            BeanUtils.copyProperties(medicalBillDto, personHospitalDTO);
            personHospitalDTO.setHospitalPropertyDes(medicalBillDto.getHospitalProperty());
            personHospitalDTO.setMedicalStatus(oneStepEndCaseDto.getLossAssessmentInfoDto().getPeopleHurtVO().getMedicalStatus());
            personHospitalDTOs.add(personHospitalDTO);

            //收单数据
            MedicalInfoDTO medicalInfoDTO = new MedicalInfoDTO();
            medicalInfoDTO.setDiagnoseList(medicalBillDto.getDiagnoseInfos());
            List<MedicalBillDTO> medicalBillList = new ArrayList<>();
            MedicalBillDTO medicalBillDTO = new MedicalBillDTO();
            BeanUtils.copyProperties(medicalBillDto, medicalBillDTO);
            medicalBillDTO.setPrepaidType(medicalBillDto.getThirdPaidType());
            medicalBillDTO.setPrepaidAmount(medicalBillDto.getThirdPaidAmount());
            medicalBillDTO.setDeductibleAmount(medicalBillDto.getSelfPaidAmount());
            medicalBillDTO.setImmoderateAmount(medicalBillDto.getImmoderateAmount());
            medicalBillDTO.setPartialDeductible(medicalBillDto.getPartSelfPaidAmount());
            medicalBillDTO.setStartDate(medicalBillDto.getTreatmentStartDate());
            medicalBillDTO.setEndDate(medicalBillDto.getTreatmentEndDate());
            medicalBillDTO.setHospitalPropertyDes(medicalBillDto.getHospitalProperty());

            List<MedicalBillReduceDto> medicalBillReduceDtoList = medicalBillDto.getMedicalBillReduceDtoList();
            if (ListUtils.isNotEmpty(medicalBillReduceDtoList)) {
                List<MedicalBillReduceDTO> medicalBillReduceDTOList = new ArrayList<>();
                for (MedicalBillReduceDto medicalBillReduceDto : medicalBillReduceDtoList) {
                    MedicalBillReduceDTO medicalBillReduceDTO = new MedicalBillReduceDTO();
                    BeanUtils.copyProperties(medicalBillReduceDto, medicalBillReduceDTO);
                    medicalBillReduceDTOList.add(medicalBillReduceDTO);
                }
                medicalBillDTO.setMedicalBillReduceDTOList(medicalBillReduceDTOList);
            }
            medicalBillList.add(medicalBillDTO);
            medicalInfoDTO.setMedicalBillDTOList(medicalBillList);
            medicalInfos.add(medicalInfoDTO);
        }
        medicalDTO.setMedicalInfos(medicalInfos);
        PersonHospitalVO personHospitalVO = new PersonHospitalVO();
        personHospitalVO.setPersonHospitalList(personHospitalDTOs);
        peopleHurtVO.setHospitalVO(personHospitalVO);
        dutySurveyVO.setPeopleHurtVO(peopleHurtVO);
        EstimatePolicyFormDTO estimatePolicyFormDTO = new EstimatePolicyFormDTO();
        List<DutyEstimateLossDTO> dutyEstimateLossList = new ArrayList<>();
        BigDecimal sumDutyAmount = BigDecimal.ZERO;
        for (PlanPayDto planPayDto : oneStepEndCaseDto.getSettleInfoDto().getPlanPayDtoList()) {
            for (DutyPayDto dutyPayDto : planPayDto.getDutyPayDtoList()) {
                DutyEstimateLossDTO dutyEstimateLossDTO = new DutyEstimateLossDTO();
                String dutyCode = dutyPayDto.getDutyCode();
                BigDecimal dutyAmount = BigDecimal.ZERO;
                for (DutyDetailPayDto dutyDetailPayDto : dutyPayDto.getDutyDetailPayList()) {
                    if (null != dutyDetailPayDto.getAutoSettleAmount()) {
                        dutyAmount = dutyAmount.add(dutyDetailPayDto.getAutoSettleAmount());
                        sumDutyAmount = sumDutyAmount.add(dutyDetailPayDto.getAutoSettleAmount());
                    }
                }
                dutyEstimateLossDTO.setDutyCode(dutyCode);
                dutyEstimateLossDTO.setEstimateLossAmount(dutyAmount);
                dutyEstimateLossList.add(dutyEstimateLossDTO);
            }
        }
        //立案金额不能为0
        if (sumDutyAmount.compareTo(BigDecimal.ZERO) == 0) {
            if (null != dutyEstimateLossList && dutyEstimateLossList.size() > 0) {
                dutyEstimateLossList.get(0).setEstimateLossAmount(BigDecimal.ONE);
            }
        }
        estimatePolicyFormDTO.setDutyEstimateLossList(dutyEstimateLossList);
        dutySurveyVO.setEstimatePolicyFormDTO(estimatePolicyFormDTO);
    }

    private void buildReportVO(OneStepEndCaseDto oneStepEndCaseDto, OnlineReportVO onlineReportVO) {
        ReportBaseInfoDto reportBaseInfoDto = oneStepEndCaseDto.getReportBaseInfoDto();
        String policyNo = reportBaseInfoDto.getPolicyNo();
        AccidentInfoDto accidentInfoDto = oneStepEndCaseDto.getAccidentInfoDto();


        BeanUtils.copyProperties(reportBaseInfoDto, onlineReportVO);
        BeanUtils.copyProperties(accidentInfoDto, onlineReportVO);
        List<LinkManEntity> linkManList = new ArrayList<>();
        LinkManEntity linkManEntity = new LinkManEntity();
        linkManEntity.setLinkManName(reportBaseInfoDto.getLinkManName());
        linkManEntity.setLinkManTelephone(reportBaseInfoDto.getLinkManTelephone());
        linkManEntity.setLinkManRelation(reportBaseInfoDto.getLinkManRelation());
        linkManEntity.setApplicantPerson(reportBaseInfoDto.getApplicantPerson());
        linkManEntity.setApplicantType(reportBaseInfoDto.getApplicantType());
        linkManEntity.setCertificateNo(reportBaseInfoDto.getReporterCertificateNo());
        linkManEntity.setCertificateType(reportBaseInfoDto.getReporterCertificateType());
        linkManList.add(linkManEntity);
        onlineReportVO.setLinkManList(linkManList);
        onlineReportVO.setMedicalStatus(oneStepEndCaseDto.getLossAssessmentInfoDto().getPeopleHurtVO().getMedicalStatus());
        List<PaymentInfoVO> paymentList = new ArrayList<>();
        List<PaymentDto> paymentDtoList = oneStepEndCaseDto.getSettleInfoDto().getPaymentDtoList();
        for (PaymentDto paymentDto : paymentDtoList) {
            PaymentInfoVO paymentInfoVO = new PaymentInfoVO();
            BeanUtils.copyProperties(paymentDto, paymentInfoVO);
            paymentList.add(paymentInfoVO);
        }

        onlineReportVO.setPaymentList(paymentList);
        List<String> policyNoList = new ArrayList<>();
        policyNoList.add(reportBaseInfoDto.getPolicyNo());
        onlineReportVO.setPolicyNos(policyNoList);
    }

    private OneStepEndCaseResponse checkReportRepeat(String acceptanceNo) {
        //判断是否重复报案

        List<ReportInfoExEntity> reportInfoExList = reportInfoExService.getReportInfoExByAcceptanceNo(acceptanceNo);
        if (!reportInfoExList.isEmpty()) {
            OneStepEndCaseResponse oneStepEndCaseResponse = new OneStepEndCaseResponse();
            String reportNo = reportInfoExList.get(0).getReportNo();
            CaseRegisterApplyDTO dto = registerCaseService.getLastCaseRegisterApplyDTO(reportNo,1);
            oneStepEndCaseResponse.setReportNo(reportNo);
            oneStepEndCaseResponse.setRegisterNo(dto.getIdAhcsCaseRegisterApply());
            oneStepEndCaseResponse.setCaseTimes(1);
//            throw new GlobalBusinessException(GlobalResultStatus.EX_REPORT_944000.getCode(),
//                    GlobalResultStatus.EX_REPORT_944000.format(reportNo));
            return oneStepEndCaseResponse;
        }
        return null;

    }
}
