package com.paic.ncbs.claim.controller.who.facade;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.vo.checkloss.LossReduceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;

@Api(tags = "历史案件减损信息")
@Controller
@RequestMapping("/who/app/lossReduceAction")
public class LossReduceController extends BaseController {

    @ApiOperation(value = "获取减损信息")
    @ResponseBody
    @RequestMapping(value = "/getLossReduceInfo/{reportNo}/{caseTimes}", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<LossReduceVO> getLossReduceInfo(@PathVariable("reportNo") String reportNo,
                                                          @PathVariable("caseTimes") String caseTimes) {
        LogUtil.audit("获取减损信息 reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        return ResponseResult.success(new LossReduceVO());
    }

    @ResponseBody
    @RequestMapping(value = "/getReduceOutsourcingCompany/{reportNo}/{caseTimes}", method = RequestMethod.GET)
    public ResponseResult getReduceOutsourcingCompany(@ApiParam("报案号") @PathVariable("reportNo") String reportNo,
                                                      @ApiParam("赔付次数") @PathVariable("caseTimes") String caseTimes) {
        return ResponseResult.success(new ArrayList<>());
    }

    @ResponseBody
    @RequestMapping(value = "/getReduceType", method = RequestMethod.GET)
    public ResponseResult getReduceType() {

        return ResponseResult.success(new ArrayList<>());
    }


}
