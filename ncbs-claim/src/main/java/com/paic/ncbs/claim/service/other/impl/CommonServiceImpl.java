package com.paic.ncbs.claim.service.other.impl;

import cn.hutool.core.util.ObjectUtil;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.RapeDateUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.other.CommonDTO;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.document.model.dto.RequestNoDTO;
import com.paic.ncbs.document.model.dto.ResponseNoDTO;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import com.paic.ncbs.document.service.GenerateVoucherUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.LogUtil.log;

@Service
@RefreshScope
public class CommonServiceImpl extends BaseServiceImpl<CommonDTO> implements CommonService {

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private FeePayMapper feePayMapper;

    @Autowired
    private AhcsPolicyPlanMapper policyPlanMapper;

    @Autowired
    private CoinsureService coinsureService;

    @Value("${env}")
    private String env;

    @Value("${switch.redisSeq}")
    private Boolean redisSeq;

    @Value("${initRedisSeq}")
    private Long initRedisSeq;

    private int seq_report = 1;
    private int seq_case = 1;

    @Override
    public String generateIdBySysGuid() {
        return commonMapper.generateIdBySysGuid();
    }

    @Override
    public BaseDao<CommonDTO> getDao() {
        return commonMapper;
    }

    @Override
    public String generateReportNo(String departmentCode) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        return "8" + departmentCode + getReportNo();
    }

    @Override
    public String generateReportNoNew(String departmentCode, String flag) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        return "9" + departmentCode + flag + getReportNo();
    }

    /*
     * 生成报案申请号
     */
    @Override
    public String generateApplyNo(String departmentCode, String flag) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        return "8" + departmentCode + flag + getApplyNo();
    }

    @Override
    public String generateCaseNo(String departmentCode) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        return "4" + departmentCode + getCaseNo();
    }

    @Override
    public String generateCaseNoNew(String departmentCode, String flag) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        String yearMonth = DateUtils.getCurrentDateFormat("YYMM");
        return "4" + departmentCode + yearMonth + flag+ getCaseNoEleven();
    }

    private String getReportNo() {
        String seq = "";
        if(redisSeq){
            try{
                RAtomicLong reportAtomicLong = redissonClient.getAtomicLong("SEQ_CLM_REPORT_NO_NEW");
                if(reportAtomicLong.get() < initRedisSeq){
                    reportAtomicLong.set(initRedisSeq);
                }
                seq = String.valueOf(reportAtomicLong.incrementAndGet());
            } catch (Exception e) {
                seq = commonMapper.getReportSeq();
                log.error("redis生成报案号异常！，message:{}", e.getMessage(), e);
            }
        } else {
            seq = commonMapper.getReportSeq();
        }
        StringBuilder sb = new StringBuilder();
        int diff = 15 - seq.length();
        for (int i = 0; i < diff; i++) {
            sb.append("0");
        }
        return sb.toString() + seq;
    }

    private String getApplyNo() {
        String seq = "";
        if(redisSeq){
            try{
                RAtomicLong reportAtomicLong = redissonClient.getAtomicLong("SEQ_CLM_APPLY_NO_NEW");
                if(reportAtomicLong.get() < initRedisSeq){
                    reportAtomicLong.set(initRedisSeq);
                }
                seq = String.valueOf(reportAtomicLong.incrementAndGet());
            } catch (Exception e) {
                seq = commonMapper.getApplySeq();
                log.error("redis生成报案申请号异常！，message:{}", e.getMessage(), e);
            }
        } else {
            seq = commonMapper.getApplySeq();
        }
        StringBuilder sb = new StringBuilder();
        int diff = 15 - seq.length();
        for (int i = 0; i < diff; i++) {
            sb.append("0");
        }
        return sb.toString() + seq;
    }

    private String getCaseNo() {
        String seq = "";
        if(redisSeq){
            try{
                RAtomicLong reportAtomicLong = redissonClient.getAtomicLong("SEQ_CLM_CASE_NO_NEW");
                if(reportAtomicLong.get() < initRedisSeq){
                    reportAtomicLong.set(initRedisSeq);
                }
                seq = String.valueOf(reportAtomicLong.incrementAndGet());
            } catch (Exception e) {
                seq = commonMapper.getCaseSeq();
                log.error("redis生成赔案号异常！，message:{}", e.getMessage(), e);
            }
        } else {
            seq = commonMapper.getCaseSeq();
        }
        StringBuilder sb = new StringBuilder();
        int diff = 16 - seq.length();
        for (int i = 0; i < diff; i++) {
            sb.append("0");
        }
        return sb.toString() + seq;
    }

    private String getCaseNoEleven() {
        String seq = "";
        if(redisSeq){
            try{
                RAtomicLong reportAtomicLong = redissonClient.getAtomicLong("SEQ_CLM_CASE_NO_NEW");
                if(reportAtomicLong.get() < initRedisSeq){
                    reportAtomicLong.set(initRedisSeq);
                }
                seq = String.valueOf(reportAtomicLong.incrementAndGet());
            } catch (Exception e) {
                seq = commonMapper.getCaseSeq();
                log.error("redis生成赔案号异常！，message:{}", e.getMessage(), e);
            }
        } else {
            seq = commonMapper.getCaseSeq();
        }
        StringBuilder sb = new StringBuilder();
        int diff = 11 - seq.length();
        for (int i = 0; i < diff; i++) {
            sb.append("0");
        }
        return sb.toString() + seq;
    }


    @Override
    public String generateNo(String noType, VoucherTypeEnum voucherTypeEnum, String departmentCode) {
        String generateNo = "";
        if ("zking".equals(env)) {
            RequestNoDTO requestNoDTO = new RequestNoDTO();
            requestNoDTO.setComCode(departmentCode);
            requestNoDTO.setVoucherType(voucherTypeEnum);
            requestNoDTO.setRiskCode(BaseConstant.STRING_0000);
            ResponseNoDTO generate = GenerateVoucherUtils.generate(requestNoDTO);
            if ("9999".equals(generate.getRtnCode())) {
                generate = GenerateVoucherUtils.generate(requestNoDTO);
                if ("9999".equals(generate.getRtnCode())) {
                    generate = GenerateVoucherUtils.generate(requestNoDTO);
                    if ("9999".equals(generate.getRtnCode())) {
                        throw new GlobalBusinessException("取号器异常!!!");
                    }
                }
            }
            generateNo = generate.getBillNo();
        } else {
            switch (noType) {
                case NoConstants.BEATCH_REPORT_NO:
                    String timestamp = RapeDateUtil.parseToFormatString(new Date(), "yyyyMMddHHmmssSSS"); // 17位
                    String randomPart = UuidUtil.getUUID().substring(0, 6);// 6位
                    generateNo = timestamp + randomPart; // 23位
                    // generateNo = RapeDateUtil.parseToFormatString(new Date(), "yyyyMMddHHmmssSSS");
                    break;
                case NoConstants.REPORT_NO: //报案号
                    generateNo = generateReportNoNew(departmentCode, Constants.OFFLINE_FLAG);
                    break;
                case NoConstants.CASE_NO: //赔案号
                    generateNo = generateCaseNoNew(departmentCode, Constants.OFFLINE_FLAG);
                    break;
                case NoConstants.REGIST_NO:  //立案号
                    generateNo = "5" + UuidUtil.getUUID().substring(0, 19);
                    break;
                case NoConstants.APPLY_NO://报案申请号
                    generateNo = generateApplyNo(departmentCode, Constants.OFFLINE_FLAG);
                    break;
                default:
                    generateNo = UuidUtil.getUUID();
                    break;
            }
        }
        return generateNo;
    }

    @Override
    public String generateNoOnline(String noType, VoucherTypeEnum voucherTypeEnum, String departmentCode) {
        String generateNo;
        switch (noType) {
            case NoConstants.BEATCH_REPORT_NO:
                generateNo = RapeDateUtil.parseToFormatString(new Date(), "yyyyMMddHHmmssSSS");
                break;
            case NoConstants.REPORT_NO: //报案号
                generateNo = generateReportNoNew(departmentCode,Constants.ONLINE_FLAG);
                break;
            case NoConstants.CASE_NO: //赔案号
                generateNo = generateCaseNoNew(departmentCode,Constants.ONLINE_FLAG);
                break;
            case NoConstants.REGIST_NO:  //立案号
                generateNo = "5" + UuidUtil.getUUID().substring(0, 19);
                break;
            default:
                generateNo = UuidUtil.getUUID();
                break;
        }
        return generateNo;
    }

    /**
     * 校验费用信息的关联责任
     * 校验费用关联责任的赔款金额不为0，除非整案赔款金额都为0，否则提示“责任xxx未赔付，请重新关联责任！”
     * reportNo 报案号
     * caseTimes 赔付次数
     * payDutyCodes 有赔款金额的责任编码集
     * */
    @Override
    public void checkFeeDutyCode(String reportNo, Integer caseTimes, List<String> payDutyCodes,String claimType) {
        if(payDutyCodes!=null && payDutyCodes.size()>0){
            //当payDutyCodes为空时，整案赔款金额为0，不校验
            //1.通过报案号reportNo+caseTimes 查询费用信息表获取关联责任集
            List<String> feeDutyCodes = feePayMapper.selectFeeDutyCodes(reportNo,caseTimes,claimType);
            //2.费用信息关联责任集过滤有赔款金额的责任编码集
            if(feeDutyCodes!=null && feeDutyCodes.size()>0){
                feeDutyCodes.removeAll(payDutyCodes);
            }
            //提示信息组装
            String msg = this.printMsg(feeDutyCodes,"责任{msg}未赔付，请重新关联责任！");
            if(ObjectUtil.isNotEmpty(msg)){
                throw new GlobalBusinessException(msg);
            }
        }
    }

    @Override
    public void checkFeedutyCodeToTax(String reportNo, List<String> invoceDutyCodes) {
        if(invoceDutyCodes!=null && invoceDutyCodes.size()>0){
            //获取费率大于0的责任编码
            List<String> taxDutyCodes = policyPlanMapper.selectTaxRateByDutyCode(reportNo,invoceDutyCodes);
            //将含税的责任过滤掉
            invoceDutyCodes.removeAll(taxDutyCodes);
            //提示信息组装
            String msg = this.printMsg(invoceDutyCodes,"责任{msg}为免税责任，请重新选择责任!");
            if(ObjectUtil.isNotEmpty(msg)){
                throw new GlobalBusinessException(msg);
            }
        }
    }

    private String printMsg(List<String> codeList, String text) {
        if(codeList!=null && codeList.size()>0){
            //去重
            codeList = codeList.stream().distinct().collect(Collectors.toList());
            String msg = "";
            for (int j = 0; j < codeList.size(); j++) {
                if(j==codeList.size()-1){
                    msg += codeList.get(j);
                }else{
                    msg += codeList.get(j)+"，";
                }
            }
            return text.replace("{msg}",msg);
        }
        return null;
    }

}
