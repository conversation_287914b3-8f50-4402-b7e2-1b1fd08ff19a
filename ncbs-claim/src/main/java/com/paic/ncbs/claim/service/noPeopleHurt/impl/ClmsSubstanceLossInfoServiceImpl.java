package com.paic.ncbs.claim.service.noPeopleHurt.impl;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSubstanceLossInfo;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsSubstanceLossInfoMapper;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsSubstanceLossInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 物质损失信息表(ClmsSubstanceLossInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:50
 */
@Service("clmsSubstanceLossInfoService")
public class ClmsSubstanceLossInfoServiceImpl implements ClmsSubstanceLossInfoService {
    @Resource
    private ClmsSubstanceLossInfoMapper clmsSubstanceLossInfoMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsSubstanceLossInfo queryById(String id) {
        return this.clmsSubstanceLossInfoMapper.queryById(id);
    }

    /**
     * 通过报案号查询数据
     *
     * @return 实例对象
     */
    @Override
    public List<ClmsSubstanceLossInfo> queryByReportNo(String reportNo, int caseTimes) {
        return this.clmsSubstanceLossInfoMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsSubstanceLossInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsSubstanceLossInfo insert(ClmsSubstanceLossInfo clmsSubstanceLossInfo) {
        clmsSubstanceLossInfo.setCreatedBy(WebServletContext.getUserId());
        clmsSubstanceLossInfo.setCreatedDate(new Date());
        clmsSubstanceLossInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsSubstanceLossInfo.setUpdatedDate(new Date());
        clmsSubstanceLossInfo.setId(UuidUtil.getUUID());
        this.clmsSubstanceLossInfoMapper.insert(clmsSubstanceLossInfo);
        return clmsSubstanceLossInfo;
    }

    /**
     * 新增多条数据
     *
     * @param substanceLossInfoList 实例对象
     * @return 实例对象
     */
    @Override
    public void insertBatch(List<ClmsSubstanceLossInfo> substanceLossInfoList) {
        if (CollectionUtils.isEmpty(substanceLossInfoList)) {
            return;
        }
        for (int i = 0; i < substanceLossInfoList.size(); i++) {
            ClmsSubstanceLossInfo substanceLossInfo = substanceLossInfoList.get(i);
            substanceLossInfo.setSerialNo(i + 1);
            substanceLossInfo.setCreatedBy(WebServletContext.getUserId());
            substanceLossInfo.setCreatedDate(new Date());
            substanceLossInfo.setUpdatedBy(WebServletContext.getUserId());
            substanceLossInfo.setUpdatedDate(new Date());
            substanceLossInfo.setId(UuidUtil.getUUID());
        }
        this.clmsSubstanceLossInfoMapper.insertBatch(substanceLossInfoList);
    }

    /**
     * 修改数据
     *
     * @param clmsSubstanceLossInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsSubstanceLossInfo update(ClmsSubstanceLossInfo clmsSubstanceLossInfo) {
        clmsSubstanceLossInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsSubstanceLossInfo.setUpdatedDate(new Date());
        this.clmsSubstanceLossInfoMapper.update(clmsSubstanceLossInfo);
        return this.queryById(clmsSubstanceLossInfo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsSubstanceLossInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByCondition(String reportNo, int caseTimes) {
        clmsSubstanceLossInfoMapper.deleteByCondition(reportNo, caseTimes);
    }
}
