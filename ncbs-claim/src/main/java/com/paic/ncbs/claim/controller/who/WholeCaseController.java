package com.paic.ncbs.claim.controller.who;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.model.dto.report.GetReportBaseInfoSearchVO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseTimeVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyHistoryRequestVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyHistoryVO;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.risk.RiskService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "整案信息")
@RestController
@RequestMapping("/who/app/wholeCaseAction")
@Component("whoWholeCaseAction")
@Slf4j
public class WholeCaseController extends BaseController {

    @Autowired
    private WholeCaseService wholeCaseService;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private RiskService riskService;

    @ApiOperation("查询保单赔付的历史信息")
    @GetMapping(value = "/getWholeCaseTime/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<WholeCaseTimeVO> getWholeCaseTime(@PathVariable("reportNo") String reportNo,
                                                            @PathVariable("caseTimes") Integer caseTimes)
            throws GlobalBusinessException {
        LogUtil.audit("#根据保单和客户号查询保单赔付的历史信息，人参reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        return ResponseResult.success(wholeCaseService.getWholeCaseTime(reportNo, caseTimes));
    }

    @ApiOperation("获取报案信息")
    @PostMapping(value = "/getReportBaseInfo")
    public ResponseResult<ReportBaseInfoResData> getReportBaseInfo(@RequestBody GetReportBaseInfoSearchVO getReportBaseInfoSearchVO) throws GlobalBusinessException {
        log.info("获取报案信息-getReportBaseInfo-入参:{}", JSON.toJSONString(getReportBaseInfoSearchVO));
        return ResponseResult.success(wholeCaseService.getReportBaseInfo(getReportBaseInfoSearchVO.getReportNo(),
                getReportBaseInfoSearchVO.getCaseTimes()));
    }

    @ApiOperation(value = "获取整案信息")
    @GetMapping(value = "/getWholeCaseBase")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query"),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", required = true, dataType = "Integer", dataTypeClass=Integer.class, paramType = "Query")
    })
    public ResponseResult<WholeCaseBaseDTO> getWholeCaseBase(@RequestParam String reportNo, @RequestParam Integer caseTimes) {
        WholeCaseBaseDTO wholeCaseBase = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);

        return ResponseResult.success(wholeCaseBase);
    }

    @ApiOperation(value = "获取客户家财险历史出险记录")
    @GetMapping(value = "/getCustomerAccidentRecord")
    public ResponseResult getCustomerAccidentRecord(@ApiParam("报案号") @RequestParam String reportNo) {
        LogUtil.audit("#获取客户家财险历史出险记录#入参reportNo=%s", reportNo);
        Map<String, Object> resultMap = new HashMap<>();
        String clientNo = riskService.getClientNoByReportNo(reportNo);
        if (StringUtils.isEmptyStr(clientNo)) {
            return ResponseResult.success(resultMap);
        }
        LogUtil.audit("#客户号#clientNo=%s", clientNo);
        Integer accidentCount = wholeCaseService.getCustomerAccidentCount(clientNo);
        resultMap.put("clinetNo", clientNo);
        resultMap.put("accidentCount", accidentCount);
        return ResponseResult.success(resultMap);
    }

    @ApiOperation(value = "查询保单赔付的历史信息")
    @RequestMapping(value = "/getPolicyHistory", method = RequestMethod.POST)
    public PolicyHistoryVO getPolicyHistory(@RequestBody PolicyHistoryRequestVO policyHistoryVO) throws GlobalBusinessException {
        LogUtil.audit("#查询保单赔付的历史信息，人参policyHistoryVO={}", policyHistoryVO);
        return wholeCaseService.getPolicyHistory(policyHistoryVO);
    }

    @ApiOperation(value = "超时清单下载")
    @RequestMapping(value = "/exportExpiredCase", method = RequestMethod.POST)
    public PolicyHistoryVO exportExpiredCase (@ApiParam("系统来源") @RequestParam String requestType) throws GlobalBusinessException {
        LogUtil.audit("#超时清单下载，requestType={}", requestType);
        return null;
    }

}
