package com.paic.ncbs.claim.service.investigate;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.investigate.OffSiteInvestigateVO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;

public interface InvestigateAuditService {


    void addInvestigateAudit(InvestigateAuditDTO investigateAudit)throws GlobalBusinessException;

	void addOffSiteInvestigate(OffSiteInvestigateVO offSiteInvestigateVO, UserInfoDTO u) throws GlobalBusinessException;

}