package com.paic.ncbs.claim.model.dto.investigate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskQueryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "调查查询统一参数")
@Data
public class InvestigateQueryDTO {

    @ApiModelProperty(value = "请求类型", required = true)
    private String requestType;

    @ApiModelProperty(value = "请求ID")
    private String requestId;

    @ApiModelProperty(value = "请求时间")
    private String requestTime;

    @ApiModelProperty(value = "公司ID")
    private String companyId;

    @ApiModelProperty(value = "请求数据")
    private FileInfoDTO fileInfoDTO;

    @ApiModelProperty(value = "请求数据")
    private HttpServletRequest request;

    @ApiModelProperty(value = "请求数据")
    private RequestData requestData;

    @ApiModel("请求数据")
    @Data
    public static class RequestData {
        // 原有字段映射到requestData中
        @ApiModelProperty(value = "接口名称", required = true)
        private String interfaceName;

        @ApiModelProperty(value = "报案号", required = true)
        private String reportNo;

        @ApiModelProperty(value = "赔付次数", required = true)
        private Integer caseTimes;

        @ApiModelProperty(value = "用户code")
        private String userCode;
        @ApiModelProperty(value = "用户名称")
        private String userName;
        @ApiModelProperty(value = "用户机构code")
        private String departmentCode;
        @ApiModelProperty(value = "用户机构Name")
        private String departmentName;
        @ApiModelProperty("公估公司服务代码")
        private String serverCode;
        @ApiModelProperty("公估公司名称")
        private String serverName;

        @ApiModelProperty("Y-暂存 N-提交")
        private String isStorage;

        @ApiModelProperty(value = "系统来源")
        private String systemSource;
        
        @ApiModelProperty(value = "客户代码")
        private String clientCode;
        
        @ApiModelProperty(value = "产品代码")
        private String productCode;
        
        @ApiModelProperty(value = "方案代码")
        private String schemeCode;
        
        @ApiModelProperty(value = "TPA代码")
        private String tpaCode;
        
        @ApiModelProperty(value = "处理方式")
        private String dealWay;
        
        @ApiModelProperty(value = "案件标识")
        private String caseFlag;
        
        @ApiModelProperty(value = "发票明细标识")
        private String invoiceDetailFlag;
        
        @ApiModelProperty(value = "删除标识")
        private String deleted;

        @ApiModelProperty(value = "页码")
        private Integer pageIndex = 1;

        @ApiModelProperty(value = "每页行数")
        private Integer pageRows = 10;

        @ApiModelProperty(value = "每页条数")
        private Integer perPageSize = 10;

        @ApiModelProperty(value = "当前页码")
        private Integer currentPage = 1;

        // 文件下载查询参数
        @ApiModelProperty(value = "文件ID")
        private String fileId;

        @ApiModelProperty(value = "文件名")
        private String fileName;

        // 调查详情查询参数
        @ApiModelProperty(value = "调查ID")
        private String investigateId;

        @ApiModelProperty(value = "调查任务ID")
        private String investigateTaskId;

        // 电子保单下载查询参数
        @ApiModelProperty(value = "保单号")
        private String policyNo;

        // 工作台任务查询参数
        @ApiModelProperty("工作台任务查询条件")
        private WorkBenchTaskQueryVO workBenchTaskQuery;

        // 文件信息查询参数
        @ApiModelProperty("文件信息查询条件")
        private FileInfoVO fileInfo;

        // 文件上传信息查询参数
        @ApiModelProperty("文件上传信息查询条件")
        private FileInfoDTO fileUploadInfo;

        // 调查任务审核参数
        @ApiModelProperty("调查任务审核信息")
        private InvestigateTaskAuditPublicDTO taskAuditInfo;

        // 整案查询参数
        @ApiModelProperty("整案查询条件")
        private WholeCaseVO wholeCaseQuery;

        // 调查任务表VO
        @ApiModelProperty("调查任务表VO")
        private InvestigateTaskDTO investigateTaskDTO;

        // 调查任务表VO
        @ApiModelProperty("调查经过信息List")
        private List<InvestigateProcessDTO> investigateProcessVOs;

        // 调查经过信息DTO
        @ApiModelProperty("调查经过信息DTO")
        private InvestigateProcessDTO investigateProcessDTO;

        // 调查经过信息JSON DTO
        @ApiModelProperty("调查经过信息JSON DTO")
        private List<InvestigateProcessJsonDTO> investigateProcessJsonDTOs;

        // 调查经过ID
        @ApiModelProperty("调查经过ID")
        private String idAhcsInvestigateProcess;

        // 参数类型码数组
        @ApiModelProperty("参数类型码数组")
        private String collectionCodeList;

        @ApiModelProperty("Json")
        private Object jsonStr;

    }
}