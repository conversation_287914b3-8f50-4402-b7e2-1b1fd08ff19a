package com.paic.ncbs.claim.service.indicator.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.paic.ncbs.claim.common.enums.CaseIndicatorEnum;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.entity.indicator.CaseIndicatorEntity;
import com.paic.ncbs.claim.dao.mapper.indicator.CaseIndicatorMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.indicator.*;
import com.paic.ncbs.claim.service.indicator.CaseIndicatorService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * 案件指标统计service
 * @author: justinwu
 * @create 2025/3/7 10:19
 */
@Service("caseIndicatorService")
public class CaseIndicatorServiceImpl extends ServiceImpl<CaseIndicatorMapper, CaseIndicatorEntity> implements CaseIndicatorService {
    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_ROW=2000;
    public static final String UN_REGISTRATION="UN-REGISTRATION";
    public static final String REGISTRATION="REGISTRATION";

    @Override
    public void settleDeadlineForUnsettle(CaseIndicatorDTO caseIndicatorDTO) throws ParseException {
        // 构建查询DTO
        CaseIndicatorQueryDTO caseIndicatorQueryDTO = this.covertCaseIndicatorQueryDTO(caseIndicatorDTO);
        // 未结案
        processUnSettleCase(caseIndicatorQueryDTO);
        PageMethod.clearPage();
    }

    public void settleDeadlineForSettle(CaseIndicatorDTO caseIndicatorDTO) throws ParseException {
        // 构建查询DTO
        CaseIndicatorQueryDTO caseIndicatorQueryDTO = this.covertCaseIndicatorQueryDTO(caseIndicatorDTO);
        // 已结案，结案时效不存在的
        processSettleCase(caseIndicatorQueryDTO);
        PageMethod.clearPage();
    }

    @Override
    public void regDeadlineForUnReg(CaseIndicatorDTO caseIndicatorDTO) throws ParseException {
        // 构建查询DTO
        CaseIndicatorQueryDTO caseIndicatorQueryDTO = this.covertCaseIndicatorQueryDTO(caseIndicatorDTO);
        // 未立案
        processRegistrationCase(caseIndicatorQueryDTO,UN_REGISTRATION);
        PageMethod.clearPage();
    }

    @Override
    public void regDeadlineForReg(CaseIndicatorDTO caseIndicatorDTO) throws ParseException {
        // 构建查询DTO
        CaseIndicatorQueryDTO caseIndicatorQueryDTO = this.covertCaseIndicatorQueryDTO(caseIndicatorDTO);
        // 已立案
        processRegistrationCase(caseIndicatorQueryDTO,REGISTRATION);
        PageMethod.clearPage();
    }

    @Override
    public void reopenDeadline(CaseIndicatorDTO caseIndicatorDTO) throws ParseException {
        // 构建查询DTO
        CaseIndicatorQueryDTO caseIndicatorQueryDTO = this.covertCaseIndicatorQueryDTO(caseIndicatorDTO);
        // 重开案件
        processReopenCase(caseIndicatorQueryDTO);
        PageMethod.clearPage();
    }

    @Override
    public void calCaseRegIndicator(String reportNo,Integer caseTimes) {
        CaseIndicatorQueryDTO caseIndicatorQueryDTO = new CaseIndicatorQueryDTO();
        caseIndicatorQueryDTO.setReportNo(reportNo);
        caseIndicatorQueryDTO.setCaseTimes(caseTimes);
        caseIndicatorQueryDTO.setNowTime(new Date());
        List<RegistrationIndicatorDTO> regCases = this.getBaseMapper().getRegCaseList(caseIndicatorQueryDTO);
        if(regCases!=null&&regCases.size()>0) {
            this.saveCaseRegIndicator(regCases,caseIndicatorQueryDTO.getNowTime());
        }
    }

    @Override
    public void calCaseSettleIndicator(String reportNo,Integer caseTimes) {
        CaseIndicatorQueryDTO caseIndicatorQueryDTO = new CaseIndicatorQueryDTO();
        caseIndicatorQueryDTO.setReportNo(reportNo);
        caseIndicatorQueryDTO.setCaseTimes(caseTimes);
        caseIndicatorQueryDTO.setNowTime(new Date());
        if(caseTimes > 1) {
            // 重开结案时间
            List<ReopenIndicatorDTO> reopenCases = this.getBaseMapper().getReopenCaseList(caseIndicatorQueryDTO);
            if(reopenCases!=null&&reopenCases.size()>0) {
                this.saveCaseReopenIndicator(reopenCases,caseIndicatorQueryDTO.getNowTime());
            }
        }else {
            // 首次结案时间
            List<SettleIndicatorDTO> settleCases = this.getBaseMapper().getSettleCaseList(caseIndicatorQueryDTO);
            if(settleCases!=null&&settleCases.size()>0) {
                this.saveCaseSettleIndicator(settleCases,caseIndicatorQueryDTO.getNowTime());
            }
        }
    }

    /**
     * 构建查询DTO
     * @param caseIndicatorDTO
     * @return
     */
    private CaseIndicatorQueryDTO covertCaseIndicatorQueryDTO(CaseIndicatorDTO caseIndicatorDTO) throws ParseException {
        // 报案开始时间格式检验
        Date sReportBeginDate = null;
        try {
            sReportBeginDate = DateUtils.parseToFormatDate(caseIndicatorDTO.getReportBeginDate(),DateUtils.DATE_FORMAT_YYYYMMDD);
        } catch (ParseException e) {
            throw new GlobalBusinessException("报案开始时间格式不对！");
        }
        // 报案结束时间格式检验
        Date sReportEndDate = null;
        try {
            sReportEndDate = DateUtils.parseToFormatDate(caseIndicatorDTO.getReportEndDate(),DateUtils.DATE_FORMAT_YYYYMMDD);
        } catch (ParseException e) {
            throw new GlobalBusinessException("报案结束时间格式不对！");
        }
        // 立案开始时间格式检验
        Date sRegisterBeginDate = null;
        try {
            sRegisterBeginDate = DateUtils.parseToFormatDate(caseIndicatorDTO.getRegisterBeginDate(),DateUtils.DATE_FORMAT_YYYYMMDD);
        } catch (ParseException e) {
            throw new GlobalBusinessException("立案开始时间格式不对！");
        }
        // 立案结束时间格式检验
        Date sRegisterEndDate = null;
        try {
            sRegisterEndDate = DateUtils.parseToFormatDate(caseIndicatorDTO.getRegisterEndDate(),DateUtils.DATE_FORMAT_YYYYMMDD);
        } catch (ParseException e) {
            throw new GlobalBusinessException("立案结束时间格式不对！");
        }
        // 结案开始时间格式检验
        Date sEndCaseBeginDate = null;
        try {
            sEndCaseBeginDate = DateUtils.parseToFormatDate(caseIndicatorDTO.getEndCaseBeginDate(),DateUtils.DATE_FORMAT_YYYYMMDD);
        } catch (ParseException e) {
            throw new GlobalBusinessException("结案开始时间格式不对！");
        }
        // 结案结束时间格式检验
        Date sEndCaseEndDate = null;
        try {
            sEndCaseEndDate = DateUtils.parseToFormatDate(caseIndicatorDTO.getEndCaseEndDate(),DateUtils.DATE_FORMAT_YYYYMMDD);
        } catch (ParseException e) {
            throw new GlobalBusinessException("结案结束时间格式不对！");
        }
        CaseIndicatorQueryDTO caseIndicatorQueryDTO = new CaseIndicatorQueryDTO();
        BeanUtils.copyProperties(caseIndicatorDTO,caseIndicatorQueryDTO);
        // 当前时间
        Date nowDate = new Date();
        String nowDateStr = DateUtils.parseToFormatString(nowDate,DateUtils.FULL_DATE_STR);
        caseIndicatorQueryDTO.setNowDate(nowDateStr);
        caseIndicatorQueryDTO.setNowTime(nowDate);
        // 查询参数特殊处理
        Date beginOfNowDate = DateUtils.beginOfDay(nowDate);
        // 报案开始时间
        String sReportBeginDateStr = null;
        if(sReportBeginDate != null) {
            sReportBeginDateStr = DateUtils.parseToFormatString(sReportBeginDate,DateUtils.FULL_DATE_STR);
        }
        caseIndicatorQueryDTO.setReportBeginDate(sReportBeginDateStr);
        // 报案结束时间
        String sReportEndDateStr = null;
        if(sReportEndDate != null) {
            sReportEndDateStr = DateUtils.parseToFormatString(sReportEndDate,DateUtils.FULL_DATE_STR);
        }
        caseIndicatorQueryDTO.setReportEndDate(sReportEndDateStr);
        // 立案开始时间
        String sRegisterBeginDateStr = null;
        if(sRegisterBeginDate != null) {
            sRegisterBeginDateStr = DateUtils.parseToFormatString(sRegisterBeginDate,DateUtils.FULL_DATE_STR);
        }else {
            sRegisterBeginDateStr = DateUtils.parseToFormatString(DateUtils.addDate(beginOfNowDate,-1),DateUtils.FULL_DATE_STR);
        }
        caseIndicatorQueryDTO.setRegisterBeginDate(sRegisterBeginDateStr);
        // 立案结束时间
        String sRegisterEndDateStr = null;
        if(sRegisterEndDate != null) {
            sRegisterEndDateStr = DateUtils.parseToFormatString(sRegisterEndDate,DateUtils.FULL_DATE_STR);
        }else {
            sRegisterEndDateStr = DateUtils.parseToFormatString(beginOfNowDate,DateUtils.FULL_DATE_STR);
        }
        caseIndicatorQueryDTO.setRegisterEndDate(sRegisterEndDateStr);
        // 结案开始时间
        String sEndCaseBeginDateStr = null;
        if(sEndCaseBeginDate != null) {
            sEndCaseBeginDateStr = DateUtils.parseToFormatString(sEndCaseBeginDate,DateUtils.FULL_DATE_STR);
        }else {
            sEndCaseBeginDateStr = DateUtils.parseToFormatString(DateUtils.addDate(beginOfNowDate,-1),DateUtils.FULL_DATE_STR);
        }
        caseIndicatorQueryDTO.setEndCaseBeginDate(sEndCaseBeginDateStr);
        // 结案结束时间
        String sEndCaseDateStr = null;
        if(sEndCaseEndDate != null) {
            sEndCaseDateStr = DateUtils.parseToFormatString(sEndCaseEndDate,DateUtils.FULL_DATE_STR);
        }else {
            sEndCaseDateStr = DateUtils.parseToFormatString(beginOfNowDate,DateUtils.FULL_DATE_STR);
        }
        caseIndicatorQueryDTO.setEndCaseEndDate(sEndCaseDateStr);
        // 处理条数
        if(caseIndicatorDTO.getProcessNum() == null){
            caseIndicatorQueryDTO.setProcessNum(DEFAULT_PAGE_ROW);
        }
        return caseIndicatorQueryDTO;
    }

    /**
     *  未结案分页处理
     * @param caseIndicatorQueryDTO
     */
    private void processUnSettleCase(CaseIndicatorQueryDTO caseIndicatorQueryDTO) {
        Pager pager = new Pager();
        pager.setPageIndex(1);
        pager.setPageRows(caseIndicatorQueryDTO.getProcessNum());
        List<SettleIndicatorDTO> unSettleCases = null;
        PageInfo<SettleIndicatorDTO> pageInfo = null;
        while(true) {
            PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
            PageHelper.orderBy("reportDate desc,reportNo desc,caseTimes desc");
            unSettleCases = this.getBaseMapper().getUnSettleCaseList(caseIndicatorQueryDTO);
            if(unSettleCases!=null&&unSettleCases.size()>0) {
                saveCaseSettleIndicator(unSettleCases,caseIndicatorQueryDTO.getNowTime());
            }
            pageInfo = new PageInfo<>(unSettleCases);
            pager.setTotalRows((int) pageInfo.getTotal());
            if(!pager.getHasNextPage()) {
                break;
            }
            pager.setPageIndex(pager.getPageIndex()+1);
        }
    }

    /**
     *  已结案分页处理
     * @param caseIndicatorQueryDTO
     */
    private void processSettleCase(CaseIndicatorQueryDTO caseIndicatorQueryDTO) {
        Pager pager = new Pager();
        pager.setPageIndex(1);
        pager.setPageRows(caseIndicatorQueryDTO.getProcessNum());
        List<SettleIndicatorDTO> unSettleCases = null;
        PageInfo<SettleIndicatorDTO> pageInfo = null;
        while(true) {
            PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
            PageHelper.orderBy("endCaseDate desc,reportDate desc,reportNo desc,caseTimes desc");
            unSettleCases = this.getBaseMapper().getSettleCaseList(caseIndicatorQueryDTO);
            if(unSettleCases!=null&&unSettleCases.size()>0) {
                saveCaseSettleIndicator(unSettleCases,caseIndicatorQueryDTO.getNowTime());
            }
            pageInfo = new PageInfo<>(unSettleCases);
            pager.setTotalRows((int) pageInfo.getTotal());
            if(!pager.getHasNextPage()) {
                break;
            }
            pager.setPageIndex(pager.getPageIndex()+1);
        }
    }

    /**
     * 保存首次结案时效指标
     * @param cases 案件列表
     * @param nowDate 当前时间
     */
    private void saveCaseSettleIndicator(List<SettleIndicatorDTO> cases,Date nowDate) {
        for(SettleIndicatorDTO settleIndicatorDTO:cases) {
            CaseIndicatorEntity caseIndicatorEntity = new CaseIndicatorEntity();
            caseIndicatorEntity.setReportNo(settleIndicatorDTO.getReportNo());
            caseIndicatorEntity.setCaseTimes(settleIndicatorDTO.getCaseTimes());
            caseIndicatorEntity.setIndicatorCode(CaseIndicatorEnum.SETTLE_DEADLINE.getCode());
            caseIndicatorEntity.setIndicatorName(CaseIndicatorEnum.SETTLE_DEADLINE.getName());
            caseIndicatorEntity.setIndicatorUnit(CaseIndicatorEnum.SETTLE_DEADLINE.getUnit());
            caseIndicatorEntity.setIndicatorCal(CaseIndicatorEnum.SETTLE_DEADLINE.getCal());
            caseIndicatorEntity.setCreatedBy("system");
            caseIndicatorEntity.setUpdatedBy("system");
            caseIndicatorEntity.setSysCtime(new Date());
            caseIndicatorEntity.setSysUtime(new Date());
            caseIndicatorEntity.setDelFlag("0");
            // 计算时间
            caseIndicatorEntity.setCalTime(DateUtils.getPreciseTime(nowDate));
            // 指标值
            long secValue = (settleIndicatorDTO.getEndCaseDate().getTime()-settleIndicatorDTO.getReportDate().getTime())/1000;
            caseIndicatorEntity.setIndicatorValue(String.valueOf(secValue-settleIndicatorDTO.getSupplementSec()));
            // 计算描述
            String calDes = String.format("结案时间-报案时间-补材经过时间=%d-%d-%d",settleIndicatorDTO.getEndCaseDate().getTime()/1000,settleIndicatorDTO.getReportDate().getTime()/1000,settleIndicatorDTO.getSupplementSec());
            caseIndicatorEntity.setIndicatorCalDes(calDes);
            saveOrUpdateCaseIndicator(caseIndicatorEntity);
        }
    }

    /**
     * 保存修改指标数据
     * @param caseIndicatorEntity
     */
    private void saveOrUpdateCaseIndicator(CaseIndicatorEntity caseIndicatorEntity) {
        LambdaQueryWrapper<CaseIndicatorEntity> qryCaseIndicatorEntity = new LambdaQueryWrapper<>();
        qryCaseIndicatorEntity.eq(CaseIndicatorEntity::getDelFlag,caseIndicatorEntity.getDelFlag());
        qryCaseIndicatorEntity.eq(CaseIndicatorEntity::getReportNo,caseIndicatorEntity.getReportNo());
        qryCaseIndicatorEntity.eq(CaseIndicatorEntity::getCaseTimes,caseIndicatorEntity.getCaseTimes());
        qryCaseIndicatorEntity.eq(CaseIndicatorEntity::getIndicatorCode,caseIndicatorEntity.getIndicatorCode());
        CaseIndicatorEntity rCaseIndicatorEntity = this.getBaseMapper().selectOne(qryCaseIndicatorEntity);
        if(rCaseIndicatorEntity!=null) {
            caseIndicatorEntity.setId(rCaseIndicatorEntity.getId());
            caseIndicatorEntity.setCreatedBy(null);
            caseIndicatorEntity.setSysCtime(null);
            this.getBaseMapper().updateById(caseIndicatorEntity);
        }else {
            this.getBaseMapper().insert(caseIndicatorEntity);
        }
    }

    /**
     *  立案指标分页处理
     * @param caseIndicatorQueryDTO
     * @param regType
     */
    private void processRegistrationCase(CaseIndicatorQueryDTO caseIndicatorQueryDTO,String regType) {
        Pager pager = new Pager();
        pager.setPageIndex(1);
        pager.setPageRows(caseIndicatorQueryDTO.getProcessNum());
        List<RegistrationIndicatorDTO> regCases = null;
        PageInfo<RegistrationIndicatorDTO> pageInfo = null;
        while(true) {
            PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
            PageHelper.orderBy("registerDate desc,reportDate desc,reportNo desc,caseTimes desc");
            if(UN_REGISTRATION.equals(regType)) {
                regCases = this.getBaseMapper().getUnRegCaseList(caseIndicatorQueryDTO);
            }
            if(REGISTRATION.equals(regType)) {
                regCases = this.getBaseMapper().getRegCaseList(caseIndicatorQueryDTO);
            }
            if(regCases!=null&&regCases.size()>0) {
                saveCaseRegIndicator(regCases,caseIndicatorQueryDTO.getNowTime());
            }
            pageInfo = new PageInfo<>(regCases);
            pager.setTotalRows((int) pageInfo.getTotal());
            if(!pager.getHasNextPage()) {
                break;
            }
            pager.setPageIndex(pager.getPageIndex()+1);

        }
    }

    /**
     * 保存立案时效指标
     * @param cases 案件列表
     * @param nowDate 当前时间
     */
    private void saveCaseRegIndicator(List<RegistrationIndicatorDTO> cases,Date nowDate) {
        for(RegistrationIndicatorDTO regIndicatorDTO:cases) {
            CaseIndicatorEntity caseIndicatorEntity = new CaseIndicatorEntity();
            caseIndicatorEntity.setReportNo(regIndicatorDTO.getReportNo());
            caseIndicatorEntity.setCaseTimes(regIndicatorDTO.getCaseTimes());
            caseIndicatorEntity.setIndicatorCode(CaseIndicatorEnum.REGISTRATION_DEADLINE.getCode());
            caseIndicatorEntity.setIndicatorName(CaseIndicatorEnum.REGISTRATION_DEADLINE.getName());
            caseIndicatorEntity.setIndicatorUnit(CaseIndicatorEnum.REGISTRATION_DEADLINE.getUnit());
            caseIndicatorEntity.setIndicatorCal(CaseIndicatorEnum.REGISTRATION_DEADLINE.getCal());
            caseIndicatorEntity.setCreatedBy("system");
            caseIndicatorEntity.setUpdatedBy("system");
            caseIndicatorEntity.setSysCtime(new Date());
            caseIndicatorEntity.setSysUtime(new Date());
            caseIndicatorEntity.setDelFlag("0");
            // 计算时间
            caseIndicatorEntity.setCalTime(DateUtils.getPreciseTime(nowDate));
            // 指标值
            long secValue = (regIndicatorDTO.getRegisterDate().getTime()-regIndicatorDTO.getReportDate().getTime())/1000;
            caseIndicatorEntity.setIndicatorValue(String.valueOf(secValue));
            // 计算描述
            String calDes = String.format("立案时间-报案时间=%d-%d",regIndicatorDTO.getRegisterDate().getTime()/1000,regIndicatorDTO.getReportDate().getTime()/1000);
            caseIndicatorEntity.setIndicatorCalDes(calDes);
            saveOrUpdateCaseIndicator(caseIndicatorEntity);
        }
    }


    /**
     * 处理重开案件
     * @param caseIndicatorQueryDTO
     */
    private void processReopenCase(CaseIndicatorQueryDTO caseIndicatorQueryDTO) {
        Pager pager = new Pager();
        pager.setPageIndex(1);
        pager.setPageRows(caseIndicatorQueryDTO.getProcessNum());
        List<ReopenIndicatorDTO> reopenCases = null;
        PageInfo<ReopenIndicatorDTO> pageInfo = null;
        while(true) {
            PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
            PageHelper.orderBy("endCaseDate desc,reopenApproveDate desc,reportNo desc,caseTimes desc");
            reopenCases = this.getBaseMapper().getReopenCaseList(caseIndicatorQueryDTO);
            if(reopenCases!=null&&reopenCases.size()>0) {
                saveCaseReopenIndicator(reopenCases,caseIndicatorQueryDTO.getNowTime());
            }
            pageInfo = new PageInfo<>(reopenCases);
            pager.setTotalRows((int) pageInfo.getTotal());
            if(!pager.getHasNextPage()) {
                break;
            }
            pager.setPageIndex(pager.getPageIndex()+1);
        }
    }

    /**
     * 保存重开结案时效指标
     * @param cases 案件列表
     * @param nowDate 当前时间
     */
    private void saveCaseReopenIndicator(List<ReopenIndicatorDTO> cases,Date nowDate) {
        for(ReopenIndicatorDTO reopenIndicatorDTO:cases) {
            CaseIndicatorEntity caseIndicatorEntity = new CaseIndicatorEntity();
            caseIndicatorEntity.setReportNo(reopenIndicatorDTO.getReportNo());
            caseIndicatorEntity.setCaseTimes(reopenIndicatorDTO.getCaseTimes());
            caseIndicatorEntity.setIndicatorCode(CaseIndicatorEnum.REOPEN_SETTLE_DEADLINE.getCode());
            caseIndicatorEntity.setIndicatorName(CaseIndicatorEnum.REOPEN_SETTLE_DEADLINE.getName());
            caseIndicatorEntity.setIndicatorUnit(CaseIndicatorEnum.REOPEN_SETTLE_DEADLINE.getUnit());
            caseIndicatorEntity.setIndicatorCal(CaseIndicatorEnum.REOPEN_SETTLE_DEADLINE.getCal());
            caseIndicatorEntity.setCreatedBy("system");
            caseIndicatorEntity.setUpdatedBy("system");
            caseIndicatorEntity.setSysCtime(new Date());
            caseIndicatorEntity.setSysUtime(new Date());
            caseIndicatorEntity.setDelFlag("0");
            // 计算时间
            caseIndicatorEntity.setCalTime(DateUtils.getPreciseTime(nowDate));
            // 指标值
            long secValue = (reopenIndicatorDTO.getEndCaseDate().getTime()-reopenIndicatorDTO.getReopenApproveDate().getTime())/1000;
            caseIndicatorEntity.setIndicatorValue(String.valueOf(secValue-reopenIndicatorDTO.getSupplementSec()));
            // 计算描述
            String calDes = String.format("重开结案时间-重开审批通过时间-补材经过时间=%d-%d-%d",reopenIndicatorDTO.getEndCaseDate().getTime()/1000,reopenIndicatorDTO.getReopenApproveDate().getTime()/1000,reopenIndicatorDTO.getSupplementSec());
            caseIndicatorEntity.setIndicatorCalDes(calDes);
            saveOrUpdateCaseIndicator(caseIndicatorEntity);
        }
    }
}
