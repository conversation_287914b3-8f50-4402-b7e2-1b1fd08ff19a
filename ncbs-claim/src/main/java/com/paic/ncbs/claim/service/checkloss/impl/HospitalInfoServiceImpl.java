package com.paic.ncbs.claim.service.checkloss.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.checkloss.HospitalInfoMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.HospitalBatchImportMapper;
import com.paic.ncbs.claim.dao.mapper.other.HospitalAliasMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fileupload.HospitalBatchImportDTO;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.other.HospitalAliasInfoDTO;
import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.model.vo.fileupolad.HospitalImportResultVO;
import com.paic.ncbs.claim.model.vo.fileupolad.HospitalInfoImportFailVO;
import com.paic.ncbs.claim.model.vo.fileupolad.HospitalInfoImportVO;
import com.paic.ncbs.claim.model.vo.other.HospitalAliasInfoVO;
import com.paic.ncbs.claim.service.checkloss.HospitalInfoService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.utils.ExcelListener;
import com.paic.ncbs.claim.utils.ExcelUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.LogUtil.log;

@Service("HospitalInfoService")
public class HospitalInfoServiceImpl implements HospitalInfoService {

    @Autowired
    private HospitalInfoMapper hospitalInfoDao;
    @Autowired
    private CommonParameterService commonService;
    @Autowired
    private HospitalBatchImportMapper batchImportMapper;
    @Autowired
    private HospitalAliasMapper hospitalAliasMapper;


    @Override
    public List<HospitalInfoVO> getValidHospitalList(String hospitalName,String reportNo) {

        String orgType = getOrgTypeByReportNo(reportNo, NcbsConstant.HOSPITAL);

        return hospitalInfoDao.getHospitalList(hospitalName,orgType);
    }

    /**
     * 根据报案号查询所属机构，判断医院编码类型 1-全国，2-北京，3-上海，监管上报需要
     * @param reportNo
     * @return
     */
    @Override
    public String getOrgTypeByReportNo(String reportNo,String checkType) {
        //默认全国
        String orgType = NcbsConstant.ORG_TYPE_ONE;
        //根据报案号查询机构编码
//        String acceptDepartmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);
//        if(StringUtils.isEmptyStr(acceptDepartmentCode)){
//            throw new GlobalBusinessException("根据报案号查询机构编码为空，请重试");
//        }
        //根据机构编码确认机构是全国、北京(211)还是上海(231)
//        if(acceptDepartmentCode.startsWith(NcbsConstant.BEIJING)){
//            orgType = NcbsConstant.ORG_TYPE_TWO;
//        }
        //hospital 医疗机构有全国/北京/上海三套编码，diagnose 诊断 有全国/北京两套编码，operation 手术 有全国/北京两套编码
//        if(acceptDepartmentCode.startsWith(NcbsConstant.SHANGHAI) && NcbsConstant.HOSPITAL.equals(checkType)){
//            orgType = NcbsConstant.ORG_TYPE_THREE;
//        }
        return orgType;
    }

    @Override
    public HospitalInfoVO getHospitalByCode(String hospitalCode) {
        return hospitalInfoDao.getHospitalByCode(hospitalCode);
    }

    @Override
    @Transactional
    public HospitalInfoVO saveHospitalInfo(HospitalInfoVO hospitalInfoVO) {
        if (null != hospitalInfoVO) {
            if (StringUtils.isBlank(hospitalInfoVO.getHospitalName())) {
                throw new GlobalBusinessException("医院名称不能为空！");
            }
            if (StringUtils.isBlank(hospitalInfoVO.getGrade())) {
                throw new GlobalBusinessException("医院等级不能为空！");
            }
            if (StringUtils.isBlank(hospitalInfoVO.getHospitalPropertyDes())) {
                throw new GlobalBusinessException("医院性质不能为空！");
            }
           /* if (StringUtils.isBlank(hospitalInfoVO.getHospitalPropertyDes())) {
                hospitalInfoVO.setHospitalCode("9999999");
            }*/
            HospitalInfoVO hospitalInfoConditionVO = new HospitalInfoVO();
            hospitalInfoConditionVO.setHospitalName(hospitalInfoVO.getHospitalName());
            List<HospitalInfoVO> hospitalInfoVOList = hospitalInfoDao.getHospitalInfoList(hospitalInfoConditionVO);
            if (null != hospitalInfoVOList && hospitalInfoVOList.size() > 0) {
                for (HospitalInfoVO hospitalInfoResVO : hospitalInfoVOList) {
                    if (hospitalInfoResVO.getHospitalName().equals(hospitalInfoVO.getHospitalName())
                            && (StringUtils.isBlank(hospitalInfoVO.getIdHospitalInfo()) || !hospitalInfoVO.getIdHospitalInfo().equals(hospitalInfoResVO.getIdHospitalInfo()))) {
                        throw new GlobalBusinessException("已存在相同医院名称的医院信息，请检查！");
                    }
                }
            }
            if (StringUtils.isNotBlank(hospitalInfoVO.getIdHospitalInfo())) {
                hospitalInfoVO.setOrgType("1");
                hospitalInfoVO.setUpdatedBy(WebServletContext.getUser().getUserCode());
                hospitalInfoDao.updateHospitalInfo(hospitalInfoVO);
            } else {
                //医院编码
                String hospitalCode = "";
                Integer count = hospitalInfoDao.getHospitalInfoCount(null) + 1;
                if (count <9) {
                    hospitalCode = "000000" +count;
                } else if (count <99){
                    hospitalCode = "00000" +count;
                } else if (count <999){
                    hospitalCode = "0000" +count;
                } else if (count <9999){
                    hospitalCode = "000" +count;
                }  else if (count <99999){
                    hospitalCode = "00" +count;
                }  else {
                    hospitalCode = "00" +count;
                }
                hospitalInfoVO.setIdHospitalInfo(UuidUtil.getUUID());
                hospitalInfoVO.setHospitalCode(hospitalCode);
                hospitalInfoVO.setOrgType("1");
                hospitalInfoVO.setCreatedBy(WebServletContext.getUser().getUserCode());
                hospitalInfoVO.setUpdatedBy(WebServletContext.getUser().getUserCode());
                hospitalInfoDao.saveHospitalInfo(hospitalInfoVO);
            }
            hospitalInfoVO = hospitalInfoDao.getHospitalByKey(hospitalInfoVO.getIdHospitalInfo());
        } else {
            throw new GlobalBusinessException("保存信息不能为空，请检查！");
        }
        return hospitalInfoVO;
    }


    /**
     * 保存医院别名信息
     */
    @Override
    public HospitalAliasInfoVO batchAddHospitalAlias(List<HospitalAliasInfoDTO> dtoList) {
        HospitalAliasInfoVO hospitalAliasInfoVO =  new HospitalAliasInfoVO();
        if (!dtoList.isEmpty() || dtoList.size() > 0) {
            for(HospitalAliasInfoDTO dto : dtoList) {
                if (StringUtils.isBlank(dto.getHospitalAlias())) {
                    throw new GlobalBusinessException("医院别名不能为空！");
                }
                if (StringUtils.isBlank(dto.getHospitalId())) {
                    throw new GlobalBusinessException("医院表id不能为空！");
                }
            }
            //查询已存在的别名
            List<Map<String, String>> existingList = hospitalAliasMapper.selectExistingAliases(dtoList);
            //转换为 Set（存储 "hospitalId:hospitalAlias" 格式）
            Set<String> existingKeys = existingList.stream()
                            .map(map -> map.get("hospitalId") + ":" + map.get("hospitalAlias"))
                            .collect(Collectors.toSet());
            //过滤出需要新增的记录（不在已存在集合中的）
            List<HospitalAliasInfoDTO> toInsertList = dtoList.stream()
                            .filter(alias -> {
                                String key = alias.getHospitalId() + ":" + alias.getHospitalAlias();
                                return !existingKeys.contains(key);
                            })
                            .collect(Collectors.toList());
            if(!toInsertList.isEmpty() || toInsertList.size() > 0){
                //构建新增数据
                buildHospitalAliasInfo(toInsertList);
                try {
                    hospitalAliasMapper.batchAddHospitalAlias(toInsertList);
                } catch (Exception e) {
                    log.error("保存医院别名信息提交失败：" , e);
                    throw new GlobalBusinessException("保存医院别名信息提交失败");
                }
            }
        }
        return hospitalAliasInfoVO;
    }

    public void buildHospitalAliasInfo(List<HospitalAliasInfoDTO> dtoList){
        for(HospitalAliasInfoDTO dto: dtoList) {
            dto.setCreatedBy(WebServletContext.getUser().getUserCode());
            dto.setUpdatedBy(WebServletContext.getUser().getUserCode());
            dto.setDeleteFlag("0");
            dto.setDataSource("1");//1-理赔页面录入
        }
    }

    /**
     * 查询医院别名信息
     */
    @Override
    public List<HospitalAliasInfoVO> getHospitalAliasList(HospitalAliasInfoDTO hospitalAliasInfoDTO) {
        List<HospitalAliasInfoVO> resultList =  new ArrayList<>();
        if (null != hospitalAliasInfoDTO) {
            if (StringUtils.isBlank(hospitalAliasInfoDTO.getHospitalId())) {
                throw new GlobalBusinessException("医院表id不能为空！");
            }
            try {
                resultList = hospitalAliasMapper.getHospitalAliasList(hospitalAliasInfoDTO);
            } catch (Exception e) {
                log.error("查询医院别名信息失败：" , e);
                throw new GlobalBusinessException("查询医院别名信息失败");
            }
        } else {
            throw new GlobalBusinessException("查询医院别名信息不能为空，请检查！");
        }
        return resultList;
    }


    /**
     * 删除医院别名信息
     * @param hospitalAliasInfoDTO
     * @return
     */
    @Override
    public HospitalAliasInfoVO deleteHospitalInfo(HospitalAliasInfoDTO hospitalAliasInfoDTO) {
        HospitalAliasInfoVO hospitalAliasInfoVO = new HospitalAliasInfoVO();
        if(hospitalAliasInfoDTO.getId() == null){
            throw new GlobalBusinessException("医院别名信息表id不能为空！");
        }
        Long id = hospitalAliasInfoDTO.getId();
        hospitalAliasInfoVO.setId(id);
        try {
            //构建删除医院别名信息DTO
            buildHospitalAliasInfoDTO(hospitalAliasInfoDTO);
            //将别名信息置为失效 delete_flag = '1'
            hospitalAliasMapper.deleteHospitalInfo(hospitalAliasInfoDTO);
        } catch (Exception e) {
            log.error("查询医院别名信息失败：" , e);
            throw new GlobalBusinessException("查询医院别名信息失败");
        }
        return hospitalAliasInfoVO;
    }


    /**
     * 构建删除医院别名信息DTO
     * @param hospitalAliasInfoDTO
     * @return
     */
    public void buildHospitalAliasInfoDTO (HospitalAliasInfoDTO hospitalAliasInfoDTO){
        hospitalAliasInfoDTO.setUpdatedBy(WebServletContext.getUser().getUserCode());
        hospitalAliasInfoDTO.setDeleteFlag("1");
    }

    @Override
    public HospitalInfoVO deleteHospitalInfo(String idHospitalInfo) {
        HospitalInfoVO hospitalInfoVO = hospitalInfoDao.getHospitalByKey(idHospitalInfo);
        if (null != hospitalInfoVO) {
            hospitalInfoVO.setIsValid("N");
            hospitalInfoVO.setOrgType("1");
            hospitalInfoVO.setUpdatedBy(WebServletContext.getUser().getUserCode());
            hospitalInfoDao.updateHospitalInfo(hospitalInfoVO);
        } else {
            throw new GlobalBusinessException("未查询到ID为"+idHospitalInfo+"的医信息！");
        }
        return hospitalInfoVO;
    }

    @Override
    public List<HospitalInfoVO> getHospitalInfoList(@RequestBody HospitalInfoVO hospitalInfoVO){
        List<HospitalInfoVO> list = hospitalInfoDao.getHospitalInfoList(hospitalInfoVO);
        return list;
    }


    @Override
    @Transactional
    public HospitalImportResultVO importHospitalInfo(MultipartFile file) {
        InputStream inputStream = null;
        try {
            String batchNo = UUID.randomUUID().toString().replace("-", "");
            String[] collectionCodeList = {"HOSPITAL_TYPE", "HOSPITAL_GRADE"};
            Map<String, List<CommonParameterTinyDTO>> commonParams = commonService.getDataDictList(collectionCodeList);
            List<String> hospitalGradeList = commonParams.get("HOSPITAL_GRADE").stream().map(CommonParameterTinyDTO::getValueChineseName).collect(Collectors.toList());
            List<String> hospitalTypeList = commonParams.get("HOSPITAL_TYPE").stream().map(CommonParameterTinyDTO::getValueChineseName).collect(Collectors.toList());
            EasyExcel excel = new EasyExcel();
            inputStream = file.getInputStream();
            ExcelListener listener = new ExcelListener();
            ExcelReader excelReader = EasyExcel.read(inputStream, listener).excelType(ExcelTypeEnum.XLSX).build();
            // 读取数据
            ReadSheet sheet0 = EasyExcel.readSheet(0).head(HospitalInfoImportVO.class).build();// 第一个sheet读取类型
            // 开始读取第一个sheet数据
            excelReader.read(sheet0);
            List<Object> sheetData2 = listener.getDatas();
            JSONArray jsonArray2 = JSONUtil.parseArray(sheetData2);
            List<HospitalInfoImportVO> hospitalInfoUploadList = JSONUtil.toList(jsonArray2, HospitalInfoImportVO.class);
            listener.getDatas().clear();// 清空上次读取数据
            int successCount = 0;
            int failCount = 0;

            //医院编码
            String hospitalCode = "";
            Integer count = hospitalInfoDao.getHospitalInfoCount(null) + 1;
            for (HospitalInfoImportVO hospitalInfoExportVO : hospitalInfoUploadList) {
                StringBuilder failReason = new StringBuilder();
                HospitalInfoVO hospitalInfoVO = new HospitalInfoVO();
                BeanUtils.copyProperties(hospitalInfoExportVO, hospitalInfoVO);

                //校验医院名称、医院等级、医院性质必录
                //校验医院等级、医院性质是否规范
                if(StringUtils.isBlank(hospitalInfoVO.getHospitalName()) ) {
                    failReason.append(" 医院名称不能为空");
                } else {
                    //校验医院是否重复
                    HospitalInfoVO hospitalInfoConditionVO = new HospitalInfoVO();
                    hospitalInfoConditionVO.setHospitalName(hospitalInfoVO.getHospitalName());
                    List<HospitalInfoVO> hospitalInfoVOList = hospitalInfoDao.getHospitalInfoList(hospitalInfoConditionVO);
                    if (ListUtils.isNotEmpty(hospitalInfoVOList)) {
                        for (HospitalInfoVO hospitalInfoResVO : hospitalInfoVOList) {
                            if (hospitalInfoResVO.getHospitalName().equals(hospitalInfoVO.getHospitalName())
                                    && (StringUtils.isBlank(hospitalInfoVO.getIdHospitalInfo()) || !hospitalInfoVO.getIdHospitalInfo().equals(hospitalInfoResVO.getIdHospitalInfo()))) {
                                failReason.append(" 已存在相同医院名称的医院信息");
                            }
                        }
                    }
                }

                if (StringUtils.isBlank(hospitalInfoVO.getHospitalPropertyDes())) {
                    failReason.append(" 医院性质不能为空");
                }
                if (StringUtils.isNotBlank(hospitalInfoVO.getHospitalPropertyDes()) && !hospitalTypeList.contains(hospitalInfoVO.getHospitalPropertyDes())) {
                    failReason.append(" 医院性质填写不正确");
                }
                if (StringUtils.isBlank(hospitalInfoVO.getGrade())) {
                    failReason.append(" 医院等级不能为空");
                }
                if (StringUtils.isNotBlank(hospitalInfoVO.getGrade()) && !hospitalGradeList.contains(hospitalInfoVO.getGrade())) {
                    failReason.append(" 医院等级填写不正确");
                }
                //赋默认属性
                if (count <9) {
                    hospitalCode = "000000" +count;
                } else if (count <99){
                    hospitalCode = "00000" +count;
                } else if (count <999){
                    hospitalCode = "0000" +count;
                } else if (count <9999){
                    hospitalCode = "000" +count;
                }  else if (count <99999){
                    hospitalCode = "00" +count;
                }  else {
                    hospitalCode = "00" +count;
                }
                hospitalInfoVO.setHospitalCode(hospitalCode);
                hospitalInfoVO.setIdHospitalInfo(UuidUtil.getUUID());
                hospitalInfoVO.setOrgType("1");
                hospitalInfoVO.setCreatedBy(WebServletContext.getUser().getUserCode());
                hospitalInfoVO.setUpdatedBy(WebServletContext.getUser().getUserCode());
                hospitalInfoVO.setIsValid("Y");
                //判断成功与否
                if(StringUtils.isBlank(failReason)) {
                    try {
                        hospitalInfoDao.saveHospitalInfo(hospitalInfoVO);
                        failReason = new StringBuilder("success");
                        successCount++;
                    }catch (RuntimeException e) {
                        failReason.append(" ").append(e.getMessage());
                    }
                }else {
                    failCount++;
                }
                HospitalBatchImportDTO importDTO = new HospitalBatchImportDTO();
                BeanUtils.copyProperties(hospitalInfoExportVO, importDTO);
                importDTO.setBatchNo(batchNo);
                importDTO.setIdAhcsHospitalBatchImport(UuidUtil.getUUID());
                importDTO.setFailReason(String.valueOf(failReason));
                importDTO.setCreatedBy(WebServletContext.getUser().getUserCode());
                importDTO.setUpdatedBy(WebServletContext.getUser().getUserCode());
                batchImportMapper.insertImportInfo(importDTO);
                count ++;
            }
            HospitalImportResultVO vo = new HospitalImportResultVO();
            vo.setFailCount(failCount);
            vo.setSuccessCount(successCount);
            vo.setBatchNo(batchNo);
            return vo;
        } catch (Exception e) {
            throw new GlobalBusinessException("医院批量导入失败，失败原因：{}",e.getMessage());

        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    throw new GlobalBusinessException("医院批量导入失败，失败原因：{}",e.getMessage());
                }
            }
        }
    }

    @Override
    public void getHospitalInfoTemplate(HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(response.getOutputStream()).build();
        } catch (Exception e) {
            throw new GlobalBusinessException("医院批量导入模板下载失败，失败原因：{}",e.getMessage());
        }
        WriteSheet writeSheet3 = EasyExcel.writerSheet(3, "医院信息导入模板").head(HospitalInfoImportVO.class).build();
        //表格写入对应sheet的数据
        excelWriter.write(Collections.singletonList((java.util.Collection<?>) null), writeSheet3);
        excelWriter.finish();
        try {
            response.flushBuffer();
        } catch (Exception e) {
            throw new GlobalBusinessException("医院批量导入模板下载失败，失败原因：{}",e.getMessage());
        }

    }

    @Override
    public void getImportFailInfo(String batchNo,HttpServletResponse response)  throws IOException {
        List<HospitalInfoImportFailVO> list = batchImportMapper.getImportFailInfo(batchNo);
        // 导出 Excel
        ExcelUtils.write(response, "批量导入失败信息.xls", "数据", HospitalInfoImportFailVO.class,
                list);
    }

    @Override
    public void updateHospitalCode() {
        HospitalInfoVO hospitalInfoConditionVO = new HospitalInfoVO();
        hospitalInfoConditionVO.setHospitalCode("9999999");
        List<HospitalInfoVO> hospitalInfoVOList = hospitalInfoDao.getHospitalInfoList(hospitalInfoConditionVO);
        if (null != hospitalInfoVOList && hospitalInfoVOList.size() > 0) {
            //医院编码
            String hospitalCode = "";
            Integer count = hospitalInfoDao.getHospitalInfoCount(hospitalInfoConditionVO) + 1;
            for (HospitalInfoVO hospitalInfoVO : hospitalInfoVOList) {
                //医院编码
                if (count <9) {
                    hospitalCode = "000000" +count;
                } else if (count <99){
                    hospitalCode = "00000" +count;
                } else if (count <999){
                    hospitalCode = "0000" +count;
                } else if (count <9999){
                    hospitalCode = "000" +count;
                }  else if (count <99999){
                    hospitalCode = "00" +count;
                }  else {
                    hospitalCode = "00" +count;
                }
                hospitalInfoVO.setHospitalCode(hospitalCode);
                hospitalInfoDao.updateHospitalInfo(hospitalInfoVO);
                count++;
            }
        }
    }
}
