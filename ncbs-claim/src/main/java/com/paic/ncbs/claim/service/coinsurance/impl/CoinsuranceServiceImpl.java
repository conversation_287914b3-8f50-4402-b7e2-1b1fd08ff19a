package com.paic.ncbs.claim.service.coinsurance.impl;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.ReplevyConstant;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.InvoiceTypeEnum;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.coinsurance.*;
import com.paic.ncbs.claim.dao.mapper.coinsurance.RecoveryInfoMapper;
import com.paic.ncbs.claim.dao.mapper.coinsurance.RecoveryRecordMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.settle.CoinsureInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.replevy.dao.ClmsRelatedActualReceiptMapper;
import com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt;
import com.paic.ncbs.claim.replevy.service.ReplevyChargeService;
import com.paic.ncbs.claim.replevy.service.ReplevyService;
import com.paic.ncbs.claim.replevy.vo.ClmsRelatedActualReceiptVo;
import com.paic.ncbs.claim.service.coinsurance.CoinsuranceService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.prepay.PrePayService;
import com.paic.ncbs.claim.service.schedule.impl.JobServiceImpl;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CoinsuranceServiceImpl implements CoinsuranceService {
    @Autowired
    private CoinsureInfoMapper coinsuranceMapper;
    @Autowired
    private FeePayMapper feePayMapper;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private CoinsureService coinsService;
    @Autowired
    private TaskListService taskListService;
    @Autowired
    private RecoveryInfoMapper recoveryInfoMapper;
    @Autowired
    private CoinsuranceServiceImpl coinsuranceService;
    @Autowired
    private ClmsRelatedActualReceiptMapper clmsRelatedActualReceiptMapper;
    @Autowired
    private RecoveryRecordMapper recoveryRecordMapper;
    @Autowired
    private ReplevyService replevyService;
    @Autowired
    private ReplevyChargeService replevyChargeService;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private FileInfoMapper fileInfoMapper;
    @Autowired
    private PrePayService prePayService;

//    @Override
//    //通过页面数据计算共保分摊信息数据
//    public List<CoinsAmortizationVo> settleCoinsuranceList(SettleCoinsVo settleCoinsVo) {
//        //接口入参： 报案号，期次号，页面赔款费用信息大类 List<支付项目，是否全额赔款，金额>
//        //实现过程： 通过报案号查询共保情况 List<共保公司，共保比例，是否首席>
//        List<CoinsAmortizationVo> coinsAmortizationVoList = new ArrayList<>();
//        List<CoinsureDTO> coinsureDTOList = coinsuranceMapper.getCoinsList(settleCoinsVo.getReportNo());
//        if(coinsureDTOList!=null && coinsureDTOList.size()>0){
//            //根据支付项目分类 ，赔款一类，费用一类
//            List<SettleCoinsReqVo> settleCoinsReqVoList = settleCoinsVo.getSettleCoinsReqVoList();
//            if(settleCoinsReqVoList!=null && settleCoinsReqVoList.size()>0){
//                //赔款
//                List<SettleCoinsReqVo> settleCoinsAmountList = settleCoinsReqVoList.stream()
//                        .filter(coins -> "11".equals(coins.getPayItem()) || "13".equals(coins.getPayItem())).collect(Collectors.toList());
//                //费用
//                List<SettleCoinsReqVo> settleCoinsFeeList = settleCoinsReqVoList.stream()
//                        .filter(coins -> "11J".equals(coins.getPayItem()) || "13J".equals(coins.getPayItem())).collect(Collectors.toList());
//                if(settleCoinsAmountList!=null && settleCoinsAmountList.size()>0){
//                    //赔款： 计算总赔款金额
//                    BigDecimal coinsTotalAmount = settleCoinsAmountList.stream()
//                            .map(SettleCoinsReqVo::getAmount)
//                            .reduce(BigDecimal.ZERO,BigDecimal::add);
//                    //循环共保公司信息列表， 赔款金额*共保比例，最后一次循环计算尾差
//                    BigDecimal coinsAmount = BigDecimal.ZERO;
//                    for (int i = 0; i < coinsureDTOList.size(); i++) {
//                        CoinsAmortizationVo coinsAmortizationVo = new CoinsAmortizationVo();
//                        BeanUtils.copyProperties(coinsureDTOList.get(i),coinsAmortizationVo);
//                        BigDecimal coinsRate = coinsureDTOList.get(i).getReinsureScale().divide(new BigDecimal(100));
//                        if(i == coinsureDTOList.size()-1){
//                            coinsAmortizationVo.setCoinsAmount(coinsTotalAmount.subtract(coinsAmount));
//                        }else{
//                            coinsAmortizationVo.setCoinsAmount(coinsTotalAmount.multiply(coinsRate));
//                            coinsAmount = coinsAmount.add(coinsTotalAmount.multiply(coinsRate));
//                        }
//                        coinsAmortizationVo.setPayItem("赔款");
//                        coinsAmortizationVoList.add(coinsAmortizationVo);
//                    }
//                }
//                if(settleCoinsFeeList!=null && settleCoinsFeeList.size()>0){
//                    //费用： 计算费用总额，计算专票税额总额 按是否首席进行分组
//                    BigDecimal coinsTotalFee = settleCoinsFeeList.stream()
//                            .map(SettleCoinsReqVo::getAmount)
//                            .reduce(BigDecimal.ZERO,BigDecimal::add);
//                    //专票税额
//                    BigDecimal coinsTotalTaxAmount = settleCoinsFeeList.stream()
//                            .filter(fee -> InvoiceTypeEnum.isExistSpecialInvoice(fee.getInvoiceType()))
//                            .map(SettleCoinsReqVo::getTaxAmount)
//                            .reduce(BigDecimal.ZERO,BigDecimal::add);
//                    //费用-专票税额
//                    BigDecimal notTaxFee = coinsTotalFee.subtract(coinsTotalTaxAmount);
//                    BigDecimal fee = BigDecimal.ZERO;
//                    for (int i = 0; i < coinsureDTOList.size(); i++) {
//                        CoinsAmortizationVo coinsAmortizationVo = new CoinsAmortizationVo();
//                        BeanUtils.copyProperties(coinsureDTOList.get(i),coinsAmortizationVo);
//                        BigDecimal coinsRate = coinsureDTOList.get(i).getReinsureScale().divide(new BigDecimal(100));
//                        if("1".equals(coinsureDTOList.get(i).getAcceptInsuranceFlag())){
//                            //首席： （总费用-专票）*共保比例+专票，最后一次循环计算尾差
//                            if(i == coinsureDTOList.size()-1){
//                                coinsAmortizationVo.setCoinsAmount(coinsTotalFee.subtract(fee));
//                            }else{
//                                coinsAmortizationVo.setCoinsAmount(notTaxFee.multiply(coinsRate).add(coinsTotalTaxAmount));
//                                fee = fee.add(notTaxFee.multiply(coinsRate).add(coinsTotalTaxAmount));
//                            }
//                        } else {
//                            //非首席： （总费用-专票）* 共保比例，最后一次循环计算尾差
//                            if(i == coinsureDTOList.size()-1){
//                                coinsAmortizationVo.setCoinsAmount(coinsTotalFee.subtract(fee));
//                            }else{
//                                coinsAmortizationVo.setCoinsAmount(notTaxFee.multiply(coinsRate));
//                                fee = fee.add(notTaxFee.multiply(coinsRate));
//                            }
//                        }
//                        coinsAmortizationVo.setPayItem("费用");
//                        coinsAmortizationVoList.add(coinsAmortizationVo);
//                    }
//                }
//            }
//        }
//        //回参： 分摊信息大类
//        return coinsAmortizationVoList;
//    }


    //通过页面数据计算共保分摊信息数据
    @Override
    public List<CoinsAmortizationVo> settleCoinsuranceList(String reportNo,Integer caseTimes, BigDecimal payAmount,String payType) {
        //接口入参： 报案号，期次号，页面赔款费用信息大类 List<支付项目，是否全额赔款，金额>
        //实现过程： 通过报案号查询共保情况 List<共保公司，共保比例，是否首席>
        List<CoinsAmortizationVo> coinsAmortizationVoList = new ArrayList<>();
        List<CoinsureDTO> coinsureDTOList = coinsuranceMapper.getCoinsList(reportNo);
        //费用 payType 1 核赔 2预赔
        Integer subTimes = 0;
        if(SettleConst.CLAIM_TYPE_PRE_PAY.equals(payType)){
            subTimes = prePayService.getApprovedSubTimes(reportNo,caseTimes);
        }
        List<SettleCoinsReqVo> settleCoinsFeeList = feePayMapper.getCoinsFeeSettle(reportNo,caseTimes,payType,subTimes);
        //当费用为空时不计算费用
        Boolean feeSettleFlag = settleCoinsFeeList!=null && settleCoinsFeeList.size()>0;
        //赔款为零不计算赔款分摊
        Boolean amountSettleFlag = ObjectUtil.isNotEmpty(payAmount) && payAmount.compareTo(BigDecimal.ZERO)!=0;
        //费用： 计算费用总额，计算专票税额总额 按是否首席进行分组
        BigDecimal coinsTotalFee = settleCoinsFeeList.stream()
                .map(SettleCoinsReqVo::getAmount)
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        //专票税额
        BigDecimal coinsTotalTaxAmount = settleCoinsFeeList.stream()
                .filter(fee -> InvoiceTypeEnum.isExistSpecialInvoice(fee.getInvoiceType()))
                .map(SettleCoinsReqVo::getTaxAmount)
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        //费用-专票税额
        BigDecimal notTaxFee = coinsTotalFee.subtract(coinsTotalTaxAmount);
        String currency = "CNY";

        if(coinsureDTOList!=null && coinsureDTOList.size()>0){
            //循环共保公司信息列表， 赔款金额*共保比例，最后一次循环计算尾差
            //计算赔款尾差用
            BigDecimal amountTemp = BigDecimal.ZERO;
            //计算费用尾差用
            BigDecimal feeTemp = BigDecimal.ZERO;
            for (int i = 0; i < coinsureDTOList.size(); i++) {
                CoinsureDTO coinsureDTO =  coinsureDTOList.get(i);

                BigDecimal coinsRate = coinsureDTO.getReinsureScale().divide(new BigDecimal(100));

                CoinsAmortizationVo coinsAmortizationVo = new CoinsAmortizationVo();

                CoinsAmortizationVo coinsAmortFeeVo = new CoinsAmortizationVo();
                BeanUtils.copyProperties(coinsureDTO,coinsAmortFeeVo);
                BeanUtils.copyProperties(coinsureDTO,coinsAmortizationVo);
                //分摊赔款 amortAmount;
                BigDecimal amortAmount;
                //分摊费用 amortFee
                BigDecimal amortFee;
                //是否首席
                String firstFlag = coinsureDTO.getAcceptInsuranceFlag();
                if(i == coinsureDTOList.size()-1){
                    amortAmount = payAmount.subtract(amountTemp);
                    amortFee = coinsTotalFee.subtract(feeTemp);
                }else{
                    amortFee = notTaxFee.multiply(coinsRate).setScale(2, RoundingMode.HALF_UP);
                    amortAmount = payAmount.multiply(coinsRate).setScale(2, RoundingMode.HALF_UP);
                    //首席 加专票税额
                    if("1".equals(firstFlag)){
                        amortFee = amortFee.add(coinsTotalTaxAmount);
                    }
                    feeTemp = feeTemp.add(amortFee);
                    amountTemp = amountTemp.add(amortAmount);
                }
//                coinsAmortizationVo.setPayType(payType);
                coinsAmortizationVo.setCoinsAmount(amortAmount);
                coinsAmortizationVo.setCoinsCompanyName(coinsureDTO.getReinsureCompanyName());
                coinsAmortizationVo.setCoinsCompanyCode(coinsureDTO.getReinsureCompanyCode());
                coinsAmortizationVo.setCurrency(currency);

                coinsAmortizationVoList.add(coinsAmortizationVo);

                coinsAmortFeeVo.setCoinsAmount(amortFee);
                coinsAmortFeeVo.setCoinsCompanyName(coinsureDTO.getReinsureCompanyName());
                coinsAmortFeeVo.setCoinsCompanyCode(coinsureDTO.getReinsureCompanyCode());
                coinsAmortFeeVo.setCurrency(currency);

                coinsAmortizationVoList.add(coinsAmortFeeVo);

                //支付项目取值
                if("1".equals(payType)){
                    coinsAmortizationVo.setPayItem(PaymentTypeEnum.PAY.getType());
                    coinsAmortFeeVo.setPayItem(PaymentTypeEnum.FEE.getType());
                }else{
                    coinsAmortizationVo.setPayItem(PaymentTypeEnum.PRE_PAY.getType());
                    coinsAmortFeeVo.setPayItem(PaymentTypeEnum.PRE_FEE.getType());
                }
            }
        }
        if(!feeSettleFlag){
            coinsAmortizationVoList = coinsAmortizationVoList.stream()
                    .filter(coins -> PaymentTypeEnum.PAY.getType().equals(coins.getPayItem())
                            || PaymentTypeEnum.PRE_PAY.getType().equals(coins.getPayItem()))
                    .collect(Collectors.toList());
        }
        if(!amountSettleFlag){
            coinsAmortizationVoList = coinsAmortizationVoList.stream()
                    .filter(coins -> PaymentTypeEnum.FEE.getType().equals(coins.getPayItem())
                            || PaymentTypeEnum.PRE_FEE.getType().equals(coins.getPayItem()))
                    .collect(Collectors.toList());
        }
        //回参： 分摊信息大类
        return coinsAmortizationVoList;
    }

    @Override
    public List<CoinsAmortizationVo> settleCoinsuranceList(String reportNo, Integer caseTimes,String claimType,Integer subTimes) {
        //接口入参： 报案号，期次号，页面赔款费用信息大类 List<支付项目，是否全额赔款，金额>
        //实现过程： 通过报案号查询共保情况 List<共保公司，共保比例，是否首席>
        List<CoinsAmortizationVo> coinsAmortizationVoList = new ArrayList<>();
        List<CoinsureDTO> coinsureDTOList = coinsuranceMapper.getCoinsList(reportNo);
        //费用 payType 1 核赔 2预赔
        SettleCoinsReqVo settleCoinsFee = new SettleCoinsReqVo();
        SettleCoinsReqVo settleCoinsAmount = new SettleCoinsReqVo();
        settleCoinsFee = paymentItemMapper.getCoinsSettleFeeVo(reportNo,caseTimes,claimType,subTimes);
        settleCoinsAmount = paymentItemMapper.getCoinsSettleAmountVo(reportNo,caseTimes,claimType,subTimes);
        //默认人民币
        String currency = "01";
        BigDecimal payAmount = BigDecimal.ZERO;
        //费用： 计算费用总额
        BigDecimal coinsTotalFee = BigDecimal.ZERO;
        //专票税额
        BigDecimal coinsTotalTaxAmount = BigDecimal.ZERO;
        //费用-专票税额
        BigDecimal notTaxFee = BigDecimal.ZERO;
        Boolean amountSettleFlag = false;
        Boolean feeSettleFlag = false;

        if(ObjectUtil.isNotEmpty(settleCoinsAmount)){
            currency = settleCoinsAmount.getCurrency();
            payAmount = settleCoinsAmount.getAmount();
            //赔款为零不计算赔款分摊
            amountSettleFlag = ObjectUtil.isNotEmpty(payAmount) && payAmount.compareTo(BigDecimal.ZERO)!=0;
        }
        if(ObjectUtil.isNotEmpty(settleCoinsFee) && ObjectUtil.isNotEmpty(settleCoinsFee.getAmount())){
            //当费用为空时不计算费用
            feeSettleFlag = true;
            //费用
            coinsTotalFee = settleCoinsFee.getAmount();
            //专票税额
            coinsTotalTaxAmount = settleCoinsFee.getTaxAmount();
            //费用-专票税额
            notTaxFee = coinsTotalFee.subtract(coinsTotalTaxAmount);
        }

        if(coinsureDTOList!=null && coinsureDTOList.size()>0){
            coinsureDTOList = coinsureDTOList.stream().sorted(Comparator.comparing(CoinsureDTO::getCompanyFlag)).collect(Collectors.toList());
            //循环共保公司信息列表， 赔款金额*共保比例，最后一次循环计算尾差
            //计算赔款尾差用
            BigDecimal amountTemp = BigDecimal.ZERO;
            //计算费用尾差用
            BigDecimal feeTemp = BigDecimal.ZERO;
            //计算税额尾差
            BigDecimal taxTemp = BigDecimal.ZERO;
            for (int i = 0; i < coinsureDTOList.size(); i++) {
                CoinsureDTO coinsureDTO =  coinsureDTOList.get(i);

                BigDecimal coinsRate = coinsureDTO.getReinsureScale().divide(new BigDecimal(100));

                CoinsAmortizationVo coinsAmortizationVo = new CoinsAmortizationVo();

                CoinsAmortizationVo coinsAmortFeeVo = new CoinsAmortizationVo();
                BeanUtils.copyProperties(coinsureDTO,coinsAmortFeeVo);
                BeanUtils.copyProperties(coinsureDTO,coinsAmortizationVo);
                //分摊赔款 amortAmount;
                BigDecimal amortAmount;
                //分摊费用 amortFee
                BigDecimal amortFee;
                //分摊税额 ;
                BigDecimal amortTax;
                //是否首席
                String firstFlag = coinsureDTO.getAcceptInsuranceFlag();
                if(i == coinsureDTOList.size()-1){
                    amortAmount = payAmount.subtract(amountTemp);
                    amortFee = coinsTotalFee.subtract(feeTemp);
                    amortTax = coinsTotalTaxAmount.subtract(taxTemp);
                }else{
                    amortFee = notTaxFee.multiply(coinsRate).setScale(2, RoundingMode.HALF_UP);
                    amortAmount = payAmount.multiply(coinsRate).setScale(2, RoundingMode.HALF_UP);
                    amortTax = coinsTotalTaxAmount.multiply(coinsRate).setScale(2, RoundingMode.HALF_UP);
                    //首席 加专票税额
                    if("1".equals(firstFlag)){
                        amortFee = amortFee.add(coinsTotalTaxAmount);
                    }
                    feeTemp = feeTemp.add(amortFee);
                    amountTemp = amountTemp.add(payAmount.multiply(coinsRate));
                    taxTemp = taxTemp.add(amortTax);
                }
//                coinsAmortizationVo.setPayType(payType);
                coinsAmortizationVo.setCoinsAmount(amortAmount);
                coinsAmortizationVo.setSubTimes(subTimes);
                //支付项目取值
                coinsAmortizationVo.setPayItem("1".equals(claimType) ? PaymentTypeEnum.PAY.getType() : PaymentTypeEnum.PRE_PAY.getType());
                coinsAmortizationVo.setCoinsCompanyName(coinsureDTO.getReinsureCompanyName());
                coinsAmortizationVo.setCoinsCompanyCode(coinsureDTO.getReinsureCompanyCode());
                coinsAmortizationVo.setCurrency(currency);
                coinsAmortizationVoList.add(coinsAmortizationVo);

                coinsAmortFeeVo.setCoinsAmount(amortFee);
                //税额
                coinsAmortFeeVo.setCoinsTax(amortTax);
                coinsAmortFeeVo.setSubTimes(subTimes);
                //支付项目取值
                coinsAmortFeeVo.setPayItem("1".equals(claimType) ? PaymentTypeEnum.FEE.getType() : PaymentTypeEnum.PRE_FEE.getType());
                coinsAmortFeeVo.setCoinsCompanyName(coinsureDTO.getReinsureCompanyName());
                coinsAmortFeeVo.setCoinsCompanyCode(coinsureDTO.getReinsureCompanyCode());
                coinsAmortFeeVo.setCurrency(currency);
                coinsAmortizationVoList.add(coinsAmortFeeVo);

            }
        }
        if(!feeSettleFlag){
            coinsAmortizationVoList = coinsAmortizationVoList.stream()
                    .filter(coins -> PaymentTypeEnum.PAY.getType().equals(coins.getPayItem()) || PaymentTypeEnum.PRE_PAY.equals(coins.getPayItem()))
                    .collect(Collectors.toList());
        }
        if(!amountSettleFlag){
            coinsAmortizationVoList = coinsAmortizationVoList.stream()
                    .filter(coins -> PaymentTypeEnum.FEE.getType().equals(coins.getPayItem()) || PaymentTypeEnum.PRE_FEE.getType().equals(coins.getPayItem()))
                    .collect(Collectors.toList());
        }
        coinsAmortizationVoList.stream().forEach(coins->{
            coins.setReportNo(reportNo);
            coins.setCaseTimes(caseTimes);
        });
        //回参： 分摊信息大类
        return coinsAmortizationVoList;
    }

    @Override
    public void addRecoveryInfo(String reportNo, Integer caseTimes,String claimType,Integer subTimes) {
        //todo 后续增加币别需要按币别，共保公司分组
        boolean isMainCoinsureFlag = coinsService.isMainCoinsureFlag(reportNo);
        //主共保案件生成共保摊回记录
        if (isMainCoinsureFlag) {
            UserInfoDTO userInfoDTO = WebServletContext.getUser();
            List<RecoveryInfo> recoveryInfoList = new ArrayList<>();
            List<CoinsAmortizationVo> coinsAmortizationVoList = this.settleCoinsuranceList(reportNo, caseTimes,claimType,subTimes);
            log.info("计算结果："+JsonUtils.toJsonString(coinsAmortizationVoList));
            coinsAmortizationVoList.stream()
                    .filter(coins -> !"1".equals(coins.getCompanyFlag()))
                    .forEach(coins -> {
                        RecoveryInfo recoveryInfo = new RecoveryInfo();
                        BeanUtils.copyProperties(coins, recoveryInfo);
                        recoveryInfo.setIdCoinsInfo(UuidUtil.getUUID().replace("-",""));
                        recoveryInfo.setMainFlag(coins.getAcceptInsuranceFlag());
                        recoveryInfo.setCoinsRate(coins.getReinsureScale());
                        recoveryInfo.setCurrency(coins.getCurrency());
                        recoveryInfo.setSubTimes(coins.getSubTimes());
                        recoveryInfoList.add(recoveryInfo);
                    });
            if (recoveryInfoList != null && recoveryInfoList.size() > 0) {
                recoveryInfoList.stream().forEach(recoveryInfo -> {
                    recoveryInfo.setCreatedBy(userInfoDTO.getUserCode());
                    recoveryInfo.setUpdatedBy(userInfoDTO.getUserCode());
                    recoveryInfo.setReportNo(reportNo);
                    recoveryInfo.setCaseTimes(caseTimes);
                    recoveryInfo.setRecoveryFlag("0");
                });
                recoveryInfoMapper.insertList(recoveryInfoList);
                log.info("存表结果："+JsonUtils.toJsonString(recoveryInfoList));
            }
        }else{
            log.info("非主共保案件不生成共保摊回记录");
        }
    }

    @Override
    public PageInfo<CoinsAmortizationVo> getCoinsAmortizationList(CoinsSearchVo coinsSearchVo) {
        //分页
        PageHelper.startPage(coinsSearchVo.getCurrentPage(),coinsSearchVo.getPageSize());
        //查询登录人机构下级数据
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
        coinsSearchVo.setDepartmentCodes(departmentCodes);
        //查询
        List<CoinsAmortizationVo> coinsAmortizationVoList = recoveryInfoMapper.selectCoinsList(coinsSearchVo);
        coinsAmortizationVoList.stream().forEach(vo->{
            vo.setCurrency(SettleConst.CURRENCY_MAP.get(vo.getCurrency()));
        });

        return new PageInfo<>(coinsAmortizationVoList);
    }

    @Override
    public CoinsAmorDetailVo getCoinsAmorDetail(AmortSearcherVo amortSearcherVo) {
        List<String> idList = amortSearcherVo.getIdList();
        //操作/查看标识 0操作 1查看
        String viewType = amortSearcherVo.getViewType();
        if(ObjectUtil.isEmpty(idList)){
            throw new RuntimeException("入参为空请检查入参");
        }
        CoinsAmorDetailVo coinsAmorDetailVo = new CoinsAmorDetailVo();
        //1.通过id查询共保摊回记录表 获取共保摊回记录
        List<CoinsAmortizationVo> coinsAmortizationVoList = recoveryInfoMapper.selectByIdList(idList);
        //校验摊回记录 相同共保公司+相同币别时，允许合并摊回。
        if(coinsAmortizationVoList!=null && coinsAmortizationVoList.size()>0){
            CoinsAmortizationVo coinsAmortizationVo = coinsAmortizationVoList.get(0);
            coinsAmortizationVoList.stream().forEach(amort->{
                amort.setCurrency(SettleConst.CURRENCY_MAP.get(amort.getCurrency()));
                if(!amort.getCoinsCompanyCode().equals(coinsAmortizationVo.getCoinsCompanyCode())
                || !amort.getCurrency().equals(coinsAmortizationVo.getCurrency())){
                    throw new RuntimeException("共保公司或币别不一致，不允许合并摊回");
                }
            });
        }
        //操作页面，查询到的摊回记录直接返回
        if("0".equals(viewType)){
            //币别转换处理
            coinsAmortizationVoList.stream().forEach(vo->{
                vo.setCurrency(SettleConst.CURRENCY_MAP.get(vo.getCurrency()));
            });
            coinsAmorDetailVo.setCoinsAmortizationVoList(coinsAmortizationVoList);
            coinsAmorDetailVo.setIdRecoveryRecord(UuidUtil.getUUID().replace("-",""));
        }else{
            //查看页面，通过进入的id查询到的摊回记录关联摊回主表，获取此次关联的所有摊回记录，实收记录，附件信息
            //2.通过id查询关联的实收记录，获取实收记录数据
            String idRecoveryRecord = coinsAmortizationVoList.get(0).getIdRecoveryRecord();
            if(ObjectUtil.isNotEmpty(idRecoveryRecord)){
                //共保摊回记录
                List<CoinsAmortizationVo> amortizationVoList = recoveryInfoMapper.selectAmortByRecordId(idRecoveryRecord);
                coinsAmorDetailVo.setCoinsAmortizationVoList(amortizationVoList);
                //关联实收记录
                List<ClmsRelatedActualReceipt> clmsRelatedActualReceiptList = clmsRelatedActualReceiptMapper.selectByIdRecovery(idRecoveryRecord);
                List<ClmsRelatedActualReceiptVo> clmsRelatedActualReceiptVoList = new ArrayList<>();
                clmsRelatedActualReceiptList.stream().forEach(v->{
                    ClmsRelatedActualReceiptVo clmsRelatedActualReceiptVo = new ClmsRelatedActualReceiptVo();
                    BeanUtils.copyProperties(v,clmsRelatedActualReceiptVo);
                    clmsRelatedActualReceiptVoList.add(clmsRelatedActualReceiptVo);
                });
                coinsAmorDetailVo.setClmsRelatedActualReceiptVoList(clmsRelatedActualReceiptVoList);
                //摊回主表数据
                RecoveryRecord recoveryRecord = recoveryRecordMapper.selectByIdRecovery(idRecoveryRecord);
                coinsAmorDetailVo.setSumAmorAmount(recoveryRecord.getSumAmorAmount());
                coinsAmorDetailVo.setSumReceiptsAmount(recoveryRecord.getSumReceiptsAmount());
                coinsAmorDetailVo.setAmountDifferenceReason(recoveryRecord.getAmountDifferenceReason());
                coinsAmorDetailVo.setCompanyPaymentMismatchReason(recoveryRecord.getCompanyMismatchReason());
                coinsAmorDetailVo.setIdRecoveryRecord(idRecoveryRecord);
                //坏账文件数据
                List<FileDocumentDTO> list = fileInfoMapper.queryDocumentByGroupId(idRecoveryRecord);
                if(list!=null && list.size()>0){
                    List<AmorFileVo> amorFileVoList = new ArrayList<>();
                    list.stream().forEach(document->{
                        AmorFileVo amorFileVo = new AmorFileVo();
                        amorFileVo.setCosFilePath(document.getUploadPath());

                        amorFileVoList.add(amorFileVo);
                    });
                    coinsAmorDetailVo.setFileInfoList(amorFileVoList);
                }
            }
        }
        return coinsAmorDetailVo;
    }

    @Override
    public ResponseResult checkAmor(CoinsAmorDetailVo coinsAmorDetailVo) {
        //(1)	校验所选记录均为未摊回状态，否则不允许提交；
        List<CoinsAmortizationVo> coinsAmortizationVoList = coinsAmorDetailVo.getCoinsAmortizationVoList();
        coinsAmortizationVoList.stream().forEach(coinsAmortizationVo -> {
            if("1".equals(coinsAmortizationVo.getAmortizationFlag())){
                throw new GlobalBusinessException("存在以摊回记录，不允许提交");
            }
        });
        //(2)	校验-1≤“总实收金额”-“应摊回金额”总和≤0；
        BigDecimal sumAmorAmount = coinsAmorDetailVo.getSumAmorAmount();
        BigDecimal sumReceiptsAmount = coinsAmorDetailVo.getSumReceiptsAmount();
        BigDecimal diffAmount = sumReceiptsAmount.subtract(sumAmorAmount);
        if(diffAmount.compareTo(new BigDecimal(-1))<0){
//            return ResponseResult.fail("9999","实收金额低于应摊回金额，差额已超过1元阈值！");
            throw new GlobalBusinessException("实收金额低于应摊回金额，差额已超过1元阈值,系统暂不支持！");
        }
        if(diffAmount.compareTo(BigDecimal.ZERO)>0){
            throw new GlobalBusinessException("实收金额高于应摊回金额，不允许提交！");
        }

        return ResponseResult.success();
    }

    @Override
    @Transactional
    public ResponseResult submitAmor(CoinsAmorDetailVo coinsAmorDetailVo) {
        //存在坏账文件提交则 送坏账接口
        //生成主表主键用于送收付的 originBusinessNo 理赔结算单号取值
        String idRecoveryRecord = coinsAmorDetailVo.getIdRecoveryRecord();
        coinsAmorDetailVo.setIdRecoveryRecord(idRecoveryRecord);
        this.checkParam(coinsAmorDetailVo);
        String batchNo = JobServiceImpl.generateBatchNumber();
        //数据存表，共保摊回主表，共保摊回记录表，关联实收表
        coinsuranceService.saveAmorData(coinsAmorDetailVo,batchNo);
        this.sendRecovery(coinsAmorDetailVo.getClmsRelatedActualReceiptVoList(),batchNo,idRecoveryRecord);
        return ResponseResult.success();
    }

    @Override
    public void addRecoveryInfoNew(List<CoinsAmortizationVo> coinsAmortizationVoList) {
        //todo 后续增加币别需要按币别，共保公司分组
        String reportNo = coinsAmortizationVoList.get(0).getReportNo();
        Integer caseTimes = Integer.valueOf(coinsAmortizationVoList.get(0).getCaseTimes());
        boolean isMainCoinsureFlag = coinsService.isMainCoinsureFlag(reportNo);
        //主共保案件生成共保摊回记录
        if (isMainCoinsureFlag) {
            UserInfoDTO userInfoDTO = WebServletContext.getUser();
            List<RecoveryInfo> recoveryInfoList = new ArrayList<>();
            log.info("计算结果："+JsonUtils.toJsonString(coinsAmortizationVoList));
            coinsAmortizationVoList.stream()
                    .filter(coins -> !"1".equals(coins.getCompanyFlag()))
                    .forEach(coins -> {
                        RecoveryInfo recoveryInfo = new RecoveryInfo();
                        BeanUtils.copyProperties(coins, recoveryInfo);
                        recoveryInfo.setIdCoinsInfo(UuidUtil.getUUID().replace("-",""));
                        recoveryInfo.setMainFlag(coins.getAcceptInsuranceFlag());
                        recoveryInfo.setCoinsRate(coins.getReinsureScale());
                        recoveryInfo.setCurrency(coins.getCurrency());
                        recoveryInfoList.add(recoveryInfo);
                    });
            if (recoveryInfoList != null && recoveryInfoList.size() > 0) {
                recoveryInfoList.stream().forEach(recoveryInfo -> {
                    recoveryInfo.setCreatedBy(userInfoDTO.getUserCode());
                    recoveryInfo.setUpdatedBy(userInfoDTO.getUserCode());
                    recoveryInfo.setReportNo(reportNo);
                    recoveryInfo.setCaseTimes(caseTimes);
                    recoveryInfo.setRecoveryFlag("0");
                });
                recoveryInfoMapper.insertList(recoveryInfoList);
                log.info("存表结果："+JsonUtils.toJsonString(recoveryInfoList));
            }
        }else{
            log.info("非主共保案件不生成共保摊回记录");
        }
    }

    @Override
    public void sendRecovery(List<ClmsRelatedActualReceiptVo> RelatedActualReceiptVoList, String batchNo, String idRecoveryRecord) {
        try{
            //实收冻结
            replevyService.sendPayThaw(null,null,idRecoveryRecord, ReplevyConstant.FREEZE_STATUS_F,ReplevyConstant.RECEIPT_TYPE_COINS);
            //核销实收数据
            replevyChargeService.sendPaymentConfirmAmor(ReplevyConstant.RECEIPT_TYPE_COINS,idRecoveryRecord,batchNo);
        } catch (Exception e) {
            log.error(e.getMessage());
            //解冻
            replevyService.sendPayThaw(null,null,idRecoveryRecord, ReplevyConstant.FREEZE_STATUS_R,ReplevyConstant.RECEIPT_TYPE_COINS);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    private void checkParam(CoinsAmorDetailVo coinsAmorDetailVo) {
        if(coinsAmorDetailVo.getSumAmorAmount()
                .subtract(coinsAmorDetailVo.getSumReceiptsAmount())
                .compareTo(new BigDecimal(1))>0
        && ObjectUtil.isNotEmpty(coinsAmorDetailVo.getFileInfoList())){
            throw new GlobalBusinessException("应摊回金额与实收金额差额大于一元，坏账附件不能为空");
        }
    }


    public void saveAmorData(CoinsAmorDetailVo coinsAmorDetailVo,String batchNo) {
        String idRecoveryRecord = coinsAmorDetailVo.getIdRecoveryRecord();
        //计算共保摊回记录已摊回金额
        settleCoinsAmortAmount(coinsAmorDetailVo);
        List<CoinsAmortizationVo> coinsAmortizationVoList = coinsAmorDetailVo.getCoinsAmortizationVoList();
        List<ClmsRelatedActualReceipt> clmsRelatedActualReceiptList = new ArrayList<>();
        coinsAmorDetailVo.getClmsRelatedActualReceiptVoList()
                .stream().forEach(vo->{
                    ClmsRelatedActualReceipt clmsRelatedActualReceipt = new ClmsRelatedActualReceipt();
                    BeanUtils.copyProperties(vo,clmsRelatedActualReceipt);
                    clmsRelatedActualReceiptList.add(clmsRelatedActualReceipt);
                });
        //共保摊回主表 clms_recovery_record
        RecoveryRecord recoveryRecord = new RecoveryRecord();
        recoveryRecord.setIdRecoveryRecord(idRecoveryRecord);
        recoveryRecord.setSumAmorAmount(coinsAmorDetailVo.getSumAmorAmount());
        recoveryRecord.setSumReceiptsAmount(coinsAmorDetailVo.getSumReceiptsAmount());
        recoveryRecord.setAmountDifferenceReason(coinsAmorDetailVo.getAmountDifferenceReason());
        recoveryRecord.setCompanyMismatchReason(coinsAmorDetailVo.getCompanyPaymentMismatchReason());
        recoveryRecord.setBatchNo(batchNo);
        recoveryRecord.setCreatedBy(WebServletContext.getUserId());
        recoveryRecord.setUpdatedBy(WebServletContext.getUserId());
        recoveryRecordMapper.insert(recoveryRecord);
        //共保摊回记录表 clms_recovery_info
        recoveryInfoMapper.updateList(coinsAmortizationVoList,idRecoveryRecord);
        //关联实收表 Clms_Related_Actual_Receipt
        clmsRelatedActualReceiptList.stream().forEach(clms->{
            clms.setId(UuidUtil.getUUID());
            clms.setBatchNo(batchNo);
            clms.setIdRecoveryRecord(idRecoveryRecord);
            clms.setCaseTimes(1);
            clms.setReceiptType(ReplevyConstant.RECEIPT_TYPE_COINS);
            clms.setValidFlag("Y");
            clms.setFlag(Constants.PAYMENT_ITEM_STATUS_30);
            clms.setCreatedBy(WebServletContext.getUserId());
            clms.setUpdatedBy(WebServletContext.getUserId());
            clms.setSysCtime(new Date());
            clms.setSysUtime(new Date());
            clmsRelatedActualReceiptMapper.insertSelective(clms);
        });

    }

    private void settleCoinsAmortAmount(CoinsAmorDetailVo coinsAmorDetailVo) {
        //获取总实收金额
        BigDecimal sumReceiptsAmount = coinsAmorDetailVo.getSumReceiptsAmount();
        //遍历摊回记录，计算所占必录，给已摊回金额赋值
        //已摊回金额用减法  总实收金额减去当去摊回记录应摊回金额为正数则 已摊回=应摊回
        //总实收-应摊回为负数 则 已摊回=应摊回-余差
        //如果减余差之后结果还是负数 则 已摊回=0；
        List<CoinsAmortizationVo> coinsAmortizationVoList = coinsAmorDetailVo.getCoinsAmortizationVoList();
        if(coinsAmortizationVoList!=null && coinsAmortizationVoList.size()>0){
            for(int i=0;i<coinsAmortizationVoList.size();i++){
                BigDecimal reAmount = coinsAmortizationVoList.get(i).getCoinsAmount();
                sumReceiptsAmount = sumReceiptsAmount.subtract(reAmount);
                if(sumReceiptsAmount.compareTo(BigDecimal.ZERO)>0){
                    coinsAmortizationVoList.get(i).setReCoinsAmount(reAmount);
                }else{
                    coinsAmortizationVoList.get(i).setReCoinsAmount(reAmount.add(sumReceiptsAmount));
                }
                if(coinsAmortizationVoList.get(i).getReCoinsAmount().compareTo(BigDecimal.ZERO)<0){
                    coinsAmortizationVoList.get(i).setReCoinsAmount(BigDecimal.ZERO);
                }
            }
        }
    }
}
