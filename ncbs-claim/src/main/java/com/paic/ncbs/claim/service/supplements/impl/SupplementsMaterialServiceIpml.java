package com.paic.ncbs.claim.service.supplements.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.SupplementsStateEnum;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity;
import com.paic.ncbs.claim.dao.mapper.supplements.SupplementsMaterialMapper;
import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;
import com.paic.ncbs.claim.service.supplements.SupplementsMaterialService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 补材任务记录
 */
@Service
public class SupplementsMaterialServiceIpml implements SupplementsMaterialService {

    @Autowired
    private SupplementsMaterialMapper supplementsMaterialMapper;


    /**
     * 根据ID查询补材任务信息
     *
     * @param id
     * @return
     */
    @Override
    public SupplementsMaterialDTO selectByPrimaryKey(String id) {
        SupplementsMaterialEntity supplementsMaterialEntity = supplementsMaterialMapper.selectByPrimaryKey(id);
        SupplementsMaterialDTO dto = null;
        if(null != supplementsMaterialEntity){
             dto = new SupplementsMaterialDTO();
            BeanUtils.copyProperties(supplementsMaterialEntity,dto);
        }
        return dto;
    }

    /**
     * 保存补材记录保存
     * @param dto
     */
    @Override
    public void saveData(SupplementsMaterialDTO dto) {

        SupplementsMaterialEntity entity = new SupplementsMaterialEntity();

        BeanUtils.copyProperties(dto, entity);
        if (StringUtils.isNotEmpty(dto.getReportAcceptUm())) {
            entity.setCreatedBy(dto.getReportAcceptUm());
            entity.setUpdatedBy(dto.getReportAcceptUm());
        } else {
            entity.setCreatedBy(WebServletContext.getUserId());
            entity.setUpdatedBy(WebServletContext.getUserId());
        }
        entity.setId(UuidUtil.getUUID());
        Date date = new Date();
        entity.setCreatedDate(date);
        entity.setUpdatedDate(date);
        SupplementsMaterialEntity entity1 = supplementsMaterialMapper.getOneNewEntity(entity);
        if (ObjectUtil.isEmpty(entity1)) {
            entity.setSupplementCount(1);
        } else {
            entity.setSupplementCount(entity1.getSupplementCount() + 1);
        }

        supplementsMaterialMapper.insert(entity);

    }

    @Override
    public void updateData(SupplementsMaterialDTO dto) {
        // 取上下文中的用户ID，不存在时再默认
        String updatedBy = WebServletContext.getUserIdForLog();
        SupplementsMaterialEntity entity = new SupplementsMaterialEntity();
        BeanUtils.copyProperties(dto, entity);
        entity.setUpdatedBy(updatedBy);
        entity.setUpdatedDate(new Date());
        entity.setSupplementsState(SupplementsStateEnum.SUPPLEMENTS_STATE_01.getCode());
        supplementsMaterialMapper.update(entity);

    }

    /**
     * 查询补材任务记录
     *
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public List<SupplementsMaterialDTO> getSupplementsMaterial(String reportNo, Integer caseTimes) {
        SupplementsMaterialEntity entity = new SupplementsMaterialEntity();
        entity.setReportNo(reportNo);
        entity.setCaseTimes(caseTimes);
        List<SupplementsMaterialEntity> resultEntityList = supplementsMaterialMapper.getSupplementsMaterial(entity);
        List<SupplementsMaterialDTO> dtos = null;
        if (CollectionUtil.isNotEmpty(resultEntityList)) {
            dtos = new ArrayList<>();
            for (SupplementsMaterialEntity ent : resultEntityList) {
                SupplementsMaterialDTO dto = new SupplementsMaterialDTO();
                BeanUtils.copyProperties(ent, dto);
                dtos.add(dto);
            }

        }
        return dtos;
    }

    /**
     * 批量处理超期没有处理的任务
     */
    @Override
    public List<SupplementsMaterialDTO> getServiceData(Integer days) {
        List<SupplementsMaterialDTO> dtoList = new ArrayList<>();
        List<SupplementsMaterialEntity> entityList = supplementsMaterialMapper.getAllDataList(days);
        if (CollectionUtil.isEmpty(entityList)) {
            return dtoList;
        }

        for (SupplementsMaterialEntity entity : entityList) {
            SupplementsMaterialDTO dto = new SupplementsMaterialDTO();
            BeanUtils.copyProperties(entity,dto);
            dtoList.add(dto);
        }
        return dtoList;

    }

    /**
     * 更新业务数据 为关闭状态
     *
     * @param dto
     */
    public void updateServiceData(SupplementsMaterialDTO dto) {
        SupplementsMaterialEntity entity = new SupplementsMaterialEntity();
        BeanUtils.copyProperties(dto,entity);
        entity.setUpdatedBy("SYSTEM");
        entity.setUpdatedDate(new Date());
        entity.setSupplementsState(SupplementsStateEnum.SUPPLEMENTS_STATE_02.getCode());
        LogUtil.info("客户补材updateServiceData参数={}", JsonUtils.toJsonString(entity));
        supplementsMaterialMapper.update(entity);
    }
    /**
     * 查询客户补材任务记录
     * @param reportNo
     * @param caseTimes
     * @return
     */
    /**
     * 客户补材任务记录
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public List<SupplementsMaterialDTO> getCustomerSupplements(String reportNo, Integer caseTimes) {
        List<SupplementsMaterialEntity> sList =  supplementsMaterialMapper.getCustomerSupplements(reportNo,caseTimes);
        if(CollectionUtil.isEmpty(sList)){
            return null;
        }
        List<SupplementsMaterialDTO> resultList = new ArrayList<>();
        for (SupplementsMaterialEntity entity : sList) {
            SupplementsMaterialDTO dto = new SupplementsMaterialDTO();
            BeanUtils.copyProperties(entity,dto);
            resultList.add(dto);
        }
        return resultList;
    }

    /**
     * 获取补材任务短信通知列表
     *
     * @param smsType
     * @param days
     * @return
     */
    @Override
    public List<SupplementsMaterialDTO> getSmsNotificationTask(String smsType, Integer days) {
        List<SupplementsMaterialDTO> dtoList = new ArrayList<>();
        List<SupplementsMaterialEntity> entityList = supplementsMaterialMapper.getSmsNotificationTask(smsType,days);
        if (CollectionUtil.isEmpty(entityList)) {
            return dtoList;
        }

        for (SupplementsMaterialEntity entity : entityList) {
            SupplementsMaterialDTO dto = new SupplementsMaterialDTO();
            BeanUtils.copyProperties(entity,dto);
            dtoList.add(dto);
        }
        return dtoList;
    }
}
