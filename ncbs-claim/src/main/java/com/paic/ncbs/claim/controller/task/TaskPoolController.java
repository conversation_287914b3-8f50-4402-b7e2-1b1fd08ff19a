package com.paic.ncbs.claim.controller.task;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.settle.VerifyInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.model.vo.user.DepartmentUserVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;


@Api(tags = "调度-案件池")
@RestController
@RequestMapping("/who/app/taskPoolAction")
@Slf4j
public class TaskPoolController extends BaseController {

    @Autowired
    TaskPoolService taskPoolService;

    @Autowired
    CacheService cacheService;

    @Qualifier("whoTaskInfoService")
    @Autowired
    TaskInfoService taskInfoService;

    @Autowired
    BpmService bpmService;

    @Autowired
    private DepartmentUserMapper departmentUserMapper;

    @ApiOperation("调度--获取所有任务类型")
    @GetMapping("/getTaskTypeMap")
    public ResponseResult<Object> getTaskTypeMap(){
        return ResponseResult.success(BpmConstants.TASK_MAP);
    }

    @ApiOperation("工作台--获取任务类型")
    @GetMapping("/getTaskTypeMap4WorkBench")
    public ResponseResult<Object> getTaskTypeMap4WorkBench() {
        List<UserGradeInfoDTO> userGradeInfoDTOS = new ArrayList<>();
        try {
            userGradeInfoDTOS = cacheService.queryUserGradeList(WebServletContext.getUserId(), WebServletContext.getDepartmentCode());
            log.info("gradeMap-获取用户岗位-userGradeInfoDTOS{}", JSON.toJSONString(userGradeInfoDTOS));
        } catch (Exception e) {
            log.info("getTaskTypeMap4WorkBench-获取用户岗位异常",e);
        }
        if (CollectionUtils.isEmpty(userGradeInfoDTOS)){
            return ResponseResult.success();
        }
        List<String> gradeNames = userGradeInfoDTOS.stream().map(UserGradeInfoDTO::getGradeName).collect(Collectors.toList());
        Map<String, String> taskMap = new HashMap<>();
        Map<String, String> gradeMap = NcbsConstant.GRADE_MAP;
        LogUtil.audit("gradeMap-获取用户岗位{}", JSON.toJSONString(gradeMap));
        List<String> bpmKeys = new ArrayList<>();
        gradeMap.forEach((k,v)->{
            if (CollectionUtils.containsAny(gradeNames, Arrays.asList(v.split(",")))){
                bpmKeys.add(k);
            }
            /*if (gradeNames.contains(v)){
                bpmKeys.add(k);
            } else if (BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS.equals(k)
                    && CollectionUtils.containsAny(gradeNames, Arrays.asList(v.split(",")))) {
                // 客户补材支持三个岗位，调度权限那边先不改了。
                bpmKeys.add(k);
            }*/
        });
        for (String bpmKey : bpmKeys) {
            taskMap.put(bpmKey,BpmConstants.TASK_MAP.get(bpmKey));
        }
        //taskMap.put(BpmConstants.OC_COMMUNICATE,BpmConstants.TASK_MAP.get(BpmConstants.OC_COMMUNICATE));
        LogUtil.audit("gradeMap-获取用户岗位bpmKeys{}", JSON.toJSONString(bpmKeys));
        return ResponseResult.success(taskMap);
    }


    @ApiOperation("调度--查询案件任务列表")
    @PostMapping("/getTaskList")
    public ResponseResult<Object> getTaskListByPage(@RequestBody TaskInfoVO vo){
        try{
            return ResponseResult.success(taskPoolService.getNotDealTaskList(vo));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }

    }

    @ApiOperation("调度--案件任务分派/改派")
    @PostMapping("/dispatchTaskToUser")
    public ResponseResult<Object> dispatchTaskToUser(@RequestBody TaskInfoVO vo) throws GlobalBusinessException {
        if(vo.getTaskIdList() == null || vo.getTaskIdList().isEmpty()){
            return ResponseResult.fail(ErrorCode.Core.CAN_NOT_NULL,"案件信息不可为空");
        }
        String newUserId = vo.getAssignee();
        if(StringUtils.isEmptyStr(newUserId)){
            return ResponseResult.fail(ErrorCode.Core.CAN_NOT_NULL,"分配人不可为空");
        }
        String newUserName = vo.getAssigneeName();
        if(StringUtils.isEmptyStr(newUserName)){
            return ResponseResult.fail(ErrorCode.Core.CAN_NOT_NULL,"分配人不可为空");
        }
        String departmentCode = vo.getDepartmentCode();
        if(StringUtils.isEmptyStr(departmentCode)){
            return ResponseResult.fail(ErrorCode.Core.CAN_NOT_NULL,"权限机构不可为空");
        }
        try{
            taskPoolService.dispatchTask(vo.getTaskIdList(),newUserId,newUserName,departmentCode);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
       //添加任务提醒
        taskPoolService.addNoticesList(vo.getTaskIdList(),newUserId);
        return ResponseResult.success();
    }

    @ApiOperation("工作台--案件任务主动认领")
    @PostMapping("/claimTaskToSelf/{id}")
    public ResponseResult<Object> claimTaskToSelf(@PathVariable String id) throws GlobalBusinessException {
        UserInfoDTO user = WebServletContext.getUser();
        log.info("user信息={}", JsonUtils.toJsonString(user));
        taskPoolService.reAssign(id,user.getUserCode(),user.getUserName(),WebServletContext.getDepartmentCode());
        return ResponseResult.success();
    }
    @ApiOperation("工作台--核赔认领前校验")
    @PostMapping("/confirmVerify")
    //核赔认领前校验核赔人是否有改案件的核赔记录
    public ResponseResult<Object> confirmVerify(@RequestBody VerifyInfoVO verifyInfoVO) throws GlobalBusinessException {
        UserInfoDTO user = WebServletContext.getUser();
        log.info("user信息={}", JsonUtils.toJsonString(user));
        TaskInfoVO vo = new TaskInfoVO();
        vo.setReportNo(verifyInfoVO.getReportNo());
        vo.setCaseTimes(verifyInfoVO.getCaseTimes());
        vo.setAssignee(user.getUserCode());
        //查询核赔人是否有改案件的核赔记录，返回结果
        log.info("查询核赔人是否有改案件的核赔记录，请求数据={}", JsonUtils.toJsonString(vo));
        return ResponseResult.success(taskPoolService.isExistsVerifyRecord(vo));
    }

    @ApiOperation("调度--可被分派人员姓名搜索")
    @GetMapping(value = "/searchTaskDealUser")
    public ResponseResult<Object> searchTaskDealUser(@RequestParam(value = "taskDefinitionBpmKey")String taskDefinitionBpmKey,
                                                     @RequestParam(value = "departmentCode")String departmentCode) throws NcbsException {
        return ResponseResult.success(taskPoolService.searchTaskDealUser(departmentCode,taskDefinitionBpmKey));
    }

    @ApiOperation("调度--可被分派人员信息查询")
    @GetMapping(value = "/getTaskDealUser")
    public ResponseResult<Object> getTaskDealUser(
            @RequestParam(value = "taskDefinitionBpmKey")String taskDefinitionBpmKey,
            @RequestParam(value = "departmentCode")String departmentCode,
            @RequestParam(value = "userCode",required = false)String userCode){
        try{
            return ResponseResult.success(taskPoolService.getTaskDealUser(taskDefinitionBpmKey,departmentCode,userCode));
        }catch (NcbsException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("获取处理机构")
    @GetMapping(value = "/getSelectDepartmentList4Query")
    public ResponseResult<Object> getSelectDepartmentList4Query() {
        String departmentCode = WebServletContext.getDepartmentCode();
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            departmentCode = departmentUserVO.getDepartmentCode();
        }
        return ResponseResult.success(taskPoolService.getSelectDepartmentList4Query(departmentCode));

    }

    @ApiOperation("获取所有机构")
    @GetMapping(value = "/getAllDepartmentList4Query")
    public ResponseResult<Object> getAllDepartmentList4Query() {
        String departmentCode = ConfigConstValues.HQ_DEPARTMENT;
        return ResponseResult.success(taskPoolService.getSelectDepartmentList4Query(departmentCode));

    }

    @ApiOperation("获取可分配调度的机构列表-暂弃用")
    @GetMapping(value = "/getSelectDepartmentList4Dispatch")
    public ResponseResult<Object> getSelectDepartmentList4Dispatch(@RequestParam(value = "departmentCode",required = false)String departmentCode) {

        return ResponseResult.success(taskPoolService.getSelectDepartmentList4Dispatch(departmentCode));

    }

    @ApiOperation("获取沟通分配的机构列表")
    @GetMapping(value = "/getSelectDepartmentList4Communicate")
    public ResponseResult<Object> getSelectDepartmentList4Communicate() {
        return ResponseResult.success(taskPoolService.getSelectDepartmentList4Dispatch(WebServletContext.getDepartmentCode()));
    }

    @GetMapping(value = "/addPayBackModifyTask")
    public ResponseResult<Object> addPayBackModifyTask(@RequestParam(value = "reportNo")String reportNo,
                                               @RequestParam(value = "caseTimes")Integer caseTimes){
        bpmService.startProcess_oc(reportNo,caseTimes,BpmConstants.OC_PAY_BACK_MODIFY);
        return ResponseResult.success();
    }

    @GetMapping(value = "/addPayBackModifyReviewTask")
    public ResponseResult<Object> addPayBackModifyReviewTask(@RequestParam(value = "reportNo")String reportNo,
                                               @RequestParam(value = "caseTimes")Integer caseTimes){
        bpmService.startProcess_oc(reportNo,caseTimes,BpmConstants.OC_PAY_BACK_MODIFY_REVIEW);
        return ResponseResult.success();
    }

    @ApiOperation("获取可分配调度的机构列表")
    @GetMapping(value = "/getSelectDepartmentList")
    public ResponseResult<Object> getSelectDepartmentList() {

        return ResponseResult.success(taskPoolService.getSelectDepartmentList());

    }


}
