package com.paic.ncbs.claim.service.pay;

import com.paic.ncbs.claim.model.dto.pay.BatchPaymentDTO;
import com.paic.ncbs.claim.model.dto.pay.MergePaymentVO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.model.dto.endcase.ClmsPayModifyRecordDTO;

import java.math.BigDecimal;
import java.util.List;

public interface PaymentItemService {

    void addPaymentItem(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getPaymentItem(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getHisPaymentItem(PaymentItemDTO paymentItemDTO);

    void updatePaymentItem(PaymentItemDTO paymentItemDTO);

    void updateCompensateNoPaymentItem(String reportNo, Integer caseTimes,String claimType);

    void failurePaymentItem(PaymentItemDTO paymentItemDTO);

    void delPaymentItem(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> addPaymentItemList(List<PaymentItemDTO> paymentItemList);

    List<PaymentItemDTO> addPaymentItemDataList(List<PaymentItemComData> paymentItemList);

    /**
     * 共保分摊支付项
     * @return
     */
    List<PaymentItemComData> splitCoinsureItem(List<CoinsureInfoDTO> coinsureList, List<PaymentItemComData> payItemList);

    List<PaymentItemComData> getPaymentItemList(PaymentItemComData paymentItemComData);


    List<PaymentItemDTO> paydInfo(PaymentItemDTO exchangePaymentItem);

    void updatePaymentItemStatus(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getPrePaymentItem(PaymentItemDTO paymentItemDTO);

    void modifyPayBackItem(PaymentInfoVO paymentInfoVO, PaymentItemDTO itemDTO);

    void submitPayBackModifyReview(String idClmPaymentItem, ClmsPayModifyRecordDTO clmsPayModifyRecordDTO, PaymentItemDTO itemDTO);

    void submitBatchPayBackModifyReview(String batchNo, ClmsPayModifyRecordDTO clmsPayModifyRecordDTO, MergePaymentVO mergePaymentVO);

    int getPaymentItemCount(PaymentItemDTO itemDTO);

    /**
     * 根据报案号和赔付次数（不等于当次的次数）
     * @param paymentItemDTO 入参
     * @return 出参
     */
    List<PaymentItemDTO> getPaymentItemByReportNo(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getPaymentItemByReportNoAndPayType(PaymentItemDTO paymentItemDTO);

    void checkAndPayFee(String reportNo, Integer caseTimes);

    BigDecimal getLastPaymentAmount(PaymentItemDTO paymentItemDTO);

    /**
     * 保存监管赔付中间表
     * @param reportNo
     * @param caseTimes
     */
    void saveCompensationIntermediateData(String reportNo, Integer caseTimes);

    void delPaymentItemByInfo(String idClmPaymentInfo);
    //获取包含追偿的支付项
    List<PaymentItemDTO> getAllPaymentItem(PaymentItemDTO paymentItemDTO);

    List<PaymentItemDTO> getAllPaymentItemFee(PaymentItemDTO paymentItemDTO);

    // 添加批量查询方法
    List<PaymentItemDTO> getPaymentItemsByIds(List<String> paymentItemIds);
}
