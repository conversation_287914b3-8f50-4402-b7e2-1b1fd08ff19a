package com.paic.ncbs.claim.service.indicators;

import com.paic.ncbs.claim.common.constant.IndicatorEnum;
import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;

/**
 * <p>
 * 理赔案件时效运行日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface IClmsCaseIndicatorLogService extends IService<ClmsCaseIndicatorLog> {

    /**
     * 查询上次计算成功的时效计算日志
     *
     * @param indicatorEnum 指标枚举
     * @return ClmsCaseIndicatorLog
     */
    ClmsCaseIndicatorLog selectLastSuccess(IndicatorEnum indicatorEnum);

    /**
     * 生成新的执行日志
     *
     * @param lastLog
     * @param now
     * @param result
     * @param message
     */
    void generateNewLog(ClmsCaseIndicatorLog lastLog, LocalDateTime now, boolean result, String message);
}
