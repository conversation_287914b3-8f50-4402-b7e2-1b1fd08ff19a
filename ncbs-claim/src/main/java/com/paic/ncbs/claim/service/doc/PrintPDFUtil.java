package com.paic.ncbs.claim.service.doc;

import com.alibaba.fastjson.JSONObject;
import com.lowagie.text.DocumentException;
import com.paic.ncbs.base.util.StringUtils;
import com.paic.ncbs.print.util.GeneratePDFUtil;
import com.paic.ncbs.print.util.ITextRendererFactory;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;
import org.xhtmlrenderer.util.XRLog;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;
import java.util.Map;

@Component
public class PrintPDFUtil extends GeneratePDFUtil {

    private static final Logger log = LoggerFactory.getLogger(PrintPDFUtil.class);


    public <T> byte[] generatePDFFromHTML(String htmlStr, T object) throws IOException, TemplateException {
        if (StringUtils.isBlank(htmlStr)) {
            throw new RuntimeException("传入的要转换的html文档字符串不能为空");
        } else {
            htmlStr = this.replaceHtml(htmlStr);
            StringTemplateLoader stringLoader = new StringTemplateLoader();
            stringLoader.putTemplate("myTemplate", htmlStr);
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
            cfg.setTemplateLoader(stringLoader);
            cfg.setDefaultEncoding("UTF-8");
            cfg.setLogTemplateExceptions(false);
            cfg.setWrapUncheckedExceptions(true);
            cfg.setFallbackOnNullLoopVariable(false);
            Template template = cfg.getTemplate("myTemplate");
            Map<String, Object> variables = (Map) JSONObject.parseObject(JSONObject.toJSONString(object), Map.class);
            StringWriter outTmp = new StringWriter();
            template.process(variables, outTmp);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            this.generatePDF(outTmp.toString(), baos);
            return baos.toByteArray();
        }
    }

    public void generatePDF(String htmlStr, OutputStream out) {
        if (StringUtils.isBlank(htmlStr)) {
            throw new RuntimeException("传入的要转换的html文档字符串不能为空");
        } else {
            String fontPath = "/pdf_font/";
            XRLog.setLoggingEnabled(false);
            ITextRenderer renderer = ITextRendererFactory.getITextRenderer();
            ITextFontResolver fontResolver = renderer.getFontResolver();

            try {
                fontResolver.addFont(fontPath + "simsun.ttc", "Identity-H", false);
                fontResolver.addFont(fontPath + "simhei.ttf", "Identity-H", false);
                fontResolver.addFont(fontPath + "simkai.ttf", "Identity-H", false);
                htmlStr = this.replaceHtml(htmlStr);
                renderer.setDocumentFromString(htmlStr);
                renderer.layout();
                renderer.createPDF(out);
            } catch (IOException | DocumentException e) {
                log.error("根据htmlStr生成PDF出错", e);
            } finally {
                try {
                    if (out != null) {
                        out.flush();
                        out.close();
                    }
                } catch (IOException e) {
                    log.error("关闭PDF输出流出错", e);
                }

            }

        }
    }

    private String replaceHtml(String htmlStr) {
        return htmlStr.replace("&nbsp;", " ").replace("&quot;", "\"");
    }
}
