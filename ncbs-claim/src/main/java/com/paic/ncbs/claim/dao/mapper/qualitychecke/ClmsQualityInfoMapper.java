package com.paic.ncbs.claim.dao.mapper.qualitychecke;

import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.qualitychecke.QualityInfoDetailVO;
import com.paic.ncbs.claim.dao.entity.qualitychecke.QualityInfoWithDetailVO;
import com.paic.ncbs.claim.dao.entity.qualitychecke.QualityQueryVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
/**
 * <p>
 * 案件质检信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@MapperScan
public interface ClmsQualityInfoMapper extends BaseMapper<ClmsQualityInfo> {
    /**
     * 根据ID查询质检信息
     * @param id 质检信息ID
     * @return 质检信息实体
     */
    ClmsQualityInfo selectQualityInfoById(@Param("id") String id);

    /**
     * 查询当前账号所有质检信息
     * @param qinspector 质检员ID
     * @return 质检信息列表
     */
    List<ClmsQualityInfo> selectAllQualityInfo(@Param("qinspector") String qinspector);

    /**
     * 根据条件查询质检信息
     * @param condition 查询条件
     * @return 质检信息列表
     */
    List<ClmsQualityInfo> selectQualityInfoByCondition(ClmsQualityInfo condition);

    /**
     * 根据多种条件查询质检信息
     * @param queryVO 查询条件
     * @return 质检信息列表
     */
    List<QualityInfoWithDetailVO> selectQualityInfoByConditions(QualityQueryVO queryVO);
    /**
     * 更新质检信息
     * @param clmsQualityInfo 质检信息实体
     * @return 更新结果
     */
    int updateQualityInfo(ClmsQualityInfo clmsQualityInfo);

    /**
     * 根据ID删除质检信息
     * @param id 质检信息ID
     * @return 删除结果
     */
    int deleteQualityInfoById(@Param("id") String id);

    /**
     * 插入质检信息
     * @param clmsQualityInfo 质检信息实体
     * @return 插入结果
     */
    int insertQualityInfo(ClmsQualityInfo clmsQualityInfo);

    /**
     * 查询表中最大的serialNo
     * @return 最大的serialNo
     */
    String selectMaxSerialNo(String reportNo, Short caseTimes);

    /**
     * 查询表中最大的serialNo
     * @return 最大的serialNo
     */
    String selectMaxBatchNoByDate(String batchNoPrefix);

    /**
     * 根据报案号、赔付次数和ID关联查询质检信息及详情
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param id 主键ID
     * @return 质检信息及详情联合查询结果
     */
    QualityInfoDetailVO selectQualityInfoDetailByCondition(
            @Param("reportNo") String reportNo,
            @Param("caseTimes") Short caseTimes,
            @Param("id") String id);

    /**
     * 根据报案号和赔付次数更新质检信息
     * @param clmsQualityInfo 质检信息实体
     * @return 更新结果
     */
    int updateQualityInfoByReportNoAndCaseTimes(ClmsQualityInfo clmsQualityInfo);

    List<ClmsQualityInfo> selectQualityInfoByBatch(String batchno, String qinitiator);

    ClmsQualityInfo selectQualityInfoWithMaxQiserNo(String reportNo, Short caseTimes);
}
