package com.paic.ncbs.claim.service.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsLegalLiabilityClassInfo;

import java.util.List;

/**
 * 法律责任及其他信息(ClmsLegalLiabilityClassInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:47
 */
public interface ClmsLegalLiabilityClassInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsLegalLiabilityClassInfo queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    List<ClmsLegalLiabilityClassInfo> queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsLegalLiabilityClassInfo 实例对象
     * @return 实例对象
     */
    ClmsLegalLiabilityClassInfo insert(ClmsLegalLiabilityClassInfo clmsLegalLiabilityClassInfo);

    /**
     * 修改数据
     *
     * @param clmsLegalLiabilityClassInfo 实例对象
     * @return 实例对象
     */
    ClmsLegalLiabilityClassInfo update(ClmsLegalLiabilityClassInfo clmsLegalLiabilityClassInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    void deleteByCondition(String reportNo, int caseTimes);
}
