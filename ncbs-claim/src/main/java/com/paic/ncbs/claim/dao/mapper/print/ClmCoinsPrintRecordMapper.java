package com.paic.ncbs.claim.dao.mapper.print;

import com.paic.ncbs.claim.model.dto.print.ClmCoinsPrintRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 共保摊回通知书打印日志记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ClmCoinsPrintRecordMapper extends BaseMapper<ClmCoinsPrintRecord> {

    ClmCoinsPrintRecord selectByHis(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes,@Param("claimType") String claimType);

    void updatePrintLog(ClmCoinsPrintRecord coinsPrintRecord);
}
