package com.paic.ncbs.claim.dao.mapper.fee;

import com.paic.ncbs.claim.dao.entity.coinsurance.SettleCoinsReqVo;
import com.paic.ncbs.claim.model.dto.fee.FeeCostDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.FeeInvoiceSendPaymentDTO;
import com.paic.ncbs.claim.model.dto.pay.InvoiceTypeTaxInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PrePaymentVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface FeePayMapper {

    List<FeePayDTO> getFeePays(FeePayDTO feePay);

    List<FeePayDTO> getReplevyFeePays(FeePayDTO feePay);

    void delPrepayFee(@Param("clmPaymentItemId") List<String> clmPaymentItemId);

    void insertFeePay(FeeCostDTO feeCost);

    void updateFeePay(FeeCostDTO feeCost);


    void updateClmPaymentItem(FeeInfoDTO feeInfoDTO);

    void addInvoiceInfo(InvoiceInfoDTO invoiceInfo);

    void modifyInvoiceInfo(InvoiceInfoDTO invoiceInfoDTO);

    List<FeePayDTO> getPolicyDepartment(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


    List<FeeInfoDTO> getFeePayByParam(FeePayDTO feePay);

    List<FeeInfoDTO> getPrePayFeeAmountByParam(FeePayDTO feePay);

    /**
     * @description 逻辑删除-将理赔费用设置为失效
     * @param idAhcsFeePay
     */
    void removeFeePay(@Param("idAhcsFeePay") String idAhcsFeePay);

    /**
     * @description 逻辑删除-将理赔费用设置为失效
     * @param idAhcsFeePay
     */
    void delFeePay(@Param("idAhcsFeePay") String idAhcsFeePay);

    /**
     * 根据报案号和赔付次数查询赔付明细
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<FeeInfoDTO> getFeeByClaimType(@Param("reportNo") String reportNo, @Param("caseTimes")Integer caseTimes, @Param("claimType")String claimType, @Param("subTimes")Integer subTimes);


    List<FeeInfoDTO> getSyncPrePayItem(@Param("reportNo") String reportNo, @Param("caseTimes")Integer caseTimes, @Param("claimType")String claimType, @Param("subTimes")Integer subTimes);



    /**
     * 根据报案号，赔付次数，赔付类型查询赔付费用；
     * @param feePay
     * @return
     */
    BigDecimal getFeePayAmount(@Param("feePay") FeePayDTO feePay) ;

    /**
     * 根据id获取
     * @param idAhcsFeePay
     * @return
     */
    FeeInfoDTO getFeePayById(@Param("idAhcsFeePay") String idAhcsFeePay);

    /**
     * @Description: 获取IdClmPaymentInfos
     * @param reportNo
     * @param caseTimes
     * @return
     * 返回类型  List<FeeInfoDTO>
     */
    List<String> getIdClmPaymentInfos(@Param("reportNo") String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     * @Description: 删除发票信息
     * @param idAhcsFeePay
     * 返回类型  void
     */
    void removeInvoiceInfo(String idAhcsFeePay);

    /**
     * @Description: 获取发票详细信息
     * @param invoiceInfo
     * 返回类型  InvoiceInfoDTO
     */
    InvoiceInfoDTO getInvoiceInfo(InvoiceInfoDTO invoiceInfo);

    /**
     * @Description: 通过ID获取发票详细信息
     * @param invoiceInfo
     * 返回类型  InvoiceInfoDTO
     */
    InvoiceInfoDTO getInvoiceInfoById(InvoiceInfoDTO invoiceInfo);


    /**
     * 通过报案号和赔付次数和保单号 查询保单案件记录
     * @Description 方法描述
     * @param reportNo
     * @param caseTimes
     * @param policyNo
     * @return
     * @return CaseBaseDTO
     */
    String getCaseBaseBySome(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes, @Param("policyNo")String policyNo);

    /**
     * 查询拒赔费用总和
     * @Description 方法描述
     * @param reportNo
     * @param caseTimes
     * @return
     * @return String
     */
    String getFeePayForSum(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    void modefyInvoiceInfo(InvoiceInfoDTO invoiceInfoDTO);

    /**
     * 新增批次
     * @Description 方法描述
     * @param batch
     * @return void
     */
    void insertBatch(@Param("batchDto") BatchDTO batch);

    /**
     * 根据报案号赔付次数查询批次细信息
     * @Description 方法描述
     * @param reportNo
     * @param caseTimes
     * @return
     * @return BatchDTO
     */
    BatchDTO getBatchByReportNo(@Param("reportNo")String reportNo,@Param("caseTimes") Integer caseTimes);

    /**
     * @Description: 查询保单的所有费用（不含奖励费）
     * @param reportNo
     * @param caseTimes
     * @param policyNo
     * @return
     * 返回类型  BigDecimal
     */
    BigDecimal getPolicyFeePayAmount(@Param("reportNo")String reportNo,@Param("caseTimes") Integer caseTimes,
                                     @Param("policyNo")String policyNo,@Param("claimType")String claimType) ;


    /**
     * 获取预赔总金额
     * @param reportNo
     * @param caseTimes
     * @return
     * @return String
     */
    String getPrePayAmountForSum(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    /*
     * 查询拒赔模板
     */
    String getEndorseTemplate(@Param("indemnityMode") String indemnityMode);

    /**
     * 根据报案号查询所有保单号
     * @param reportNo
     * @return
     * @return List<String>
     */
    List<String> getAllPolicyByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     * 根据支付信息id(CLM_PAYMENT_INFO表id)查询直接理赔费用id
     * @param feePayDTO: idClmPaymentInfoList（支付信息id集合）
     * @return List<String> 直接理赔费用id
     */
    List<String> getIdFeePayByIdPayinfo(FeePayDTO feePayDTO);

    /**
     * 根据支付信息id(CLM_PAYMENT_INFO表id)查询理赔费用id
     * @param idClmPaymentInfo 支付信息id
     * @return String 直接理赔费用id
     */
    List<String> getIdFeePayByIdPaymentInfo(@Param("idClmPaymentInfo") String idClmPaymentInfo);

    /**
     * 根据报案号赔付次数查询理赔费用id
     * @param reportNo(报案号)、caseTimes（赔付次数）
     */
    List<String> getIdFeePayByRnCt(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 删除发票信息
     * @param idAhcsFeePayList
     * 返回类型  void
     */
    void removeInvoiceInfoList(@Param("idAhcsFeePayList") List<String> idAhcsFeePayList);

    /**
     * 删除理赔费用
     * @param idAhcsFeePayList
     */
    void removeFeePayList(@Param("idAhcsFeePayList") List<String> idAhcsFeePayList);

    /**
     * 获取理赔费用总额
     * @param reportNo
     * @param caseTimes
     * @return sumFee 理赔费用总额
     */
    BigDecimal getSumFeePay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * @Description: 方法描述 查历史案件是否有相同的发票号
     * @param invoiceNo
     * @return
     * 返回类型  int
     */
    int hasEqualsInvoiceNo(@Param("invoiceNo") String invoiceNo);

    /**
     * 获取被保险人姓名
     * @param reportNo
     * @return
     * @return String
     */
    String getInsuredNameByReportNo(@Param("reportNo") String reportNo);

    /**
     * 将费用置失效
     * @param reportNo
     * @param caseTimes
     * @param claimType
     */
    void delFeeByReport(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("claimType")String claimType);

    /**
     * 根据保单查询赔案号
     * @param feePay
     * @return
     */
    String getCaseNoByPolicyNo(FeePayDTO feePay);


    /**
     * 获取案件费用类型及金额
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<FeePayDTO> getFeeTypeAmountByReportNo(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    void addFeePayList(@Param("feeList") List<FeeInfoDTO> feeList);

    void delPreFeeBySubTimes(FeeInfoDTO feeInfoDTO);

    List<PrePaymentVO> getPolicyPrePayFeeList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("subTimes")Integer subTimes);

    // 根据iTem表的主键去查询费用、发票相关信息
    List<FeeInfoDTO> getFeePayByIdClmPaymentInfo(@Param("idClmPaymentItem") String idClmPaymentItem);

    void addInvoiceInfoList(@Param("invoiceList") List<InvoiceInfoDTO> invoiceList);

    void delPreInvoiceBySubTimes(FeeInfoDTO feeInfoDTO);

    List<InvoiceInfoDTO> getInvoiceByFeeIds(@Param("feeIdList") List<String> feeIdList);

    /**
     * 修改费用发票
     * @param invoiceCode
     * @param invoiceNo
     * @param returnReason
     * @param idAhcsFeePay
     */
    Integer updateFeeInvoiceBackResult(@Param("invoiceCode") String invoiceCode, @Param("invoiceNo") String invoiceNo, @Param("returnReason") String returnReason, @Param("idAhcsFeePay") String idAhcsFeePay);

    /**
     * 根据payment_item表主键即流水号查询发票信息
     * @param paySerialNo
     * @return
     */
    List<FeeInfoDTO> getInvoiceInfoByPaySerialNo(@Param("paySerialNo") String paySerialNo);

    WorkBenchTaskVO getInvoiceInfoByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId")String taskId);

    WorkBenchTaskVO getReplevyInvoiceInfoByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,@Param("taskId")String taskId);

    List<FeeInvoiceSendPaymentDTO> getInvoiceListByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("idClmPaymentItem") String idClmPaymentItem);
    List<FeeInvoiceSendPaymentDTO> getReplevyInvoiceListByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("idClmPaymentItem") String idClmPaymentItem);
    /**
     * 查询发票类型
     * @param paySerialNo
     * @return
     */
    List<InvoiceTypeTaxInfoDTO> getInvoiceTypeList(@Param("paySerialNo")String paySerialNo);

    /**
     * 根据费用主键查询发票信息数据
     * @param id
     */
    InvoiceInfoDTO getClmsInvoiceInfoById(@Param("id")String id);

    InvoiceInfoDTO getInvoiceInfoById(String feeId);
    List<InvoiceTypeTaxInfoDTO> getReplevyInvoiceTypeList(@Param("paySerialNo")String paySerialNo);

    void updateInvoiceInfoById(InvoiceInfoDTO invoiceInfoDTO);
    List<SettleCoinsReqVo> getCoinsFeeSettle(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                             @Param("payType") String payType,@Param("subTimes")Integer subTimes);

    List<String> selectFeeDutyCodes(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes,@Param("claimType") String claimType);

    List<InvoiceInfoDTO> getInvoiceList(@Param("idList") List<String> idList);

    List<FeeInfoDTO> getInvoiceFeeInfoByPaySerialNo(@Param("paySerialNo") String paySerialNo);

    WorkBenchTaskVO getInvoiceFeeInfoByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId")String taskId);

    List<FeePayDTO> getFeeItemPays(FeePayDTO feePay);

    List<FeeInvoiceSendPaymentDTO> getInvoiceFeeListByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("idClmPaymentItem") String idClmPaymentItem);
}
