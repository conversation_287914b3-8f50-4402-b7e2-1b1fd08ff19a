package com.paic.ncbs.claim.controller.doc;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.PrintConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.doc.PrintParameterDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.print.PrintCalculationVO;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.dto.report.PolicyNoNbsQueryDTO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyPayVO;
import com.paic.ncbs.claim.model.vo.doc.PrintSimpleVo;
import com.paic.ncbs.claim.model.vo.doc.PrintVO;
import com.paic.ncbs.claim.model.vo.doc.TimeOutExportVo;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

@Api(tags = "打印中心-理赔通知书")
@RestController
@RequestMapping("/doc/app/printAction")
@Slf4j
@RefreshScope
public class PrintController extends BaseController {

    @Autowired
    private PrintService printService;

    // 协议赔付
    public static final String PROTOCOL_MODEL = "5";

    @Autowired
    private FileCommonService fileCommonService;

    @Autowired
    private PrintCoreService printCoreService;

    @Autowired
    private InvestigateMapper investigateMapper;


    /********************newcode***********************/
    private void validPrintParam(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(printDutyPayVO.getReportNo())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "案件号");
        } else if (printDutyPayVO.getCaseTimes() == null) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "赔付次数");
        }
    }

    @ApiOperation("查询历史已结案件信息列表")
    @PostMapping(value = "/getHistoryCaseList", produces = {"application/json"})
    public ResponseResult<Object> getHistoryCaseList(@RequestBody WholeCaseVO queryVO) {
        LogUtil.audit("#获取结案信息#入参# reportNo={},caseNo={},reportBatchNo={},policyNo={}", queryVO.getReportNo(), queryVO.getCaseNo(), queryVO.getReportBatchNo(), queryVO.getPolicyNo());
        try {
            this.validParam(queryVO);
            queryVO.setUserId(WebServletContext.getUserId());
            return ResponseResult.success(printService.getHistoryCaseList(queryVO));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }
    private void validParam(WholeCaseVO queryVO){
        boolean isMatch;
        if(StringUtils.isNotEmpty(queryVO.getReportNo())) {
            //只允许有字母和数字
            isMatch = StringUtils.validLetterNumber(queryVO.getReportNo());
            if(!isMatch){
                throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "案件号输入不符合规范");
            }
        }
        if(StringUtils.isNotEmpty(queryVO.getPolicyNo())){
            isMatch = StringUtils.validLetterNumberAndSize(queryVO.getPolicyNo(),"0","20");
            if(!isMatch){
                throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "保单号输入不符合规范");
            }
        }
    }
    @ApiOperation("获取正常赔付通知书信息")
    @PostMapping(value = "/getFormalPay", produces = {"application/json"})
    public ResponseResult<Object> getFormalPay(@RequestBody PrintDutyPayVO printDutyPayVO) {
        LogUtil.audit("#获取正常赔付客户通知书信息#入参# reportNo={},caseTimes={}", printDutyPayVO.getReportNo(), printDutyPayVO.getCaseTimes());
        this.validPrintParam(printDutyPayVO);
        PrintVO printVO = null;
        try {
            printVO = printService.getFormalPayInfo(printDutyPayVO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
        printVO.setDocType(PrintConstValues.DOCTYPE_FORMAL_PAY_BUSI);
        return ResponseResult.success(printVO);
    }

    @ApiOperation("获取协议赔付通知书信息")
    @PostMapping(value = "/getProtocolPay", produces = {"application/json"})
    public ResponseResult<Object> getProtocolPay(@RequestBody PrintDutyPayVO printDutyPayVO) {
        LogUtil.audit("#获取协议赔付客户通知书信息#入参# reportNo={},caseTimes={}", printDutyPayVO.getReportNo(), printDutyPayVO.getCaseTimes());
        this.validPrintParam(printDutyPayVO);
        PrintVO printVO = null;
        try {
            printVO = printService.getProtocolPayPrintInfo(printDutyPayVO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
        printVO.setDocType(PrintConstValues.DOCTYPE_PROTOCOL_PAY_BUSI);
        return ResponseResult.success(printVO);
    }

    @ApiOperation("获取注销客户通知书信息")
    @PostMapping(value = "/getCancel", produces = {"application/json"})
    @ResponseBody
    public ResponseResult<Object> getCancel(@RequestBody PrintDutyPayVO printDutyPayVO) {
        LogUtil.audit("#获取注销客户通知书信息#入参#reportNo={}", printDutyPayVO.getReportNo());
        this.validPrintParam(printDutyPayVO);
        PrintVO printVO = null;
        try {
            printVO = printService.getCancelPrintInfo(printDutyPayVO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
        printVO.setDocType(PrintConstValues.DOCTYPE_BUSI);
        return ResponseResult.success(printVO);
    }

    @ApiOperation("获取拒赔客户通知书信息")
    @PostMapping(value = "/getRefusePay", produces = {"application/json"})
    public ResponseResult<Object> getRefusePay(@RequestBody PrintDutyPayVO printDutyPayVO) {
        LogUtil.audit("#获取拒赔客户通知书信息#入参# reportNo={}", printDutyPayVO.getReportNo());
        this.validPrintParam(printDutyPayVO);
        PrintVO printVO = null;
        try {
            printVO = printService.getRefusePay(printDutyPayVO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
        printVO.setDocType(PrintConstValues.DOCTYPE_REFUSE_PAY_BUSI);
        return ResponseResult.success(printVO);
    }


    @ApiOperation("获取零结客户通知书信息")
    @PostMapping(value = "/getZeroEndPrintInfo", produces = {"application/json"})
    public ResponseResult<Object> getZeroEndPrintInfo(@RequestBody PrintDutyPayVO printDutyPayVO) {
        LogUtil.audit("#获取零结客户通知书信息#入参# reportNo={}", printDutyPayVO.getReportNo());
        this.validPrintParam(printDutyPayVO);
        PrintVO printVO = null;
        try {
            printVO = printService.getZeroEndPrintInfo(printDutyPayVO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
        printVO.setDocType(PrintConstValues.DOCTYPE_ZERO_END_BUSI);
        return ResponseResult.success(printVO);
    }


    @ApiOperation("获取通知书busi/汇总获取信息")
    @PostMapping(value = "/getBusiPrint", produces = {"application/json"})
    public ResponseResult<Object> getBusiPrint(@RequestBody PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException {
        this.validPrintParam(printDutyPayVO);
        LogUtil.audit("通知书busi#入参reportNo={}", printDutyPayVO.getReportNo());
        PrintVO printVO = null;
        WholeCaseBaseDTO wholeCaseBase = printService.getWholeCaseIndemnityStatus(printDutyPayVO.getReportNo(), printDutyPayVO.getCaseTimes());
        if (wholeCaseBase == null) {
            return ResponseResult.success(printVO);
        }
        String conclusion = wholeCaseBase.getIndemnityConclusion();
        String model = wholeCaseBase.getIndemnityModel();
        String docType = null;
        try {
            if (ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED.equals(conclusion)) {
                docType = PrintConstValues.DOCTYPE_ZERO_END_BUSI;
                printVO = printService.getZeroEndPrintInfo(printDutyPayVO);
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_REFUSE.equals(conclusion)) {
                docType = PrintConstValues.DOCTYPE_REFUSE_PAY_BUSI;
                printVO = printService.getRefusePay(printDutyPayVO);
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_PAY.equals(conclusion)) {
                if (StringUtils.isEmptyStr(model)) {
                    docType = PrintConstValues.DOCTYPE_FORMAL_PAY_BUSI;
                    printVO = printService.getFormalPayInfo(printDutyPayVO);
                    //增加是协议赔付还是通融赔付的判断
                } else if(PROTOCOL_MODEL.equals(model)){
                    docType = PrintConstValues.DOCTYPE_PROTOCOL_PAY_BUSI;
                    printVO = printService.getProtocolPayPrintInfo(printDutyPayVO);
                } else {
                    docType = PrintConstValues.DOCTYPE_ACCOMMODATION_PAY_BUSI;
                    printVO = printService.getProtocolPayPrintInfo(printDutyPayVO);
                }
            } else {
                docType = PrintConstValues.DOCTYPE_BUSI;
                printVO = printService.getCancelPrintInfo(printDutyPayVO);
            }
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
        printVO.setDocType(docType);
        printVO.setCompanyName("第三方系统财产保险股份有限公司");//写死？
        printVO.setCaseTimes(printDutyPayVO.getCaseTimes());
        if (StringUtils.isNotEmpty(printVO.getVerifyRemark())) {
            printVO.setVerifyRemark(HtmlUtils.htmlEscape(printVO.getVerifyRemark()));
        }
        if (StringUtils.isNotEmpty(printVO.getChsEndorsement())) {
            printVO.setChsEndorsement(HtmlUtils.htmlEscape(printVO.getChsEndorsement()));
        }
        return ResponseResult.success(printVO);
    }

    //        PrintParameterDTO printParameterDTO
    @ApiOperation("下载")
    @PostMapping(value = "/generatePrintResult", produces = {"application/json"})
    public ResponseResult<Object> generatePrintResult(@RequestBody PrintVO printVO) throws Exception {
        PrintParameterDTO printParameterDTO = new PrintParameterDTO();
        printParameterDTO.setReportNo(printVO.getReportNo());
        printParameterDTO.setCaseTimes(printVO.getCaseTimes());
        try {
            //获取文件field
            String fileId = printService.findFileId(printVO.getReportNo(),printVO.getCaseTimes());
            if(StringUtils.isEmptyStr(fileId)){
                printService.sendPrintCore(printVO.getReportNo(),printVO.getCaseTimes());
                return ResponseResult.fail(ErrorCode.Print.PRINT_FILE_NOT_EXEIST,"文件未生成，请稍后再试");
            }
            UserInfoDTO userDTO = WebServletContext.getUser();
            //fileName
            String url = fileCommonService.getDownloadUrl(fileId,null,userDTO.getUserName());
            printParameterDTO.setDownload(url);
            return ResponseResult.success(printParameterDTO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    @ApiOperation("下载公估委托书")
    @PostMapping(value = "/commissionPrintResult", produces = {"application/json"})
    public ResponseResult<Object> commissionPrintResult(@RequestBody PrintEntrustDTO printEntrustDTO) throws Exception {
        LogUtil.info("下载公估委托书请求参数：{}", JSON.toJSON(printEntrustDTO));
        PrintParameterDTO printParameterDTO = new PrintParameterDTO();
        String now = System.currentTimeMillis()+"";
        try {
            String fileId = printService.findEntrustFileId(printEntrustDTO);
            if(StringUtils.isEmptyStr(fileId)){
                printCoreService.saveCommissionFileAsync(now,null,now,printEntrustDTO);
                return ResponseResult.fail(ErrorCode.Print.PRINT_FILE_NOT_EXEIST,"文件未生成，请稍后再试");
            }
            UserInfoDTO userDTO = WebServletContext.getUser();
            //fileName
            String url = fileCommonService.getDownloadUrl(fileId,null,userDTO.getUserName());
            printParameterDTO.setDownload(url);
            return ResponseResult.success(printParameterDTO);

        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    @ApiOperation("打印")
    @PostMapping(value = "/pintResultByUrl", produces = {"application/json"})
    public void pintResultByUrl(@RequestBody PrintSimpleVo printVO, HttpServletResponse response) throws Exception {
        try {
            //获取文件field
            String fileId = printService.findFileId(printVO.getReportNo(),printVO.getCaseTimes());
            if(StringUtils.isEmptyStr(fileId)){
                printService.sendPrintCore(printVO.getReportNo(),printVO.getCaseTimes());
                throw new GlobalBusinessException(ErrorCode.Print.PRINT_FILE_NOT_EXEIST,"文件未生成，请稍后再试");
            }
            UserInfoDTO userDTO = WebServletContext.getUser();
            //fileName
            String pdfFileURL = fileCommonService.getDownloadUrl(fileId,null,userDTO.getUserName());

            response.setContentType("application/pdf;charset=UTF-8");
            response.setHeader("Content-Disposition", "inline; filename=file");
            ServletOutputStream outputStream = response.getOutputStream();

            HttpRequest request = HttpUtil.createGet(pdfFileURL);
            InputStream stream = request.execute().bodyStream();
            IOUtils.write(IOUtils.toByteArray(stream), outputStream);
            stream.close();
        } catch (GlobalBusinessException e) {
            e.printStackTrace();
            LogUtil.info("接口异常");
        }
    }

    @ApiOperation("打印记录")
    @PostMapping(value = "/PrintRecord")
    public void printRecord(@RequestBody PrintVO printVO) throws Exception {
        printService.addPrintRecord(printVO);
    }

    @ApiOperation("案件超时导出")
    @PostMapping(value = "/timeOutExport")
    public void timeOutExport(@RequestBody TimeOutExportVo timeOutExportVo, HttpServletResponse response) throws Exception {
        String fileName = printService.timeOutExport(timeOutExportVo);
        printService.dealResponse(response,fileName);
    }

    @ApiOperation("美团上门服务-获取电子凭证")
    @PostMapping(value = "getEPolicy")
    public ResponseResult<Object> getEPolicy(@RequestBody PolicyNoNbsQueryDTO policyNoNbsQueryDTO) {

        PrintParameterDTO printParameterDTO = new PrintParameterDTO();
        String url = printService.getEPolicy(policyNoNbsQueryDTO);
        printParameterDTO.setDownload(url);
        return ResponseResult.success(printParameterDTO);

    }
    @ApiOperation("下载计算书")
    @PostMapping(value = "/calculationPrintResult", produces = {"application/json"})
    public ResponseResult<Object> calculationPrintResult(@RequestBody PrintCalculationVO printCalculationVO) throws Exception {
        LogUtil.info("下载计算书");
        PrintParameterDTO printParameterDTO = new PrintParameterDTO();
        try {
            //获取文件field
            String fileId = printService.findCalculationId(printCalculationVO.getReportNo(),printCalculationVO.getCaseTimes());
            if(StringUtils.isEmptyStr(fileId)){
                printService.sendPrintCalculation(printCalculationVO.getReportNo(),printCalculationVO.getCaseTimes());
                return ResponseResult.fail(ErrorCode.Print.PRINT_FILE_NOT_EXEIST,"文件未生成，请稍后再试");
            }
            UserInfoDTO userDTO = WebServletContext.getUser();
            //fileName
            String url = fileCommonService.getDownloadUrl(fileId,null,userDTO.getUserName());
            printParameterDTO.setDownload(url);
            return ResponseResult.success(printParameterDTO);

        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }
    @PostMapping(value = "/calculationPrintResultWord", produces = {"application/json"})
    public void calculationPrintResultWord(@RequestBody PrintCalculationVO printCalculationVO, HttpServletResponse response) throws IOException {
        BufferedOutputStream out = null;
        ByteArrayOutputStream wordout = null;
        String reportNo = printCalculationVO.getReportNo();
        Integer caseTimes = printCalculationVO.getCaseTimes();
        try {
            String templatePath = Objects.requireNonNull(this.getClass().getResource("/")).getPath() + "templates";
            templatePath += "\\calculation.docx";
            log.info("calculationPrintResultWord templatePath:{}", templatePath);
            PrintCalculationVO data = printService.buildCalaculation(reportNo, caseTimes);
            //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            out = new BufferedOutputStream(response.getOutputStream());
            Configure configure = Configure.builder()
                    .bind("detailArr", new LoopRowTableRenderPolicy())
                    .bind("paymentItemList", new LoopRowTableRenderPolicy())
                    .build();
            // 1. 生成 Word 文档字节流
            XWPFTemplate template = XWPFTemplate.compile(templatePath,configure).render(data);
            wordout  = new ByteArrayOutputStream();
            template.write(wordout);
            template.close();
            byte[] wordBytes = wordout.toByteArray();
            ByteArrayInputStream in = new ByteArrayInputStream(wordBytes);

            int len = 0;
            while ((len = in.read()) != -1) {
                out.write(len);
                out.flush();
            }
        } catch (IOException e) {
            LogUtil.error("下载文件异常", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if(wordout  != null){
                    wordout.close();
                }
            } catch (IOException e) {
                LogUtil.error("流关闭异常", e);
            }
        }
    }

}
