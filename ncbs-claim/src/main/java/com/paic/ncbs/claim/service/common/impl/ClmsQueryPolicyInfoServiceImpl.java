package com.paic.ncbs.claim.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 查询保单最新的起止日期，抄单时会根据出险时间抄，可能会抄不到退保后更新的保单终止日，
 * 此处查询保单终止日的作用时理算时要剔除不在保单有效期内的发票
 */
@Slf4j
@Service
public class ClmsQueryPolicyInfoServiceImpl implements ClmsQueryPolicyInfoService {
    @Autowired
    private OcasRequest ocasRequest;
    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;
    @Override
    public PolicyDto getPolicyDto(String policyNo,String reportNo) {
        Map<String,String> param=new HashMap<>();
        PolicyDto dto =new PolicyDto();
        //保单最新起止日期查询
        param.put("policyNo",policyNo);
        param.put("productKind","0");
        param.put("isElecSubPolicyNo","0");
        param.put("umsDisplayFlag", "claim");
        param.put("reportNo",reportNo);
        log.info("入参={}，policyNo保单号={},",JsonUtils.toJsonString(param),policyNo);
        String res = customerInfoStoreSAO.getPolicyInfoByPolicyNo(param);
        log.info("policyNo保单号={},保单起止日期信息返回结果={}",policyNo,res);
        Map resultMap = JSON.parseObject(res, Map.class);
        Map<String, Object> contractDTO = (Map) resultMap.get("contractDTO");
        Map baseInfoMap = (Map) contractDTO.get("baseInfo");
        Map extendInfoMap = (Map) contractDTO.get("extendInfo");
        log.info("入参={}，policyNo保单号={},保单起止日期信息返回结果={}",JsonUtils.toJsonString(param),policyNo,res);
        String insuranceBeginDate = null;
        String insuranceEndDate = null;
        if (baseInfoMap.get("insuranceBeginDate") != null) {
            insuranceBeginDate = StringUtils.stripToEmpty(baseInfoMap.get("insuranceBeginDate").toString());
        }
        if (baseInfoMap.get("insuranceEndDate") != null) {
            insuranceEndDate = StringUtils.stripToEmpty(baseInfoMap.get("insuranceEndDate").toString());
        }

        try {
            Date policyStartDate = DateUtils.parseToFormatDate(insuranceBeginDate, DateUtils.FULL_DATE_STR);
            dto.setPolicyStartDate(policyStartDate);
            Date policyEndDate = DateUtils.parseToFormatDate(insuranceEndDate, DateUtils.FULL_DATE_STR);
            dto.setPolicyEndDate(policyEndDate);
            dto.setProsecutionPeriod(Objects.nonNull(extendInfoMap.get("prosecutionPeriod")) ? Integer.parseInt(extendInfoMap.get("prosecutionPeriod").toString()) : 0);
            dto.setExtendReportDate(Objects.nonNull(extendInfoMap.get("extendReportDate")) ? Integer.parseInt(extendInfoMap.get("extendReportDate").toString()) : 0);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return dto;
    }
}
