package com.paic.ncbs.claim.service.fileupload;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.api.DocumentListDTO;
import com.paic.ncbs.claim.model.dto.api.QueryDocumentInfoDTO;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentReqDTO;
import com.paic.ncbs.claim.model.dto.api.UploadDocumentResDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileRealDownLoadAddressInfoDTO;
import com.paic.ncbs.claim.model.vo.doc.DocSmallTypeVO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;

import java.io.UnsupportedEncodingException;
import java.util.List;


public interface FileUploadService {

    List<FileInfoVO> getDocumentList(String userId, FileInfoDTO fileInfoDTO)throws GlobalBusinessException;

    List<DocSmallTypeVO> getMissingDocNotExempt(String reportNo, Integer caseTimes, String alias, String userId) throws GlobalBusinessException;

    List<FileDocumentDTO> getDocumentListByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    String getDocumentGroupIdByDb(FileInfoDTO fileInfoDTO) throws GlobalBusinessException;

    String addFilesInfo(FileInfoDTO fileInfoDTO) throws GlobalBusinessException;

    UploadDocumentResDTO addFilesInfo(UploadDocumentReqDTO fileInfoDTO);

    List<FileInfoDTO> getFileGroupIdForDocument(FileInfoDTO fileInfoDTO) throws GlobalBusinessException;

    String getSeq();

    boolean removeFileList(FileInfoVO fileInfoVO, String userId) throws GlobalBusinessException;

    boolean modifyDocument(FileInfoDTO fileInfoDTO) throws GlobalBusinessException;

    /**
     * 获取单证真实访问地址
     * @param fileId
     * @return
     */
    FileRealDownLoadAddressInfoDTO getDocumentsRealAddress(String fileId);

    /**
     * 查询单证信息列表
     * @param queryDocumentInfoDto
     * @return
     */
    List<FileDocumentDTO> queryDocumentInfo(QueryDocumentInfoDTO queryDocumentInfoDto);

    List<DocumentListDTO> queryDocumenList(String reportNo, Integer caseTimes);

    List<FileDocumentDTO> queryDocumentList(String reportNo);
    FileDocumentDTO queryDocumentListById(String documentGroupItemsId);
    ResponseResult uploadDocument(byte[] file, String smallType, String reportNo, Integer caseTimes, String name, String id) throws UnsupportedEncodingException;

    ResponseResult uploadDocument(byte[] file, String smallType, String reportNo, Integer caseTimes, String name, UserInfoDTO userInfoDTO) throws UnsupportedEncodingException;

    ResponseResult uploadDocumentByAmort(byte[] file, String smallType, String idRecoveryRecord, String fileName) throws UnsupportedEncodingException;
}
