package com.paic.ncbs.claim.model.dto.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: EX-TANGCHUNHUI001
 * @Description:
 * @Date: 2023-04-17 9:28
 */
@ApiModel("保单信息")
@Data
public class PolicyMainInfo {

    @ApiModelProperty("中介代码")
    private String intermediaryCode;

    @ApiModelProperty("中介协议号")
    private String agreementNo;

    @ApiModelProperty("中介协议号")
    private String solutionCode;

    // 微保："WB00000001" 一期默认这个渠道
    @ApiModelProperty("渠道代码")
    private String channelDetailCode;

    @ApiModelProperty("业务类别，1-直保业务，2-分入业务")
    private String businessType;

    @ApiModelProperty("出单机构")
    private String issueCompany;

    @ApiModelProperty("被保人代码")
    private String insuredCode;

    @ApiModelProperty("被保人名称")
    private String insuredName;

    @ApiModelProperty("投保人代码")
    private String appliCode;

    @ApiModelProperty("投保人名称")
    private String appliName;

    @ApiModelProperty("联共保标志：0非联共保，1主共保，2从共保")
    private String coinsInd;

    @ApiModelProperty("业务归属机构")
    private String companyCode;

    @ApiModelProperty("起保日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date startDate;

    @ApiModelProperty("终保日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date endDate;
    /**
     * 利润中心
     */
    @ApiModelProperty("利润中心")
    private String profitCenterCode;

    //业务来源，nocarcore、(默认为核心nocarcore，非核心保单必传)
    private String policySource;
}

