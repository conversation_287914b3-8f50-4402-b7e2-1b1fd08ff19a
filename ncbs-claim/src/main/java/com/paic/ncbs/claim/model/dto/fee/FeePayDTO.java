package com.paic.ncbs.claim.model.dto.fee;


import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@Data
public class FeePayDTO extends EntityDTO {
    private static final long serialVersionUID = 1L;

    private String policyNo;

    private String productCode;

    private String policyCerNo;

    private String reportNo;

    private Integer caseTimes;

    private String caseNo;

    private String departmentName;

    private String departmentCode;

    private List<FeeInfoDTO> feeInfos;

    private String claimType;

    private Integer subTimes;

    private List<String> idClmPaymentInfoList;

    private BigDecimal feeAmount;

    private String feeType;

	private String feeTypeName;

	private String idClmPaymentInfo;

    private String lossObjectNo;

    /**
     * 共保展示描述文字
     */
    private String coinsuranceDesc;

    /**
     * 费用发票修改标记 0:未修改 1:待修改 2:修改完成 3:临时修改
     */
    private String isModifiedFlag;

    /**
     * task表的taskId，费用退回即支付流水号
     */
    private String taskId;

    private String isFullPay;
    private String dutyCode;
}
