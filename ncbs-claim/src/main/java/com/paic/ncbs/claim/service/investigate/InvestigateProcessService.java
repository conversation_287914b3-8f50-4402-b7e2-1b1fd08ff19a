package com.paic.ncbs.claim.service.investigate;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO;

import java.util.List;


public interface InvestigateProcessService {

    void addInvestigateProcess(InvestigateProcessDTO investigateProcess);

    void addInvestigateProcessList(List<InvestigateProcessDTO> investigateProcessList, String userId);

    void saveOrUpdateInvestigateProcess(InvestigateProcessDTO investigateProcess) throws GlobalBusinessException;

    void deleteInvestigateProcessById(String idAhcsInvestigateProcess);

}