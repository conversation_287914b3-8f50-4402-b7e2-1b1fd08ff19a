package com.paic.ncbs.claim.dao.mapper.qualitychecke;

import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
/**
 * <p>
 * 质检意见明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@MapperScan
public interface ClmsQualityDetailMapper extends BaseMapper<ClmsQualityDetail> {
    /**
     * 根据ID查询质检详情
     * @param id 质检详情ID
     * @return 质检详情实体
     */
    ClmsQualityDetail selectQualityDetailById(@Param("id") String id);

    /**
     * 查询所有质检详情
     * @return 质检详情列表
     */
    List<ClmsQualityDetail> selectAllQualityDetails();

    /**
     * 根据条件查询质检详情
     * @param condition 查询条件
     * @return 质检详情列表
     */
    List<ClmsQualityDetail> selectQualityDetailByCondition(ClmsQualityDetail condition);

    /**
     * 根据serialNo查询质检详情列表
     * @param serialNo 序列号
     * @return 质检详情列表
     */
    List<ClmsQualityDetail> selectQualityDetailsBySerialNo(@Param("serialNo") String serialNo);

    /**
     * 插入质检详情
     * @param clmsQualityDetail 质检详情实体
     * @return 插入结果
     */
    int insertQualityDetail(ClmsQualityDetail clmsQualityDetail);

    /**
     * 根据ID删除质检详情
     * @param id 质检详情ID
     * @return 删除结果
     */
    int deleteQualityDetailById(@Param("id") String id);

    /**
     * 更新质检详情
     * @param clmsQualityDetail 质检详情实体
     * @return 更新结果
     */
    int updateQualityDetail(ClmsQualityDetail clmsQualityDetail);

    /**
     * 根据serialNo删除质检详情
     * @param serialNo 序列号
     * @return 删除结果
     */
    int deleteQualityDetailsBySerialNo(@Param("serialNo") String serialNo);

}
