package com.paic.ncbs.claim.service.indicators.impl;

import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 重开结案时效计算
 */
@Service("Indicator4reopenEndCase")
public class Indicator4reopenEndCase extends  ClmsCaseIndicatorServiceImpl{
    /**
     * empty stub ,活动和非活动全部放在resaveUnstablePart方法
     * @param lastLog
     * @param now
     */
    @Override
    protected void saveStablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {

    }

    @Override
    protected void resaveUnstablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.reopenEndCase(lastLog, now);
    }
}
