package com.paic.ncbs.claim.service.instalment;


import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.instalment.InstalmentFailDTO;

import java.util.List;


public interface InstalmentFailService {

    List<InstalmentFailDTO> findByReportNoAndCaseTimes(String reportNo, Integer caseTimes);

    List<PaymentItemComData> findAllPaymentItem(String reportNo, Integer caseTimes);
}
