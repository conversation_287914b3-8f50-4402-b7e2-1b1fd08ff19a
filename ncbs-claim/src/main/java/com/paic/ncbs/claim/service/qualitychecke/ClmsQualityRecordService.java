package com.paic.ncbs.claim.service.qualitychecke;

import com.baomidou.mybatisplus.extension.service.IService;
import com.paic.ncbs.claim.dao.entity.qualitychecke.*;
import com.paic.ncbs.claim.model.vo.quality.QualityrequstVO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimQuanlityCaseVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;

import java.util.List;

/**
 * <p>
 * 案件质检信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface ClmsQualityRecordService extends IService<ClmsQualityRecord> {
    /**
     * 根据报案号和赔付次数查询质检轨迹记录
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 质检轨迹记录列表
     */
    List<ClmsQualityRecord> selectByReportNoAndCaseTimes(String reportNo, Short caseTimes, String id);

    /**
     * 插入质检轨迹记录
     * @param clmsQualityInfo 质检轨迹记录
     * @return 插入结果
     */
    int insertrecord (ClmsQualityInfo clmsQualityInfo);
}
