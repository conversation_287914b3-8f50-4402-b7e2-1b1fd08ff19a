package com.paic.ncbs.claim.service.noPeopleHurt.impl;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsPropertyLossInfoMapper;
import com.paic.ncbs.claim.dao.entity.clms.ClmsPropertyLossInfo;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsPropertyLossInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 财产损失信息表(ClmsPropertyLossInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:49
 */
@Service("clmsPropertyLossInfoService")
public class ClmsPropertyLossInfoServiceImpl implements ClmsPropertyLossInfoService {
    @Resource
    private ClmsPropertyLossInfoMapper clmsPropertyLossInfoMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsPropertyLossInfo queryById(String id) {
        return this.clmsPropertyLossInfoMapper.queryById(id);
    }

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public List<ClmsPropertyLossInfo> queryByReportNo(String reportNo, int caseTimes) {
        return this.clmsPropertyLossInfoMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsPropertyLossInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsPropertyLossInfo insert(ClmsPropertyLossInfo clmsPropertyLossInfo) {
        clmsPropertyLossInfo.setCreatedBy(WebServletContext.getUserId());
        clmsPropertyLossInfo.setCreatedDate(new Date());
        clmsPropertyLossInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsPropertyLossInfo.setUpdatedDate(new Date());
        clmsPropertyLossInfo.setId(UuidUtil.getUUID());
        this.clmsPropertyLossInfoMapper.insert(clmsPropertyLossInfo);
        return clmsPropertyLossInfo;
    }

    /**
     * 修改数据
     *
     * @param clmsPropertyLossInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsPropertyLossInfo update(ClmsPropertyLossInfo clmsPropertyLossInfo) {
        clmsPropertyLossInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsPropertyLossInfo.setUpdatedDate(new Date());
        this.clmsPropertyLossInfoMapper.update(clmsPropertyLossInfo);
        return this.queryById(clmsPropertyLossInfo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsPropertyLossInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByCondition(String reportNo, int caseTimes) {
        clmsPropertyLossInfoMapper.deleteByCondition(reportNo, caseTimes);
    }
}
