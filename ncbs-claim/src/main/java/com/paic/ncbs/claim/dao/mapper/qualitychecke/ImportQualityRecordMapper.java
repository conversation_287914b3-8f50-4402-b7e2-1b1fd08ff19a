package com.paic.ncbs.claim.dao.mapper.qualitychecke;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.qualitychecke.*;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * <p>
 * 案件质检信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@MapperScan
public interface ImportQualityRecordMapper extends BaseMapper<ClmsQualityInfo> {
    List<ImportQualityRecord> selectQualityInfoByBatch(String batchno, String qinitiator);

    int qualityRecordInsert(ImportQualityRecord clmsQualityInfo);

    void qualityRecordBatchInsert(@Param("list") List<ImportQualityRecord> clmsQualityInfos);

    String selectMaxBatchNoByDate(String batchNoPrefix);
}
