package com.paic.ncbs.claim.service.indicators.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.paic.ncbs.claim.common.constant.IndicatorEnum;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicator;
import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import com.paic.ncbs.claim.dao.mapper.indicators.ClmsCaseIndicatorMapper;
import com.paic.ncbs.claim.service.indicators.IClmsCaseIndicatorLogService;
import com.paic.ncbs.claim.service.indicators.IClmsCaseIndicatorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <p>
 * 理赔案件时效表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public abstract class ClmsCaseIndicatorServiceImpl extends ServiceImpl<ClmsCaseIndicatorMapper, ClmsCaseIndicator> implements IClmsCaseIndicatorService {

    @Resource
    private IClmsCaseIndicatorLogService indicatorLogService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void calcAndSave(IndicatorEnum indicatorEnum) {
        LocalDateTime now = LocalDateTime.now();
        boolean result = true;
        String message = null;
        ClmsCaseIndicatorLog lastLog = indicatorLogService.selectLastSuccess(indicatorEnum);
        try{
            saveStablePart(lastLog, now);
            deleteUnstablePart(indicatorEnum);
            resaveUnstablePart(lastLog, now);
            message = "成功";
        } catch (Exception e){
            result = false;
            message = e.getMessage();
            log.warn("指标:" + indicatorEnum.getCode() + " 计算失败异常：" + message);
            if(StringUtils.isNotEmpty(message) && message.length() > 50){
                message = message.substring(0,49);
            }
            throw  e;
        } finally {
            indicatorLogService.generateNewLog(lastLog, now, result ,message);
        }
    }

    /**
     * 写入稳定数据(指标值不再发生变化的数据部分)
     *
     * @param lastLog 上次成功日志记录
     * @param now     当前时间
     */
    protected abstract void saveStablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now);

    /**
     *  重新写入非稳定数据(指标值发生变化的数据部分)
     *
     * @param lastLog 上一次成功日志记录
     * @param now    系统时间
     */
    protected abstract void resaveUnstablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now);


    /**
     * 删除非稳定数据部分
     *
     * @param indicatorEnum 指标枚举
     */
    private void deleteUnstablePart(IndicatorEnum indicatorEnum) {
        LambdaQueryWrapper<ClmsCaseIndicator> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClmsCaseIndicator::getIndicatorCode, indicatorEnum.getCode());
        wrapper.eq(ClmsCaseIndicator::getValueStable, "0");
        this.baseMapper.delete(wrapper);
    }
}
