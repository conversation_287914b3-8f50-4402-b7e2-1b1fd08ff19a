package com.paic.ncbs.claim.service.noPeopleHurt.impl;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsTravelDelayInfoMapper;
import com.paic.ncbs.claim.dao.entity.clms.ClmsTravelDelayInfo;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsTravelDelayInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 延误信息表(ClmsTravelDelayInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:51
 */
@Service("clmsTravelDelayInfoService")
public class ClmsTravelDelayInfoServiceImpl implements ClmsTravelDelayInfoService {
    @Resource
    private ClmsTravelDelayInfoMapper clmsTravelDelayInfoMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsTravelDelayInfo queryById(String id) {
        return this.clmsTravelDelayInfoMapper.queryById(id);
    }

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public ClmsTravelDelayInfo queryByReportNo(String reportNo, int caseTimes) {
        return this.clmsTravelDelayInfoMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsTravelDelayInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsTravelDelayInfo insert(ClmsTravelDelayInfo clmsTravelDelayInfo) {
        clmsTravelDelayInfo.setCreatedBy(WebServletContext.getUserId());
        clmsTravelDelayInfo.setCreatedDate(new Date());
        clmsTravelDelayInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsTravelDelayInfo.setUpdatedDate(new Date());
        clmsTravelDelayInfo.setId(UuidUtil.getUUID());
        this.clmsTravelDelayInfoMapper.insert(clmsTravelDelayInfo);
        return clmsTravelDelayInfo;
    }

    /**
     * 修改数据
     *
     * @param clmsTravelDelayInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsTravelDelayInfo update(ClmsTravelDelayInfo clmsTravelDelayInfo) {
        clmsTravelDelayInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsTravelDelayInfo.setUpdatedDate(new Date());
        this.clmsTravelDelayInfoMapper.update(clmsTravelDelayInfo);
        return this.queryById(clmsTravelDelayInfo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsTravelDelayInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByCondition(String reportNo, int caseTimes) {
        clmsTravelDelayInfoMapper.deleteByCondition(reportNo, caseTimes);
    }
}
