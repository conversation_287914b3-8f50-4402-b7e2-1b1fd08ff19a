package com.paic.ncbs.claim.service.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsRescueInfo;

import java.util.List;

/**
 * 救援信息表(ClmsRescueInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:49
 */
public interface ClmsRescueInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsRescueInfo queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    List<ClmsRescueInfo> queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsRescueInfo 实例对象
     * @return 实例对象
     */
    ClmsRescueInfo insert(ClmsRescueInfo clmsRescueInfo);

    /**
     * 修改数据
     *
     * @param clmsRescueInfo 实例对象
     * @return 实例对象
     */
    ClmsRescueInfo update(ClmsRescueInfo clmsRescueInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    void deleteByCondition(String reportNo, int caseTimes);
}
