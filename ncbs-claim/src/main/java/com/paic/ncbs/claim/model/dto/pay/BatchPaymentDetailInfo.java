package com.paic.ncbs.claim.model.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 理赔明细信息
 */
public class BatchPaymentDetailInfo {

    /**
     * 支付ID号
     */
    private String businessNo;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 保单币别
     */
    private String currency;

    /**
     * 应付理赔金额(含税金额)
     */
    private BigDecimal planFee;

    /**
     * 理赔税额（元）险种代码下对应的税额
     */
    private BigDecimal taxFee;

    /**
     * 险种代码
     */
    private String riskCode;

    /**
     * 险种大类
     */
    private String kindCode;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品线编码
     */
    private String productLineCode;

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getPlanFee() {
        return planFee;
    }

    public void setPlanFee(BigDecimal planFee) {
        this.planFee = planFee;
    }

    public BigDecimal getTaxFee() {
        return taxFee;
    }

    public void setTaxFee(BigDecimal taxFee) {
        this.taxFee = taxFee;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getKindCode() {
        return kindCode;
    }

    public void setKindCode(String kindCode) {
        this.kindCode = kindCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductLineCode() {
        return productLineCode;
    }

    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode;
    }

}
