package com.paic.ncbs.claim.controller.doc;

import java.util.List;
import java.util.Map;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.duty.AirportInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.AirportInfoVO;
import com.paic.ncbs.claim.model.vo.duty.CityVO;
import com.paic.ncbs.claim.model.vo.duty.ProvinceVO;
import com.paic.ncbs.claim.service.duty.AirportInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 机场信息维护Action
 */
@Controller
@RequestMapping("/app/airportInfoAction")
public class AirportInfoController extends BaseController {

    @Autowired
    private AirportInfoService airportInfoService;

    /**
     * @Description: 根据类型获取机场信息(类型:0-境外,1-境内)
     * @param airportInfoVO
     * @return
     * @return List<Map<String,String>>
     */
    @ResponseBody
    @RequestMapping(value = "/getAirportInfo", method = RequestMethod.POST)
    public ResponseResult<List<Map<String, String>>> getAirportInfo(@RequestBody AirportInfoVO airportInfoVO){
        return ResponseResult.success(airportInfoService.getAirportInfoByType(airportInfoVO));
    }

    /**
     * @Description: 获取机场信息
     * @param airportInfoVO
     * @return
     * @return List<AirportInfoDTO>
     */
    @ResponseBody
    @RequestMapping(value = "/getAirportInfoList", method = RequestMethod.POST)
    public ResponseResult<List<AirportInfoDTO>> getAirportInfoList(@RequestBody AirportInfoVO airportInfoVO){
        return ResponseResult.success(airportInfoService.getAirportInfoList(airportInfoVO));
    }

    /**
     * @Description: 获取存在机场的省或者洲
     * @param airportInfoVO
     * @return
     * @return List<ProvinceVO>
     */
    @ResponseBody
    @RequestMapping(value = "/getProvince", method = RequestMethod.POST)
    public List<ProvinceVO> getProvince(@RequestBody AirportInfoVO airportInfoVO){
        return airportInfoService.getProvince(airportInfoVO.getAirportType());
    }

    /**
     * @Description: 根据省代码或洲代码获取市(国家)
     * @param airportInfoVO
     * @return
     * @return List<CityVO>
     */
    @ResponseBody
    @RequestMapping(value = "/getCity", method = RequestMethod.POST)
    public List<CityVO> getCity(@RequestBody AirportInfoVO airportInfoVO){
        return airportInfoService.getCity(airportInfoVO.getProvinceCode());
    }

    /**
     * @Description: 保存机场信息
     * @param airportInfoDTO
     * @return void
     */
    @ResponseBody
    @RequestMapping(value = "/saveOrUpdateAirportInfo", method = RequestMethod.POST)
    public void saveOrUpdateAirportInfo(@RequestBody AirportInfoDTO airportInfoDTO) {
        String uid = WebServletContext.getUserId();
        airportInfoDTO.setCreatedBy(uid);
        airportInfoDTO.setUpdatedBy(uid);
        airportInfoService.saveOrUpdateAirportInfo(airportInfoDTO);
    }

    /**
     * @Description: 删除机场信息
     * @param idAhcsAirportInfo
     * @return void
     */
    @ResponseBody
    @RequestMapping(value = "/deleteAirportInfo/{idAhcsAirportInfo}", method = RequestMethod.GET)
    public void deleteAirportInfo(@PathVariable("idAhcsAirportInfo") String idAhcsAirportInfo) {
        airportInfoService.deleteAirportInfo(idAhcsAirportInfo, WebServletContext.getUserId());

    }

}