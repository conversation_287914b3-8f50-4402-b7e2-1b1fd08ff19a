package com.paic.ncbs.claim.controller.who.facade;


import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.vo.risk.AccidentPolicyVO;
import com.paic.ncbs.claim.model.vo.risk.RiskInfoVO;
import com.paic.ncbs.claim.service.risk.RiskService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "风险信息")
@RestController
@RequestMapping("/who/app/riskAction")
public class RiskController extends BaseController {

    @Autowired
    private RiskService riskService;

    @ApiOperation(value = "保存风险信息")
    @PostMapping(value = "/addRisk")
    public ResponseResult addRisk(@RequestBody RiskInfoVO riskInfoVO) {
        LogUtil.audit("#保存风险信息");
        riskService.addRisk(riskInfoVO, WebServletContext.getUserId());
        return ResponseResult.success();
    }

    @ApiOperation(value = "获取风险信息")
    @GetMapping(value = "/getRisk/{reportNo}/{caseTimes}")
    public ResponseResult<RiskInfoVO> getRisk(@PathVariable("reportNo") String reportNo,
                              @PathVariable("caseTimes") Integer caseTimes)  {
        LogUtil.audit("#获取风险信息，人参:reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        RiskInfoVO riskInfoVO = riskService.getRisk(reportNo, caseTimes);
        return ResponseResult.success(riskInfoVO);
    }

    @ApiOperation(value = "记录风险信息时初始化页面数据")
    @GetMapping(value = "/initPage/{reportNo}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class)
    })
    public ResponseResult<AccidentPolicyVO> initPage(@ApiParam("报案号") @PathVariable("reportNo") String reportNo) {
        LogUtil.audit("#记录风险信息时初始化页面数据，入参:reportNo={}", reportNo);
        AccidentPolicyVO riskVO = riskService.initPage(reportNo, WebServletContext.getUserId());
        LogUtil.audit("#记录风险信息时初始化页面数据，出参:" + (riskVO != null));
        return ResponseResult.success(riskVO);
    }

}
