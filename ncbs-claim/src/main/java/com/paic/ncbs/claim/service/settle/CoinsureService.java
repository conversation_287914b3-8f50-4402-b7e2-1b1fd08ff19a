package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureRecordDTO;

import java.util.List;
import java.util.Map;

public interface CoinsureService {

	/**
	 * 判断是否是共保案件
	 * @param reportNo
	 * @return
	 * @return boolean
	 */
	boolean isCoinsureCase(String reportNo);

	Map<String,String> getCoinsureDescByReportNo(String reportNo);

	List<CoinsureDTO> getCoinsureByPolicyNo(String policyNo);

	Map<String,List<CoinsureDTO>> getCoinsureListByReportNo(String reportNo);

	Map<String,List<CoinsureDTO>> getCoinsureListByReportNoNew(String reportNo);

	Map<String, List<CoinsureRecordDTO>> getCoinsureRecordByReportNo(String reportNo);

	boolean isMainCoinsureFlag(String reportNo);
}
