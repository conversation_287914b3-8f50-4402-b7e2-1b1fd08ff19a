package com.paic.ncbs.claim.service.qualitychecke;

import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 质检意见明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface ClmsQualityDetailService extends IService<ClmsQualityDetail> {
    ClmsQualityDetail selectQualityDetailById(String id);

    List<ClmsQualityDetail> selectAllQualityDetails();

    List<ClmsQualityDetail> selectQualityDetailByCondition(ClmsQualityDetail condition);

    List<ClmsQualityDetail> selectQualityDetailsBySerialNo(String serialNo);

    int insertQualityDetail(ClmsQualityDetail clmsQualityDetail);

    int deleteQualityDetailById(String id);

    int updateQualityDetail(ClmsQualityDetail clmsQualityDetail);

}
