package com.paic.ncbs.claim.service.antimoneylaundering;

import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;

/**
 * 公司反洗钱信息接口
 */
public interface ClmsAmlCompanyInfoService {

    /**
     * 处理反洗钱信息
     * @param dto
     */
    public void  dealServiceData(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 根据报案号，赔付次数 客户号查询
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto getAmlCompanyInfo(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 根据客户号,报案号，赔付次数删除
     * @param dto
     */
    public void delAmlCompanyInfo(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 根据姓名，证件类型，证件号，查询反洗钱信息
     * @param dto
     */
    ClmsAntiMoneyLaunderingInfoDto getAmlCompanyByNameAndCardTypeAndCardNo(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 根据客户号查询反洗钱信息
     * @param dto
     */
    ClmsAntiMoneyLaunderingInfoDto getAmlCompanyByCustomerNo(ClmsAntiMoneyLaunderingInfoDto dto);
}
