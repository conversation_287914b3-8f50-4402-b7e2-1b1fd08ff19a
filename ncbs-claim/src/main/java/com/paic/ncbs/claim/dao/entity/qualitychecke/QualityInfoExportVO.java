package com.paic.ncbs.claim.dao.entity.qualitychecke;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
public class QualityInfoExportVO {

    @ExcelProperty("序号")
    private String serialNo;

    @ExcelProperty("报案号")
    private String reportNoWithCaseTimes;

    /*@ExcelProperty("立案号")
    private String registNo;*/

    @ExcelProperty("处理机构")
    private String handleCom;

    @ExcelProperty("产品名称")
    private String productName;

    @ExcelProperty("结案日期")
    private String closeDate;

    @ExcelProperty("质检发起人")
    private String qinitiator;

    @ExcelProperty("质检发起时间")
    private String startTime;

    @ExcelProperty("质检结束时间")
    private String endTime;

    @ExcelProperty("质检人")
    private String qinspector;

    @ExcelProperty("质检结果")
    private String qualityResult;

    @ExcelProperty("质检环节")
    private String inspnStage;

    @ExcelProperty("质检规范")
    private String inspnStandard;

    @ExcelProperty("差错等级")
    private String errorLevelDesc;

    @ExcelProperty("偏差金额")
    private BigDecimal diffAmount;

    @ExcelProperty("减损金额")
    private BigDecimal lossAmount;

    @ExcelProperty("赔付金额/估损金额")
    private BigDecimal paymentAmount;

    @ExcelProperty("质检是否结束")
    private String isEndDesc;

    @ExcelProperty("任务状态")
    private String taskStatusDesc;

    /**
     * 将LocalDateTime转换为指定格式的字符串
     * @param dateTime LocalDateTime对象
     * @return 格式化后的字符串，格式为"yyyy-MM-dd HH:mm"
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        return dateTime.format(formatter);
    }
}
