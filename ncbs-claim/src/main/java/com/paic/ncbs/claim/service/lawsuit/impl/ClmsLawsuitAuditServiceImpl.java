package com.paic.ncbs.claim.service.lawsuit.impl;

import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitAudit;
import com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitCase;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseExMapper;
import com.paic.ncbs.claim.dao.mapper.lawsuit.ClmsLawsuitAuditMapper;
import com.paic.ncbs.claim.dao.mapper.lawsuit.ClmsLawsuitCaseMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.bpm.impl.BpmServiceImpl;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.lawsuit.ClmsLawsuitAuditService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 诉讼批复表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Service
public class ClmsLawsuitAuditServiceImpl implements ClmsLawsuitAuditService {

    @Autowired
    private ClmsLawsuitCaseMapper clmsLawsuitCaseMapper;

    @Autowired
    private ClmsLawsuitAuditMapper clmsLawsuitAuditMapper;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private BpmServiceImpl bpmServiceImpl;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private WholeCaseBaseExMapper wholeCaseBaseExMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void litigationApproval(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) {
        if (clmsLawsuitAuditCaseVO == null) {
            throw new GlobalBusinessException("诉讼批复参数为空，请检查！");
        }
        String reportNo = clmsLawsuitAuditCaseVO.getReportNo();
        Integer caseTimes = clmsLawsuitAuditCaseVO.getCaseTimes();
        String lawsuitCaseId = clmsLawsuitAuditCaseVO.getLawsuitCaseId();
        String auditOpinion = clmsLawsuitAuditCaseVO.getAuditOpinion();
        String auditCode = clmsLawsuitAuditCaseVO.getAuditCode();
        String auditName = clmsLawsuitAuditCaseVO.getAuditName();
        String submitter = clmsLawsuitAuditCaseVO.getSubmitter();

        String userId = WebServletContext.getUserId();
        String departmentCode = WebServletContext.getDepartmentCode();
        String userName = WebServletContext.getUserName();

        ClmsLawsuitAuditCaseVO lawsuitCaseById = clmsLawsuitCaseMapper.getLawsuitCaseById(lawsuitCaseId);

        ClmsLawsuitAudit clmsLawsuitAudit = new ClmsLawsuitAudit();
        ClmsLawsuitCase clmsLawsuitCase = new ClmsLawsuitCase();

        BeanUtils.copyProperties(clmsLawsuitAuditCaseVO, clmsLawsuitAudit);

        clmsLawsuitAudit.setLawsuitCaseId(lawsuitCaseId);
        clmsLawsuitAudit.setUpdatedBy(userId);

        if (BaseConstant.STRING_1.equals(auditOpinion)) {
            clmsLawsuitAudit.setStatus(BaseConstant.STRING_2);
            clmsLawsuitAuditMapper.updateClmsLawsuitAudit(clmsLawsuitAudit);

            clmsLawsuitCase.setId(lawsuitCaseId);
            clmsLawsuitCase.setStatus(BaseConstant.STRING_2);
            clmsLawsuitCase.setUpdatedBy(userId);
            clmsLawsuitCaseMapper.updateClmsLawsuitCase(clmsLawsuitCase);

            // 诉讼批复完成，结束任务
            bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_LITIGATION_APPROVAL, lawsuitCaseId);
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_LITIGATION_APPROVAL, "通过", null);
            LogUtil.audit("#诉讼批复工作流完成任务-结束#:reportNo=%s,caseTimes=%s", reportNo, caseTimes);

            // 开始创建诉讼办结任务
            bpmService.startProcessOc(reportNo, caseTimes, BpmConstants.OC_LITIGATION_WAS_CONCLUDED, lawsuitCaseId, submitter, departmentCode);
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_LITIGATION_WAS_CONCLUDED, "发起", null);
            LogUtil.audit("#诉讼办结开始提交至工作流#:reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        } else {
            if (auditCode.equals(userId) && auditName.equals(userName)) {
                throw new GlobalBusinessException("不能移交给自己！");
            }
            if (auditCode.equals(lawsuitCaseById.getSubmitter()) && auditName.equals(lawsuitCaseById.getSubmitterName())){
                throw new GlobalBusinessException("不能移交诉讼登记提交人！");
            }
            clmsLawsuitAuditMapper.updateClmsLawsuitAudit(clmsLawsuitAudit);
            taskInfoMapper.reAssign(lawsuitCaseId, auditCode, auditName, departmentCode);

            // 记录操作日志
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_LITIGATION_APPROVAL, "移交批复", null);
            LogUtil.audit("#移交诉讼批复#:reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void lawsuitWasConcluded(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) {
        if (clmsLawsuitAuditCaseVO == null){
            throw new GlobalBusinessException("诉讼办结参数为空，请检查！");
        }
        String reportNo = clmsLawsuitAuditCaseVO.getReportNo();
        Integer caseTimes = clmsLawsuitAuditCaseVO.getCaseTimes();
        String lawsuitCaseId = clmsLawsuitAuditCaseVO.getLawsuitCaseId();
        String userId = WebServletContext.getUserId();

        ClmsLawsuitAudit clmsLawsuitAudit = new ClmsLawsuitAudit();
        ClmsLawsuitCase clmsLawsuitCase = new ClmsLawsuitCase();

        BeanUtils.copyProperties(clmsLawsuitAuditCaseVO, clmsLawsuitCase);

        clmsLawsuitCase.setId(lawsuitCaseId);
        clmsLawsuitCase.setStatus(BaseConstant.STRING_3);
        clmsLawsuitCase.setUpdatedBy(userId);
        clmsLawsuitCaseMapper.updateClmsLawsuitCase(clmsLawsuitCase);

        clmsLawsuitAudit.setLawsuitCaseId(lawsuitCaseId);
        clmsLawsuitAudit.setUpdatedBy(userId);
        clmsLawsuitAudit.setStatus(BaseConstant.STRING_3);
        clmsLawsuitAuditMapper.updateClmsLawsuitAudit(clmsLawsuitAudit);

        // 诉讼办结完成，结束任务
        bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_LITIGATION_WAS_CONCLUDED, lawsuitCaseId);
        // 更新案件状态
        wholeCaseBaseExMapper.updateCaseLawsuitStatus(reportNo, caseTimes, BaseConstant.STRING_1, clmsLawsuitAuditCaseVO.getCourtCaseNo());
        operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_LITIGATION_WAS_CONCLUDED, "结束", null);
        LogUtil.audit("#诉讼办结工作流完成任务-结束#:reportNo=%s,caseTimes=%s", reportNo, caseTimes);

    }

    @Override
    public List<ClmsLawsuitAuditCaseVO> getClmsLawsuitCase(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) {
        List<ClmsLawsuitAuditCaseVO> clmsLawsuitAudit = clmsLawsuitAuditMapper.getClmsLawsuitAudit(clmsLawsuitAuditCaseVO);
        if (clmsLawsuitAudit != null) {
            return clmsLawsuitAudit;
        }
        return null;
    }

    @Override
    public List<ClmsLawsuitAuditCaseVO> getClmsLawsuitCaseConcluded(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) {
        List<ClmsLawsuitAuditCaseVO> clmsLawsuitAuditConcluded = clmsLawsuitAuditMapper.getClmsLawsuitAuditConcluded(clmsLawsuitAuditCaseVO);
        if (clmsLawsuitAuditConcluded != null) {
            return clmsLawsuitAuditConcluded;
        }
        return null;
    }

}
