package com.paic.ncbs.claim.model.dto.copypolicy;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ClaimDetailCasualtyDrug {
    private Long invoiceSeqNum;//发票序号
    private Long seqNum;//药品序号 从1 开始
    private String drugCode;//药品编码（详见药品编码说明）
    private String drugName;//药品名称
    private BigDecimal selfPayRate;//自付比例
    private BigDecimal selfPayAmt;//金额
    private String remarks;//扣减原因备注 如果有必传 没有可为空

}
