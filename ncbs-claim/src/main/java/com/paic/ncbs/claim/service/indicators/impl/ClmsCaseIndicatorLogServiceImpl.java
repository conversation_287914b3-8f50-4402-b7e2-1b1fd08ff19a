package com.paic.ncbs.claim.service.indicators.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.paic.ncbs.claim.common.constant.IndicatorEnum;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import com.paic.ncbs.claim.dao.mapper.indicators.ClmsCaseIndicatorLogMapper;
import com.paic.ncbs.claim.service.indicators.IClmsCaseIndicatorLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 * 理赔案件时效运行日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class ClmsCaseIndicatorLogServiceImpl extends ServiceImpl<ClmsCaseIndicatorLogMapper, ClmsCaseIndicatorLog> implements IClmsCaseIndicatorLogService {

    /**
     * 查询上次计算成功的时效计算日志
     *
     * @param indicatorEnum 指标枚举
     * @return 上次成功的指标计算日志
     */
    public ClmsCaseIndicatorLog selectLastSuccess(IndicatorEnum indicatorEnum) {
        LambdaQueryWrapper<ClmsCaseIndicatorLog> wrapper = new LambdaQueryWrapper<ClmsCaseIndicatorLog>();
        wrapper.eq(ClmsCaseIndicatorLog::getIndicatorCode, indicatorEnum.getCode())
                .eq(ClmsCaseIndicatorLog::getRunResult, "1")
                .orderByDesc(ClmsCaseIndicatorLog::getSysUtime)
                .last("limit 1");
        ClmsCaseIndicatorLog lastSuccess = this.getBaseMapper().selectOne(wrapper);
        // 未找到执行成功的，设置首次执行的逻辑
        if(lastSuccess==null){
            lastSuccess = new ClmsCaseIndicatorLog();
            lastSuccess.setCreatedBy("System");
            lastSuccess.setIndicatorCode(indicatorEnum.getCode());
            lastSuccess.setIndicatorName(indicatorEnum.getName());
            lastSuccess.setEndTime(DateUtil.parseLocalDateTime("2000-01-01 00:00:00"));
        }
        return lastSuccess;
    }

    /**
     * 生成新的指标计算日志
     *
     * @param lastLog 上次成功的指标计算日志
     * @param now 当前时间
     * @param result 执行结果
     * @param message 执行结果信息
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void generateNewLog(ClmsCaseIndicatorLog lastLog, LocalDateTime now, boolean result, String message) {
        ClmsCaseIndicatorLog newLog = new ClmsCaseIndicatorLog();
        newLog.setCreatedBy("System"); // 自动批量计算， 非人工调度
        newLog.setUpdatedBy("System");
        newLog.setIndicatorCode(lastLog.getIndicatorCode());
        newLog.setIndicatorName(lastLog.getIndicatorName());
        newLog.setStartTime(lastLog.getEndTime());
        newLog.setEndTime(now);
        newLog.setRunResult(result?"1":"0");
        if(StringUtils.isNotEmpty(message) && message.length() > 50 ){
            message = message.substring(0,49);
        }
        newLog.setErrorInfo(message);
        this.save(newLog);
    }
}
