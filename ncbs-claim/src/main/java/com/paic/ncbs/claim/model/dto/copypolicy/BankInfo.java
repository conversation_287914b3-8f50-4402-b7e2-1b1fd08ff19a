package com.paic.ncbs.claim.model.dto.copypolicy;

import lombok.Data;

@Data
public class BankInfo {
    private String payType;//付款方式（0003;工商银行转账 9999:线下付款）
    private String clientName;//开户名
    private String clientIdType;//证件类型
    private String clientId;//证件号码
    private String bankCd;//银行编码
    private String bankNo;//银行账号
    private String bankNm;//银行名称
    private String accountTypeCode;//账户类型：0：对公账户；1：个人账户
}
