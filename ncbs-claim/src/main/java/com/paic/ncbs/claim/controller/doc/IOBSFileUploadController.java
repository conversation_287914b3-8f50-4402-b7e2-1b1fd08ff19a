package com.paic.ncbs.claim.controller.doc;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Collections;

@Slf4j
@RestController
@RequestMapping("/doc/app/iobsFileUpload")
public class IOBSFileUploadController extends BaseController {

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @PostMapping(value = "/uploadFileToIntranetIOBS")
    public ResponseResult<Object> uploadFileToIntranetIOBS(HttpServletRequest request){
        String fileId = "";
        try {
            MultipartFile file = ((MultipartHttpServletRequest)request).getFile("file");
            assert file != null;
            fileId = iobsFileUploadService.uploadFileToFilePlatform(file.getName(), file.getBytes());
        } catch (IOException e) {
            log.error("上传文件至iobs失败!", e);
            return ResponseResult.fail(ErrorCode.Core.THROW_EXCEPTION_MSG,"上传文件至iobs失败!");
        }
        return ResponseResult.success(Collections.singletonMap("iobsFileId", fileId));
    }

    @GetMapping(value = "/getIntranetIOBSDownloadUrl")
    public ResponseResult<Object> getIntranetIOBSDownloadUrl(@RequestParam("fileId") String fileId,@RequestParam("fileName") String fileName) {
        String iobsFileDownloadUrl = iobsFileUploadService.getPerpetualDownloadAuditUrl(fileId,fileName);
        return ResponseResult.success(Collections.singletonMap("iobsFileDownloadUrl", iobsFileDownloadUrl));
    }

}
