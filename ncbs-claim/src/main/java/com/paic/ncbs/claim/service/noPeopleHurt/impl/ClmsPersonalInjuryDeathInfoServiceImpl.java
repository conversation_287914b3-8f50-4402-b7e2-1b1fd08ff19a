package com.paic.ncbs.claim.service.noPeopleHurt.impl;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsPersonalInjuryDeathInfoMapper;
import com.paic.ncbs.claim.dao.entity.clms.ClmsPersonalInjuryDeathInfo;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsPersonalInjuryDeathInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 人身伤亡信息表(ClmsPersonalInjuryDeathInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:48
 */
@Service("clmsPersonalInjuryDeathInfoService")
public class ClmsPersonalInjuryDeathInfoServiceImpl implements ClmsPersonalInjuryDeathInfoService {
    @Resource
    private ClmsPersonalInjuryDeathInfoMapper clmsPersonalInjuryDeathInfoMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsPersonalInjuryDeathInfo queryById(String id) {
        return this.clmsPersonalInjuryDeathInfoMapper.queryById(id);
    }

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public ClmsPersonalInjuryDeathInfo queryByReportNo(String reportNo, int caseTimes) {
        return this.clmsPersonalInjuryDeathInfoMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsPersonalInjuryDeathInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsPersonalInjuryDeathInfo insert(ClmsPersonalInjuryDeathInfo clmsPersonalInjuryDeathInfo) {
        clmsPersonalInjuryDeathInfo.setCreatedBy(WebServletContext.getUserId());
        clmsPersonalInjuryDeathInfo.setCreatedDate(new Date());
        clmsPersonalInjuryDeathInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsPersonalInjuryDeathInfo.setUpdatedDate(new Date());
        clmsPersonalInjuryDeathInfo.setId(UuidUtil.getUUID());
        this.clmsPersonalInjuryDeathInfoMapper.insert(clmsPersonalInjuryDeathInfo);
        return clmsPersonalInjuryDeathInfo;
    }

    /**
     * 修改数据
     *
     * @param clmsPersonalInjuryDeathInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsPersonalInjuryDeathInfo update(ClmsPersonalInjuryDeathInfo clmsPersonalInjuryDeathInfo) {
        clmsPersonalInjuryDeathInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsPersonalInjuryDeathInfo.setUpdatedDate(new Date());
        this.clmsPersonalInjuryDeathInfoMapper.update(clmsPersonalInjuryDeathInfo);
        return this.queryById(clmsPersonalInjuryDeathInfo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsPersonalInjuryDeathInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByCondition(String reportNo, int caseTimes, String taskId) {
        clmsPersonalInjuryDeathInfoMapper.deleteByCondition(reportNo, caseTimes, taskId);
    }
}
