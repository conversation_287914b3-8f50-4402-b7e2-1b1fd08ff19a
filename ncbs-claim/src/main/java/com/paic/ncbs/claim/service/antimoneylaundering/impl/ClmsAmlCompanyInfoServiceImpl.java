package com.paic.ncbs.claim.service.antimoneylaundering.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.AgencyTypeEnum;
import com.paic.ncbs.claim.common.enums.BankAccountTypeEnum;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.ClmsAmlCompanyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsShareholderInfoDto;
import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsShareholderInfoEntity;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.vo.doc.FileDocumentVO;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsAmlCompanyInfoService;
import com.paic.ncbs.claim.service.common.ClaimCommonQueryFileInfoService;
import com.paic.ncbs.claim.service.customer.CustomerInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 公司反洗钱信息处理实现类
 */
@Slf4j
@Service
public class ClmsAmlCompanyInfoServiceImpl implements ClmsAmlCompanyInfoService {

    @Autowired
    private ClmsAmlCompanyInfoMapper clmsAmlCompanyInfoMapper;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private ClaimCommonQueryFileInfoService claimCommonQueryFileInfoService;

    @Autowired
    private FileInfoMapper fileInfoMapper;

    @Override
    @Transactional
    public void dealServiceData(ClmsAntiMoneyLaunderingInfoDto dto) {
        //校验入参
        checkInputData(dto);
        if(StrUtil.isNotEmpty(dto.getIdClmsAmlCompanyInfo())){
            //更新反洗钱信息
            updateData(dto);
        }else{
            //保存公司反洗钱信息
            saveAmlCompanyInfo(dto);
        }
    }

    private void saveAmlCompanyInfo(ClmsAntiMoneyLaunderingInfoDto dto) {
        String reportNo = dto.getReportNo();
        //根据公司名称，机构类型，证件号调用crm接口获取客户号
        getCustomerNo(dto);
        ClmsAmlCompanyInfoEntity entity = new ClmsAmlCompanyInfoEntity();
        BeanUtils.copyProperties(dto,entity);
        //1设置值
        setEntityValue(dto,entity);
        //2设置股东信息
        List<ClmsShareholderInfoEntity> entityList = setShareholderInfo(dto,entity.getId());

        //影像校验，新增
        imageCheck(entity.getLegalFileId(), reportNo);
        imageCheck(entity.getBussPeopleFileId(), reportNo);

        //3插入
        clmsAmlCompanyInfoMapper.saveAmlCompanyInfo(entity);
        if(CollectionUtil.isNotEmpty(entityList)){
            for (ClmsShareholderInfoEntity clmsShareholderInfoEntity : entityList) {
                imageCheck(clmsShareholderInfoEntity.getShareholderCardFileId(), reportNo);
            }
            clmsAmlCompanyInfoMapper.saveShareholderInfos(entityList);
        }


    }

    private void imageCheck(String fileId, String reportNo) {
        if(StringUtils.isEmptyStr(fileId)){
            return;
        }
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setFileId(fileId);
        List<FileDocumentDTO> documentByFileId = fileInfoMapper.getDocumentByFileId(fileInfoDTO);
        if(CollectionUtil.isEmpty(documentByFileId)){
            return;
        }
        long count = documentByFileId.stream().filter(i -> i.getDocumentGroupId().equals(reportNo)).count();
        if(count > 0){
            return;
        }
        String userId = WebServletContext.getUserId();
        FileDocumentVO fileDocumentVOSave = new FileDocumentVO();
        FileDocumentDTO fileDocumentDTO = documentByFileId.get(0);
        BeanUtils.copyProperties(fileDocumentDTO, fileDocumentVOSave);
        fileDocumentVOSave.setDocumentGroupItemsId(fileInfoMapper.getDocumentItemId());
        fileDocumentVOSave.setDocumentGroupId(reportNo);
        fileDocumentVOSave.setDocumentId(reportNo);
        fileDocumentVOSave.setCreatedBy(userId);
        fileDocumentVOSave.setUpdatedBy(userId);
        fileInfoMapper.createDocument(fileDocumentVOSave);
    }

    /**
     * 入参校验
     * @param dto
     */
    private void checkInputData(ClmsAntiMoneyLaunderingInfoDto dto) {
        if(StrUtil.isEmpty(dto.getOrganizeCode())){
            throw new GlobalBusinessException("企业证件号必填！");
        }
        if(dto.getRegisCapital()<=0){
            throw new GlobalBusinessException("注册资金不能小于等于0");
        }

        if(CollectionUtil.isNotEmpty(dto.getShareholderInfoDtoList())){
            BigDecimal sum = BigDecimal.ZERO;
            for (ClmsShareholderInfoDto sdto : dto.getShareholderInfoDtoList()) {
                if(BigDecimalUtils.compareBigDecimalPlus(sdto.getShareholderRatio(),new BigDecimal(100)) || BigDecimalUtils.compareBigDecimalAndNullMinus(sdto.getShareholderRatio(),BigDecimal.ZERO)||BigDecimalUtils.isNullOrZero(sdto.getShareholderRatio())){
                    throw new GlobalBusinessException(sdto.getShareholderName()+"占比为"+sdto.getShareholderRatio()+"股东股权占比在（0-100]之间");
                }
                sum=sum.add(sdto.getShareholderRatio());
            }
            if(BigDecimalUtils.compareBigDecimalPlus(sum,new BigDecimal(100))){
                throw new GlobalBusinessException("股东占比总合为"+sum+","+"多个股东股权占比总和在（0-100]之间");

            }

        }
    }

    /**
     * 设置股东信息
     * @param dto
     */
    private List<ClmsShareholderInfoEntity> setShareholderInfo(ClmsAntiMoneyLaunderingInfoDto dto,String idClmsAmlCompanyInfo) {
        if(CollectionUtil.isEmpty(dto.getShareholderInfoDtoList())){
           return Arrays.asList();
        }
        Date date = new Date();
        List<ClmsShareholderInfoEntity> clmsShareholderInfoEntityList = new ArrayList<>();

        for (ClmsShareholderInfoDto shareholderInfoDto : dto.getShareholderInfoDtoList()) {
            ClmsShareholderInfoEntity entity = new ClmsShareholderInfoEntity();
            BeanUtils.copyProperties(shareholderInfoDto,entity);
            entity.setId(UuidUtil.getUUID());
            entity.setReportNo(dto.getReportNo());
            entity.setCaseTimes(dto.getCaseTimes());
            entity.setIdClmsAmlCompanyInfo(idClmsAmlCompanyInfo);
            entity.setCreatedBy(WebServletContext.getUserId());
            entity.setCreatedDate(date);
            entity.setUpdatedBy(WebServletContext.getUserId());
            entity.setUpdatedDate(date);
            entity.setEffectiveStatus(Constants.EFFECTIVE_STATUS_ZERO);
            clmsShareholderInfoEntityList.add(entity);

        }

        return clmsShareholderInfoEntityList;
    }

    /**
     * 设置entity值
     * @param dto
     * @param entity
     */
    private void setEntityValue(ClmsAntiMoneyLaunderingInfoDto dto, ClmsAmlCompanyInfoEntity entity) {
        if(Objects.equals(AgencyTypeEnum.AGENCY_TYPE_GENERAL.getCode(),dto.getAgencyType())){
            //机构类型为“一般机构”时  取统一社会信用代码
            entity.setAgencyCode(dto.getOrganizeCode());
        }else{
            //机构类型为"其他"时  取经营证件号码
            entity.setAgencyCode(dto.getBussCertificateNo());
        }
        //公司名称：取领款人姓名
        entity.setCompanyName(dto.getClientName());
        entity.setId(UuidUtil.getUUID());
        entity.setAddress(dto.getAmlAddress());
        entity.setCreatedBy(WebServletContext.getUserId());
        entity.setUpdatedBy(WebServletContext.getUserId());
        entity.setEffectiveStatus(Constants.EFFECTIVE_STATUS_ZERO);
        entity.setCreatedDate(new Date());
        entity.setUpdatedDate(new Date());
    }

    /**
     * 调用crm接口获取客户号
     * @param dto
     * @return
     */
    private void getCustomerNo(ClmsAntiMoneyLaunderingInfoDto dto) {
        String customerName=dto.getClientName();
        String cardType=dto.getClientCertificateType();
        String idNo=dto.getClientCertificateNo();
        if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),dto.getBankAccountAttribute())){
            cardType = dto.getCompanyCardType();
            idNo = dto.getOrganizeCode();
        }
        String customerNo =  customerInfoService.getCustomerNo(customerName, dto.getBankAccountAttribute(), cardType, idNo);
        dto.setCustomerNo(customerNo);
        dto.setCompanyName(dto.getClientName());
    }

    @Override
    public ClmsAntiMoneyLaunderingInfoDto getAmlCompanyInfo(ClmsAntiMoneyLaunderingInfoDto dto) {
        ClmsAntiMoneyLaunderingInfoDto returnDto = new ClmsAntiMoneyLaunderingInfoDto();
        ClmsAmlCompanyInfoEntity entity = new  ClmsAmlCompanyInfoEntity();
        entity.setReportNo(dto.getReportNo());
        entity.setCaseTimes(dto.getCaseTimes());
        entity.setCustomerNo(dto.getCustomerNo());
        ClmsAmlCompanyInfoEntity resultEntity = clmsAmlCompanyInfoMapper.getAmlCompanyInfo(entity);
        if(ObjectUtil.isEmpty(resultEntity)){
            return returnDto;
        }

        convertAmlCompanyInfo(returnDto, resultEntity);
        returnDto.setIdClmsAmlCompanyInfo(resultEntity.getId());
        return returnDto;
    }

    /**
     * 更新反洗钱数据 根据主键id更新
     * @param dto
     */

    private void updateData(ClmsAntiMoneyLaunderingInfoDto dto) {

       //根据主键id查询反洗钱信息
        ClmsAmlCompanyInfoEntity resultEntity = clmsAmlCompanyInfoMapper.getAmlCompanyInfoById(dto.getIdClmsAmlCompanyInfo());
        if(ObjectUtil.isEmpty(resultEntity)){
            throw new GlobalBusinessException("更新失败没有查询到相关数据");
        }

        //如果 领款人姓名，证件类型（公司为机构类型），证件号没有变化 则无需调用crm接口,如果其中任何一个发生变化需要调用crm接口获取最新客户号
        if(!Objects.equals(dto.getClientName(),resultEntity.getCompanyName()) || !Objects.equals(dto.getAgencyType(),resultEntity.getAgencyType()) || !Objects.equals(dto.getAgencyCode(),resultEntity.getAgencyCode()) ){
            //获取客户号
            getCustomerNo(dto);
        }
        BeanUtils.copyProperties(dto,resultEntity);
        resultEntity.setAddress(dto.getAddress());
        resultEntity.setUpdatedDate(new Date());
        resultEntity.setUpdatedBy(WebServletContext.getUserId());
        //更新股东信息：删除原有的数据 逻辑删除
        updateShareHolderBYIdCompanyInfo(resultEntity.getId());

        //插入新的数据
        List<ClmsShareholderInfoEntity>  shareholderInfoEntities = setShareholderInfo(dto,resultEntity.getId());
        if(CollectionUtil.isNotEmpty(shareholderInfoEntities)){
            clmsAmlCompanyInfoMapper.saveShareholderInfos(shareholderInfoEntities);
        }
        clmsAmlCompanyInfoMapper.updateAmlCompanyInfo(resultEntity);
    }

    @Override
    @Transactional
    public void delAmlCompanyInfo(ClmsAntiMoneyLaunderingInfoDto dto) {
        ClmsAmlCompanyInfoEntity entity = new ClmsAmlCompanyInfoEntity();
        BeanUtils.copyProperties(dto,entity);
        ClmsAmlCompanyInfoEntity oldEntity = clmsAmlCompanyInfoMapper.getAmlCompanyInfo(entity);
        if(ObjectUtil.isEmpty(oldEntity)){
            log.info(dto.getReportNo()+"删除delAmlCompanyInfo为空"+ JsonUtils.toJsonString(oldEntity));
            return;
        }
        log.info(dto.getReportNo()+"删除delAmlCompanyInfo"+ JsonUtils.toJsonString(oldEntity));
        clmsAmlCompanyInfoMapper.updateCompanyStatus(oldEntity.getId());
        updateShareHolderBYIdCompanyInfo(oldEntity.getId());

    }

    /**
     * 通过公司反洗钱信息表主键 去查询股东信息表数据
     * 并更新删除标志为Y
     * @param idClmsAmlCompanyInfo
     */
    private void updateShareHolderBYIdCompanyInfo(String idClmsAmlCompanyInfo){
        //通过反洗钱公司主键作为股东信息表外键查询
        List<ClmsShareholderInfoEntity> entityList = clmsAmlCompanyInfoMapper.getShareHolderInfos(idClmsAmlCompanyInfo);
        if(CollectionUtil.isNotEmpty(entityList)){
            //逻辑删除股东信息 删除标记更新为Y
            for (ClmsShareholderInfoEntity e : entityList) {
                clmsAmlCompanyInfoMapper.updateShareholderData(e.getId());
            }

        }
    }

    /**
     * 查询反洗钱信息根据姓名，证件类型，证件号
     * @param dto
     * @return
     */
    @Override
    public ClmsAntiMoneyLaunderingInfoDto getAmlCompanyByNameAndCardTypeAndCardNo(ClmsAntiMoneyLaunderingInfoDto dto) {
        ClmsAntiMoneyLaunderingInfoDto returnDto = new ClmsAntiMoneyLaunderingInfoDto();
        ClmsAmlCompanyInfoEntity entity = new ClmsAmlCompanyInfoEntity();
        entity.setCompanyName(dto.getClientName());
        entity.setCompanyCardType(dto.getCompanyCardType());
        entity.setAgencyCode(dto.getAgencyCode());
        log.info("getAmlCompanyByNameAndCardTypeAndCardNo查询参数={}",JsonUtils.toJsonString(entity));
        ClmsAmlCompanyInfoEntity returnEntity =   clmsAmlCompanyInfoMapper.getAmlCompanyByNameAndCardTypeAndCardNo(entity);
        if(ObjectUtil.isEmpty(returnEntity)){
            return returnDto;
        }

        convertAmlCompanyInfo(returnDto, returnEntity);
        return returnDto;
    }

    @Override
    public ClmsAntiMoneyLaunderingInfoDto getAmlCompanyByCustomerNo(ClmsAntiMoneyLaunderingInfoDto dto) {
        ClmsAntiMoneyLaunderingInfoDto returnDto = new ClmsAntiMoneyLaunderingInfoDto();
        ClmsAmlCompanyInfoEntity entity = new ClmsAmlCompanyInfoEntity();
        entity.setCustomerNo(dto.getCustomerNo());
        log.info("getAmlByCustomerNo查询参数={}", JsonUtils.toJsonString(entity));
        ClmsAmlCompanyInfoEntity returnEntity = clmsAmlCompanyInfoMapper.getAmlCompanyByCustomerNo(entity);
        if(ObjectUtil.isEmpty(returnEntity)){
            return returnDto;
        }

        convertAmlCompanyInfo(returnDto, returnEntity);
        return returnDto;
    }

    private void convertAmlCompanyInfo(ClmsAntiMoneyLaunderingInfoDto dto, ClmsAmlCompanyInfoEntity entity) {
        BeanUtils.copyProperties(entity, dto);
        dto.setAmlAddress(entity.getAddress());

        //查询 业务人员上传的文件信息
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setReportNo(entity.getReportNo());
        fileInfoDTO.setFileId(entity.getBussPeopleFileId());
        FileInfoDTO busfileDto = claimCommonQueryFileInfoService.getFileInfo(fileInfoDTO);
        dto.setBussPeopleFileInfoDTO(busfileDto);

        //法人的上传的证件信息
        fileInfoDTO.setFileId(entity.getLegalFileId());
        FileInfoDTO legalFileDto = claimCommonQueryFileInfoService.getFileInfo(fileInfoDTO);
        dto.setLegalFileInfoDTO(legalFileDto);

        //查询股东信息
        List<ClmsShareholderInfoEntity> entities = clmsAmlCompanyInfoMapper.getShareHolderInfos(entity.getId());
        if(CollectionUtil.isNotEmpty(entities)){
            List<ClmsShareholderInfoDto> shareholderInfoDtoList = BeanUtil.copyToList(entities, ClmsShareholderInfoDto.class);
            for (ClmsShareholderInfoDto sdto : shareholderInfoDtoList) {
                fileInfoDTO.setFileId(sdto.getShareholderCardFileId());
                FileInfoDTO shareFileDto = claimCommonQueryFileInfoService.getFileInfo(fileInfoDTO);
                sdto.setShareFileInfoDTO(shareFileDto);
            }
            dto.setShareholderInfoDtoList(shareholderInfoDtoList);
        }
    }
}
