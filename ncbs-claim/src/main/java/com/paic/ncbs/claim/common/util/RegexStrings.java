package com.paic.ncbs.claim.common.util;

public class RegexStrings {

    public final static String REG_RULE_FULL_DATE = "^(19|20)\\d{2}[/]([0]{0,1}[1-9]|[1][0-2])[/]([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])[ ](([0]{0,1}[0-9]|[1][0-9]|[2][0-3])([:](([0]{0,1}[0-9])|([1-5][0-9]))){2})";
    
    public final static String REG_RULE_DATE = "^(19|20)\\d{2}[/]{0,1}([0]{0,1}[1-9]|[1][0-2])[/]{0,1}([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])$";
    
    public final static String REG_RULE_PART_DATE = "^(19|20)\\d{2}[/]([0]{0,1}[1-9]|[1][0-2])[/]([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])[ ](([0]{0,1}[0-9]|[1][0-9]|[2][0-3])([:](([0]{0,1}[0-9])|([1-5][0-9]))))";

    public final static String REG_DATE_WITH_CONNECTOR = "^(19|20)\\d{2}[-]([0]{0,1}[1-9]|[1][0-2])[-]([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])$";

    public final static String REG_DATE_WITH_DELIMITER = "^(19|20)\\d{2}([-]|[/])([0]{0,1}[1-9]|[1][0-2])([-]|[/])([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])$";

    public final static String REG_DATE = "^(19|20)\\d{2}[-]{0,1}([0]{0,1}[1-9]|[1][0-2])[-]{0,1}([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])$";

    public final static String REG_TIME = "^[0]{0,1}[0-9]|[1][0-9]|[2][0-3]([:](([0]{0,1}[0-9])|([1-5][0-9]))){2}$";
    
    public final static String REG_FULL_DATE = "^(19|20)\\d{2}[-]([0]{0,1}[1-9]|[1][0-2])[-]([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])[ ](([0]{0,1}[0-9]|[1][0-9]|[2][0-3])([:](([0]{0,1}[0-9])|([1-5][0-9]))){2})";

    public final static String REG_LONG_DATE = "^(19|20)\\d{2}([0]{0,1}[1-9]|[1][0-2])([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])(([0]{0,1}[0-9]|[1][0-9]|[2][0-3])((([0]{0,1}[0-9])|([1-5][0-9]))){2})";
    
    public final static String REG_PART_DATE = "^(19|20)\\d{2}[-]([0]{0,1}[1-9]|[1][0-2])[-]([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])[ ](([0]{0,1}[0-9]|[1][0-9]|[2][0-3])([:](([0]{0,1}[0-9])|([1-5][0-9]))))";

    public final static String REG_SIMPLE_DAY = "^(19|20)\\d{2}{0,1}([0]{0,1}[1-9]|[1][0-2]){0,1}([0]{0,1}[1-9]|[1-2][0-9]|[3][0-1])$";

    public final static String REG_IDCARD_15 = "^[1-9]\\d{13}[Xx|0-9]$";

    public final static String REG_IDCARD_18 = "^[1-9]\\d{16}[Xx|0-9]$";
    
    public final static String REG_MONEY = "^([1-9]{1}\\d{0,13})([.]\\d{0,2}){0,1}$";
    
    public final static String REG_MONEY_ALLOW_ONLY_FEN ="^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$";

    public final static String REG_MONEY_COST_ESTIMATE="^(([1-9]{1}\\d{0,6})|([0]{1}))(\\.(\\d){0,2})?$";
    
    public final static String REG_MONEY_LOSSPAY = "^(([1-9]{1}\\d{0,9})|([0]{1}))(\\.(\\d){0,2})?$";
    
    public final static String REG_CHINA = "[\\u4e00-\\u9fa5]";

    public final static String REG_PHONE_NUMBER = "^[1]\\d{10}$";
    
    public final static String REG_HOME_TELEPHONE_FIRST = "^(\\+?\\d{1,3}\\-){0,1}0\\d{2,3}\\-\\d{7,8}(\\-\\d*){0,1}$";
    public final static String REG_HOME_TELEPHONE_TWO = "^{0,1}0\\d{2,3}\\,\\d{7,8}(\\-\\d*){0,1}$";
    
    public final static String REG_CAR_MARK = "^[\u4e00-\u9fa5]{1}[a-zA-Z]{1}[a-zA-Z0-9_-]{5}[a-zA-Z_0-9_\u4e00-\u9fa5]$";
    public final static String REG_CAR_MARK_ONE = "^([\u4e00-\u9fa5]|WJ)[A-Z]*\\d*[A-Z]*$";
    public final static String REG_CAR_MARK_TWO = "^([\u4e00-\u9fa5]|WJ)([A-Z]|\\d)*$";

    public static final String REG_FITS_CODE = "^([0-9]|[A-Z]){6}$";

    public static final String REG_FITS_NAME = "^([A-Z]|[\\u4e00-\\u9fa5]|\\(|\\)|\\uff08|\\uff09|-|[0-9]){1,20}$";

    public static final String REG_USED_COUNT = "^([1-9]\\d{0,7})|(0)$";

    public static final String REG_CAR_LIMIT_COUNT = "^([1-9]|(1\\d)|20)$";

    public static final String REG_PINYIN_CODE = "^([A-Z]|\\(|\\)|-|[0-9]){0,20}$";

    public static final String REG_FITS_STRUCTURE_NAME = "^([A-Z]|[\\u4e00-\\u9fa5]|\\(|\\)|\\uff08|\\uff09|-|[0-9]|/){1,20}$";

    public static final String REG_ORIGINAL_CODE = "^([0-9]|[A-Z]|\\(|\\)|-|\\\\|.|/){1,20}$";

    public static final String REG_FITS_PRICE = "^([1-9]([0-9]{0,7})([.]([0-9]{1,2}))?)|(0)|(0.([0-9]{1,2}))$";

    public static final String REG_LOSS_SUM = "^\\d+\\.?\\d+$";

}
