package com.paic.ncbs.claim.model.vo.quality;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QualityrequstVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("报案号/案件号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("归属机构")
    private String company;

    @ApiModelProperty("人员姓名")
    private String person;

    @ApiModelProperty("工号")
    private String personno;

    @ApiModelProperty("任务数")
    private Integer sum;

    @ApiModelProperty("质检任务号")
    private String qualityno;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("是否global")
    private String isglobal;
}
