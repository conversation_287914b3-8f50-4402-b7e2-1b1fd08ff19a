package com.paic.ncbs.claim.service.verify.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentDuty;
import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentPlan;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.SmsInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.product.ProductMapper;
import com.paic.ncbs.claim.dao.mapper.report.PolicyMapper;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsPolicySurrenderInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.BatchMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PlanPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper;
import com.paic.ncbs.claim.dao.mapper.verify.VerifyMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.message.SmsInfoDTO;
import com.paic.ncbs.claim.model.dto.mistake.MistakeRecordDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.dto.user.UserDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyTaskDTO;
import com.paic.ncbs.claim.model.vo.record.SmsRecordVO;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsPolicySurrenderInfoVO;
import com.paic.ncbs.claim.model.vo.settle.VerifyInfoVO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyChargeMapper;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge;
import com.paic.ncbs.claim.replevy.service.ReplevyChargeService;
import com.paic.ncbs.claim.replevy.service.ReplevyService;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.coinsurance.CoinsuranceService;
import com.paic.ncbs.claim.service.common.*;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.mistake.MistakeRecordService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.other.impl.SmsInfoServiceImpl;
import com.paic.ncbs.claim.service.pay.ClmsPaymentDutyService;
import com.paic.ncbs.claim.service.pay.ClmsPaymentPlanService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.product.ProductService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.report.ReportService;
import com.paic.ncbs.claim.service.restartcase.RestartCaseService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicySurrenderInfoService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.EndorsementService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ClmsDutyDetailBillSettleService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.service.verify.VerifyCheckService;
import com.paic.ncbs.claim.service.verify.VerifyService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import com.paic.ncbs.message.model.dto.SmsResult;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service("verifyService")
@Transactional
@Slf4j
@RefreshScope
public class VerifyServiceImpl implements VerifyService {

    @Autowired
    private VerifyMapper verifyDao;

    @Autowired
    private CommonService commonService;
    @Autowired
    private EndorsementService endorsementService;
    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private MistakeRecordService mistakeRecordService;
    @Autowired
    private BpmService bpmService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ReportService reportService;

    @Autowired
    private SmsInfoService smsInfoService;
    @Autowired
    private PolicyPayMapper policyPayMapper;

    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private MaxPayService maxPayService;

    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private BatchMapper batchDao;

    @Autowired
    private FeePayMapper feePayMapper;

    @Autowired
    private OcasRequest ocasRequest;

    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private ClmsPaymentPlanService clmsPaymentPlanService;

    @Autowired
    private ClmsPaymentDutyService clmsPaymentDutyService;

    @Autowired
    private PrintService printService;

    @Autowired
    private ClmsPolicySurrenderInfoService clmsPolicySurrenderInfoService;

    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

    @Autowired
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;

    @Autowired
    private ReinsuranceService reinsuranceService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private DutyBillLimitInfoService dutyBillLimitInfoService;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private ClmsQueryPolicyAllInfoService clmsQueryPolicyAllInfoService;
    @Autowired
    private ClmsEveryMonthPayTimesCheckService clmsEveryMonthPayTimesCheckService;
    @Autowired
    private VerifyCheckService verifyCheckService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;

    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private PlanPayMapper planPayMapper;


    @Value("${switch.limitAmount}")
    private String switchLimitAmount;
    @Autowired
    private ClmsYearlyPayDaysService clmsYearlyPayDaysService;
    @Autowired
    private ClmsDutyDetailBillSettleService clmsDutyDetailBillSettleService;

    @Autowired
    private ReportInfoService reportInfoService;

    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private TaskPoolService taskPoolService;
    @Autowired
    private PermissionUserMapper permissionUserMapper;
    @Autowired
    private ClmsPolicySurrenderInfoMapper clmsPolicySurrenderInfoMapper;

    @Autowired
    private PolicyMapper policyMapper;
    @Autowired
    private PrintCoreService printCoreService;
    @Autowired
    private CoinsuranceService coinsuranceService;
    @Autowired
    private AhcsPolicyPlanMapper ahcsPolicyPlanMapper;
    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private GlobalPolicyService globalPolicyService;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;


    @Autowired
    private SmsInfoMapper smsInfoMapper;

    @Autowired
    private SmsInfoServiceImpl smsInfoServiceimpl;

    @Autowired
    private ClmsReplevyChargeMapper clmsReplevyChargeMapper;
    @Autowired
    private RestartCaseService restartCaseService;

    @Autowired
    private ReplevyService replevyService;

    @Autowired
    private ReplevyChargeService replevyChargeService;

    @Override
    @Transactional
    public void addVerify(VerifyInfoVO verifyInfoVO, List<String> msg) throws Exception {
        String reportNo = verifyInfoVO.getReportNo();
        Integer caseTimes = verifyInfoVO.getCaseTimes();
        String indemnityModel = "";
        WholeCaseBaseEntity wholeCaseBase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
        if (BaseConstant.STRING_0.equals(wholeCaseBase.getWholeCaseStatus())) {
            throw new GlobalBusinessException("已结案不能核赔");
        }
        if (null != wholeCaseBase) {
            indemnityModel = wholeCaseBase.getIndemnityModel();
        }
        verifyInfoVO.setIndemnityModel(indemnityModel);
        String caseProcessStatus = caseProcessService.getCaseProcessStatus(reportNo, caseTimes);
        if(CaseProcessStatus.PENDING_SETTLE.getCode().equals(caseProcessStatus)){
            throw new GlobalBusinessException("请先完成理算再进行核赔");
        }

        updateVerify(verifyInfoVO);
        String verifyConclusion = verifyInfoVO.getVerify().getVerifyConclusion();
        String verifyOpinion = verifyInfoVO.getVerify().getVerifyOpinion();
        ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO = new ClmsDutyDetailBillSettleDTO();
        clmsDutyDetailBillSettleDTO.setReportNo(reportNo);
        clmsDutyDetailBillSettleDTO.setCaseTimes(caseTimes);

        // 通过报案号和赔付次数查询案件重开记录
        List<RestartCaseRecordEntity> restartCaseRecordEntityList=restartCaseService.getRestartCaseList(reportNo, caseTimes-1);
        String restartReason="";
        if(!CollectionUtils.isEmpty(restartCaseRecordEntityList)){
            restartReason=restartCaseRecordEntityList.get(0).getRestartReason();
        }
        // 查询新增赔款信息
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
        List<PaymentItemDTO> payItemList = paymentItemMapper.getPaymentItem(paymentItemDTO);
        switch (verifyConclusion) {
            case VerifyDTO.CONCLUSION_UPDATE_PASS:
                LogUtil.audit("CONCLUSION_UPDATE_PASS:{}",verifyConclusion);
                //判断是否宽限期出险 宽限期出险验证调整到前端校验，已提供接口/queyPolicyGarcePerid/{reportNo}/{caseTimes}给前端
                //checkGarcePerid(verifyInfoVO.getReportNo(),verifyInfoVO.getCaseTimes());
                //checkSettleAmount(verifyInfoVO);
                // 核赔提交前校验
                verifyCheckService.checkVerifyBefore(verifyInfoVO);
                updateEndorsementInfo(verifyInfoVO);

                if(!checkVerifyPremission(reportNo,verifyConclusion,verifyInfoVO.getTaskId(),msg, verifyOpinion, verifyInfoVO.getSelectedUserId())){
                    LogUtil.audit("权限不足{}",msg);
                    break;
                }else {
                    //操作记录
                    operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_SETTLE_REVIEW, "修改通过", verifyOpinion);
                }
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.CASE_CLOSED.getCode());
//                bpmService.completeTask_oc(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW);
//                sendReportSms(reportNo,caseTimes);
                // 测试环境先验证，没问题后上sit
                // TODO 0613价税分离免税计算
                this.initNoSttlePaymentItem(reportNo,caseTimes);
                paymentItemService.updateCompensateNoPaymentItem(reportNo,caseTimes,"1");
                //更新账单日限额表
//                this.updateBillLimitInfo(reportNo,caseTimes);
                //dutyBillLimitInfoMapper.updateBillLimitInfo(verifyInfoVO);
                clmsDutyDetailBillSettleDTO.setApprovalStatus("1");
                break;

            case VerifyDTO.CONCLUSION_NOT_PASS:
                //核赔退回分支流程校验
                bpmService.processCheck(reportNo, BpmConstants.OC_SETTLE_REVIEW,BpmConstants.OPERATION_BACK);
                addMistakeRecord(verifyInfoVO);
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.PENDING_SETTLE.getCode());
                TaskInfoDTO endTask = new TaskInfoDTO();
                endTask.setReportNo(reportNo);
                endTask.setCaseTimes(caseTimes);
                endTask.setTaskId(verifyInfoVO.getTaskId());
                endTask.setUpdatedBy(WebServletContext.getUserId());
                endTask.setStatus(BpmConstants.TASK_STATUS_COMPLETED);
                taskInfoService.modifyTaskInfoByDefKey(endTask);
                bpmService.activeManualSettle(reportNo,caseTimes,BpmConstants.OC_MANUAL_SETTLE,false);
                //clmsDutyDetailBillSettleDTO.setIsDeleted("1");
                //操作记录
                operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_SETTLE_REVIEW, "退回理算", verifyOpinion);
                //负数重开场景，解冻
                if("7".equals(restartReason)){
                    if(!CollectionUtils.isEmpty(payItemList)){
                        replevyService.sendPayThaw(reportNo,caseTimes,payItemList.get(0).getIdClmPaymentItem(),"R","2");
                    }
                }
                break;

            default:
                LogUtil.audit("addVerify-default:{}",verifyConclusion);
                //判断是否宽限期出险
                //checkGarcePerid(verifyInfoVO.getReportNo(),verifyInfoVO.getCaseTimes());
                //checkSettleAmount(verifyInfoVO);
                // 核赔提交前校验
                verifyCheckService.checkVerifyBefore(verifyInfoVO);
                if(!checkVerifyPremission(reportNo,verifyConclusion,verifyInfoVO.getTaskId(),msg, verifyOpinion, verifyInfoVO.getSelectedUserId())){
                    LogUtil.audit("权限不足{}",msg);
                    break;
                }else {
                    //操作记录
                    operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_SETTLE_REVIEW, "通过", verifyOpinion);
                }
                //校验每月赔付次数;通融案件不校验责任属性相关的所有因子
                if(!"6".equals(indemnityModel)) {
                    checkEverMonthPayTims(reportNo,caseTimes);
                }
                //clmsDutyDetailBillSettleService.checkRemimount(reportNo,caseTimes);
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.CASE_CLOSED.getCode());
//                bpmService.completeTask_oc(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW);
//                sendReportSms(reportNo,caseTimes);
                // 测试环境先验证，没问题后上sit
                this.initNoSttlePaymentItem(reportNo,caseTimes);
                paymentItemService.updateCompensateNoPaymentItem(reportNo,caseTimes,"1");
                clmsDutyDetailBillSettleDTO.setApprovalStatus("1");
                break;
        }
        if(VerifyDTO.CONCLUSION_NOT_PASS.equals(verifyConclusion)||VerifyDTO.CONCLUSION_AUDIT_BACK.equals(verifyConclusion)){
            //核赔审核重新理算添加消息提醒
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_SETTLE_REVIEW);
            noticesDTO.setReportNo(reportNo);
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(caseTimes);
            TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(reportNo, caseTimes, BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);
            if (taskInfoDTO!=null){
                noticeService.saveNotices(noticesDTO,taskInfoDTO.getAssigner());
            }
        }
            log.info("核赔通过且无需上级审批的时候是结案={}",reportNo);
        // 核赔通过且无需上级审批的时候是结案
        clmsDutyDetailBillSettleService.updateClmsDutyDetailBillSettle(clmsDutyDetailBillSettleDTO);
        if (!VerifyDTO.CONCLUSION_NOT_PASS.equals(verifyInfoVO.getVerify().getVerifyConclusion())) {
            if (!msg.contains(VerifyDTO.CONCLUSION_AUDIT_MSG)) {
                return;
            }

            //通用年度赔付天数校验;通融案件不校验责任属性相关的所有因子
            if(!"6".equals(indemnityModel)) {
                clmsYearlyPayDaysService.dealData(reportNo,caseTimes);
            }
            //更新日限额的记录状态为1clms_duty_bill_limit
            dutyBillLimitInfoService.dealBIllLimtData(reportNo,caseTimes);
            // 判断是否有解约，有的话调用批改解约
            clmsPolicySurrenderInfoService.applyPolicySurrender(reportNo,caseTimes);
            printService.sendPrintCore(verifyInfoVO.getReportNo(),verifyInfoVO.getCaseTimes());
            log.info("核赔通过准备调用支付接口noticePayment={}",reportNo);
            // 调用收付费支付接口
            payInfoNoticeThirdPartyCoreSAO.noticePayment(verifyInfoVO.getReportNo(),verifyInfoVO.getCaseTimes(), null, true, false);
            log.info("核赔通过调用支付接口noticePayment完成={}",reportNo);
            //核赔审批通过，插入监管赔付中间表
            paymentItemService.saveCompensationIntermediateData(verifyInfoVO.getReportNo(), verifyInfoVO.getCaseTimes());
            log.info("核赔审批通过，插入监管赔付中间表完成={}",reportNo);
            log.info("核赔审批通过，更新账单日限额表完成={}",reportNo);
            //负数重开，构建收费上报报文，并调收费上报确认核销
            if("7".equals(restartReason)){
                if(!CollectionUtils.isEmpty(payItemList)) {
                    replevyChargeService.sendPaymentConfirm(payItemList.get(0).getBatchNo(),"2",payItemList.get(0).getIdClmPaymentItem());
                }
            }
            //核赔通过，主共保案件生成提醒信息
            noticeService.addMainCoinNotice(reportNo,caseTimes);
            UserInfoDTO userInfoDTO = WebServletContext.getUser();
            if(!"7".equals(restartReason)){
                //生成共保通知书并上传影像 异步,生成共保摊回记录
                printCoreService.saveCoinsFileAsync(reportNo,caseTimes,SettleConst.CLAIM_TYPE_PAY,userInfoDTO,null);
                coinsuranceService.addRecoveryInfo(reportNo,caseTimes,SettleConst.CLAIM_TYPE_PAY,null);
            }
            log.info("核赔审批通过，调用global理算回流接口开始"+reportNo+"-"+caseTimes);
            globalPolicyService.sendReturnSettleToGlobal(reportNo,caseTimes,GlobalConstants.CASE_CONCLUSION_PAYMENT_CLOSING,false);
            log.info("核赔审批通过，调用global理算回流接口结束"+reportNo+"-"+caseTimes);
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 调用渠道mq
                    SyncCaseStatusDto dto = new SyncCaseStatusDto();
                    dto.setReportNo(verifyInfoVO.getReportNo());
                    dto.setCaseTimes(verifyInfoVO.getCaseTimes());
                    dto.setCaseStatus(SyncCaseStatusEnum.ENDCASE);
                    dto.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
                    mqProducerSyncCaseStatusService.syncCaseStatus(dto);

                    // 发送再保
                    RepayCalDTO repayCalDTO = new RepayCalDTO();
                    repayCalDTO.setReportNo(verifyInfoVO.getReportNo());
                    repayCalDTO.setCaseTimes(verifyInfoVO.getCaseTimes());
                    repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
                    repayCalDTO.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
                    repayCalDTO.setLoginUm(WebServletContext.getUserId());
                    repayCalDTO.setTaskId(verifyInfoVO.getTaskId());
                    reinsuranceService.sendReinsurance(repayCalDTO);
                }
            });

            //判断短信发送表是否有待发送短信，有则取该条短信发送；没有则走原流程
            SmsRecordVO smsRecordVO = null;
            try {
                smsRecordVO = smsInfoMapper.querySmsInfo(reportNo,Constants.SMS_STATUS_INIT);
            } catch (Exception e) {
                LogUtil.warn("查询待发送短信异常！",e);
            }
            if (smsRecordVO != null){
                SmsResult smsResult = null;
                try {
                    log.info("待发送短信内容：{}", smsRecordVO.getSmsContent());
                    smsResult = smsInfoServiceimpl.sendMessage(smsRecordVO.getMobileNo(), smsRecordVO.getSmsContent());
                } catch (Exception e) {
                    LogUtil.warn("核赔完成发送短信异常！",e);
                }
                if (smsResult != null){
                    if (smsResult.getIsSuccess().equals(Constants.SMS_SEND_SUCCESS)) {
                        log.info("核赔完成发送短信成功！");
                        smsRecordVO.setSmsStatus(Constants.SMS_STATUS_SUCCESS);
                        smsRecordVO.setBaseId(null);
                        smsRecordVO.setBaseDesc(smsResult.getFailReason());
                        smsRecordVO.setBaseStatus(new BigDecimal(smsResult.getFailCode()));
                        smsRecordVO.setRequestId(smsResult.getSerialNo());
                        smsRecordVO.setSendLink(SmsTypeEnum.VERIFY_REVIEW.getType());
                        SmsInfoDTO smsInfoDTO = new SmsInfoDTO();
                        BeanUtils.copyProperties(smsRecordVO, smsInfoDTO);
                        smsInfoMapper.updateClmsSmsInfo(smsInfoDTO);
                    }else {
                        log.info("核赔完成发送短信失败，短信平台发送失败原因：{}", smsResult.getFailReason());
                        smsRecordVO.setSmsStatus(Constants.SMS_STATUS_FAIL);
                        smsRecordVO.setBaseDesc(smsResult.getFailReason());
                        smsRecordVO.setBaseStatus(new BigDecimal(smsResult.getFailCode()));
                        smsRecordVO.setRequestId(smsResult.getSerialNo());
                        SmsInfoDTO smsInfoDTO = new SmsInfoDTO();
                        BeanUtils.copyProperties(smsRecordVO, smsInfoDTO);
                        smsInfoMapper.updateClmsSmsInfo(smsInfoDTO);
                    }
                }

            }else {
                //重开案件无赔款不发短信
                Boolean flag = true;
                if(caseTimes > 1){
                    Integer count = paymentItemMapper.isPay(reportNo,caseTimes);
                    if(count == 0){
                        flag = false;
                    }
                }
                if (flag) {
                    try {
                        String requestId = MDC.get(BaseConstant.REQUEST_ID);
                        smsInfoService.sendBusinessSmsAsync(SmsTypeEnum.VERIFY_REVIEW, reportNo, caseTimes, null, requestId);
                    } catch (Exception e) {
                        LogUtil.warn("核赔完成发送短信失败,不影响原有流程", e);
                    }

                }
            }
        }
    }

    /**
     * 自动核赔逻辑
     * @param reportNo
     * @param caseTimes
     */
    @Override
    @Transactional
    public void autoVerify(String reportNo, Integer caseTimes) throws Exception {
        LogUtil.audit("autoVerify:案件{} {} ,自动核赔业务开始：", reportNo, caseTimes);
        WholeCaseBaseEntity wholeCaseBase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
        if (BaseConstant.STRING_0.equals(wholeCaseBase.getWholeCaseStatus())) {
            throw new GlobalBusinessException("案件已结案,不能核赔!");
        }
        String indemnityModel = "";
        if (null != wholeCaseBase) {
            indemnityModel = wholeCaseBase.getIndemnityModel();
        }
        TaskInfoDTO taskInfo = taskInfoService.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_SETTLE_REVIEW);
        if (Objects.isNull(taskInfo) || !BaseConstant.STRING_0.equals(taskInfo.getStatus())) {
            throw new GlobalBusinessException("案件核赔任务不存在或者已完成，无法提交！");
        }

        VerifyDTO verifyDTO = this.getVerify(reportNo, caseTimes);
        verifyDTO.setVerifyConclusion(VerifyDTO.CONCLUSION_PASS);
        verifyDTO.setVerifyOpinion("自动核赔通过！");

        VerifyInfoVO verifyInfoVO = new VerifyInfoVO();
        verifyInfoVO.setReportNo(verifyDTO.getReportNo());
        verifyInfoVO.setIdAhcsBatch(verifyDTO.getIdAhcsBatch());
        verifyInfoVO.setCaseTimes(verifyDTO.getCaseTimes());
        verifyInfoVO.setVerifyUm(ConstValues.SYSTEM_UM);
        // 不需要更新不再赋值
        verifyInfoVO.setEndorsement(null);
        verifyInfoVO.setTaskId(taskInfo.getTaskId());
        verifyInfoVO.setVerify(verifyDTO);
        verifyInfoVO.setIndemnityModel(indemnityModel);
        // 更新结案号
        String endCseNo = commonService.generateNo(NoConstants.END_CASE_NO, VoucherTypeEnum.END_CASE_NO, WebServletContext.getDepartmentCode());
        wholeCaseBaseMapper.modifyWholeCaseEndCaseNo(endCseNo, verifyInfoVO.getReportNo(), verifyInfoVO.getCaseTimes());
        // 更新核赔结论
        updateVerify(verifyInfoVO);
        // checkSettleAmount(verifyInfoVO);
        // 核赔提交前校验
        verifyCheckService.checkVerifyBefore(verifyInfoVO);
        // 测试环境先验证
        this.initNoSttlePaymentItem(reportNo,caseTimes);
        paymentItemService.updateCompensateNoPaymentItem(reportNo,caseTimes,"1");
        ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO = new ClmsDutyDetailBillSettleDTO();
        clmsDutyDetailBillSettleDTO.setReportNo(reportNo);
        clmsDutyDetailBillSettleDTO.setCaseTimes(caseTimes);
        clmsDutyDetailBillSettleDTO.setApprovalStatus("1");
        clmsDutyDetailBillSettleService.updateClmsDutyDetailBillSettle(clmsDutyDetailBillSettleDTO);
        // 自动核赔没有处理人需要先认领一下处理 处理人SYSTEM
        taskInfoMapper.reAssign(taskInfo.getTaskId(),ConstValues.SYSTEM_UM,ConstValues.SYSTEM_UM,taskInfo.getDepartmentCode());
        // 完成核赔处理任务：使用 completeTask_oc_css 处理人SYSTEM
        bpmService.completeTask_oc_css(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW);
        caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.CASE_CLOSED.getCode());

        log.info("自动核赔通过继续后继逻辑:{}",reportNo);
        //通用年度赔付天数校验;通融案件不校验责任属性相关的所有因子
        if (!"6".equals(indemnityModel)) {
            clmsYearlyPayDaysService.dealData(reportNo,caseTimes);
        }
        //更新日限额的记录状态为1clms_duty_bill_limit
        dutyBillLimitInfoService.dealBIllLimtData(reportNo,caseTimes);
        // 判断是否有解约，有的话调用批改解约
        clmsPolicySurrenderInfoService.applyPolicySurrender(reportNo,caseTimes);
        printService.sendPrintCore(reportNo,caseTimes);
        log.info("自动核赔通过准备调用支付接口noticePayment={}",reportNo);
        // 调用收付费支付接口
        payInfoNoticeThirdPartyCoreSAO.noticePayment(verifyInfoVO.getReportNo(),verifyInfoVO.getCaseTimes(), null, true, false);
        log.info("自动核赔通过调用支付接口noticePayment完成={}",reportNo);
        // 核赔审批通过，插入监管赔付中间表
        paymentItemService.saveCompensationIntermediateData(verifyInfoVO.getReportNo(), verifyInfoVO.getCaseTimes());
        log.info("自动核赔审批通过，插入监管赔付中间表完成={}",reportNo);
        log.info("自动核赔审批通过，调用global理算回流接口开始"+reportNo+"-"+caseTimes);
        globalPolicyService.sendReturnSettleToGlobal(reportNo,caseTimes,GlobalConstants.CASE_CONCLUSION_PAYMENT_CLOSING,false);
        log.info("自动核赔审批通过，调用global理算回流接口结束"+reportNo+"-"+caseTimes);
        log.info("自动核赔审批通过，更新账单日限额表完成={}",reportNo);

        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_SETTLE_REVIEW, "自动核赔", null, ConstValues.SYSTEM_UM);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 调用渠道mq
                SyncCaseStatusDto dto = new SyncCaseStatusDto();
                dto.setReportNo(verifyInfoVO.getReportNo());
                dto.setCaseTimes(verifyInfoVO.getCaseTimes());
                dto.setCaseStatus(SyncCaseStatusEnum.ENDCASE);
                dto.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
                mqProducerSyncCaseStatusService.syncCaseStatus(dto);

                // 发送再保
                RepayCalDTO repayCalDTO = new RepayCalDTO();
                repayCalDTO.setReportNo(verifyInfoVO.getReportNo());
                repayCalDTO.setCaseTimes(verifyInfoVO.getCaseTimes());
                repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
                repayCalDTO.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_PAY);
                reinsuranceService.sendReinsurance(repayCalDTO);
            }
        });
        //重开案件无赔款不发短信
        Boolean flag = true;
        if(caseTimes > 1){
            Integer count = paymentItemMapper.isPay(reportNo,caseTimes);
            if(count == 0){
                flag = false;
            }
        }
        if (flag) {
            try {
                String requestId = MDC.get(BaseConstant.REQUEST_ID);
                smsInfoService.sendBusinessSmsAsync(SmsTypeEnum.VERIFY_REVIEW, reportNo, caseTimes, null, requestId);
            } catch (Exception e) {
                LogUtil.warn("核赔完成发送短信失败,不影响原有流程", e);
            }

        }
    }

    /**
     * 有预赔但未理算的会有点问题 默认插入一条
     * @param reportNo
     * @param caseTimes
     */
    private void initNoSttlePaymentItem(String reportNo, Integer caseTimes){
        LogUtil.info("initNoSttlePaymentItem1");
        // 查理赔理算保单赔付金额
        List<PolicyPayDTO> policyPayDTOS = policyPayService.getByReportNo(reportNo,caseTimes);
//        riskPropertyService.getRiskPropertyPlan(policyPayDTOS);
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        paymentItemDTO.setClaimType("1");
        List<PaymentItemDTO> allItems = paymentItemService.getPaymentItem(paymentItemDTO);
        //将费用支付项目替换为子表支付项
        Map<String,List<PaymentItemDTO>> feeItemMap = payInfoNoticeThirdPartyCoreSAO.getPaymentItemAndFeeItem(reportNo,caseTimes,null);
        allItems.stream()
                .filter(item->item.getPaymentType().contains("J") && "1".equals(item.getIsFullPay())).collect(Collectors.toList())
                .forEach(fee ->{
                    List<PaymentItemDTO> feeItemList = feeItemMap.get(fee.getIdClmPaymentItem());
                    if(feeItemList!=null && feeItemList.size()>0){
                        allItems.remove(fee);
                        allItems.addAll(feeItemList);
                    }
                });
        if (CollectionUtils.isEmpty(allItems)){
            return;
        }
        // 责任拆分 很重要的逻辑 后续应该还要调整 预赔的没算在内
        splitPlanAndDuty(policyPayDTOS,allItems);
        //已预赔未理算的保单，需要生成一条支付项
//        List<PolicyPayDTO> differentSet = policyPayDTOS.stream().filter(policy->
//                 !allItems.stream().map(PaymentItemDTO::getPolicyNo).collect(Collectors.toList())
//                        .contains(policy.getPolicyNo())).collect(Collectors.toList());
//        differentSet.forEach(policy->{
//            if (policyPayService.isPolicyHasPrePay(reportNo,caseTimes,policy.getPolicyNo())){
//                LogUtil.info("initNoSttlePaymentItem2");
//                String generateCaseNo = commonService.generateNo( NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO,policy.getDepartmentCode());
//                PaymentItemDTO dto = new PaymentItemDTO();
//                // 计算书号
//                dto.setCompensateNo(generateCaseNo);
//                dto.setReportNo(reportNo);
//                dto.setCaseTimes(caseTimes);
//                dto.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
//                dto.setPolicyNo(policy.getPolicyNo());
//                dto.setCaseNo(policy.getCaseNo());
//                dto.setClaimType(PrintConstValues.CLAIM_TYPE_PAY);
//                dto.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
//                dto.setCollectPaySign(SettleConst.COLLECTION);
//                dto.setMergeSign(SettleConst.NOT_MERGE);
//                dto.setIdClmBatch("");
//                dto.setPaymentCurrencyCode(SettleConst.RMB);
//                dto.setPaymentAmount(BigDecimal.ZERO);
//                dto.setIdClmPaymentInfo("");
//                dto.setClientName("");
//                dto.setCollectPayApproach("");
//                paymentItemService.addPaymentItem(dto);
//            }
//
//        });
    }

    /**
      *
      * @Description 核赔通过 根据责任赔付比例拆分 修改此方法请注意同步修改 splitPlanAndDuty4Fee 方法
      * <AUTHOR>
      * @Date 2023/6/28 14:04
      **/
    @Override
    public void splitPlanAndDuty(List<PolicyPayDTO> policyPayList, List<PaymentItemDTO> allItems) {
        log.info("splitPlanAndDuty入参,policyPayList:{}, allItems:{}",JSON.toJSONString(policyPayList),JSON.toJSONString(allItems));
        BigDecimal sumChgItemAmount = allItems.stream().map(PaymentItemDTO::getChgPaymentAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        allItems.forEach(dto -> {
            // 根据支付项的保单号找到对应理算的理算信息
            PolicyPayDTO policyPayDTO = policyPayList.stream().filter(policyPay -> dto.getPolicyNo().equals(policyPay.getPolicyNo())).findFirst().orElseThrow(() -> new GlobalBusinessException("未查到待支付的支付项信息"));
            // 保单理算金额
            BigDecimal policyAmount = policyPayDTO.getSettleAmount();
            // 根据保单号查询产品编码和产品大类
            //todo tzj 直接查询的承保表，global不适用
            Map<String, String> productMap = new HashMap<>();
            if(globalPolicyService.checkGlobalPolicyNo(policyPayDTO.getPolicyNo())){
                //global保单查询产品大类，产品小类
                productMap = ahcsPolicyInfoMapper.getPlyBaseInfo(policyPayDTO.getReportNo(),policyPayDTO.getPolicyNo());
            }else{
                productMap = ocasMapper.getPlyBaseInfo(policyPayDTO.getPolicyNo());
            }
            String productCode = MapUtils.getString(productMap, "productCode");
            String productClass = MapUtils.getString(productMap, "productClass");
//            productCode = ObjectUtil.isNotEmpty(productCode)?productCode:"03";
//            productClass = ObjectUtil.isNotEmpty(productClass)?productClass:"03";

            String idClmPaymentItem = dto.getIdClmPaymentItem();
            // 共保取coinsuranceActualAmount
            BigDecimal paymentAmount = BaseConstant.STRING_1.equals(dto.getCoinsuranceMark()) ? dto.getCoinsuranceActualAmount() : dto.getPaymentAmount();
            // 费用时，我司承担金额变化量等于理算金额
            if(SettleConst.PAYMENT_TYPE_FEE.equals(dto.getPaymentType()) ){
                dto.setChgCoinsuranceActualAmount(dto.getPaymentAmount());
            }
            // 赔付金额变化量 共保取ChgCoinsuranceActualAmount(实付金额变化量，已经根据是否全额给付判断是否乘以共保比例)
            BigDecimal chgPaymentAmount = BaseConstant.STRING_1.equals(dto.getCoinsuranceMark()) ?dto.getChgCoinsuranceActualAmount() :
                                                        dto.getChgPaymentAmount() == null ? BigDecimal.ZERO : dto.getChgPaymentAmount();
            if(chgPaymentAmount == null){
                chgPaymentAmount=BigDecimal.ZERO;
            }
            List<ClmsPaymentPlan> clmsPaymentPlans = new ArrayList<>();
            List<ClmsPaymentDuty> clmsPaymentDutys = new ArrayList<>();
            // 根据理算金额升序排序
            List<PlanPayDTO> planPayDTOS = policyPayDTO.getPlanPayArr().stream().sorted(Comparator.comparing(PlanPayDTO::getSettleAmount)).collect(Collectors.toList());
            LogUtil.info("报案号={}，根据理算金额升序排序后的信息={}",dto.getReportNo(), JsonUtils.toJsonString(planPayDTOS));
            // 险种累计金额（价税合计） 最后一个险种做减法
            BigDecimal planPaySumAmount = BigDecimal.ZERO;
            // 险种税额累计金额
            BigDecimal planTaxAmountSum = BigDecimal.ZERO;
            // 险种不含税金额累计金额
            BigDecimal planNoTaxAmountSum = BigDecimal.ZERO;
            // 税率
            BigDecimal taxRate = BigDecimal.ZERO;
            // 支付项不含税金额 默认不计税 等于 支付项金额
            BigDecimal noTaxAmountPay = paymentAmount;
            // 支付项税额 默认0
            BigDecimal taxAmountPay = BigDecimal.ZERO;
            // 赔付金额合计变化量
            BigDecimal chgPaymentSumAmount = BigDecimal.ZERO;
            String invoiceType = "";
            // 查询费用的发票金额 税率等信息
            if (PaymentTypeEnum.FEE.getType().equals(dto.getPaymentType()) || PaymentTypeEnum.PRE_FEE.getType().equals(dto.getPaymentType())
            || PaymentTypeEnum.PRE_COIN_FEE.getType().equals(dto.getPaymentType()) || PaymentTypeEnum.COIN_FEE.getType().equals(dto.getPaymentType())) {
                //费用有关联责任，不用拆分到每个有赔款的责任上
                List<FeeInfoDTO> feeInfoDTOList = Optional.ofNullable(feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem)).orElse(new ArrayList<>());
                FeeInfoDTO feeInfoDTO = Optional.ofNullable(feeInfoDTOList.get(0)).orElse(new FeeInfoDTO());
                if (null != feeInfoDTO && null !=  feeInfoDTO.getIdAhcsFeePay()) {
                    InvoiceInfoDTO invoiceInfo = new InvoiceInfoDTO();
                    invoiceInfo.setIdAhcsFeePay(feeInfoDTO.getIdAhcsFeePay());
                    invoiceInfo = feePayMapper.getInvoiceInfo(invoiceInfo);
                    feeInfoDTO.setInvoiceInfo(invoiceInfo);
                }
                InvoiceInfoDTO invoiceInfoDTO = Optional.ofNullable(feeInfoDTO.getInvoiceInfo()).orElse(new InvoiceInfoDTO());
                Integer tax = Optional.ofNullable(invoiceInfoDTO.getTaxRate()).orElse(0);
                taxRate = nvl(BigDecimal.valueOf(tax),0).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                noTaxAmountPay = Optional.ofNullable(invoiceInfoDTO.getNoTaxAmount()).orElse(paymentAmount);
                taxAmountPay= Optional.ofNullable(invoiceInfoDTO.getTaxAmount()).orElse(BigDecimal.ZERO);
                invoiceType = invoiceInfoDTO.getInvoiceType();
                boolean isMainCoinsFlag = coinsureService.isMainCoinsureFlag(policyPayDTO.getReportNo());
                if(isMainCoinsFlag && "1".equals(dto.getIsFullPay())){
                    taxAmountPay = dto.getCoinsuranceActualTax();
                    noTaxAmountPay = dto.getCoinsuranceActualNoTax();
                }
                String dutyCode = feeInfoDTO.getDutyCode();
                String planCode = planPayMapper.getPlanCodeByCaseNo(dto.getCaseNo(),dutyCode);
                if(ObjectUtil.isEmpty(planCode)){
                    planCode = ahcsPolicyPlanMapper.getPlanCodeByDutyCode(dto.getReportNo(),dutyCode);
                }
                //查询险种大类
                //todo tzj 直接查的承保表，global不适用
                String kindCode = "";
                if(globalPolicyService.checkGlobalPolicyNo(policyPayDTO.getPolicyNo())){
                    //global保单获取险种大类
                    Map<String,String> map = ahcsPolicyInfoMapper.getPlyBaseInfo(policyPayDTO.getReportNo(),policyPayDTO.getPolicyNo());
                    kindCode = map.get("productClass");
                }else {
                    kindCode = ocasMapper.getPlyPlanInfo(policyPayDTO.getPolicyNo(), planCode);
                }
                String planId = UuidUtil.getUUID();
                ClmsPaymentPlan clmsPaymentPlan = new ClmsPaymentPlan();
                clmsPaymentPlan.setIdClmsPaymentPlan(planId);
                clmsPaymentPlan.setIdClmPaymentItem(ObjectUtil.isNotEmpty(dto.getIdClmPaymentItemFee())?dto.getIdClmPaymentItemFee():dto.getIdClmPaymentItem());
                clmsPaymentPlan.setPlanCode(planCode);
                clmsPaymentPlan.setKindCode(kindCode);
                clmsPaymentPlan.setProductCode(productCode);
                clmsPaymentPlan.setProductLineCode(productClass);
                clmsPaymentPlan.setPlanPayAmount(paymentAmount);
                clmsPaymentPlan.setNoTaxAmount(noTaxAmountPay);
                clmsPaymentPlan.setTaxAmount(taxAmountPay);

                clmsPaymentPlans.add(clmsPaymentPlan);
                log.info("核赔通过组装paymentPlan数据:{}",JSON.toJSONString(clmsPaymentPlans));
                ClmsPaymentDuty clmsPaymentDuty = new ClmsPaymentDuty();
                clmsPaymentDuty.setIdClmsPaymentPlan(planId);
                clmsPaymentDuty.setDutyCode(dutyCode);
                clmsPaymentDuty.setDutyPayAmount(paymentAmount);
                clmsPaymentDuty.setNoTaxAmount(noTaxAmountPay);
                clmsPaymentDuty.setTaxAmount(taxAmountPay);
                clmsPaymentDutys.add(clmsPaymentDuty);
            }else{
                // 如果有理算赔付金额则用理算赔付金额比例拆分 否则直接平摊 最后一个险种、责任上做尾差处理
                int planSize = planPayDTOS.size();
                for (int i = 0; i < planSize; i++) {
                    PlanPayDTO planPayDTO = planPayDTOS.get(i);
                    BigDecimal planSettleAmount = planPayDTO.getSettleAmount();
                    BigDecimal chgPlanPayAmount = planPayDTO.getChgPlanPayAmount();
                    //查询险种大类
                    //查询险种大类
                    //todo tzj 直接查的承保表，global不适用
                    String kindCode = "";
                    if(globalPolicyService.checkGlobalPolicyNo(policyPayDTO.getPolicyNo())){
                        //global保单获取险种大类
                        Map<String,String> map = ahcsPolicyInfoMapper.getPlyBaseInfo(policyPayDTO.getReportNo(),policyPayDTO.getPolicyNo());
                        kindCode = map.get("productClass");
                    }else {
                        kindCode = ocasMapper.getPlyPlanInfo(policyPayDTO.getPolicyNo(), planPayDTO.getPlanCode());
                    }
                    String planId = UuidUtil.getUUID();
                    ClmsPaymentPlan clmsPaymentPlan = new ClmsPaymentPlan();
                    clmsPaymentPlan.setIdClmsPaymentPlan(planId);
                    clmsPaymentPlan.setIdClmPaymentItem(idClmPaymentItem);
                    clmsPaymentPlan.setPlanCode(planPayDTO.getPlanCode());
                    clmsPaymentPlan.setKindCode(kindCode);
                    clmsPaymentPlan.setProductCode(productCode);
                    clmsPaymentPlan.setProductLineCode(productClass);
                    // 每个险种上的拆分 金额 税额 不含税金额
                    BigDecimal planSplitPayAmount;
                    BigDecimal planTaxAmount;
                    BigDecimal planNoTaxAmount;
                    // 险种赔付金额变化量
                    BigDecimal chgPlanSplitPayAmount =BigDecimal.ZERO;

                    // 根据险种大类判断是否免税 02健康险 免税  03意外险 计税 不计税的也正常摊
                    // 最后一个用减法
                    if (i == (planSize - 1)) {
                        planSplitPayAmount = paymentAmount.subtract(planPaySumAmount).setScale(2, RoundingMode.HALF_UP);
                        planTaxAmount = taxAmountPay.subtract(planTaxAmountSum).setScale(2, RoundingMode.HALF_UP);
                        planNoTaxAmount = noTaxAmountPay.subtract(planNoTaxAmountSum).setScale(2, RoundingMode.HALF_UP);
                        // 险种赔付金额变化量
                        chgPlanSplitPayAmount = chgPaymentAmount.subtract(chgPaymentSumAmount).setScale(2, RoundingMode.HALF_UP);
                    } else {
                        // 如果保单理算金额是 0 则直接各个险种平摊，该保单只配费用 没有赔款时 会如此
                        if (policyAmount.compareTo(BigDecimal.ZERO) != 0) {
                            // 险种拆分金额 = 赔付金额 * 险种理算金额 / 保单理算金额
                            planSplitPayAmount = paymentAmount.multiply(planSettleAmount).divide(policyAmount, 2, RoundingMode.HALF_UP);
                        } else {
                            planSplitPayAmount = paymentAmount.divide(BigDecimal.valueOf(planSize), 2, RoundingMode.HALF_UP);
                        }
                        if(!(SettleConst.PAYMENT_TYPE_PREPAY.equals(dto.getPaymentType())
                                ||SettleConst.PAYMENT_TYPE_REINSURE_PAY.equals(dto.getPaymentType())
                                ||SettleConst.PAYMENT_TYPE_FEE_PREPAY.equals(dto.getPaymentType())
                                ||SettleConst.PAYMENT_TYPE_REINSURE_FEE.equals(dto.getPaymentType())
                                ||chgPlanPayAmount==null
                        )){
                            //如果领款人总新增赔付金额 大于0 则用新增赔付金额比例拆分
                            if (sumChgItemAmount.compareTo(BigDecimal.ZERO) != 0) {

                                chgPlanSplitPayAmount = chgPaymentAmount.multiply(chgPlanPayAmount).divide(sumChgItemAmount, 2, RoundingMode.HALF_UP);
                            } else if (chgPlanPayAmount != null && chgPlanPayAmount.compareTo(BigDecimal.ZERO) != 0) {

                                //如果是共保且非全额给付，需要乘共保比例
                                if(BaseConstant.STRING_1.equals(dto.getCoinsuranceMark()) && BaseConstant.STRING_0.equals(dto.getIsFullPay())){
                                    paymentAmount = dto.getPaymentAmount();
                                    chgPlanPayAmount = chgPlanPayAmount.multiply(dto.getCoinsuranceRatio()).setScale(2, RoundingMode.HALF_UP);
                                }
                                //如果新增总赔付金额为0，且新增险种赔付金额不为0，则用领款人理算金额占比对险种赔付金额变化量进行拆分
                                chgPlanSplitPayAmount = chgPlanPayAmount.multiply(paymentAmount).divide(policyAmount, 2, RoundingMode.HALF_UP);
                            }
                        }

                        planNoTaxAmount = planSplitPayAmount.divide(BigDecimal.ONE.add(taxRate), 2, RoundingMode.HALF_UP);
                        planTaxAmount = planSplitPayAmount.subtract(planNoTaxAmount);
                    }
                    planTaxAmountSum = planTaxAmountSum.add(planTaxAmount).setScale(2, RoundingMode.HALF_UP);
                    planNoTaxAmountSum = planNoTaxAmountSum.add(planNoTaxAmount).setScale(2, RoundingMode.HALF_UP);
                    planPaySumAmount = planPaySumAmount.add(planSplitPayAmount).setScale(2, RoundingMode.HALF_UP);
                    chgPaymentSumAmount = chgPaymentSumAmount.add(chgPlanSplitPayAmount).setScale(2, RoundingMode.HALF_UP);
                    log.info("planSize={},planPaySumAmount={},chgPaymentSumAmount={},chgPlanSplitPayAmount={},clmsPaymentPlan={}",planSize,planPaySumAmount,chgPaymentSumAmount,chgPlanSplitPayAmount,clmsPaymentPlan);
                    //根据险种编码、发票是否专票普票判断是否价税拆分
                    //1 险种不免税+专票 价税拆分,   no_tax_amount和plan_pay_amount金额不同
                    //2 险种不免税+普票 价税不拆分， no_tax_amount和plan_pay_amount金额一致
                    //3 险种免税+专票  价税不拆分，  no_tax_amount和plan_pay_amount金额一致
                    //4 险种免税+普票  价税不拆分，  no_tax_amount和plan_pay_amount金额一致
                    Boolean isFeeDutyFree = isFeeDutyFree(policyPayDTO.getPolicyNo(), planPayDTO.getPlanCode(), dto, invoiceType);
                    //费用发票：如果险种不免税，并且是专票，才需要价税拆分，否则含税金额和不含税金额一致
                    if (isFeeDutyFree){
                        clmsPaymentPlan.setNoTaxAmount(planSplitPayAmount);
                        clmsPaymentPlan.setTaxAmount(new BigDecimal(0));
                    }else {
                        clmsPaymentPlan.setNoTaxAmount(planNoTaxAmount);
                        clmsPaymentPlan.setTaxAmount(planTaxAmount);
                    }
                    clmsPaymentPlan.setPlanPayAmount(planSplitPayAmount);

                    if(SettleConst.PAYMENT_TYPE_PREPAY.equals(dto.getPaymentType())
                            ||SettleConst.PAYMENT_TYPE_REINSURE_PAY.equals(dto.getPaymentType())
                            ||SettleConst.PAYMENT_TYPE_FEE_PREPAY.equals(dto.getPaymentType())
                            ||SettleConst.PAYMENT_TYPE_REINSURE_FEE.equals(dto.getPaymentType())
                            ||chgPlanPayAmount == null
                    ){
                        clmsPaymentPlan.setChgPlanPayAmount(planSplitPayAmount);
                    }else{
                        clmsPaymentPlan.setChgPlanPayAmount(chgPlanSplitPayAmount);
                    }
                    clmsPaymentPlans.add(clmsPaymentPlan);
                    log.info("核赔通过组装paymentPlan数据:{}",JSON.toJSONString(clmsPaymentPlans));
                    List<DutyPayDTO> dutyPayDTOS = planPayDTO.getDutyPayArr().stream().sorted(Comparator.comparing(DutyPayDTO::getSettleAmount)).collect(Collectors.toList());
                    int dutySize = dutyPayDTOS.size();
                    BigDecimal dutyPaySumAmount = BigDecimal.ZERO;
                    BigDecimal dutyTaxAmountSum = BigDecimal.ZERO;
                    BigDecimal dutyNoTaxAmountSum = BigDecimal.ZERO;
                    BigDecimal chgDutyPaymentSumAmount = BigDecimal.ZERO;
                    // 责任赔付金额变化量
                    BigDecimal chgDutyPayAmount = BigDecimal.ZERO;
                    for (int j = 0; j < dutySize; j++) {
                        DutyPayDTO dutyPayDTO = dutyPayDTOS.get(j);
                        BigDecimal dutySettleAmount = dutyPayDTO.getSettleAmount();
                        ClmsPaymentDuty clmsPaymentDuty = new ClmsPaymentDuty();
                        clmsPaymentDuty.setIdClmsPaymentPlan(planId);
                        clmsPaymentDuty.setDutyCode(dutyPayDTO.getDutyCode());
                        BigDecimal dutySplitPayAmount;
                        BigDecimal dutyTaxAmount;
                        BigDecimal dutyNoTaxAmount;
                        // 最后一个用减法
                        if (j == (dutySize - 1)) {
                            dutySplitPayAmount = planSplitPayAmount.subtract(dutyPaySumAmount).setScale(2, RoundingMode.HALF_UP);
                            dutyTaxAmount = planTaxAmount.subtract(dutyTaxAmountSum).setScale(2, RoundingMode.HALF_UP);
                            dutyNoTaxAmount = planNoTaxAmount.subtract(dutyNoTaxAmountSum).setScale(2, RoundingMode.HALF_UP);
                            chgDutyPayAmount  = chgPlanSplitPayAmount.subtract(chgDutyPaymentSumAmount).setScale(2, RoundingMode.HALF_UP);
                        } else {
                            if (planSettleAmount.compareTo(BigDecimal.ZERO) != 0) {
                                // 责任拆分 = 赔付金额 * 责任理算金额 / 险种理算金额
                                dutySplitPayAmount = planSplitPayAmount.multiply(dutySettleAmount).divide(planSettleAmount, 2, RoundingMode.HALF_UP);
                            } else {
                                dutySplitPayAmount = planSplitPayAmount.divide(BigDecimal.valueOf(dutySize), 2, RoundingMode.HALF_UP);
                            }
//                        if(chgDutyPayAmount.compareTo(BigDecimal.ZERO) != 0){
//                            chgDutyPayAmount = chgPlanSplitPayAmount.multiply(dutySettleAmount).divide(planSettleAmount, 2, RoundingMode.HALF_UP);
//                        }else {
//                            chgDutyPayAmount = chgPlanSplitPayAmount.divide(BigDecimal.valueOf(dutySize), 2, RoundingMode.HALF_UP);
//
//                        }
                            dutyNoTaxAmount = dutySplitPayAmount.divide(BigDecimal.ONE.add(taxRate), 2, RoundingMode.HALF_UP);
                            dutyTaxAmount = dutySplitPayAmount.subtract(dutyNoTaxAmount);
                        }
                        dutyPaySumAmount = dutyPaySumAmount.add(dutySplitPayAmount).setScale(2, RoundingMode.HALF_UP);
                        dutyNoTaxAmountSum = dutyNoTaxAmountSum.add(dutyNoTaxAmount).setScale(2, RoundingMode.HALF_UP);
                        dutyTaxAmountSum = dutyTaxAmountSum.add(dutyTaxAmount).setScale(2, RoundingMode.HALF_UP);
                        chgDutyPaymentSumAmount = chgDutyPaymentSumAmount.add(chgDutyPayAmount).setScale(2, RoundingMode.HALF_UP);

                        //费用发票：如果险种不免税，并且是专票，才需要价税拆分，否则含税金额和不含税金额一致
                        if (isFeeDutyFree){
                            clmsPaymentDuty.setTaxAmount(new BigDecimal(0));
                            clmsPaymentDuty.setNoTaxAmount(dutySplitPayAmount);
                        }else {
                            clmsPaymentDuty.setTaxAmount(dutyTaxAmount);
                            clmsPaymentDuty.setNoTaxAmount(dutyNoTaxAmount);
                        }
                        clmsPaymentDuty.setDutyPayAmount(dutySplitPayAmount);
//                    clmsPaymentDuty.setChgDutyPayAmount(chgDutyPayAmount);
                        clmsPaymentDutys.add(clmsPaymentDuty);
                    }
                }
            }

            LogUtil.info("报案号={}，险种信息clmsPaymentPlans={}",dto.getReportNo(), JsonUtils.toJsonString(clmsPaymentPlans));
            LogUtil.info("报案号={}，责任信息clmsPaymentDutys={}",dto.getReportNo(), JsonUtils.toJsonString(clmsPaymentDutys));
            clmsPaymentPlanService.addBatch(clmsPaymentPlans);
            clmsPaymentDutyService.addBatch(clmsPaymentDutys);
        });
    }

    /**
     * 判断费用类型的险种是否免税
     *
     * @param policyNo
     * @param planCode
     * @param dto
     * @param invoiceType
     * @return
     */
    private Boolean isFeeDutyFree(String policyNo, String planCode, PaymentItemDTO dto, String invoiceType) {
        //默认不免税
        Boolean isDutyFree = false;
        if (!PaymentTypeEnum.FEE.getType().equals(dto.getPaymentType()) && !PaymentTypeEnum.PRE_FEE.getType().equals(dto.getPaymentType())) {
            return false;
        }
        //todo tzj 查询产品配置税率配置 global不适用
        Map<String, Object> taxRateProductClassMap = productMapper.getPlanTaxRateAndProductClass(planCode);
        log.info("splitPlanAndDuty-isSplitTax价税分离查询险种大类和税率,policyNo:{},planCode:{},result:{}",policyNo, planCode, JSON.toJSONString(taxRateProductClassMap));
        BigDecimal planTaxRate = null;
        //todo tzj 当前查询不到且是global保单时默认为0,后续确认调整
        if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
            planTaxRate = BigDecimal.ZERO;
            taxRateProductClassMap = new HashMap<>();
        }else{
            planTaxRate = (BigDecimal)taxRateProductClassMap.get("taxRate");
        }
        if (com.paic.ncbs.base.constant.Constants.PRODUCT_CLASS_HEATH.equals(taxRateProductClassMap.get("productClass"))) {
            PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(policyNo,null);
            //大于365天免税
            if (DateUtils.daysOfTwoDate(policyDto.getPolicyEndDate(), policyDto.getPolicyStartDate()) >= 365) {
                isDutyFree = true;
            }
        } else {
            if (planTaxRate.compareTo(new BigDecimal(0)) <= 0) {
                isDutyFree = true;
            }
        }
        Boolean isExistSpecialInvoice = InvoiceTypeEnum.isExistSpecialInvoice(invoiceType);
        if (!isDutyFree && isExistSpecialInvoice){
            isDutyFree = false;
        }else {
            isDutyFree = true;
        }
        return isDutyFree;
    }

    /**
      *
      * @Description 费用类的拆分
      * <AUTHOR>
      * @Date 2023/6/6 13:52
      **/
    @Override
    public void splitPlanAndDuty4Fee(List<EstimatePolicyDTO> estimatePolicyDTOS, List<PaymentItemDTO> allItems) {
        allItems.forEach(dto -> {
            EstimatePolicyDTO estimatePolicyDTO = estimatePolicyDTOS.stream().filter(policyPay -> dto.getPolicyNo().equals(policyPay.getPolicyNo())).findFirst().orElseThrow(() -> new GlobalBusinessException("未查到待支付的支付项信息"));
            // 如果有理算赔付金额则用理算赔付金额比例拆分，否则用立案金额拆分
            String policyNo = estimatePolicyDTO.getPolicyNo();
            Map<String, String> productMap = new HashMap<>();
            if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                productMap = ahcsPolicyInfoMapper.getPlyBaseInfo(estimatePolicyDTO.getReportNo(),policyNo);
            }else{
                productMap = ocasMapper.getPlyBaseInfo(policyNo);
            }
            String productCode = MapUtils.getString(productMap, "productCode");
            String productClass = MapUtils.getString(productMap, "productClass");
            String idClmPaymentItem = dto.getIdClmPaymentItem();
            BigDecimal paymentAmount = BaseConstant.STRING_1.equals(dto.getCoinsuranceMark()) ? dto.getCoinsuranceActualAmount() : dto.getPaymentAmount();
            List<EstimatePlanDTO> estimatePlanList = estimatePolicyDTO.getEstimatePlanList();
            List<ClmsPaymentPlan> clmsPaymentPlans = new ArrayList<>();
            List<ClmsPaymentDuty> clmsPaymentDutys = new ArrayList<>();
            BigDecimal planPaySumAmount = BigDecimal.ZERO;
            BigDecimal planTaxAmountSum = BigDecimal.ZERO;
            BigDecimal planNoTaxAmountSum = BigDecimal.ZERO;
            BigDecimal taxRate = BigDecimal.ZERO;
            // 不含税金额
            BigDecimal noTaxAmountPay = paymentAmount;
            // 计算总税额
            BigDecimal taxAmountPay = BigDecimal.ZERO;
            String invoiceType = "";
            if (PaymentTypeEnum.FEE.getType().equals(dto.getPaymentType()) || PaymentTypeEnum.PRE_FEE.getType().equals(dto.getPaymentType())
                    || PaymentTypeEnum.PRE_COIN_FEE.getType().equals(dto.getPaymentType()) || PaymentTypeEnum.COIN_FEE.getType().equals(dto.getPaymentType())) {
                //费用有关联责任，不用拆分到每个有赔款的责任上
                List<FeeInfoDTO> feeInfoDTOList = Optional.ofNullable(feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem)).orElse(new ArrayList<>());
                FeeInfoDTO feeInfoDTO = Optional.ofNullable(feeInfoDTOList.get(0)).orElse(new FeeInfoDTO());
                if (null != feeInfoDTO && null !=  feeInfoDTO.getIdAhcsFeePay()) {
                    InvoiceInfoDTO invoiceInfo = new InvoiceInfoDTO();
                    invoiceInfo.setIdAhcsFeePay(feeInfoDTO.getIdAhcsFeePay());
                    invoiceInfo = feePayMapper.getInvoiceInfo(invoiceInfo);
                    feeInfoDTO.setInvoiceInfo(invoiceInfo);
                }
                InvoiceInfoDTO invoiceInfoDTO = Optional.ofNullable(feeInfoDTO.getInvoiceInfo()).orElse(new InvoiceInfoDTO());
                Integer tax = Optional.ofNullable(invoiceInfoDTO.getTaxRate()).orElse(0);
                taxRate = nvl(BigDecimal.valueOf(tax),0).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                noTaxAmountPay = Optional.ofNullable(invoiceInfoDTO.getNoTaxAmount()).orElse(paymentAmount);
                taxAmountPay= Optional.ofNullable(invoiceInfoDTO.getTaxAmount()).orElse(BigDecimal.ZERO);
                invoiceType = invoiceInfoDTO.getInvoiceType();
                boolean isMainCoinsFlag = coinsureService.isMainCoinsureFlag(estimatePolicyDTO.getReportNo());
                if(isMainCoinsFlag && "1".equals(dto.getIsFullPay())){
                    taxAmountPay = dto.getCoinsuranceActualTax();
                    noTaxAmountPay = dto.getCoinsuranceActualNoTax();
                }
                String dutyCode = feeInfoDTO.getDutyCode();
                String planCode = planPayMapper.getPlanCodeByCaseNo(dto.getCaseNo(),dutyCode);
                if(ObjectUtil.isEmpty(planCode)){
                    planCode = ahcsPolicyPlanMapper.getPlanCodeByDutyCode(dto.getReportNo(),dutyCode);
                }
                String kindCode = "";
                if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                    kindCode = productClass;
                }else{
                    kindCode = ocasMapper.getPlyPlanInfo(policyNo, planCode);
                }
                String planId = UuidUtil.getUUID();
                ClmsPaymentPlan clmsPaymentPlan = new ClmsPaymentPlan();
                clmsPaymentPlan.setIdClmsPaymentPlan(planId);
                clmsPaymentPlan.setIdClmPaymentItem(ObjectUtil.isNotEmpty(dto.getIdClmPaymentItemFee())?dto.getIdClmPaymentItemFee():dto.getIdClmPaymentItem());
                clmsPaymentPlan.setPlanCode(planCode);
                clmsPaymentPlan.setKindCode(kindCode);
                clmsPaymentPlan.setProductCode(productCode);
                clmsPaymentPlan.setProductLineCode(productClass);
                clmsPaymentPlan.setPlanPayAmount(paymentAmount);
                clmsPaymentPlan.setNoTaxAmount(noTaxAmountPay);
                clmsPaymentPlan.setTaxAmount(taxAmountPay);

                clmsPaymentPlans.add(clmsPaymentPlan);
                log.info("核赔通过组装paymentPlan数据:{}",JSON.toJSONString(clmsPaymentPlans));
                ClmsPaymentDuty clmsPaymentDuty = new ClmsPaymentDuty();
                clmsPaymentDuty.setIdClmsPaymentPlan(planId);
                clmsPaymentDuty.setDutyCode(dutyCode);
                clmsPaymentDuty.setDutyPayAmount(paymentAmount);
                clmsPaymentDuty.setNoTaxAmount(noTaxAmountPay);
                clmsPaymentDuty.setTaxAmount(taxAmountPay);
                clmsPaymentDutys.add(clmsPaymentDuty);
            }
            clmsPaymentPlanService.addBatch(clmsPaymentPlans);
            clmsPaymentDutyService.addBatch(clmsPaymentDutys);
        });
    }

    /*
    private void checkSettleAmount(VerifyInfoVO verifyInfoVO){
        List<PolicyPayDTO> policyPayInfoArr = policyPayService.getByReportNo(verifyInfoVO.getReportNo(), verifyInfoVO.getCaseTimes());
        if (CollectionUtils.isEmpty(policyPayInfoArr)) {
            throw new GlobalBusinessException(ErrorCode.Settle.POLICY_PAY_IS_NOT_EXSITS, "赔付数据不存在,请先初始化");
        }

//        riskPropertyService.getRiskPropertyPlan(policyPayInfoArr);
        SettleHelper.setDetailExInfo(policyPayInfoArr);

        maxPayService.initPoliciesPayMaxPay(policyPayInfoArr, null);
        policyPayInfoArr.forEach(policy->{
            List<PlanPayDTO> planPayList = policy.getPlanPayArr();
            planPayList.forEach(plan->{
                List<DutyPayDTO> dutyPayList = plan.getDutyPayArr();
                dutyPayList.forEach(duty->{
                    if (duty.getMaxAmountPay().compareTo(nvl(duty.getSettleAmount(),0)) < 0) {
                        throw new GlobalBusinessException("理算金额不能大于剩余赔付额，请重新理算");
                    }
                });
            });
        });
    }
    */

    //发送短信
    public void sendReportSms(String reportNo,Integer caseTimes){
        try {
            BigDecimal payAmount = Optional.ofNullable(policyPayMapper.getPolicyPay(reportNo,caseTimes)).orElse(new BigDecimal("0"));
            Set<String> phoneNumbers = new HashSet<>();
            List<LinkManEntity> linkManEntities = reportService.getLinkMans(reportNo,caseTimes);
            for (LinkManEntity man : linkManEntities){
                String telephone = man.getLinkManTelephone();
                if(!ConstValues.YES.equals(man.getSendMessage()) || telephone == null || telephone.length() != 11){
                    continue;
                }
                if(phoneNumbers.add(telephone)){
                    //手机号去重发送
                    String txt = String.format(Constants.VERIFY_SMS_TEMPLATE,reportNo,payAmount);
                    smsInfoService.sendSmsByAsync(new SmsInfoDTO(reportNo,telephone,txt,Constants.SMS_LINK_REPORT));
                }
            }
        }catch (Exception e){
            LogUtil.error("核赔通过发送短信失败，报案号：{}", reportNo, e);
        }

    }

    public void updateVerify(VerifyInfoVO verifyInfoVO) {
        String reportNo = verifyInfoVO.getReportNo();
        Integer caseTimes = verifyInfoVO.getCaseTimes();
        String user = verifyInfoVO.getVerifyUm();
        if (StringUtils.isEmptyStr(user)) {
            user = WebServletContext.getUserId();
        }
        VerifyDTO verify = verifyInfoVO.getVerify();
        if(StringUtils.isEmptyStr(verify.getIdAhcsVerify())){
            verify.setIdAhcsVerify(getVerify(reportNo,caseTimes).getIdAhcsVerify());
        }
        verify.setCreatedBy(user);
        verify.setUpdatedBy(user);
        verify.setVerifyUn(user);
        updateVerify(verify);
    }

    private void updateEndorsementInfo(VerifyInfoVO verifyInfoVO) {
        EndorsementDTO endorsement = verifyInfoVO.getEndorsement();
        if (endorsement!=null){
            endorsement.setUpdatedBy(WebServletContext.getUserId());
            endorsement.setUpdatedDate(new Date());
            endorsementService.modifyEndorsementInfo(endorsement);
        }
    }

	private String getUserId(){
		String userId = WebServletContext.getUserId();
        if (StringUtils.isEmptyStr(userId)){
            userId = "SYSTEM";
        }
		return userId;
	}

    @Override
    public VerifyDTO getVerify(String reportNo, Integer caseTimes) {
        return verifyDao.getVerify(reportNo,caseTimes);
    }

    @Override
    public void insertVerify(VerifyDTO verify) {
        if(StringUtils.isEmptyStr(verify.getCreatedBy())){
            verify.setCreatedBy(getUserId());
        }
        if(StringUtils.isEmptyStr(verify.getUpdatedBy())){
            verify.setUpdatedBy(getUserId());
        }
        if(StringUtils.isEmptyStr(verify.getIsEffective())){
            verify.setIsEffective(ConstValues.YES);
        }
        verifyDao.insertVerify(verify);
    }

    @Override
    public List<VerifyDTO> getVerifyList(String reportNo, Integer caseTimes) {
        List<VerifyDTO> verifyDTOS =  verifyDao.getVerifyList(reportNo,caseTimes);
        verifyDTOS.forEach(verifyDTO -> {
            String settleUM = verifyDTO.getSettleUM();
            if (!StringUtils.isEmptyStr(settleUM)){
                String userName = userInfoService.getUserNameById(settleUM);
                verifyDTO.setSettleUM(settleUM + "-" + userName);
            }
            String verifyUM = verifyDTO.getVerifyUn();
            if (!StringUtils.isEmptyStr(verifyUM)){
                String userName = userInfoService.getUserNameById(verifyUM);
                verifyDTO.setVerifyUn(verifyUM + "-" + userName);
            }
        });
        return verifyDTOS;
    }

    @Override
    public void updateVerify(VerifyDTO verify) {
        if(VerifyDTO.CONCLUSION_NOT_PASS.equals(verify.getVerifyConclusion())){
            verify.setIsEffective(ConstValues.NO);
        }
        if(StringUtils.isEmptyStr(verify.getVerifyUn())){
            verify.setVerifyUn(getUserId());
        }
        verifyDao.updateVerify(verify);
    }

    public void addMistakeRecord(VerifyInfoVO verifyInfoVO){
        List<String> mistakeCodeList = verifyInfoVO.getMistakeCodeList();
        if (ListUtils.isNotEmpty(mistakeCodeList)) {
            MistakeRecordDTO mistakeRecordDTO = new MistakeRecordDTO();
            mistakeRecordDTO.setReportNo(verifyInfoVO.getReportNo());
            mistakeRecordDTO.setCaseTimes(verifyInfoVO.getCaseTimes());
            mistakeRecordDTO.setMistakeRemark(verifyInfoVO.getVerify().getVerifyOpinion());
            mistakeRecordDTO.setMistakeCodeList(mistakeCodeList);
            mistakeRecordService.addMistakeRecord(mistakeRecordDTO, BpmConstants.OC_SETTLE_REVIEW);
        }
    }

    @Override
    public void checkVerifyUserGrade(String taskId,List<String> msg){
        if(StringUtils.isEmptyStr(taskId)){
            LogUtil.audit("taskId不能为空");
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        LogUtil.audit("当前任务id={}",taskId);
        TaskInfoDTO taskDto = taskInfoService.getTaskDtoByTaskId(taskId);
        if(taskDto == null){
            LogUtil.audit("taskId查任务为空");
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        //完成当前任务
        String userId = WebServletContext.getUserId();
        Integer userGrade = permissionService.getUserGrade(Constants.PERMISSION_VERIFY,taskDto.getDepartmentCode(),userId);
        if(userGrade == null){
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"你未配置案件机构的核赔权限");
        }
        if(taskDto.getTaskGrade() == null){
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"案件核赔权限为空");
        }
        String reportNo = taskDto.getReportNo();
        Integer caseTimes = taskDto.getCaseTimes();
        clmsPolicySurrenderInfoService.checkSurrenderIsOtherCase(reportNo,caseTimes);

        List<PolicyPayDTO> policyPayListByReportNo = policyPayMapper.getPolicyPayListByReportNo(reportNo, caseTimes);
        if(!CollectionUtils.isEmpty(policyPayListByReportNo)){
            String policyNo = policyPayListByReportNo.get(0).getPolicyNo();
            Date date = policyMapper.querySurrender(policyNo);
            if(null != date){
                if(new Date().before(date)){
                    List<ClmsPolicySurrenderInfoVO> surrenderSuccessCount = clmsPolicySurrenderInfoMapper.getSurrenderSuccessCount(policyNo);
                    if(!CollectionUtils.isEmpty(surrenderSuccessCount)){
                        msg.add("保单" + policyNo + "在案件" + surrenderSuccessCount.get(0).getReportNo() + "做预约解约，请注意审核");
                    }
                }else {
                    msg.add("保单已失效，请注意审核");
                }
            }
        }


        if(userGrade >= taskDto.getTaskGrade()){
            //权限满足
            msg.add(VerifyDTO.CONCLUSION_AUDIT_MSG);
        }else {
            //权限不满足，继续找高一个级别的人处理
            msg.add("提交成功，待上级审批人继续处理");
        }
    }

    private boolean checkVerifyPremission(String reportNo,String verifyConclusion,String taskId,List<String> msg, String verifyOpinion, String selectedUserId){
        if(StringUtils.isEmptyStr(taskId)){
            LogUtil.audit("taskId不能为空");
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        LogUtil.audit("当前任务id={}",taskId);
        String managerUserId = null;
        String managerUserName = null;
        TaskInfoDTO taskDto = taskInfoService.getTaskDtoByTaskId(taskId);
        if(taskDto == null){
            LogUtil.audit("taskId查任务为空");
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        //完成当前任务
        String userId = WebServletContext.getUserId();
        TaskInfoDTO endTask = new TaskInfoDTO();
        endTask.setReportNo(taskDto.getReportNo());
        endTask.setCaseTimes(taskDto.getCaseTimes());
        endTask.setTaskId(taskId);
        endTask.setUpdatedBy(userId);
        endTask.setStatus(BpmConstants.TASK_STATUS_COMPLETED);
        taskInfoService.modifyTaskInfoByDefKey(endTask);

        //审批退回前一个处理人
        if(VerifyDTO.CONCLUSION_AUDIT_BACK.equals(verifyConclusion)){
            TaskInfoDTO preTaskDto = taskInfoService.getTaskDtoByTaskId(taskDto.getPreTaskId());
            taskInfoMapper.updateTaskDtoByTaskId(preTaskDto.getTaskId(),userId);
            insertVerifyRecord(reportNo,taskDto.getCaseTimes(),userId);
            //操作记录
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_SETTLE_REVIEW
                    , "退回", verifyOpinion + " 审批退回前一个处理人");
            return false;
        }

        if(VerifyDTO.CONCLUSION_PASS.equals(verifyConclusion) || VerifyDTO.CONCLUSION_UPDATE_PASS.equals(verifyConclusion)){
            Integer userGrade = permissionService.getUserGrade(Constants.PERMISSION_VERIFY,taskDto.getDepartmentCode(),userId);
            if(userGrade == null){
                throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"你未配置案件机构的核赔权限");
            }
            if(taskDto.getTaskGrade() == null){
                throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"案件核赔权限为空");
            }
            if(userGrade >= taskDto.getTaskGrade()){
                //权限满足
                msg.add(VerifyDTO.CONCLUSION_AUDIT_MSG);
                return true;
            }else{
                //权限不满足，继续找高一个级别的人处理
                msg.add("提交成功，待上级审批人继续处理");
                String departmentCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
                userGrade++;
                PermissionUserDTO permissionUser = permissionService.getLatestGrade(Constants.PERMISSION_VERIFY,departmentCode,userGrade);
                TaskInfoDTO startTask = new TaskInfoDTO();
                List<String> taskIdList = new ArrayList<>();
                String newTaskId = UuidUtil.getUUID();
                taskIdList.add(newTaskId);
                BeanUtils.copyProperties(taskDto,startTask);
                startTask.setTaskId(newTaskId);
                startTask.setTaskDefinitionBpmKey(BpmConstants.OC_SETTLE_REVIEW);
                startTask.setAssigneeTime(new Date());
                startTask.setStatus(BpmConstants.TASK_STATUS_PENDING);
                startTask.setCreatedBy(userId);
                startTask.setUpdatedBy(userId);
                startTask.setAssigner(null);
                startTask.setAssigneeName(null);
                startTask.setApplyer(taskDto.getAssigner());
                startTask.setApplyerName(taskDto.getAssigneeName());
                startTask.setPreTaskId(taskDto.getTaskId());
                startTask.setAuditGrade(userGrade);
                startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
                startTask.setDepartmentCode(permissionUser.getComCode());
                taskInfoMapper.addTaskInfo(startTask);
                insertVerifyRecord(reportNo,taskDto.getCaseTimes(),userId);
                //操作记录
                operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_SETTLE_REVIEW
                        , VerifyDTO.CONCLUSION_PASS.equals(verifyConclusion) ? "通过" : "修改通过", verifyOpinion + " 待上级审批人继续处理");
                //自动调度
                if(!StringUtils.isEmptyStr(selectedUserId)){
                    String [] parts = selectedUserId.split("-",2);
                    managerUserId = parts[0];
                    managerUserName = parts[1];
                    String comCode = permissionUserMapper.getComCodeByUserId(managerUserId);
                    taskPoolService.dispatchTask(taskIdList,managerUserId,managerUserName,comCode);
                    //添加任务提醒
                    taskPoolService.addNoticesList(taskIdList,managerUserId);
                }
                return false;
            }
        }

        LogUtil.audit("未知场景");
        return false;

    }

    @Override
    public VerifyTaskDTO queryVerifyTask(String reportNo, Integer caseTimes, String taskId) {
        TaskInfoDTO taskInfoDTO = taskInfoService.getTaskDtoByTaskId(taskId);
        VerifyTaskDTO result = new VerifyTaskDTO();
        if(StringUtils.isNotEmpty(taskInfoDTO.getPreTaskId())){
            result.setShowVerifyBack(ConstValues.YES);
        }
//        if(taskInfoService.checkPolicyActualPremium(reportNo)){
//            result.setPremiumTips("保费未缴齐，请谨慎审核！");
//        }

        return result;
    }

    private void insertVerifyRecord(String reportNo,Integer caseTimes,String userId){
        VerifyDTO verify = getVerify(reportNo,caseTimes);
        if(verify != null){
            //先删除
            verify.setIsEffective(ConstValues.NO);
            verify.setUpdatedBy(userId);
            updateVerify(verify);
        }
        VerifyDTO verifyDTO = new VerifyDTO();
        verifyDTO.setReportNo(reportNo);
        verifyDTO.setCaseTimes(caseTimes);
        verifyDTO.setCreatedBy(userId);
        verifyDTO.setUpdatedBy(userId);
        verifyDTO.setSettleUM(userId);
        verifyDTO.setSettleDate(new Date());
        verifyDTO.setIdAhcsVerify(UuidUtil.getUUID());
        verifyDTO.setIdAhcsBatch(batchDao.getBatchInfo(reportNo,caseTimes).getIdAhcsBatch());
        insertVerify(verifyDTO);
    }

    private void checkGarcePerid(String reportNo, Integer caseTimes){
        //根据reportNo获取出险时间
        Date accidentDate = taskInfoMapper.getAccidentDate(reportNo);
        if(null == accidentDate){
            throw new GlobalBusinessException(GlobalResultStatus.NULL_ERROR.getCode(),"出险时间为空");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(
                "yyyy-MM-dd hh:mm:ss");
        String time = simpleDateFormat.format(accidentDate);
        //根据报案号查询所有保单号
        List<String> policyNos = feePayMapper.getAllPolicyByReportNo(reportNo, caseTimes);
        if(null == policyNos || policyNos.isEmpty()){
            throw new GlobalBusinessException(GlobalResultStatus.NULL_ERROR.getCode(),"保单号为空");
        }
        //循环调用批改接口，判断保单号是否宽限期出险,有一条是则整个流程终止并给出提示
        policyNos.forEach(policyNo->{
            CopyPolicyQueryVO vo = new CopyPolicyQueryVO();
            vo.setPolicyNo(policyNo);
            vo.setTime(time);
            String result = ocasRequest.isGarcePerid(vo);
            LogUtil.audit("checkGarcePerid policyNo:{},result:{}",policyNo,result);
            Map resultMap = JSON.parseObject(result, Map.class);
            if("success".equals(resultMap.get("returnMsg")) && null != resultMap.get("data")){
                if(Boolean.valueOf(resultMap.get("data").toString())){
                    throw new GlobalBusinessException(GlobalResultStatus.VALID_FAIL.getCode(),"宽限期出险，请联系客户补缴保费");
                }
            }else{
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"调用批改宽限期判断接口异常,请重试");
            }

        });
    }
    /**
     * 每月赔付次数校验
     * @param reportNo
     */
    private void checkEverMonthPayTims(String reportNo,Integer caseTimes) {
        List<PolicyPayDTO> copyPolicyPays = clmsQueryPolicyAllInfoService.getPolicyAllInfo(reportNo,caseTimes);
        clmsEveryMonthPayTimesCheckService.checkPayTimes(copyPolicyPays,reportNo);
    }
    /**
     * 将费用拆分到页面录入的一个责任上
     * @param dutyCode 责任代码
     * @param planCode 险种代码
     * @param paymentItemDTO 支付项Id
     */
    @Override
    public void splitPlanAndDutyFee(PaymentItemDTO paymentItemDTO, String dutyCode, String planCode) {
        List<ClmsPaymentPlan> clmsPaymentPlans = new ArrayList<>();
        List<ClmsPaymentDuty> clmsPaymentDutys = new ArrayList<>();
        String policyNo = paymentItemDTO.getPolicyNo();
        BigDecimal paymentAmount = paymentItemDTO.getPaymentAmount();
        BigDecimal noTaxAmount = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;
        InvoiceInfoDTO invoiceInfo = new InvoiceInfoDTO();
        String idClmPaymentItem = paymentItemDTO.getIdClmPaymentItem();
        //查询费用对应的税率
        if("4".equals(paymentItemDTO.getClaimType())){//追偿费用表与费用表不是同一个表
            ClmsReplevyCharge replevyChargeVo = clmsReplevyChargeMapper.getReplevyChargeByIdClmPaymentItem(idClmPaymentItem);
            if(replevyChargeVo!=null){
                invoiceInfo = feePayMapper.getInvoiceInfoById(replevyChargeVo.getId());
            }
        }else {
            List<FeeInfoDTO> feeInfoDTOList = Optional.ofNullable(feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem)).orElse(new ArrayList<>());
            FeeInfoDTO feeInfoDTO = Optional.ofNullable(feeInfoDTOList.get(0)).orElse(new FeeInfoDTO());
            if (null != feeInfoDTO && null != feeInfoDTO.getIdAhcsFeePay()) {
                invoiceInfo.setIdAhcsFeePay(feeInfoDTO.getIdAhcsFeePay());
                invoiceInfo = feePayMapper.getInvoiceInfo(invoiceInfo);
            }
        }
        String invoiceType = invoiceInfo.getInvoiceType();
        Boolean isFeeDutyFree = isFeeDutyFree(policyNo, planCode, paymentItemDTO, invoiceType);
        //费用发票：如果险种不免税，并且是专票，才需要价税拆分，否则含税金额和不含税金额一致
        if (isFeeDutyFree){
            noTaxAmount = paymentAmount;
        }else {
            noTaxAmount = Optional.ofNullable(invoiceInfo.getNoTaxAmount()).orElse(paymentAmount);
            taxAmount= Optional.ofNullable(invoiceInfo.getTaxAmount()).orElse(BigDecimal.ZERO);
        }
        //将费用金额拆分到一个责任上
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
        String productCode = MapUtils.getString(productMap, "productCode");
        String productClass = MapUtils.getString(productMap, "productClass");
        String kindCode = ocasMapper.getPlyPlanInfo(policyNo, planCode);
        String planId = UuidUtil.getUUID();
        ClmsPaymentPlan clmsPaymentPlan = new ClmsPaymentPlan();
        clmsPaymentPlan.setIdClmsPaymentPlan(planId);
        clmsPaymentPlan.setIdClmPaymentItem(paymentItemDTO.getIdClmPaymentItem());
        clmsPaymentPlan.setPlanCode(planCode);
        clmsPaymentPlan.setKindCode(kindCode);
        clmsPaymentPlan.setProductCode(productCode);
        clmsPaymentPlan.setProductLineCode(productClass);
        clmsPaymentPlan.setPlanPayAmount(paymentAmount);
        clmsPaymentPlan.setTaxAmount(taxAmount);
        clmsPaymentPlan.setNoTaxAmount(noTaxAmount);
        //责任
        ClmsPaymentDuty clmsPaymentDuty = new ClmsPaymentDuty();
        clmsPaymentDuty.setIdClmsPaymentPlan(planId);
        clmsPaymentDuty.setDutyCode(dutyCode);
        clmsPaymentDuty.setDutyPayAmount(paymentAmount);
        clmsPaymentDuty.setNoTaxAmount(noTaxAmount);
        clmsPaymentDuty.setTaxAmount(taxAmount);
        clmsPaymentDutys.add(clmsPaymentDuty);
        clmsPaymentPlans.add(clmsPaymentPlan);
        clmsPaymentPlanService.addBatch(clmsPaymentPlans);
        clmsPaymentDutyService.addBatch(clmsPaymentDutys);
    }

}
