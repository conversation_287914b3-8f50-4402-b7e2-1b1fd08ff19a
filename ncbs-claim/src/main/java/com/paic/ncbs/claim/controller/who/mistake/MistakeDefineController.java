package com.paic.ncbs.claim.controller.who.mistake;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.mistake.MistakeDefineDTO;
import com.paic.ncbs.claim.service.mistake.MistakeDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "差错原因管理")
@Controller
@RequestMapping("/who/app/mistakeDefineAction")
public class MistakeDefineController extends BaseController {

	@Autowired
	private MistakeDefineService mistakeDefineService;


	@ApiOperation("查询差错信息")
	@ResponseBody
	@GetMapping(value="/getMistakeDefineList")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "mistakeType", value = "差错类型",dataType = "String",dataTypeClass=String.class)
	})
	public ResponseResult<List<MistakeDefineDTO>> getMistakeDefineList(@RequestParam(required = false,name = "mistakeType") String mistakeType){
		// mistakeType : CheckDuty 收单差错 , Settle理算差错 不传查全部
	    LogUtil.audit("#复核差错管理#查询差错信息 mistakeType={" + mistakeType + "}");
		return ResponseResult.success(mistakeDefineService.getMistakeDefineList(mistakeType));
	}

}
