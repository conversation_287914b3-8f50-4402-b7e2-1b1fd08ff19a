package com.paic.ncbs.claim.service.qualitychecke.impl;

import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail;
import com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityDetailMapper;
import com.paic.ncbs.claim.service.qualitychecke.ClmsQualityDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 质检意见明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */

@Service
public class ClmsQualityDetailServiceImpl extends ServiceImpl<ClmsQualityDetailMapper, ClmsQualityDetail> implements ClmsQualityDetailService {
    @Autowired
    ClmsQualityDetailMapper clmsQualityDetailMapper;

    public ClmsQualityDetail selectQualityDetailById(String id) {
        return clmsQualityDetailMapper.selectQualityDetailById(id);
    }

    public List<ClmsQualityDetail> selectAllQualityDetails() {
        return clmsQualityDetailMapper.selectAllQualityDetails();
    }

    public List<ClmsQualityDetail> selectQualityDetailByCondition(ClmsQualityDetail condition) {
        return clmsQualityDetailMapper.selectQualityDetailByCondition(condition);
    }

    public List<ClmsQualityDetail> selectQualityDetailsBySerialNo(String serialNo) {
        return clmsQualityDetailMapper.selectQualityDetailsBySerialNo(serialNo);
    }

    public int insertQualityDetail(ClmsQualityDetail clmsQualityDetail) {
        String uuidDetail = UuidUtil.getUUID();
        clmsQualityDetail.setId(uuidDetail);
        return clmsQualityDetailMapper.insertQualityDetail(clmsQualityDetail);
    }

    public int deleteQualityDetailById(String id) {
        return clmsQualityDetailMapper.deleteQualityDetailById(id);
    }

    public int updateQualityDetail(ClmsQualityDetail clmsQualityDetail) {
        return clmsQualityDetailMapper.updateQualityDetail(clmsQualityDetail);
    }

}
