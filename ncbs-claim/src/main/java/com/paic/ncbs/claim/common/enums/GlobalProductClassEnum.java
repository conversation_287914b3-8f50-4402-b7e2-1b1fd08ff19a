package com.paic.ncbs.claim.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum GlobalProductClassEnum {

    GZ("04", "GZ","02"),
    TY("03", "TY","04"),
    ;

    private final String claimProductClass;
    private final String productClass;
    private final String globalProductClass;

    GlobalProductClassEnum(String claimProductClass, String productClass,String globalProductClass) {
        this.claimProductClass = claimProductClass;
        this.productClass = productClass;
        this.globalProductClass = globalProductClass;
    }

    public static GlobalProductClassEnum getProductClassEnum(String productClass) {
        if (StringUtils.isBlank(productClass)) {
            return null;
        }

        for (GlobalProductClassEnum globalProductClassEnum : GlobalProductClassEnum.values()) {
            if (globalProductClassEnum.getProductClass().equals(productClass)) {
                return globalProductClassEnum;
            }
        }
        return null;
    }

    public static GlobalProductClassEnum getProductClassEnumTOClaim (String ClaimProductClass) {
        if (StringUtils.isBlank(ClaimProductClass)) {
            return null;
        }

        for (GlobalProductClassEnum globalProductClassEnum : GlobalProductClassEnum.values()) {
            if (globalProductClassEnum.getClaimProductClass().equals(ClaimProductClass)) {
                return globalProductClassEnum;
            }
        }
        return null;
    }
}
