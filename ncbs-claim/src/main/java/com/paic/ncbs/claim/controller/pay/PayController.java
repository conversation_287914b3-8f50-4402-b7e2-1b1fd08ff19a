package com.paic.ncbs.claim.controller.pay;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.ClmsPayModifyRecordDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.pay.BatchPaymentDTO;
import com.paic.ncbs.claim.model.dto.pay.MergePaymentVO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentBackReasonVO;
import com.paic.ncbs.claim.model.vo.pay.PaymentBackResultVO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.model.vo.pay.PaymentItemVO;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.pay.ClmsPayModifyRecordService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "支付中心")
@RestController
@Slf4j
@RequestMapping("/pay/payAction")
public class PayController extends BaseController {

    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    private CommonParameterService commonService;
    @Autowired
    private ClmsPayModifyRecordService clmsPayModifyRecordService;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

    @Autowired
    private PaymentItemMapper paymentItemMapper;

    @PostMapping("/addPaymentInfo")
    public ResponseResult<Object> addPaymentInfo(@RequestBody PaymentInfoVO paymentInfoVO){
        LogUtil.audit("支付信息新增领款人addPaymentInfo报案号{}，请求入参{}",paymentInfoVO.getReportNo(),JSON.toJSONString(paymentInfoVO));
        paymentInfoService.addPaymentInfo(exchangePaymentInfo(paymentInfoVO));
        return ResponseResult.success();
    }

    @PostMapping("/getPaymentInfo")
    public ResponseResult<Object> getPaymentInfo(@RequestBody PaymentInfoVO paymentInfoVO){
        List<PaymentInfoDTO> paymentList = paymentInfoService.getPaymentInfo(exchangePaymentInfo(paymentInfoVO));
        if(ListUtils.isEmptyList(paymentList)){
            return ResponseResult.successEmptyList();
        }
        List<PaymentInfoVO> paymentInfoVOList = new ArrayList<>();
        PaymentInfoVO vo = new PaymentInfoVO();
        for(PaymentInfoDTO dto : paymentList){
            BeanUtils.copyProperties(dto,vo);
            paymentInfoVOList.add(vo);
        }
        return ResponseResult.success(paymentInfoVOList);
    }

    @GetMapping("/getPaymentInfoById")
    public ResponseResult<PaymentInfoVO> getPaymentInfoById(@RequestParam String idClmPaymentInfo){
        PaymentInfoDTO paymentInfoDTO= paymentInfoService.getPaymentInfoById(idClmPaymentInfo);
        PaymentInfoVO vo = new PaymentInfoVO();
        BeanUtils.copyProperties(paymentInfoDTO,vo);
        AdressSearchDto adressSearchDto  = new AdressSearchDto();
        adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(vo.getProvinceName())
                        .setAccidentCountyCode(vo.getRegionCode()).setAccidentCityCode(vo.getCityCode());
         AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
        vo.setProName(detailAdressFormCode.getAccidentProvinceName());
        vo.setCiName(detailAdressFormCode.getAccidentCityName());
        vo.setCountryName(detailAdressFormCode.getAccidentCountyName());
        return ResponseResult.success(vo);
    }

    @PostMapping("/updatePaymentInfo")
    public ResponseResult<Object> updatePaymentInfo(@RequestBody PaymentInfoVO paymentInfoVO){
        paymentInfoService.updatePaymentInfo(exchangePaymentInfo(paymentInfoVO));
        return ResponseResult.success();
    }

    @PostMapping("/delPaymentInfo")
    public ResponseResult<Object> delPaymentInfo(@RequestBody PaymentInfoVO paymentInfoVO){
        if(StringUtils.isEmptyStr(paymentInfoVO.getIdClmPaymentInfo())
                ||StringUtils.isEmptyStr(paymentInfoVO.getReportNo())){
            throw new GlobalBusinessException("参数不能为空");
        }
        WholeCaseBaseDTO wholecase = wholeCaseBaseService.getWholeCaseBase2(paymentInfoVO.getReportNo(),paymentInfoVO.getCaseTimes());
        if("0".equals(wholecase.getWholeCaseStatus())){
            throw new GlobalBusinessException("已结案不能删除");
        }

        PaymentItemDTO itemDTO = new PaymentItemDTO();
        itemDTO.setIdClmPaymentInfo(paymentInfoVO.getIdClmPaymentInfo());
        itemDTO.setClaimType(SettleConst.CLAIM_TYPE_PRE_PAY);
        int prePayCount = paymentItemService.getPaymentItemCount(itemDTO);
        if(prePayCount > 0){
            throw new GlobalBusinessException("已预赔不能删除");
        }
        itemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        itemDTO.setPaymentType(PaymentTypeEnum.FEE.getType());
        int feePayCount = paymentItemService.getPaymentItemCount(itemDTO);
        if(feePayCount > 0){
            throw new GlobalBusinessException("请先删除费用");
        }

        paymentInfoService.delPaymentInfo(paymentInfoVO);
        paymentItemService.delPaymentItemByInfo(paymentInfoVO.getIdClmPaymentInfo());
        return ResponseResult.success();
    }

    @PostMapping("/addPaymentItem")
    public ResponseResult<Object> addPaymentItem(@RequestBody PaymentItemVO paymentItemVO){
        paymentItemService.addPaymentItem(exchangePaymentItem(paymentItemVO));
        return ResponseResult.success();
    }

    @PostMapping("/getPaymentItem")
    public ResponseResult<Object> getPaymentItem(@RequestBody PaymentItemVO paymentItemVO){
        PaymentItemDTO paymentItem = exchangePaymentItem(paymentItemVO);
        List<PaymentItemDTO> paymentItemList = paymentItemService.getPaymentItem(paymentItem);
        if(ListUtils.isEmptyList(paymentItemList)){
            return ResponseResult.successEmptyList();
        }
        Map<String, Integer> typePriority = new HashMap<>();
//            typePriority.put(PaymentTypeEnum.PRE_COIN_FEE.getType(), 0);// 共保代付预赔费用
//            typePriority.put(PaymentTypeEnum.PRE_COIN_PAY.getType(), 1);// 共保代付预赔赔款
//            typePriority.put(PaymentTypeEnum.COIN_FEE.getType(), 2);// 共保代付费用
//            typePriority.put(PaymentTypeEnum.COIN_PAY.getType(), 3);// 共保代付赔款
            typePriority.put(PaymentTypeEnum.REPLEVY_FEE.getType(), 4);// 追偿费用
            typePriority.put(PaymentTypeEnum.REPLEVY_PAY.getType(), 5);// 追偿赔款
            typePriority.put(PaymentTypeEnum.PRE_FEE.getType(), 6);// 预赔费用
            typePriority.put(PaymentTypeEnum.PRE_PAY.getType(), 7);// 预赔赔款
            typePriority.put(PaymentTypeEnum.FEE.getType(), 8);// 费用
            typePriority.put(PaymentTypeEnum.PAY.getType(), 9);// 赔款
        // 排序逻辑：先按赔付次数正序，同赔付次数内13-赔款类型放最后
        List<PaymentItemDTO> filteredAndSortedList = paymentItemList.stream()
                .filter(item -> typePriority.containsKey(item.getPaymentType())) // 保留map中存在的类型
                .sorted(new Comparator<PaymentItemDTO>() {
                    @Override
                    public int compare(PaymentItemDTO d1, PaymentItemDTO d2) {
                        // 先比较赔付次数
                        if (d1.getCaseTimes() != d2.getCaseTimes()) {
                            return Integer.compare(d1.getCaseTimes(), d2.getCaseTimes());
                        }
                        // 次数相同则按类型优先级排序
                        return Integer.compare(
                                typePriority.get(d1.getPaymentType()),
                                typePriority.get(d2.getPaymentType())
                        );
                    }
                })
                .collect(Collectors.toList());
        return ResponseResult.success(filteredAndSortedList);
//        List<PaymentItemVO> paymentInfoVOList = paymentItemList.stream()
//                .map(PaymentItemDTO::convertToVO)
//                .collect(Collectors.toList());
//        return ResponseResult.success(paymentInfoVOList.stream().filter(i -> !SettleConst.PAYMENT_TYPE_REINSURE_RECEIVE_PAY.equals(i.getPaymentType()))
//                .sorted(Comparator.comparing(PaymentItemVO::getPolicyNo, Comparator.nullsFirst(Comparator.naturalOrder()))
//                        .thenComparing(PaymentItemVO::getPaymentType))
//                .collect(Collectors.toList()));

    }


    @PostMapping("/paydInfo")
    public ResponseResult<Object> paydInfo(@RequestBody PaymentItemVO paymentItemVO){
        List<PaymentItemDTO> paymentItemList = paymentItemService.getHisPaymentItem(exchangePaymentItem(paymentItemVO));
        if(ListUtils.isEmptyList(paymentItemList)){
            return ResponseResult.successEmptyList();
        }
        paymentItemList =paymentItemList.stream().filter(x-> (
          null !=x.getPaymentAmount() && x.getPaymentAmount().compareTo(new BigDecimal("0")) > 0 )).collect(Collectors.toList());
        for(PaymentItemDTO paymentItemDTO : paymentItemList){
            String reportNo= paymentItemDTO.getReportNo()!=null?paymentItemDTO.getReportNo():"";
            String caseTimes= paymentItemDTO.getCaseTimes()!=null?paymentItemDTO.getCaseTimes().toString():"";
            paymentItemDTO.setReportCaseTimesNo(reportNo+"_"+caseTimes);
        }
        return ResponseResult.success(paymentItemList);
    }


    @PostMapping("/updatePaymentItem")
    public ResponseResult<Object> updatePaymentItem(@RequestBody PaymentItemVO paymentItemVO){
        paymentItemService.updatePaymentItem(exchangePaymentItem(paymentItemVO));
        return ResponseResult.success();
    }

    @PostMapping("/delPaymentItem")
    public ResponseResult<Object> delPaymentItem(@RequestBody PaymentItemVO paymentItemVO){
        paymentItemService.delPaymentItem(exchangePaymentItem(paymentItemVO));
        return ResponseResult.success();
    }

    @PostMapping("/addPaymentItemList")
    public ResponseResult<Object> addPaymentItemList(@RequestBody List<PaymentItemVO> paymentItemVOList){
        if(ListUtils.isEmptyList(paymentItemVOList)){
            throw new GlobalBusinessException("参数不能为空");
        }
        List<PaymentItemDTO> paymentItemList = new ArrayList<>();
        for(PaymentItemVO vo : paymentItemVOList){
            paymentItemList.add(exchangePaymentItem(vo));
        }
        paymentItemService.addPaymentItemList(paymentItemList);
        return ResponseResult.success();
    }

    @ApiOperation(value = "查询支付修改页面的信息")
    @GetMapping(value = "/getPaymentBackNotice")
    public ResponseResult<PaymentBackResultVO> getPaymentBackNotice(String idClmPaymentItem) {
        // 修改和审批共有一个查询接口
        boolean isReview = false;
        TaskInfoDTO taskInfoDTO = taskInfoMapper.selectTaskByTaskId(idClmPaymentItem);
        if (idClmPaymentItem.contains("_REVIEW")){
            idClmPaymentItem = idClmPaymentItem.substring(0, idClmPaymentItem.length() - 7);
            isReview = true;
        }
        PaymentItemDTO itemDTO = getPaymentItemDTO(idClmPaymentItem);
        // 获取支付修改 页面数据
        PaymentBackResultVO vo = new PaymentBackResultVO();
        PaymentBackReasonVO paymentBackReasonVO = new PaymentBackReasonVO();
        paymentBackReasonVO.setReportNo(itemDTO.getReportNo());
        paymentBackReasonVO.setCaseNo(itemDTO.getCaseNo());
        // 退回时间
        paymentBackReasonVO.setBackDate(itemDTO.getPayBackDate());
        paymentBackReasonVO.setPaymentAmount(BaseConstant.STRING_1.equals(itemDTO.getIsFullPay()) ? itemDTO.getCoinsuranceActualAmount() : itemDTO.getPaymentAmount()); // 共保取coinsuranceActualAmount
        ClmsPayModifyRecordDTO clmsPayModifyRecordDTO = new ClmsPayModifyRecordDTO();
        if (Constants.PAYMENT_ITEM_STATUS_70.equals(itemDTO.getPaymentItemStatus())){
            paymentBackReasonVO.setBackType("失败");
            clmsPayModifyRecordDTO.setApplyReason("失败");
        }
        if (Constants.PAYMENT_ITEM_STATUS_71.equals(itemDTO.getPaymentItemStatus())){
            paymentBackReasonVO.setBackType("退票");
            clmsPayModifyRecordDTO.setApplyReason("退票");
        }
        paymentBackReasonVO.setReason(itemDTO.getExtendInfo());
        paymentBackReasonVO.setClmPaymentTypeName(itemDTO.getPaymentTypeName());// 理赔支出类型

        List<ClmsPayModifyRecordDTO> clmsPayModifyRecordDTOs = clmsPayModifyRecordService.getClmsPayModifyRecordList(idClmPaymentItem);
        PaymentInfoVO paymentInfoVO =new PaymentInfoVO();
        if (isReview){
            String modifyInfo = itemDTO.getModifyInfo();
            paymentInfoVO = JSON.parseObject(modifyInfo, PaymentInfoVO.class);
            if (StringUtils.isEmptyStr(paymentInfoVO.getProvinceName())) {
                paymentInfoVO.setProvinceName(paymentInfoVO.getProvinceCode());
            }
        }else {
            BeanUtils.copyProperties(itemDTO,paymentInfoVO);
            paymentInfoVO.setCityCode(itemDTO.getCityName());
        }
        AdressSearchDto adressSearchDto  = new AdressSearchDto();
        adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(paymentInfoVO.getProvinceName())
                .setAccidentCountyCode(paymentInfoVO.getRegionCode()).setAccidentCityCode(paymentInfoVO.getCityCode());
        AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
        paymentInfoVO.setProName(detailAdressFormCode.getAccidentProvinceName());
        paymentInfoVO.setCiName(detailAdressFormCode.getAccidentCityName());
        paymentInfoVO.setCountryName(detailAdressFormCode.getAccidentCountyName());
        vo.setPaymentBackReasonVO(paymentBackReasonVO);
        vo.setPaymentInfoVO(paymentInfoVO);
        vo.setClmsPayModifyRecordDTOS(clmsPayModifyRecordDTOs);

        clmsPayModifyRecordDTO.setIdClmsPayModifyRecord("");
        clmsPayModifyRecordDTO.setIdClmPaymentItem("");
        String updatedBy = itemDTO.getUpdatedBy();
        if (StringUtils.isNotEmpty(updatedBy) && !BaseConstant.SYSTEM.equals(updatedBy)){
            String userNameById = userInfoService.getUserNameById(updatedBy);
            clmsPayModifyRecordDTO.setApplyUm(userNameById);
        }else {
            clmsPayModifyRecordDTO.setApplyUm("SYSTEM");
        }

        clmsPayModifyRecordDTO.setApplyDate(taskInfoDTO==null?itemDTO.getPayBackDate():taskInfoDTO.getUpdatedDate());
        if (CollectionUtils.isEmpty(clmsPayModifyRecordDTOs)){
            clmsPayModifyRecordDTO.setApplyTimes(1);
        } else {
            clmsPayModifyRecordDTO.setApplyTimes(clmsPayModifyRecordDTOs.size() + 1);
        }
        clmsPayModifyRecordDTO.setAuditUm("");
        clmsPayModifyRecordDTO.setAuditOpinion("");
        clmsPayModifyRecordDTO.setAuditRemark("");
        vo.setClmsPayModifyRecordDTO(clmsPayModifyRecordDTO);
        return ResponseResult.success(vo);
    }

    @ApiOperation(value = "查询支付修改页面的信息--批量支付")
    @GetMapping(value = "/getBatchPaymentBackNotice")
    public ResponseResult<PaymentBackResultVO> getBatchPaymentBackNotice(@RequestParam(value = "batchNo") String batchNo) {
        // 批量支付查询接口
        TaskInfoDTO taskInfoDTO = taskInfoMapper.selectTaskByTaskId(batchNo);
        if (batchNo.contains("BP") && batchNo.contains("_REVIEW")){
            batchNo = batchNo.substring(0, batchNo.length() - 7);
        }
        MergePaymentVO mergePaymentVO = paymentItemMapper.queryMergePaymentByBatchNo(batchNo);
        // 获取支付修改 页面数据
        PaymentBackResultVO vo = new PaymentBackResultVO();
        PaymentBackReasonVO paymentBackReasonVO = new PaymentBackReasonVO();
        paymentBackReasonVO.setReportNo(mergePaymentVO.getBatchNo());
        // 退回时间
        paymentBackReasonVO.setBackDate(mergePaymentVO.getSysUtime());
        paymentBackReasonVO.setPaymentAmount(mergePaymentVO.getSumAmount());
        ClmsPayModifyRecordDTO clmsPayModifyRecordDTO = new ClmsPayModifyRecordDTO();
        if (Constants.PAYMENT_ITEM_STATUS_70.equals(mergePaymentVO.getMergePaymentStatus())){
            paymentBackReasonVO.setBackType("失败");
            clmsPayModifyRecordDTO.setApplyReason("失败");
        }
        paymentBackReasonVO.setReason(mergePaymentVO.getErrorMsg());
        paymentBackReasonVO.setClmPaymentTypeName(mergePaymentVO.getPaymentUsage());// 理赔支出类型

        List<ClmsPayModifyRecordDTO> clmsPayModifyRecordDTOs =
                clmsPayModifyRecordService.getClmsPayModifyRecordList(mergePaymentVO.getBatchNo());

        PaymentInfoVO paymentInfoVO = new PaymentInfoVO();
        String modifyInfo = mergePaymentVO.getModifyInfo();
        paymentInfoVO = JSON.parseObject(modifyInfo, PaymentInfoVO.class);
        AdressSearchDto adressSearchDto  = new AdressSearchDto();
        PaymentItemDTO paymentItemDTO = paymentItemMapper.getAddressInfo(batchNo);
//        adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(paymentItemDTO.getProvinceName())
//                .setAccidentCountyCode(paymentItemDTO.getRegionCode()).setAccidentCityCode(paymentItemDTO.getCityName());
//        AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
//        paymentInfoVO.setProName(detailAdressFormCode.getAccidentProvinceName());
//        paymentInfoVO.setCiName(detailAdressFormCode.getAccidentCityName());
//        paymentInfoVO.setCountryName(detailAdressFormCode.getAccidentCountyName());
        vo.setPaymentBackReasonVO(paymentBackReasonVO);
        vo.setPaymentInfoVO(paymentInfoVO);
        vo.setClmsPayModifyRecordDTOS(clmsPayModifyRecordDTOs);

        clmsPayModifyRecordDTO.setIdClmsPayModifyRecord("");
        clmsPayModifyRecordDTO.setIdClmPaymentItem("");
        String updatedBy = mergePaymentVO.getUpdatedBy();
        if (StringUtils.isNotEmpty(updatedBy) && !BaseConstant.SYSTEM.equals(updatedBy)){
            String userNameById = userInfoService.getUserNameById(updatedBy);
            clmsPayModifyRecordDTO.setApplyUm(userNameById);
        }else {
            clmsPayModifyRecordDTO.setApplyUm("SYSTEM");
        }

        clmsPayModifyRecordDTO.setApplyDate(taskInfoDTO==null?mergePaymentVO.getSysUtime():taskInfoDTO.getUpdatedDate());
        if (CollectionUtils.isEmpty(clmsPayModifyRecordDTOs)){
            clmsPayModifyRecordDTO.setApplyTimes(1);
        } else {
            clmsPayModifyRecordDTO.setApplyTimes(clmsPayModifyRecordDTOs.size() + 1);
        }
        clmsPayModifyRecordDTO.setAuditUm("");
        clmsPayModifyRecordDTO.setAuditOpinion("");
        clmsPayModifyRecordDTO.setAuditRemark("");
        vo.setClmsPayModifyRecordDTO(clmsPayModifyRecordDTO);
        return ResponseResult.success(vo);
    }

    private PaymentItemDTO getPaymentItemDTO(String idClmPaymentItem) {
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setIdClmPaymentItem(idClmPaymentItem);
        List<PaymentItemDTO> paymentItemList = paymentItemService.getAllPaymentItem(paymentItemDTO);
        if(CollectionUtils.isEmpty(paymentItemList)){
            paymentItemList = paymentItemService.getAllPaymentItemFee(paymentItemDTO);
        }
        if (CollectionUtils.isEmpty(paymentItemList) || paymentItemList.size() > 1){
            throw new GlobalBusinessException("参数异常");
        }
        return paymentItemList.get(CommonConstant.ZERO);
    }

    private MergePaymentVO getMergePaymentInfo(String batchNo) {
        MergePaymentVO mergePaymentVO = paymentItemMapper.queryMergePaymentByBatchNo(batchNo);
        if (mergePaymentVO == null){
            throw new GlobalBusinessException("参数异常");
        }
        return mergePaymentVO;
    }

    @ApiOperation(value = "支付修改 提交接口")
    @PostMapping(value = "/submitPayBack/{idClmPaymentItem}")
    public ResponseResult<Object> modifyPayBackItem(@PathVariable String idClmPaymentItem , @RequestBody PaymentInfoVO paymentInfoVO) {
        // 支付修改 提交接口
        PaymentItemDTO itemDTO = getPaymentItemDTO(idClmPaymentItem);
        paymentItemService.modifyPayBackItem(paymentInfoVO,itemDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "支付修改审批 提交接口")
    @PostMapping(value = "/submitPayBackModifyReview/{idClmPaymentItem}")
    public ResponseResult<Object> submitPayBackModifyReview(@PathVariable String idClmPaymentItem , @RequestBody ClmsPayModifyRecordDTO clmsPayModifyRecordDTO) {
        // 支付修改 提交接口
        if (idClmPaymentItem.contains("_REVIEW")){
            idClmPaymentItem = idClmPaymentItem.substring(0, idClmPaymentItem.length() - 7);
        }
        PaymentItemDTO itemDTO = getPaymentItemDTO(idClmPaymentItem);
        paymentItemService.submitPayBackModifyReview(idClmPaymentItem,clmsPayModifyRecordDTO,itemDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "支付修改审批 提交接口--批量支付")
    @PostMapping(value = "/submitBatchPayBackModifyReview/{batchNo}")
    public ResponseResult<Object> submitBatchPayBackModifyReview(@PathVariable String batchNo , @RequestBody ClmsPayModifyRecordDTO clmsPayModifyRecordDTO) {
        // 支付修改 提交接口--批量支付
        if (batchNo.contains("BP") && batchNo.contains("_REVIEW")){
            batchNo = batchNo.substring(0, batchNo.length() - 7);
        }
        MergePaymentVO mergePaymentVO = getMergePaymentInfo(batchNo);
        paymentItemService.submitBatchPayBackModifyReview(batchNo,clmsPayModifyRecordDTO,mergePaymentVO);
        return ResponseResult.success();
    }

    private PaymentInfoDTO exchangePaymentInfo(@RequestBody PaymentInfoVO paymentInfoVO){
        //校验入参必填字段
        checkInputData(paymentInfoVO);
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        BeanUtils.copyProperties(paymentInfoVO,paymentInfoDTO);
        return paymentInfoDTO;
    }

    /**
     * 校验入必填字段
     * @param paymentInfoVO
     */
    private void checkInputData(PaymentInfoVO paymentInfoVO) {
        if(StringUtils.isEmptyStr(paymentInfoVO.getBankAccountAttribute())){
            throw new GlobalBusinessException("账户类型不能为空");
        }

    }

    private PaymentItemDTO exchangePaymentItem(@RequestBody PaymentItemVO paymentItemVO){
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        BeanUtils.copyProperties(paymentItemVO,paymentItemDTO);
        return paymentItemDTO;
    }
    /***
     * 根据姓名，证件类型，证件号，查询历史支付信息 账号信息
     */
    @PostMapping("/getPaymentInfoByNameAndTypeAndCardNo")
    public ResponseResult<PaymentInfoDTO>  getPaymentInfoByNameAndTypeAndCardNo(@RequestBody PaymentInfoVO paymentInfoVO){
        log.info("getPaymentInfoByNameAndTypeAndCardNo入参{}=", JsonUtils.toJsonString(paymentInfoVO));
        PaymentInfoDTO paymentInfoDTO = paymentInfoService.getPaymentInfoByNameAndTypeAndCardNo(paymentInfoVO);
        return ResponseResult.success(paymentInfoDTO);
    }

    /**
     * 获取支付凭证
     * @param paymentItemVO
     * @return 支付凭证COS地址
     */
    @PostMapping("/queryPaymenVoucherUrl")
    public ResponseResult<String>  queryPaymenVoucherUrl(@RequestBody PaymentItemVO paymentItemVO){
        log.info("queryPaymenVoucherUrl入参{}=", JsonUtils.toJsonString(paymentItemVO));
        PaymentItemDTO paymentItem = exchangePaymentItem(paymentItemVO);
        String cosUrl = payInfoNoticeThirdPartyCoreSAO.queryPaymenVoucherUrl(paymentItem);
        return ResponseResult.success(cosUrl);
    }
}