package com.paic.ncbs.claim.service.secondunderwriting.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.LetterTypeEnum;
import com.paic.ncbs.claim.common.enums.LettersCancelStatusEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUwLetterDTO;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUwLetterEntity;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSecondUnderwritingMapper;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSecondUwLetterMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.feign.mesh.UWRequest;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.UwConclusionDTO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.UwSendPosConclusionDTO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.UwSendPosInsurantInfoDTO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.UwSendPosPolicyDTO;
import com.paic.ncbs.claim.model.vo.senconduw.LetterSellBackSubmitDTO;
import com.paic.ncbs.claim.model.vo.senconduw.UwsDocumentDTO;
import com.paic.ncbs.claim.model.vo.senconduw.UwsLetterInfoDTO;
import com.paic.ncbs.claim.model.vo.senconduw.UwsLetterSellBackDTO;
import com.paic.ncbs.claim.service.common.ClaimCommonQueryFileInfoService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUwLetterService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 函件信息业务实现类
 */
@Slf4j
@Service
@RefreshScope
public class ClmsSecondUwLetterServiceImpl implements ClmsSecondUwLetterService {

    @Autowired
    private ClmsSecondUwLetterMapper clmsSecondUwLetterMapper;

    @Resource
    private ClmsSecondUnderwritingMapper clmsSecondUnderwritingMapper;

    @Autowired
    private ClaimCommonQueryFileInfoService claimCommonQueryFileInfoService;

    @Autowired
    private UWRequest uwRequest;

    @Autowired
    private OcasRequest ocasRequest;

    @Override
    public void saveBatch(List<ClmsSecondUwLetterDTO> dtos) {
        List<ClmsSecondUwLetterEntity> entitys = BeanUtil.copyToList(dtos,ClmsSecondUwLetterEntity.class);
        clmsSecondUwLetterMapper.insertBatch(entitys);
    }

    /**
     * 根据理赔核保任务记录id查询函件信息
     * @param idClmsSecondUnderwriting
     * @return
     */
    @Override
    public List<ClmsSecondUwLetterDTO> getLists(String idClmsSecondUnderwriting) {

        List<ClmsSecondUwLetterEntity> entityList = clmsSecondUwLetterMapper.getLists(idClmsSecondUnderwriting);
        if(CollectionUtil.isEmpty(entityList)){
            return new ArrayList<>();
        }
        List<ClmsSecondUwLetterDTO> dtoList = new ArrayList<>();
        for (ClmsSecondUwLetterEntity en : entityList) {
            ClmsSecondUwLetterDTO letterDTO = new ClmsSecondUwLetterDTO();
            BeanUtils.copyProperties(en,letterDTO);
            dtoList.add(letterDTO);
        }
        return dtoList;
    }

    @Transactional
    @Override
    public ResponseResult letterSellBack(LetterSellBackSubmitDTO letterSellBackSubmitDTO) {
        Integer count = clmsSecondUnderwritingMapper.getUnderwritingPassRecord(letterSellBackSubmitDTO.getReportNo(),letterSellBackSubmitDTO.getCaseTimes());
        if (count == null || count.intValue() == 0){
            log.info("函件回销前需进行核保或无函件, 报案号：{}, 赔付次数：{}",letterSellBackSubmitDTO.getReportNo(),letterSellBackSubmitDTO.getCaseTimes());
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"请确保有无回销的函件或核保通过后进行回销!");
        }
        UserInfoDTO userDTO = WebServletContext.getUser();
        List<ClmsSecondUwLetterEntity> updateEntityList = new ArrayList<>();
        List<UwsLetterInfoDTO> uwsletterInfoList = new ArrayList<>();
        for (LetterSellBackSubmitDTO.LetterInfoDTO dto : letterSellBackSubmitDTO.getLetterInfoList()) {
            //组装发送核保的函件信息
            uwsletterInfoList.add(asseembleUwsLetterInfoDTO(userDTO, dto,letterSellBackSubmitDTO.getReportNo()));
            //组装更新理赔二核函件信息表数据
            updateEntityList.add(assembleSecondUWLetter(userDTO, dto));
        }
        UwsLetterSellBackDTO uwsLetterSellBackDTO = new UwsLetterSellBackDTO();
        uwsLetterSellBackDTO.setManualInfoId(letterSellBackSubmitDTO.getManualInfoId());
        uwsLetterSellBackDTO.setBusinessKey(letterSellBackSubmitDTO.getReportNo()+"_"+letterSellBackSubmitDTO.getCaseTimes());
        uwsLetterSellBackDTO.setCaseTimes(letterSellBackSubmitDTO.getCaseTimes());
        uwsLetterSellBackDTO.setLetterInfoList(uwsletterInfoList);
        //批量更新二核函件信息表
        clmsSecondUwLetterMapper.updateBatch(updateEntityList);
        //更新
        clmsSecondUnderwritingMapper.update(ClmsSecondUnderwritingEntity.builder()
                        .id(letterSellBackSubmitDTO.getIdSecondUnderwriting())
                        .lettersCancelStatus(LettersCancelStatusEnum.STATUS_ENUM_03.getCode())
                        .build());

        // 核保结论返给批改
        LetterSellBackSubmitDTO.LetterInfoDTO letterInfoDTO = letterSellBackSubmitDTO.getLetterInfoList().stream()
                .filter(i -> LetterTypeEnum.LETTER_TYPE_TWO.getCode().equals(i.getFileType()) && BaseConstant.STRING_01.equals(i.getLetterConclusion()))
                .findFirst().orElse(null);
        if (Objects.nonNull(letterInfoDTO)) {
            List<UwConclusionDTO> list = clmsSecondUwLetterMapper.getUwConclusionList(letterInfoDTO.getIdSecondUWLetter());
            if (CollectionUtil.isNotEmpty(list)) {
                UwSendPosConclusionDTO sendPosConclusion = new UwSendPosConclusionDTO();
                sendPosConclusion.setUnderwriterCode(WebServletContext.getUserId());
                sendPosConclusion.setUnderwriterName(WebServletContext.getUserName());
                sendPosConclusion.setUnderwriteDate(new Date());
                List<UwSendPosPolicyDTO> sendPosPolicyList = Lists.newArrayList();
                for (UwConclusionDTO uwConclusion : list) {
                    UwSendPosPolicyDTO sendPosPolicy = new UwSendPosPolicyDTO();
                    sendPosPolicy.setPolicyNo(uwConclusion.getPolicyNo());
                    sendPosPolicy.setUnderwritingConclusion(uwConclusion.getUwConclusion());

                    // 理赔核保目前都是个单，只有一个被保人
                    List<UwSendPosInsurantInfoDTO> insurantInfoList = Lists.newArrayList();
                    UwSendPosInsurantInfoDTO sendPosInsurantInfo = new UwSendPosInsurantInfoDTO();
                    sendPosInsurantInfo.setClientNo(uwConclusion.getClientNo());
                    sendPosInsurantInfo.setUnderwritingConclusion(uwConclusion.getUwConclusion());
                    sendPosInsurantInfo.setUnderwritingOpinion(uwConclusion.getUwExceptions());
                    insurantInfoList.add(sendPosInsurantInfo);

                    sendPosPolicy.setInsurantInfoList(insurantInfoList);
                    sendPosPolicyList.add(sendPosPolicy);
                }
                sendPosConclusion.setPolicyList(sendPosPolicyList);

                log.info("ocasRequest.updateUnderwritingConclusion, param={}", JSON.toJSONString(sendPosConclusion));
                String result = ocasRequest.updateUnderwritingConclusion(sendPosConclusion);
                log.info("ocasRequest.updateUnderwritingConclusion, result={}", result);
                JSONObject jsonObject = JSON.parseObject(result, JSONObject.class);
                if (Objects.isNull(jsonObject) || !GlobalResultStatus.SUCCESS.getCode().equals(jsonObject.get("returnCode"))) {
                    throw new GlobalBusinessException("调用批改同步核保结论失败！");
                }
            }
        }

        String uwsResult = uwRequest.letterSellBack(uwsLetterSellBackDTO);
        Map resultMap = JSON.parseObject(uwsResult, Map.class);
        LogUtil.info("调用核保-letterSellBack-函件回销返回结果-resultMap-" + JSON.toJSONString(resultMap));
        Map data = (Map) resultMap.get("data");
        // 判断函件回销是否成功 如失败则抛异常回滚
        if (MapUtils.isNotEmpty(data) && "1".equals(MapUtils.getString(data, "resultCode"))) {
            LogUtil.info("调用核保函件回销成功");
        } else {
            LogUtil.info("调用核保函件回销失败:" + MapUtils.getString(data, "resultCode"));
            throw new GlobalBusinessException("调用核保函件回销失败！");
        }


        return ResponseResult.success();
    }

    @Override
    public String getUwsLetterCode(String manualInfoId) {
        return clmsSecondUnderwritingMapper.getUwsLetterCode(manualInfoId);
    }

    /**
     * 组装发送核保的函件信息
     * @param userDTO
     * @param dto
     * @return
     */
    private UwsLetterInfoDTO asseembleUwsLetterInfoDTO(UserInfoDTO userDTO, LetterSellBackSubmitDTO.LetterInfoDTO dto,String reportNo) {
        dto.setSellBackName(userDTO.getUserName());
        dto.setSellBackId(userDTO.getUserCode());
        UwsLetterInfoDTO uwsLetterInfoDTO = new UwsLetterInfoDTO();
        BeanUtils.copyProperties(dto, uwsLetterInfoDTO);

        Map<String, String> map = new HashMap<>();
        map.put("letterConclusion", dto.getLetterConclusion());
        map.put("letterExplain", dto.getLetterExplain());
        uwsLetterInfoDTO.setSellbackMsg(JSONObject.toJSONString(map));

        if (CollectionUtils.isEmpty(dto.getMaterialInfoList())){
            return uwsLetterInfoDTO;
        }
        //如果有材料进行组装组装
        List<UwsDocumentDTO> materialList = new ArrayList<>();
        dto.getMaterialInfoList().stream().forEach(v ->{
            FileInfoDTO letterFileDtoParams = new FileInfoDTO();
            letterFileDtoParams.setReportNo(reportNo);
            letterFileDtoParams.setFileId(v.getDocumentId());
            FileInfoDTO resultFileDto =  claimCommonQueryFileInfoService.getFileInfo(letterFileDtoParams);
            UwsDocumentDTO uwsDocumentDTO = new UwsDocumentDTO();
            uwsDocumentDTO.setDocumentId(v.getDocumentId());
            uwsDocumentDTO.setMaterialType(v.getMaterialType());
            uwsDocumentDTO.setDocumentName(resultFileDto.getFileName());
            materialList.add(uwsDocumentDTO);
        });
        uwsLetterInfoDTO.setMaterialList(materialList);
        return uwsLetterInfoDTO;
    }

    /**
     * 组装更新理赔二核函件信息表数据
     * @param userDTO
     * @param dto
     */
    private ClmsSecondUwLetterEntity assembleSecondUWLetter(UserInfoDTO userDTO, LetterSellBackSubmitDTO.LetterInfoDTO dto) {
        ClmsSecondUwLetterEntity letterEntity = new ClmsSecondUwLetterEntity();
        letterEntity.setId(dto.getIdSecondUWLetter());
        letterEntity.setLetterConclusion(dto.getLetterConclusion());
        letterEntity.setLetterExplain(dto.getLetterExplain());
        letterEntity.setUploadFileId(dto.getUploadFileId());
        letterEntity.setLetterCancelOperator(userDTO.getUserCode());
        letterEntity.setLetterCancelDate(new Date());
        letterEntity.setUpdatedBy(userDTO.getUserName());
        letterEntity.setUpdatedDate(new Date());
        return letterEntity;
    }

}
