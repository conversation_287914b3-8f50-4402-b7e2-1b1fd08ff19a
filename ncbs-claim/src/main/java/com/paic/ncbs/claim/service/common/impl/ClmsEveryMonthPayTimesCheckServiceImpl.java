package com.paic.ncbs.claim.service.common.impl;

import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.report.QueryAccidentVo;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.service.common.ClmsEveryMonthPayTimesCheckService;
import com.paic.ncbs.claim.service.common.ClmsGetPolicyMonthInfoService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 每月赔付次数校验
 */
@Slf4j
@Service
public class ClmsEveryMonthPayTimesCheckServiceImpl implements ClmsEveryMonthPayTimesCheckService {

    @Autowired
    private ReportAccidentMapper reportAccidentMapper;
    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;

    @Autowired
    private  ClmsGetPolicyMonthInfoService clmsGetPolicyMonthInfoService;

    @Autowired
    private  DutyAttributeMapper dutyAttributeMapper;
    @Override
    public void checkPayTimes(List<PolicyPayDTO> policyPays,String reportNo) {
        QueryAccidentVo qureyVo=new QueryAccidentVo();
        for (PolicyPayDTO policy : policyPays) {
            //1得到出险日期（事故日期）
            ReportAccidentEntity accidentEntity = reportAccidentMapper.getReportAccident(reportNo);
            checkData(accidentEntity);
            //2得到保单的起止日期
            PolicyDto policyDto =clmsQueryPolicyInfoService.getPolicyDto(policy.getPolicyNo(),reportNo);
            //3根据起止日期计算月数
            List<PolicyMonthDto> monthDtoList = clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),policyDto.getPolicyEndDate());
            //4判断事故日期在哪一个月，得到该月的起始日期
            PolicyMonthDto monthDto = getStartEndDate(accidentEntity.getAccidentDate(),monthDtoList);
            if(Objects.isNull(monthDto) && (policyDto.getProsecutionPeriod() == 0 && policyDto.getExtendReportDate()==0)){
                throw new GlobalBusinessException("事故日期不在保单有效期内");
            }
            if(Objects.isNull(monthDto)) {
                return;
            }
            //5查询事故日期所在月起止时间范围内的本次报案号对应的保单号报案的且事故时间在该范围内且正常赔付且金额大于0的案件
            List<PlanPayDTO> planPayDTOS = policy.getPlanPayArr();
            qureyVo.setPolicyNo(policy.getPolicyNo());
            qureyVo.setStartDate(monthDto.getStartDate());
            qureyVo.setEndDate(monthDto.getEndDate());
            for (PlanPayDTO plan: planPayDTOS) {
                List<DutyPayDTO> dutyPayDTOS =  plan.getDutyPayArr();
                for (DutyPayDTO duty:dutyPayDTOS) {
                    String  idpolicyDuty = duty.getIdCopyDuty();
                    //查询是否有每月赔付次数属性
                   String attributeValue= dutyAttributeMapper.getAttributeByAttrCodeInfo(idpolicyDuty, DutyAttributeConst. EVERY_MONTH_PAY_TIMES_617);
                   if(StringUtils.isEmptyStr(attributeValue)){
                        continue;
                   }
                   //查询责任在该月已已报案赔付的次数
                    qureyVo.setDutyCode(duty.getDutyCode());
                    List<String> countList= reportAccidentMapper.getAccidentReportInfo(qureyVo);
                    Integer confiTimes =Integer.parseInt(attributeValue);
                    Integer payTimes= CollectionUtils.isEmpty(countList) ? 0 : countList.size();
                    if(payTimes>=confiTimes){
                        //阻断提示。 当前案件超赔付次数！
                        log.info("责任"+duty.getDutyCode()+"在"+monthDto.getSdate()+"-"+monthDto.getEDate()+"间已赔付过"+countList.size()+"次，报案号"+ JsonUtils.toJsonString(countList));
                        throw new GlobalBusinessException("当前案件超赔付次数，保单月：" + monthDto.getSdate() + "至" + monthDto.getEDate());

                    }
                }

            }


        }
    }

    public List<String> getMonthEndCase(WholeCaseVO wholeCaseVO) {
        //1得到出险日期（事故日期）
        ReportAccidentEntity accidentEntity = reportAccidentMapper.getReportAccident(wholeCaseVO.getReportNo());
        checkData(accidentEntity);
        //2得到保单的起止日期
        PolicyDto policyDto =clmsQueryPolicyInfoService.getPolicyDto(wholeCaseVO.getPolicyNo(),null);
        //3根据起止日期计算月数
        List<PolicyMonthDto> monthDtoList = clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),policyDto.getPolicyEndDate());
        //4判断事故日期在哪一个月，得到该月的起始日期
        PolicyMonthDto monthDto = getStartEndDate(accidentEntity.getAccidentDate(),monthDtoList);
        if(Objects.isNull(monthDto)){
            throw new GlobalBusinessException("事故日期不在保单有效期内");
        }
        QueryAccidentVo qureyVo=new QueryAccidentVo();
        qureyVo.setPolicyNo(wholeCaseVO.getPolicyNo());
        qureyVo.setStartDate(monthDto.getStartDate());
        qureyVo.setEndDate(monthDto.getEndDate());
        return reportAccidentMapper.getAccidentReportInfo(qureyVo);
    }

    /**
     * 事故信息校验
     * @param accidentEntity
     */
    private void checkData(ReportAccidentEntity accidentEntity) {
        if(Objects.isNull(accidentEntity)){
            throw new GlobalBusinessException("事故信息不能为空");
        }
        if(Objects.isNull(accidentEntity.getAccidentDate())){
            throw new GlobalBusinessException("事故日期不能为空");
        }
    }

    /**
     * Match匹配事故日期在那一个月的时间段
     * @param accidentDate
     * @param monthDtoList
     */
    private PolicyMonthDto getStartEndDate(Date accidentDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if(accidentDate.compareTo(monthDto.getStartDate())>=0 && accidentDate.compareTo(monthDto.getEndDate())<=0){
                return monthDto;
            }
        }
        return null;
    }
}
