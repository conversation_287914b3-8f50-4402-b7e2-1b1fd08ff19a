package com.paic.ncbs.claim.service.noPeopleHurt.impl;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsCashLossInfoMapper;
import com.paic.ncbs.claim.dao.entity.clms.ClmsCashLossInfo;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsCashLossInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 现金损失信息表(ClmsCashLossInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:46
 */
@Service("clmsCashLossInfoService")
public class ClmsCashLossInfoServiceImpl implements ClmsCashLossInfoService {
    @Resource
    private ClmsCashLossInfoMapper clmsCashLossInfoMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsCashLossInfo queryById(String id) {
        return this.clmsCashLossInfoMapper.queryById(id);
    }

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public List<ClmsCashLossInfo> queryByReportNo(String reportNo, int caseTimes) {
        return this.clmsCashLossInfoMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsCashLossInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsCashLossInfo insert(ClmsCashLossInfo clmsCashLossInfo) {
        clmsCashLossInfo.setCreatedBy(WebServletContext.getUserId());
        clmsCashLossInfo.setCreatedDate(new Date());
        clmsCashLossInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsCashLossInfo.setUpdatedDate(new Date());
        clmsCashLossInfo.setId(UuidUtil.getUUID());
        this.clmsCashLossInfoMapper.insert(clmsCashLossInfo);
        return clmsCashLossInfo;
    }

    /**
     * 修改数据
     *
     * @param clmsCashLossInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsCashLossInfo update(ClmsCashLossInfo clmsCashLossInfo) {
        clmsCashLossInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsCashLossInfo.setUpdatedDate(new Date());
        this.clmsCashLossInfoMapper.update(clmsCashLossInfo);
        return this.queryById(clmsCashLossInfo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsCashLossInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByCondition(String reportNo, int caseTimes) {
        clmsCashLossInfoMapper.deleteByCondition(reportNo, caseTimes);
    }
}
