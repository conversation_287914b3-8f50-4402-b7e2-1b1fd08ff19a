package com.paic.ncbs.claim.model.dto.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("城市基本信息")
public class CityDefineDTO extends EntityDTO {

    private static final long serialVersionUID = -6729635825080346393L;
    @ApiModelProperty("城市编码")
    private String cityCode;
    @ApiModelProperty("城市中文名称")
    private String cityChineseName;
    @ApiModelProperty("省份编码")
    private String provinceCode;
    @ApiModelProperty("邮编")
    private String postCode;
    @ApiModelProperty("城市英文名称")
    private String cityEnglishName;
    @ApiModelProperty("区域编码")
    private String areaCode;
    @ApiModelProperty("车牌前缀")
    private String vehicleLicencePrefix;
    @ApiModelProperty("城市简称")
    private String cityShortName;
    @ApiModelProperty("城市拼音")
    private String citySpellName;
    @ApiModelProperty("城市拼音首字母简称")
    private String cityInitialName;
    @ApiModelProperty("地区标识")
    private String isArea;

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityChineseName() {
        return cityChineseName;
    }

    public void setCityChineseName(String cityChineseName) {
        this.cityChineseName = cityChineseName;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getCityEnglishName() {
        return cityEnglishName;
    }

    public void setCityEnglishName(String cityEnglishName) {
        this.cityEnglishName = cityEnglishName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getVehicleLicencePrefix() {
        return vehicleLicencePrefix;
    }

    public void setVehicleLicencePrefix(String vehicleLicencePrefix) {
        this.vehicleLicencePrefix = vehicleLicencePrefix;
    }

    public String getCityShortName() {
        return cityShortName;
    }

    public void setCityShortName(String cityShortName) {
        this.cityShortName = cityShortName;
    }

    public String getCitySpellName() {
        return citySpellName;
    }

    public void setCitySpellName(String citySpellName) {
        this.citySpellName = citySpellName;
    }

    public String getCityInitialName() {
        return cityInitialName;
    }

    public void setCityInitialName(String cityInitialName) {
        this.cityInitialName = cityInitialName;
    }

    public String getIsArea() {
        return isArea;
    }

    public void setIsArea(String isArea) {
        this.isArea = isArea;
    }
}
