package com.paic.ncbs.claim.controller.who.investigate;


import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO;
import com.paic.ncbs.claim.service.investigate.InvestigateProcessService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "调查过程")
@RestController
@RequestMapping(value = "/who/app/investigateProcessAction")
public class InvestigateProcessController extends BaseController {

    @Autowired
    private InvestigateProcessService investigateProcessService;

    @ApiOperation("新增调查经过表信息")
    @PostMapping(value = "/addInvestigateProcess")
    public ResponseResult<Object> addInvestigateProcess(@RequestBody InvestigateProcessDTO investigateProcess) {
        LogUtil.audit("#调查·新增(经过表)信息#入参#investigateProcess=" + investigateProcess);
        UserInfoDTO u = WebServletContext.getUser();
        investigateProcess.setCreatedBy(u.getUserCode());
        investigateProcess.setUpdatedBy(u.getUserCode());
        investigateProcessService.addInvestigateProcess(investigateProcess);
        return ResponseResult.success();
    }


    @ApiOperation("批量新增经过信息")
    @PostMapping(value = "/addInvestigateProcessList")
    public ResponseResult<Object> addInvestigateProcessList(@RequestBody List<InvestigateProcessDTO> investigateProcessList) {
        LogUtil.audit("#调查·批量新增经过信息#入参#investigateProcessList=" + investigateProcessList);
        UserInfoDTO u = WebServletContext.getUser();
        String userId = u.getUserCode();
        investigateProcessService.addInvestigateProcessList(investigateProcessList, userId);
        return ResponseResult.success();
    }


    @ApiOperation("保存或更新调查经过信息")
    @PostMapping(value = "/saveOrUpdateInvestigateProcess")
    public ResponseResult<Object> saveOrUpdateInvestigateProcess(@RequestBody InvestigateProcessDTO investigateProcess) throws GlobalBusinessException {
        LogUtil.audit("#调查·保存或更新调查经过信息#入参#investigateProcess=" + investigateProcess);
        UserInfoDTO u = WebServletContext.getUser();
        investigateProcess.setCreatedBy(u.getUserCode());
        investigateProcess.setUpdatedBy(u.getUserCode());
        investigateProcessService.saveOrUpdateInvestigateProcess(investigateProcess);
        return ResponseResult.success();
    }


    @ApiOperation("删除经过信息")
    @GetMapping(value = "/deleteInvestigateProcess/{idAhcsInvestigateProcess}")
    public ResponseResult<Object> deleteInvestigateProcess(@ApiParam("调查任务id") @PathVariable("idAhcsInvestigateProcess") String idAhcsInvestigateProcess) {
        LogUtil.audit("#调查·删除经过信息#入参#idAhcsInvestigateProcess=" + idAhcsInvestigateProcess);
        investigateProcessService.deleteInvestigateProcessById(idAhcsInvestigateProcess);
        return ResponseResult.success();
    }
}