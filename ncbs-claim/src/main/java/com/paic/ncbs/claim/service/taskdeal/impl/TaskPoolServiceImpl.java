package com.paic.ncbs.claim.service.taskdeal.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity;
import com.paic.ncbs.claim.dao.mapper.communicate.CommunicateBaseMapper;
import com.paic.ncbs.claim.dao.mapper.communicate.CommunicateDetailMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentUserMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.communicate.CommunicateDetailDTO;
import com.paic.ncbs.claim.model.dto.message.SmsInfoDTO;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.communicate.CommunicateBaseVO;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.UserWithTaskCountVO;
import com.paic.ncbs.claim.model.vo.user.DepartmentUserVO;
import com.paic.ncbs.claim.model.vo.user.PermissionUserVO;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.report.ReportService;
import com.paic.ncbs.claim.service.taskdeal.ITaskInfoPlusService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.service.waitingcenter.WaitingCenterService;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import com.paic.ncbs.um.service.UserCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service("taskPoolService")
@Slf4j
public class TaskPoolServiceImpl implements TaskPoolService {

    @Autowired
    DepartmentDefineService departmentDefineService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private UserCommonService userCommonService;

    @Autowired
    TaskInfoMapper taskInfoMapper;
    @Autowired
    private TaskListService taskListService;
    @Autowired
    private ReportService reportService;
    @Autowired
    private SmsInfoService smsInfoService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private CommonParameterMapper commonParameterMapper;
    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private ITaskInfoPlusService taskInfoPlusService;
    @Autowired
    NoticeService noticeService;
    @Autowired
    private PermissionUserMapper permissionUserMapper;

    @Autowired
    private CommunicateBaseMapper communicateBaseMapper;

    @Autowired
    private CommunicateDetailMapper communicateDetailMapper;

    @Autowired
    private WaitingCenterService unSettleApproveWaitingCenterServiceImpl;
    @Autowired
    private DepartmentUserMapper departmentUserMapper;

    @Override
    @SuppressWarnings("unchecked")
    public PageInfo<TaskInfoVO> getNotDealTaskList(TaskInfoVO taskInfoVO) throws GlobalBusinessException {
        String reportNo = taskInfoVO.getReportNo();
        String departmentCode = taskInfoVO.getDepartmentCode();
        String assigneeName = taskInfoVO.getAssigneeName();
        String packageName = taskInfoVO.getPackageName();
//        String productCode = StringUtils.isNotEmpty(taskInfoVO.getProductCode()) ? taskInfoVO.getProductCode().substring(0,taskInfoVO.getProductCode().indexOf("-")) : null ;
        if (this.checkTaskQueryParam(reportNo,departmentCode,assigneeName,packageName)) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL,  "报案号、机构号、处理人姓名不能同时为空");
        }
        TaskInfoDTO taskInfoDTO = taskInfoVO.convertToDTO();
        String dept = WebServletContext.getDepartmentCode();
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            dept = departmentUserVO.getDepartmentCode();
        }
        if (StringUtils.isEmpty(departmentCode) && !ConfigConstValues.HQ_DEPARTMENT.equals(dept)){
            List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(dept);
            taskInfoDTO.setDepartmentCodes(departmentCodes);
        }
//        if(StringUtils.isNotEmpty(productCode)) {
//            taskInfoDTO.setProductCode(productCode);
//        }
        if(StringUtils.isNotEmpty(packageName)) {
            taskInfoDTO.setRiskGroupName(packageName);
        }
        int pageNum = taskInfoVO.getCurrentPage() == 0 ? 1 :taskInfoVO.getCurrentPage();
        int pageSize = taskInfoVO.getPerPageSize() == 0 ? 10 : taskInfoVO.getPerPageSize();
        PageHelper.startPage(pageNum,pageSize);
        List<TaskInfoVO> taskInfoList = taskInfoMapper.getTaskInfo(taskInfoDTO);
        List<CommonParameterTinyDTO> commonParameterTinyDTOList = commonParameterMapper.getCommonParameterList(new String[]{"LIMIT_POLICY_WHITE"});
        UserInfoDTO user = WebServletContext.getUser();
        taskInfoList.forEach(t-> {
            t.setTaskName(Optional.ofNullable(BpmConstants.TASK_MAP.get(t.getTaskDefinitionBpmKey())).orElse("未知"));
            t.setCostTime(this.getCostTime(t.getAssignTime()));
            t.setLimitPolicyDealFlag("N");
            if(!ListUtils.isEmptyList(commonParameterTinyDTOList)){
                for (CommonParameterTinyDTO commonParameterTinyDTO : commonParameterTinyDTOList) {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(commonParameterTinyDTO.getValueCode())) {
                        String [] policyNoStrArray = commonParameterTinyDTO.getValueCode().split("-");
                        if (policyNoStrArray.length > 1) {
                            String userCodeStr = policyNoStrArray[0];
                            String policyNoStr = policyNoStrArray[1];
                            if (policyNoStr.equals(t.getPolicyNo())) {
                                t.setLimitPolicyDealFlag("Y");
                                if (userCodeStr.equals(user.getUserCode())) {
                                    t.setLimitPolicyDealFlag("N");
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        });
        return new PageInfo<>(taskInfoList);
    }

    private boolean checkTaskQueryParam(String reportNo,String processor,String assignee,String riskGroupName){
        return StringUtils.isAllEmpty(reportNo,processor,assignee,riskGroupName);
    }

    private String getCostTime(Date createTime) {
        BigDecimal currentTimeBigDecimal = new BigDecimal(new Date().getTime());
        BigDecimal createTimeBigDecimal;
        if (createTime == null) {
            return null;
        }
        createTimeBigDecimal = new BigDecimal(createTime.getTime());

        BigDecimal costTime = currentTimeBigDecimal.subtract(createTimeBigDecimal);

        return costTime.divide(new BigDecimal(60 * 60 * 1000), 2, RoundingMode.HALF_UP).toString();
    }

    @Override
    public Boolean isExistsVerifyRecord(TaskInfoVO taskInfoVO) throws GlobalBusinessException{
        //根据处理人和案件号查询是否存在核赔记录
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setReportNo(taskInfoVO.getReportNo());
        taskInfoDTO.setAssigner(taskInfoVO.getAssignee());
        taskInfoDTO.setCaseTimes(taskInfoVO.getCaseTimes());
        int count = taskInfoMapper.getTaskRecord(taskInfoDTO);
        if(count == 0){
            return false;
        }
        return true;
    }
    @Override
    public List<UserInfoDTO> searchTaskDealUser(String departmentCode, String key) throws GlobalBusinessException{
        if (BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS.equals(key)) {
            key = BpmConstants.OC_REPORT_TRACK;
        }
        Set<UserInfoDTO> userInfoDTOAll = new HashSet<>();
        //调用第三方系统接口，查询岗位编码列表，根据案件环节对应的岗位名称，去匹配对应的岗位编码
        List<String> gradeCodeList = this.getGradeCodeByTaskId(key);
        //调用第三方系统接口，根据岗位编码和机构编码，查询符合的用户人员编码列表
        if (ListUtils.isNotEmpty(gradeCodeList)) {
            for (String gradeCode : gradeCodeList) {
                try{
                    List<UserInfoDTO> userInfoDTOS =userCommonService.queryUserInfoList(departmentCode,gradeCode);
                    if (ListUtils.isNotEmpty(userInfoDTOS)) {
                        userInfoDTOAll.addAll(userInfoDTOS);
                    }
                }catch (Exception e){
                    LogUtil.error("调用[根据岗位和机构编码查询人员列表]接口异常!",e);
                }
            }
        }
        return  new ArrayList<>(userInfoDTOAll);
    }

    private List<String> getGradeCodeByTaskId(String taskId) {
        List<String> gradeCodeList = new ArrayList<>();
        List<UserGradeInfoDTO> gradeInfoDTOList = new ArrayList<>();
        try{
            gradeInfoDTOList = cacheService.querySystemGradeList();
        }catch (Exception e){
            LogUtil.error("调用[根据系统编码查询岗位列表]接口异常!",e);
        }
        if (ListUtils.isNotEmpty(gradeInfoDTOList)) {
            String gradeStr = NcbsConstant.GRADE_MAP.get(taskId);
            if (StringUtils.isNotBlank(gradeStr)) {
                List<String> gradeList = Arrays.asList(gradeStr.split(","));
                for (UserGradeInfoDTO gradeInfo:gradeInfoDTOList) {
                    for (String grade : gradeList) {
                        if (gradeInfo.getGradeName().equals(grade)){
                            gradeCodeList.add(gradeInfo.getGradeCode());
                        }
                    }
                }
            }
        }
        return gradeCodeList;
    }

    @Override
    public List<UserWithTaskCountVO> getTaskDealUser(String key, String departmentCode, String assignee) throws NcbsException {
        Set<UserInfoDTO> userInfoDTOAll = new HashSet<>();
        if (StringUtils.isEmpty(assignee)){
            if (BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS.equals(key)) {
                key = BpmConstants.OC_REPORT_TRACK;
            }
            List<String> gradeCodeList = this.getGradeCodeByTaskId(key);
            if (ListUtils.isNotEmpty(gradeCodeList)) {
                for (String gradeCode : gradeCodeList) {
                    try{
                        List<UserInfoDTO> userInfoDTOS =userCommonService.queryUserInfoList(departmentCode,gradeCode);
                        if (ListUtils.isNotEmpty(userInfoDTOS)) {
                            userInfoDTOAll.addAll(userInfoDTOS);
                        }
                    }catch (Exception e){
                        LogUtil.error("调用[根据岗位和机构编码查询人员列表]接口异常!",e);
                    }
                }
            }
        } else {
            userInfoDTOAll = Stream.of(cacheService.queryUserInfo(assignee)).collect(Collectors.toSet());
        }
        return this.convertToUserWithTaskCountVO(userInfoDTOAll,departmentCode);
    }

    private List<UserWithTaskCountVO> convertToUserWithTaskCountVO(Set<UserInfoDTO> userInfoDTOS,String departmentCode){
        DepartmentDefineEntity departmentInfo = departmentDefineService.getDepartmentInfo(departmentCode);
        return userInfoDTOS.stream()
                .map(u->{
                    String userCode = u.getUserCode();
                    String userName = u.getUserName();
                    UserWithTaskCountVO vo = new UserWithTaskCountVO();
                    vo.setUserName(userName);
                    vo.setUserId(userCode);
                    vo.setShowName(userCode + "-" + userName);
                    vo.setTaskCount(taskInfoMapper.getNoFinishTaskCount(userCode,departmentCode));
                    vo.setDepartmentName(departmentInfo.getDepartmentAbbrName());
                    vo.setDepartmentCode(departmentCode);
                    return vo;
        }).collect(Collectors.toList());
    }


    /**
     * 任务派工
     */
    @Override
    public void dispatchTask(List<String> taskIdList, String userId,String assigneeName,String departmentCode) throws GlobalBusinessException{
        for(String id : taskIdList){
            TaskInfoDTO taskInfoDTO = taskInfoMapper.selectTaskByTaskId(id);
            if (BpmConstants.APPROVETASK_CONTROL.contains(taskInfoDTO.getTaskDefinitionBpmKey()) && userId.equals(taskInfoDTO.getApplyer())){
                throw new GlobalBusinessException(ErrorCode.Dispatch.DO_NOT_SELF_DISPATCH,"不能把本人提交的审批案件派工给他本人！");
            }
        }
        for(String id : taskIdList){
//            String mobileNo = "";
//            try{
//                UserInfoDTO userInfo = cacheService.queryUserInfo(userId);
////                if (userInfo != null){
////                    mobileNo = userInfo.getMobile();
////                }
//            }catch (Exception e){
//                LogUtil.error("调用[查询人员信息]接口异常!",e);
//            }
            if (StringUtils.isEmpty(assigneeName)){
                assigneeName = getUserName(userId);
            }

            List<TaskInfoDTO> taskInfoDTOS = taskInfoPlusService.lambdaQuery()
                    .eq(TaskInfoDTO::getTaskId, id)
                    .in(TaskInfoDTO::getStatus, Arrays.asList("0", "3")).list();
            if(CollectionUtils.isEmpty(taskInfoDTOS)){
                log.error("未查询到需调度案件， taskId:{}", id);
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "未查询到需调度案件");
            }
            TaskInfoDTO taskInfoDTO = taskInfoDTOS.get(0);
            if(BpmConstants.OC_REGISTER_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey()) || BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
                PermissionUserVO permissionUserVO = new PermissionUserVO();
                permissionUserVO .setUserId(userId);
                permissionUserVO.setTypeCode(Constants.PERMISSION_REGIST);
                List<PermissionUserDTO> permissionUserList = permissionUserMapper.getPermissionUserList(permissionUserVO);
                if (!CollectionUtils.isEmpty(permissionUserList)) {
                    PermissionUserDTO permissionUserDTO = permissionUserList.get(0);
                    if (permissionUserDTO.getGrade() < taskInfoDTO.getTaskGrade()) {
//                        throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "该人员立案审批权限低于案件权限，请重新分配");
                    }
                } else {
                    throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "该人员未配置案件机构的立案审批权限，请联系管理员配置权限！");
                }
            }

            taskInfoMapper.reAssign(id,userId,assigneeName,departmentCode);
            //沟通时，还需要修改沟通业务表的处理人
            if(BpmConstants.OC_COMMUNICATE.equals(taskInfoDTO.getTaskDefinitionBpmKey())) {
                CommunicateBaseVO communicateBaseVO = communicateBaseMapper.getCommunicateBaseInfo(taskInfoDTO.getReportNo());
                //更新沟通业务表的处理人
                CommunicateDetailDTO communicateDetailDTO = new CommunicateDetailDTO();
                communicateDetailDTO.setIdAhcsCommunicateBase(communicateBaseVO.getIdAhcsCommunicateBase());
                communicateDetailDTO.setDealUm(userId);
                communicateDetailDTO.setUpdatedBy(WebServletContext.getUserId());
                communicateDetailMapper.updateAssignInfoForCommunicate(communicateDetailDTO);
            }

            operationRecordService.insertDispatchRecordByLabour(taskInfoDTO.getReportNo(), taskInfoDTO.getTaskDefinitionBpmKey(), false, userId);
            /**
             * 发送调度邮件
             */
//            String reportNo = taskInfoMapper.getReportNoById(id);
//            mailSendService.sendCaseMail(reportNo,userId);

            //发送调度短信
//            sendDispatchSms(reportNo,mobileNo);

            // 未决审批，立案审批调度到人时同步对接办事中心
            if(BpmConstants.OC_REGISTER_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey()) || BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
                unSettleApproveWaitingCenterServiceImpl.createInstance(id);
            }
        }
    }

    /**
     * 任务认领
     */
    @Override
    public void reAssign(String id, String userId, String assigneeName,String comCode) throws GlobalBusinessException {
        TaskInfoDTO taskInfoDTO = taskInfoMapper.selectTaskByTaskId(id);
        if (BpmConstants.APPROVETASK_CONTROL.contains(taskInfoDTO.getTaskDefinitionBpmKey()) && userId.equals(taskInfoDTO.getApplyer())){
            throw new GlobalBusinessException(ErrorCode.Dispatch.DO_NOT_SELF_DISPATCH,"不能认领自己提交的审批案件！");
        }
        /** 20240716 madelynsun 要求去掉该校验
        if(BpmConstants.OC_SETTLE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
            Integer leave = permissionService.getUserGrade(Constants.PERMISSION_VERIFY,comCode,userId);
            if (leave == null || taskInfoDTO.getAuditGrade() == null || leave < taskInfoDTO.getAuditGrade()){
                LogUtil.audit("待审批等级={},用户等级={}",taskInfoDTO.getAuditGrade(),leave);
                throw new GlobalBusinessException(ErrorCode.Dispatch.DO_NOT_SELF_DISPATCH,"非本人权限内案件，不予领取");
            }
        }
        **/
        //20250328 获取当前用户估损审批的权限等级:案件的估损金额(赔款+费用)大于等于当前处理人的审核权限的上限金额，需要审核，否则不需要审批
        /*if(BpmConstants.OC_REGISTER_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
            Integer leave = permissionService.getUserGrade(Constants.PERMISSION_REGIST,comCode,userId);
            if (leave == null || taskInfoDTO.getAuditGrade() == null || leave < taskInfoDTO.getAuditGrade()){
                LogUtil.audit("待审批等级={},用户等级={}",taskInfoDTO.getAuditGrade(),leave);
                throw new GlobalBusinessException(ErrorCode.Dispatch.DO_NOT_SELF_DISPATCH,"非本人权限内案件，不予领取");
            }
        }*/

        if(BpmConstants.OC_REGISTER_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey()) || BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
            PermissionUserVO permissionUserVO = new PermissionUserVO();
            permissionUserVO.setUserId(userId);
            permissionUserVO.setTypeCode(Constants.PERMISSION_REGIST);
            List<PermissionUserDTO> permissionUserList = permissionUserMapper.getPermissionUserList(permissionUserVO);
            if (!CollectionUtils.isEmpty(permissionUserList)) {
                PermissionUserDTO permissionUserDTO = permissionUserList.get(0);
                if (permissionUserDTO.getGrade() < taskInfoDTO.getTaskGrade()) {
//                    throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "非本人立案审批权限内案件，不予领取");
                }
            } else {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "未配置案件机构的立案审批权限，请联系管理员配置权限！");
            }
        }

        taskInfoMapper.reAssign(id, userId, assigneeName,comCode);
        operationRecordService.insertDispatchRecordByLabour(taskInfoDTO.getReportNo(), taskInfoDTO.getTaskDefinitionBpmKey(), false, userId);
        // 未决审批，立案审批认领到人时同步对接办事中心
        if(BpmConstants.OC_REGISTER_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey()) || BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
            unSettleApproveWaitingCenterServiceImpl.createInstance(id);
        }
    }


    @Override
    public DepartmentVO getSelectDepartmentList4Query(String departmentCode){
        DepartmentVO departmentVO = new DepartmentVO();
        DepartmentDTO departmentDTO = departmentDefineService.queryDepartmentInfoByDeptCode(departmentCode);
        if (departmentDTO != null){
            departmentVO = departmentDTO.convertToVo();
            List<DepartmentVO> list = departmentDefineService.queryChildDepartmentRecursion(departmentCode);
            if (ConfigConstValues.HQ_DEPARTMENT.equals(departmentCode)){
                list = list.stream().filter(d->!ConfigConstValues.HQ_DEPARTMENT.equals(d.getDepartmentCode())).collect(Collectors.toList());
            }
            Map<String,List<DepartmentVO>> map = list.stream().collect(Collectors.groupingBy(DepartmentVO::getUpperDepartmentCode));
           childDepartmentRecursion(departmentVO,map);
        }
        return departmentVO;
    }

    /**
     * 递归查找子节点列表
     */
    private void childDepartmentRecursion(DepartmentVO vo,Map<String,List<DepartmentVO>> map){
        if(!map.containsKey(vo.getDepartmentCode())){
            return;
        }
        List<DepartmentVO> child = map.get(vo.getDepartmentCode());
        child.forEach(c->childDepartmentRecursion(c,map));
        vo.setChildDepartmentVO(child);
    }

    @Override
    public List<DepartmentVO> getSelectDepartmentList4Dispatch(String departmentCode){
        departmentCode = StringUtils.isEmpty(departmentCode) ? WebServletContext.getDepartmentCode() : departmentCode;
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            departmentCode = departmentUserVO.getDepartmentCode();
        }
        DepartmentVO departmentVO = new DepartmentVO();
        DepartmentDTO departmentDTO = departmentDefineService.queryDepartmentInfoByDeptCode(departmentCode);
        if (departmentDTO != null){
            departmentVO = departmentDTO.convertToVo();
            if(!ConfigConstValues.HQ_DEPARTMENT.equals(departmentCode)){
                List<DepartmentVO> list = departmentDefineService.queryParentDepartmentRecursion(departmentCode);
                Map<String,DepartmentVO> map = list.stream()
                        .collect(Collectors.toMap(DepartmentVO::getDepartmentCode, Function.identity()));
                DepartmentVO vo = parentDepartmentRecursion(departmentCode,map);
                // 根据操作人的机构等级权限做筛选
                return Stream.of(vo).collect(Collectors.toList());
            }
        }
        return Stream.of(departmentVO).collect(Collectors.toList());
    }

    @Override
    public List<DepartmentVO> getSelectDepartmentList(){
        String deptCode = WebServletContext.getDepartmentCode();
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            deptCode = departmentUserVO.getDepartmentCode();
        }
        List<DepartmentVO> departmentVOList = departmentDefineService.getSelectDepartmentList();
        if(!"1".equals(deptCode)){
            String finalDeptCode = deptCode;
            List<DepartmentVO> filterDepartmentVOList = departmentVOList.stream()
                    .filter(dept -> dept.getDepartmentCode().equals(finalDeptCode))
                    .collect(Collectors.toList());
            return filterDepartmentVOList;
        }
        return departmentVOList;
    }

    /**
     * 递归查找父节点列表
     */
    private DepartmentVO parentDepartmentRecursion(String departmentCode,Map<String,DepartmentVO> map){
        DepartmentVO vo = map.get(departmentCode);
        String parentCode = vo.getUpperDepartmentCode();
        if (!parentCode.equals(BaseConstant.STRING_1)){
            vo.setParentDepartment(parentDepartmentRecursion(parentCode,map));
        }
        return vo;
    }

    //发送调度短信
    public void sendDispatchSms(String reportNo ,String mobileNo){
        try {
            if(mobileNo != null && mobileNo.length() == 11 && mobileNo.startsWith("1")){
                List<LinkManEntity> linkManEntities = reportService.getLinkMans(reportNo,null);
                for (LinkManEntity man : linkManEntities){
                    if(com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(man.getLinkManName())){
                        //获取投保人、出险时间、出险类型
                        List<String> holderNameList = Optional.ofNullable(taskInfoMapper.getPolicyHolder(reportNo)).orElse(new ArrayList<>());
                        String holderNames = String.join(",",holderNameList);
                        String accidentDate = DateUtils.parseToFormatString(taskInfoMapper.getAccidentDate(reportNo));
                        String accidentType = InsuredApplyTypeEnum.getName(taskInfoMapper.getAccidentType(reportNo));
                        String txt = String.format(Constants.DISPATCH_SMS_TEMPLATE,reportNo,man.getLinkManName(),
                                man.getLinkManTelephone(),holderNames,accidentDate,accidentType);
                        smsInfoService.sendSmsByAsync(new SmsInfoDTO(reportNo,mobileNo,txt,Constants.SMS_LINK_DISPATCH));
                        return;
                    }
                }
            }

        }catch (Exception e){
            LogUtil.error("调度完成发送短信失败，报案号：{}", reportNo, e);
        }
    }

    private String getUserName(String userCode){
        if (com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(userCode) ){
            try {
                UserInfoDTO userInfo = cacheService.queryUserInfo(userCode);
                if (userInfo != null){
                    return userInfo.getUserName();
                }
            }catch (NcbsException e){
                log.info("调用[根据用户编码查询用户信息]接口异常!");
            }
        }
        return "";
    }

    /**
     * 添加通知
     * @param taskIdList 任务列表
     * @param newUserId  接收人工号
     */
    @Override
    public void addNoticesList(List<String> taskIdList, String newUserId) {
        String userId = WebServletContext.getUserId();
        for(String id : taskIdList){
            TaskInfoDTO taskInfoDTO = taskInfoMapper.selectTaskByTaskId(id);
            //判断任务接收人与登录人是否同一人。
            if (!newUserId.equals(userId)){
                NoticesDTO noticesDTO = new NoticesDTO();
                noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_TASK);
                noticesDTO.setReportNo(taskInfoDTO.getReportNo());
                noticesDTO.setCaseTimes(taskInfoDTO.getCaseTimes());
                noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
                noticesDTO.setNoticeSubClass(taskInfoDTO.getTaskDefinitionBpmKey());
                noticeService.saveNotices(noticesDTO,newUserId);
            }
        }
    }
}
