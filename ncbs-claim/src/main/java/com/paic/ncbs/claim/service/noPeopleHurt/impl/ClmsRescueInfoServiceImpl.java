package com.paic.ncbs.claim.service.noPeopleHurt.impl;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsRescueInfoMapper;
import com.paic.ncbs.claim.dao.entity.clms.ClmsRescueInfo;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsRescueInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 救援信息表(ClmsRescueInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:50
 */
@Service("clmsRescueInfoService")
public class ClmsRescueInfoServiceImpl implements ClmsRescueInfoService {
    @Resource
    private ClmsRescueInfoMapper clmsRescueInfoMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsRescueInfo queryById(String id) {
        return this.clmsRescueInfoMapper.queryById(id);
    }

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public List<ClmsRescueInfo> queryByReportNo(String reportNo, int caseTimes) {
        return this.clmsRescueInfoMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsRescueInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsRescueInfo insert(ClmsRescueInfo clmsRescueInfo) {
        clmsRescueInfo.setCreatedBy(WebServletContext.getUserId());
        clmsRescueInfo.setCreatedDate(new Date());
        clmsRescueInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsRescueInfo.setUpdatedDate(new Date());
        clmsRescueInfo.setId(UuidUtil.getUUID());
        this.clmsRescueInfoMapper.insert(clmsRescueInfo);
        return clmsRescueInfo;
    }

    /**
     * 修改数据
     *
     * @param clmsRescueInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsRescueInfo update(ClmsRescueInfo clmsRescueInfo) {
        clmsRescueInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsRescueInfo.setUpdatedDate(new Date());
        this.clmsRescueInfoMapper.update(clmsRescueInfo);
        return this.queryById(clmsRescueInfo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsRescueInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByCondition(String reportNo, int caseTimes) {
        clmsRescueInfoMapper.deleteByCondition(reportNo, caseTimes);
    }
}
