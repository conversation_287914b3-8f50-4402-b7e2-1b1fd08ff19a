package com.paic.ncbs.claim.controller.mng;


import com.paic.ncbs.claim.common.page.PageResult;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.vo.mng.UcmsCallRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

@Api(tags = "虚拟号")
@RestController
@RequestMapping("mng/app/virtualNumAction")
public class VirtualNumController extends BaseController {

    @ApiOperation("查询呼叫记录")
    @GetMapping(value = "/queryCallRecords")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String", dataTypeClass=String.class),
            @ApiImplicitParam(name = "currentPage", value = "当前页", dataType = "Int",dataTypeClass=Integer.class),
            @ApiImplicitParam(name = "perPageSize", value = "每页显示条数", dataType = "Int",dataTypeClass=Integer.class)
    })    public ResponseResult<PageResult<UcmsCallRecordVO>> queryCallRecords(@RequestParam(required = false) String reportNo,
                                                                               @RequestParam(required = false) Integer currentPage,
                                                                               @RequestParam(required = false) Integer perPageSize) {
        return ResponseResult.success(new PageResult<>(Collections.emptyList()));
    }

}
