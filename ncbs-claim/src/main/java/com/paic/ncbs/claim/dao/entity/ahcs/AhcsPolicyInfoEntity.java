package com.paic.ncbs.claim.dao.entity.ahcs;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 保单基本信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AhcsPolicyInfoEntity extends EntityDTO {

    private static final long serialVersionUID = 8771003733112197871L;

    /**  主键 */
    private String idAhcsPolicyInfo;

    /**  报案表里面的报案号 */
    private String reportNo;

    /**  保单号 */
    private String policyNo;

    /**  承保时间 */
    private Date acceptInsuranceDate;

    /**  承保机构编码 */
    private String departmentCode;

    /**  保单开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceBeginTime;

    /**  保单结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceEndTime;

    /**  核保通过时间 */
    private Date underwriteDate;

    /**  保单状态 01退保 02注销 03退保恢复 04契撤 B5在保 */
    private String policyStatus;

    /**  产品代码 */
    private String productCode;

    /**  产品名称 */
    private String productName;

    /**  业务类型 1-个人 2-团体 */
    private String businessType;

    /**  应交保费 */
    private BigDecimal totalAgreePremium;

    /**  实交保费 */
    private BigDecimal totalActualPremium;

    /**  保额币种 */
    private String amountCurrencyCode;

    /**  保费币种 */
    private String premiumCurrencyCode;

    /**  数据产生源 */
    private String dataSource;

    /**  共保标志 0 非共保1 共保 */
    private String coinsuranceMark;

    /**  总保额 */
    private BigDecimal totalInsuredAmount;

    /**  组合产品再保批复编号 */
    private String replyCode;

    /**  组合产品再保批复名称 */
    private String replyName;

    /**  保单出单系统ID */
    private String systemId;

    /**  批单系统ID */
    private String endorseSystemId;

    /**  业务员代码 */
    private String saleAgentCode;

    /**  业务员姓名 */
    private String saleAgentName;

    /**  是否社保保单 1是 0 否 */
    private String socialFlag;

    /**  是否协议定义 1是 0否 */
    private String agreenmentDefine;

    /**  业务来源编码 */
    private String businessSourceCode;

    /**  业务来源编码名称 */
    private String businessSourceName;

    /**  业务来源细分编码 */
    private String businessSourceDetailCode;

    /**  业务来源细分编码名称 */
    private String businessSourceDetailName;

    /**  渠道编码 */
    private String channelSourceCode;

    /**  渠道编码名称 */
    private String channelSourceName;

    /**  渠道细分编码 */
    private String channelSourceDetailCode;

    /**  渠道细分编码名称 */
    private String channelSourceDetailName;

    /**  虚拟标的数量 大于0 说明是虚拟保单 */
    private Long virtualTargetNum;
    /**  保单有效性（Y：有效 N：无效） */
    private String policyValid;
    /**  批改生效时间 */
    private Date edrEffectiveDate;
    /**  批单号 */
    private String endorseNo;
    /**  投保日期 */
    private Date applyDate;
    /**  套餐编码 */
    private String productPackageType;
    /**  层级号 */
    private String subjectId;
    /**  版本号 */
    private String productVersion;
    /**  批次号 */
    private String batchNo;
    /**  自助卡号 */
    private String selfCardNo;
    /**  电子保单号 */
    private String policyCerNo;
    /**  上传标记（0：上传失败， 1：上传成功） */
    private String isUploadFlatSuccessed;
    /**  赔案号 */
    private String caseNo;
    /**  旧转新标识  00：PAS原始数据，01：旧转新生成的数据 */
    private String generateFlag;
    /**  标的共享保额,1：是，0：否 */
    private String shareInsuredAmount;
    /**  老系统的产品编码(旧转新用)  */
    private String orgProductCode;
    /**  老系统的产品名称(旧转新用)  */
    private String orgProductName;
    /**  是否是见费出单(1-是;0-否) */
    private String isPolicyBeforePayfee;

    private String renewalType;
    /**  备注 */
    private String remark;
    /**  续保上次保单号 */
    private String lastPolicyNo;

    /**  影像ID */
    private String groupId;

    /**  班次 */
    private String trafficNo;
    /**  保单投保份数 */
    private BigDecimal applyNum;

    /**  保单表扩展领域外字段，理算解析使用，可扩展增加字段 */
    private String policyExtend;

    /**  合作伙伴编码 */
    private String partnerCode;

    /**  健康告知 */
    private String healthNotification;

    /**  缴费期数 */
    private Integer payTermNo;


    private String reportRegisterUm;


    private String relatedType;

    /**  合作伙伴名 */
    private String partnerName;

    /**  主介绍人代码 */
    private String primaryIntroducerCode;

    /**  代理人/经纪人代码 */
    private String agentBrokerCode;

    /**  代理人/经纪人类型,1:代理人,2:经纪人 */
    private String agentBrokerType;

    /**  代理人/经纪人名称 */
    private String agentBrokerName;

    /**
     * 是否临分业务(1-是;0-否)
     */
    private String isFacultativeBusiness;

    /**
     * 是否家庭单(1-是;0-否)
     */
    private String isFamily;

    /**
     * 是否家庭单理赔用(1-是;0-否)
     */
    private String isClaimFamily;

    /**
     * 家庭单单号
     * */
    private String onlineOrderNo;

    /**
     * 产品大类
     */
    private String productClass;

    /**
     * 保单累计责任限额
     */
    private BigDecimal totalDutyLimit;

    /**
     * 每次事故责任限额
     */
    private BigDecimal onceDutyLimit;

    /**
     * 累计法律费用责任限额
     */
    private BigDecimal totalLawDutyLimit;

    /**
     * 每次事故法律费用责任限额
     */
    private BigDecimal onceLawDutyLimit;

    /**
     * 免赔条件(最大1000字)
     */
    private String remitCondition;

    /**
     * 特别约定(最大20000字)
     */
    private String policySpecialPromise;

    /**
     * 转保保单
     */
    private String transferInsurancePolicyNo;

    /**
     * 转保止期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date transferInsuranceEndDate;

    /**
     * 转保产品名称
     */
    private String transferInsuranceProductName;

    public String getTransferInsurancePolicyNo() {
        return transferInsurancePolicyNo;
    }

    public void setTransferInsurancePolicyNo(String transferInsurancePolicyNo) {
        this.transferInsurancePolicyNo = transferInsurancePolicyNo;
    }

    public Date getTransferInsuranceEndDate() {
        return transferInsuranceEndDate;
    }

    public void setTransferInsuranceEndDate(Date transferInsuranceEndDate) {
        this.transferInsuranceEndDate = transferInsuranceEndDate;
    }

    public String getTransferInsuranceProductName() {
        return transferInsuranceProductName;
    }

    public void setTransferInsuranceProductName(String transferInsuranceProductName) {
        this.transferInsuranceProductName = transferInsuranceProductName;
    }

    /**
     * 标的类型
     */
    private String targetType;

    public String getDepartmentCodeNew() {
        return departmentCodeNew;
    }

    public void setDepartmentCodeNew(String departmentCodeNew) {
        this.departmentCodeNew = departmentCodeNew;
    }

    /**
     * 承保机构代码（原承保机构代码改为业绩归属机构，故新增该字段)
     */
    private String departmentCodeNew;

    public String getRiskGroupType() {
        return riskGroupType;
    }

    public void setRiskGroupType(String riskGroupType) {
        this.riskGroupType = riskGroupType;
    }

    /**
     * 标的组类别
     */
    private String riskGroupType;

    public String getProfitCenter() {
        return profitCenter;
    }

    public Integer getProsecutionPeriod() {
        return prosecutionPeriod;
    }

    public void setProsecutionPeriod(Integer prosecutionPeriod) {
        this.prosecutionPeriod = prosecutionPeriod;
    }

    public Integer getExtendReportDate() {
        return extendReportDate;
    }

    public void setExtendReportDate(Integer extendReportDate) {
        this.extendReportDate = extendReportDate;
    }

    /**
     * 追溯期-单位天
     */
    private Integer prosecutionPeriod;

    /**
     * 延长报告期-单位天
     */

    private Integer extendReportDate;

    public void setProfitCenter(String profitCenter) {
        this.profitCenter = profitCenter;
    }

    /**
     * 利润中心
     */
    private String profitCenter;

    public String getIsTransferInsure() {
        return isTransferInsure;
    }

    public void setIsTransferInsure(String isTransferInsure) {
        this.isTransferInsure = isTransferInsure;
    }

    /**
     * 是否转保 1是 0否
     */
    private String isTransferInsure;

    public String getIsFamily() {
        return isFamily;
    }

    public void setIsFamily(String isFamily) {
        this.isFamily = isFamily;
    }

    public String getOnlineOrderNo() {
        return onlineOrderNo;
    }

    public void setOnlineOrderNo(String onlineOrderNo) {
        this.onlineOrderNo = onlineOrderNo;
    }

    public String getReportRegisterUm() {
        return reportRegisterUm;
    }

    public void setReportRegisterUm(String reportRegisterUm) {
        this.reportRegisterUm = reportRegisterUm;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getPayTermNo() {
        return payTermNo;
    }

    public void setPayTermNo(Integer payTermNo) {
        this.payTermNo = payTermNo;
    }

    public String getTrafficNo() {
        return trafficNo;
    }

    public void setTrafficNo(String trafficNo) {
        this.trafficNo = trafficNo;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getIsUploadFlatSuccessed() {
        return isUploadFlatSuccessed;
    }

    public void setIsUploadFlatSuccessed(String isUploadFlatSuccessed) {
        this.isUploadFlatSuccessed = isUploadFlatSuccessed;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getProductPackageType() {
        return productPackageType;
    }

    public void setProductPackageType(String productPackageType) {
        this.productPackageType = productPackageType;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }

    public String getProductVersion() {
        return productVersion;
    }

    public void setProductVersion(String productVersion) {
        this.productVersion = productVersion;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public Date getEdrEffectiveDate() {
        return edrEffectiveDate;
    }

    public void setEdrEffectiveDate(Date edrEffectiveDate) {
        this.edrEffectiveDate = edrEffectiveDate;
    }

    public String getPolicyValid() {
        return policyValid;
    }

    public void setPolicyValid(String policyValid) {
        this.policyValid = policyValid;
    }


    public String getIdAhcsPolicyInfo() {
        return idAhcsPolicyInfo;
    }


    public void setIdAhcsPolicyInfo(String idAhcsPolicyInfo) {
        this.idAhcsPolicyInfo = idAhcsPolicyInfo == null ? null : idAhcsPolicyInfo.trim();
    }


    public String getReportNo() {
        return reportNo;
    }


    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }


    public String getPolicyNo() {
        return policyNo;
    }


    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo == null ? null : policyNo.trim();
    }


    public Date getAcceptInsuranceDate() {
        return acceptInsuranceDate;
    }


    public void setAcceptInsuranceDate(Date acceptInsuranceDate) {
        this.acceptInsuranceDate = acceptInsuranceDate;
    }


    public String getDepartmentCode() {
        return departmentCode;
    }


    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode == null ? null : departmentCode.trim();
    }


    public Date getInsuranceBeginTime() {
        return insuranceBeginTime;
    }


    public void setInsuranceBeginTime(Date insuranceBeginTime) {
        this.insuranceBeginTime = insuranceBeginTime;
    }


    public Date getInsuranceEndTime() {
        return insuranceEndTime;
    }


    public void setInsuranceEndTime(Date insuranceEndTime) {
        this.insuranceEndTime = insuranceEndTime;
    }


    public Date getUnderwriteDate() {
        return underwriteDate;
    }


    public void setUnderwriteDate(Date underwriteDate) {
        this.underwriteDate = underwriteDate;
    }


    public String getPolicyStatus() {
        return policyStatus;
    }


    public void setPolicyStatus(String policyStatus) {
        this.policyStatus = policyStatus == null ? null : policyStatus.trim();
    }


    public String getProductCode() {
        return productCode;
    }


    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }


    public String getProductName() {
        return productName;
    }


    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }


    public String getBusinessType() {
        return businessType;
    }


    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }


    public BigDecimal getTotalAgreePremium() {
        return totalAgreePremium;
    }


    public void setTotalAgreePremium(BigDecimal totalAgreePremium) {
        this.totalAgreePremium = totalAgreePremium;
    }


    public BigDecimal getTotalActualPremium() {
        return totalActualPremium;
    }


    public void setTotalActualPremium(BigDecimal totalActualPremium) {
        this.totalActualPremium = totalActualPremium;
    }


    public String getAmountCurrencyCode() {
        return amountCurrencyCode;
    }


    public void setAmountCurrencyCode(String amountCurrencyCode) {
        this.amountCurrencyCode = amountCurrencyCode == null ? null : amountCurrencyCode.trim();
    }


    public String getPremiumCurrencyCode() {
        return premiumCurrencyCode;
    }


    public void setPremiumCurrencyCode(String premiumCurrencyCode) {
        this.premiumCurrencyCode = premiumCurrencyCode == null ? null : premiumCurrencyCode.trim();
    }


    public String getDataSource() {
        return dataSource;
    }


    public void setDataSource(String dataSource) {
        this.dataSource = dataSource == null ? null : dataSource.trim();
    }


    public String getCoinsuranceMark() {
        return coinsuranceMark;
    }


    public void setCoinsuranceMark(String coinsuranceMark) {
        this.coinsuranceMark = coinsuranceMark == null ? null : coinsuranceMark.trim();
    }


    public BigDecimal getTotalInsuredAmount() {
        return totalInsuredAmount;
    }


    public void setTotalInsuredAmount(BigDecimal totalInsuredAmount) {
        this.totalInsuredAmount = totalInsuredAmount;
    }


    public String getReplyCode() {
        return replyCode;
    }


    public void setReplyCode(String replyCode) {
        this.replyCode = replyCode == null ? null : replyCode.trim();
    }


    public String getReplyName() {
        return replyName;
    }


    public void setReplyName(String replyName) {
        this.replyName = replyName == null ? null : replyName.trim();
    }


    public String getSystemId() {
        return systemId;
    }


    public void setSystemId(String systemId) {
        this.systemId = systemId == null ? null : systemId.trim();
    }


    public String getEndorseSystemId() {
        return endorseSystemId;
    }


    public void setEndorseSystemId(String endorseSystemId) {
        this.endorseSystemId = endorseSystemId == null ? null : endorseSystemId.trim();
    }


    public String getSaleAgentCode() {
        return saleAgentCode;
    }


    public void setSaleAgentCode(String saleAgentCode) {
        this.saleAgentCode = saleAgentCode == null ? null : saleAgentCode.trim();
    }


    public String getSaleAgentName() {
        return saleAgentName;
    }


    public void setSaleAgentName(String saleAgentName) {
        this.saleAgentName = saleAgentName == null ? null : saleAgentName.trim();
    }


    public String getSocialFlag() {
        return socialFlag;
    }


    public void setSocialFlag(String socialFlag) {
        this.socialFlag = socialFlag == null ? null : socialFlag.trim();
    }


    public String getAgreenmentDefine() {
        return agreenmentDefine;
    }


    public void setAgreenmentDefine(String agreenmentDefine) {
        this.agreenmentDefine = agreenmentDefine == null ? null : agreenmentDefine.trim();
    }


    public String getBusinessSourceCode() {
        return businessSourceCode;
    }


    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode == null ? null : businessSourceCode.trim();
    }


    public String getBusinessSourceName() {
        return businessSourceName;
    }


    public void setBusinessSourceName(String businessSourceName) {
        this.businessSourceName = businessSourceName == null ? null : businessSourceName.trim();
    }


    public String getBusinessSourceDetailCode() {
        return businessSourceDetailCode;
    }


    public void setBusinessSourceDetailCode(String businessSourceDetailCode) {
        this.businessSourceDetailCode = businessSourceDetailCode == null ? null : businessSourceDetailCode.trim();
    }


    public String getBusinessSourceDetailName() {
        return businessSourceDetailName;
    }


    public void setBusinessSourceDetailName(String businessSourceDetailName) {
        this.businessSourceDetailName = businessSourceDetailName == null ? null : businessSourceDetailName.trim();
    }


    public String getChannelSourceCode() {
        return channelSourceCode;
    }


    public void setChannelSourceCode(String channelSourceCode) {
        this.channelSourceCode = channelSourceCode == null ? null : channelSourceCode.trim();
    }


    public String getChannelSourceName() {
        return channelSourceName;
    }


    public void setChannelSourceName(String channelSourceName) {
        this.channelSourceName = channelSourceName == null ? null : channelSourceName.trim();
    }


    public String getChannelSourceDetailCode() {
        return channelSourceDetailCode;
    }


    public void setChannelSourceDetailCode(String channelSourceDetailCode) {
        this.channelSourceDetailCode = channelSourceDetailCode == null ? null : channelSourceDetailCode.trim();
    }


    public String getChannelSourceDetailName() {
        return channelSourceDetailName;
    }


    public void setChannelSourceDetailName(String channelSourceDetailName) {
        this.channelSourceDetailName = channelSourceDetailName == null ? null : channelSourceDetailName.trim();
    }


    public Long getVirtualTargetNum() {
        return virtualTargetNum;
    }


    public void setVirtualTargetNum(Long virtualTargetNum) {
        this.virtualTargetNum = virtualTargetNum;
    }

    public String getSelfCardNo() {
        return selfCardNo;
    }

    public void setSelfCardNo(String selfCardNo) {
        this.selfCardNo = selfCardNo;
    }

    public String getPolicyCerNo() {
        return policyCerNo;
    }

    public void setPolicyCerNo(String policyCerNo) {
        this.policyCerNo = policyCerNo;
    }

    public String getGenerateFlag() {
        return generateFlag;
    }

    public void setGenerateFlag(String generateFlag) {
        this.generateFlag = generateFlag;
    }

    public String getShareInsuredAmount() {
        return shareInsuredAmount;
    }

    public void setShareInsuredAmount(String shareInsuredAmount) {
        this.shareInsuredAmount = shareInsuredAmount;
    }

    public String getOrgProductCode() {
        return orgProductCode;
    }

    public void setOrgProductCode(String orgProductCode) {
        this.orgProductCode = orgProductCode;
    }

    public String getOrgProductName() {
        return orgProductName;
    }

    public void setOrgProductName(String orgProductName) {
        this.orgProductName = orgProductName;
    }

    public String getIsPolicyBeforePayfee() {
        return isPolicyBeforePayfee;
    }

    public void setIsPolicyBeforePayfee(String isPolicyBeforePayfee) {
        this.isPolicyBeforePayfee = isPolicyBeforePayfee;
    }

    public String getRenewalType() {
        return renewalType;
    }

    public void setRenewalType(String renewalType) {
        this.renewalType = renewalType;
    }


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLastPolicyNo() {
        return lastPolicyNo;
    }

    public void setLastPolicyNo(String lastPolicyNo) {
        this.lastPolicyNo = lastPolicyNo;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public BigDecimal getApplyNum() {
        return applyNum;
    }

    public void setApplyNum(BigDecimal applyNum) {
        this.applyNum = applyNum;
    }

    public String getPolicyExtend() {
        return policyExtend;
    }

    public void setPolicyExtend(String policyExtend) {
        this.policyExtend = policyExtend;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getHealthNotification() {
        return healthNotification;
    }

    public void setHealthNotification(String healthNotification) {
        this.healthNotification = healthNotification;
    }

    public String getRelatedType() {
        return relatedType;
    }

    public void setRelatedType(String relatedType) {
        this.relatedType = relatedType;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPrimaryIntroducerCode() {
        return primaryIntroducerCode;
    }

    public void setPrimaryIntroducerCode(String primaryIntroducerCode) {
        this.primaryIntroducerCode = primaryIntroducerCode;
    }

    public String getAgentBrokerCode() {
        return agentBrokerCode;
    }

    public void setAgentBrokerCode(String agentBrokerCode) {
        this.agentBrokerCode = agentBrokerCode;
    }

    public String getAgentBrokerType() {
        return agentBrokerType;
    }

    public void setAgentBrokerType(String agentBrokerType) {
        this.agentBrokerType = agentBrokerType;
    }

    public String getAgentBrokerName() {
        return agentBrokerName;
    }

    public void setAgentBrokerName(String agentBrokerName) {
        this.agentBrokerName = agentBrokerName;
    }

    public String getIsFacultativeBusiness() {
        return isFacultativeBusiness;
    }

    public void setIsFacultativeBusiness(String isFacultativeBusiness) {
        this.isFacultativeBusiness = isFacultativeBusiness;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public BigDecimal getTotalDutyLimit() {
        return totalDutyLimit;
    }

    public void setTotalDutyLimit(BigDecimal totalDutyLimit) {
        this.totalDutyLimit = totalDutyLimit;
    }

    public BigDecimal getOnceDutyLimit() {
        return onceDutyLimit;
    }

    public void setOnceDutyLimit(BigDecimal onceDutyLimit) {
        this.onceDutyLimit = onceDutyLimit;
    }

    public BigDecimal getTotalLawDutyLimit() {
        return totalLawDutyLimit;
    }

    public void setTotalLawDutyLimit(BigDecimal totalLawDutyLimit) {
        this.totalLawDutyLimit = totalLawDutyLimit;
    }

    public BigDecimal getOnceLawDutyLimit() {
        return onceLawDutyLimit;
    }

    public void setOnceLawDutyLimit(BigDecimal onceLawDutyLimit) {
        this.onceLawDutyLimit = onceLawDutyLimit;
    }

    public String getRemitCondition() {
        return remitCondition;
    }

    public void setRemitCondition(String remitCondition) {
        this.remitCondition = remitCondition;
    }

    public String getPolicySpecialPromise() {
        return policySpecialPromise;
    }

    public void setPolicySpecialPromise(String policySpecialPromise) {
        this.policySpecialPromise = policySpecialPromise;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }
}