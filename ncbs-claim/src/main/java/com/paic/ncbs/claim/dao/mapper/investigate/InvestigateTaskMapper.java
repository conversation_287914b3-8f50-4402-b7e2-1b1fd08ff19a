package com.paic.ncbs.claim.dao.mapper.investigate;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.user.UserDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateCooperationCompanyVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskVO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface InvestigateTaskMapper extends BaseDao<InvestigateTaskDTO> {

	int addInvestigateTask(InvestigateTaskDTO investigateTask);


    int addInvestigateTaskList(@Param("list") List<InvestigateTaskDTO> investigateTaskList);


    int modifyInvestigateTask(InvestigateTaskDTO investigateTask);

	int modifyInvestigateTaskWiThoutUpdatetime(InvestigateTaskDTO investigateTask);


    InvestigateTaskDTO getInvestigateTaskById(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);


	InvestigateTaskVO getInvestigateTaskLinkedByTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);


	List<InvestigateTaskVO> getInvestigateTaskByInvestigateId(
            @Param("idAhcsInvestigate") String idAhcsInvestigate);


	InvestigateTaskVO getMajorInvestigateTaskLinkedByInvestigateId(
            @Param("idAhcsInvestigate") String idAhcsInvestigate);


	InvestigateTaskDTO getMajorInvestigateTaskByInvestigateId(
            @Param("idAhcsInvestigate") String idAhcsInvestigate);


	List<InvestigateTaskVO> getAssistInvestigateTaskAllByMajorTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask, @Param("isOffsiteTask") String isOffsiteTask);


	int getCountUnfinishedTaskByIdAhcsInvestigate(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	int getCountNlAuditingByIdAhcsInvestigate(@Param("idAhcsInvestigate") String idAhcsInvestigate);

	List<InvestigateTaskDTO> getInvestigateTaskDTOList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


	UserDTO getLocalMajorManageByInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	InvestigateTaskDTO getInvestigateTask(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
	

	String getInvestigationEndDate(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);
	

	String getInvestigationEndDay(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);


	void modifyForWorkTransfer(@Param("oldUM") String oldUM, @Param("newUM") String newUM);


	String getMajorTaskIdByInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);
	

	List<String> getTaskIdByInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


    List<InvestigateCooperationCompanyVO> getCompanyByIdAhcsInvestigate(@Param("idAhcsInvestigate")  String idAhcsInvestigate);

	List<InvestigateCooperationCompanyVO> getCompanyByIdAhcsInvestigateAudit(@Param("idAhcsInvestigateAudit")  String idAhcsInvestigateAudit);

	List<InvestigateCooperationCompanyVO> getCompanyByIdAhcsInvestigateTask(@Param("idAhcsInvestigateTask")  String idAhcsInvestigateTask);

	Integer checkExist(@Param("investigateAudit") InvestigateAuditDTO investigateAudit);

	String getinvestigatorumname (@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}