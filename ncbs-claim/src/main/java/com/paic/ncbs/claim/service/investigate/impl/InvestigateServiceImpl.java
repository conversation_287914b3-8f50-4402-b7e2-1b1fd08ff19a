package com.paic.ncbs.claim.service.investigate.impl;


import com.google.common.base.Joiner;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseProcessMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.*;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.TpaGlobalRequest;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.chase.ChaseApplyDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAdditionalDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.investigate.TpaGlobalAgentDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.investigate.*;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseClassService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.estimate.MultipleClaimService;
import com.paic.ncbs.claim.service.investigate.ChaseService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service("investigateService")
@RefreshScope
public class InvestigateServiceImpl implements InvestigateService {

    @Autowired
    BpmService bpmService;

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    InvestigateMapper investigateDao;

    @Autowired
    private CaseClassService caseClassService;

    @Autowired
    InvestigateAuditMapper investigateAuditDao;

    @Autowired
    InvestigateAssistMapper investigateAssistDao;

    @Autowired
    InvestigateTaskAuditMapper investigateTaskAuditDao;

    @Autowired
    InvestigateTaskMapper investigateTaskDao;

    @Autowired
    InvestigateProcessMapper investigateProcessDao;

    @Autowired
    private CaseProcessMapper caseProcessDao;

    @Autowired
    private DepartmentDefineService departmentDefineService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private MultipleClaimService multipleClaimService;

    @Autowired
    private ChaseService chaseService;

    @Autowired
    private CaseZeroCancelService zeroCancelService;
    @Autowired
    private TaskPoolService taskPoolService ;
    @Autowired
    private CaseProcessService caseProcessService ;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;


    @Autowired
    private TpaGlobalRequest tpaGlobalRequest;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Value("${tpa.supplierTypeCode}")
    String supplierTypeCode;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String initInvestigate(InvestigateDTO investigate, UserInfoDTO u) throws GlobalBusinessException {
        /*  delete by zjtang 取消旧校验逻辑
        //校验当前案件是否存在除过主任务外其他未完成的流程,若存在，就不能发起提调
        TaskInfoDTO taskInfoDTOCondition = new TaskInfoDTO();
        taskInfoDTOCondition.setCaseTimes(investigate.getCaseTimes());
        taskInfoDTOCondition.setReportNo(investigate.getReportNo());
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getUndoTaskInfoList(taskInfoDTOCondition);
        if(ListUtils.isNotEmpty(taskInfoVOList) && taskInfoVOList.size() > 0) {
            throw new GlobalBusinessException("当前案件存在其他未处理任务，不能发起提调！");
        }

         */
        //校验当前流程是否有冲突 调查 发起
        bpmService.processCheck(investigate.getReportNo(),BpmConstants.OC_INVESTIGATE_APPROVAL,BpmConstants.OPERATION_INITIATE);
        investigate.setCreatedBy(u.getUserCode());
        investigate.setUpdatedBy(u.getUserCode());
        investigate.setInitiatorUm(u.getUserCode());

        // 在暂存或提交时，将审核人信息合并为auditor_info字段
        if (StringUtils.isNotEmpty(investigate.getAuditorUm()) ||
            StringUtils.isNotEmpty(investigate.getAuditorName()) ||
            StringUtils.isNotEmpty(investigate.getAuditorDepartmentCode())) {
            StringBuilder auditorInfo = new StringBuilder();
            if (StringUtils.isNotEmpty(investigate.getAuditorUm())) {
                auditorInfo.append(investigate.getAuditorUm());
            }
            auditorInfo.append("|");
            if (StringUtils.isNotEmpty(investigate.getAuditorName())) {
                auditorInfo.append(investigate.getAuditorName());
            }
            auditorInfo.append("|");
            if (StringUtils.isNotEmpty(investigate.getAuditorDepartmentCode())) {
                auditorInfo.append(investigate.getAuditorDepartmentCode());
            }
            investigate.setAuditorInfo(auditorInfo.toString());
        }

        if (investigate.getOperate() == 0){
            List<InvestigateDTO> investigateAdditionalVO =investigateDao.countNoCommit(investigate.getReportNo(),investigate.getCaseTimes()) ;
            if (CollectionUtils.isEmpty(investigateAdditionalVO)){
                investigate.setIdAhcsInvestigate(UuidUtil.getUUID());
                investigateDao.addInvestigate(investigate);
            }else {
                investigate.setIdAhcsInvestigate(investigateAdditionalVO.get(0).getIdAhcsInvestigate());
                investigateDao.modifyInvestigate(investigate);
            }
            return null;
       }
        this.checkIsCanSendInvestigate(investigate);
        investigate.setIdAhcsInvestigate(UuidUtil.getUUID());
        InvestigateDTO dto = investigateDao.getNoFinishInvestigateRecord(investigate.getReportNo(), investigate.getCaseTimes());
        if (null != dto) {
            InvestigateAdditionalDTO additional = new InvestigateAdditionalDTO();
            BeanUtils.copyProperties(dto, additional);
            additional.setIdAhcsInvestigateAdditional(UuidUtil.getUUID());
            additional.setCreatedBy(u.getUserCode());
            additional.setUpdatedBy(u.getUserCode());
            additional.setInitiatorUm(u.getUserCode());//发起人
            additional.setInvestigateItems(investigate.getInvestigateItems());
            investigateDao.addInvestigateAdditional(additional);
            return null;
        }

        investigate.setCreatedBy(u.getUserCode());
        investigate.setUpdatedBy(u.getUserCode());
        investigate.setInitiatorUm(u.getUserCode());

        // 在提交时，将审核人信息合并为auditor_info字段
        if (StringUtils.isNotEmpty(investigate.getAuditorUm()) ||
            StringUtils.isNotEmpty(investigate.getAuditorName()) ||
            StringUtils.isNotEmpty(investigate.getAuditorDepartmentCode())) {
            StringBuilder auditorInfo = new StringBuilder();
            if (StringUtils.isNotEmpty(investigate.getAuditorUm())) {
                auditorInfo.append(investigate.getAuditorUm());
            }
            auditorInfo.append("|");
            if (StringUtils.isNotEmpty(investigate.getAuditorName())) {
                auditorInfo.append(investigate.getAuditorName());
            }
            auditorInfo.append("|");
            if (StringUtils.isNotEmpty(investigate.getAuditorDepartmentCode())) {
                auditorInfo.append(investigate.getAuditorDepartmentCode());
            }
            investigate.setAuditorInfo(auditorInfo.toString());
        }

        if (ConfigConstValues.HQ_DEPARTMENT.equals(investigate.getInitiateDepartment()) || StringUtils.isEmptyStr(investigate.getInitiateDepartment())){
            String departmentCode = taskInfoService.getMajoyProcessDepartmentByReport(investigate.getReportNo(),investigate.getCaseTimes());
            if (StringUtils.isEmptyStr(departmentCode)){
                CaseProcessDTO caseProcessDTO = caseProcessDao.getCaseCompanyCode(investigate.getReportNo(),investigate.getCaseTimes());
                if (null != caseProcessDTO){
                    DepartmentDTO departmentDTO = departmentDefineService.getDepartmentL2ByCode(caseProcessDTO.getCompanyCode());
                    if (null != departmentDTO){
                        investigate.setInitiateDepartment(departmentDTO.getCode());
                    }
                }
            }else {
                DepartmentDTO dept = departmentDefineService.getDepartmentL2ByCode(departmentCode);
                if (null != dept){
                    investigate.setInitiateDepartment(dept.getCode());
                }
            }
        }
        if (StringUtils.isEmptyStr(investigate.getInitiateDepartment())) {
            investigate.setInitiateDepartment(WebServletContext.getDepartmentCode());
        }

        //提交时删除草稿任务
        investigateDao.deleteInvestigateNoOperate(investigate.getReportNo(),investigate.getCaseTimes());
        TaskInfoDTO taskInfo = taskInfoService.getTaskInfoForInvestigate(investigate.getReportNo(), investigate.getCaseTimes());
        if (null != taskInfo) {
            investigate.setTaskInstId(taskInfo.getTaskId());
        }
        investigateDao.addInvestigate(investigate);
        
        // 使用新的bpmService方法，支持指定审核人
        if (StringUtils.isNotEmpty(investigate.getAuditorUm())) {
            bpmService.startProcess_oc(investigate.getReportNo(), investigate.getCaseTimes(), BpmConstants.OC_INVESTIGATE_APPROVAL,
                    investigate.getIdAhcsInvestigate(), investigate.getInvestigateDepartment(), investigate.getInitMode(),
                    investigate.getAuditorUm(), investigate.getAuditorName(), investigate.getAuditorDepartmentCode());
        } else {
            bpmService.startProcess_oc(investigate.getReportNo(), investigate.getCaseTimes(), BpmConstants.OC_INVESTIGATE_APPROVAL,
                    investigate.getIdAhcsInvestigate(), investigate.getInvestigateDepartment(), investigate.getInitMode());
        }
        //挂起跳转状态之前的状态 ，限报案跟踪、收单、理算
        TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(investigate.getReportNo(),
                investigate.getCaseTimes(),BpmConstants.SUPEND_USE, BaseConstant.STRING_0);
        if(null != taskInfoDTO && BpmConstants.SURVER.contains(taskInfoDTO.getTaskDefinitionBpmKey())){
            bpmService.suspendOrActiveTask_oc(investigate.getReportNo(),investigate.getCaseTimes(),taskInfoDTO.getTaskDefinitionBpmKey(),true);
        }
        //更新案件子状态
        caseProcessService.updateCaseProcess(investigate.getReportNo(), investigate.getCaseTimes(), CaseProcessStatus.WAIT_INVESTIGATION_APPROVING.getCode());
        //操作记录
        operationRecordService.insertOperationRecord(investigate.getReportNo(), BpmConstants.OC_INVESTIGATE_APPROVAL, "发起", null, u.getUserCode());
        return investigate.getIdAhcsInvestigate();
    }

    @Override
    public void addIsFromOldSystem(Map<String, Object> extVariable, String reportNo) {
        String result = this.getIsOldInvestigate(reportNo);
        if ("Y".equals(result)) {
            extVariable.put(InvestigateConstants.IS_OLD_INVESTIGATE, InvestigateConstants.VALIDATE_FLAG_YES);
        } else {
            extVariable.put(InvestigateConstants.IS_OLD_INVESTIGATE, InvestigateConstants.VALIDATE_FLAG_NO);
        }
    }

    @Override
    public String getIsOldInvestigate(String reportNO) {
        int count = investigateDao.getCountInvestigateByreportNo(reportNO);
        if (count == 0) {
            return null;
        }
        String flag = investigateDao.getIsOldInvestigateByreportNo(reportNO);
        if (!ConfigConstValues.YES.equals(flag)) {
            return ConfigConstValues.NO;
        }
        return ConfigConstValues.YES;
    }

    @Override
    public void checkIsCanSendInvestigate(InvestigateDTO investigate) throws GlobalBusinessException {
        String reportNo = investigate.getReportNo();
        Integer caseTimes = investigate.getCaseTimes();

        InvestigateDTO dto = investigateDao.getNoFinishInvestigateRecord(reportNo, caseTimes);
        if (null != dto) {
            if (dto.getInvestigateStatus().equals(InvestigateConstants.AHCS_INVESTIGATE_STATUS_AUDIT)) {
                throw new GlobalBusinessException(ErrorCode.Investigate.INVESTIGATION_PROCESSING, "存在未完成的调查审核任务");
            }


            int count = investigateDao.getAdditionalCountByInvestigateId(dto.getIdAhcsInvestigate());
            if (!investigate.getInitMode().equals(InvestigateConstants.AHCS_INVESTIGATE_INIT_MODE_AUTO) && count > 0) {
                throw new GlobalBusinessException(ErrorCode.Investigate.INVESTIGATION_ADD_TO, "一次提调任务中只允许人工追加一次,前期已经追加过了");
            }
        }

        if (zeroCancelService.getIsContinueByReportNo(reportNo, caseTimes)) {
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "该报案号本次赔付下，已发起零注申请审批中或者签报中，不允许发起调查");
        }

    }


    @Override
    public InvestigateVO getInvestigateById(String idAhcsInvestigate) {
        InvestigateVO vo = investigateDao.getInvestigateById(idAhcsInvestigate);
        InvestigateVO vo2 = investigateAuditDao.getInvestigateAuditForBack(idAhcsInvestigate);
        if (null != vo2) {
            vo.setSendBackMan(vo2.getSendBackMan());
            vo.setSendBackReason(vo2.getSendBackReason());
            vo.setSendBackTime(vo2.getSendBackTime());
        }
        List<InvestigateAdditionalVO> additionals = investigateDao.getAdditionalByInvestigateId(idAhcsInvestigate);
        if (null == additionals) {
            additionals = new ArrayList<>();
        }
        vo.setAdditionals(additionals);
        InvestigateEvaluateVO evaluateVO = investigateDao.getInvestigateEvaluateVersionByIdAhcsInvestigate(idAhcsInvestigate);
        if (null != evaluateVO && StringUtils.isEmptyStr(evaluateVO.getRejectReason())) {
            vo.setInvestigateEvaluateVO(null);
        } else {
            vo.setInvestigateEvaluateVO(evaluateVO);
        }
        if (!StringUtils.isEmptyStr(vo.getAccidentScene())){
            List<String> itemNames= Arrays.asList(vo.getAccidentScene().split(",") );
            Collections.sort(itemNames);
            String  accidentSceneName = investigateDao.getSelectItemName(itemNames) ;
            vo.setAccidentSceneName(accidentSceneName);
            itemNames.forEach(e->{
                if (ConstValues.INVESTIGATE_OTHER.contains(e)){
                    vo.setAccidentSceneName(accidentSceneName+" "+vo.getOther());
                }
            });
        }
        vo.setInitiatorName(userInfoService.getUserNameById(vo.getInitiatorUm()));
        return vo;
    }


    @Override
    public List<InvestigateVO> getHistoryInvestigate(String reportNo, Integer caseTimes) {
        List<InvestigateVO> list = investigateDao.getHistoryInvestigate(reportNo, caseTimes);
        List<InvestigateVO> list2 = new ArrayList<>();
        int i = 1;  int j= 0;
        for (InvestigateVO investigateVO : list) {
            investigateVO.setOrderNo(i + "-"+ ++j);
            list2.add(investigateVO);
        }
        list2.forEach(e->{
            if (e.getInitMode().equals("02")){
                e.setInvestigateDepartmentName("外部调查机构");
            }
            e.setInitiatorName(userInfoService.getUserNameById(e.getInitiatorUm()));
        });
        return list2;
    }

    @Override
    public List<InvestigateVO> getHistoryOutInvestigate(InvestigateDTO investigateDTO) {
        List<InvestigateVO> list = investigateDao.getHistoryOutInvestigate(investigateDTO);
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setReportNo(investigateDTO.getReportNo());
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getOutInvestigateTaskInfo(taskInfoDTO);
        Map<String, TaskInfoVO> mapA = new HashMap<>();
        for (TaskInfoVO taskInfoVO : taskInfoVOList) {
            mapA.put(taskInfoVO.getOrderNo(), taskInfoVO);
        }
        for (InvestigateVO vo1 : list) {
            TaskInfoVO vo2 = mapA.get(vo1.getOrderNo());
            if (vo2 != null) {
                vo1.setCompleteTime(vo2.getCompleteTime());
            }
        }
        //增加公估公司
        TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
        List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
        Map<String, ServerInfoVO> mapB = new HashMap<>();
        for (ServerInfoVO serverInfoVO : serverInfoList) {
            mapB.put(serverInfoVO.getServerCode(), serverInfoVO);
        }
        for (InvestigateVO vo1 : list) {
            ServerInfoVO vo2 = mapB.get(vo1.getServerCode());
            if (vo2 != null) {
                vo1.setInvestigateDepartmentName(vo2.getServerName());
            }
        }
        List<InvestigateVO> list2 = new ArrayList<>();
        int i = 1;  int j= 0;
        for (InvestigateVO investigateVO : list) {
            investigateVO.setOrderNo(i + "-"+ ++j);
            list2.add(investigateVO);
        }
        list2.removeIf(vo -> vo.getInvestigateStatus() == 1 || vo.getInvestigateStatus() == 5);
        list2.sort((p1, p2) -> p2.getCompleteTime().compareTo(p1.getCompleteTime()));
        return list2;
    }


    @Override
    public List<AccidentSceneDto> getAccidentSceneData(String collectionCode) {
        List<AccidentSceneDto> list = new ArrayList<>();
        String[] codes = collectionCode.split(",");
        for (String code : codes) {
            List<AccidentSceneDto> temp = investigateDao.getAccidentSceneData(code);
            list.addAll(temp);
        }
        // 只保留 ASM_1001、ASM_1002、ASM_1003、ASM_1004、ASM_1009
        list.removeIf(item -> !"ASM_1001".equals(item.getValueCode())
                && !"ASM_1002".equals(item.getValueCode())
                && !"ASM_1003".equals(item.getValueCode())
                && !"ASM_1004".equals(item.getValueCode())
                && !"ASM_1009".equals(item.getValueCode()));
        return list;
    }


    @Override
    public List<String> getCaseClassListCode(String reportNo, Integer caseTimes) {
        return  caseClassService.getCaseClassList(reportNo, caseTimes, null);
    }


    @Override
    public InvestigateDTO getNoFinishInvestigateRecord(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        return investigateDao.getNoFinishInvestigateRecord(reportNo, caseTimes);
    }


    @Override
    public String getIsHaveEnteringFee(String reportNo, Integer caseTimes) {
        List<InvestigateVO> vos = investigateDao.getInvestigateRecord(reportNo, caseTimes);
        if (ListUtils.isNotEmpty(vos)) {
            for (InvestigateVO vo : vos) {
                if (ConfigConstValues.YES.equals(vo.getFeeAuditOption())
                        && (InvestigateConstants.AHCS_INVESTIGATE_STATUS_FINISH.equals(vo.getInvestigateStatus())
                        || InvestigateConstants.AHCS_INVESTIGATE_STATUS_EVALUATE.equals(vo.getInvestigateStatus()))) {
                    return ConfigConstValues.YES;
                }
            }
        }
        if (caseTimes > 1) {
            List<InvestigateVO> vos2 = investigateDao.getInvestigateRecord(reportNo, caseTimes - 1);
            if (ListUtils.isNotEmpty(vos2)) {
                for (InvestigateVO vo : vos2) {
                    if (ConfigConstValues.YES.equals(vo.getFeeAuditOption())
                            && (InvestigateConstants.AHCS_INVESTIGATE_STATUS_FINISH.equals(vo.getInvestigateStatus())
                            || InvestigateConstants.AHCS_INVESTIGATE_STATUS_EVALUATE.equals(vo.getInvestigateStatus()))
                            && TacheConstants.MULTI_CLAIM_AUDIT.equals(vo.getInitiateInvestigateNode())) {
                        return ConfigConstValues.YES;
                    }
                }
            }

        }

        return ConfigConstValues.NO;
    }


    @Override
    public boolean checkIsCanSendForMultiClaim(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        boolean isMultiClaimIng = false;
        boolean isChaseIng = false;
        boolean isEndIng = false;

        int count = multipleClaimService.getMultiClaimIngByReportNo(reportNo, caseTimes + 1);
        if (count > 0) {
            isMultiClaimIng = true;
        }

        ChaseApplyDTO dto = chaseService.getChaseApply(reportNo, caseTimes + 1);
        if (null != dto && "1".equals(dto.getStatus())) {
            isChaseIng = true;
        }

        CaseProcessDTO param = new CaseProcessDTO();
        param.setReportNo(reportNo);
        param.setCaseTimes(caseTimes);
        CaseProcessDTO caseDTO = caseProcessDao.getCaseProcessDTO(param);
        if (null != caseDTO) {
            String processStatus = caseDTO.getProcessStatus();
            if (ConfigConstValues.PROCESS_STATUS_PENDING_CASE.equals(processStatus)
                    || ConfigConstValues.PROCESS_STATUS_CASE_CLOSED.equals(processStatus)
                    || ConfigConstValues.PROCESS_STATUS_ZORE_CANCEL.equals(processStatus)
                    || ConfigConstValues.PROCESS_STATUS_CANCELLATION.equals(processStatus)) {
                isEndIng = true;
            }
        }

        if (isEndIng && !isMultiClaimIng && !isChaseIng) {

            return false;
        }

        return true;
    }

    @Override
    public List<InvestigateCooperationCompanyVO> getCompanyByIdAhcsInvestigate(String idAhcsInvestigate) {

        List<InvestigateCooperationCompanyVO> companyByIdAhcsInvestigate = investigateTaskDao.getCompanyByIdAhcsInvestigate(idAhcsInvestigate);
        if (CollectionUtils.isEmpty(companyByIdAhcsInvestigate)) {
            companyByIdAhcsInvestigate = new ArrayList<>();
        }
        return companyByIdAhcsInvestigate;
    }


    @Override
    public List<InvestigateCooperationCompanyVO> getCompanyByIdAhcsInvestigateTask(String idAhcsInvestigateTask) {
        List<InvestigateCooperationCompanyVO> result = investigateTaskDao.getCompanyByIdAhcsInvestigateTask(idAhcsInvestigateTask);
        if (CollectionUtils.isEmpty(result)) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public List<InvestigateVO> getInvestigateRecord(String reportNo, Integer caseTimes) {
        return investigateDao.getInvestigateRecord(reportNo, caseTimes);
    }

    @Override
    public void modifyInvestigateForOption(String idAhcsInvestigate, String feeAuditOption) {
        if (StringUtils.isNotEmpty(feeAuditOption)) {
            investigateDao.modifyInvestigateForOption(idAhcsInvestigate, feeAuditOption);
        }
    }

    @Override
    public void modifyInvestigateForFee(String idAhcsInvestigate, String isHasAdjustingFee , BigDecimal commonEstimateFee) {
        if (StringUtils.isNotEmpty(isHasAdjustingFee) ) {
            investigateDao.modifyInvestigateForFee(idAhcsInvestigate, isHasAdjustingFee,commonEstimateFee);
        }
    }

    @Override
        public InvestigateDTO getNoCommitData(String reportNo, Integer caseTimes) {
            InvestigateDTO noCommitData = investigateDao.getNoCommitData(reportNo, caseTimes);
            if (null != noCommitData) {
                noCommitData.setInvestigateDepartmentOut(noCommitData.getInvestigateDepartment());
                if (null != noCommitData.getInvestigateDepartment()){
                    noCommitData.setInvestigateDepartment(parentCodeComb(noCommitData.getInvestigateDepartment()));
                }
    
                try{
                    if (StringUtils.isNotEmpty(noCommitData.getAuditorInfo())) {
                        String[] auditorInfoParts = noCommitData.getAuditorInfo().split("\\|", -1);
                        if (auditorInfoParts.length >= 3) {
                            if (StringUtils.isNotEmpty(auditorInfoParts[0])) {
                                noCommitData.setAuditorUm(auditorInfoParts[0]);
                            }
                            if (StringUtils.isNotEmpty(auditorInfoParts[1])) {
                                noCommitData.setAuditorName(auditorInfoParts[1]);
                            }
                            if (StringUtils.isNotEmpty(auditorInfoParts[2])) {
                                noCommitData.setAuditorDepartmentCode(auditorInfoParts[2]);
                            }
                        }
                    }
                } catch (Exception e) {
                   log.error("审批人字段拆解异常auditorInfo");
                }
            }
            return noCommitData;
        }

    @Override
    public Integer getIvvesigateCount(String reportNo, Integer caseTimes) {
        return investigateDao.getIvvesigateCount(reportNo,caseTimes);
    }
    public String parentCodeComb(String departmentCode){
        List<DepartmentVO> selectDepartmentList4Dispatch = taskPoolService.getSelectDepartmentList4Dispatch(departmentCode);
        StringBuffer  parentCodeComb =new StringBuffer("") ;
        if (!CollectionUtils.isEmpty(selectDepartmentList4Dispatch) && (null != selectDepartmentList4Dispatch.get(0).getDepartmentCode())){
            DepartmentVO parentDepartment = selectDepartmentList4Dispatch.get(0).getParentDepartment() ;
            parentCodeComb.append(selectDepartmentList4Dispatch.get(0).getDepartmentCode()).append(",") ;
            while (null !=parentDepartment && null !=parentDepartment.getParentDepartment()){
                parentCodeComb.append(parentDepartment.getDepartmentCode()+",") ;
                parentDepartment =parentDepartment.getParentDepartment();
                if(null == parentDepartment.getParentDepartment()){
                    parentCodeComb.append(parentDepartment.getDepartmentCode()) ;
                }
            }
        }
        List<String>  codeComb= Arrays.asList(parentCodeComb.toString().split(","));
        Collections.reverse(codeComb);
        return  CollectionUtils.isEmpty(codeComb) ? "" : Joiner.on(",").join(codeComb) ;
    }
    /**
     * 获取外部部门列表
     * @param userCode 查询用户所在公估公司必传，否则返回所有外部部门
     */
    @Override
    public  ResponseResult getExternalDepartmentList(String userCode) {
        TpaGlobalAgentDTO tpaGlobalAgentDTO = new TpaGlobalAgentDTO();
        if(tpaGlobalAgentDTO.getRequestData() == null){
            TpaGlobalAgentDTO.TpaRequestData tpaRequestData = tpaGlobalAgentDTO.createTpaRequestData();
            LogUtil.audit("供应商类型："+supplierTypeCode);
            tpaRequestData.setSupplierTypeCode(supplierTypeCode);
            tpaGlobalAgentDTO.setRequestData(tpaRequestData);
        }
        // 查询用户所在公估公司
        if (StringUtils.isNotEmpty(userCode)){
            TpaGlobalAgentDTO.TpaRequestData tpaRequestData = tpaGlobalAgentDTO.createTpaRequestData();
            tpaRequestData.setSupplierTypeCode("01");
            tpaRequestData.setExternalOperator(userCode);
            tpaGlobalAgentDTO.setRequestData(tpaRequestData);
        }
        tpaGlobalAgentDTO.setRequestTime(""+System.currentTimeMillis());
        tpaGlobalAgentDTO.setRequestId(MDC.get(BaseConstant.REQUEST_ID));
        tpaGlobalAgentDTO.setRequestType("FST-008");
        tpaGlobalAgentDTO.setCompanyId("tpa-dev");
        LogUtil.audit(JsonUtils.toJsonString(tpaGlobalAgentDTO));
        String result = null;
//        String  result = "{\n" +
//                "    \"requestType\": \"FST-008\",\n" +
//                "    \"requestId\": \"7AA6A7CCFA1E488DA258D77BB6D4E56B\",\n" +
//                "    \"responseTime\": \"20240614110458001\",\n" +
//                "    \"companyId\": \"tpa-dev\",\n" +
//                "    \"resultCode\": \"000000\",\n" +
//                "    \"resultMsg\": \"消息处理成功\",\n" +
//                "    \"responseData\": {\n" +
//                "        \"supplierInfoList\": [\n" +
//                "            {\n" +
//                "                \"supplierCode\": \"code1\",\n" +
//                "                \"supplierName\": \"北京环球医疗救援有限责任公司111\",\n" +
//                "                \"supplierTypeCode\": \"01\",\n" +
//                "                \"customId\": \"C0001973932\",\n" +
//                "                \"supplierProvinceCode\": \"\",\n" +
//                "                \"supplierCityCode\": \"\",\n" +
//                "                \"supplierNameCode\": \"\",\n" +
//                "                \"supplierDistrictType\": \"统一社会信用证号码\",\n" +
//                "                \"supplierOrganizeCode\": \"91110105801393983L\",\n" +
//                "                \"supplierMobile\": \"\",\n" +
//                "                \"accName\": \"北京环球医疗救援有限责任公司\",\n" +
//                "                \"accNo\": \"*****************\",\n" +
//                "                \"bankCode\": \"\",\n" +
//                "                \"bankName\": \"农业银行\",\n" +
//                "                \"subBranch\": \"中国农业银行股份有限公司北京双井支行\",\n" +
//                "                \"accTypeCode\": \"\",\n" +
//                "                \"provinceCode\": \"\",\n" +
//                "                \"cityCode\": \"\",\n" +
//                "                \"regionCode\": \"\",\n" +
//                "                \"collectPayApproach\": \"\"\n" +
//                "            },\n" +
//                "           {\n" +
//                "                \"supplierCode\": \"code2\",\n" +
//                "                \"supplierName\": \"北京环球医疗救援有限责任公司222\",\n" +
//                "                \"supplierTypeCode\": \"01\",\n" +
//                "                \"customId\": \"C0001973932\",\n" +
//                "                \"supplierProvinceCode\": \"\",\n" +
//                "                \"supplierCityCode\": \"\",\n" +
//                "                \"supplierNameCode\": \"\",\n" +
//                "                \"supplierDistrictType\": \"统一社会信用证号码\",\n" +
//                "                \"supplierOrganizeCode\": \"91110105801393983L\",\n" +
//                "                \"supplierMobile\": \"\",\n" +
//                "                \"accName\": \"北京环球医疗救援有限责任公司\",\n" +
//                "                \"accNo\": \"*****************\",\n" +
//                "                \"bankCode\": \"\",\n" +
//                "                \"bankName\": \"农业银行\",\n" +
//                "                \"subBranch\": \"中国农业银行股份有限公司北京双井支行\",\n" +
//                "                \"accTypeCode\": \"\",\n" +
//                "                \"provinceCode\": \"\",\n" +
//                "                \"cityCode\": \"\",\n" +
//                "                \"regionCode\": \"\",\n" +
//                "                \"collectPayApproach\": \"\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "      \n" +
//                "    }\n" +
//                "}";
       result = tpaGlobalRequest.getExternalDepartmentList(tpaGlobalAgentDTO);
      Map<String, Object> map = JsonUtils.jsonToMap(result);
      if(StringUtils.isEmptyStr(result)){
          return ResponseResult.fail(FileUploadConstants.FAIL_CODE,"返回结果为空请确认！");
      }
        if("000000".equals(String.valueOf(map.get("resultCode")))){
          String responseData = JsonUtils.toJsonString(map.get("responseData"));
          TpaSupplierInfoListVO tpaSupplierInfoListVO = JsonUtils.toObject(responseData, TpaSupplierInfoListVO.class);
          return ResponseResult.success(tpaSupplierInfoListVO);
      }else{
          return ResponseResult.fail(FileUploadConstants.FAIL_CODE,String.valueOf(map.get("resultMsg")));
      }

    }

    /**
     * 根据报案号查询出出险人的证件号和保单号， 通过证件号和保单号查询所有理赔案件中存在在途调查任务的案件 报案号集合。
     *
     * @param reportNo
     * @param caseTimes
     */
    @Override
    public List<String> getUnfinishedInvestigateOther(String reportNo, Integer caseTimes) {
        List<Integer> unfinishedStatusList = Arrays.asList(1,
                InvestigateConstants.AHCS_INVESTIGATE_STATUS_PROCESSING,
                InvestigateConstants.AHCS_INVESTIGATE_STATUS_AUDIT);
        List<InvestigateDTO> investigateDTOList = investigateDao.getInvestigateRecordByPolicyClient(reportNo, caseTimes, unfinishedStatusList);
        List<String> reportNoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(investigateDTOList)) {
            reportNoList = investigateDTOList.stream()
                    .map(InvestigateDTO::getReportNo)
                    .distinct().collect(Collectors.toList());
        }

        return reportNoList;
    }

    /**
     * 根据报案号查询出险人证件号；再通过证件号查询客户所有保单出险的理赔案件中存在调查结论异常的 报案号集合（没有保单条件，客户的所有理赔案件）
     *
     * @param reportNo
     */
    @Override
    public List<String> getAbnormalInvestigate(String reportNo) {
        ReportCustomerInfoEntity reportCustomerInfoEntity = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
        String clientNo = reportCustomerInfoEntity.getClientNo();
        List<String> reportNoList = new ArrayList<>();
        if(StringUtils.isNotEmpty(clientNo)){
            List<InvestigateDTO> investigateDTOList =
                    investigateDao.getAbnormalInvestigateByClientNo(clientNo);

            if (CollectionUtils.isNotEmpty(investigateDTOList)) {
                reportNoList = investigateDTOList.stream()
                        .map(InvestigateDTO::getReportNo)
                        .distinct().collect(Collectors.toList());
            }
        }
        return reportNoList;
    }

    @Override
    public ResponseResult<Object> getServerInfoList() {
        TpaGlobalAgentDTO tpaGlobalAgentDTO = new TpaGlobalAgentDTO();
        if(tpaGlobalAgentDTO.getRequestData() == null){
            TpaGlobalAgentDTO.TpaRequestData tpaRequestData = tpaGlobalAgentDTO.createTpaRequestData();
//            LogUtil.audit("服务类型代码："+supplierTypeCode);
            tpaRequestData.setServerTypeCode("2");
            tpaGlobalAgentDTO.setRequestData(tpaRequestData);
        }
        tpaGlobalAgentDTO.setRequestTime(""+System.currentTimeMillis());
        tpaGlobalAgentDTO.setRequestId(MDC.get(BaseConstant.REQUEST_ID));
        tpaGlobalAgentDTO.setRequestType("FST-002");
        tpaGlobalAgentDTO.setCompanyId("tpa-dev");
        LogUtil.audit(JsonUtils.toJsonString(tpaGlobalAgentDTO));
        String result = null;
//        String  result = "{\n" +
//                "    \"requestType\": \"FST-008\",\n" +
//                "    \"requestId\": \"7AA6A7CCFA1E488DA258D77BB6D4E56B\",\n" +
//                "    \"responseTime\": \"20240614110458001\",\n" +
//                "    \"companyId\": \"tpa-dev\",\n" +
//                "    \"resultCode\": \"000000\",\n" +
//                "    \"resultMsg\": \"消息处理成功\",\n" +
//                "    \"responseData\": {\n" +
//                "        \"supplierInfoList\": [\n" +
//                "            {\n" +
//                "                \"supplierCode\": \"code1\",\n" +
//                "                \"supplierName\": \"北京环球医疗救援有限责任公司111\",\n" +
//                "                \"supplierTypeCode\": \"01\",\n" +
//                "                \"customId\": \"C0001973932\",\n" +
//                "                \"supplierProvinceCode\": \"\",\n" +
//                "                \"supplierCityCode\": \"\",\n" +
//                "                \"supplierNameCode\": \"\",\n" +
//                "                \"supplierDistrictType\": \"统一社会信用证号码\",\n" +
//                "                \"supplierOrganizeCode\": \"91110105801393983L\",\n" +
//                "                \"supplierMobile\": \"\",\n" +
//                "                \"accName\": \"北京环球医疗救援有限责任公司\",\n" +
//                "                \"accNo\": \"*****************\",\n" +
//                "                \"bankCode\": \"\",\n" +
//                "                \"bankName\": \"农业银行\",\n" +
//                "                \"subBranch\": \"中国农业银行股份有限公司北京双井支行\",\n" +
//                "                \"accTypeCode\": \"\",\n" +
//                "                \"provinceCode\": \"\",\n" +
//                "                \"cityCode\": \"\",\n" +
//                "                \"regionCode\": \"\",\n" +
//                "                \"collectPayApproach\": \"\"\n" +
//                "            },\n" +
//                "           {\n" +
//                "                \"supplierCode\": \"code2\",\n" +
//                "                \"supplierName\": \"北京环球医疗救援有限责任公司222\",\n" +
//                "                \"supplierTypeCode\": \"01\",\n" +
//                "                \"customId\": \"C0001973932\",\n" +
//                "                \"supplierProvinceCode\": \"\",\n" +
//                "                \"supplierCityCode\": \"\",\n" +
//                "                \"supplierNameCode\": \"\",\n" +
//                "                \"supplierDistrictType\": \"统一社会信用证号码\",\n" +
//                "                \"supplierOrganizeCode\": \"91110105801393983L\",\n" +
//                "                \"supplierMobile\": \"\",\n" +
//                "                \"accName\": \"北京环球医疗救援有限责任公司\",\n" +
//                "                \"accNo\": \"*****************\",\n" +
//                "                \"bankCode\": \"\",\n" +
//                "                \"bankName\": \"农业银行\",\n" +
//                "                \"subBranch\": \"中国农业银行股份有限公司北京双井支行\",\n" +
//                "                \"accTypeCode\": \"\",\n" +
//                "                \"provinceCode\": \"\",\n" +
//                "                \"cityCode\": \"\",\n" +
//                "                \"regionCode\": \"\",\n" +
//                "                \"collectPayApproach\": \"\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "      \n" +
//                "    }\n" +
//                "}";
        result = tpaGlobalRequest.getExternalDepartmentList(tpaGlobalAgentDTO);
        Map<String, Object> map = JsonUtils.jsonToMap(result);
        if(StringUtils.isEmptyStr(result)){
            return ResponseResult.fail(FileUploadConstants.FAIL_CODE,"返回结果为空请确认！");
        }
        if("000000".equals(String.valueOf(map.get("resultCode")))){
            String responseData = JsonUtils.toJsonString(map.get("responseData"));
            TpaServerInfoListVO tpaServerInfoListVO = JsonUtils.toObject(responseData, TpaServerInfoListVO.class);
            return ResponseResult.success(tpaServerInfoListVO);
        }else{
            return ResponseResult.fail(FileUploadConstants.FAIL_CODE,String.valueOf(map.get("resultMsg")));
        }
    }
}