package com.paic.ncbs.claim.dao.mapper.copypolicy;

import com.paic.ncbs.claim.dao.entity.copypolicy.ClmGlobalReturnInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * global信息主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface ClmGlobalReturnInfoMapper extends BaseMapper<ClmGlobalReturnInfo> {

    ClmGlobalReturnInfo selectByReportNo(@Param("reportNo") String reportNo);

    void updateByReportNo(ClmGlobalReturnInfo clmGlobalReturnInfo);
}
