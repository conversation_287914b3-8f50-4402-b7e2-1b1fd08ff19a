package com.paic.ncbs.claim.service.noPeopleHurt.impl;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsAllowanceInfoMapper;
import com.paic.ncbs.claim.dao.entity.clms.ClmsAllowanceInfo;
import com.paic.ncbs.claim.service.noPeopleHurt.ClmsAllowanceInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 给付津贴信息表(ClmsAllowanceInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:45
 */
@Service("clmsAllowanceInfoService")
public class ClmsAllowanceInfoServiceImpl implements ClmsAllowanceInfoService {
    @Resource
    private ClmsAllowanceInfoMapper clmsAllowanceInfoMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsAllowanceInfo queryById(String id) {
        return this.clmsAllowanceInfoMapper.queryById(id);
    }

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public List<ClmsAllowanceInfo> queryByReportNo(String reportNo, int caseTimes) {
        return this.clmsAllowanceInfoMapper.queryByReportNo(reportNo, caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsAllowanceInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsAllowanceInfo insert(ClmsAllowanceInfo clmsAllowanceInfo) {
        clmsAllowanceInfo.setCreatedBy(WebServletContext.getUserId());
        clmsAllowanceInfo.setCreatedDate(new Date());
        clmsAllowanceInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsAllowanceInfo.setUpdatedDate(new Date());
        clmsAllowanceInfo.setId(UuidUtil.getUUID());
        this.clmsAllowanceInfoMapper.insert(clmsAllowanceInfo);
        return clmsAllowanceInfo;
    }

    /**
     * 修改数据
     *
     * @param clmsAllowanceInfo 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsAllowanceInfo update(ClmsAllowanceInfo clmsAllowanceInfo) {
        clmsAllowanceInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsAllowanceInfo.setUpdatedDate(new Date());
        this.clmsAllowanceInfoMapper.update(clmsAllowanceInfo);
        return this.queryById(clmsAllowanceInfo.getId());
    }


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsAllowanceInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void deleteByCondition(String reportNo, int caseTimes) {
        clmsAllowanceInfoMapper.deleteByCondition(reportNo, caseTimes);
    }
}
