package com.paic.ncbs.claim.exception;

import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import lombok.Getter;

@Getter
public class GlobalBusinessException extends RuntimeException {

    private static final long serialVersionUID = -9048554643128518808L;

    private String code;

    private String message;

    public GlobalBusinessException(String errorMsg) {
        super(errorMsg);
        this.code = GlobalResultStatus.FAIL.getCode();
        this.message = errorMsg;
    }

    public GlobalBusinessException(GlobalResultStatus resultStatus) {
        super(resultStatus.getMsg());
        this.code = resultStatus.getCode();
        this.message = resultStatus.getMsg();
    }

    public GlobalBusinessException(String errorCode, String errorMsg) {
        super(errorMsg);
        this.code = errorCode;
        this.message = errorMsg;
    }

    public GlobalBusinessException(String errorCode, Throwable cause) {
        super(cause);
        this.code = errorCode;
        this.message = cause.getMessage();
    }

    public GlobalBusinessException(String errorCode, String... msgParams) {
        super(msgParams.toString());
        this.code = errorCode;
    }
}
