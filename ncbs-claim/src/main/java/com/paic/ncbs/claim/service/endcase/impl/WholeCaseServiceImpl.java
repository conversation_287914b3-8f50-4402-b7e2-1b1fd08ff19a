package com.paic.ncbs.claim.service.endcase.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.base.Joiner;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.enums.RelationType;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateChangeMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.BatchMapper;
import com.paic.ncbs.claim.dao.mapper.settle.EndorsementMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.SettleBatchMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.HugeAccidentInfoDTO;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsDomainDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.duty.SurveyDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.model.dto.ocas.PolicyFilesInfoVO;
import com.paic.ncbs.claim.model.dto.ocas.ProductInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyHistoryDTO;
import com.paic.ncbs.claim.model.dto.report.*;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.SettleBatchInfoDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.endcase.*;
import com.paic.ncbs.claim.model.vo.policy.PolicyHistoryRequestVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyHistoryVO;
import com.paic.ncbs.claim.model.vo.report.HistoryCaseVO;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import com.paic.ncbs.claim.model.vo.settle.SttleBatchInfoVO;
import com.paic.ncbs.claim.model.vo.user.DepartmentUserVO;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.endcase.*;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.report.*;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.sum;


@Service("wholeCaseService")
@RefreshScope
public class WholeCaseServiceImpl implements WholeCaseService {

    @Autowired
    private WholeCaseMapper wholeCaseMapper;

    @Autowired
    private PersonAccidentMapper personAccidentMapper;

    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private CommonParameterService commonParameterService;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private ReportService reportService;

    @Autowired
    private ReportInfoService reportInfoService;

    @Autowired
    private AcceptRecordService acceptRecordService;

    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private CaseClassService caseClassService;
    @Autowired
    private CaseClassMapper caseClassDao;

    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private SettleBatchMapper settleBatchMapper;

    @Autowired
    private EndorsementMapper endorsementMapper;
    @Autowired
    private DepartmentDefineService departmentDefineService;
    @Autowired
    private ClmsEstimateRecordService clmsEstimateRecordService;
    @Autowired
    private EstimateChangeMapper estimateChangeMapper;
    @Autowired
    private EstimateService estimateService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private HugeAccidentInfoMapper hugeAccidentInfoMapper;

    @Autowired
    private TaskListService taskListService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private PolicyService policyService;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    @Autowired
    private LinkManService linkManService;
    @Autowired
    private DepartmentUserMapper departmentUserMapper;

    @Value("${switch.investigateFlag:N}")
    private String investigateFlag;
    @Value("${aiModel.productPackage}")
    private String productPackage;

    @Override
    public WholeCasePageResult getHistoryCaseList(WholeCaseVO wholeCaseVO) throws GlobalBusinessException {
        LogUtil.audit("查询历史案件信息入参={}", JSONObject.toJSONString(wholeCaseVO));

        long startTime = System.currentTimeMillis();

//        if (StringUtils.isNotEmpty(wholeCaseVO.getCaseNo())) {
//            getReportListByCaseNo(wholeCaseVO);
//        }
        String departmentCode = WebServletContext.getDepartmentCode();
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            departmentCode = departmentUserVO.getDepartmentCode();
        }
        // 包含下级机构
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(departmentCode);
        wholeCaseVO.setDepartmentCodes(departmentCodes);
//        getCaseWithCondition(wholeCaseVO);
        if (System.currentTimeMillis() - startTime >= 1500) {
            LogUtil.audit("查询历史案件信息耗时A=" + (System.currentTimeMillis() - startTime) + "人参:" + JSONObject.toJSONString(wholeCaseVO));
        }
        startTime = System.currentTimeMillis();
        WholeCasePageResult pageResult = this.getHistoryCaseNew(wholeCaseVO);

        if (System.currentTimeMillis() - startTime >= 2000) {
            LogUtil.audit("查询历史案件信息耗时A=" + (System.currentTimeMillis() - startTime) + "入参:" + JSONObject.toJSONString(wholeCaseVO));
        }
        startTime = System.currentTimeMillis();
        if (null != pageResult && ListUtils.isNotEmpty(pageResult.getList())) {
            for (WholeCaseVO wholeCase : pageResult.getList()) {
                List<ClmsEstimateRecord> records = clmsEstimateRecordService.getRecordByReportNoAndType(wholeCase.getReportNo(), String.valueOf(wholeCase.getCaseTimes()), null);
                if(CollectionUtils.isNotEmpty(records)){
                    wholeCase.setEstimateAmount(records.get(0).getEstimateAmount());
                }
                BigDecimal changeAmt = estimateChangeMapper.getEstimateChangeAmount(wholeCase.getReportNo(),wholeCase.getCaseTimes());
                if(changeAmt != null){
                    //有修正金额，替换为修正金额
                    wholeCase.setEstimateAmount(changeAmt);
                }

                if (ConfigConstValues.PROCESS_STATUS_CASE_CLOSED_NAME.equals(wholeCase.getCaseStatusName())) {
                    BigDecimal sumPayFee = policyPayService.getSumPayFee(wholeCase.getReportNo(), wholeCase.getCaseTimes());
                    wholeCase.setEndCaseAmount(sumPayFee);
                    LogUtil.audit(wholeCase.getReportNo() + "历史案件~设置结案金额" + wholeCase.getEndCaseAmount());
                }
                WholeCaseVO wv = wholeCaseMapper.getWhleCaseVoByReportNo(wholeCase);
                if (wv == null) {
                    LogUtil.info("WholeCaseVO is null:{}", wholeCase.getReportNo());
                    continue;
                }
                wholeCase.setEndCaseFlag(wv.getEndCaseFlag());

                handleWholeCaseVO(wholeCase, wv.getIndemnityConclusion(), wv.getIndemnityModel());

                wholeCase.setAssigner(taskInfoMapper.getAssigner(wholeCase.getReportNo(),wholeCase.getCaseTimes()));
            }
        }

        long endTime = System.currentTimeMillis();
        if (endTime - startTime >= 2000) {
            LogUtil.audit("查询历史案件信息耗时C=" + (endTime - startTime) + "人参:" + JSONObject.toJSONString(wholeCaseVO));
        }

        return pageResult;
    }

    /**
     * report/getHistoryCase
     *
     * @title
     * @description
     */
    public WholeCasePageResult getHistoryCase(WholeCaseVO wholeCase) {
        Pager pager = new Pager();
        pager.setPageIndex(wholeCase.getCurrentPage());
        pager.setPageRows(wholeCase.getPerPageSize());

        HistoryReportAgrsDTO dto = new HistoryReportAgrsDTO();
        BeanUtils.copyProperties(wholeCase, dto);
        try {
            if (wholeCase.getBirthday() != null){
                dto.setBirthday(DateUtils.parseToFormatString(wholeCase.getBirthday(), DateUtils.SIMPLE_DATE_STR));
            }
        } catch (Exception e) {
            LogUtil.error("日期格式转换异常,异常信息", e);
        }

        List<HistoryCaseDTO> reportInfos = new ArrayList<>();
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        PageHelper.orderBy("reportDate desc");

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getCertificateNo())) {
            reportInfos = reportInfoService.getHistoryReportByCertificateNo(dto.getCertificateNo(), dto.getSpecialCaseType());
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getReportNo())) {
            reportInfos = reportInfoService.getHistoryCaseByReportNo(dto.getReportNo(), dto.getSpecialCaseType());
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getPolicyNo())) {
            reportInfos = reportInfoService.getHistoryReportByPolicyNoAndName(dto.getPolicyNo(), dto.getName(), dto.getSpecialCaseType());
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getElectronicNo())) {
            try {
                reportInfos = reportInfoService.getHistoryReportByElectronicNoNew(dto.getElectronicNo());
            } catch (Exception e) {
                LogUtil.error("根据电子保单号查询历史案件信息发生异常！");
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getBirthday()) && org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getName())) {
            reportInfos = reportInfoService.getHistoryReportByBirthdayAndName(dto.getBirthday(), dto.getName());
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getTelephoneNo())) {
            String[] s = dto.getTelephoneNo().split("-");
            String callNo = s[s.length - 1];
            reportInfos = reportInfoService.getHistoryReportByTelephoneNo(callNo);
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getReportDateBegin()) && org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getReportDateEnd())
                && org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getName())) {
            reportInfos = reportInfoService.getHistoryReportByReportDateAndName(dto);
        }
        if (RapeCheckUtil.isNotEmpty(dto.getCaseList())) {
            reportInfos = reportInfoService.getHistoryReportByDateAndDepartmentCode(dto);
        }

        if (RapeCheckUtil.isNotEmpty(dto.getReportBatchNo())) {
            reportInfos = reportInfoService.getHistoryCaseByReportBatchNo(dto.getReportBatchNo());
        }

        List<HistoryCaseVO> historyCaseList = transformVO(reportInfos);
        PageInfo<HistoryCaseDTO> pageInfo = new PageInfo<>(reportInfos);
        WholeCasePage wholeCasePage = new WholeCasePage();
        wholeCasePage.setPageIndex(pager.getPageIndex());
        wholeCasePage.setPageRows(pager.getPageRows());
        wholeCasePage.setCurrPageRows(pager.getPageRows());
        wholeCasePage.setDefaultPageRows(20);
        wholeCasePage.setTotalPages(pageInfo.getPages());
        wholeCasePage.setHasNextPage(pageInfo.isHasNextPage());
        wholeCasePage.setHasPrevPage(pageInfo.isHasPreviousPage());
        wholeCasePage.setTotalRows((int) pageInfo.getTotal());
        PageMethod.clearPage();
        List<WholeCaseVO> list = new ArrayList<>();
        historyCaseList.forEach(temp -> {
            WholeCaseVO vo = new WholeCaseVO();
            BeanUtils.copyProperties(temp, vo);
            //caseTimes在copyProperties两个参数对象中的类型不一致
            vo.setCaseTimes(Integer.valueOf(temp.getCaseTimes()));
            list.add(vo);
        });
        WholeCasePageResult pageResult = new WholeCasePageResult();
        pageResult.setPager(wholeCasePage);
//        pageResult.setPager(list, pager.getPageIndex(), pager.getPageRows());
        pageResult.setList(list);
        return pageResult;
    }

    public WholeCasePageResult getHistoryCaseNew(WholeCaseVO wholeCase) {
        Pager pager = new Pager();
        pager.setPageIndex(wholeCase.getCurrentPage());
        pager.setPageRows(wholeCase.getPerPageSize());

        HistoryReportAgrsDTO dto = new HistoryReportAgrsDTO();
        BeanUtils.copyProperties(wholeCase, dto);
        try {
            if (wholeCase.getBirthday() != null){
                dto.setBirthday(DateUtils.parseToFormatString(wholeCase.getBirthday(), DateUtils.SIMPLE_DATE_STR));
            }
        } catch (Exception e) {
            LogUtil.error("日期格式转换异常,异常信息", e);
        }

        List<HistoryCaseDTO> reportInfos = new ArrayList<>();
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        PageHelper.orderBy("reportDate desc");
        reportInfos = reportInfoService.getHistoryCaseNew(wholeCase);
        for(HistoryCaseDTO historyCaseDTO: reportInfos){
            try {
                historyCaseDTO.setCaseInputDuration(reportInfoMapper.getCaseInputDuration(historyCaseDTO.getReportNo()));
                historyCaseDTO.setIsAIModel(reportInfoMapper.isAIModel(historyCaseDTO.getReportNo()));
            } catch (Exception e) {
                LogUtil.error("案件查询-案件录入时长查询失败,异常信息", e);
            }

        }


        List<HistoryCaseVO> historyCaseList = transformVO(reportInfos);
        PageInfo<HistoryCaseDTO> pageInfo = new PageInfo<>(reportInfos);
        WholeCasePage wholeCasePage = new WholeCasePage();
        wholeCasePage.setPageIndex(pager.getPageIndex());
        wholeCasePage.setPageRows(pager.getPageRows());
        wholeCasePage.setCurrPageRows(pager.getPageRows());
        wholeCasePage.setDefaultPageRows(20);
        wholeCasePage.setTotalPages(pageInfo.getPages());
        wholeCasePage.setHasNextPage(pageInfo.isHasNextPage());
        wholeCasePage.setHasPrevPage(pageInfo.isHasPreviousPage());
        wholeCasePage.setTotalRows((int) pageInfo.getTotal());
        PageMethod.clearPage();
        List<WholeCaseVO> list = new ArrayList<>();
        historyCaseList.forEach(temp -> {
            WholeCaseVO vo = new WholeCaseVO();
            BeanUtils.copyProperties(temp, vo);
            //caseTimes在copyProperties两个参数对象中的类型不一致
            vo.setCaseTimes(Integer.valueOf(temp.getCaseTimes()));
            list.add(vo);
        });
        WholeCasePageResult pageResult = new WholeCasePageResult();
        pageResult.setPager(wholeCasePage);
//        pageResult.setPager(list, pager.getPageIndex(), pager.getPageRows());
        pageResult.setList(list);
        return pageResult;
    }
    private List<HistoryCaseVO> transformVO(List<HistoryCaseDTO> historyCaseDTOs) {
        List<HistoryCaseVO> historyCaseList = new ArrayList<HistoryCaseVO>();
        if (RapeCheckUtil.isListNotEmpty(historyCaseDTOs)) {
            for (HistoryCaseDTO dto : historyCaseDTOs) {
                HistoryCaseVO historyCaseVO = new HistoryCaseVO();
                BeanUtils.copyProperties(dto, historyCaseVO);
                CaseProcessDTO caseProcess = new CaseProcessDTO();
                caseProcess.setReportNo(dto.getReportNo());
                caseProcess.setCaseTimes(Integer.valueOf(dto.getCaseTimes()));
                CaseProcessDTO caseProcessDTO = caseProcessService.getProcessStatusAndEndAmount(caseProcess);
                if (caseProcessDTO!=null){
                    historyCaseVO.setCaseStatusName(caseProcessDTO.getProcessStatusName());
                    historyCaseVO.setEndCaseAmount(caseProcessDTO.getEndCaseAmount());
                    historyCaseVO.setEstimateAmount(caseProcessDTO.getEstimateAmount());
                    historyCaseVO.setEndCaseDate(caseProcessDTO.getEndCaseDate());
                    historyCaseVO.setDepartmentCode(caseProcessDTO.getDepartmentCode());
                    historyCaseVO.setDepartmentAbbrName(caseProcessDTO.getDepartmentAbbrName());
                    historyCaseList.add(historyCaseVO);
                }
            }
        }
        return historyCaseList;
    }

    private void handleWholeCaseVO(WholeCaseVO wholeCaseVO, String indemnityConclusion, String indemnityModel) {
        if (ConfigConstValues.INDEMNITYCONCLUSION_PAY.equals(indemnityConclusion)) {
            if (ConfigConstValues.INDEMNITYMODEL_PROTOCOL.equals(indemnityModel)) {
                wholeCaseVO.setIndemnityConclusion(ConstValues.CONCLUSION_PROTOCOL_VERIFY_PAY);
                wholeCaseVO.setDocType(ConfigConstValues.PRINT_DOCTYPE_PROTOCOL_PAY);
            } else if (ConfigConstValues.INDEMNITYMODEL_ACCOMMODATION.equals(indemnityModel)) {
                wholeCaseVO.setIndemnityConclusion(ConstValues.CONCLUSION_ACCOMMODATION_VERIFY_PAY);
                wholeCaseVO.setDocType(ConfigConstValues.PRINT_DOCTYPE_ACCOMMODATION_PAY);
            } else {
                wholeCaseVO.setIndemnityConclusion(indemnityConclusion);
                wholeCaseVO.setDocType(ConfigConstValues.PRINT_DOCTYPE_FORMAL_PAY);
            }
        } else {
            wholeCaseVO.setIndemnityConclusion(indemnityConclusion);
            if (ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED.equals(indemnityConclusion)) {
                wholeCaseVO.setDocType(ConfigConstValues.PRINT_DOCTYPE_ZERO_END);
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_CANCEL.equals(indemnityConclusion)) {
                wholeCaseVO.setDocType(ConfigConstValues.PRINT_DOCTYPE_CANCEL_END);
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_REFUSE.equals(indemnityConclusion)) {
                wholeCaseVO.setDocType(ConfigConstValues.PRINT_DOCTYPE_REFUSE_PAY);
            } else {
            }
        }
    }

    public SttleBatchInfoVO getSettleAmountsSum(String reportNo, Integer caseTimes) {
        BatchDTO batchDTO = batchMapper.getBatch(reportNo, caseTimes);
        SettleBatchInfoDTO sttleBatchInfoDTO = settleBatchMapper.getSettleAmountsSum(reportNo, caseTimes);
        SttleBatchInfoVO sttleBatchInfoVO = new SttleBatchInfoVO();
        String mode = "1";
        if (sttleBatchInfoDTO == null) {
            sttleBatchInfoDTO = new SettleBatchInfoDTO();
            LogUtil.audit("报案号:{},赔付次数:{},查询赔付数据为空,无法计算赔付金额合计信息", reportNo, caseTimes);
            String temp = "";
            if ("4".equals(mode)) {
                EndorsementDTO endorsement = endorsementMapper.selectByReportNoAndCaseTime(reportNo, caseTimes);
                if (null != endorsement) {
                    temp = endorsement.getEndorsementRemark();
                }
                sttleBatchInfoDTO.setPreFeeAmount(BigDecimal.ZERO);
                sttleBatchInfoDTO.setPreFeeAmount(BigDecimal.ZERO);
                sttleBatchInfoDTO.setPolicyFee(BigDecimal.ZERO);
                sttleBatchInfoDTO.setPolicyPayAmount(BigDecimal.ZERO);
                sttleBatchInfoDTO.setOriginalAmount(BigDecimal.ZERO);
                sttleBatchInfoDTO.setFinalPayAmount(BigDecimal.ZERO);
                sttleBatchInfoDTO.setFinalFee(BigDecimal.ZERO);
                sttleBatchInfoVO.setOriginalAmount(BigDecimal.ZERO);
                sttleBatchInfoVO.setPolicyPayTotal(BigDecimal.ZERO);
                sttleBatchInfoVO.setFinalPay(BigDecimal.ZERO);
                sttleBatchInfoVO.setPrePay(BigDecimal.ZERO);

            }
            sttleBatchInfoVO.setIndemnityMode(mode);
            sttleBatchInfoVO.setEndorseTemplate(temp);
            sttleBatchInfoVO.setSettleBatchInfoDTO(sttleBatchInfoDTO);
            return sttleBatchInfoVO;
        }

        sttleBatchInfoVO.setBatchDTO(batchDTO);
        sttleBatchInfoVO.setPolicyPayTotal(sum(sttleBatchInfoDTO.getPolicyPayAmount(), sttleBatchInfoDTO.getPolicyFee()));
        sttleBatchInfoVO.setFinalPay(sum(sttleBatchInfoDTO.getFinalPayAmount(), sttleBatchInfoDTO.getFinalFee()));
        sttleBatchInfoVO.setPrePay(sum(sttleBatchInfoDTO.getPrePayAmount(), sttleBatchInfoDTO.getPreFeeAmount()));
        sttleBatchInfoVO.setPolicyPayTotalNoFee(sttleBatchInfoDTO.getPolicyPayAmount());
        sttleBatchInfoVO.setFinalPayNoFee(sttleBatchInfoDTO.getFinalPayAmount());
        sttleBatchInfoVO.setPrePayNoFee(sttleBatchInfoDTO.getPrePayAmount());
        SettleBatchInfoDTO proAndAccInfo = settleBatchMapper.getAcmAndProAmountSum(reportNo, caseTimes);
        if (null == proAndAccInfo) {
            proAndAccInfo = new SettleBatchInfoDTO();
        }
        sttleBatchInfoVO.setProtocolAmount(proAndAccInfo.getProtocolAmount());
        sttleBatchInfoVO.setAccommodationAmount(proAndAccInfo.getAccommodationAmount());

        sttleBatchInfoVO.setIndemnityMode(mode);
        BigDecimal originalAmount = BigDecimal.ZERO;
        if (SettleConst.INDEMNITY_MODE_ACCOMMODATE.equals(sttleBatchInfoVO.getIndemnityMode())) {
            BigDecimal accommodationAmount = nvl(proAndAccInfo.getAccommodationAmount(), 0);
            originalAmount = sttleBatchInfoDTO.getPolicyPayAmount().subtract(accommodationAmount);
            sttleBatchInfoDTO.setAccommodationAmount(accommodationAmount);
        } else if (SettleConst.INDEMNITY_MODE_PROTOCOL.equals(sttleBatchInfoVO.getIndemnityMode())) {
            BigDecimal protocolAmount = nvl(proAndAccInfo.getProtocolAmount(), 0);
            originalAmount = sttleBatchInfoDTO.getPolicyPayAmount().subtract(protocolAmount);
            sttleBatchInfoDTO.setProtocolAmount(protocolAmount);
        } else {
            originalAmount = sttleBatchInfoDTO.getPolicyPayAmount();
        }
        sttleBatchInfoVO.setOriginalAmount(originalAmount);
        sttleBatchInfoDTO.setOriginalAmount(originalAmount);
        sttleBatchInfoVO.setSettleBatchInfoDTO(sttleBatchInfoDTO);

        if (StringUtils.isEmptyStr(mode) || mode.equals("1")) {
            return sttleBatchInfoVO;
        }

        String temp = "";
        if ("4".equals(mode)) {
            EndorsementDTO endorsement = endorsementMapper.selectByReportNoAndCaseTime(reportNo, caseTimes);
            if (null != endorsement) {
                temp = endorsement.getEndorsementRemark();
            }
        } else {
            temp = endorsementMapper.getEndorseTemplate(sttleBatchInfoVO.getIndemnityMode());
        }
        LogUtil.audit(mode + "批单文案模板查询结果：" + temp);
        sttleBatchInfoVO.setEndorseTemplate(temp);
        return sttleBatchInfoVO;
    }


    @Override
    public void getCaseWithCondition(WholeCaseVO wholeCaseVO) throws GlobalBusinessException {

        if (StringUtils.isEmptyStr(wholeCaseVO.getDepartmentCode())) {
            return;
        }
        //List<DepartmentDTO> departments = bpmInvokeMng.queryChildDepartments(wholeCaseVO.getDepartmentCode());
        List<DepartmentDTO> departments = departmentDefineService.queryChildDepartments(wholeCaseVO.getDepartmentCode());
        if (ListUtils.isEmptyList(departments)) {
            LogUtil.audit("#历史案件查询,机构为空#");
            return;
        }

        if (wholeCaseVO.getEndCaseDateBegin() != null && wholeCaseVO.getEndCaseDateEnd() != null) {
            try {

                if (DateUtils.getDaysBetween(wholeCaseVO.getEndCaseDateBegin(), wholeCaseVO.getEndCaseDateEnd()) > 7) {
                    LogUtil.audit("查询的时间超过7天,不做查询");
                    return;
                }
                String endCaseDateBegin = DateUtils.parseToFormatString(wholeCaseVO.getEndCaseDateBegin(), "yyyy-MM-dd") + " 00:00:00";
                String endCaseDateEnd = DateUtils.parseToFormatString(wholeCaseVO.getEndCaseDateEnd(), "yyyy-MM-dd") + " 23:59:59";

                wholeCaseVO.setEndCaseDateBegin(DateUtils.parse2Date(endCaseDateBegin));
                wholeCaseVO.setEndCaseDateEnd(DateUtils.parse2Date(endCaseDateEnd));
                LogUtil.audit("结案开始时间：%s, 结案结束时间=%s", endCaseDateBegin, endCaseDateEnd);
            } catch (Exception e) {
                LogUtil.error("#以结案时间查询案件,时间转换异常#", e);
            }
        }

        String reportDateBegin = "";
        String reportDateEnd = "";
        if (StringUtils.isNotEmpty(wholeCaseVO.getBeginReportDate()) && StringUtils.isNotEmpty(wholeCaseVO.getEndReportDate())) {
            try {
                reportDateBegin = wholeCaseVO.getBeginReportDate();
                reportDateEnd = wholeCaseVO.getEndReportDate();

                if (DateUtils.getDaysBetween(DateUtils.parse2Date(reportDateBegin), DateUtils.parse2Date(reportDateEnd)) > 7) {
                    LogUtil.audit("查询的时间超过7天,不做查询");
                    return;
                }
                reportDateBegin = reportDateBegin + " 00:00:00";
                reportDateEnd = reportDateEnd + " 23:59:59";
                LogUtil.audit("报案开始时间：%s, 报案结束时间=%s", reportDateBegin, reportDateEnd);
            } catch (Exception e) {
                LogUtil.error("#以报案时间查询案件,时间转换异常#", e);
            }
        }

        LogUtil.audit("机构个数=%s", departments.size());

        List<CaseParamVO> caseList = new ArrayList<>();
        for (int i = 0; i <= departments.size() / 100; i++) {
            List<DepartmentDTO> subDeptList = departments.subList(i * 100, (i * 100) + 99 < departments.size() ? (i * 100) + 99 : departments.size());
            if (ListUtils.isEmptyList(subDeptList)) {


                LogUtil.audit("subDeptList为空, i=%s", i);
                continue;
            }
            List<CaseParamVO> tempList = wholeCaseMapper.getCaseWithCondition(reportDateBegin, reportDateEnd, wholeCaseVO.getEndCaseDateBegin(),
                    wholeCaseVO.getEndCaseDateEnd(), subDeptList);
            if (ListUtils.isNotEmpty(tempList)) {
                caseList.addAll(tempList);
            }
        }

        wholeCaseVO.setCaseList(caseList);
        LogUtil.audit("#统计机构案件列表=%s#", caseList.size());
        if (StringUtils.isEmptyStr(wholeCaseVO.getPayStatus()) || ListUtils.isEmptyList(caseList)) {
            return;
        }
        List<CaseParamVO> tempCaseList = new ArrayList<>();
        List<CaseParamVO> resultList = new ArrayList<>();

        for (int i = 0; i <= caseList.size() / 100; i++) {
            tempCaseList = caseList.subList(i * 100, (i * 100) + 99 < caseList.size() ? (i * 100) + 99 : caseList.size());
            if (ListUtils.isEmptyList(tempCaseList)) {
                LogUtil.audit("tempCaseList为空, i=%s", i);
                continue;
            }
            wholeCaseVO.setCaseList(tempCaseList);
            List<CaseParamVO> casePayStatusList = null;
            if (ListUtils.isNotEmpty(casePayStatusList)) {
                resultList.addAll(casePayStatusList);
            }
        }
        if (ListUtils.isEmptyList(resultList)) {
            wholeCaseVO.setCaseList(resultList);
            return;
        }


        List<CaseParamVO> suitPayStatusCaseList = resultList.stream()
                .filter(caseItem -> wholeCaseVO.getPayStatus().equals(caseItem.getPayStatus()))
                .collect(Collectors.toList());
        LogUtil.audit("#过滤掉支付后的案件列表=%s#", suitPayStatusCaseList.size());
        wholeCaseVO.setCaseList(suitPayStatusCaseList);
        resultList.clear();
    }

    private void getReportListByCaseNo(WholeCaseVO wholeCaseVO) throws GlobalBusinessException {
        wholeCaseVO.setCaseList(wholeCaseMapper.getReportListByCaseNo(wholeCaseVO.getCaseNo()));
    }

    @Override
    public void modifyDocumentStatus(String reportNo, Integer caseTimes, String status, String userId) {
        wholeCaseMapper.modifyDocumentStatus(reportNo, caseTimes, status, userId);
    }

    @Override
    public WholeCaseTimeVO getWholeCaseTime(String reportNo, Integer caseTimes) throws GlobalBusinessException {

        WholeCaseTimeVO wholeCaseTimeVO = new WholeCaseTimeVO();

        SurveyDTO surveyDTO = getSurvey(reportNo, caseTimes, TacheConstants.TEL_SURVEY);

        AcceptRecordDTO acceptRecordDTO = acceptRecordService.getAcceptRecordByAsc(reportNo, 1);
        if (null != acceptRecordDTO) {
            wholeCaseTimeVO.setClaimDate(acceptRecordDTO.getClaimantApplyDate());
        }

        if (this.isTurnToNormalCase(reportNo, caseTimes)) {
            if (null != acceptRecordDTO) {
                wholeCaseTimeVO.setOneTimeNotificationDate(acceptRecordDTO.getClaimantApplyDate());
            }
        } else if (Objects.nonNull(surveyDTO)) {
            wholeCaseTimeVO.setOneTimeNotificationDate(surveyDTO.getSurveyDate());
        } else {
            LogUtil.audit("#根据报案号和赔付次数查整案相关时间信息#该案件没有进行过电话查勘,reportNo=%s", reportNo);
            surveyDTO = getSurvey(reportNo, caseTimes, TacheConstants.FIELD_SURVEY);
            if (Objects.nonNull(surveyDTO)) {
                wholeCaseTimeVO.setOneTimeNotificationDate(surveyDTO.getSurveyDate());
            } else {
                LogUtil.audit("#根据报案号和赔付次数查整案相关时间信息#该案件也没有进行过电话查勘与实地查勘,reportNo=%s", reportNo);
                if (null != acceptRecordDTO) {
                    wholeCaseTimeVO.setOneTimeNotificationDate(acceptRecordDTO.getClaimantApplyDate());
                }
            }
        }

        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
        if (Objects.nonNull(wholeCaseBaseDTO)) {

            wholeCaseTimeVO.setRegisterDate(wholeCaseBaseDTO.getRegisterDate());

            wholeCaseTimeVO.setDocumentCompletedDate(wholeCaseBaseDTO.getDocumentFullDate());

            wholeCaseTimeVO.setEndCaseDate(wholeCaseBaseDTO.getEndCaseDate());
        }


            LogUtil.audit("#根据报案号和赔付次数查整案相关时间信息#该案件没无单证上传任务,reportNo=%s", reportNo);

            CaseProcessDTO caseProcess = caseProcessService.getCaseProcessDTO(reportNo, caseTimes);
            boolean flag = null != wholeCaseTimeVO.getRegisterDate() && null != caseProcess &&
                    !ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT.equals(caseProcess.getProcessStatus());
            if (flag) {
                wholeCaseTimeVO.setDocumentUploadedDate(wholeCaseTimeVO.getRegisterDate());
            }


        if (!DateUtils.compareTimeBetweenDate(wholeCaseTimeVO.getOneTimeNotificationDate(), wholeCaseTimeVO.getDocumentCompletedDate())) {
            wholeCaseTimeVO.setOneTimeNotificationDate(wholeCaseTimeVO.getDocumentCompletedDate());
        }

//        CaseProcessDTO caseProcess = caseProcessService.getCaseProcessDTO(reportNo, caseTimes);
        if (Objects.nonNull(caseProcess)) {
            wholeCaseTimeVO.setCaseStatus(caseProcess.getProcessStatus());
            wholeCaseTimeVO.setCaseStatusName(caseProcess.getProcessStatusName());
            if (ConfigConstValues.PROCESS_STATUS_WAIT_CONFIRM.equals(caseProcess.getProcessStatus())) {
                wholeCaseTimeVO.setCaseStatus(ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT);
                wholeCaseTimeVO.setCaseStatusName(ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT_NAME);
            }
            wholeCaseTimeVO.setCompanyCode(caseProcess.getCompanyCode());
            wholeCaseTimeVO.setCompanyName(caseProcess.getCompanyName());
            wholeCaseTimeVO.setPrivilegeGroupName(caseProcess.getPrivilegeGroupName());
        }

        return wholeCaseTimeVO;
    }

    private SurveyDTO getSurvey(String reportNo, Integer caseTimes, String taskId) {
        return new SurveyDTO();
    }

    @Override
    public ReportBaseInfoResData getReportBaseInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(reportNo)) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "报案号");
        }
        ReportBaseInfoResData baseInfoResData = reportInfoService.requestReportBaseInfo(reportNo);

        AhcsDomainDTO ahcsDomainDTO = reportService.requestReportDomainInfoNoCache(reportNo);

        List<ReportInfoExEntity> reportInfoExList = ahcsDomainDTO.getReportInfoExs();

        EstimatePolicyFormDTO estimateDataByTache = estimateService.getEstimateDataByTache(reportNo, caseTimes, EstimateUtil.ESTIMATE_TYPE_REGISTRATION, null);
        if (null != estimateDataByTache &&  null != estimateDataByTache.getEstimatePolicyList() && !CollectionUtils.isEmpty(estimateDataByTache.getEstimatePolicyList())){
            List<EstimatePolicyDTO> estimatePolicyList = estimateDataByTache.getEstimatePolicyList();
            List<String> policys =estimatePolicyList.stream().map(EstimatePolicyDTO :: getPolicyNo).distinct().collect(Collectors.toList());
            if (policys.size() > 0){
                baseInfoResData.setPolicyNo(Joiner.on(",").join(policys));
            }
        }

        // 案件重开后联系人信息会复制多条展示，上面的baseInfoResData中的LinkManList仅通过报案号查询，这里修正重新按照报案号+赔付次数查询 start
        List<LinkManEntity> linkMans = linkManService.getLinkMans(reportNo,caseTimes); // 重新获取 联系人 列表
        if (RapeCheckUtil.isListNotEmpty(linkMans)) {
            List<LinkManDTO> linkManDTOs = new ArrayList<>();
            for (LinkManEntity l : linkMans) {
                LinkManDTO linkman = new LinkManDTO();
                linkman.setLinkManName(l.getLinkManName());
                linkman.setLinkManRelation(l.getLinkManRelation());
                linkman.setLinkManRelationName(RelationType.getName(l.getLinkManRelation()));
                linkman.setApplicantPerson(l.getApplicantPerson());
                linkman.setApplicantType(l.getApplicantType());
                linkman.setCertificateType(l.getCertificateType());
                linkman.setCertificateNo(l.getCertificateNo());
                linkman.setLinkManTelephone(l.getLinkManTelephone());
                linkman.setSendMessage(l.getSendMessage());
                linkman.setIsReport(l.getIsReport());
                linkManDTOs.add(linkman);
            }
            baseInfoResData.setLinkManList(linkManDTOs);// 重新赋值对象
        }else {
            baseInfoResData.setLinkManList(Collections.emptyList());
        }
        // 案件重开后联系人信息会复制多条展示，上面的baseInfoResData中的LinkManList仅通过报案号查询，这里修正重新按照报案号+赔付次数查询 end

        // 信息脱敏
        if (ListUtils.isNotEmpty(reportInfoExList) && !ConstValues.NO.equals(reportInfoExList.get(0).getSuccorService())) {
            baseInfoResData.setSuccorNo(reportInfoExList.get(0).getSuccorNo());
            baseInfoResData.setCertificateNo(DesensitizeUtil.around(baseInfoResData.getCertificateNo(), 4, 4));
            baseInfoResData.setReporterName(DesensitizeUtil.left(baseInfoResData.getReporterName(), 1));
            List<LinkManDTO> linkManList = baseInfoResData.getLinkManList();
            LogUtil.audit("#客户信息中查询报联络人信息：{}", linkManList);
            for (LinkManDTO link : linkManList) {
                link.setLinkManName(DesensitizeUtil.left(link.getLinkManName(), 1));
            }
        }

        if (null != caseTimes) {
            getHugeAccidentInfo(reportNo, caseTimes, baseInfoResData);
        }
        // 人伤 -- 案件出险类型取最新的
        List<String> caseSubClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, null);
        if (CollectionUtils.isNotEmpty(caseSubClassList)) {
            List<String> caseSubClassName = new ArrayList<>();
            caseSubClassList.forEach(e -> caseSubClassName.add(InsuredApplyTypeEnum.getName(e)));
            String accidentTypeName ="" ;
            if(caseSubClassName.size() == 1){
                accidentTypeName = caseSubClassName.get(0);
            }else{
                accidentTypeName = Joiner.on(",").skipNulls().join(caseSubClassName);
            }
            baseInfoResData.setAccidentTypeName(accidentTypeName);
        }
        // 人伤 --事故地点联动查询
        PersonAccidentDTO accidentDTO = personAccidentMapper.getPersonAccidentByReportNo(reportNo, null, caseTimes);
        if (accidentDTO != null) {
            if ("1".equals(baseInfoResData.getOverseasOccur()) && StringUtils.isEmptyStr(accidentDTO.getAccidentNation()) ){
                accidentDTO.setAccidentNation(baseInfoResData.getAccidentNation());
            }
            String accidentPlace = getAccidentPlace(accidentDTO);
            baseInfoResData.setAccidentPlace(accidentPlace);
        }
        String currStatus = caseProcessService.getCaseProcessStatus(reportNo, caseTimes);
        baseInfoResData.setAbnormalCaseStatus(currStatus);
        baseInfoResData.setAbnormalCaseStatusDesc(CaseProcessStatus.getName(currStatus));

        try {
            baseInfoResData.setDisplayTargets(riskPropertyService.displayRiskProperty(reportNo,null));
            if (baseInfoResData.isDisplayTargets()) {
                List<CaseBaseDTO> caseBaseList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
                CaseBaseDTO caseBase = caseBaseList.get(0);
                baseInfoResData.setRiskGroupNo(caseBase.getRiskGroupNo());
                baseInfoResData.setRiskGroupName(caseBase.getRiskGroupName());
                ProductInfoDTO prudocutInfo = null;
                String policyNo = caseBase.getPolicyNo();
                if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                    //用保单号查询clms_policy_info表 distinct 只返回一条的标的和产品大类编码
                    prudocutInfo = policyInfoMapper.getPrudocutInfo(policyNo);
                }else{
                    prudocutInfo = ocasMapper.getPrudocutInfo(policyNo);
                }
                if (null != prudocutInfo) {
                    baseInfoResData.setTargetType(prudocutInfo.getTargetType());
                }
            }
        }catch (Exception e){
            LogUtil.info("判断标的类型失败,不影响原有流程",e);
        }

        boolean coinsureCase = coinsureService.isCoinsureCase(reportNo);
        boolean isMainCoinsureFlag = coinsureService.isMainCoinsureFlag(reportNo);
        baseInfoResData.setIsCoinsureCase(coinsureCase ? BaseConstant.STRING_1 : BaseConstant.STRING_0);
        baseInfoResData.setIsMainCoinsureFlag(isMainCoinsureFlag ? BaseConstant.STRING_1 : BaseConstant.STRING_0);
        ReportInfoExEntity reportInfoExEntity = reportInfoExMapper.getAcceptanceNoByReportNo(reportNo);
        if(StringUtils.isNotEmpty(reportInfoExEntity.getAcceptanceNumber())){
            baseInfoResData.setAcceptanceNo(reportInfoExEntity.getAcceptanceNumber());
        }
        //联共保标志：0非联共保，1主共保，2从共保
        List<CoinsureDTO> coinsureDTOList= coinsureService.getCoinsureByPolicyNo(baseInfoResData.getPolicyNo());
        if (!CollectionUtils.isEmpty(coinsureDTOList)){
            String coinsInd = "2".equals(coinsureDTOList.get(0).getCoinsuranceType()) ? "2" : "1";
            baseInfoResData.setCoinsInd(coinsInd);
        } else {
            baseInfoResData.setCoinsInd("0");
        }

        String businessType = "";
        try {
            //赔付率
            //查询案件个团属性 P-个人 G-团体
            String policyNo = "";
            BigDecimal sumEstimateFee = BigDecimal.ZERO;
            BigDecimal sumEstimatePayment = BigDecimal.ZERO;
            BigDecimal sumFee = BigDecimal.ZERO;
            BigDecimal sumPayment = BigDecimal.ZERO;
            BigDecimal lossRatio = BigDecimal.ZERO;
            BigDecimal sumAmount = BigDecimal.ZERO;
            List<PaymentItemDTO> feeItemList = new ArrayList<>();
            List<PaymentItemDTO> paymentItemList = new ArrayList<>();
            List<String> policyNoList = ahcsPolicyInfoMapper.getPolicyNoByReportNo(reportNo);
            if(CollectionUtils.isNotEmpty(policyNoList)){
                policyNo = policyNoList.get(0);
            }
            //查询保单的个团属性：P-个人，G-团体
            AhcsPolicyInfoEntity policyEntity = new AhcsPolicyInfoEntity();
            policyEntity.setPolicyNo(policyNo);
            List<AhcsPolicyInfoEntity> policyInfoList = ahcsPolicyInfoMapper.getList(policyEntity);
            if(CollectionUtils.isNotEmpty(policyInfoList)){
                businessType = policyInfoList.get(0).getBusinessType();
                baseInfoResData.setIsFamily(policyInfoList.get(0).getIsClaimFamily());
            }
            baseInfoResData.setBusinessType(businessType);
            if("G".equals(businessType)){
            /*赔付率计算逻辑:
                赔付率 = 保单所有案件：（未决费用 + 未决赔款 + 已决费用 + 已决赔款）
                    1、原案件：
                        未结案 = 未决费用 + 未决赔款
                        已结案 = 已决费用 + 已决赔款
                    2、重开案件：案件所有费用总和（原案件+重开案件）+ 最新一次案件重开：（未决费用 + 未决赔款 + 已决费用 + 已决赔款）
                        最新一次案件重开：
                            未结案 = 未决费用 + 未决赔款
                            已结案 = 已决费用 + 已决赔款
            */
                PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
                paymentItemDTO.setPolicyNo(policyNo);
                //保单下最新案件的费用
                List<PaymentItemDTO> newFeeList = paymentItemMapper.getAllFeeByPolicyNo(paymentItemDTO);
                //保单下最新案件的未决费用
                List<PaymentItemDTO> newEstimateFeeList = paymentItemMapper.getNewEstimateFeeByPolicyNo(paymentItemDTO);
                //保单下最新案件的赔款
                List<PaymentItemDTO> newPaymentList = paymentItemMapper.getNewPaymentByPolicyNo(paymentItemDTO);
                //保单下最新案件的未决赔款
                List<PaymentItemDTO> newEstimatePaymentList = paymentItemMapper.getNewEstimatePaymentByPolicyNo(paymentItemDTO);
                //案件费用集合
                if(CollectionUtils.isNotEmpty(newFeeList)){
                    feeItemList.addAll(newFeeList);
                }
                if(CollectionUtils.isNotEmpty(newEstimateFeeList)){
                    feeItemList.addAll(newEstimateFeeList);
                }
                //案件赔款集合
                if(CollectionUtils.isNotEmpty(newPaymentList)){
                    paymentItemList.addAll(newPaymentList);
                }
                if(CollectionUtils.isNotEmpty(newEstimatePaymentList)){
                    paymentItemList.addAll(newEstimatePaymentList);
                }
                if(CollectionUtils.isNotEmpty(feeItemList)){
                    //未决费用
                    sumEstimateFee = feeItemList.stream()
                            .filter(item -> "1".equals(item.getWholeCaseStatus()))
                            .map(item -> item.getPaymentAmount() != null ? item.getPaymentAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    //已决费用
                    sumFee = feeItemList.stream()
                            .filter(item -> "0".equals(item.getWholeCaseStatus()))
                            .map(item -> item.getPaymentAmount() != null ? item.getPaymentAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                if(CollectionUtils.isNotEmpty(paymentItemList)){
                    //未决赔款
                    sumEstimatePayment = paymentItemList.stream()
                            .filter(item -> "1".equals(item.getWholeCaseStatus()))
                            .map(item -> item.getPaymentAmount() != null ? item.getPaymentAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    //已决赔款
                    sumPayment = paymentItemList.stream()
                            .filter(item -> "0".equals(item.getWholeCaseStatus()))
                            .map(item -> item.getPaymentAmount() != null ? item.getPaymentAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                PolicyQueryVO queryVO = new PolicyQueryVO();
                queryVO.setPolicyNo(policyNo);
                BigDecimal totalAgreePremium = policyService.getPremiumByPolicyNo(queryVO);
                sumAmount = sumEstimateFee.add(sumEstimatePayment).add(sumFee).add(sumPayment);//总金额（赔款+费用）
                lossRatio  = sumAmount.divide(totalAgreePremium, 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"))
                                .setScale(2, RoundingMode.HALF_UP);
                baseInfoResData.setLossRatio(lossRatio + "%"
                        + " 计算公式：赔付率 = 保单所有案件：（未决费用" + sumEstimateFee
                        + " + 未决赔款" + sumEstimatePayment
                        + " + 已决费用" + sumFee
                        + " + 已决赔款" + sumPayment
                        + " ）/保单最新保费" + totalAgreePremium + "*100%"
                        + "=" + lossRatio + "%");
            }
        }catch (Exception e){
            LogUtil.error("计算赔付率失败,不影响原有流程",e);
        }
        baseInfoResData.setInvestigateFlag(investigateFlag);

        return baseInfoResData;
    }

    /**
     *  获取人伤 出险地址
     * @param accidentDTO
     * @return
     */
    private String getAccidentPlace(PersonAccidentDTO accidentDTO) {
        String accidentPlace;
        AdressSearchDto adressSearchDto  = new AdressSearchDto();
        if ("0".equals(accidentDTO.getOverseasOccur())){
            adressSearchDto.setOverseasOccur(accidentDTO.getOverseasOccur()).setAccidentProvinceCode(accidentDTO.getProvinceCode())
                    .setAccidentCountyCode(accidentDTO.getAccidentCountyCode()).setAccidentCityCode(accidentDTO.getAccidentCityCode());
        }else {
            adressSearchDto.setOverseasOccur(accidentDTO.getOverseasOccur()).setAccidentAreaCode(accidentDTO.getAccidentArea())
                    .setAccidentNationCode(accidentDTO.getAccidentNation());
        }
        AdressSearchDto detailAdressFormCode = commonParameterService.getDetailAdressFormCode(adressSearchDto);

        if ("0".equals(accidentDTO.getOverseasOccur())){
            accidentPlace = detailAdressFormCode.getAccidentProvinceName() + detailAdressFormCode.getAccidentCityName()
                    + detailAdressFormCode.getAccidentCountyName() +
                    (StringUtils.isEmptyStr(accidentDTO.getAccidentPlace()) ? "":accidentDTO.getAccidentPlace());
        }else{
            accidentPlace = detailAdressFormCode.getAccidentAreaName() +
                    (StringUtils.isEmptyStr(detailAdressFormCode.getAccidentNationName()) ?"":detailAdressFormCode.getAccidentNationName())+
                    (StringUtils.isEmptyStr(accidentDTO.getAccidentPlace()) ? "":accidentDTO.getAccidentPlace());
        }
        return accidentPlace;
    }


    private void getHugeAccidentInfo(String reportNo, Integer caseTimes, ReportBaseInfoResData baseInfoResData) {
        WholeCaseBaseDTO wholeCaseBase = wholeCaseBaseService.getHugeInfo(reportNo, caseTimes);
        String hugeAccident = "";
        if(null != wholeCaseBase){
            if (Objects.equals(wholeCaseBase.getIsHugeAccident(), "N")) {
                hugeAccident = "否";
            } else if (Objects.equals(wholeCaseBase.getIsHugeAccident(), "Y")) {
                hugeAccident = "是";
            }

            // 重灾名称
            if (StringUtils.isNotEmpty(wholeCaseBase.getHugeAccidentCode())) {
                HugeAccidentInfoDTO queryDTO = new HugeAccidentInfoDTO();
                queryDTO.setAccidentCode(wholeCaseBase.getHugeAccidentCode());
                HugeAccidentInfoDTO accidentInfo = hugeAccidentInfoMapper.queryOneByCondition(queryDTO);
                if (Objects.nonNull(accidentInfo)) {
                    baseInfoResData.setHugeAccidentName(accidentInfo.getAccidentName());
                }
            }
        }
        baseInfoResData.setHugeAccident(hugeAccident);
    }

    @Override
    public boolean isTurnToNormalCase(String reportNo, Integer caseTimes) throws GlobalBusinessException {

        AhcsDomainDTO jsonObject = reportService.requestReportDomainInfoNoCache(reportNo);

        return jsonObject != null && null != jsonObject.getReportExc();
    }

    @Override
    public String getOnlineCaseType(String reportNo, Integer caseTimes) {
        boolean importantCase = caseClassService.importantCase(reportNo, caseTimes);
        if (importantCase) {
            return "2";
        }
        return "1";
    }

    @Override
    public List<TrackInfoVO> getTrackInfoList(String reportNo, Integer caseTimes) {

        List<TrackInfoVO> trackInfoList = new ArrayList<>();
        List<EstimatePolicyDTO> policyListByHistory = estimatePolicyMapper.getEstimatePolicyByHistoryList(reportNo, caseTimes);
        List<EstimateDutyHistoryDTO> dutyByEstimateList = new ArrayList<>();
        List<EstimateDutyHistoryDTO> dutyByRegistrationList = new ArrayList<>();
        List<EstimateDutyHistoryDTO> dutyByTrackEstimateList = new ArrayList<>();
        Map<String, BigDecimal> policyClaimPercentMap = new HashMap<String, BigDecimal>();
        for (EstimatePolicyDTO policy : policyListByHistory) {
            if (policyClaimPercentMap.get(policy.getCaseNo()) == null) {
                policyClaimPercentMap.put(policy.getCaseNo(), policy.getCoShare());
            }
            for (EstimatePlanDTO plan : policy.getEstimatePlanList()) {
                for (EstimateDutyHistoryDTO dutyHistory : plan.getEstimateDutyHistoryList()) {
                    if (EstimateUtil.ESTIMATE_TYPE_ESTIMATE.equals(dutyHistory.getEstimateType())) {
                        dutyByEstimateList.add(dutyHistory);
                    } else if (EstimateUtil.ESTIMATE_TYPE_REGISTRATION.equals(dutyHistory.getEstimateType())) {
                        dutyByRegistrationList.add(dutyHistory);
                    } else if (EstimateUtil.ESTIMATE_TYPE_TRACK.equals(dutyHistory.getEstimateType())) {
                        dutyByTrackEstimateList.add(dutyHistory);
                    } else {
                    }
                }
            }
        }
        List<EstimatePolicyDTO> policyList = estimatePolicyMapper.getEstimatePolicyByHistoryList(reportNo, caseTimes);
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
        if (ListUtils.isEmptyList(dutyByEstimateList)) {
            LogUtil.audit("#历史案件查询 未决信息#还没有进行预估,没有数据可以返回");
            return trackInfoList;
        }
        if (ListUtils.isNotEmpty(dutyByEstimateList) && ListUtils.isEmptyList(dutyByRegistrationList)) {
            LogUtil.audit("#历史案件查询 未决信息#还没有进行立案,返回预估数据");
            trackInfoList.add(getEstimateTrackInfoByPolicy(policyList));
            return trackInfoList;
        }
        if (ListUtils.isNotEmpty(dutyByRegistrationList) && ListUtils.isEmptyList(dutyByTrackEstimateList)) {
            LogUtil.audit("#历史案件查询 未决信息#有进行立案,没有进行未决跟踪，返回立案与预估数据");
            trackInfoList.add(getEstimateTrackInfo(dutyByEstimateList, policyClaimPercentMap));
            TrackInfoVO trackInfoVO = getRegisterTrackInfoByPolicy(policyList);
            trackInfoVO.setGenerationTime(wholeCaseBaseDTO.getRegisterDate());
            trackInfoList.add(trackInfoVO);
            trackInfoList.add(getSettleTrackInfo(reportNo, caseTimes));
            return trackInfoList;
        }
        BatchDTO batch = batchMapper.getBatch(reportNo, caseTimes);
        if (ListUtils.isNotEmpty(dutyByTrackEstimateList) && null == batch) {
            LogUtil.audit("#历史案件查询 未决信息#有进行未决跟踪还没有理算，返回立案与预估数据");
            trackInfoList.add(getEstimateTrackInfo(dutyByEstimateList, policyClaimPercentMap));
            TrackInfoVO trackInfoVO = getRegisterTrack(dutyByRegistrationList, policyClaimPercentMap);
            trackInfoVO.setGenerationTime(wholeCaseBaseDTO.getRegisterDate());
            trackInfoList.add(trackInfoVO);
            trackInfoList.add(getTrackTrackInfo(policyList));
            return trackInfoList;
        }
        LogUtil.audit("#历史案件查询 未决信息#进行了未决跟踪，返回未决、立案与预估数据");
        trackInfoList.add(getEstimateTrackInfo(dutyByEstimateList, policyClaimPercentMap));
        TrackInfoVO trackInfoVO = getRegisterTrack(dutyByRegistrationList, policyClaimPercentMap);
        trackInfoVO.setGenerationTime(wholeCaseBaseDTO.getRegisterDate());
        trackInfoList.add(trackInfoVO);
        trackInfoList.add(getTrackTrackInfo(policyList));
        trackInfoList.add(getSettleTrackInfo(reportNo, caseTimes));
        return trackInfoList;
    }

    @Override
    public String getCaseTypeByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        return wholeCaseMapper.getCaseTypeByReportNo(reportNo, caseTimes);
    }

    @Override
    public Integer getCustomerAccidentCount(String clientNo) {

        return wholeCaseMapper.getCustomerAccidentCount(clientNo);

    }

    private TrackInfoVO getRegisterTrack(List<EstimateDutyHistoryDTO> dutyByEstimateList, Map<String, BigDecimal> policyClaimPercentMap) {
        TrackInfoVO trackInfoByEstimate = new TrackInfoVO();
        trackInfoByEstimate.setDataSources(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        trackInfoByEstimate.setDataSourcesName(EstimateUtil.ESTIMATE_TYPE_REGISTRATION_NAME);
        trackInfoByEstimate.setGenerationTime(dutyByEstimateList.get(0).getUpdatedDate());
        String um = dutyByEstimateList.get(0).getUpdatedBy();
        if (ConstValues.SYSTEM_UM.equals(um)) {
            trackInfoByEstimate.setPersonLiable(um + "_" + um);
        } else {
            String name = userInfoService.getUserNameById(um);
            trackInfoByEstimate.setPersonLiable(name + "_" + um);
        }
        trackInfoByEstimate.setAmountSum(EstimateUtil.getEstimateAmountSumByDutyHistory(dutyByEstimateList, policyClaimPercentMap));
        return trackInfoByEstimate;
    }

    private TrackInfoVO getRegisterTrackInfoByPolicy(List<EstimatePolicyDTO> policyList) {
        TrackInfoVO trackInfoByRegistration = new TrackInfoVO();
        trackInfoByRegistration.setDataSources(EstimateUtil.ESTIMATE_TYPE_REGISTRATION);
        trackInfoByRegistration.setDataSourcesName(EstimateUtil.ESTIMATE_TYPE_REGISTRATION_NAME);
        trackInfoByRegistration.setGenerationTime(policyList.get(0).getUpdatedDate());
        String um = policyList.get(0).getUpdatedBy();
        if (ConstValues.SYSTEM_UM.equals(um)) {
            trackInfoByRegistration.setPersonLiable(um + "_" + um);
        } else {
            String name = userInfoService.getUserNameById(um);
            trackInfoByRegistration.setPersonLiable(name + "_" + um);
        }
        trackInfoByRegistration.setAmountSum(EstimateUtil.getEstimateAmountSum(policyList));
        return trackInfoByRegistration;
    }

    private TrackInfoVO getSettleTrackInfo(String reportNo, Integer caseTimes) {
        TrackInfoVO trackInfoByCheckLoss = new TrackInfoVO();

        CaseProcessDTO caseProcess = caseProcessService.getCaseProcessDTO(reportNo, caseTimes);
        if (ConfigConstValues.PROCESS_STATUS_CASE_CLOSED.equals(caseProcess.getProcessStatus())) {
            LogUtil.audit("#历史案件查询 未决信息#案件已结案，返回理算、未决、立案与预估数据");


            trackInfoByCheckLoss.setDataSources(EstimateUtil.ESTIMATE_TYPE_CHECKLOSS);
            trackInfoByCheckLoss.setDataSourcesName(EstimateUtil.ESTIMATE_TYPE_CHECKLOSS_NAME);
            BatchDTO batch = batchMapper.getBatch(reportNo, caseTimes);
            if (null != batch) {
                trackInfoByCheckLoss.setGenerationTime(batch.getSettleTime());
                String um = batch.getSettleUserUm();
                if (ConstValues.SYSTEM_UM.equals(um)) {
                    trackInfoByCheckLoss.setPersonLiable(um + "_" + um);
                } else {
                    String name = userInfoService.getUserNameById(um);
                    trackInfoByCheckLoss.setPersonLiable(name + "_" + um);
                }
            }
            SttleBatchInfoVO sbi = getSettleAmountsSum(reportNo, caseTimes);
            if (null != sbi) {
                trackInfoByCheckLoss.setAmountSum(sbi.getFinalPay());
            }
        }
        return trackInfoByCheckLoss;
    }

    private TrackInfoVO getTrackTrackInfo(List<EstimatePolicyDTO> policyList) {
        TrackInfoVO trackInfoByTrack = new TrackInfoVO();
        trackInfoByTrack.setDataSources(EstimateUtil.ESTIMATE_TYPE_TRACK);
        trackInfoByTrack.setDataSourcesName(EstimateUtil.ESTIMATE_TYPE_TRACK_NAME);
        trackInfoByTrack.setGenerationTime(policyList.get(0).getUpdatedDate());
        String um = policyList.get(0).getUpdatedBy();
        if (ConstValues.SYSTEM_UM.equals(um)) {
            trackInfoByTrack.setPersonLiable(um + "_" + um);
        } else {
            String name = userInfoService.getUserNameById(um);
            trackInfoByTrack.setPersonLiable(name + "_" + um);
        }
        trackInfoByTrack.setAmountSum(EstimateUtil.getEstimateAmountSum(policyList));
        return trackInfoByTrack;
    }

    private TrackInfoVO getEstimateTrackInfo(List<EstimateDutyHistoryDTO> dutyByEstimateList, Map<String, BigDecimal> policyClaimPercentMap) {
        TrackInfoVO trackInfoByEstimate = new TrackInfoVO();
        trackInfoByEstimate.setDataSources(EstimateUtil.ESTIMATE_TYPE_ESTIMATE);
        trackInfoByEstimate.setDataSourcesName(EstimateUtil.ESTIMATE_TYPE_ESTIMATE_NAME);
        trackInfoByEstimate.setGenerationTime(dutyByEstimateList.get(0).getUpdatedDate());
        String um = dutyByEstimateList.get(0).getUpdatedBy();
        if (ConstValues.SYSTEM_UM.equals(um)) {
            trackInfoByEstimate.setPersonLiable(um + "_" + um);
        } else {
            String name = userInfoService.getUserNameById(um);
            trackInfoByEstimate.setPersonLiable(name + "_" + um);
        }
        trackInfoByEstimate.setAmountSum(EstimateUtil.getEstimateAmountSumByDutyHistory(dutyByEstimateList, policyClaimPercentMap));
        return trackInfoByEstimate;
    }

    private TrackInfoVO getEstimateTrackInfoByPolicy(List<EstimatePolicyDTO> policyList) {
        TrackInfoVO trackInfoByEstimate = new TrackInfoVO();
        trackInfoByEstimate.setDataSources(EstimateUtil.ESTIMATE_TYPE_ESTIMATE);
        trackInfoByEstimate.setDataSourcesName(EstimateUtil.ESTIMATE_TYPE_ESTIMATE_NAME);
        trackInfoByEstimate.setGenerationTime(policyList.get(0).getUpdatedDate());
        String um = policyList.get(0).getUpdatedBy();
        if (ConstValues.SYSTEM_UM.equals(um)) {
            trackInfoByEstimate.setPersonLiable(um + "_" + um);
        } else {
            String name = userInfoService.getUserNameById(um);
            trackInfoByEstimate.setPersonLiable(name + "_" + um);
        }
        trackInfoByEstimate.setAmountSum(EstimateUtil.getEstimateAmountSum(policyList));
        return trackInfoByEstimate;
    }


    @Override
    public PolicyHistoryVO getPolicyHistory(PolicyHistoryRequestVO requestVO) throws GlobalBusinessException {
        PolicyHistoryVO policyHistoryVO = new PolicyHistoryVO();
        String clientNo = requestVO.getClientNo();
        String policyNo = requestVO.getPolicyNo();
        String policyCerNo = requestVO.getPolicyCerNo();
        String reportNo = requestVO.getReportNo();
        if (StringUtils.isEmptyStr(clientNo) && StringUtils.isEmptyStr(reportNo)) {
            LogUtil.audit("#查询保单记录传入参数为空,查询保单信息为空,查询参数为:{}", requestVO);
            return policyHistoryVO;
        }
        if (StringUtils.isEmptyStr(clientNo) && !StringUtils.isEmptyStr(reportNo)) {
            clientNo = wholeCaseMapper.getClientNo(reportNo, policyNo);
        }

        if (StringUtils.isEmptyStr(clientNo)) {
            LogUtil.audit("#查询保单记录传入参数为空,查询保单信息为空,查询参数为:{}", requestVO);
            return policyHistoryVO;
        }
        List<PolicyHistoryDTO> allPolicyHistorys = wholeCaseMapper.getPolicyHistory(clientNo);
        if (CollectionUtils.isEmpty(allPolicyHistorys)) {
            return policyHistoryVO;
        }
        for (PolicyHistoryDTO policyHistory : allPolicyHistorys) {
            String processStatus = policyHistory.getProcessStatus();
            if (ConfigConstValues.PROCESS_STATUS_END_SET.contains(processStatus)) {
                if (null == policyHistory.getPolicySumPay()) {
                    policyHistory.setPolicySumPay(BigDecimal.ZERO);
                }
            } else {
                policyHistory.setPolicySumPay(null);
            }
        }
        List<PolicyHistoryDTO> policyHistorys = new ArrayList<>();
        List<PolicyHistoryDTO> otherPolicyHistorys = new ArrayList<>();
        for (PolicyHistoryDTO policyHistory : allPolicyHistorys) {
            if (StringUtils.isEqualStr(policyHistory.getPolicyNo(), policyNo) && StringUtils.isEqualStr(policyHistory.getPolicyCerNo(), policyCerNo)) {
                policyHistorys.add(policyHistory);
                continue;
            }
            otherPolicyHistorys.add(policyHistory);
        }
        policyHistoryVO.setPolicyHistorys(policyHistorys);
        policyHistoryVO.setOtherPolicyHistorys(otherPolicyHistorys);
        return policyHistoryVO;
    }

    @Override
    public ResponseResult<Object> getPendingManagementQueryList(WholeCaseVO queryVO) {
        ResponseResult<Object> responseResult = new ResponseResult<>();

        WholeCasePageResult result = getHistoryCaseList(queryVO);
        if (CollectionUtils.isEmpty(result.getList())) {
            responseResult.setCode(GlobalResultStatus.FAIL.getCode());
            responseResult.setMsg("未查询到相关信息，请核实");
            return responseResult;
        }
        List<WholeCaseVO> list = result.getList();

        List<WholeCaseVO> tempList = list.stream().filter(wholeCaseVO -> !"0".equals(wholeCaseVO.getEndCaseFlag())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tempList)) {
            responseResult.setCode(GlobalResultStatus.FAIL.getCode());
            responseResult.setMsg("已结案案件无法修正，请核实");
            return responseResult;
        }
        List<WholeCaseVO> resultList = tempList.stream().filter(wholeCaseVO -> "Y".equals(wholeCaseVO.getIsRegister())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultList)) {
            responseResult.setCode(GlobalResultStatus.FAIL.getCode());
            responseResult.setMsg("未立案案件无法修正，请核实");
            return responseResult;
        }
        result.setList(resultList);

        responseResult.setCode(GlobalResultStatus.SUCCESS.getCode());
        responseResult.setData(result);
        return responseResult;
    }

    @Override
    public AiModelStatisticsVO getAiModelStatistics() {
        AiModelStatisticsVO aiModelStatisticsVO = new AiModelStatisticsVO();
        List<String> list = Arrays.asList(productPackage.split(","));
        Integer allCaseCount = reportInfoMapper.getAllCaseCount(list);
        Integer identifyCaseCount = reportInfoMapper.getIdentifyCaseCount(list);
        aiModelStatisticsVO.setIdentifyCaseCount(identifyCaseCount);
        aiModelStatisticsVO.setReportCaseCount(reportInfoMapper.getReportCaseCount(list));
        aiModelStatisticsVO.setInRecognitionCount(reportInfoMapper.getInRecognitionCount(list));
        if(allCaseCount > 0){
            BigDecimal denominator = new BigDecimal(allCaseCount);
            BigDecimal numerator = new BigDecimal(reportInfoMapper.getIdentifyCaseCountYesterday(list));
            BigDecimal result = numerator.divide(denominator,2,RoundingMode.HALF_UP);
            aiModelStatisticsVO.setAICoverageRate(result.multiply(new BigDecimal(100)) + "%");
        } else {
            aiModelStatisticsVO.setAICoverageRate("0%");
        }
        return aiModelStatisticsVO;
    }
}
