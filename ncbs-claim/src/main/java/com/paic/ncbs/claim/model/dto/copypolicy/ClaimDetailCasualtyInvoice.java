package com.paic.ncbs.claim.model.dto.copypolicy;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ClaimDetailCasualtyInvoice {
    private Long hospitalSeqNum;//医院序号 对应ClaimDetailCasualtyMedical的seqNum
    private Long seqNum;//序号 从1开始
    private String invoiceNo;//发票号码
    private String invoiceType;//费用类型（1：总费用 2：牙科费用 3：中医费用 4：腰间盘突出费用 5：其它）
    private String billType;//账单类型（1：医保账单 2：自费账单）
    private BigDecimal strBillAmt;//发票金额
    private BigDecimal strPayableAllAmt;//全部自费
    private BigDecimal strPayablePartialAmt;//部分自费
    private BigDecimal strFundPayAmt;//统筹支付
    private BigDecimal strOtherDeductionAmt;//其他扣减金额
    private BigDecimal strDeductionAmt;//免赔额
    private BigDecimal strDeductionRate;//免赔率
    private BigDecimal strFinalPayAmount;//实际支付金额
    private String reason;//扣减原因
    private String otherDeductionReason;//其他扣减原因描述

}
