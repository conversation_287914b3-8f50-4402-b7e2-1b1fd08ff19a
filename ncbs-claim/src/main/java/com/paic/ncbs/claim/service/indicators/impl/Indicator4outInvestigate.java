package com.paic.ncbs.claim.service.indicators.impl;

import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 公估调查时效计算
 */
@Service("Indicator4outInvestigate")
public class Indicator4outInvestigate extends  ClmsCaseIndicatorServiceImpl{
    @Override
    protected void saveStablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.stable4outInvestigate(lastLog, now);
    }

    @Override
    protected void resaveUnstablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.unstable4outInvestigate(lastLog, now);
    }
}
