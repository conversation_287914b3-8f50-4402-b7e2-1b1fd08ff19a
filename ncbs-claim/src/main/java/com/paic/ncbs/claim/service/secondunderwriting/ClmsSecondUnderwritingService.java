package com.paic.ncbs.claim.service.secondunderwriting;

import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsSecondUnderwritingVO;

import java.util.List;

/**
 * 理赔二核申请记录表(ClmsSecondUnderwritingEntity)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-08 15:15:58
 */
public interface ClmsSecondUnderwritingService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSecondUnderwritingEntity queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    List<ClmsSecondUnderwritingEntity> queryByReportNo(String reportNo, Integer caseTimes);


    /**
     * 新增数据
     *
     * @param clmsSecondUnderwritingEntity 实例对象
     * @return 实例对象
     */
    ClmsSecondUnderwritingEntity insert(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity);

    /**
     * 发送二核任务
     *
     */
    void sendTask(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity);

    /**
      *
      * @Description 更新
      * <AUTHOR>
      * @Date 2023/9/12 20:22
      **/
    ClmsSecondUnderwritingEntity update(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity);

    /**
      *
      * @Description 查询历史二核
      * <AUTHOR>
      * @Date 2023/9/12 20:22
      **/
    List<ClmsSecondUnderwritingVO> getUWRecords(String reportNo, Integer caseTimes);

    /**
      *
      * @Description 获取次数
      * <AUTHOR>
      * @Date 2023/9/13 17:28
      **/
    Integer getUWCount(String reportNo, Integer caseTimes);

    /**
     *
     *根据报案号赔付次数查询送核状态为01-送核审批中 的数据
     * 一个报案号只会存在 一条 状态为 01-送核审批中的数据
     **/
    ClmsSecondUnderwritingEntity getUWRecord(String reportNo, Integer caseTimes);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    ClmsSecondUnderwritingVO getUwDetailInfo(String id);

    /**
     * 根据保单号+被保险人查询
     * @param reportNo
     * @param caseTimes
     * @param hitICDList  命中的ICD集合
     * @return
     */
    boolean isAllSecondUW(String reportNo, Integer caseTimes, List<String> hitICDList);

    /**
     * 根据保单号+被保险人查询出存在二核未完成的案件号集合。
     * @param reportNo
     * @param caseTimes
     */
    List<String> getUnfinishedSecondUWOther(String reportNo, Integer caseTimes);

    /**
     * 根据被保险人客户号查询所有存在二核任务结果为拒保的案件号集合。
     * @param reportNo
     */
    List<String> getRejectSecondUW(String reportNo);

    /**
     * 查询非退回二核次数
     * @param reportNo
     * @param caseTimes
     * @return
     */
    Integer getUnBackUWCount(String reportNo,  Integer caseTimes);
}
