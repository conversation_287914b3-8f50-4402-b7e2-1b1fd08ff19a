package com.paic.ncbs.claim.dao.mapper.investigate;

import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InvestigateTaskAuditPublicMapper {

    /**
     * 查询调查任务列表
     * @param investigateDepartment 调查部门
     * @return 任务列表
     */
    List<WorkBenchTaskVO> getInvestigateTaskList(List<String> investigateDepartment);

}