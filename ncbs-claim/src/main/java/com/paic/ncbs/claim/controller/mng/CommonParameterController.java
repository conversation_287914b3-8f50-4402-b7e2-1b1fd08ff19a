package com.paic.ncbs.claim.controller.mng;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.model.dto.other.*;
import com.paic.ncbs.claim.model.vo.duty.LossTypeVO;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/mng/app/common")
@Api(tags = {"公共下拉数据"})
public class CommonParameterController extends BaseController {

    @Autowired
    private CommonParameterService commonService;


    @ApiOperation(value = "获取公共下拉数据选项List")
    @ResponseBody
    @GetMapping(value = "/getDataDict/{collectionCode}")
    public ResponseResult<List<CommonParameterTinyDTO>> getDataDict(@ApiParam("参数类型码") @PathVariable("collectionCode") String collectionCode) {
        if (collectionCode == null || StringUtils.isEmptyStr(collectionCode)) {
            LogUtil.audit("获取公共下拉数据选项出错：查询参数不能为空");
            return null;
        }

        List<CommonParameterTinyDTO> commonParameterList = commonService.getCommonParameterList(new String[]{collectionCode});
        return ResponseResult.success(commonParameterList);
    }

    @ApiOperation(value = "获取公共下拉数据Map")
    @ResponseBody
    @GetMapping(value = "/getDataDictList")
    public ResponseResult<Map<String, List<CommonParameterTinyDTO>>> getDataDictList(@ApiParam(" 参数类型码数组") String[] collectionCodeList) {
        if (collectionCodeList == null || collectionCodeList.length == 0) {
            LogUtil.audit("获取公共下拉数据选项出错：查询参数不能为空");
            return null;
        }

        Map<String, List<CommonParameterTinyDTO>> map = commonService.getDataDictList(collectionCodeList);
        return ResponseResult.success(map);
    }

    @ApiOperation(value = "根据编码获取地址")
    @PostMapping(value = "/getDetailAdressFormCode")
    @ResponseBody
    public ResponseResult<Object> getDetailAdressFormCode(@RequestBody AdressSearchDto adressSearchDto) {
        return ResponseResult.success(commonService.getDetailAdressFormCode(adressSearchDto));
    }

    @ApiOperation(value = "获取出险原因下拉数据")
    @ResponseBody
    @GetMapping(value = "/getAccidentReason")
    public ResponseResult<List<AccidentReasonTypeInfoDTO>> getAccidentReason(@RequestParam("certificateNo") String certificateNo,
                                                                             @RequestParam("policyList") String policyList,
                                                                             @RequestParam("insuredName") String insuredName) {
        List<AccidentReasonTypeInfoDTO> accidentReasonDTOList = (StringUtils.isNotEmpty(certificateNo) && StringUtils.isNotEmpty(policyList) && StringUtils.isNotEmpty(insuredName)) ? commonService.getAccidentReason(certificateNo, policyList, insuredName)
                : commonService.getAccidentReason();
        return ResponseResult.success(accidentReasonDTOList);
    }

    @ApiOperation(value = "获取财产损失下拉数据")
    @ResponseBody
    @GetMapping(value = "/getPropertyLossReason")
    public ResponseResult<List<PropertyLossReasonDTO>> getPropertyLossReason() {
        List<PropertyLossReasonDTO>  propertyLossReasonDTOList = commonService.getPropertyLossReason();
        return ResponseResult.success(propertyLossReasonDTOList);
    }

    @ApiOperation(value = "获取救援类型下拉数据")
    @ResponseBody
    @GetMapping(value = "/getSuccourType")
    public ResponseResult<List<SuccourTypeDTO>> getSuccourType() {
        List<SuccourTypeDTO>  succourTypeDTOList = commonService.getSusccourTypeList();
        return ResponseResult.success(succourTypeDTOList);
    }

    @ApiOperation(value = "获取重点项目")
    @ResponseBody
    @GetMapping(value = "/getKeyProjects")
    public ResponseResult<List<KeyProjectsDTO>> getKeyProjects() {
        List<KeyProjectsDTO>  keyProjectsDTOS = commonService.getKeyProjectsList();
        return ResponseResult.success(keyProjectsDTOS);
    }
    @ApiOperation(value = "获取损失类别")
    @ResponseBody
    @GetMapping(value = "/getLossClass")
    public ResponseResult<List<LossTypeVO>> getLossClass(@RequestParam("certificateNo") String certificateNo,
                                                                             @RequestParam("policyList") String policyList,
                                                                             @RequestParam("insuredName") String insuredName) {
        List<LossTypeVO> commonParameterList = (StringUtils.isNotEmpty(certificateNo) && StringUtils.isNotEmpty(policyList) && StringUtils.isNotEmpty(insuredName)) ? commonService.getLossClass(certificateNo, policyList, insuredName)
                : commonService.getLossClass();
        return ResponseResult.success(commonParameterList);
    }

    @ApiOperation(value = "获取货运险货物类别下拉数据")
    @ResponseBody
    @GetMapping(value = "/getGoodsCategory")
    public ResponseResult<List<CargoCategoryDTO>> getGoodsCategory() {
        List<CargoCategoryDTO> goodsCategory = commonService.getGoodsCategory();
        return ResponseResult.success(goodsCategory);
    }

}
