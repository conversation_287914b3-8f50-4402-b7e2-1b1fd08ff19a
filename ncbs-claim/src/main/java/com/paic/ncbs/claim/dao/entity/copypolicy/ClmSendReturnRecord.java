package com.paic.ncbs.claim.dao.entity.copypolicy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * global回流接口发送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Getter
@Setter
@TableName("clm_send_return_record")
public class ClmSendReturnRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField("id")
    private String id;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 发送次数，用于补偿机制
     */
    @TableField("send_times")
    private Integer sendTimes;

    /**
     * 请求方法
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 请求参数
     */
    @TableField("request_param")
    private String requestParam;

    /**
     * 响应参数
     */
    @TableField("response_param")
    private String responseParam;

    /**
     * 请求是否成功，Y-成功 N-失败
     */
    @TableField("is_success")
    private String isSuccess;

    /**
     * 案件环节描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 最新修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;
}
