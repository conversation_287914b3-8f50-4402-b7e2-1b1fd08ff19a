package com.paic.ncbs.claim.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class NumberToChineseUtils {

    // 中文数字字符
    private static final String[] CHINESE_NUMBERS = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};

    // 中文单位
    private static final String[] CHINESE_UNITS = {"", "拾", "佰", "仟"};

    // 中文大单位
    private static final String[] CHINESE_BIG_UNITS = {"", "万", "亿", "兆"};

    /**
     * 将数字转换为中文大写形式
     * @param number 需要转换的数字
     * @return 中文大写形式
     */
    public static String numberToChinese(double number) {
        if (number < 0) {
            return "负" + numberToChinese(-number);
        }

        String numStr = String.valueOf(number);
        String integerPart;
        String decimalPart;

        // 分离整数部分和小数部分
        int dotIndex = numStr.indexOf('.');
        if (dotIndex >= 0) {
            integerPart = numStr.substring(0, dotIndex);
            decimalPart = numStr.substring(dotIndex + 1);
        } else {
            integerPart = numStr;
            decimalPart = "";
        }

        StringBuilder result = new StringBuilder();

        // 处理整数部分
        if ("0".equals(integerPart)) {
            result.append("零");
        } else {
            result.append(integerPartToChinese(integerPart));
        }

        // 处理小数部分
        if (!decimalPart.isEmpty() && !"00".equals(decimalPart)) {
            result.append("点");
            for (int i = 0; i < decimalPart.length(); i++) {
                int digit = Character.getNumericValue(decimalPart.charAt(i));
                result.append(CHINESE_NUMBERS[digit]);
            }
        }

        return result.toString();
    }

    /**
     * 将整数部分转换为中文大写
     * @param integerPart 整数部分字符串
     * @return 中文大写形式
     */
    private static String integerPartToChinese(String integerPart) {
        StringBuilder result = new StringBuilder();
        int len = integerPart.length();

        // 按四位一段处理
        int segmentCount = (len + 3) / 4;
        for (int i = 0; i < segmentCount; i++) {
            int start = Math.max(0, len - (i + 1) * 4);
            int end = len - i * 4;
            String segment = integerPart.substring(start, end);

            String segmentChinese = convertSegment(segment);
            if (!segmentChinese.isEmpty() && !"零".equals(segmentChinese)) {
                result.insert(0, segmentChinese + CHINESE_BIG_UNITS[i]);
            }
        }

        // 处理连续的零
        String resultStr = result.toString();
        resultStr = resultStr.replaceAll("零+", "零");
        resultStr = resultStr.replaceAll("零$", "");
        resultStr = resultStr.replaceAll("^零+", "");

        return resultStr;
    }

    /**
     * 转换四位以内的数字段
     * @param segment 数字段
     * @return 中文大写形式
     */
    private static String convertSegment(String segment) {
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < segment.length(); i++) {
            int digit = Character.getNumericValue(segment.charAt(i));
            int position = segment.length() - i - 1;

            if (digit != 0) {
                result.append(CHINESE_NUMBERS[digit]).append(CHINESE_UNITS[position]);
            } else if (result.length() > 0 && !result.toString().endsWith("零")) {
                // 添加零（避免连续的零）
                result.append("零");
            }
        }

        return result.toString();
    }

    /**
     * 将金额转换为中文大写形式（带元角分）
     * @param amount 金额
     * @return 中文大写金额
     */
    public static String amountToChinese(double amount) {
        if (amount < 0) {
            return "负" + amountToChinese(-amount);
        }

        String amountStr = String.format("%.2f", amount);
        String integerPart;
        String decimalPart;

        // 分离整数部分和小数部分
        int dotIndex = amountStr.indexOf('.');
        if (dotIndex >= 0) {
            integerPart = amountStr.substring(0, dotIndex);
            decimalPart = amountStr.substring(dotIndex + 1);
        } else {
            integerPart = amountStr;
            decimalPart = "00";
        }

        StringBuilder result = new StringBuilder();

        // 处理整数部分
        if ("0".equals(integerPart)) {
            result.append("零");
        } else {
            result.append(integerPartToChinese(integerPart));
        }

        result.append("元");

        // 处理小数部分（角分）
        if (!"00".equals(decimalPart)) {
            int jiao = Character.getNumericValue(decimalPart.charAt(0));
            int fen = Character.getNumericValue(decimalPart.charAt(1));

            if (jiao != 0) {
                result.append(CHINESE_NUMBERS[jiao]).append("角");
            }

            if (fen != 0) {
                result.append(CHINESE_NUMBERS[fen]).append("分");
            } else if (jiao != 0 && result.toString().contains("元")) {
                result.append("整");
            }
        } else {
            result.append("整");
        }

        return result.toString();
    }
    /**
     * 将金额转换为中文大写形式（带元分）
     * @param amount 金额
     * @return 中文大写金额
     */
    public static String amountToChinese(BigDecimal amount) {
        if (amount == null) {
            return "零元整";
        }

        // 保留两位小数
        amount = amount.setScale(2, RoundingMode.HALF_UP);

        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            return "负" + amountToChinese(amount.abs());
        }

        String amountStr = amount.toString();
        String integerPart;
        String decimalPart;

        // 分离整数部分和小数部分
        int dotIndex = amountStr.indexOf('.');
        if (dotIndex >= 0) {
            integerPart = amountStr.substring(0, dotIndex);
            decimalPart = amountStr.substring(dotIndex + 1);
        } else {
            integerPart = amountStr;
            decimalPart = "00";
        }

        StringBuilder result = new StringBuilder();

        // 处理整数部分
        if ("0".equals(integerPart)) {
            result.append("零");
        } else {
            result.append(integerPartToChinese(integerPart));
        }

        result.append("元");

        // 处理小数部分（角分）
        if (!"00".equals(decimalPart)) {
            int jiao = Character.getNumericValue(decimalPart.charAt(0));
            int fen = Character.getNumericValue(decimalPart.charAt(1));

            if (jiao != 0) {
                result.append(CHINESE_NUMBERS[jiao]).append("角");
            }

            if (fen != 0) {
                result.append(CHINESE_NUMBERS[fen]).append("分");
            } else if (jiao != 0) {
                result.append("整");
            }
        } else {
            result.append("整");
        }

        return result.toString();
    }

}
