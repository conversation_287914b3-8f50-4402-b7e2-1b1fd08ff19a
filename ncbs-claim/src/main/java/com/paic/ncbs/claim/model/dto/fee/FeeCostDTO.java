package com.paic.ncbs.claim.model.dto.fee;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 理赔费用DTO 与数据库ahcs_fee_pay对应
 */
public class FeeCostDTO {
	private String createdBy;
	private Date createDate;
	private String updatedBy;
	private Date updateDate;
	private String idAhcsFeePay;
	private String reportNo;
	private String policyNo;
	private String caseNo;
	private Integer caseTimes;
	private String feeType;
	private BigDecimal feeAmount;
	private String idClmPaymentInfo;
	private String idClmPaymentItem;
	
	/**
	 * 赔付类型
	 */
	private String claimType;
	/**
	 * 是否有效
	 */
	private String isEffective;
	
	/**
	 * 客户名称,被保险人
	 */
	private String clientName;
	/**
	 * 预赔次数
	 */
	private Integer subTimes;
	/**
	 * 损失对象代码
	 */
	private String lossObjectNo;

	private String dutyCode;
	
	
	
	public String getLossObjectNo() {
		return lossObjectNo;
	}
	public void setLossObjectNo(String lossObjectNo) {
		this.lossObjectNo = lossObjectNo;
	}
	public Integer getSubTimes() {
		return subTimes;
	}
	public void setSubTimes(Integer subTimes) {
		this.subTimes = subTimes;
	}
	public String getIdClmPaymentItem() {
		return idClmPaymentItem;
	}
	public void setIdClmPaymentItem(String idClmPaymentItem) {
		this.idClmPaymentItem = idClmPaymentItem;
	}
	public String getClientName() {
		return clientName;
	}
	public void setClientName(String clientName) {
		this.clientName = clientName;
	}
	public String getIsEffective() {
		return isEffective;
	}
	public void setIsEffective(String isEffective) {
		this.isEffective = isEffective;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public String getIdAhcsFeePay() {
		return idAhcsFeePay;
	}
	public void setIdAhcsFeePay(String idAhcsFeePay) {
		this.idAhcsFeePay = idAhcsFeePay;
	}
	public String getReportNo() {
		return reportNo;
	}
	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}
	public String getPolicyNo() {
		return policyNo;
	}
	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}
	public String getCaseNo() {
		return caseNo;
	}
	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}
	public Integer getCaseTimes() {
		return caseTimes;
	}
	public void setCaseTimes(Integer caseTimes) {
		this.caseTimes = caseTimes;
	}
	public String getFeeType() {
		return feeType;
	}
	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}
	public BigDecimal getFeeAmount() {
		return feeAmount;
	}
	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}
	public String getIdClmPaymentInfo() {
		return idClmPaymentInfo;
	}
	public void setIdClmPaymentInfo(String idClmPaymentInfo) {
		this.idClmPaymentInfo = idClmPaymentInfo;
	}
	public String getClaimType() {
		return claimType;
	}
	public void setClaimType(String claimType) {
		this.claimType = claimType;
	}

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }
}
