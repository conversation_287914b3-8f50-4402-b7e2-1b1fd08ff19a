package com.paic.ncbs.claim.dao.entity.coinsurance;

import com.paic.ncbs.claim.replevy.vo.ClmsRelatedActualReceiptVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CoinsAmorDetailVo {
    //共保摊回记录
    private List<CoinsAmortizationVo> coinsAmortizationVoList;
    //关联实收记录
    private List<ClmsRelatedActualReceiptVo> clmsRelatedActualReceiptVoList;
    private String amountDifferenceReason;//应摊回金额与实收金额不一致原因
    private String companyPaymentMismatchReason;//共保公司与打款方不一致原因
    private BigDecimal sumAmorAmount;//总摊回金额
    private BigDecimal sumReceiptsAmount;//总实收金额
    private String idRecoveryRecord;
    private List<AmorFileVo> fileInfoList;
}
