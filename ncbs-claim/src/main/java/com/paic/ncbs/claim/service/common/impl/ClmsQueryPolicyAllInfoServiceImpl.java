package com.paic.ncbs.claim.service.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsSpecialPromiseEntity;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPresonMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.ahcs.AhcsSpecialPromiseService;
import com.paic.ncbs.claim.service.common.ClmsGetPolicyMonthInfoService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyAllInfoService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.service.duty.DutyAttrAsyncInitService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Slf4j
@Service
@RefreshScope
public class ClmsQueryPolicyAllInfoServiceImpl implements ClmsQueryPolicyAllInfoService {
    @Autowired
    private PolicyPayMapper policyPayMapper;
    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private AhcsSpecialPromiseService ahcsSpecialPromiseService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;
    @Autowired
    private MedicalBillService medicalBillService;
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;

    @Autowired
    private AhcsInsuredPresonMapper ahcsInsuredPresonMapper;

    @Autowired
    private AhcsPolicyPlanMapper ahcsPolicyPlanMapper;

    @Autowired
    private ClmsGetPolicyMonthInfoService clmsGetPolicyMonthInfoService;

    @Autowired
    private DutyAttrAsyncInitService dutyAttrAsyncInitService;

    @Value("${special.productCode}")
    private String specialProduct;

    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    @Override
    public List<PolicyPayDTO> getPolicyAllInfo(String reportNo, Integer caseTimes) {
        //从抄单表clms_policy_info、clm_case_base、clms_policy_duty_detail 中查询保单信息
        List<PolicyPayDTO> policys = policyPayMapper.selectFromPolicyCopy(reportNo, caseTimes);
        removeRepeatDate(policys);
        mergeRepeatData(policys);
        //共保描述
        Map<String,String> coinsMap = coinsureService.getCoinsureDescByReportNo(reportNo);
        if(coinsMap.size() > 0){
            for (PolicyPayDTO dto : policys) {
                dto.setCoinsuranceDesc(coinsMap.getOrDefault(dto.getPolicyNo(),""));
            }
        }
        //查询特约
        querySpecialPromise(policys);
        /*try {
            riskPropertyService.setRiskPropertyPlan(policys);
        }catch (Exception e){
            LogUtil.info("标的组设置失败,不影响原有流程",e);
        }*/
        //设置被保险人社保属性  算理赔费用时要用，保单历史报案录入发票信息的合理费用
        setIsSociaSecurity(policys,reportNo);
        LogUtil.info("TPA报案号：{},理算查询保单信息:{}", reportNo, JSON.toJSONString(policys));
        return policys;
    }
    public void removeRepeatDate(List<PolicyPayDTO> policys) {
        String userId = WebServletContext.getUserId();

        for (PolicyPayDTO policy : policys) {
            if (StringUtils.isNotEmpty(userId)) {
                policy.setCreatedBy(userId);
                policy.setUpdatedBy(userId);
            }
            for (PlanPayDTO plan : policy.getPlanPayArr()) {
                if (StringUtils.isNotEmpty(userId)) {
                    plan.setCreatedBy(userId);
                    plan.setUpdatedBy(userId);
                }
                Map<String, DutyPayDTO> dutyMap = new LinkedHashMap<>();
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    if (StringUtils.isNotEmpty(userId)) {
                        duty.setCreatedBy(userId);
                        duty.setUpdatedBy(userId);
                    }
                    String dutyCode = duty.getDutyCode();
                    DutyPayDTO dutyDto = dutyMap.get(dutyCode);
                    if (dutyDto == null || nvl(duty.getBaseAmountPay(), 0).compareTo(nvl(dutyDto.getBaseAmountPay(), 0)) > 0) {
                        dutyMap.put(dutyCode, duty);
                    }

                    Map<String, DutyDetailPayDTO> dutyDetailMap = new LinkedHashMap<>();
                    for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
                        if (StringUtils.isNotEmpty(userId)) {
                            detail.setCreatedBy(userId);
                            detail.setUpdatedBy(userId);
                        }
                        String detailCode = detail.getDutyDetailCode();
                        DutyDetailPayDTO detailDto = dutyDetailMap.get(detailCode);
                        if (detailDto == null || nvl(detail.getBaseAmountPay(), 0).compareTo(nvl(detailDto.getBaseAmountPay(), 0)) > 0) {
                            dutyDetailMap.put(detailCode, detail);
                        }
                    }
                    List<DutyDetailPayDTO> details = new ArrayList<>();
                    details.addAll(dutyDetailMap.values());
                    duty.setDutyDetailPayArr(details);
                }
                List<DutyPayDTO> dutys = new ArrayList<>();
                dutys.addAll(dutyMap.values());
                plan.setDutyPayArr(dutys);
            }
        }
    }
    private void mergeRepeatData(List<PolicyPayDTO> policys) {
        for (PolicyPayDTO policy : policys) {
            Map<String, PlanPayDTO> planRepeatMap = new HashMap<>();
            List<PlanPayDTO> planList = policy.getPlanPayArr();
            if (planList == null) {
                continue;
            }
            Iterator<PlanPayDTO> pit = planList.iterator();
            while (pit.hasNext()) {
                PlanPayDTO plan = pit.next();
                String planCode = plan.getPlanCode();
                if (StringUtils.isEmpty(planCode)) {
                    continue;
                }
                if (!planRepeatMap.containsKey(planCode)) {
                    planRepeatMap.put(planCode, plan);
                    continue;
                }
                PlanPayDTO repeatPlan = planRepeatMap.get(planCode);
                this.mergeRepeatPlan(repeatPlan, plan);
                pit.remove();
            }
        }
    }
    private void mergeRepeatPlan(PlanPayDTO repeatPlan, PlanPayDTO plan) {
        repeatPlan.setIsMergePolicyDuty("Y");
        List<DutyPayDTO> repeatDutyPayArr = repeatPlan.getDutyPayArr();
        Map<String, DutyPayDTO> repeatDutyPayMap = new HashMap<>();
        for (DutyPayDTO dutyPayDTO : repeatDutyPayArr) {
            repeatDutyPayMap.put(dutyPayDTO.getDutyCode(), dutyPayDTO);
        }
        List<DutyPayDTO> dutyPayArr = plan.getDutyPayArr();
        for (DutyPayDTO duty : dutyPayArr) {
            String dutyCode = duty.getDutyCode();
            if (repeatDutyPayMap.containsKey(dutyCode)) {
                DutyPayDTO repeatDutyPay = repeatDutyPayMap.get(dutyCode);
                repeatDutyPay.setBaseAmountPay(repeatDutyPay.getBaseAmountPay().add(duty.getBaseAmountPay()));
                this.mergeRepeatDuty(repeatDutyPay, duty);
            } else {
                repeatDutyPayArr.add(duty);
            }
        }
    }
    private void mergeRepeatDuty(DutyPayDTO repeatDuty, DutyPayDTO duty) {
        List<DutyDetailPayDTO> repeatDutyDetailArr = repeatDuty.getDutyDetailPayArr();
        Map<String, DutyDetailPayDTO> repeatDutyDetailPayMap = new HashMap<>();
        for (DutyDetailPayDTO repeatDutyDetail : repeatDutyDetailArr) {
            repeatDutyDetailPayMap.put(repeatDutyDetail.getDutyDetailCode(), repeatDutyDetail);
        }
        for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
            String detailCode = detail.getDutyDetailCode();
            if (repeatDutyDetailPayMap.containsKey(detailCode)) {
                DutyDetailPayDTO repeatDutyDetailPay = repeatDutyDetailPayMap.get(detailCode);
                repeatDutyDetailPay.setIsMergePolicyDuty(detail.getIsMergePolicyDuty());
                repeatDutyDetailPay.setBaseAmountPay(repeatDutyDetailPay.getBaseAmountPay().add(detail.getBaseAmountPay()));
            } else {
                repeatDutyDetailArr.add(detail);
            }
        }
    }
    private void querySpecialPromise(List<PolicyPayDTO> policys) {
        for (PolicyPayDTO policy : policys) {
            List<AhcsSpecialPromiseEntity> promiseEntities = ahcsSpecialPromiseService.getInfoByPolicyId(policy.getIdAhcsPolicyInfo());
            policy.setSpecialPromises(promiseEntities.stream().map(AhcsSpecialPromiseEntity::convertToDTO).collect(Collectors.toList()));

        }
    }

    private void setIsSociaSecurity(List<PolicyPayDTO> policys,String reportNo) {
        BigDecimal reasonableAmount = medicalBillService.getPolicyHistoryBillInfo(reportNo);
        ReportCustomerInfoEntity entity = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
        //查询被保险人是否有社保
        List<AhcsInsuredPresonEntity> persionList = ahcsInsuredPresonMapper.getAhcsInsuredPersionByClientNo(entity.getClientNo());
        String isSociaSecurity="";
        if(CollectionUtil.isNotEmpty(persionList)){
            isSociaSecurity=persionList.get(0).getIsSociaSecurity();
        }

        for (PolicyPayDTO payDTO : policys) {
            PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(payDTO.getPolicyNo(),reportNo);
            //合同月
            List<PolicyMonthDto> monthDtoList = clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),policyDto.getPolicyEndDate());
            payDTO.setMonthDtoList(monthDtoList);
            log.info("报案号={}，保单最新信息={}",reportNo, JsonUtils.toJsonString(policyDto));
            String productPackage;
            if(specialProduct.contains(payDTO.getProductCode())){
                List<PolicyInfoDTO> policyInfoListByReportNo = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
                if (CollectionUtils.isNotEmpty(policyInfoListByReportNo)) {
                    productPackage= policyInfoListByReportNo.get(0).getProductPackageType();
                }else {
                    productPackage= ahcsPolicyPlanMapper.getPolicyProductPackage(payDTO.getPolicyNo());
                }
            }else {
                productPackage= ahcsPolicyPlanMapper.getPolicyProductPackage(payDTO.getPolicyNo());
            }
            payDTO.setProductPackage(productPackage);
            for (PlanPayDTO planPayDTO : payDTO.getPlanPayArr()) {
                for (DutyPayDTO dutyPayDTO: planPayDTO.getDutyPayArr()) {
                    dutyPayDTO.setProductPackage(productPackage);
                    for (DutyDetailPayDTO dutyDetailPayDTO :dutyPayDTO.getDutyDetailPayArr()) {
                        dutyDetailPayDTO.setIsSociaSecurity(isSociaSecurity);
                        //保单历史报案录服发票信息的 合理费用
                        dutyDetailPayDTO.setPolicyBillHistoryAmount(reasonableAmount);
                        dutyDetailPayDTO.setProductPackage(productPackage);
                        dutyDetailPayDTO.setProductCode(payDTO.getProductCode());
                        dutyDetailPayDTO.setInsuranceBeginTime(policyDto.getPolicyStartDate());
                        dutyDetailPayDTO.setInsuranceEndTime(policyDto.getPolicyEndDate());
                        dutyDetailPayDTO.setMonthDtoList(monthDtoList);
                    }
                    //重新获取责任属性
                    dutyAttrAsyncInitService.getAttrByDutyDetailType(dutyPayDTO,payDTO.getReportNo(),payDTO.getCaseTimes());
                }
            }
        }
    }
    @Override
    public String getPolicyProductCode(String reportNo, Integer caseTimes) {
        return policyPayMapper.getPolicyProductCode(reportNo,caseTimes);
    }

}
