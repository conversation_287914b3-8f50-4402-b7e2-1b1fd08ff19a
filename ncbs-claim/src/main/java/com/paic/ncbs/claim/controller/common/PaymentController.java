package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.ReplevyConstant;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.other.BatchRequest;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.vo.settle.PaymentCompensateVO;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyChargeMapper;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyMainMapper;
import com.paic.ncbs.claim.replevy.service.ReplevyChargeService;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyChargeVo;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Api(tags = "送收付")
@RestController
@RequestMapping("/public/payment")
public class PaymentController {

    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;
    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private ClmsReplevyChargeMapper clmsReplevyChargeMapper;
    @Autowired
    private ReplevyChargeService replevyChargeService;
    @Autowired
    private ClmsReplevyMainMapper clmsReplevyMainMapper;
    @ApiOperation("送收付补推")
    @PostMapping(value = "/compensate")
    public ResponseResult<Object> paymentCompensate(@RequestBody (required = false) List<PaymentCompensateVO> paymentCompensateVOList) {
        if (ListUtils.isNotEmpty(paymentCompensateVOList)) {
            for (PaymentCompensateVO paymentCompensateVO : paymentCompensateVOList) {
                payInfoNoticeThirdPartyCoreSAO.noticePayment(paymentCompensateVO.getReportNo(),paymentCompensateVO.getCaseTimes(),
                        paymentCompensateVO.getPaySerialNo(), paymentCompensateVO.isVerifyFirstPay(), paymentCompensateVO.isPrePay());
            }
        }
        return ResponseResult.success();
    }

    @ApiOperation("退货险合并支付送收付补推")
    @GetMapping(value = "/mergePayCompensate")
    public ResponseResult<Object> mergePayCompensate(@RequestParam("failDate")
                                                         String failDate) {
        if(StringUtils.isBlank(failDate)){
            throw new GlobalBusinessException("失败日期不能为空");
        } else {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            try {
                Date date = format.parse(failDate);
                paymentInfoService.mergePayCompensate(date);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return ResponseResult.success();
    }
    /**
     * 补推核销接口C03
     * @param batchNos 核销批次号
     * @return
     */
    @ApiOperation("核销补推")
    @PostMapping(value = "/confirmCompensate")
    public ResponseResult<Object> confirmCompensate(@RequestBody List<String> batchNos) {
        for(String batchNo : batchNos){
            PaymentItemDTO itemDTO = new PaymentItemDTO();
            itemDTO.setBatchNo(batchNo);
            itemDTO.setClientType(SettleConst.CLAIM_TYPE_REP_PAY);
            List<PaymentItemDTO> items = paymentItemService.getAllPaymentItem(itemDTO);
            if(CollectionUtils.isNotEmpty(items)){
               if(Constants.PAYMENT_ITEM_STATUS_11.equals(items.get(0).getPaymentItemStatus())||Constants.PAYMENT_ITEM_STATUS_32.equals(items.get(0).getPaymentItemStatus())){
                   ClmsReplevyMainVo replevyMain = clmsReplevyMainMapper.selectReplevyMainBySerialNo(items.get(0).getReportNo(), items.get(0).getSubTimes());
                   if(replevyMain!=null&&"3".equals(replevyMain.getApproveFlag())){
                       String replevyNo = replevyMain.getReplevyNo();
                       //追偿赔款核销
                       replevyChargeService.sendPaymentConfirm(items.get(0).getBatchNo(), ReplevyConstant.RECEIPT_TYPE_RELEVY,replevyNo);
                   }
               }
            }
        }
        return ResponseResult.success();
    }
}
