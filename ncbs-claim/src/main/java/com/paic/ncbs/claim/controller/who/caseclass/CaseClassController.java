package com.paic.ncbs.claim.controller.who.caseclass;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDefineDTO;
import com.paic.ncbs.claim.model.vo.endcase.CaseClassDefineVO;
import com.paic.ncbs.claim.service.endcase.CaseClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Api(tags = "案件类别")
@Slf4j
@RestController
@RequestMapping("/who/app/caseClassAction")
public class CaseClassController extends BaseController {

    @Resource(name = "caseClassService")
    private CaseClassService caseClassService;

    @ApiOperation("获取案件类别")
    @GetMapping(value = "/getCaseClassList/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String", dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<List<CaseClassDefineVO>> getCaseClassList(@PathVariable("reportNo") String reportNo,
                                                                    @PathVariable("caseTimes") int caseTimes) {
        LogUtil.audit("获取案件类别列表 reportNo：{},caseTimes:{}", reportNo, caseTimes);
        return ResponseResult.success(caseClassService.getCaseClassDefines(reportNo, caseTimes));
    }

    @ApiOperation("获取案件类别定义列表")
    @GetMapping(value = "/getCaseClassDefineList")
    public ResponseResult<List<CaseClassDefineDTO>> getCaseClassDefineList() throws GlobalBusinessException {
        log.info("获取案件类别定义列表");
        return ResponseResult.success(caseClassService.getCaseClassDefineList());
    }

}
