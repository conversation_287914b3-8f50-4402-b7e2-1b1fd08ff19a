package com.paic.ncbs.claim.controller.who.mistake;


import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.mistake.MistakeRecordDTO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.mistake.MistakeDefineService;
import com.paic.ncbs.claim.service.mistake.MistakeRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Api(tags = "差错记录管理")
@Controller
@RequestMapping("/who/app/mistakeRecordAction")
public class MistakeRecordController extends BaseController {

	@Autowired
	private MistakeRecordService mistakeRecordService;

	@Autowired
	private MistakeDefineService mistakeDefineService;

	@Autowired

	private BpmService bpmService;


	@ApiOperation("插入差错信息")
	@ResponseBody
	@PostMapping(value="/addMistakeRecord/{taskDefinitionBpmKey}")
	public ResponseResult getMistakeDefineList(@RequestBody MistakeRecordDTO mistakeRecordDTO,
											   @PathVariable("taskDefinitionBpmKey") String taskDefinitionBpmKey){
		//zjtang 流程校验
		if(BpmConstants.OC_CHECK_DUTY.equals(taskDefinitionBpmKey)){
			bpmService.processCheck(mistakeRecordDTO.getReportNo(), BpmConstants.OC_MANUAL_SETTLE,BpmConstants.OPERATION_BACK);
		}
		// 理算页面 taskDefinitionBpmKey ：OC_CHECK_DUTY, 核赔页面 taskDefinitionBpmKey ：OC_MANUAL_SETTLE
		mistakeRecordService.addMistakeRecord(mistakeRecordDTO,taskDefinitionBpmKey);
		return ResponseResult.success();
	}

	@ApiOperation("查询差错信息")
	@ResponseBody
	@PostMapping(value="/getMistakeRecordList/{taskDefinitionBpmKey}")
	public ResponseResult<List<MistakeRecordDTO>> getMistakeRecordList(@RequestBody MistakeRecordDTO mistakeRecordDTO,
																	   @PathVariable("taskDefinitionBpmKey") String taskDefinitionBpmKey){
		List<MistakeRecordDTO> mistakeRecordDTOs = new ArrayList<>();
		// 理算页面 taskDefinitionBpmKey ：OC_CHECK_DUTY, 核赔页面 taskDefinitionBpmKey ：OC_MANUAL_SETTLE
		List<MistakeRecordDTO> simpleMistakeRecord = mistakeRecordService.getSimpleMistakeRecord(mistakeRecordDTO, taskDefinitionBpmKey);
		if (CollectionUtils.isEmpty(simpleMistakeRecord)){
			return ResponseResult.success(mistakeRecordDTOs);
		}
		Map<String, List<MistakeRecordDTO>> collect = simpleMistakeRecord.stream().collect(Collectors.groupingBy(MistakeRecordDTO::getDeptCode));
		collect.forEach((k,v)->{
			MistakeRecordDTO recordDTO = v.get(0);
			List<String> mistakeNames = new ArrayList<>();
			v.forEach(r -> mistakeNames.add(mistakeDefineService.getMistakeName(r.getMistakeCode())));
			recordDTO.setMistakeName(mistakeNames);
			mistakeRecordDTOs.add(recordDTO);
		});
		return ResponseResult.success(mistakeRecordDTOs);
	}

}
