package com.paic.ncbs.claim.service.settle.factor.impl.savesettle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.googlecode.aviator.Expression;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.ClmsDutyDetailBillSettleMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyDetailSettleRequest;
import com.paic.ncbs.claim.model.dto.settle.DutyPayInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ClmsDutyDetailBillSettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service
public class ClmsDutyDetailBillSettleServiceImpl implements ClmsDutyDetailBillSettleService {
    private static final Logger log = LoggerFactory.getLogger(ClmsDutyDetailBillSettleServiceImpl.class);
    @Autowired
    private ClmsDutyDetailBillSettleMapper clmsDutyDetailBillSettleMapper;
    @Autowired
    private DutyDetailPayMapper dutyDetailPayMapper;

    @Autowired
    private PolicyPayMapper policyPayMapper;
    @Autowired
    private DutyPayMapper dutyPayMapper;

    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;

    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;
    @Override
    @Transactional
    public void saveBatch(List<BIllSettleResultDTO> list) {
        List<ClmsDutyDetailBillSettleDTO> savaList = new ArrayList<>();
        savaList = BeanUtil.copyToList(list, ClmsDutyDetailBillSettleDTO.class);
        clmsDutyDetailBillSettleMapper.deleteByReportNo(list.get(0).getReportNo(),list.get(0).getCaseTimes());
        savaList.stream().forEach(clmsDutyDetailBillSettleDTO -> {
            clmsDutyDetailBillSettleDTO.setId(UuidUtil.getUUID());
            clmsDutyDetailBillSettleDTO.setApprovalStatus("0");
            clmsDutyDetailBillSettleDTO.setIsDeleted("0");
            clmsDutyDetailBillSettleDTO.setSettleAmount(clmsDutyDetailBillSettleDTO.getAutoSettleAmount());
            clmsDutyDetailBillSettleDTO.setCreatedBy(StringUtils.isEmptyStr(WebServletContext.getUserId()) ? "system" : WebServletContext.getUserId());
            clmsDutyDetailBillSettleDTO.setUpdatedBy(StringUtils.isEmptyStr(WebServletContext.getUserId()) ? "system" : WebServletContext.getUserId());
        });
        clmsDutyDetailBillSettleMapper.batchSaveData(savaList);
    }

    @Override
    public void updateClmsDutyDetailBillSettle(ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO) {
        List<ClmsDutyDetailBillSettleDTO> clmsDutyDetailBillSettleList = clmsDutyDetailBillSettleMapper.getClmsDutyDetailBillSettleList(clmsDutyDetailBillSettleDTO);
        if(CollectionUtil.isNotEmpty(clmsDutyDetailBillSettleList)) {
            clmsDutyDetailBillSettleMapper.updateClmsDutyDetailBillSettle(clmsDutyDetailBillSettleDTO);
        }
    }

    /**
     * 查询报案号下所有发票理算信息
     * @param policy
     * @return
     */
    @Override
    public List<ClmsDutyDetailBillSettleDTO> getAllInfoByReportNo(PolicyPayDTO policy) {
        ClmsDutyDetailBillSettleDTO queryParamsDto=new ClmsDutyDetailBillSettleDTO();
        queryParamsDto.setReportNo(policy.getReportNo());
        queryParamsDto.setPolicyNo(policy.getPolicyNo());
        queryParamsDto.setCaseTimes(policy.getCaseTimes());
        queryParamsDto.setScene(policy.getScene());
        log.info("queryParamsDto Scene{}",queryParamsDto.getScene());
        List<ClmsDutyDetailBillSettleDTO> dutyDetailBillList=  clmsDutyDetailBillSettleMapper.getAllInfoByReportNo(queryParamsDto);
        log.info("dutyDetailBillList Scene{}",dutyDetailBillList);
        for (ClmsDutyDetailBillSettleDTO dto :dutyDetailBillList) {
           if(Objects.equals(ChecklossConst.AHCS_THERAPY_TWO,dto.getTherapyType())){
               dto.setTherapyType(BaseConstant.STRING_1);
           }
           if(Objects.equals(ChecklossConst.AHCS_THERAPY_ONE,dto.getTherapyType())){
               dto.setTherapyType(BaseConstant.STRING_2);
           }
        }
        return dutyDetailBillList;
    }

    /**
     * 更新责任明细发票理算金额
     * @param detailBillSettleList
     */
    @Override
    @Transactional
    public void updateListById(List<ClmsDutyDetailBillSettleDTO> detailBillSettleList) {
        if(CollectionUtil.isEmpty(detailBillSettleList)){
           return;
        }
        for (ClmsDutyDetailBillSettleDTO dto :detailBillSettleList) {
            clmsDutyDetailBillSettleMapper.updateOneById(dto);
        }

    }

    private BigDecimal getSettleAmount(List<DutyDetailBillSettleDTO> detailBillSettleList) {
        BigDecimal detailSumAmount=BigDecimal.ZERO;
        for (DutyDetailBillSettleDTO dto :detailBillSettleList) {
            detailSumAmount=  detailSumAmount.add(dto.getSettleAmount());
        }
        return detailSumAmount;
    }

    @Override
    public List<ClmsDutyDetailBillSettleDTO> autoAllocation(DutyDetailSettleRequest detail) {
        LogUtil.info("自动分摊报案号={}，入参={}",detail.getReportNo(), JsonUtils.toJsonString(detail));
        //把责任明细的手动理算金额分摊到责任明细下的所有发票上
        if(!Objects.equals("04",detail.getDutyDetailType())){
            return null;
        }
        String caseNo= policyPayMapper.getCaseNo(detail.getReportNo(),detail.getCaseTimes());
        //查询发票理算信息
        List<ClmsDutyDetailBillSettleDTO> clmsDutyDetailBillSettleDTOList =getBillSettleInfoByCondtion(detail);
        //分摊手动理算金额
        autoAllocationSettleAmount(detail,caseNo,clmsDutyDetailBillSettleDTOList);
        autoAllocationRemitAmount(detail,clmsDutyDetailBillSettleDTOList);

        return clmsDutyDetailBillSettleDTOList;

    }

    private void autoAllocationSettleAmount(DutyDetailSettleRequest detail, String caseNo, List<ClmsDutyDetailBillSettleDTO> clmsDutyDetailBillSettleDTOList) {
        DutyDetailPayDTO detailPayDTO = new DutyDetailPayDTO();
        detailPayDTO.setCaseNo(caseNo);
        detailPayDTO.setPolicyNo(detail.getPolicyNo());
        detailPayDTO.setPlanCode(detail.getPlanCode());
        detailPayDTO.setDutyCode(detail.getDutyCode());
        detailPayDTO.setDutyDetailCode(detail.getDutyDetailCode());
        detailPayDTO.setCaseTimes(detail.getCaseTimes());
        BigDecimal amount = dutyDetailPayMapper.getDutyDetailCodeAmount(detailPayDTO);
        //金额不变 不用分摊
        if(nvl(detail.getSettleAmount(),0).compareTo(nvl(amount,0))==0){
            return ;
        }

        //自动分配手动金额
        if(CollectionUtil.isEmpty(clmsDutyDetailBillSettleDTOList)){
            return ;
        }
        BigDecimal settleAmount = detail.getSettleAmount();
        if(Objects.isNull(settleAmount)){
            return ;
        }
        for (ClmsDutyDetailBillSettleDTO dto :clmsDutyDetailBillSettleDTOList) {
            if(Objects.equals("N",dto.getCanModifyFlag())){
               continue;
            }
            StringBuffer remark=new StringBuffer();
            String oldRemark="";
            if(settleAmount.compareTo(BigDecimal.ZERO)<=0){
                dto.setSettleAmount(BigDecimal.ZERO);
                if(StringUtils.isNotEmpty(dto.getRemark())){
                    oldRemark=dto.getRemark()+",";
                }
                remark.append(oldRemark).append(Constants.SETTLE_AMOUNT_REMARK).append("分摊金额为0");
            }else if(settleAmount.compareTo(dto.getReasonableAmount())<=0){
                dto.setSettleAmount(settleAmount);
                if(StringUtils.isNotEmpty(dto.getRemark())){
                    oldRemark=dto.getRemark()+",";
                }
                remark.append(oldRemark).append(Constants.SETTLE_AMOUNT_REMARK).append("分摊金额为").append(settleAmount.toString());
                settleAmount=BigDecimal.ZERO;
            }else{
                dto.setSettleAmount(dto.getReasonableAmount());
                if(StringUtils.isNotEmpty(dto.getRemark())){
                    oldRemark=dto.getRemark()+",";
                }
                remark.append(oldRemark).append(Constants.SETTLE_AMOUNT_REMARK).append("分摊金额为最大合理费用").append(dto.getReasonableAmount().toString());
                settleAmount=settleAmount.subtract(dto.getReasonableAmount());
            }
            dto.setRemark(remark.toString());

        }
        LogUtil.info("自动分摊理算金额报案号={}，责任明细编码={},分摊结果={}",detail.getReportNo(),detail.getDutyDetailCode(), JsonUtils.toJsonString(clmsDutyDetailBillSettleDTOList));
    }

    /**
     * 查询责任明细下的发票
     * @param detail
     * @return
     */
    private List<ClmsDutyDetailBillSettleDTO> getBillSettleInfoByCondtion(DutyDetailSettleRequest detail) {
//        List<ClmsDutyDetailBillSettleDTO> list =  clmsDutyDetailBillSettleMapper.getBillSettleInfoByCondition(detail);
        List<ClmsDutyDetailBillSettleDTO> list = detail.getDetailBillSettleList();
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<ClmsDutyDetailBillSettleDTO>();
        }
        for (ClmsDutyDetailBillSettleDTO dto : list) {
            dto.setCanModifyFlag("Y");
            dto.setRemark("");
        }
        return list;
    }

    public void autoAllocationRemitAmount(DutyDetailSettleRequest detail, List<ClmsDutyDetailBillSettleDTO> clmsDutyDetailBillSettleDTOList) {
        LogUtil.info("自动分摊免赔额报案号={},入参={}",detail.getReportNo(),JsonUtils.toJsonString(clmsDutyDetailBillSettleDTOList));
        BigDecimal sumitAmount =getSumitAmount(clmsDutyDetailBillSettleDTOList);
        //免赔金额不变 不用分摊
        if(nvl(detail.getRemitAmount(),0).compareTo(sumitAmount)==0){
            return ;
        }

        //自动分配手动金额
        if(CollectionUtil.isEmpty(clmsDutyDetailBillSettleDTOList)){
            return ;
        }

        BigDecimal remitAmount = detail.getRemitAmount();
        if(Objects.isNull(remitAmount)){
            return;
        }
        for (ClmsDutyDetailBillSettleDTO dto :clmsDutyDetailBillSettleDTOList) {
            if(Objects.equals("N",dto.getCanModifyFlag())){
                continue;
            }
            StringBuffer remark=new StringBuffer();
            String oldRemark="";
            Map<String, Object> calparamsMap = new LinkedHashMap<>();
            if(remitAmount.compareTo(BigDecimal.ZERO)<=0){
                dto.setRemitAmount(BigDecimal.ZERO);
                if(!StringUtils.isEmptyStr(dto.getRemark())){
                    oldRemark=dto.getRemark()+",";
                }
                remark.append(oldRemark).append(Constants.REMIT_AMOUNT_REMARK).append("分摊免赔额为0");
            }else if(remitAmount.compareTo(dto.getReasonableAmount())<=0){
                dto.setRemitAmount(remitAmount);
                if(!StringUtils.isEmptyStr(dto.getRemark())){
                    oldRemark=dto.getRemark()+",";
                }
                remark.append(dto.getRemark()).append(Constants.REMIT_AMOUNT_REMARK).append("分摊免赔额为").append(remitAmount.toString());
                remitAmount=BigDecimal.ZERO;
            }else{
                dto.setRemitAmount(dto.getReasonableAmount());
                if(!StringUtils.isEmptyStr(dto.getRemark())){
                    oldRemark=dto.getRemark()+",";
                }
                remark.append(dto.getRemark()).append(Constants.REMIT_AMOUNT_REMARK).append("分摊免赔额为最大合理费用").append(dto.getReasonableAmount().toString());
                remitAmount=remitAmount.subtract(dto.getReasonableAmount());
            }
            dto.setRemark(remark.toString());

        }
        LogUtil.info("自动分摊免赔额报案号={},责任明细编码={}，分摊结果={}",detail.getReportNo(),detail.getDutyDetailCode(),JsonUtils.toJsonString(clmsDutyDetailBillSettleDTOList));
    }


    /**
     *
     * @param list
     * @return
     */
    private BigDecimal getSumitAmount(List<ClmsDutyDetailBillSettleDTO> list) {
        BigDecimal remitAmount=BigDecimal.ZERO;
        for (ClmsDutyDetailBillSettleDTO dto :list) {
            remitAmount=remitAmount.add(nvl(dto.getRemitAmount(),0));
        }
        return remitAmount;
    }
    private BigDecimal calulateAmount(Expression expression, Map<String, Object> calparamsMap, ClmsDutyDetailBillSettleDTO dto) {
        calparamsMap.put("reasonableAmount",dto.getReasonableAmount());
        calparamsMap.put("remitAmount",dto.getRemitAmount());
        calparamsMap.put("payProportion",dto.getPayProportion());
        BigDecimal result = (BigDecimal) expression.execute(calparamsMap);//理算金额
        if (result.compareTo(BigDecimal.ZERO) < 0) {
            result=BigDecimal.ZERO;
        }
        return result;
    }

    @Override
    public void checkRemimount(String reportNo, Integer caseTimes) {
        //获取当前报案号的最终理算金额

        List<ClmsDutyDetailBillSettleDTO> currentReportList= clmsDutyDetailBillSettleMapper.getCurrentPolicyDutyBillSettleInfo(reportNo,caseTimes);
        if(CollectionUtils.isEmpty(currentReportList)){
            return;
        }

        List<DutyAttributeValueDTO> dutyAttributeValueDTOList = dutyAttributeMapper.getDutyAttributeRemiamount(reportNo);
        if(CollectionUtil.isEmpty(dutyAttributeValueDTOList)){
            return;
        }

        PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(currentReportList.get(0).getPolicyNo(),null);
        for (ClmsDutyDetailBillSettleDTO settleBill :currentReportList) {
            //查询本次报案责任所抵扣免赔额
            if(nvl(settleBill.getRemitAmount(),0).compareTo(BigDecimal.ZERO)==0){
                continue;
            }

            List<DutyAttributeValueDTO> attlist =  dutyAttributeValueDTOList.stream().filter(dutyAttributeValueDTO ->Objects.equals(settleBill.getPolicyNo(),dutyAttributeValueDTO.getPolicyNo()) && Objects.equals(settleBill.getPlanCode(),dutyAttributeValueDTO.getPlanCode()) && Objects.equals(settleBill.getDutyCode(),dutyAttributeValueDTO.getDutyCode())).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(attlist)){
               continue;
            }
            Map<String,String>  attribueMap= getattribueMap(attlist);
            if(!(attribueMap.containsKey(DutyAttributeConst.REMIT_AMOUNT_TYPE) && Objects.equals("1",attribueMap.get(DutyAttributeConst.REMIT_AMOUNT_TYPE)) && attribueMap.containsKey(DutyAttributeConst.REMIT_AMOUNT))){
                LogUtil.info("责任编码={}没有配置了年免赔额,详细属性配置={}",settleBill.getDutyCode(), JsonUtils.toJsonString(attribueMap));
                continue;
            }
            LogUtil.info("责任编码={}配置了年免赔额,详细属性配置={}",settleBill.getDutyCode(), JsonUtils.toJsonString(attribueMap));
            String configRemiamount = attribueMap.get(DutyAttributeConst.REMIT_AMOUNT);

            //查询责任已使用的免赔额
            BigDecimal usedRemiamount = getDutyUsedRemiamount(settleBill,policyDto,reportNo,caseTimes);
            //剩余免赔额
            BigDecimal reduseRemiamount= BigDecimalUtils.getBigDecimal(configRemiamount).subtract(usedRemiamount);
            LogUtil.info("报案号={},责任编码={},本次报案抵扣免赔额={},历史抵扣免赔额={},责任配置年免赔={}",reportNo,settleBill.getDutyCode(),BigDecimalUtils.toString(settleBill.getBillAmount()),BigDecimalUtils.toString(usedRemiamount),configRemiamount);
            if(reduseRemiamount.compareTo(BigDecimal.ZERO)<=0){
                throw new GlobalBusinessException("责任"+settleBill.getDutyCode()+"免赔额已抵扣完，本次理算不能再抵扣，请退回重新理算！");
            }else {
                if(reduseRemiamount.compareTo(nvl(settleBill.getRemitAmount(),0))<0){
                    throw new GlobalBusinessException("责任"+settleBill.getDutyCode()+"本次理算抵扣免赔额"+settleBill.getRemitAmount()+"超剩余年免赔"+reduseRemiamount+"请重新理算！");
                }
            }

        }

    }

    private BigDecimal getDutyUsedRemiamount(ClmsDutyDetailBillSettleDTO duty, PolicyDto policyDto, String reportNo, Integer caseTimes) {
        BigDecimal usedRemiamount=BigDecimal.ZERO;
        RemitAmountDTO queryParams=new RemitAmountDTO();
        queryParams.setPolicyNo(duty.getPolicyNo());
        queryParams.setPlanCode(duty.getPlanCode());
        queryParams.setDutyCode(duty.getDutyCode());
        queryParams.setInsuranceBeginDate(DateUtils.dateFormat(policyDto.getPolicyStartDate(),DateUtils.FULL_DATE_STR));
        queryParams.setInsuranceEndDate(DateUtils.dateFormat(policyDto.getPolicyEndDate(),DateUtils.FULL_DATE_STR));
        LogUtil.info("报案号={},责任编码={},查询已结案抵扣免赔额度入参={}",reportNo,duty.getDutyCode(),JsonUtils.toJsonString(queryParams));
        List<ClmsDutyDetailBillSettleDTO> usedDtoList =clmsDutyDetailBillSettleMapper.getDutyUsedRemitAmount(queryParams);
        LogUtil.info("报案号={},责任编码={},查询已结案抵扣免赔额度结果={}",reportNo,duty.getDutyCode(),JsonUtils.toJsonString(usedDtoList));
        if(CollectionUtil.isEmpty(usedDtoList)){
            return  usedRemiamount;
        }

        Map<String,List<ClmsDutyDetailBillSettleDTO>> mapList =usedDtoList.stream().collect(Collectors.groupingBy(ClmsDutyDetailBillSettleDTO::getReportNo));
        LogUtil.info("报案号={},责任编码={},查询已结案抵扣免赔额度结果按报案号分组={}",reportNo,duty.getDutyCode(),JsonUtils.toJsonString(mapList));

        for (Map.Entry<String,List<ClmsDutyDetailBillSettleDTO>> entry :mapList.entrySet()) {

            List<ClmsDutyDetailBillSettleDTO> sortedList =  entry.getValue().stream().sorted(Comparator.comparing(ClmsDutyDetailBillSettleDTO::getCaseTimes).reversed()).collect(Collectors.toList());
            if(caseTimes>1){
               if(Objects.equals(reportNo,entry.getKey())){
                 continue;
               }
            }
            usedRemiamount=usedRemiamount.add(nvl(sortedList.get(0).getRemitAmount(),0));
        }
        LogUtil.info("报案号={},责任编码={},查询已结案抵扣免赔额={}",reportNo,duty.getDutyCode(),JsonUtils.toJsonString(usedRemiamount));

        return usedRemiamount;

    }

    private BigDecimal getDutyRemiAmountByDutyCode(ClmsDutyDetailBillSettleDTO duty, String reportNo, Integer caseTimes) {
        RemitAmountDTO remitAmountDTO=new RemitAmountDTO();
        remitAmountDTO.setReportNo(reportNo);
        remitAmountDTO.setCaseTimes(caseTimes);
        remitAmountDTO.setPolicyNo(duty.getPolicyNo());
        remitAmountDTO.setPlanCode(duty.getPlanCode());
        remitAmountDTO.setDutyCode(duty.getDutyCode());
        BigDecimal  remiAmount = clmsDutyDetailBillSettleMapper.getClmsDutyDetailByDutyCode(remitAmountDTO);
        return nvl(remiAmount,0);
    }

    private Map<String,String> getattribueMap(List<DutyAttributeValueDTO> dtoList) {
        Map<String,String> attribueMap=new HashMap<>();
        for (DutyAttributeValueDTO dto : dtoList) {
            attribueMap.put(dto.getAttributeCode(), dto.getAttributeValue());
        }
        return attribueMap;
    }
}
