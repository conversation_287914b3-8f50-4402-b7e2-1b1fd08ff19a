package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.duty.ReportDutyDetailVo;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.policy.dto.PlanDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface AhcsPolicyInfoMapper extends BaseDao<AhcsPolicyInfoEntity> {


    void insertList(@Param("list") List<AhcsPolicyInfoEntity> list);

    void deleteByReportNoAndPolicyNo(@Param("policyNo") String policyNo, @Param("reportNo") String reportNo);

    String selectDepartmentCodeByReportNo(@Param("reportNo") String reportNo);

    String selectDepartmentNameByReportNo(@Param("reportNo") String reportNo);

    List<String> getReportNoByPolicyNo(@Param("policyNo") String policyNo);

    List<String> getPolicyNoByReportNo(@Param("reportNo") String reportNo);

    /**
     * 根据报案号查询保单号
     * @param reportNo
     * @return
     */
    String getOnePolicyNoByReportNo(@Param("reportNo") String reportNo);

    /**
     * 根据报案号查询产品名称
     * @param reportNo
     * @return
     */
    String getProductNameByReportNo(@Param("reportNo") String reportNo);

    String getDepartmentNameByReportNo(@Param("reportNo") String reportNo);

    List<AhcsPolicyInfoEntity> selectByReportNo(@Param("reportNo") String reportNo);

    /**
     * 查询续保保单号
     * @param reportNo
     * @return
     */
    String getLastPolicyNo(String reportNo);

    List<Map<String, String>> getProductInfo(Map<String, String> map);

    /**
     * 查询保单起止日期
     * @param policyNo
     * @return
     */
    AhcsPolicyInfoEntity getPolicyStartAndEnddate(String reportNo,String policyNo);

    AhcsPolicyInfoEntity getPolicyProductCode(String reportNo,String policyNo);

    String getProductClassByReportNo(@Param("reportNo") String reportNo);

    List<ReportDutyDetailVo> getDutyDetails(@Param("reportNo") String reportNo);

    OcasPolicyPayDTO getpolicyInfoForPay(@Param("reportNo") String reportNo,@Param("clientNo") String clientNo);

    Map<String, String> getPlySaleByPolicyNo(@Param("policyNo") String policyNo,@Param("reportNo")String reportNo);

    Map<String, String> getPlyBaseInfo(@Param("reportNo") String reportNo,@Param("policyNo")String policyNo);

    PolicyInfoDTO getPolicyBaseInfo(@Param("reportNo") String reportNo);

    PlanDTO selectTaxRate(@Param("reportNo") String reportNo, @Param("paySerialNo") String paySerialNo);
}