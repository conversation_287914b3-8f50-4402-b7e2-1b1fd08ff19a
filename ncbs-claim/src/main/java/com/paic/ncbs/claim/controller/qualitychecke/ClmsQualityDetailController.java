package com.paic.ncbs.claim.controller.qualitychecke;

import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail;
import com.paic.ncbs.claim.service.qualitychecke.ClmsQualityDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 质检意见明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@RestController
@RequestMapping("/clmsQualityDetail")
public class ClmsQualityDetailController {
    @Autowired
    ClmsQualityDetailService clmsQualityDetailService;

    @RequestMapping("/selectQualityDetailByCondition")
    public List<ClmsQualityDetail> selectQualityDetailByCondition(ClmsQualityDetail condition) {
        return clmsQualityDetailService.selectQualityDetailByCondition(condition);
    }

    @PostMapping("/selectQualityDetailsBySerialNo")
    public List<ClmsQualityDetail> selectQualityDetailsBySerialNo(@RequestParam String serialNo) {
        return clmsQualityDetailService.selectQualityDetailsBySerialNo(serialNo);
    }

    @PostMapping("/insertQualityDetail")
    public int insertQualityDetail(ClmsQualityDetail clmsQualityDetail) {
        return clmsQualityDetailService.insertQualityDetail(clmsQualityDetail);
    }

    @PostMapping("/deleteQualityDetailById")
    public int deleteQualityDetailById(@RequestParam String id) {
        return clmsQualityDetailService.deleteQualityDetailById(id);
    }

    @PostMapping("/updateQualityDetail")
    public int updateQualityDetail(ClmsQualityDetail clmsQualityDetail) {
        return clmsQualityDetailService.updateQualityDetail(clmsQualityDetail);
    }
}
