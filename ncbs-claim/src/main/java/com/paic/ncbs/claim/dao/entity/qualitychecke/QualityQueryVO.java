package com.paic.ncbs.claim.dao.entity.qualitychecke;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(description = "质检信息查询参数")
public class QualityQueryVO {

    @ApiModelProperty("处理机构")
    private String handleCom;

    @ApiModelProperty("案件性质")
    private String isProblemCase;

    @ApiModelProperty("最小赔款金额")
    private BigDecimal minCaseAmount;

    @ApiModelProperty("最大赔款金额")
    private BigDecimal maxCaseAmount;

    @ApiModelProperty("最小结案日期")
    private LocalDateTime minCloseDate;

    @ApiModelProperty("最大结案日期")
    private LocalDateTime maxCloseDate;

    @ApiModelProperty("理赔产品")
    private String[] productName;

    @ApiModelProperty("质检处理人")
    private String qinspector;

    @ApiModelProperty("最小质检发起时间")
    private LocalDateTime minStartTime;

    @ApiModelProperty("最大质检发起时间")
    private LocalDateTime maxStartTime;

    @ApiModelProperty("最小质检结束时间")
    private LocalDateTime minEndTime;

    @ApiModelProperty("最大质检结束时间")
    private LocalDateTime maxEndTime;

    @ApiModelProperty("任务状态")
    private String taskStatus;

    @ApiModelProperty("差错等级")
    private String errorLevel;

    @ApiModelProperty("除外产品")
    private String[] excludeProductName;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Short caseTimes;

    @ApiModelProperty("质检任务")
    private String id;
}

