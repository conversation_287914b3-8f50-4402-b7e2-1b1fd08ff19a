package com.paic.ncbs.claim.model.dto.copypolicy;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class GlobalReportDto {
    private String rc00RcivType; //案件类型
    private String accdDate; //事故日期 格式：YYYYMMDD
    private String accdTime; //事故时间 格式：HHmmss
    private String lossLocAreaCode; //出险地区码值
    private String lossLocDetl; //详细事故地点
    private String claimDescription; //事故描述
    private String ntfCls; //报案人与被保险人关系
    private String ntfCmpName; //报案人公司名称
    private String ntfName; //报案人名称
    private String ntfIdType; //报案人证件类型
    private String contactNo21; //手机号1-4位
    private String contactNo22; //手机号第5~8位
    private String contactNo23; //手机号第9~11位
    private String contactNo24; //默认空格
    private String email; //邮箱
    private String referAgrmDate; //和事故日期同值
    private String agrmNo; //保单号
    private String pibojaId; //出险人Code
    private String pibojaName; //出险人名称
    private String pibojaIdentifyNo; //出险人证件号码
    private String contractorCd; //投保人代码
    private String contractorName; //投保人名称
    private BigDecimal requestedMny; //索赔金额
    private String bankNm; //收款账户开户行名称
    private String bankNo; //收款账户号
    private String bankAccountName; //收款账户名称
    private String relationOfBankAndPibojia; //收款账户和被保险人关系
    private String otherReportCompanyYn; //是否重复投保
    private String otherReportCompany; //投保的其他保险公司名称
    private String formerlyMedicalHistoryYn; //是否有既往病史
    private String formerlyMedicalHistoryDate; //确诊日期
    private String deathYN; //是否死亡 （Y-是 N-否）
    private String firstAidYN; //是否急救（Y-是 N-否）
    private String sendTextYN; //是否给理赔人发短信（Y-是 N-否）
    private String fractureYN; //是否骨折（Y-是 N-否）
    private String highFallYN; //是否高坠（Y-是 N-否）
    private String rc00RcivYear; //报案号 年
    private Long rc00RcivSeqNum; //报案号
    private String claimReportNo; //新理赔报案号
    //private List<MedicalDetail> medicalDetailList; //药品明细 非必录 不考虑先
    //private List<ContactDetail> contactDetailList;//联系人 非必录 不考虑先
}
