package com.paic.ncbs.claim.service.reinsurance;

import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import com.paic.ncbs.claim.model.dto.reinsurance.ReinsuranceRateDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.ReinsuranceReqDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.vo.reinsurance.RecipientsVO;
import com.paic.ncbs.claim.model.vo.reinsurance.ReinsApiVO;

import java.util.Date;
import java.util.List;

/**
 * 再保服务
 */
public interface ReinsuranceService {

    /**
     * 补推
     * @param dto
     */
    void compensate(RepayCalDTO dto);

    /**
     * 理赔环节送再保-异步线程
     * @param dto
     */
    void sendReinsurance(RepayCalDTO dto);

    /**
     * 理赔环节送再保-同步线程
     * @param dto
     */
    void sendReinsuranceOnSync(RepayCalDTO dto);
    /**
     * 获取最新再保信息
     * @return
     */
    List<ReinsuranceRateDTO> getReinsuranceInfo(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType);
    /**
     * 获取再保联系人
     */
    List<RecipientsVO> getReinsuranceContact(String reinsCode);
    /**
     * 获取URL
     */
    String getReinsURL(String policyNo, Date accidentDate);
    /**
     * 邮件发送
     */
    String sendReinsEmail(ReinsApiVO reinsApiVO);
    /**
     * 获取临分再保信息
     */
    List<ReinsuranceRateDTO> getReinsuranceFInfo(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType);
    /**
     * 获取再保信息
     */
    ReinsuranceReqDTO buildReinsuranceReqDTO(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType);
    /**
     * 获取再保信息
     */
    List<ReinsuranceRateDTO> getReinsuranceInfo1(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType);
}
