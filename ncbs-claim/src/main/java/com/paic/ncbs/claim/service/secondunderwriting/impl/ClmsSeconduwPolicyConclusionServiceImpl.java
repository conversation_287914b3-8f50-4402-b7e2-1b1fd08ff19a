package com.paic.ncbs.claim.service.secondunderwriting.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.enums.PolicyStatusEnum;
import com.paic.ncbs.claim.common.enums.UwConclusionTypeEnum;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPolicyConclusionEntity;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSeconduwPolicyConclusionMapper;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsSeconduwPolicyConclusionVO;
import com.paic.ncbs.claim.model.vo.senconduw.RiskConclusionVO;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSeconduwPolicyConclusionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 理赔二核保单层核保结论表(ClmsSeconduwPolicyConclusionEntity)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-14 14:07:14
 */
@Service("clmsSeconduwPolicyConclusionService")
public class ClmsSeconduwPolicyConclusionServiceImpl implements ClmsSeconduwPolicyConclusionService {
    @Resource
    private ClmsSeconduwPolicyConclusionMapper clmsSeconduwPolicyConclusionMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsSeconduwPolicyConclusionEntity queryById(String id) {
        return this.clmsSeconduwPolicyConclusionMapper.queryById(id);
    }
    
    /**
     * 通过报案号查询单条数据
     *
     * @param reportNo caseTimes主键
     * @return 实例对象
     */
     @Override
    public ClmsSeconduwPolicyConclusionEntity queryByReportNo(String reportNo, int caseTimes){
        return this.clmsSeconduwPolicyConclusionMapper.queryByReportNo(reportNo,caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsSeconduwPolicyConclusionEntity 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsSeconduwPolicyConclusionEntity insert(ClmsSeconduwPolicyConclusionEntity clmsSeconduwPolicyConclusionEntity) {
        this.clmsSeconduwPolicyConclusionMapper.insert(clmsSeconduwPolicyConclusionEntity);
        return clmsSeconduwPolicyConclusionEntity;
    }

    /**
     * 修改数据
     *
     * @param clmsSeconduwPolicyConclusionEntity 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsSeconduwPolicyConclusionEntity update(ClmsSeconduwPolicyConclusionEntity clmsSeconduwPolicyConclusionEntity) {
        this.clmsSeconduwPolicyConclusionMapper.update(clmsSeconduwPolicyConclusionEntity);
        return this.queryById(clmsSeconduwPolicyConclusionEntity.getId());
    }
    
    /**
     * 根据报案号修改数据
     *
     * @param reportNo
     * @param caseTimes 实例对象
     * @return 影响行数
     */
    @Override
    public int updateByReportNo( String reportNo, int caseTimes) {
        return this.clmsSeconduwPolicyConclusionMapper.updateByReportNo(reportNo,caseTimes);
    }
    

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsSeconduwPolicyConclusionMapper.deleteById(id) > 0;
    }

    /**
     * 批量保存保单层级核保结论信息
     * @param entityList
     */
    @Override
    public void saveBatch(List<ClmsSeconduwPolicyConclusionEntity> entityList) {
        clmsSeconduwPolicyConclusionMapper.insertBatch(entityList);
    }

    /**
     * 根据理赔二核申请表主键查询
     * @param idClmsSecondUnderwriting
     * @return
     */
    @Override
    public List<ClmsSeconduwPolicyConclusionVO> getPolicyConclusionVOList(String idClmsSecondUnderwriting) {
        List<ClmsSeconduwPolicyConclusionVO> resultVoList =  clmsSeconduwPolicyConclusionMapper.getPolicyConclusionVOList(idClmsSecondUnderwriting);
        if(CollectionUtil.isEmpty(resultVoList)){
            return  new ArrayList<ClmsSeconduwPolicyConclusionVO>();
        }
        for (ClmsSeconduwPolicyConclusionVO vo : resultVoList) {
            //保单层级核保结论 编码转换为汉字
            vo.setUwConclusion(UwConclusionTypeEnum.getName(vo.getUwConclusion()));
            //保单状态转换为汉字
            vo.setPolicyStatus(PolicyStatusEnum.getName(vo.getPolicyStatus()));
            for (RiskConclusionVO riskVo : vo.getRiskConclusionList()) {
                //险种层核保结论转换为汉字
                riskVo.setUwDecisions(UwConclusionTypeEnum.getName(riskVo.getUwDecisions()));
            }
        }
        return  resultVoList;
    }
}
