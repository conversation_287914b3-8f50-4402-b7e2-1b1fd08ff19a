package com.paic.ncbs.claim.service.fileupload;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fileupload.FileViewRecordDTO;

public interface FileViewRecordService {
	

	public Integer getFileViewRecord(FileViewRecordDTO fileViewRecordDTO) throws GlobalBusinessException;

	public void addFileViewRecord(FileViewRecordDTO fileViewRecordDTO)throws GlobalBusinessException;

}
