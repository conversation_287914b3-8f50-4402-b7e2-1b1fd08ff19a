package com.paic.ncbs.claim.controller.doc;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.Transform;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.doc.CustomerSupplementsDTO;
import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.fileupload.DocumentTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;


@Api(tags = "单证信息")
@RestController
@RequestMapping("/doc/app/documentAction")
@Validated
public class DocumentController extends BaseController {

    @Autowired
    DocumentTypeService documentTypeService;
    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;

    @ApiOperation("获取单证类型")
    @PostMapping(value = "/getAllDocumentTypeList")
    public ResponseResult<Object> getAllDocumentTypeList(@RequestBody FileInfoVO fileInfoVO) throws GlobalBusinessException {
        return ResponseResult.success(documentTypeService.getDocumentAllTypeList());
    }

    @ApiOperation("客户补材")
    @PostMapping(value = "/customerSupplements")
    public ResponseResult customerSupplements(@Valid @RequestBody CustomerSupplementsDTO customerSupplementsDTO){
        LogUtil.audit("DocumentController customerSupplements客户补材 reportNo ={},入参={}",customerSupplementsDTO.getReportNo(), JSONObject.toJSONString(customerSupplementsDTO));
        if(CollectionUtils.isEmpty(customerSupplementsDTO.getMediaList())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "补充单证类型为空");
        }
        documentTypeService.customerSupplements(customerSupplementsDTO);
        //通知TPA：只有报案跟踪节点操做了客户补材才会通知TPA，只有谐筑半包的案件发送MQ消息
        ReportInfoExEntity reportInfoExEntity = reportInfoExMapper.getAcceptanceNoByReportNo(customerSupplementsDTO.getReportNo());
        if(Objects.equals(BpmConstants.OC_REPORT_TRACK,customerSupplementsDTO.getTaskDefinitionBpmKey()) && reportInfoExEntity != null
                && Objects.equals("2",reportInfoExEntity.getClaimDealWay())  &&  Objects.equals("42300010",reportInfoExEntity.getCompanyId())){
            claimSendTpaMqInfoService.sendTpaMq(customerSupplementsDTO.getReportNo(),customerSupplementsDTO.getCaseTimes(), Transform.getSupplementsCaseProcessStatus(customerSupplementsDTO.getTaskDefinitionBpmKey()));
        }
      return ResponseResult.success();
    }
    /**
     * 客户补材任务查询
     * @return
     */
    @ApiOperation("客户补材任务查询")
    @GetMapping(value = "/getCustomerSupplements/{reportNo}/{caseTimes}")
    public ResponseResult<List<SupplementsMaterialDTO>> getCustomerSupplements(@PathVariable String reportNo, @PathVariable Integer caseTimes){
        List<SupplementsMaterialDTO> resultList = documentTypeService.getCustomerSupplements(reportNo,caseTimes);
        return ResponseResult.success(resultList);
    }
    /**
     * 测试客户补材回调工作流，更新工作流任务
     * @return
     */
    @ApiOperation("客户补材回调")
    @PostMapping(value = "/customerSupplements/submit")
    public ResponseResult submitCustomerSupplements(@RequestBody CustomerSupplementsDTO dto){
        documentTypeService.customerSupplementsSuccess(dto.getReportNo(), dto.getCaseTimes(), dto.getRemark());
        return ResponseResult.success();
    }


}
