package com.paic.ncbs.claim.service.antimoneylaundering;

import com.paic.ncbs.claim.model.dto.antimoneylaundering.AmlCheckResultDTO;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlAuditResult;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlAuditResultList;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlSendRecordVO;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;

import java.util.List;

public interface ClmsSendAmlRecordService {

    /**
     * 反洗钱可疑数据校验
     * @param dto
     */
    AmlCheckResultDTO checkPaymentItem(SettlesFormVO dto);

    /**
     * 获取可疑交易反洗钱审批记录
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return
     */
    List<AmlSendRecordVO> getRecordList(String reportNo, Integer caseTimes);

    /**
     * 接收反洗钱可疑数据审核结果
     * @param amlAuditResult
     */
    void auditResult(AmlAuditResult amlAuditResult);
}
