package com.paic.ncbs.claim.service.secondunderwriting.impl;

import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPlanConclusionEntity;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSeconduwPlanConclusionMapper;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSeconduwPlanConclusionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 理赔二核险种层核保结论表(ClmsSeconduwPlanConclusionEntity)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-14 14:07:13
 */
@Service("clmsSeconduwPlanConclusionService")
public class ClmsSeconduwPlanConclusionServiceImpl implements ClmsSeconduwPlanConclusionService {
    @Resource
    private ClmsSeconduwPlanConclusionMapper clmsSeconduwPlanConclusionMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsSeconduwPlanConclusionEntity queryById(String id) {
        return this.clmsSeconduwPlanConclusionMapper.queryById(id);
    }
    
    /**
     * 通过报案号查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
     @Override
    public ClmsSeconduwPlanConclusionEntity queryByReportNo(String reportNo, int caseTimes){
        return this.clmsSeconduwPlanConclusionMapper.queryByReportNo(reportNo,caseTimes);
    }

    /**
     * 新增数据
     *
     * @param clmsSeconduwPlanConclusionEntity 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsSeconduwPlanConclusionEntity insert(ClmsSeconduwPlanConclusionEntity clmsSeconduwPlanConclusionEntity) {
        this.clmsSeconduwPlanConclusionMapper.insert(clmsSeconduwPlanConclusionEntity);
        return clmsSeconduwPlanConclusionEntity;
    }

    /**
     * 批量保存
     * @param entityList
     */
    @Override
    public void saveBatch(List<ClmsSeconduwPlanConclusionEntity> entityList) {
        clmsSeconduwPlanConclusionMapper.insertBatch(entityList);
    }

    /**
     * 修改数据
     *
     * @param clmsSeconduwPlanConclusionEntity 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsSeconduwPlanConclusionEntity update(ClmsSeconduwPlanConclusionEntity clmsSeconduwPlanConclusionEntity) {
        this.clmsSeconduwPlanConclusionMapper.update(clmsSeconduwPlanConclusionEntity);
        return this.queryById(clmsSeconduwPlanConclusionEntity.getId());
    }
    
    /**
     * 根据报案号修改数据
     *
     * @param clmsSeconduwPlanConclusion 实例对象
     * @return 影响行数
     */
    @Override
    public int updateByReportNo( String reportNo, int caseTimes) {
        return this.clmsSeconduwPlanConclusionMapper.updateByReportNo(reportNo,caseTimes);
    }
    

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsSeconduwPlanConclusionMapper.deleteById(id) > 0;
    }
}
