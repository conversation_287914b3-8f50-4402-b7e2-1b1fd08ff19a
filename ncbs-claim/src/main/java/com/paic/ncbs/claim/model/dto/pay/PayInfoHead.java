package com.paic.ncbs.claim.model.dto.pay;

import com.paic.ncbs.claim.common.util.UuidUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
  *
  * @Description
  * <AUTHOR>
  * @Date 2023/4/21 9:42
  **/
@ApiModel("理赔送收付接口")
@Data
public class PayInfoHead {

    // 首次调用收付费
    public static final String TYPE_Q02 ="Q02";
    // 支付修改
    public static final String TYPE_Q09 ="Q09";
    // 价税分离
    public static final String TYPE_Q13 ="Q13";
    //指令支付通知
    public static final String TYPE_Q24 ="Q24";
    //修改结算批次进项发票
    public static final String TYPE_Q25 ="Q25";
    //电子回单查询接口
    public static final String TYPE_Q31 ="Q31";
    //理赔批量结算接口
    public static final String TYPE_Q18 ="Q18";
    //理赔批量支付修改接口
    public static final String TYPE_Q39 ="Q39";
    //流水查询接口
    public static final String TYPE_C01 ="C01";
    //解冻，冻结
    public static final String TYPE_C02 ="C02";
    //收费确认上报接口
    public static final String TYPE_C03 ="C03";
    //通知接口
    public static final String TYPE_N01 ="N01";

    @ApiModelProperty("passWord")
    private String passWord;

    @ApiModelProperty("requestType")
    private String requestType;

    @ApiModelProperty("requestId")
    private String requestId = UuidUtil.getUUID();

    @ApiModelProperty("userCode")
    private String userCode;
}
