package com.paic.ncbs.claim.controller.pet;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.pet.PetInjureVO;
import com.paic.ncbs.claim.service.pet.PetInjureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "宠物损伤")
@RestController
@RequestMapping("/pet/petInjureAction")
public class PetInjureController {

	@Autowired
	private PetInjureService petInjureService;

	@ApiOperation("保存宠物损伤")
	@PostMapping("/addPetInjure")
	public ResponseResult addPetInjure(@RequestBody PetInjureVO petInjureVO){
		if(StringUtils.isEmptyStr(petInjureVO.getReportNo())){
			throw new GlobalBusinessException("报案号不能为空");
		}

		if(petInjureVO.getCaseTimes() == null){
			throw new GlobalBusinessException("赔付次数不能为空");
		}
		petInjureService.addPetInjure(petInjureVO);
		return ResponseResult.success();
	}

	@ApiOperation("获取宠物损伤")
	@PostMapping("/getPetInjure")
	public ResponseResult<PetInjureVO> getPetInjure(@RequestBody PetInjureVO petInjureVO) {
		if(StringUtils.isEmptyStr(petInjureVO.getReportNo())){
			throw new GlobalBusinessException("报案号不能为空");
		}

		if(petInjureVO.getCaseTimes() == null){
			throw new GlobalBusinessException("赔付次数不能为空");
		}
		return ResponseResult.success(petInjureService.getPetInjure(petInjureVO));
	}

}

