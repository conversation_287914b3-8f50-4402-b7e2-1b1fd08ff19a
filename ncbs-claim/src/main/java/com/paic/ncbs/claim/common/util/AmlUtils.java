package com.paic.ncbs.claim.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * global反洗钱相关转换率
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AmlUtils {

    /**
      *
      * @Description 机构代码转换
      * <AUTHOR>
      * @Date 2023/10/17 15:35
      **/
    public static String dptToBranchCd(String departmentCode){
        String branchCd;
        switch (departmentCode) {
            case "776":
                branchCd= "02";
                break;
            case "814":
                branchCd= "03";
                break;
            case "808":
                branchCd= "04";
                break;
            case "785":
                branchCd= "05";
                break;
            case "793":
                branchCd= "06";
                break;
            case "802":
                branchCd= "07";
                break;
            default:
                return "01";
        }
        return branchCd;
    }

    /**
     *
     * @Description 机构代码转换
     * <AUTHOR>
     * @Date 2023/10/17 15:35
     **/
    public static String toManageCom(String departmentCode){
        String manageCom;
        switch (departmentCode) {
            case "776":
                manageCom= "北京分公司";
                break;
            case "814":
                manageCom= "天津分公司";
                break;
            case "808":
                manageCom= "深圳分公司";
                break;
            case "785":
                manageCom= "江苏分公司";
                break;
            case "793":
                manageCom= "青岛分公司";
                break;
            case "802":
                manageCom= "陕西分公司";
                break;
            default:
                return "上海(总部)";
        }
        return manageCom;
    }

    public static String toCertificateNo(String certificateType) {
        String type;
        switch (certificateType){
            case "01":
                type = "110001";
                break;
            case "02":
                type = "110023";
                break;
            case "05":
                type = "119999";
                break;
            case "06":
                type = "110019";
                break;
            case "08":
                type = "110029";
                break;
            case "10":
                type = "110021";
                break;
            case "12":
                type = "110013";
                break;
            case "13":
                type = "110005";
                break;
            case "14":
                type = "110027";
                break;
            case "95":
                type = "119999";
                break;
            case "71":
                type = "610001";
                break;
            case "72":
                type = "610051";
                break;
            case "73":
                type = "619999";
                break;
            case "74":
                type = "610047";
                break;
            case "75":
                type = "610099";
                break;
            default:
                return "@N";
        }
        return type;
    }

    public final static HashMap<String, String> RelationToAppntMap = new HashMap<String, String>();
    static{
        RelationToAppntMap.put("1", "本人");
        RelationToAppntMap.put("2", "配偶");
        RelationToAppntMap.put("9", "其他");
        RelationToAppntMap.put("I", "子女");
        RelationToAppntMap.put("J", "父母");
        RelationToAppntMap.put("05", "兄弟姐妹");
        RelationToAppntMap.put("06", "雇主");
        RelationToAppntMap.put("07", "雇员");
        RelationToAppntMap.put("08", "祖父母、外祖父母");
        RelationToAppntMap.put("09", "祖孙、外祖孙");
        RelationToAppntMap.put("10", "监护人");
        RelationToAppntMap.put("11", "被监护人");
        RelationToAppntMap.put("12", "朋友");
        RelationToAppntMap.put("98", "未知");
    }

    public static String toTeamType(String type){
        // 核心类型 channel_type
        Map<String, String> map = new HashMap<>();
        map.put("100", "19001");  // 直销业务
        map.put("200", "19002");  // 代理业务
        map.put("300", "19003");  // 经纪业务
        map.put("999", "@N");  // 其他

        return Optional.ofNullable(map.get(type)).orElse("@N");
    }

    /**
     *
     * @Description 机构代码转换
     * <AUTHOR>
     * @Date 2023/10/17 15:35
     **/
    public static String branchCdToDtp(String branchCd){
        String departmentCode;
        switch (branchCd) {
            case "02":
                departmentCode= "776";
                break;
            case "03":
                departmentCode= "814";
                break;
            case "04":
                departmentCode= "808";
                break;
            case "05":
                departmentCode= "785";
                break;
            case "06":
                departmentCode= "793";
                break;
            case "07":
                departmentCode= "802";
                break;
            default:
                return "1";
        }
        return departmentCode;
    }
}
