package com.paic.ncbs.claim.service.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsPropertyLossInfo;

import java.util.List;

/**
 * 财产损失信息表(ClmsPropertyLossInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:49
 */
public interface ClmsPropertyLossInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsPropertyLossInfo queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    List<ClmsPropertyLossInfo> queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsPropertyLossInfo 实例对象
     * @return 实例对象
     */
    ClmsPropertyLossInfo insert(ClmsPropertyLossInfo clmsPropertyLossInfo);

    /**
     * 修改数据
     *
     * @param clmsPropertyLossInfo 实例对象
     * @return 实例对象
     */
    ClmsPropertyLossInfo update(ClmsPropertyLossInfo clmsPropertyLossInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    void deleteByCondition(String reportNo, int caseTimes);
}
