package com.paic.ncbs.claim.service.secondunderwriting;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUwLetterDTO;
import com.paic.ncbs.claim.model.vo.senconduw.LetterSellBackSubmitDTO;

import java.util.List;

/**
 * 理赔二核函件信息服务接口
 */
public interface ClmsSecondUwLetterService {
    /**
     * 批量保存
     * @param
     */
    void saveBatch(List<ClmsSecondUwLetterDTO> dtos);

    /**
     * 查询信息
     * @param idClmsSecondUnderwriting
     * @return
     */
    List<ClmsSecondUwLetterDTO> getLists(String idClmsSecondUnderwriting);

    /**
     * 函件回销提交
     * @param letterSellBackSubmitDTO
     */
    ResponseResult letterSellBack(LetterSellBackSubmitDTO letterSellBackSubmitDTO);

    String getUwsLetterCode(String manualInfoId);
}
