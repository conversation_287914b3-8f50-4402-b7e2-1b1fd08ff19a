package com.paic.ncbs.claim.service.reinsurance.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.config.ThirdServiceConfig;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO;
import com.paic.ncbs.claim.dao.entity.reinsurance.SendReinsuranceRecord;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.ClmsEstimateRecordMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateChangeMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyHistoryMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.TextTemplateMapper;
import com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentDutyMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.reinsurance.*;
import com.paic.ncbs.claim.dao.mapper.restartcase.RestartCaseRecordMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.HugeAccidentInfoDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyHistoryDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.message.MailSendDTO;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.other.AsynchronousCompensationJobExtDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentDutyDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentPlanDutyDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.*;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.vo.openapi.DutyVO;
import com.paic.ncbs.claim.model.vo.openapi.PlanVO;
import com.paic.ncbs.claim.model.vo.openapi.PolicyVO;
import com.paic.ncbs.claim.model.vo.reinsurance.RecipientsVO;
import com.paic.ncbs.claim.model.vo.reinsurance.ReinsApiVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyAndCustomerInfoVO;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyChargeMapper;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyMainMapper;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyChargeVo;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo;
import com.paic.ncbs.claim.sao.ReinsuranceSAO;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.duty.DutyPayService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateDutyRecordService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.other.MailSendService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.verify.VerifyService;

import cn.hutool.core.util.StrUtil;
import com.paic.ncbs.message.model.dto.SmsResult;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@RefreshScope
@Slf4j
@Service
public class ReinsuranceServiceImpl implements ReinsuranceService {

    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    private EstimateDutyRecordService estimateDutyRecordService;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private DutyPayService dutyPayService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private CoinsureService coinsureService;

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private CaseClassMapper caseClassMapper;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private EstimateChangeMapper estimateChangeMapper;
    @Autowired
    private ClmsPaymentDutyMapper paymentDutyMapper;
    @Autowired
    private SendReinsuranceRecordMapper sendReinsuranceRecordMapper;
    @Autowired
    private HugeAccidentInfoMapper hugeAccidentInfoMapper;

    @Autowired
    private ThirdServiceConfig thirdServiceConfig;

    @Autowired
    private ReinsuranceSAO reinsuranceSAO;
    @Autowired
    private MailSendService mailSendService;
    @Autowired
    private TaskInfoService taskInfoService;

    @Value("${switch.sendReinsurance:N}")
    private String switchSendReinsurance;

    @Value("${planCode.childMedical:YLZ0001}")
    private String childMedicalPlanCode;

    @Value("${env}")
    private String env;

    @Value("${ncbs.reinsurance.processSwitch:1}")
    private String sendReinsuranceProcessSwitch;

    @Autowired
    private EstimateDutyHistoryMapper estimateDutyHistoryMapper;

    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private RestartCaseRecordMapper restartCaseRecordMapper;
    @Autowired
    private ClmsEstimateRecordMapper estimateRecordMapper;
    @Autowired
    private ClmsReplevyMainMapper clmsReplevyMainMapper;
    @Autowired
    public ClmsReplevyChargeMapper clmsReplevyChargeMapper;

    @Autowired
    private ReinsBillMapper reinsBillMapper;
    @Autowired
    private TextTemplateMapper textTemplateMapper;
    @Autowired
    private PrintCoreService printCoreService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    @Override
    public void compensate(RepayCalDTO dto) {
        try {
            // 补推时送再保不做异步
            this.sendReinsurance(dto);
        } catch (Exception e) {
            log.error("ReinsuranceService.compensate error, dto={}", JSON.toJSONString(dto), e);
        }

//        try {
//            // 确保createDate不一样
//            Thread.sleep(1000L);
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//        }
    }

    @Async("asyncPool")
    @Override
    public void sendReinsurance(RepayCalDTO dto) {
        claimSendReinsurance(dto);
        //生成再保账单
        if(ReinsuranceClaimTypeEnum.REGISTER.equals(dto.getClaimType())
                ||ReinsuranceClaimTypeEnum.ESTIMATE_LOSS.equals(dto.getClaimType())
                ||ReinsuranceClaimTypeEnum.ENDCASE.equals(dto.getClaimType())){
            printCoreService.saveReinsuranceFileAsync(dto.getReportNo(), dto.getCaseTimes(), dto.getClaimType(),dto.getLoginUm());
            //再保账单添加消息提醒
            if(ReinsuranceClaimTypeEnum.ENDCASE.equals(dto.getClaimType())){
                List<ReinsuranceRateDTO> reinsuranceRateList = this.getReinsuranceFInfo(dto.getReportNo(), dto.getCaseTimes(), dto.getClaimType());
                if(reinsuranceRateList!=null&& !reinsuranceRateList.isEmpty()) {
                    log.info("再保账单发送消息提醒账单{}，taskId：{}", JSON.toJSONString(reinsuranceRateList),dto.getTaskId());
                    NoticesDTO noticesDTO = new NoticesDTO();
                    noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_REINSURANCE);
                    noticesDTO.setReportNo(dto.getReportNo());
                    noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
                    noticesDTO.setCaseTimes(dto.getCaseTimes());
                    TaskInfoDTO taskInfoDTO = taskInfoService.getTaskDtoByTaskId(dto.getTaskId());
                    if (taskInfoDTO!=null){
                        noticeService.saveNotices(noticesDTO,taskInfoDTO.getApplyer());
                    }
                }
            }
        }
    }

    @Override
    public void sendReinsuranceOnSync(RepayCalDTO dto) {
        claimSendReinsurance(dto);
    }

    public void claimSendReinsurance (RepayCalDTO dto) {
        try {
            log.info("ReinsuranceService.sendReinsurance begin, switchSendReinsurance={}, dto={}", switchSendReinsurance, JSON.toJSONString(dto));

            // 重开暂不允许做未决调整
            //70998-重开案件的估损调整需要同步再保
        /*if (ReinsuranceClaimTypeEnum.ESTIMATE_LOSS.equals(dto.getClaimType()) && dto.getCaseTimes() > 1) {
            return;
        }*/

        // 配置开关
        if (ConstValues.NO.equals(switchSendReinsurance)) {
            return;
        }

        List<String> policyNos = policyInfoMapper.getPolicyNo(dto.getReportNo());
        if(policyNos!=null && policyNos.size()>0) {
            for (String policyNo : policyNos) {
                if (globalPolicyService.checkGlobalPolicyNo(policyNo)) {
                    log.info("global案件不送再保");
                    return;
                }
            }
        }

            List<PolicyVO> policyInfoList = policyInfoMapper.getOpenPolicyInfoList(dto.getReportNo());
            log.info("ReinsuranceService.sendReinsurance, policyInfoList={}", JSON.toJSONString(policyInfoList));
            if (CollectionUtils.isEmpty(policyInfoList)) {
                log.info("ReinsuranceService.sendReinsurance, policyInfoList empty, dto={}", JSON.toJSONString(dto));
                return;
            }

            // 获取再保的保单
            Map<String, List<CoinsureDTO>> coinsuranceMap = coinsureService.getCoinsureListByReportNo(dto.getReportNo());
            Map<String, PolicyVO> policyInfoMap = Maps.newHashMap();
            for (PolicyVO policyInfo : policyInfoList) {
                if (BaseConstant.STRING_1.equals(policyInfo.getCoinsuranceMark())) {
                    policyInfo.setCoinsuranceList(coinsuranceMap.get(policyInfo.getPolicyNo()));
                }

                // 判断是否再保
                // if (BaseConstant.STRING_1.equals(policyInfo.getIsFacultativeBusiness())) {//去掉此判断，所有案件都送再保
                policyInfoMap.put(policyInfo.getPolicyNo(), policyInfo);
                //}
            }
            if (policyInfoMap.isEmpty()) {
                log.info("ReinsuranceService.sendReinsurance, policyInfoMap empty, dto={}", JSON.toJSONString(dto));
                return;
            }

            if(!"01".equals(dto.getCaseScene())) {
                try {
                    // 确保createDate不一样
                    if (ReinsuranceClaimTypeEnum.ESTIMATE_LOSS.equals(dto.getClaimType()) || ReinsuranceClaimTypeEnum.ENDCASE.equals(dto.getClaimType())) {
                        Thread.sleep(1000L);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // 获取入参
            List<IntfClaimMainDTO> list = getIntfClaimMainList(dto, policyInfoMap);
            log.info("ReinsuranceService.sendReinsurance, params={}", JSON.toJSONString(list));

            if (Constants.ENV_SAMSUNG.equals(env)) {
                for (IntfClaimMainDTO i : list) {
                    this.repayCal(dto, i);
                }
            }
        } catch (Exception e) {
            log.error("ReinsuranceService.sendReinsuranceOnSync error, dto={}", JSON.toJSONString(dto), e);
        }
    }

    /**
     * 获取理赔各环节送再保主表入参
     * @param dto
     * @param policyInfoMap
     * @return
     */
    private List<IntfClaimMainDTO> getIntfClaimMainList(RepayCalDTO dto, Map<String, PolicyVO> policyInfoMap) {
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        ReinsuranceClaimTypeEnum claimType = dto.getClaimType();

        List<IntfClaimMainDTO> list = Lists.newArrayList();
        List<CaseBaseDTO> caseBaseList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(caseBaseList)) {
            log.info("ReinsuranceService.getIntfClaimMainList, caseBaseList empty, dto={}", JSON.toJSONString(dto));
            return list;
        }
        WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
        if (null != wholeCase && StringUtils.isNotBlank(wholeCase.getIndemnityConclusion()) && "6".equals(wholeCase.getIndemnityConclusion())) {
            log.info("报案注销不送再保, dto={}", JSON.toJSONString(dto));
            return list;
        }
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
        //List<CaseClassDTO> caseClassList = caseClassMapper.getNewCaseClassList(reportNo, caseTimes, null);
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
        VerifyDTO verify = verifyService.getVerify(reportNo, caseTimes);
        Calendar cal = Calendar.getInstance();
        for (CaseBaseDTO caseBaseDTO : caseBaseList) {
            if (!policyInfoMap.containsKey(caseBaseDTO.getPolicyNo())) {
                continue;
            }
            PolicyVO policyInfo = policyInfoMap.get(caseBaseDTO.getPolicyNo());

            IntfClaimMainDTO claimMain = new IntfClaimMainDTO();
            claimMain.setClaimNo(caseBaseDTO.getCaseNo());
            claimMain.setRegistNo(reportNo);
            claimMain.setMainClaimNo(caseBaseDTO.getRegistNo());
            claimMain.setClaimType(claimType.getType());
            claimMain.setClaimTypeDesc(claimType.getName());

            //Integer claimSerialNo = this.getClaimSerialNo(dto);
//            Integer claimSerialNo = this.getClaimSerialNo(dto, caseBaseDTO.getPolicyNo());
            claimMain.setClaimSerialNo(caseTimes);
            claimMain.setPolicyNo(caseBaseDTO.getPolicyNo());
            claimMain.setDangerNo(BaseConstant.INT_1); // 固定传 1

            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(caseBaseDTO.getPolicyNo());
            claimMain.setClassCode(MapUtils.getString(productMap, "productClass"));
            claimMain.setClassName(ProductClassEnum.getName(claimMain.getClassCode()));
            claimMain.setRiskCode(policyInfo.getProductCode());
            claimMain.setRiskName(policyInfo.getProductName());
            claimMain.setComCode(policyInfo.getDepartmentCode());
            claimMain.setComName(departmentDefineMapper.queryDepartmentNameByDeptCode(policyInfo.getDepartmentCode()));
            claimMain.setInsuredCode(customerInfo.getClientNo());
            claimMain.setInsuredName(customerInfo.getName());

            claimMain.setBusinessFlag(BaseConstant.STRING_0); // 业务类型 固定传 0-自营业务
            claimMain.setCoinsFlag(this.getCoinFlag(policyInfo)); // 联共保标志

            claimMain.setDamageDate(reportAccident.getAccidentDate());
            //2024-09-05注释开始 原因  报案超15天自动估损需求 早上会议 何永琪，吴思佳 确认 送再保这个 出险原因取值不对 调整为取事故出险原因代码和汉字描述
            //claimMain.setDamageCode(caseClassList.stream().map(CaseClassDTO::getCaseSubClass).collect(Collectors.joining(",")));
            //claimMain.setDamageReason(caseClassList.stream().map(CaseClassDTO::getCaseSubClassName).collect(Collectors.joining(",")));
            //2024-09-05注释结束
            String level1 =reportAccident.getAccidentCauseLevel1();
            String level2 = reportAccident.getAccidentCauseLevel2();
            String damageCode=level1+","+level2;
            claimMain.setDamageCode(damageCode);

            String level1Name=AccidentReasonTypeEnum.getName(reportAccident.getAccidentCauseLevel1());
            String level2Name= AccidentReasonDetailTypeEnum.getName(reportAccident.getAccidentCauseLevel2());
            String damageReason=level1Name+","+level2Name;
            claimMain.setDamageReason(damageReason);
            claimMain.setClaimDate(wholeCase.getRegisterDate());
            claimMain.setEndCaseFlag(BaseConstant.STRING_0);
            claimMain.setCreateDate(new Date());
            if (StringUtils.isBlank(dto.getSendNowTimeFlag()) || "0".equals(dto.getSendNowTimeFlag())) {
                if (ReinsuranceClaimTypeEnum.ENDCASE.equals(claimType)) {
                    claimMain.setEndCaseDate(wholeCase.getEndCaseDate());
                    claimMain.setEndCaseFlag(BaseConstant.STRING_1);
                    // 核赔时间
                    if (Objects.nonNull(verify)) {
                        claimMain.setUnderWriteDate(verify.getVerifyDate());
                    }
                    // 结案时间+100毫秒，确保createDate不一样
                    cal.setTime(wholeCase.getEndCaseDate());
                    cal.add(Calendar.MILLISECOND, 100);
                    claimMain.setCreateDate(cal.getTime());
                } else if (ReinsuranceClaimTypeEnum.REGISTER.equals(claimType)) {
                    claimMain.setCreateDate(wholeCase.getRegisterDate());
                } else if (ReinsuranceClaimTypeEnum.ESTIMATE_LOSS.equals(claimType)) {
                    List<ClmsEstimateRecord> clmsEstimateRecords = estimateRecordMapper.selectByReportNoAndType(caseBaseDTO.getReportNo(), caseTimes.toString(), EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
                    if (!CollectionUtils.isEmpty(clmsEstimateRecords)) {
                        // 估损时间+50毫秒，确保createDate不一样
                        cal.setTime(clmsEstimateRecords.get(0).getEffectiveTime());
                        cal.add(Calendar.MILLISECOND, 50);
                        claimMain.setCreateDate(cal.getTime());
                    }
                } else if (ReinsuranceClaimTypeEnum.CASE_REOPEN.equals(claimType)) {
                    RestartCaseRecordEntity entity = restartCaseRecordMapper.getRestartAuditTime(reportNo,caseTimes-1);
                    if (null != entity) {
                        claimMain.setCreateDate(entity.getUpdatedDate());
                    }
                }else if(ReinsuranceClaimTypeEnum.REPLEVY.equals(claimType)){
                    //追偿默认非共保
                    claimMain.setCoinsFlag(BaseConstant.STRING_0);
                    claimMain.setEndCaseDate(wholeCase.getEndCaseDate());
                    claimMain.setEndCaseFlag(BaseConstant.STRING_1);
                    if(SettleConst.PAYMENT_TYPE_REPLEVY_FEE.equals(dto.getPaymentType())){
                        //根据报案号和追偿次数查询追偿费用数据
                        ClmsReplevyChargeVo clmsReplevyCharge = clmsReplevyChargeMapper.selectReplevyChargeByReportNoAndSerialNo(reportNo, dto.getSubTimes());
                        claimMain.setUnderWriteDate(clmsReplevyCharge.getFinishDate());
                        claimMain.setCreateDate(clmsReplevyCharge.getSysCtime());

                    }else{
                        //根据报案号和追偿次数查询追偿数据
                        ClmsReplevyMainVo clmsReplevyMainVo = new ClmsReplevyMainVo();
                        clmsReplevyMainVo.setReportNo(reportNo);
                        clmsReplevyMainVo.setReplevyTimes(dto.getSubTimes());
                        clmsReplevyMainVo = clmsReplevyMainMapper.selectClmsReplevyMain(clmsReplevyMainVo);
                        claimMain.setUnderWriteDate(clmsReplevyMainVo.getFinishDate());
                        claimMain.setCreateDate(clmsReplevyMainVo.getSysCtime());
                    }
                }
            } else {
                if (ReinsuranceClaimTypeEnum.ENDCASE.equals(claimType)) {
                    claimMain.setEndCaseDate(new Date());
                    claimMain.setEndCaseFlag(BaseConstant.STRING_1);
                    // 核赔时间
                    if (Objects.nonNull(verify)) {
                        claimMain.setUnderWriteDate(verify.getVerifyDate());
                    }
                }else if(ReinsuranceClaimTypeEnum.REPLEVY.equals(claimType)){
                    //追偿默认非共保
                    claimMain.setCoinsFlag(BaseConstant.STRING_0);
                    claimMain.setEndCaseDate(wholeCase.getEndCaseDate());
                    claimMain.setEndCaseFlag(BaseConstant.STRING_1);
                    if(SettleConst.PAYMENT_TYPE_REPLEVY_FEE.equals(dto.getPaymentType())){
                        //根据报案号和追偿次数查询追偿费用数据
                        ClmsReplevyChargeVo clmsReplevyCharge = clmsReplevyChargeMapper.selectReplevyChargeByReportNoAndSerialNo(reportNo, dto.getSubTimes());
                        claimMain.setUnderWriteDate(clmsReplevyCharge.getFinishDate());
                    }else{
                        //根据报案号和追偿次数查询追偿数据
                        ClmsReplevyMainVo clmsReplevyMainVo = new ClmsReplevyMainVo();
                        clmsReplevyMainVo.setReportNo(reportNo);
                        clmsReplevyMainVo.setReplevyTimes(dto.getSubTimes());
                        clmsReplevyMainVo = clmsReplevyMainMapper.selectClmsReplevyMain(clmsReplevyMainVo);
                        claimMain.setUnderWriteDate(clmsReplevyMainVo.getFinishDate());
                    }
                }
            }
            claimMain.setCurrency(Constants.CURRENCY_CNY); // 保单支付币种固定传CNY
            claimMain.setChgSumLoss(BigDecimal.ZERO); // 估损金额变化量固定传0
            claimMain.setChgPaidLoss(BigDecimal.ZERO); // 赔款金额变化量固定传0
            claimMain.setChgSumPaidFee(BigDecimal.ZERO); // 费用金额变化量固定传0
            claimMain.setExchRateCny(BigDecimal.ONE); // 理赔所有案件金额均为人民币，汇率固定为1
            claimMain.setSourceSystem(BaseConstant.UPPER_CASE_F); // 数据来源 固定传 F-非车系统

            // 巨灾
            if (CommonConstant.YES.equals(wholeCase.getIsHugeAccident()) && StringUtils.isNotEmpty(wholeCase.getHugeAccidentCode())) {
                claimMain.setLev1CataCode(wholeCase.getHugeAccidentCode());

                HugeAccidentInfoDTO queryDTO = new HugeAccidentInfoDTO();
                queryDTO.setAccidentCode(wholeCase.getHugeAccidentCode());
                HugeAccidentInfoDTO accidentInfo = hugeAccidentInfoMapper.queryOneByCondition(queryDTO);
                if (Objects.nonNull(accidentInfo)) {
                    claimMain.setLev1CataName(accidentInfo.getAccidentName());
                }
            }

            //组装责任
            List<IntfClaimClauseDTO> claimClauseList = this.getIntfClaimClauseList(caseBaseDTO, dto, claimMain, policyInfo);
            claimMain.setIntfClaimClauseList(claimClauseList);

            Map<String, BigDecimal> coinsRateMap = new HashMap<>();
            if (BaseConstant.STRING_1.equals(policyInfo.getCoinsuranceMark()) && CollectionUtils.isNotEmpty(policyInfo.getCoinsuranceList())) {
                List<IntfClaimCoinsDTO> claimCoinsList = this.getIntfClaimCoinsList(claimMain, policyInfo,coinsRateMap);
                claimMain.setIntfClaimCoinsList(claimCoinsList);
            }

            this.setSendAmount(caseBaseDTO, dto, claimMain, coinsRateMap);
            list.add(claimMain);
        }

        return list;
    }

    /**
     * 获取环节流程序号
     * 1、估损：传估损次数
     * 2、重开、结案：传赔付次数
     * @param dto
     * @return
     */
    private Integer getClaimSerialNo(RepayCalDTO dto) {
        if (ReinsuranceClaimTypeEnum.ESTIMATE_LOSS.equals(dto.getClaimType())) {
//            return estimateChangeMapper.selectCount(dto.getReportNo(), dto.getCaseTimes(), policyNo);
            return sendReinsuranceRecordMapper.selectCount(dto.getReportNo(), dto.getCaseTimes(), "5") + 1;
        }

        if (ReinsuranceClaimTypeEnum.CASE_REOPEN.equals(dto.getClaimType()) || ReinsuranceClaimTypeEnum.ENDCASE.equals(dto.getClaimType())) {
            return dto.getCaseTimes();
        }
        return null;
    }

    /**
     * 获取环节流程序号
     * @param policyInfo
     * @return
     */
    private String getCoinFlag(PolicyVO policyInfo) {
        if (!BaseConstant.STRING_1.equals(policyInfo.getCoinsuranceMark()) || CollectionUtils.isEmpty(policyInfo.getCoinsuranceList())) {
            return BaseConstant.STRING_0;
        }

        // coinsuranceType 0:内部联保(我司主联)、1:外部联保(我司主承)、2:外部联保(我司非主承)
        String coinsuranceType = policyInfo.getCoinsuranceList().get(0).getCoinsuranceType();
        if (BaseConstant.STRING_0.equals(coinsuranceType)) {
            return BaseConstant.STRING_3;
        } else if (BaseConstant.STRING_2.equals(coinsuranceType)) {
            return BaseConstant.STRING_2;
        } else {
            return BaseConstant.STRING_1;
        }
    }

    /**
     * 获取送再保赔案条款表入参集合
     * @param caseBase TODO
     * @param dto
     * @param claimMain
     * @param policyInfo
     * @return
     */
    private List<IntfClaimClauseDTO> getIntfClaimClauseList(CaseBaseDTO caseBase, RepayCalDTO dto, IntfClaimMainDTO claimMain, PolicyVO policyInfo) {

        Map<String, String> riskGroup = null;

        if (riskPropertyService.displayRiskProperty(dto.getReportNo(), claimMain.getPolicyNo())) {
            riskGroup = new HashMap<>();
            riskGroup.put("productPackageType", caseBase.getRiskGroupNo());
            riskGroup.put("riskGroupName", caseBase.getRiskGroupName());
        }else {
            riskGroup = ocasMapper.getRiskGroupByPolicyNo(claimMain.getPolicyNo(), claimMain.getInsuredCode());
        }

        List<IntfClaimClauseDTO> intfClaimClauseList = Lists.newArrayList();

        // 估损环节只按照条款层级传再保，责任层级不传，损失类型统一传赔款，条款层级与赔案层级金额一致
       /* if (ReinsuranceClaimTypeEnum.ESTIMATE_LOSS.equals(dto.getClaimType())) {
            PlanVO planInfo = policyInfo.getPlanList().stream().filter(i -> childMedicalPlanCode.equals(i.getPlanCode())).findFirst().orElse(new PlanVO());
            IntfClaimClauseDTO intfClaimClause = getIntfClaimClause(claimMain, planInfo, null, BaseConstant.STRING_0, riskGroup);
            intfClaimClauseList.add(intfClaimClause);
            return intfClaimClauseList;
        }*/

        for (PlanVO planVO : policyInfo.getPlanList()) {
            for (DutyVO dutyVO : planVO.getDutyList()) {
                // 赔款
                intfClaimClauseList.add(getIntfClaimClause(claimMain, planVO, dutyVO, BaseConstant.STRING_0, riskGroup));

                // 费用
                intfClaimClauseList.add(getIntfClaimClause(claimMain, planVO, dutyVO, BaseConstant.STRING_1, riskGroup));
            }
        }
        return intfClaimClauseList;
    }

    /**
     * 获取送再保赔案条款表入参
     *
     * @param claimMain
     * @param planInfo
     * @param dutyInfo
     * @param payType   损失类型 0-物质损失赔款 1-费用
     * @param riskGroup
     * @return
     */
    private IntfClaimClauseDTO getIntfClaimClause(IntfClaimMainDTO claimMain, PlanVO planInfo, DutyVO dutyInfo, String payType, Map<String, String> riskGroup) {
        IntfClaimClauseDTO claimClause = new IntfClaimClauseDTO();
        claimClause.setClaimNo(claimMain.getClaimNo());
        claimClause.setRegistNo(claimMain.getRegistNo());
        claimClause.setMainClaimNo(claimMain.getMainClaimNo());
        claimClause.setClaimType(claimMain.getClaimType());
        claimClause.setClaimTypeDesc(claimMain.getClaimTypeDesc());
        claimClause.setClaimSerialNo(claimMain.getClaimSerialNo());
        claimClause.setPolicyNo(claimMain.getPolicyNo());
        claimClause.setDangerNo(claimMain.getDangerNo());
        claimClause.setRiskCode(claimMain.getRiskCode());
        // 新增方案编码和方案名称
        claimClause.setPlanCode(MapUtils.getString(riskGroup, "productPackageType"));
        claimClause.setPlanName(MapUtils.getString(riskGroup,"riskGroupName"));
        claimClause.setRiskName(claimMain.getRiskName());
        claimClause.setClauseCode(planInfo.getPlanCode());
        claimClause.setClauseName(planInfo.getPlanName());
        if (Objects.nonNull(dutyInfo)) {
            claimClause.setKindCode(dutyInfo.getDutyCode());
            claimClause.setKindName(dutyInfo.getDutyName());
        }
        claimClause.setCurrency(claimMain.getCurrency());
        claimClause.setPayType(payType);
        claimClause.setChgPayValue(BigDecimal.ZERO);
        claimClause.setTaxRate(planInfo.getTaxRate());
        return claimClause;
    }

    /**
     * 获取送再保赔案共保表入参集合
     *
     * @param claimMain
     * @param policyInfo
     * @param coinsRateMap
     * @return
     */
    private List<IntfClaimCoinsDTO> getIntfClaimCoinsList(IntfClaimMainDTO claimMain, PolicyVO policyInfo, Map<String, BigDecimal> coinsRateMap) {
        List<IntfClaimCoinsDTO> claimCoinsList = Lists.newArrayList();
        for (CoinsureDTO coinsureDTO : policyInfo.getCoinsuranceList()) {
            IntfClaimCoinsDTO claimCoins = new IntfClaimCoinsDTO();
            claimCoins.setClaimNo(claimMain.getClaimNo());
            claimCoins.setRegistNo(claimMain.getRegistNo());
            claimCoins.setMainClaimNo(claimMain.getMainClaimNo());
            claimCoins.setClaimType(claimMain.getClaimType());
            claimCoins.setClaimTypeDesc(claimMain.getClaimTypeDesc());
            claimCoins.setClaimSerialNo(claimMain.getClaimSerialNo());
            claimCoins.setPolicyNo(claimMain.getPolicyNo());
            claimCoins.setDangerNo(claimMain.getDangerNo());
            claimCoins.setCoinsCode(coinsureDTO.getReinsureCompanyCode());// 1:三星
            claimCoins.setCoinsName(coinsureDTO.getReinsureCompanyName());
            claimCoins.setCoinsType(this.getCoinsType(coinsureDTO.getReinsureCompanyCode(), policyInfo));
            claimCoins.setChiefFlag(BaseConstant.STRING_0);
            claimCoins.setCoinsRate(coinsureDTO.getReinsureScale());
            //构建共保我司共保比例 ReinsureCompanyCode=1:三星
            String key = claimMain.getPolicyNo()+"-"+coinsureDTO.getReinsureCompanyCode();
            coinsRateMap.put(key,coinsureDTO.getReinsureScale());
            claimCoins.setCurrency(claimMain.getCurrency());
            claimCoins.setChgCoinsSumLoss(BigDecimal.ZERO); // 固定传0
            claimCoins.setChgCoinsPaidLoss(BigDecimal.ZERO); // 固定传0
            claimCoins.setChgCoinsPaidFee(BigDecimal.ZERO); // 固定传0
            claimCoinsList.add(claimCoins);
        }
        return claimCoinsList;
    }

    /**
     * 获取共保类型 1-我方 2-系统内其它方 3-系统外其它方
     * @param coinsCode
     * @param policyInfo
     * @return
     */
    private String getCoinsType(String coinsCode, PolicyVO policyInfo){
        if(StringUtils.equals(coinsCode, policyInfo.getDepartmentCode())){
            return BaseConstant.STRING_1;
        }

        String departmentName = departmentDefineMapper.queryDepartmentNameByDeptCode(coinsCode);
        return StringUtils.isNotBlank(departmentName) ? BaseConstant.STRING_2 : BaseConstant.STRING_3;
    }

    /**
     * 赋值再保需要的金额
     *
     * @param dto
     * @param claimMain
     * @param coinsRateMap
     */
    private void setSendAmount(CaseBaseDTO caseBase, RepayCalDTO dto, IntfClaimMainDTO claimMain, Map<String, BigDecimal> coinsRateMap) {
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        ReinsuranceClaimTypeEnum claimType = dto.getClaimType();

        //是否共保
        Boolean isCoinsuranceMark = false;
        if (!"0".equals(claimMain.getCoinsFlag())) {
            isCoinsuranceMark = true;
        }
        //如果是重开，查询上一次结案的已决责任金额
        Map<String, BigDecimal> oldPlanDutyCodeAmountMap = getOldPlanDutyAmountMap(reportNo, caseTimes, claimType);
        switch (claimType) {
            case REGISTER:
            case ESTIMATE_LOSS:
            case CASE_REOPEN:
                BigDecimal registerAmount = BigDecimal.ZERO;
                List<EstimateDutyRecordDTO> registCaseList = estimateDutyRecordService.getRecordsOfRegistCase(claimMain.getClaimNo(), caseTimes, EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
                Map<String, List<EstimateDutyRecordDTO>> registMap = registCaseList.stream().collect(Collectors.groupingBy(i -> i.getPlanCode() + "_" + i.getDutyCode()));
                for (IntfClaimClauseDTO claimClause : claimMain.getIntfClaimClauseList()) {
                    BigDecimal payValue = BigDecimal.ZERO;
                    List<EstimateDutyRecordDTO> estimateDutyRecordList = registMap.get(claimClause.getClauseCode() + "_" + claimClause.getKindCode());
                    //estimateDutyRecordList正常只有一条数据
                    if (CollectionUtils.isNotEmpty(estimateDutyRecordList)) {
                        for (EstimateDutyRecordDTO estimateDuty : estimateDutyRecordList) {
                            // 赔款
                            if (BaseConstant.STRING_0.equals(claimClause.getPayType())) {
                                payValue = BigDecimalUtils.sum(payValue, estimateDuty.getEstimateAmount());
                            }

                            // 费用
                            if (BaseConstant.STRING_1.equals(claimClause.getPayType())) {
                                payValue = BigDecimalUtils.sum(payValue, estimateDuty.getVerifyAppraiseFee(), estimateDuty.getArbitrageFee(),
                                        estimateDuty.getLawsuitFee(), estimateDuty.getCommonEstimateFee(), estimateDuty.getLawyerFee(),
                                        estimateDuty.getExecuteFee(), estimateDuty.getInquireFee(), estimateDuty.getOtherFee(), estimateDuty.getSpecialSurveyFee());
                            }
                        }
                    }
                    claimClause.setPayValue(payValue);
                    //如果是重开立案记录，发送再保金额为本次金额减去上一次已决的变化量差额，如果不是重开，变化量在filterIntfClaimClauseList计算
                    if (claimType.getType().equals(ReinsuranceClaimTypeEnum.CASE_REOPEN.getType())){
                        String paymentType = "";
                        if (BaseConstant.STRING_0.equals(claimClause.getPayType())) {
                            paymentType = PaymentTypeEnum.PAY.getType();
                        }
                        if (BaseConstant.STRING_1.equals(claimClause.getPayType())) {
                            paymentType = PaymentTypeEnum.FEE.getType();
                        }
                        BigDecimal oldDutyAmount = oldPlanDutyCodeAmountMap.get(claimClause.getClauseCode()+claimClause.getKindCode()+paymentType);
                        if (oldDutyAmount != null && isCoinsuranceMark && BaseConstant.STRING_0.equals(claimClause.getPayType())){
                            //如果是共保，赔付客户金额 = 13的赔付金额 + C13的赔付金额
                            BigDecimal oldPlanDutyCodeAmount = oldPlanDutyCodeAmountMap.get(claimClause.getClauseCode()+claimClause.getKindCode()+PaymentTypeEnum.COIN_PAY.getType());
                            oldDutyAmount = oldDutyAmount.add(oldPlanDutyCodeAmount==null?new BigDecimal(0):oldPlanDutyCodeAmount);
                        }
                        //变化量 = 重开立案金额 - 已决金额
                        claimClause.setChgPayValue(payValue.subtract(oldDutyAmount==null?new BigDecimal(0):oldDutyAmount));
                    }else {
                        //首次立案变化量不变
                        claimClause.setChgPayValue(payValue);
                    }
                    //如果是共保计算我司共保赔付金额、共保比例
                    if (isCoinsuranceMark && BaseConstant.STRING_0.equals(claimClause.getPayType())){
                        BigDecimal coinsRate = coinsRateMap.get(claimClause.getPolicyNo() + "-1");
                        claimClause.setSsCoinsRate(coinsRate);
                        if ("2".equals(claimMain.getCoinsFlag())){
                            //从共，我司赔款和变化量不乘比例
                            claimClause.setSsCoinsPayValue(claimClause.getPayValue());
                            claimClause.setSsCoinsChgPayValue(claimClause.getChgPayValue());
                        }else {
                            BigDecimal ssCoinsPayValue = claimClause.getPayValue().multiply(nvl(coinsRate, 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                            claimClause.setSsCoinsPayValue(ssCoinsPayValue);
                            BigDecimal ssCoinsChgPayValue = claimClause.getChgPayValue().multiply(nvl(coinsRate, 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                            claimClause.setSsCoinsChgPayValue(ssCoinsChgPayValue);
                        }
                    }
                    if (isCoinsuranceMark && BaseConstant.STRING_1.equals(claimClause.getPayType())){
                        BigDecimal coinsRate = coinsRateMap.get(claimClause.getPolicyNo()+"-1");
                        claimClause.setSsCoinsRate(coinsRate);
                        claimClause.setSsCoinsPayValue(claimClause.getPayValue());
                        claimClause.setSsCoinsChgPayValue(claimClause.getChgPayValue());
                    }
                    registerAmount = BigDecimalUtils.sum(registerAmount, payValue);
                }

                claimMain.setSumLoss(registerAmount);
                claimMain.setPaidLoss(BigDecimal.ZERO);
                claimMain.setPaidFee(BigDecimal.ZERO);
                if (ReinsuranceClaimTypeEnum.CASE_REOPEN.equals(claimType)) {
                    BigDecimal lastPolicyPayAmount = policyPayService.getLastPolicyPayAmount(reportNo, caseTimes, claimMain.getPolicyNo());
                    claimMain.setOsLoss(registerAmount.subtract(lastPolicyPayAmount)); // 立案金额（重开）-累计已决（赔款）
                } else {
                    claimMain.setOsLoss(registerAmount);
                }
                break;
           /* case ESTIMATE_LOSS:
                BigDecimal lossAmount = BigDecimal.ZERO;
                for (IntfClaimClauseDTO claimClause : claimMain.getIntfClaimClauseList()) {
                    BigDecimal changeAmount = estimateChangeMapper.getChangeAmount(reportNo, caseTimes, claimClause.getPolicyNo());
                    claimClause.setPayValue(changeAmount);
                    lossAmount = BigDecimalUtils.sum(lossAmount, claimClause.getPayValue());
                }

                claimMain.setSumLoss(lossAmount);
                claimMain.setPaidLoss(BigDecimal.ZERO);
                claimMain.setPaidFee(BigDecimal.ZERO);
                claimMain.setOsLoss(lossAmount);
                break;*/
            case ENDCASE:
                BigDecimal payAmount = BigDecimal.ZERO;
                BigDecimal feeAmount = BigDecimal.ZERO;
                for (IntfClaimClauseDTO claimClause : claimMain.getIntfClaimClauseList()) {
                    BigDecimal payValue = BigDecimal.ZERO;
                    // 赔款，只有赔付时计算
                    if (BaseConstant.STRING_0.equals(claimClause.getPayType()) && ConfigConstValues.INDEMNITYCONCLUSION_PAY.equals(dto.getIndemnityConclusion())) {
                        payValue = dutyPayService.getDutyPayAmount(claimClause.getClaimNo(), caseTimes, claimClause.getClauseCode(), claimClause.getKindCode());

                        // 重开计算变化量
                        if (caseTimes > 1) {
                            BigDecimal lastDutyPayAmount = dutyPayService.getLastDutyPayAmount(claimClause.getClaimNo(), caseTimes, claimClause.getClauseCode(), claimClause.getKindCode());
                            payValue = payValue.subtract(lastDutyPayAmount);
                        }
                        claimClause.setChgPayValue(payValue);
                        payAmount = BigDecimalUtils.sum(payAmount, payValue);
                        //如果是共保计算我司共保赔付金额、共保比例
                        if (isCoinsuranceMark){
                            BigDecimal coinsRate = coinsRateMap.get(claimClause.getPolicyNo() + "-1");
                            claimClause.setSsCoinsRate(coinsRate);
                            if ("2".equals(claimMain.getCoinsFlag())){
                                //从共，我司赔款和变化量不乘比例
                                claimClause.setSsCoinsPayValue(payValue);
                                claimClause.setSsCoinsChgPayValue(claimClause.getChgPayValue());
                            }else {
                                BigDecimal ssCoinsPayValue = payValue.multiply(nvl(coinsRate, 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                                claimClause.setSsCoinsPayValue(ssCoinsPayValue);
                                BigDecimal ssCoinsChgPayValue = claimClause.getChgPayValue().multiply(nvl(coinsRate, 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                                claimClause.setSsCoinsChgPayValue(ssCoinsChgPayValue);
                            }
                        }
                    }

                    // 费用，赔付、零结、拒赔时计算
                    if (BaseConstant.STRING_1.equals(claimClause.getPayType()) && !ConfigConstValues.INDEMNITYCONCLUSION_CANCEL.equals(dto.getIndemnityConclusion())) {
                        List<PaymentDutyDTO> paymentDutyFees = paymentDutyMapper.getPaymentDutyFee(claimClause.getClaimNo(), caseTimes, claimClause.getClauseCode(), claimClause.getKindCode());
                        // 重开计算变化量
                        List<PaymentDutyDTO> lastPaymentDutyFees;
                        BigDecimal oldNoTaxAmount = BigDecimal.ZERO;
                        if (caseTimes > 1) {
                            lastPaymentDutyFees = paymentDutyMapper.getPaymentDutyFee(claimClause.getClaimNo(), caseTimes-1, claimClause.getClauseCode(), claimClause.getKindCode());
                            if (CollectionUtils.isNotEmpty(lastPaymentDutyFees)) {
                                oldNoTaxAmount = nvl(claimClause.getTaxRate(), 0).compareTo(BigDecimal.ZERO) > 0 ? lastPaymentDutyFees.get(0).getNoTaxAmount() : lastPaymentDutyFees.get(0).getDutyPayAmount();
                            }
                        }
                        if (CollectionUtils.isNotEmpty(paymentDutyFees)) {
                            for (PaymentDutyDTO dutyFee : paymentDutyFees) {
                                // noTaxAmount目前数据有问题，不计税则取dutyPayAmount
                                BigDecimal noTaxAmount = nvl(claimClause.getTaxRate(),0).compareTo(BigDecimal.ZERO) > 0 ? dutyFee.getNoTaxAmount() : dutyFee.getDutyPayAmount();
                                payValue = BigDecimalUtils.sum(payValue, noTaxAmount);
                                claimClause.setChgPayValue(payValue.subtract(oldNoTaxAmount));
                            }
                        }
                        feeAmount = BigDecimalUtils.sum(feeAmount, payValue);
                        //费用共保金额全部为我司
                        if (isCoinsuranceMark){
                            BigDecimal coinsRate = coinsRateMap.get(claimClause.getPolicyNo()+"-1");
                            claimClause.setSsCoinsRate(coinsRate);
                            if ("2".equals(claimMain.getCoinsFlag())){
                                //从共，我司赔款和变化量不乘比例
                                claimClause.setSsCoinsPayValue(payValue);
                                claimClause.setSsCoinsChgPayValue(payValue);
                            }else {
                                BigDecimal ssCoinsPayValue = payValue.multiply(nvl(coinsRate, 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                                claimClause.setSsCoinsPayValue(ssCoinsPayValue);
                                claimClause.setSsCoinsChgPayValue(ssCoinsPayValue);
                            }
                        }
                    }
                    claimClause.setPayValue(payValue);
                }
                log.info("结案查询费用数据claimClause:{}",JSON.toJSONString(claimMain.getIntfClaimClauseList()));

                claimMain.setSumLoss(BigDecimal.ZERO);
                claimMain.setPaidLoss(payAmount);
                claimMain.setPaidFee(feeAmount);
                claimMain.setOsLoss(BigDecimal.ZERO);
                break;
            case REPLEVY:
                 payAmount = BigDecimal.ZERO;
                 feeAmount = BigDecimal.ZERO;
                 //追偿的费用与赔款是分开传送的
                for (IntfClaimClauseDTO claimClause : claimMain.getIntfClaimClauseList()) {
                    BigDecimal payValue = BigDecimal.ZERO;
                    List<PaymentDutyDTO> paymentDutys = new ArrayList<>();
                    if (SettleConst.PAYMENT_TYPE_REPLEVY_FEE.equals(dto.getPaymentType())) {
                        // 追偿费用
                        if (BaseConstant.STRING_1.equals(claimClause.getPayType())) {
                            paymentDutys = paymentDutyMapper.getReplevyPaymentDuty(claimClause.getClaimNo(), claimClause.getClauseCode(), claimClause.getKindCode(), dto.getPaymentType(), dto.getSubTimes());
                            if(paymentDutys!=null&&paymentDutys.size()>0){
                                PaymentDutyDTO paymentDuty = paymentDutys.get(0);
                                BigDecimal noTaxAmount =paymentDuty.getNoTaxAmount();
                                payValue = BigDecimalUtils.sum(payValue, noTaxAmount);
                                claimClause.setChgPayValue(noTaxAmount);
                                feeAmount = BigDecimalUtils.sum(feeAmount, noTaxAmount);
                            }
                        }
                    } else {
                        //追偿金额
                        if (BaseConstant.STRING_0.equals(claimClause.getPayType())) {
                            paymentDutys = paymentDutyMapper.getReplevyPaymentDuty(claimClause.getClaimNo(), claimClause.getClauseCode(), claimClause.getKindCode(), dto.getPaymentType(), dto.getSubTimes());
                            if(paymentDutys!=null&&paymentDutys.size()>0){
                                for (PaymentDutyDTO paymentDuty : paymentDutys){
                                    payValue = BigDecimalUtils.sum(payValue, paymentDuty.getDutyPayAmount());
                                }
                                claimClause.setChgPayValue(payValue);
                                payAmount = BigDecimalUtils.sum(payAmount, payValue);
                            }
                        }
                    }
                    claimClause.setPayValue(payValue);
                }
                log.info("结案查询费用数据claimClause:{}",JSON.toJSONString(claimMain.getIntfClaimClauseList()));

                claimMain.setSumLoss(BigDecimal.ZERO);
                claimMain.setPaidLoss(payAmount);
                claimMain.setPaidFee(feeAmount);
                claimMain.setOsLoss(BigDecimal.ZERO);
            default:
                break;
        }
        if (StringUtils.isEmpty(dto.getOldIdFlagHistoryChange()) || !ReinsuranceClaimTypeEnum.ESTIMATE_LOSS.equals(dto.getClaimType())) {
            //第一次未决送再保或者历史数据的id标记值可能为空，全量送
            if (!sendReinsuranceProcessSwitch.contains(dto.getClaimType().getType())) {
                claimMain.setIntfClaimClauseList(claimMain.getIntfClaimClauseList().stream()
                        .filter(i -> (BigDecimal.ZERO.compareTo(i.getPayValue()) != 0 || BigDecimal.ZERO.compareTo(i.getChgPayValue()) != 0)).collect(Collectors.toList()));
            }
        }else {
            //过滤得到本次与上一次赔款和费用变化的责任,再送再保
            filterIntfClaimClauseList(caseBase, dto, claimMain);
        }

        // 过滤payValue为0的数据
        //注释是因为未决到责任层级需要把变化为0的数据送给再保
        //claimMain.setIntfClaimClauseList(claimMain.getIntfClaimClauseList().stream().filter(i -> BigDecimal.ZERO.compareTo(i.getPayValue()) != 0).collect(Collectors.toList()));
        // 赋值共保
        this.setCoinsSendAmount(claimMain);
    }

    private IntfClaimClauseDTO getHistoryIntfClaimClause(EstimateDutyHistoryDTO historyEstimateDuty, IntfClaimMainDTO claimMain,String payType, BigDecimal oldPayValue) {
        IntfClaimClauseDTO claimClause = new IntfClaimClauseDTO();
        claimClause.setClaimNo(claimMain.getClaimNo());
        claimClause.setRegistNo(claimMain.getRegistNo());
        claimClause.setMainClaimNo(claimMain.getMainClaimNo());
        claimClause.setClaimType(claimMain.getClaimType());
        claimClause.setClaimTypeDesc(claimMain.getClaimTypeDesc());
        claimClause.setClaimSerialNo(claimMain.getClaimSerialNo() - 1); //  这次的减一？
        claimClause.setPolicyNo(claimMain.getPolicyNo());
        claimClause.setDangerNo(claimMain.getDangerNo());
        claimClause.setRiskCode(claimMain.getRiskCode());
        claimClause.setPlanCode(historyEstimateDuty.getRiskGroupNo());
        claimClause.setPlanName(historyEstimateDuty.getRiskGroupName());
        claimClause.setRiskName(claimMain.getRiskName());
        claimClause.setClauseCode(historyEstimateDuty.getPlanCode());
        claimClause.setClauseName(historyEstimateDuty.getPlanName());
        claimClause.setKindCode(historyEstimateDuty.getDutyCode());
        claimClause.setKindName(historyEstimateDuty.getDutyName());
        claimClause.setCurrency(claimMain.getCurrency());
        claimClause.setPayType(payType);
        claimClause.setChgPayValue(BigDecimal.ZERO);
        claimClause.setTaxRate(historyEstimateDuty.getTaxRate());
        claimClause.setPayValue(BigDecimal.ZERO);
        return claimClause;
    }

    /**
     * 如果是重开，查询上一次结案的已决责任金额
     * @param reportNo
     * @param caseTimes
     * @param claimType
     * @return
     */
    private Map<String, BigDecimal> getOldPlanDutyAmountMap(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType) {
        Map<String, BigDecimal> oldPlanDutyCodeAmountMap = new HashMap<>();
        if (claimType.getType().equals(ReinsuranceClaimTypeEnum.CASE_REOPEN.getType())) {
            List<PaymentPlanDutyDTO> paymentDutyDTOList = paymentDutyMapper.getPaymentDutyByReportNo(reportNo, caseTimes - 1);
            oldPlanDutyCodeAmountMap = paymentDutyDTOList.stream().collect(
                    Collectors.toMap(k -> (k.getPlanCode() + k.getDutyCode()+k.getPaymentType()), PaymentPlanDutyDTO::getDutyPayAmount, BigDecimal::add));
        }
        return oldPlanDutyCodeAmountMap;
    }

    /**
     * 未决调整过滤得到本次与上一次赔款和费用变化的责任,再送再保
     * @param dto
     * @param claimMain
     */
    private void filterIntfClaimClauseList(CaseBaseDTO caseBase, RepayCalDTO dto, IntfClaimMainDTO claimMain) {
        log.info("过滤送再保历史,上次oldIdFlagHistoryChange为 :{}", JSON.toJSONString(dto));
        List<EstimateDutyHistoryDTO> historyDTOList = estimateDutyHistoryMapper.getDutyHistoryListByIdFlag(dto.getOldIdFlagHistoryChange());
        if (CollectionUtils.isEmpty(historyDTOList)) {
            log.info("估损历史数据查询为空");
            return;
        }
        //1 组装上一次责任 与 赔款/费用 的映射关系
        Map<String,BigDecimal> historyAmountMap = new HashMap<>();
        String historyRiskGroupNo = null; // 获取历史对应的方案
        for (EstimateDutyHistoryDTO historyDuty : historyDTOList) {
            BigDecimal oldPayValue = historyDuty.getDutyEstimateAmount() != null ? historyDuty.getDutyEstimateAmount() : BigDecimal.ZERO;
            BigDecimal oldFeeValue = BigDecimalUtils.sum(historyDuty.getVerifyAppraiseFee(), historyDuty.getArbitrageFee(),
                    historyDuty.getLawsuitFee(), historyDuty.getCommonEstimateFee(), historyDuty.getLawyerFee(),
                    historyDuty.getExecuteFee(), historyDuty.getInquireFee(), historyDuty.getOtherFee(), historyDuty.getSpecialSurveyFee());
            String payKey = historyDuty.getPlanCode() + "_" + historyDuty.getDutyCode() + "_" + BaseConstant.STRING_0;
            String feeKey = historyDuty.getPlanCode() + "_" + historyDuty.getDutyCode() + "_" + BaseConstant.STRING_1;
            historyAmountMap.put(payKey,oldPayValue);
            historyAmountMap.put(feeKey,oldFeeValue);
            historyRiskGroupNo = historyDuty.getRiskGroupNo();
        }
        log.info("historyAmountMap历史组装 :{}",JSON.toJSONString(historyAmountMap));
        //2. 发送再保赔款或费用数据 过滤条件：
        //2.1.如果历史责任不包含现有责任类型，
        //2.2.如果上一次和这一次的金额都是0,则不发送给再保
        List<IntfClaimClauseDTO> newIntfClaimClauseList = new ArrayList<>();
        List<IntfClaimClauseDTO> intfClaimClauseList = claimMain.getIntfClaimClauseList();
        log.info("filterIntfClaimClauseList过滤送再保打印初始数据 :{}",JSON.toJSONString(intfClaimClauseList));
        for (IntfClaimClauseDTO clauseDTO: intfClaimClauseList) {
            String payOrFeeKey = clauseDTO.getClauseCode() + "_" + clauseDTO.getKindCode() + "_" + clauseDTO.getPayType();
            if (!historyAmountMap.containsKey(payOrFeeKey)
                    || !(historyAmountMap.get(payOrFeeKey).compareTo(BigDecimal.ZERO)==0 && clauseDTO.getPayValue().compareTo(BigDecimal.ZERO)==0)) {
                //变化量 = 本次-历史表的责任金额
                BigDecimal historyDutyAmount = historyAmountMap.containsKey(payOrFeeKey)?historyAmountMap.get(payOrFeeKey):new BigDecimal(0);
                clauseDTO.setChgPayValue(clauseDTO.getPayValue());
                newIntfClaimClauseList.add(clauseDTO);
            }
        }
        // 切换了方案，上次方案大于0的记录多报送一条0
        if(StrUtil.isAllNotBlank(caseBase.getRiskGroupNo(), historyRiskGroupNo) && !caseBase.getRiskGroupNo().equals(historyRiskGroupNo) ){
            newIntfClaimClauseList = newIntfClaimClauseList.stream().filter(n -> n.getPayValue().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            for (EstimateDutyHistoryDTO historyDuty : historyDTOList) {
                BigDecimal oldPayValue = historyDuty.getDutyEstimateAmount() != null ? historyDuty.getDutyEstimateAmount() : BigDecimal.ZERO;
                if(oldPayValue.compareTo(BigDecimal.ZERO) != 0) {
                    newIntfClaimClauseList.add(getHistoryIntfClaimClause(historyDuty,claimMain,BaseConstant.STRING_0, oldPayValue));
                }
                BigDecimal oldFeeValue = BigDecimalUtils.sum(historyDuty.getVerifyAppraiseFee(), historyDuty.getArbitrageFee(),
                        historyDuty.getLawsuitFee(), historyDuty.getCommonEstimateFee(), historyDuty.getLawyerFee(),
                        historyDuty.getExecuteFee(), historyDuty.getInquireFee(), historyDuty.getOtherFee(), historyDuty.getSpecialSurveyFee());
                if(oldFeeValue.compareTo(BigDecimal.ZERO) != 0) {
                    newIntfClaimClauseList.add(getHistoryIntfClaimClause(historyDuty,claimMain,BaseConstant.STRING_1, oldFeeValue));
                }
            }
        }
        log.info("送再保未决数据:{}",JSON.toJSONString(newIntfClaimClauseList));
        claimMain.setIntfClaimClauseList(newIntfClaimClauseList);
    }

    private boolean compareAmount(){

        return true;
    }

    /**
     * 赋值共保需要的金额
     */
    private void setCoinsSendAmount(IntfClaimMainDTO claimMain) {
        List<IntfClaimCoinsDTO> claimCoinsList = claimMain.getIntfClaimCoinsList();
        if (CollectionUtils.isEmpty(claimCoinsList)) {
            return;
        }

        BigDecimal sumLoss = claimMain.getSumLoss();
        BigDecimal paidLoss = claimMain.getPaidLoss();
        BigDecimal paidFee = claimMain.getPaidFee();
        BigDecimal otherSumLoss = BigDecimal.ZERO;
        BigDecimal otherPaidLoss = BigDecimal.ZERO;
        BigDecimal otherPaidFee = BigDecimal.ZERO;

        claimCoinsList = claimCoinsList.stream().sorted(Comparator.comparing(IntfClaimCoinsDTO::getCoinsType).reversed()).collect(Collectors.toList()); // 我方排最后补尾差
        for (int i = 0; i < claimCoinsList.size(); i++) {
            IntfClaimCoinsDTO claimCoins = claimCoinsList.get(i);

            if (i == claimCoinsList.size() - 1) {
                claimCoins.setCoinsSumLoss(sumLoss.subtract(otherSumLoss));
                claimCoins.setCoinsPaidLoss(paidLoss.subtract(otherPaidLoss));
                claimCoins.setCoinsPaidFee(paidFee.subtract(otherPaidFee)); // 费用不拆分
            } else {
                claimCoins.setCoinsSumLoss(sumLoss.multiply(nvl(claimCoins.getCoinsRate(), 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                claimCoins.setCoinsPaidLoss(paidLoss.multiply(nvl(claimCoins.getCoinsRate(), 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                claimCoins.setCoinsPaidFee(paidFee.multiply(nvl(claimCoins.getCoinsRate(), 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));

                otherSumLoss = otherSumLoss.add(claimCoins.getCoinsSumLoss());
                otherPaidLoss = otherPaidLoss.add(claimCoins.getCoinsPaidLoss());
                otherPaidFee = otherPaidFee.add(claimCoins.getCoinsPaidFee());
            }
        }
    }

    /**
     * 调再报接口（失败需补偿）
     * @param dto
     * @param claimMain
     */
    private void repayCal(RepayCalDTO dto, IntfClaimMainDTO claimMain) {
        String requestParam = JSON.toJSONString(claimMain);
        log.info("发送给再保入参:{}", requestParam);
        String responseParam = null;
        String status = BaseConstant.STRING_0;
        try {
            ReinsuranceRespDTO respDTO = reinsuranceSAO.repayCal(claimMain);
            if (respDTO == null || !respDTO.isSuccess()) {
                status = BaseConstant.STRING_1;
            }
            if (respDTO != null) {
                responseParam = JSON.toJSONString(respDTO);
            }
        } catch (Exception e) {
            responseParam = e.getMessage();
            status = BaseConstant.STRING_1;
            log.error("ReinsuranceSAO.repayCal error, url={}, param={}", thirdServiceConfig.getRepayCalUrl(), requestParam, e);
        }

        //已成功送再保，忽略重复送再保
        if (BaseConstant.STRING_1.equals(status)) {
            if (StringUtils.isNotBlank(responseParam) && (responseParam.contains("数据已送再保，请勿重复送再保") || responseParam.contains("结案数据已送再保"))) {
                status = BaseConstant.STRING_0;
            }
        }
        // 存送再保记录
        SendReinsuranceRecord sendReinsuranceRecord = new SendReinsuranceRecord();
        sendReinsuranceRecord.setReportNo(dto.getReportNo());
        sendReinsuranceRecord.setCaseTimes(dto.getCaseTimes());
        sendReinsuranceRecord.setCaseNo(claimMain.getClaimNo());
        sendReinsuranceRecord.setRegistNo(claimMain.getMainClaimNo());
        sendReinsuranceRecord.setPolicyNo(claimMain.getPolicyNo());
        sendReinsuranceRecord.setClaimType(claimMain.getClaimType());
        sendReinsuranceRecord.setClaimTypeDesc(claimMain.getClaimTypeDesc());
        sendReinsuranceRecord.setRequestParam(requestParam);
        sendReinsuranceRecord.setResponseParam(responseParam);
        sendReinsuranceRecord.setIsSuccess(BaseConstant.STRING_1.equals(status) ? ConstValues.NO : ConstValues.YES);
        sendReinsuranceRecordMapper.insert(sendReinsuranceRecord);
        Long sendReinsuranceRecordId = sendReinsuranceRecord.getId();

        // 失败补偿
        if (BaseConstant.STRING_1.equals(status)) {
            Date now = DateUtils.now();
            AsynchronousCompensationJobExtDTO compensationDTO = new AsynchronousCompensationJobExtDTO();
            compensationDTO.setId(UuidUtil.getUUID());
            compensationDTO.setJobType(Constants.COMPENSATION_JOB_TYPE_7);
            compensationDTO.setRetryTimes(0);
            compensationDTO.setRequestParam(requestParam);
            compensationDTO.setStatus(status);
            compensationDTO.setUrl(thirdServiceConfig.getRepayCalUrl());
            compensationDTO.setBusinessType(Constants.COMPENSATION_BUSINESS_TYPE_72);
            compensationDTO.setBusinessNo(dto.getReportNo());
            compensationDTO.setResponseParam(responseParam);
            compensationDTO.setCreatedBy(ConstValues.SYSTEM_UM);
            compensationDTO.setCreatedDate(now);
            compensationDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
            compensationDTO.setUpdatedDate(now);
            compensationDTO.setBusinessNoSub(sendReinsuranceRecordId+"");
            paymentItemMapper.insertJobInfo(compensationDTO);
        }
    }
    /**
     * 获取再保信息
     * @return
     */
    @Override
    public List<ReinsuranceRateDTO> getReinsuranceInfo(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType) {
        List<ReinsuranceRateDTO> reinsuranceRateList = new ArrayList<>();
        WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
        if (null != wholeCase && StringUtils.isNotBlank(wholeCase.getIndemnityConclusion()) && "6".equals(wholeCase.getIndemnityConclusion())) {
            return reinsuranceRateList;
        }
        try {
            ReinsuranceReqDTO dto = this.buildReinsuranceReqDTO(reportNo,caseTimes,claimType);
            reinsuranceRateList = reinsuranceSAO.queryRepayCal(dto);
        } catch (Exception e) {
            log.error("getReinsuranceInfo error, reportNo={}, caseTimes={}", reportNo, caseTimes, e);
        }
        return reinsuranceRateList;
    }
    //获取再保账单的请求数据
    public ReinsuranceReqDTO buildReinsuranceReqDTO(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType) {
        WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
        Calendar cal = Calendar.getInstance();
        List<CaseBaseDTO> caseBaseList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
        ReinsuranceReqDTO dto = new ReinsuranceReqDTO();
        if(caseBaseList!=null && !caseBaseList.isEmpty()) {
            dto.setClaimNo(caseBaseList.get(0).getCaseNo());
            dto.setClaimType(claimType.getType());
            dto.setCreateDate(new Date());
            //按照发送再保的createdate获取对应再保信息
            if (ReinsuranceClaimTypeEnum.ENDCASE.equals(claimType)) {
                if (wholeCase != null) {
                    cal.setTime(wholeCase.getEndCaseDate());
                    dto.setSubmitter(wholeCase.getSettlerUm());
                }
                cal.add(Calendar.MILLISECOND, 100);
                dto.setCreateDate(cal.getTime());
            } else if (ReinsuranceClaimTypeEnum.REGISTER.equals(claimType)) {
                dto.setCreateDate(wholeCase.getRegisterDate());
                dto.setSubmitter(wholeCase.getRegisterUm());
            } else if (ReinsuranceClaimTypeEnum.ESTIMATE_LOSS.equals(claimType)) {
                List<ClmsEstimateRecord> clmsEstimateRecords = estimateRecordMapper.selectByReportNoAndType(reportNo, caseTimes.toString(), EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
                if (!CollectionUtils.isEmpty(clmsEstimateRecords)) {
                    // 估损时间+50毫秒，确保createDate不一样
                    cal.setTime(clmsEstimateRecords.get(0).getEffectiveTime());
                    cal.add(Calendar.MILLISECOND, 50);
                    dto.setCreateDate(cal.getTime());
                    dto.setSubmitter(clmsEstimateRecords.get(0).getRecordUserId());
                }
            }else if (ReinsuranceClaimTypeEnum.CASE_REOPEN.equals(claimType)) {
                RestartCaseRecordEntity entity = restartCaseRecordMapper.getRestartAuditTime(reportNo,caseTimes-1);
                if (null != entity) {
                    dto.setCreateDate(entity.getUpdatedDate());
                }
            }
        }
        return dto;
    }
    @Override
    public List<ReinsuranceRateDTO> getReinsuranceFInfo(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType){
        List<ReinsuranceRateDTO> reinsuranceRateList = this.getReinsuranceInfo(reportNo, caseTimes, claimType);
        if(reinsuranceRateList!=null && reinsuranceRateList.size()>0){
            reinsuranceRateList = reinsuranceRateList.stream()
                    .filter(rate -> !"合约".equals(rate.getTreatyType()))
                    .collect(Collectors.toList());
        }
        return reinsuranceRateList;
    }
    @Override
    public List<ReinsuranceRateDTO> getReinsuranceInfo1(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType){
        List<ReinsuranceRateDTO> reinsuranceRateList = this.getReinsuranceInfo(reportNo, caseTimes, claimType);
        if(reinsuranceRateList!=null && reinsuranceRateList.size()>0){
            List<ReinsuranceRateDTO> filteredList = reinsuranceRateList.stream()
                    .filter(rate -> !"合约".equals(rate.getTreatyType()))
                    .collect(Collectors.toList());
            List<ReinsuranceRateDTO> contractList = reinsuranceRateList.stream()
                    .filter(rate -> "合约".equals(rate.getTreatyType()))
                    .collect(Collectors.toList());
            if (!contractList.isEmpty()) {
                // 合并合约类型记录
                ReinsuranceRateDTO mergedContract = new ReinsuranceRateDTO();
                // 合并金额比例
                BigDecimal totalRate = contractList.stream()
                        .map(ReinsuranceRateDTO::getShareRate)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                mergedContract.setShareRate(totalRate);
                BigDecimal paidLoss = contractList.stream()
                        .map(ReinsuranceRateDTO::getPaidLoss)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                mergedContract.setPaidLoss(paidLoss);
                mergedContract.setReinsName("-");
                mergedContract.setCurrency(contractList.get(0).getCurrency());
                mergedContract.setTreatyType(contractList.get(0).getTreatyType());
                // 合约放到前面
                List<ReinsuranceRateDTO> result = new ArrayList<>();
                result.add(mergedContract);
                result.addAll(filteredList);
                return result;
            }
        }
        return reinsuranceRateList;
    }

    /**
     * 获取再保联系人
     * @param reinsCode
     * @return
     */
    public List<RecipientsVO> getReinsuranceContact(String reinsCode){
        ReinsuranceReqDTO dto = new ReinsuranceReqDTO();
        dto.setReinsCode(reinsCode);
        List<ReinsurerDTO> reinsurances = reinsuranceSAO.queryReinsurer(dto);
        List<RecipientsVO> reinsuranceList = new ArrayList<>();
        for (ReinsurerDTO reinsurance : reinsurances){
            RecipientsVO recipientVO = new RecipientsVO();
            recipientVO.setRecipientName(reinsurance.getLinker());
            recipientVO.setPhone(reinsurance.getMobilePhone());
            recipientVO.setEmail(reinsurance.getEmail());
            recipientVO.setType(reinsurance.getType());
            recipientVO.setCompanyName(reinsurance.getReinsName()) ;
            recipientVO.setCompanyCode(reinsCode);
            recipientVO.setRemarks(reinsurance.getRemarks());
            reinsuranceList.add(recipientVO);
        }
        return reinsuranceList;
    }
    /**
     * 获取跳转再保URL
     * @param policyNo 保单号
     * @param accidentDate 出险日期
     * @return
     */
    public String getReinsURL(String policyNo, Date accidentDate){
        ReinsuranceReqDTO dto = new ReinsuranceReqDTO();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dto.setPolicyNo(policyNo);
        dto.setDate(sdf.format(accidentDate));
        String repolicyNo = "";
        try{
            repolicyNo = reinsuranceSAO.queryRepolicyNo(dto);

        } catch (Exception e) {
           log.error("getReinsURL error, policyNo={}, accidentDate={}", policyNo, accidentDate, e);
        }
        if (StringUtils.isBlank(repolicyNo)) {
            return null;
        }else {
            return thirdServiceConfig.getReinsURL()+repolicyNo+"?recertifyNo="+repolicyNo;
        }
    }
    @Transactional
    public String sendReinsEmail(ReinsApiVO reinsApiVO){
        String msg = "";
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        //邮件发送
        MailSendDTO mailSendDTO = new MailSendDTO();
        ReinsBillDTO reinsBillDTO = new ReinsBillDTO();
        mailSendDTO.setHandler(userInfoDTO.getUserCode());
        mailSendDTO.setSendTime(new Date());
        reinsBillDTO.setBillType(reinsApiVO.getDocumentType());
        reinsBillDTO.setReportNo(reinsApiVO.getReportNo());
        reinsBillDTO.setBillNo(Integer.valueOf(reinsApiVO.getDocumentOrder()));
        reinsBillDTO.setReinsCode(reinsApiVO.getReinsCode());
        List<ReinsBillDTO> reinsBillList = reinsBillMapper.getBillByEntity(reinsBillDTO);
        if( reinsBillList!=null && !reinsBillList.isEmpty()){
            reinsBillDTO = reinsBillList.get(0);
        }
        mailSendDTO.setBusinessNo(reinsBillDTO.getId());
        //获取邮件接收人
        StringBuilder recipients = new StringBuilder();
        for (RecipientsVO recipientVO: reinsApiVO.getRecipientList()){
            recipients.append(recipientVO.getEmail()).append(";");
        }
        if(recipients.length()>0){
            mailSendDTO.setSendTo(recipients.toString());//接收人
        }else {
            throw new GlobalBusinessException("邮件接收人不能为空");
        }
        StringBuilder attachments = new StringBuilder();
        for (FileDocumentDTO fileDocumentDTO: reinsApiVO.getDocumentList()){
            String url = fileDocumentDTO.getUploadPath();
            attachments.append(fileDocumentDTO.getDocumentName()).append(":").append(url);
            attachments.append(";");
        }
        mailSendDTO.setAttachments(attachments.toString());
        mailSendDTO.setMailCc(Constants.MAIL_REINS_CC);//抄送人
        //邮件发送人
        mailSendDTO.setSendFrom(userInfoDTO.getEmail());
        Map<String, String> titleMap = buildContentAndTitle(reinsApiVO, null);
        MailTypeEnum mailType;
        //获取邮件标题
        String title = "";
        if("2".equals(reinsApiVO.getDocumentType())){
            mailType = MailTypeEnum.REINS_ACTUAL_TITLE;
        }else{
            mailType = MailTypeEnum.REINS_ESTIMATED_TITLE;
        }
        title = mailSendService.getContextAndTitle(mailType,titleMap);
        mailSendDTO.setTitle(title);
        if("2".equals(reinsApiVO.getDocumentType())){
            mailType = MailTypeEnum.REINS_ACTUAL;
        }else{
            mailType = MailTypeEnum.REINS_ESTIMATED;
        }
        //获取邮件内容
        String body = mailSendService.getContextAndTitle(mailType,titleMap);
        mailSendDTO.setContent(body);
        mailSendDTO.setMailType(mailType.getType());
        SmsResult result = mailSendService.sendReinsMail(mailSendDTO);
        if(Constants.SMS_SEND_SUCCESS.equals(result.getIsSuccess())){
            msg = "邮件发送成功";
        }else{
            msg = "邮件发送失败";
        }
        return msg;
    }
    //构建邮件内容及标题数据
    public Map<String,String> buildContentAndTitle(ReinsApiVO reinsApiVO, String mailTyp){
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        Map <String,String> params = new HashMap<>();
        String reportNo = reinsApiVO.getReportNo();
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
        List<PolicyAndCustomerInfoVO> policyAndCustomerInfoList =
                policyInfoMapper.getPolicyAndCustomerInfoList(reportNo);
        PolicyAndCustomerInfoVO policyAndCustomerInfoVO = policyAndCustomerInfoList.get(0);
        ReinsBillDTO reinsBillDTO = new ReinsBillDTO();
        reinsBillDTO.setBillType(reinsApiVO.getDocumentType());
        reinsBillDTO.setReportNo(reportNo);
        reinsBillDTO.setBillNo(Integer.valueOf(reinsApiVO.getDocumentOrder()));
        reinsBillDTO.setReinsCode(reinsApiVO.getReinsCode());
        List<ReinsBillDTO> reinsBillList = reinsBillMapper.getBillByEntity(reinsBillDTO);
        if( reinsBillList!=null && reinsBillList.size()>0){
            reinsBillDTO = reinsBillList.get(0);
        }
        params.put("sendName",userInfoDTO.getUserName());
        params.put("reportNo",reportNo);
        params.put("accidentDate", String.valueOf(reportAccident.getAccidentDate()));
        params.put("insuredName", policyAndCustomerInfoVO.getClientName());
        params.put("shareRate",reinsBillDTO.getShareRate().toString());
        params.put("currency",reinsBillDTO.getCurrency());
        params.put("sumLoss",reinsBillDTO.getSumLoss().toString());
        params.put("paidLoss",reinsBillDTO.getPaidLoss().toString());
        params.put("policyNo",reinsBillDTO.getPolicyNo());
        return params;
    }
}
