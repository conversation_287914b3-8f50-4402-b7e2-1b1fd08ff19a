package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.model.dto.other.CityDefineDTO;
import com.paic.ncbs.claim.model.vo.other.CityDefineVO;
import com.paic.ncbs.claim.model.vo.other.CountryVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CityDefineMapper {

    public List<CityDefineDTO> getCityDefineList(@Param("cityCode") String cityCode, @Param("isArea") String isArea);

    public String getCityCodeByCityName(@Param("provinceCode") String provinceCode, @Param("cityName") String cityName);

    public String getCityCodeByCityNameType(@Param("provinceCode") String provinceCode, @Param("cityName") String cityName, @Param("cityType") String cityType);

    public List<String> getCityCodeList(@Param("cityCode") String cityCode, @Param("isArea") String isArea);

    public List<CountryVO> getCountryByContinent(@Param("continentCode") String continentCode);

    public List<CountryVO> getContinentByCountry(@Param("countryName") String countryName);

    public List<CityDefineVO> getProvinceByCity(@Param("cityChineseName") String cityChineseName);

    String getCityCodeByProvinceCityName(@Param("provinceCode") String provinceCode,@Param("cityName") String cityName);

    String getCountryCodeByProvinceCityName(@Param("provinceCode") String provinceCode,@Param("cityName") String cityName);

    List<CountryVO> getContinentByCountryVO(CountryVO countryVO);

    List<CityDefineDTO>getCityOrDistrictList(CityDefineDTO cityDefineDTO);
}
