package com.paic.ncbs.claim.service.indicators.impl;

import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 首次结案时效计算
 */
@Service("Indicator4firstEndCase")
public class Indicator4firstEndCase extends  ClmsCaseIndicatorServiceImpl{
    @Override
    protected void saveStablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.stable4firstEndCase(lastLog, now);
    }

    @Override
    protected void resaveUnstablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.unstable4firstEndCase(lastLog, now);
    }
}
