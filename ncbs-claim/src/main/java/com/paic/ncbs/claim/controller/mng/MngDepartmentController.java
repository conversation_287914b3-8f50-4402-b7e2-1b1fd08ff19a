package com.paic.ncbs.claim.controller.mng;

import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.service.user.DepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Api(tags = "部门信息")
@Controller
@RequestMapping("/mng/app/departmentAction")
public class MngDepartmentController extends BaseController {

    @Autowired
    DepartmentService departmentService;

    @ApiOperation("查询部门列表")
    @ResponseBody
    @GetMapping(value = "/getDepartmentList")
    public ResponseResult<List<DepartmentDTO>> getDepartmentList(){
        return ResponseResult.success(departmentService.getDepartmentList());
    }
}
