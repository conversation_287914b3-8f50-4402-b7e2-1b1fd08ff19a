package com.paic.ncbs.claim.replevy.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PayThawBody {
    //请求body体内容
    private  PayThawVo cashflowUseInfo;
    @ApiModelProperty("批量结算主信息")
    BatchRecieveMainInfo batchRecieveMainInfo;
    @ApiModelProperty("理赔收款结算明细信息")
    List<BatchPaymentDetailInfo> batchPaymentDetailInfo;
    @ApiModelProperty("银行流水信息")
    List<BankTransFlow> bankTransFlowList;
    @ApiModelProperty("查询关联实收对象")
    private CashFlowSearchVo cashflowSearch;
    @ApiModelProperty("理赔收款附件信息")
    private List<PayFileDto> fileList;
    @ApiModelProperty("理赔收款坏账信息")
    private BadbillInfoVo batchBadBillInfo;
}
