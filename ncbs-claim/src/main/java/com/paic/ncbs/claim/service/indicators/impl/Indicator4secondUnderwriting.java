package com.paic.ncbs.claim.service.indicators.impl;

import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 二核时效计算
 */
@Service("Indicator4secondUnderwriting")
public class Indicator4secondUnderwriting extends  ClmsCaseIndicatorServiceImpl{
    @Override
    protected void saveStablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.stable4secondUnderwriting(lastLog, now);
    }

    @Override
    protected void resaveUnstablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.unstable4secondUnderwriting(lastLog, now);
    }
}
