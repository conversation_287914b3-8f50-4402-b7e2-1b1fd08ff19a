package com.paic.ncbs.claim.model.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("理赔案件对象")
@Data
public class ReportQueryInfoDTO {

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty(value = "案件状态", notes = "1：在途；2：结案；3：零结注销;4：零赔付")
    private String caseStatus;

    public String getKey() {
        return this.reportNo + "-" + this.policyNo;
    }

    @ApiModelProperty("案件赔款金额")
    private BigDecimal settleAmount;

    @ApiModelProperty("案件免赔金额")
    private BigDecimal remitAmount;
}
