package com.paic.ncbs.claim.service.fileupload;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.doc.CustomerSupplementsDTO;
import com.paic.ncbs.claim.model.dto.doc.DocumentTypeDTO;
import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;
import com.paic.ncbs.claim.model.dto.fileupload.SelfDocTypeDTO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.Set;


public interface DocumentTypeService {

    List<DocumentTypeDTO> getDocumentAllTypeList() throws GlobalBusinessException;

    List<DocumentTypeDTO> getDocumentSmallTypeList() throws GlobalBusinessException;

    String getBigCodeBySmallCode(String smallCode) throws GlobalBusinessException;

    List<SelfDocTypeDTO> getSelfTypeByScene(String scene) throws GlobalBusinessException;

    Map<String, Set<String>> getExemptDocumentByReportNo(String reportNo) throws GlobalBusinessException;

    void customerSupplements(@RequestBody CustomerSupplementsDTO customerSupplementsDTO);

    /**
     * 客户补材完成后回调
     * @param reportNo
     * @param caseTimes
     * @param remark
     */
    void customerSupplementsSuccess(String reportNo, Integer caseTimes, String remark);

    /**
     * 更新超期没有处理的任务
     */
    public void updateServiceData(SupplementsMaterialDTO dto);

    /**
     * 查询客户补材
     * @param reportNo
     * @param caseTimes
     */
    List<SupplementsMaterialDTO> getCustomerSupplements(String reportNo, Integer caseTimes);

}
