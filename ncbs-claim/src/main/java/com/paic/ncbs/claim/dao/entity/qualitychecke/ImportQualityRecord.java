package com.paic.ncbs.claim.dao.entity.qualitychecke;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
@Data
public class ImportQualityRecord {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id")
    private String id;
    /**
     * 批次号，生成规则：BZ+YYYYMMDD+5位序号，序号从00001开始
     */
    @TableField(value = "batch_no")
    private String batchNo;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 工号
     */
    @TableField("emp_no")
    private String qinitiator;

    /**
     * 导入时间
     */
    @TableField("import_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime importTime;

    /**
     * 状态，为生成时任务状态：0-失败、1-成功
     */
    @TableField("status")
    private String taskStatus;

    @TableField("fail_reason")
    private String failReason;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sysUtime;
}
