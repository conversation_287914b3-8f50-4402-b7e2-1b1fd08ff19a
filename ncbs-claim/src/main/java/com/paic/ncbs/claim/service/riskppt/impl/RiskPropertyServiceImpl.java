package com.paic.ncbs.claim.service.riskppt.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.EstimateTypeEnum;
import com.paic.ncbs.claim.common.enums.GlobalProductClassEnum;
import com.paic.ncbs.claim.common.enums.ProductClassEnum;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.ocas.ProductInfoDTO;
import com.paic.ncbs.claim.model.dto.riskppt.*;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyPayService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@RefreshScope
@Service("riskPropertyService")
@Slf4j
public class RiskPropertyServiceImpl implements RiskPropertyService {

    @Autowired
    private RiskPropertyMapper riskPropertyMapper;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;
    @Autowired
    private RiskPropertyPayService riskPropertyPayService;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    @Value("${switch.riskProperty:false}")
    private boolean switchRiskProperty;

    //996,04001,995,USE_PACKAGE_CONFIG,04002,89,903,23002,1044,927,M0154,M0145,M0150,M0013,610,904,902,M0167,M0163,22007,1000,01
    @Value("${riskproperty.types}")
    private List<String> riskPropertyPayTypes;
    @Value("${riskproperty.classes}")
    private List<String> riskPropertyClasses;

    @Override
    public List<PolicyGroupDTO> buildPlyGroupDTO(List<CaseRiskPropertyDTO> riskPropertyList,List<CaseRiskPropertyDTO> selectedPropertyList){
        if(ListUtils.isEmptyList(riskPropertyList)){
            return new ArrayList<>();
        }
        boolean isSelected = ListUtils.isNotEmpty(selectedPropertyList);
        Map<String,CaseRiskPropertyDTO> selectedMap = null;
        if(isSelected){
            parseRiskDetail(selectedPropertyList);
            selectedMap = getSelectedPropertyMap(selectedPropertyList);
        }

        Map<String,Map<String,List<CaseRiskPropertyDTO>>> policyRiskGroupMap = riskPropertyList.stream().collect(
                Collectors.groupingBy(RiskPropertyDTO::getPolicyNo,Collectors.groupingBy(RiskPropertyDTO::getIdPlyRiskGroup)));
        List<PolicyGroupDTO> policyGroupList = new ArrayList<>();
        PolicyGroupDTO policyGroup = null;
        for (Map.Entry<String,Map<String,List<CaseRiskPropertyDTO>>> policy : policyRiskGroupMap.entrySet()){
            policyGroup = new PolicyGroupDTO();
            policyGroup.setPolicyNo(policy.getKey());
            List<RiskGroupDTO> riskGroupList = new ArrayList<>();
            RiskGroupDTO riskGroup = null;
            CaseRiskPropertyDTO tempRiskDTO = new CaseRiskPropertyDTO();
            for (Map.Entry<String,List<CaseRiskPropertyDTO>> group : policy.getValue().entrySet()){
                List<CaseRiskPropertyDTO> propertyList = group.getValue();
                BeanUtils.copyProperties(propertyList.get(0),tempRiskDTO);
                riskGroup = new RiskGroupDTO();
                riskGroup.setIdPlyRiskGroup(group.getKey());
                riskGroup.setRiskGroupNo(tempRiskDTO.getRiskGroupNo());
                riskGroup.setRiskGroupName(tempRiskDTO.getRiskGroupName());
                riskGroup.setRiskPropertyInfoList(propertyList);
                riskGroupList.add(riskGroup);
                policyGroup.setRiskType(tempRiskDTO.getRiskType());
                if(isSelected){
                    setSelectedProperty(propertyList,selectedMap);
                }
            }
            for (RiskGroupDTO riskGroupDTO : riskGroupList) {
                for (CaseRiskPropertyDTO dto : riskGroupDTO.getRiskPropertyInfoList()) {
                    dto.setRiskGroupNo(null);
                    dto.setRiskGroupName(null);
                    dto.setRiskDetail(null);
                }
            }

            policyGroup.setRiskGroupList(riskGroupList);
            policyGroupList.add(policyGroup);
        }
        return policyGroupList;
    }

    private Map<String,CaseRiskPropertyDTO> getSelectedPropertyMap(List<CaseRiskPropertyDTO> selectedPropertyList){
        Map<String,CaseRiskPropertyDTO> selectedMap= new HashMap<>();
        if(ListUtils.isNotEmpty(selectedPropertyList)){
            selectedMap = selectedPropertyList.stream().collect(Collectors.toMap(k->k.getIdPlyRiskProperty(),v->v));
        }
        return selectedMap;
    }

    private void setSelectedProperty(List<CaseRiskPropertyDTO> caseRiskPropertyList,Map<String,CaseRiskPropertyDTO> selectedMap){
        if(ListUtils.isEmptyList(caseRiskPropertyList)){
            return;
        }
        for (CaseRiskPropertyDTO caseRiskPropertyDTO : caseRiskPropertyList) {
            CaseRiskPropertyDTO selectedPropertyDTO = selectedMap.get(caseRiskPropertyDTO.getIdPlyRiskProperty());
            if(null != selectedPropertyDTO){
                caseRiskPropertyDTO.setSelected(true);
            }
        }

    }

    private void parseRiskDetail(List<CaseRiskPropertyDTO> caseRiskPropertyList){
        if(ListUtils.isEmptyList(caseRiskPropertyList)){
            return;
        }
        for (CaseRiskPropertyDTO riskPropertyDTO : caseRiskPropertyList) {
            String riskDetail = riskPropertyDTO.getRiskDetail();
            if(riskDetail != null && riskDetail.startsWith("{")){
                riskPropertyDTO.setRiskPropertyMap(JSON.parseObject(riskDetail,Map.class));
            }
            riskPropertyDTO.setRiskDetail(null);
        }
    }

    @Override
    public List<PolicyGroupDTO> getPlyRiskProperty(PlyRiskGroupQueryDTO queryDTO) {
        if(!switchRiskProperty){
            return null;
        }

        List<CaseRiskPropertyDTO> plyRiskList = riskPropertyMapper.getPlyRiskPropertyList(queryDTO);
        parseRiskDetail(plyRiskList);
        return buildPlyGroupDTO(plyRiskList,null);
    }

    @Override
    public List<PolicyGroupDTO> getCaseRiskProperty(CaseRiskPropertyDTO caseDTO) {
        if(!switchRiskProperty){
            return null;
        }

        String taskId = caseDTO.getTaskId();
        if(StringUtils.isEmptyStr(taskId)){
            taskId = riskPropertyMapper.getCaseRiskPropertyLastTaskId(caseDTO);
            if(StringUtils.isEmptyStr(taskId)){
                throw new GlobalBusinessException("环节号不能为空");
            }
            caseDTO.setTaskId(taskId);
        }
        List<CaseRiskPropertyDTO> selectedRiskPropertyList = getCaseRiskPropertyList(caseDTO);
        if(ListUtils.isEmptyList(selectedRiskPropertyList)){
            CaseRiskPropertyDTO caseQueryDTO = new CaseRiskPropertyDTO();
            BeanUtils.copyProperties(caseDTO,caseQueryDTO);
            if(BpmConstants.REPORT_TRACK.equals(taskId)){
                caseQueryDTO.setTaskId("report1");
            }else if(BpmConstants.CHECK_DUTY.equals(taskId)){
                caseQueryDTO.setTaskId(BpmConstants.REPORT_TRACK);
            }else{
                caseQueryDTO.setTaskId(BpmConstants.CHECK_DUTY);
            }
            selectedRiskPropertyList = getCaseRiskPropertyList(caseQueryDTO);
        }

        if(!caseDTO.isMatchSelected()){
            return buildPlyGroupDTO(selectedRiskPropertyList,null);
        }

        return buildPlyGroupDTO(getReportRiskPropertyList(caseDTO),selectedRiskPropertyList);
    }

    @Override
    @Transactional
    public void saveCaseRiskPropertyList(RiskDomainDTO riskDomainDTO) {
        if(!switchRiskProperty){
            return;
        }
        buildCaseRiskPropertyList(riskDomainDTO);
        if(ListUtils.isNotEmpty(riskDomainDTO.getRiskPropertyList())){
            CaseRiskPropertyDTO caseRiskPropertyDTO = new CaseRiskPropertyDTO();
            caseRiskPropertyDTO.setUpdatedBy(riskDomainDTO.getUserId());
            caseRiskPropertyDTO.setReportNo(riskDomainDTO.getReportNo());
            caseRiskPropertyDTO.setCaseTimes(riskDomainDTO.getCaseTimes());
            caseRiskPropertyDTO.setTaskId(riskDomainDTO.getTaskId());
            riskPropertyMapper.removeCaseRiskProperty(caseRiskPropertyDTO);
            riskPropertyMapper.saveCaseRiskPropertyList(riskDomainDTO.getRiskPropertyList());
        }
    }

    private void buildCaseRiskPropertyList(RiskDomainDTO riskDomainDTO){
        if(riskDomainDTO == null || StringUtils.isEmptyStr(riskDomainDTO.getReportNo())){
            throw new GlobalBusinessException("报案号不能为空");
        }
        if(StringUtils.isEmptyStr(riskDomainDTO.getTaskId()) || StringUtils.isEmptyStr(riskDomainDTO.getUserId()) ){
            throw new GlobalBusinessException("用户ID不能为空");
        }

        List<PolicyGroupDTO> policyGroupList = null;
        if(ListUtils.isNotEmpty(riskDomainDTO.getPolicyGroupList())){
            policyGroupList = filterRiskProperty(riskDomainDTO.getPolicyGroupList());
        }
        if(ListUtils.isEmptyList(policyGroupList)){
            return;
        }

        Map<String,CaseRiskPropertyDTO> reportRiskMap = getReportRiskPropertyMap(riskDomainDTO.getReportNo(),policyGroupList,null);
        List<CaseRiskPropertyDTO> riskPropertyList = new ArrayList<>();
        riskDomainDTO.setRiskPropertyList(riskPropertyList);
        for (PolicyGroupDTO policyGroupDTO : policyGroupList) {
            if(ListUtils.isEmptyList(policyGroupDTO.getRiskGroupList())){
                continue;
            }
            for (RiskGroupDTO riskGroupDTO : policyGroupDTO.getRiskGroupList()) {
                riskPropertyList.addAll(matchCaseRiskProperty(riskDomainDTO,reportRiskMap,riskGroupDTO.getRiskPropertyInfoList()));
            }
        }
    }

    @Override
    public void setEstRiskPropertyPlan(List<EstimatePolicyDTO> estimatePolicyList) {
        if(!switchRiskProperty){
            return;
        }
        if(ListUtils.isEmptyList(estimatePolicyList)){
            return;
        }

        String reportNo = estimatePolicyList.get(0).getReportNo();
        Integer caseTimes = estimatePolicyList.get(0).getCaseTimes();
        CaseRiskPropertyDTO caseRiskQuery = new CaseRiskPropertyDTO(reportNo,caseTimes,"report1");
        List<CaseRiskPropertyDTO> reportRiskList = getCaseRiskPropertyList(caseRiskQuery);

        Map<String, List<CaseRiskPropertyDTO>> riskPropertyMap = CollectionUtils.isEmpty(reportRiskList) ?
                new HashMap<>() : reportRiskList.stream().collect(Collectors.groupingBy(RiskPropertyDTO::getPolicyNo));

        List<CaseBaseDTO> caseBaseList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
        Map<String, List<CaseBaseDTO>> caseBaseMap = caseBaseList.stream().collect(Collectors.groupingBy(CaseBaseDTO::getPolicyNo));

        for (EstimatePolicyDTO policy : estimatePolicyList) {
            List<EstimatePlanDTO> estPlanList = new ArrayList<>();

            List<CaseRiskPropertyDTO> caseRiskPropertyList = riskPropertyMap.get(policy.getPolicyNo());
            String riskId = CollectionUtils.isEmpty(caseRiskPropertyList) ? null : caseRiskPropertyList.get(0).getIdPlyRiskProperty();
            String riskGroupNo = caseBaseMap.get(policy.getPolicyNo()).get(0).getRiskGroupNo();

            for (EstimatePlanDTO plan : policy.getEstimatePlanList()) {
                if (org.apache.commons.lang3.StringUtils.equals(riskGroupNo, plan.getRiskGroupNo())) {
                    plan.setIdPlyRiskProperty(riskId);
                    estPlanList.add(plan);

                    for (EstimateDutyDTO duty : plan.getEstimateDutyList()) {
                        duty.setIdPlyRiskProperty(riskId);
                        duty.setRiskGroupNo(plan.getRiskGroupNo());
                        duty.setRiskGroupName(plan.getRiskGroupName());
                    }
                }
            }

            policy.setEstimatePlanList(estPlanList);
        }
    }

    @Override
    public void setEstRiskProperty(List<EstimatePolicyDTO> estimatePolicyList,List<CaseRiskPropertyDTO> reportRiskList) {
        if(ListUtils.isEmptyList(estimatePolicyList)){
            return;
        }

        String reportNo = estimatePolicyList.get(0).getReportNo();
        Integer caseTimes = estimatePolicyList.get(0).getCaseTimes();
        if(!displayRiskProperty(reportNo,null)){
            return;
        }

        filterDuty(estimatePolicyList);
        Map<String,List<RiskGroupDTO>> policyGroupMap = null;
        if(ListUtils.isNotEmpty(reportRiskList)){
            policyGroupMap = getReportPolicyGroupMap(reportRiskList);
        }else{
            policyGroupMap = getPolicyGroupMap(reportNo,caseTimes,BpmConstants.REPORT_TRACK);
        }
        Map<String,List<EstimatePlanDTO>> estPlanMap = null;
        Map<String,List<EstimatePlanDTO>> reportPlanMap = null;
        for (EstimatePolicyDTO policy : estimatePolicyList) {
            policy.setRiskGroupList(policyGroupMap.get(policy.getPolicyNo()));
            List<EstimatePlanDTO> planList = policy.getEstimatePlanList();
            if(ListUtils.isEmptyList(planList)){
                continue;
            }

            List<EstimatePolicyDTO> copyEstimatePolicy = null;
            estPlanMap = planList.stream().collect(Collectors.groupingBy(EstimatePlanDTO::getIdPlyRiskProperty));
            for (RiskGroupDTO riskGroup : policy.getRiskGroupList()) {
                for (CaseRiskPropertyDTO riskProp : riskGroup.getRiskPropertyInfoList()) {
                    riskProp.setSelected(true);
                    String idRiskProperty = riskProp.getIdPlyRiskProperty();
                    riskProp.setEstimatePlanList(estPlanMap.get(idRiskProperty));
                    if(ListUtils.isEmptyList(riskProp.getEstimatePlanList())){
                        if(reportPlanMap == null || reportPlanMap.get(idRiskProperty) == null){
                            if(ListUtils.isEmptyList(copyEstimatePolicy)){
                                copyEstimatePolicy = estimatePolicyMapper.getEstimatePolicyFromCopy(reportNo, caseTimes);
                            }
                            reportPlanMap = getReportCopyPlan(copyEstimatePolicy,riskProp.getIdPlyRiskGroup(),idRiskProperty);
                        }
                        riskProp.setEstimatePlanList(reportPlanMap.get(idRiskProperty));
                    }
                }
            }
            policy.setEstimatePlanList(null);
        }
    }

    @Override
    public void setEstimateRiskGroup(List<EstimatePolicyDTO> estimatePolicyList) {
        if (ListUtils.isEmptyList(estimatePolicyList)) {
            return;
        }

        String reportNo = estimatePolicyList.get(0).getReportNo();
        Integer caseTimes = estimatePolicyList.get(0).getCaseTimes();
        if (!displayRiskProperty(reportNo,null)) {
            return;
        }

        Map<String, List<RiskGroupDTO>> policyGroupMap = getPolicyGroupMap(reportNo, caseTimes);
        if (policyGroupMap == null || policyGroupMap.isEmpty()) {
            return;
        }

        for (EstimatePolicyDTO policy : estimatePolicyList) {
            policy.setRiskGroupList(policyGroupMap.get(policy.getPolicyNo()));
            List<EstimatePlanDTO> planList = policy.getEstimatePlanList();
            String riskGroupNo = ListUtils.isEmptyList(planList) ? null : planList.get(0).getRiskGroupNo();
            for (RiskGroupDTO riskGroup : policy.getRiskGroupList()) {
                if (StringUtils.isNotEmpty(riskGroupNo) && org.apache.commons.lang3.StringUtils.equals(riskGroupNo, riskGroup.getRiskGroupNo())) {
                    riskGroup.setEstimatePlanList(planList);
                    continue;
                }

                for (EstimatePlanDTO plan : riskGroup.getEstimatePlanList()) {
                    List<EstimateDutyRecordDTO> dutyRecordList =  new ArrayList<>();
                    for (EstimateDutyDTO duty : plan.getEstimateDutyList()) {
                        EstimateDutyRecordDTO dutyRecord = new EstimateDutyRecordDTO();
                        BeanUtils.copyProperties(duty, dutyRecord);
                        dutyRecord.setCaseNo(policy.getCaseNo());
                        dutyRecord.setCaseTimes(policy.getCaseTimes());
                        dutyRecord.setPolicyNo(policy.getPolicyNo());
                        dutyRecord.setPlanCode(plan.getPlanCode());
                        dutyRecord.setRiskGroupNo(riskGroup.getRiskGroupNo());
                        dutyRecord.setRiskGroupName(riskGroup.getRiskGroupName());
                        dutyRecordList.add(dutyRecord);
                    }
                    plan.setEstimateDutyRecordList(dutyRecordList);
                    plan.setEstimateDutyList(null);
                    plan.setCaseNo(policy.getCaseNo());
                    plan.setCaseTimes(policy.getCaseTimes());
                    plan.setPolicyNo(policy.getPolicyNo());
                    plan.setIdAhcsEstimatePolicy(policy.getIdAhcsEstimatePolicy());
                }
            }
            policy.setEstimatePlanList(null);
        }
    }

    private Map<String, List<RiskGroupDTO>> getPolicyGroupMap(String reportNo, Integer caseTimes) {
        List<EstimatePolicyDTO> copyEstimatePolicy = estimatePolicyMapper.getEstimatePolicyFromCopy(reportNo, caseTimes);
        log.info("预估标的查询结果，reportNo：{}，result:{}", reportNo, JsonUtils.toJsonString(copyEstimatePolicy));
        if (ListUtils.isEmptyList(copyEstimatePolicy)) {
            return null;
        }

        Map<String, List<RiskGroupDTO>> policyGroupMap = new HashMap<>();
        for (EstimatePolicyDTO estimatePolicy : copyEstimatePolicy) {
            String policyNo = estimatePolicy.getPolicyNo();
            Map<String, List<EstimatePlanDTO>> groupMap = estimatePolicy.getEstimatePlanList().stream().collect(Collectors.groupingBy(EstimatePlanDTO::getRiskGroupNo));

            List<RiskGroupDTO> riskGroupList = new ArrayList<>();
            for (Map.Entry<String, List<EstimatePlanDTO>> groupEntry : groupMap.entrySet()) {
                List<EstimatePlanDTO> estimatePlanList = groupEntry.getValue();
                EstimatePlanDTO estimatePlan = estimatePlanList.get(0);
                String plyRiskGroupType = riskPropertyMapper.getPlyRiskGroupType(policyNo, estimatePlan.getRiskGroupNo());
                RiskGroupDTO group = new RiskGroupDTO();
                group.setRiskGroupNo(estimatePlan.getRiskGroupNo());
                group.setRiskGroupName(estimatePlan.getRiskGroupName());
                group.setRiskGroupType(plyRiskGroupType);
                group.setEstimatePlanList(estimatePlanList);
                riskGroupList.add(group);
            }
            policyGroupMap.put(policyNo, riskGroupList);
        }
        return policyGroupMap;
    }

    private void filterDuty(List<EstimatePolicyDTO> estimatePolicyList){
        if(ListUtils.isEmptyList(estimatePolicyList)){
            return;
        }
        for (EstimatePolicyDTO policy:estimatePolicyList){
            List<EstimatePlanDTO> planDTOList = policy.getEstimatePlanList();
            if(ListUtils.isEmptyList(planDTOList)){
                continue;
            }
            for (EstimatePlanDTO planDTO : planDTOList) {
                String idRiskProperty = planDTO.getIdPlyRiskProperty();
                List<EstimateDutyRecordDTO> dutyList = planDTO.getEstimateDutyRecordList();
                if(StringUtils.isEmptyStr(idRiskProperty) || ListUtils.isEmptyList(dutyList)){
                    continue;
                }
                planDTO.setEstimateDutyRecordList(dutyList.stream().filter(duty -> idRiskProperty.equals(duty.getIdPlyRiskProperty())).collect(Collectors.toList()));
            }
        }
    }


    private Map<String,List<RiskGroupDTO>> getPolicyGroupMap(String reportNo,Integer caseTimes,String taskId){
        List<PolicyGroupDTO> policyGroupList = getCaseRiskProperty(new CaseRiskPropertyDTO(reportNo, caseTimes,taskId));
        if(ListUtils.isEmptyList(policyGroupList)){
            throw new GlobalBusinessException("案件标的为空");
        }
        Map<String,List<RiskGroupDTO>> riskGroupMap = new HashMap<>();
        for (PolicyGroupDTO policy : policyGroupList) {
            riskGroupMap.put(policy.getPolicyNo(),policy.getRiskGroupList());
        }
        return riskGroupMap;
    }

    private Map<String,List<RiskGroupDTO>> getReportPolicyGroupMap(List<CaseRiskPropertyDTO> reportRiskList){
        List<PolicyGroupDTO> policyGroupList = buildPlyGroupDTO(reportRiskList,null);
        if(ListUtils.isEmptyList(policyGroupList)){
            throw new GlobalBusinessException("案件标的为空");
        }
        Map<String,List<RiskGroupDTO>> riskGroupMap = new HashMap<>();
        for (PolicyGroupDTO policy : policyGroupList) {
            riskGroupMap.put(policy.getPolicyNo(),policy.getRiskGroupList());
        }
        return riskGroupMap;
    }

    @Override
    public List<CaseRiskPropertyDTO> getReportRiskPropertyList(CaseRiskPropertyDTO caseDTO) {
        List<CaseRiskPropertyDTO> reportRiskList = riskPropertyMapper.getReportRiskPropertyList(caseDTO);
        parseRiskDetail(reportRiskList);
        return reportRiskList;
    }

    @Override
    public List<CaseRiskPropertyDTO> getCaseRiskPropertyList(CaseRiskPropertyDTO caseDTO) {
        List<CaseRiskPropertyDTO> caseRiskList = riskPropertyMapper.getCaseRiskPropertyList(caseDTO);
        parseRiskDetail(caseRiskList);
        return caseRiskList;
    }

    @Override
    public void checkTargetType(List<String> policyNos) {
        if(!switchRiskProperty){
            return;
        }
        if(policyNos.size() > 1){
            for (String policyNo : policyNos) {
                if(displayRiskProperty(policyNo)){
                    throw new GlobalBusinessException("责任险暂不支持多保单理赔,请重新选择一张保单");
                }
            }
        }
    }

    @Override
    public Map<String,CaseRiskPropertyDTO> getReportRiskPropertyMap(String reportNo,List<PolicyGroupDTO> policyGroups,List<ReportRiskPropertyDTO> reportRiskDTOList){
        List<CaseRiskPropertyDTO> reportRiskList = new ArrayList<>();
        if(ListUtils.isNotEmpty(reportRiskDTOList)){
            for (ReportRiskPropertyDTO  reportRisk: reportRiskDTOList) {
                CaseRiskPropertyDTO caseRisk = new CaseRiskPropertyDTO();
                BeanUtils.copyProperties(reportRisk,caseRisk);
                reportRiskList.add(caseRisk);
            }
        }else{
            List<String> riskIdList = new ArrayList<>();
            for (PolicyGroupDTO policyGroup : policyGroups) {
                for (RiskGroupDTO riskGroup: policyGroup.getRiskGroupList()) {
                    for (CaseRiskPropertyDTO riskDTO : riskGroup.getRiskPropertyInfoList()) {
                        riskIdList.add(riskDTO.getIdPlyRiskProperty());
                    }
                }
            }
            CaseRiskPropertyDTO caseDTO = new CaseRiskPropertyDTO(reportNo,null,null);
            caseDTO.setRiskPropertyIdList(riskIdList);
            reportRiskList = getReportRiskPropertyList(caseDTO);
        }

        if(ListUtils.isEmptyList(reportRiskList)){
            return new HashMap<>();
        }

        return reportRiskList.stream().collect(Collectors.groupingBy(
                CaseRiskPropertyDTO::getIdPlyRiskProperty, Collectors.collectingAndThen(Collectors.toList(),v->v.get(0))));
    }

    @Override
    public List<CaseRiskPropertyDTO> matchCaseRiskProperty(RiskDomainDTO riskDomainDTO,Map<String, CaseRiskPropertyDTO> reportRiskMap, List<CaseRiskPropertyDTO> selectedCaseRiskList) {
        if(ListUtils.isEmptyList(selectedCaseRiskList)){
            throw new GlobalBusinessException("标的为空");
        }
        List<CaseRiskPropertyDTO> resultCaseRiskList = new ArrayList<>();
        for (CaseRiskPropertyDTO caseRisk : selectedCaseRiskList) {
            CaseRiskPropertyDTO plyRiskDTO = reportRiskMap.get(caseRisk.getIdPlyRiskProperty());
            if(plyRiskDTO == null){
                throw new GlobalBusinessException("标的异常,请核实标的");
            }
            CaseRiskPropertyDTO claimRiskDTO = new CaseRiskPropertyDTO();
            BeanUtils.copyProperties(plyRiskDTO,claimRiskDTO);
            claimRiskDTO.setIdCaseRiskProperty(UuidUtil.getUUID());
            claimRiskDTO.setReportNo(riskDomainDTO.getReportNo());
            claimRiskDTO.setCaseTimes(riskDomainDTO.getCaseTimes());
            claimRiskDTO.setCreatedBy(riskDomainDTO.getUserId());
            claimRiskDTO.setUpdatedBy(riskDomainDTO.getUserId());
            claimRiskDTO.setTaskId(riskDomainDTO.getTaskId());
            if(StringUtils.isEmptyStr(claimRiskDTO.getRiskDetail()) && plyRiskDTO.getRiskPropertyMap() != null){
                claimRiskDTO.setRiskDetail(JSON.toJSONString(plyRiskDTO.getRiskPropertyMap()));
            }
            resultCaseRiskList.add(claimRiskDTO);
        }
        return resultCaseRiskList;
    }

    @Override
    public void setRiskPropertyPlan(List<PolicyPayDTO> policyPayList) {
        if(!isRiskProprertyCase(policyPayList)){
            return;
        }
        String reportNo = policyPayList.get(0).getReportNo();
        Integer caseTimes = policyPayList.get(0).getCaseTimes();

        Map<String,List<RiskGroupDTO>> policyGroupMap = getPolicyGroupMap(reportNo,caseTimes,null);
        //key=方案；value=险种列表
        Map<String,List<PlanPayDTO>> copyPlanMap = null;
        for (PolicyPayDTO policy : policyPayList) {
            List<PlanPayDTO> riskPlanList = new ArrayList<>();
            List<PlanPayDTO> planList = policy.getPlanPayArr();
            if(ListUtils.isEmptyList(planList)){
                continue;
            }
            copyPlanMap = planList.stream().collect(Collectors.groupingBy(PlanPayDTO::getIdPlyRiskGroup));
            Map<String,List<CaseRiskPropertyDTO>> riskpptMap = new HashMap<>();
            for (RiskGroupDTO group : policyGroupMap.get(policy.getPolicyNo())) {
                for (CaseRiskPropertyDTO riskDTO : group.getRiskPropertyInfoList()){
                    List<PlanPayDTO> riskPlans = copyPlanMap.get(riskDTO.getIdPlyRiskGroup());
                    if(ListUtils.isEmptyList(riskPlans)){
                        throw new GlobalBusinessException("标的方案无险种");
                    }
                    riskPlans = clonePlanList(riskPlans);
                    setPlanDutyDetailRiskId(riskDTO.getIdPlyRiskProperty(),riskPlans);
                    riskPlanList.addAll(riskPlans);
                }
            }

            policy.setPlanPayArr(riskPlanList);
        }
    }

    private List<PlanPayDTO> clonePlanList(List<PlanPayDTO> planList){
        List<PlanPayDTO> copyPlanList = new ArrayList<>();
        PlanPayDTO plan;
        for (PlanPayDTO planPayDTO : planList) {
            List<DutyPayDTO> dutyList = new ArrayList<>();
            for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                List<DutyDetailPayDTO> detailList = new ArrayList<>();
                for (DutyDetailPayDTO detailPayDTO : dutyPayDTO.getDutyDetailPayArr()) {
                    DutyDetailPayDTO detail = new DutyDetailPayDTO();
                    BeanUtils.copyProperties(detailPayDTO,detail);
                    detailList.add(detail);
                }
                DutyPayDTO duty = new DutyPayDTO();
                BeanUtils.copyProperties(dutyPayDTO,duty);
                duty.setDutyDetailPayArr(detailList);
                dutyList.add(duty);
            }
            plan = new PlanPayDTO();
            BeanUtils.copyProperties(planPayDTO,plan);
            plan.setDutyPayArr(dutyList);
            copyPlanList.add(plan);
        }
        return copyPlanList;
    }

    private boolean isRiskProprertyCase(List<PolicyPayDTO> policyPayList){
        if(!switchRiskProperty){
            return false;
        }

        if(ListUtils.isEmptyList(policyPayList)){
            return false;
        }

        if(!displayRiskProperty(policyPayList.get(0).getReportNo(),null)){
            return false;
        }
        return true;
    }

    @Override
    public void setRiskProperty(List<PolicyPayDTO> policyPayList) {
        if(!isRiskProprertyCase(policyPayList)){
            return;
        }
        filterDutys(policyPayList);
        String reportNo = policyPayList.get(0).getReportNo();
        Integer caseTimes = policyPayList.get(0).getCaseTimes();
        Map<String,List<RiskGroupDTO>> policyGroupMap = getPolicyGroupMap(reportNo,caseTimes,BpmConstants.CHECK_DUTY);
        Map<String,List<PlanPayDTO>> planMap = null;
        for (PolicyPayDTO policy : policyPayList) {
            policy.setRiskGroupList(policyGroupMap.get(policy.getPolicyNo()));
            List<PlanPayDTO> planList = policy.getPlanPayArr();
            if(ListUtils.isEmptyList(planList)){
                continue;
            }
            planMap = planList.stream().collect(Collectors.groupingBy(PlanPayDTO::getIdPlyRiskProperty));
            for (RiskGroupDTO riskGroup : policy.getRiskGroupList()) {
                for (CaseRiskPropertyDTO riskProp : riskGroup.getRiskPropertyInfoList()) {
                    riskProp.setSelected(true);
                    riskProp.setPlanPayArr(planMap.get(riskProp.getIdPlyRiskProperty()));
                }
            }
            policy.setPlanPayArr(null);
        }
    }

    @Override
    public void setRiskGroup(List<PolicyPayDTO> policyPays) {
        if (!isRiskProprertyCase(policyPays)) {
            return;
        }

        String reportNo = policyPays.get(0).getReportNo();
        Integer caseTimes = policyPays.get(0).getCaseTimes();
        List<CaseBaseDTO> caseBaseDTOList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
        Map<String, List<CaseBaseDTO>> caseBaseMap = caseBaseDTOList.stream().collect(Collectors.groupingBy(CaseBaseDTO::getPolicyNo));
        for (PolicyPayDTO policyPay : policyPays) {
            List<CaseBaseDTO> caseBaseList = caseBaseMap.get(policyPay.getPolicyNo());
            if (CollectionUtils.isEmpty(caseBaseList)) {
                continue;
            }
            CaseBaseDTO caseBase = caseBaseList.get(0);

            List<RiskGroupDTO> riskGroupList = new ArrayList<>();
            RiskGroupDTO riskGroup = new RiskGroupDTO();
            riskGroup.setRiskGroupNo(caseBase.getRiskGroupNo());
            riskGroup.setRiskGroupName(caseBase.getRiskGroupName());
            riskGroup.setPlanPayArr(policyPay.getPlanPayArr());
            riskGroupList.add(riskGroup);
            policyPay.setRiskGroupList(riskGroupList);
            policyPay.setPlanPayArr(null);
        }
    }

    @Override
    public void setPlanDutyDetailRiskId(String idPlyRiskProperty,List<PlanPayDTO> riskPlans){
        for (PlanPayDTO plan : riskPlans) {
            plan.setIdPlyRiskProperty(idPlyRiskProperty);
            for (DutyPayDTO duty : plan.getDutyPayArr()) {
                duty.setIdPlyRiskProperty(idPlyRiskProperty);
                for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
                    detail.setIdPlyRiskProperty(idPlyRiskProperty);
                }
            }
        }
    }

    @Override
    public List<EstimatePolicyDTO> getEstPolicyByRiskPropertyId(CaseRiskPropertyDTO riskPropertyDTO) {
        String reportNo = riskPropertyDTO.getReportNo();
        Integer caseTimes = riskPropertyDTO.getCaseTimes();
        List<EstimatePolicyDTO> estimatePolicys = estimatePolicyMapper.getEstimatePolicyFromCopy(reportNo, caseTimes);
        if(ListUtils.isEmptyList(estimatePolicys)){
            return new ArrayList<>();
        }
        List<CaseRiskPropertyDTO> reportRiskList = getReportRiskPropertyList(riskPropertyDTO);
        if(ListUtils.isEmptyList(reportRiskList)){
            throw new GlobalBusinessException("标的不存在");
        }

        List<EstimatePolicyDTO> estimatePolicyList = new ArrayList<>();
        String groupId = Optional.ofNullable(reportRiskList.get(0).getIdPlyRiskGroup()).orElse("");
        for (EstimatePolicyDTO policy : estimatePolicys) {
            String estPolicyId = estimatePolicyMapper.getIdByCaseNo(policy.getCaseNo());
            policy.setIdAhcsEstimatePolicy(estPolicyId);
            List<EstimatePlanDTO> planList = new ArrayList<>();
            for (EstimatePlanDTO planDTO : policy.getEstimatePlanList()) {
                if(groupId.equals(planDTO.getIdPlyRiskGroup())){
                    planDTO.setIdPlyRiskProperty(riskPropertyDTO.getIdPlyRiskProperty());
                    planDTO.setPolicyNo(policy.getPolicyNo());
                    planDTO.setCaseNo(policy.getCaseNo());
                    planDTO.setCaseTimes(caseTimes);
                    planDTO.setIdAhcsEstimatePolicy(estPolicyId);
                    covertEstDuty(planDTO);
                    planList.add(planDTO);
                }
            }

            if(planList.size()>0){
                policy.setEstimatePlanList(planList);
                estimatePolicyList.add(policy);
            }
        }

        setEstRiskProperty(estimatePolicyList,reportRiskList);
        riskPropertyPayService.initEstRiskPropertyMaxPay(estimatePolicyList);
        return estimatePolicyList;
    }

    private void covertEstDuty(EstimatePlanDTO planDTO){
        List<EstimateDutyRecordDTO> dutyRecordList = new ArrayList<>();
        EstimateDutyRecordDTO dutyRecordDTO = null;
        for (EstimateDutyDTO duty : planDTO.getEstimateDutyList()) {
            duty.setCaseNo(planDTO.getCaseNo());
            duty.setPolicyNo(planDTO.getPolicyNo());
            duty.setPlanCode(planDTO.getPlanCode());
            duty.setCaseTimes(planDTO.getCaseTimes());

            duty.setIdPlyRiskProperty(planDTO.getIdPlyRiskProperty());
            dutyRecordDTO = new EstimateDutyRecordDTO();
            BeanUtils.copyProperties(duty,dutyRecordDTO);
            if(StringUtils.isEmptyStr(dutyRecordDTO.getEstimateType())){
                dutyRecordDTO.setEstimateType(EstimateTypeEnum.REGISTER_PENDING.getType());
            }
            if(StringUtils.isEmptyStr(dutyRecordDTO.getTaskId())){
                dutyRecordDTO.setTaskId(BpmConstants.REPORT_TRACK);
            }
            dutyRecordList.add(dutyRecordDTO);
        }
        planDTO.setEstimateDutyRecordList(dutyRecordList);
        planDTO.setEstimateDutyList(null);

    }

    private List<PolicyGroupDTO> filterRiskProperty(List<PolicyGroupDTO> policyGroups){
        List<PolicyGroupDTO> policyList = new ArrayList<>();
        for (PolicyGroupDTO policy : policyGroups) {
            List<RiskGroupDTO> groupList = new ArrayList<>();
            for (RiskGroupDTO group : policy.getRiskGroupList()) {
                List<CaseRiskPropertyDTO> caseRiskPropertyList = group.getRiskPropertyInfoList().stream().filter(
                        riskDTO -> riskDTO.isSelected()).collect(Collectors.toList());
                if(ListUtils.isNotEmpty(caseRiskPropertyList)){
                    group.setRiskPropertyInfoList(caseRiskPropertyList);
                    groupList.add(group);
                }
            }
            if(groupList.size()>0){
                policy.setRiskGroupList(groupList);
                policyList.add(policy);
            }
        }
        return policyList;
    }

    @Override
    public void getEstRiskPropertyPlan(List<EstimatePolicyDTO> estimatePolicyList) {
        if(!switchRiskProperty){
            return;
        }
        if(ListUtils.isEmptyList(estimatePolicyList)){
            return;
        }
        String reportNo = estimatePolicyList.get(0).getReportNo();
        if(!displayRiskProperty(reportNo,null)){
            return;
        }

        for (EstimatePolicyDTO policy : estimatePolicyList) {
            convertRiskPropertyPlan(policy,null);
        }
    }

    @Override
    public void getRiskPropertyPlan(List<PolicyPayDTO> policyPayList) {
        if (!isRiskProprertyCase(policyPayList)) {
            return;
        }

        for (PolicyPayDTO policy : policyPayList) {
            convertRiskPropertyPlan(null,policy);
        }
    }

    private void convertRiskPropertyPlan(EstimatePolicyDTO estPolicy,PolicyPayDTO policy){
        List<RiskGroupDTO> groupList;
        List<EstimatePlanDTO> estPlanList = new ArrayList<>();
        List<PlanPayDTO> plyPlanList = new ArrayList<>();
        boolean selectEstPlan = estPolicy != null;
        if(selectEstPlan){
            groupList = estPolicy.getRiskGroupList();
        }else{
            groupList = policy.getRiskGroupList();
        }

        if(ListUtils.isEmptyList(groupList)){
//            throw new GlobalBusinessException("方案不能为空");
            return;
        }

        for (RiskGroupDTO riskGroup : groupList) {
            if (selectEstPlan) {
                List<EstimatePlanDTO> planList = riskGroup.getEstimatePlanList();
                if (ListUtils.isEmptyList(planList)) {
                    throw new GlobalBusinessException("险种不能为空");
                }
                estPlanList.addAll(planList);
            } else {
                List<PlanPayDTO> planList = riskGroup.getPlanPayArr();
                if (ListUtils.isEmptyList(planList)) {
                    throw new GlobalBusinessException("险种不能为空");
                }
                plyPlanList.addAll(planList);
            }
        }
        /*for (RiskGroupDTO riskGroupDTO : groupList) {
            List<CaseRiskPropertyDTO> riskList = riskGroupDTO.getRiskPropertyInfoList();
            if(ListUtils.isEmptyList(riskList)){
                throw new GlobalBusinessException("标的不能为空");
            }
            for (CaseRiskPropertyDTO risk : riskList) {
                if(selectEstPlan){
                    List<EstimatePlanDTO> planList = risk.getEstimatePlanList();
                    if(ListUtils.isEmptyList(planList)){
                        throw new GlobalBusinessException("险种不能为空");
                    }
                    setIdRiskPropertyForPlan(planList,risk.getIdPlyRiskProperty());
                    estPlanList.addAll(planList);
                }else{
                    List<PlanPayDTO> planList = risk.getPlanPayArr();
                    if(ListUtils.isEmptyList(planList)){
                        throw new GlobalBusinessException("险种不能为空");
                    }
                    plyPlanList.addAll(planList);
                }
            }
        }*/

        if(selectEstPlan){
            buildEstDutyLit(estPlanList);
            estPolicy.setEstimatePlanList(estPlanList);
            estPolicy.setRiskGroupList(null);
        }else{
            policy.setPlanPayArr(plyPlanList);
            policy.setRiskGroupList(null);
        }
    }

    private void setIdRiskPropertyForPlan(List<EstimatePlanDTO> planList,String idRiskProperty){
        for (EstimatePlanDTO planDTO : planList) {
            planDTO.setIdPlyRiskProperty(idRiskProperty);
        }
    }


    private Map<String,List<EstimatePlanDTO>> getReportCopyPlan(List<EstimatePolicyDTO> copyEstimatePolicy,String idPlyRiskGroup,String idRiskProperty){

        Map<String,List<EstimatePlanDTO>> resultMap = new HashMap<>();
        if(ListUtils.isEmptyList(copyEstimatePolicy)){
            throw new GlobalBusinessException("保单险种不能为空");
        }

        for (EstimatePolicyDTO policy : copyEstimatePolicy) {
            List<EstimatePlanDTO> planList = new ArrayList<>();
            for (EstimatePlanDTO plan : policy.getEstimatePlanList()) {
                if(idPlyRiskGroup.equals(plan.getIdPlyRiskGroup())){
                    EstimatePlanDTO planDTO = new EstimatePlanDTO();
                    BeanUtils.copyProperties(plan,planDTO);
                    planDTO.setIdPlyRiskProperty(idRiskProperty);
                    planDTO.setPolicyNo(policy.getPolicyNo());
                    planDTO.setCaseNo(policy.getCaseNo());
                    planDTO.setCaseTimes(policy.getCaseTimes());
                    covertEstDuty(planDTO);
                    planList.add(planDTO);
                }
            }
            resultMap.put(idRiskProperty,planList);
        }
        return resultMap;
    }

    private void buildEstDutyLit(List<EstimatePlanDTO> planList){
        String userId = WebServletContext.getUserId();
        for (EstimatePlanDTO plan : planList) {
            for (EstimateDutyRecordDTO duty : plan.getEstimateDutyRecordList()) {
                if(StringUtils.isEmptyStr(duty.getCreatedBy())){
                    duty.setCreatedBy(userId);
                }
                if(StringUtils.isEmptyStr(duty.getUpdatedBy())){
                    duty.setUpdatedBy(userId);
                }
                duty.setIdPlyRiskProperty(plan.getIdPlyRiskProperty());
            }
        }
    }

    private void filterDutys(List<PolicyPayDTO> policyPayList){
        if(ListUtils.isEmptyList(policyPayList)){
            return;
        }
        for (PolicyPayDTO policy:policyPayList){
            List<PlanPayDTO> planDTOList = policy.getPlanPayArr();
            if(ListUtils.isEmptyList(planDTOList)){
                continue;
            }
            for (PlanPayDTO planDTO : planDTOList) {
                List<DutyPayDTO> dutyList = planDTO.getDutyPayArr();
                if(ListUtils.isEmptyList(dutyList)){
                    continue;
                }
                for (DutyPayDTO dutyDTO : dutyList) {
                    String idRiskDuty = dutyDTO.getIdPlyRiskProperty();
                    List<DutyDetailPayDTO> detailList = dutyDTO.getDutyDetailPayArr();
                    if(StringUtils.isEmptyStr(idRiskDuty) || ListUtils.isEmptyList(detailList)){
                        continue;
                    }
                    dutyDTO.setDutyDetailPayArr(detailList.stream().filter(detail -> idRiskDuty.equals(detail.getIdPlyRiskProperty())).collect(Collectors.toList()));
                }
                String idRiskProperty = planDTO.getIdPlyRiskProperty();
                if(StringUtils.isEmptyStr(idRiskProperty)){
                    continue;
                }
                planDTO.setDutyPayArr(dutyList.stream().filter(duty -> idRiskProperty.equals(duty.getIdPlyRiskProperty())).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public boolean isRiskProperty(String targetType, String productClass) {

        return riskPropertyPayTypes.contains(targetType) && Arrays.asList(ProductClassEnum.PRODUCT_CLASS_04.getType()
                                                                        , ProductClassEnum.PRODUCT_CLASS_23.getType()
                                                                        , ProductClassEnum.PRODUCT_CLASS_22.getType()
                                                                        , ProductClassEnum.PRODUCT_CLASS_20.getType()
                                                                        , ProductClassEnum.PRODUCT_CLASS_05.getType()).contains(productClass);
    }

    @Override
    public boolean displayRiskProperty(String reportNo, String policyNo) {
        if (!switchRiskProperty) {
            return false;
        }

        if (StringUtils.isNotEmpty(policyNo)) {
            return displayRiskProperty(policyNo);
        }

        List<String> policyNoList = policyInfoMapper.getPolicyNo(reportNo);
        if (ListUtils.isEmptyList(policyNoList)) {
            return false;
        }

        for (String plyNo : policyNoList) {
            if(displayRiskProperty(plyNo)){
                return true;
            }
        }
        return false;
    }

    private boolean displayRiskProperty(String policyNo) {
        ProductInfoDTO prudocutInfo = new ProductInfoDTO();
        if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
            //global GZ 展示标的，TY 不展示
            //todo tzj 用保单号查询clms_policy_info表 distinct 只返回一条的标的和产品大类编码,但报案跟踪提交之前的流程中不好使，抄单表没有数据
            prudocutInfo = policyInfoMapper.getPrudocutInfo(policyNo);
            if(ObjectUtil.isNotEmpty(prudocutInfo)){
                if(GlobalProductClassEnum.GZ.getClaimProductClass().equals(prudocutInfo.getProductClass())){
                    return true;
                }else{
                    return false;
                }
            }
        }else{
            prudocutInfo = ocasMapper.getPrudocutInfo(policyNo);
        }
        if (ObjectUtil.isEmpty(prudocutInfo)) {
            return false;
        }

        return isRiskProperty(prudocutInfo.getTargetType(), prudocutInfo.getProductClass());
    }

    public boolean isRiskPropertyByPlyNo(String policyNo) {
        ProductInfoDTO productInfoDTO =  getRropertyProduct(policyNo);
        if(productInfoDTO != null && !"".equals(productInfoDTO.getTargetType())){
            return true;
        }
        return false;
    }

    public ProductInfoDTO getRropertyProduct(String policyNo){
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setTargetTypeList(riskPropertyPayTypes);
        productInfoDTO.setClasses(riskPropertyClasses);
        productInfoDTO.setPolicyNo(policyNo);
        productInfoDTO = ocasMapper.getPropertyInfo(productInfoDTO);
        return productInfoDTO;
    }


    public void dealRiskProperty(RiskDomainDTO riskDomainDTO){
        List<CaseRiskPropertyDTO> caseRiskPropertyList = riskDomainDTO.getRiskPropertyList();
        String loginUm = WebServletContext.getUserId();
        String reportNo = riskDomainDTO.getReportNo();
        int caseTimes = riskDomainDTO.getCaseTimes();
        String taskId = riskDomainDTO.getTaskId();
        if(caseRiskPropertyList != null && !caseRiskPropertyList.isEmpty()){
            for(CaseRiskPropertyDTO caseRiskPropertyDTO : caseRiskPropertyList){
                caseRiskPropertyDTO.setReportNo(reportNo);
                caseRiskPropertyDTO.setCaseTimes(caseTimes);
                caseRiskPropertyDTO.setUpdatedBy(loginUm);
                caseRiskPropertyDTO.setTaskId(taskId);
                caseRiskPropertyDTO.setRiskDetail(JsonUtils.toJsonString(caseRiskPropertyDTO.getRiskPropertyMap()));
                caseRiskPropertyDTO.setCreatedBy(loginUm);

                if(caseRiskPropertyDTO.getIdCaseRiskProperty() != null && !"".equals(caseRiskPropertyDTO.getIdCaseRiskProperty())){
                    riskPropertyMapper.deleteCaseRiskPropertyById(caseRiskPropertyDTO.getIdCaseRiskProperty());
                }
                riskPropertyMapper.removeCaseRiskProperty(caseRiskPropertyDTO);
            }
            riskPropertyMapper.saveCaseRiskPropertyList(riskDomainDTO.getRiskPropertyList());
        }


    }

	@Override
	public void saveCaseRiskProperty(CaseRiskPropertyDTO caseRiskProperty) {
        if(!switchRiskProperty){
            return;
        }
        LogUtil.audit("开始保存标的信息{}",JSON.toJSONString(caseRiskProperty));
        LogUtil.audit("开始保存标的信息=List{}",JSON.toJSONString(ListUtil.toList(caseRiskProperty)));

        riskPropertyMapper.saveCaseRiskPropertyList(ListUtil.toList(caseRiskProperty));
	}

    /**
     * 保存案件标的
     * @param caseRiskPropertyList
     */
    @Override
    public void saveCaseRiskPropertyList(List<CaseRiskPropertyDTO> caseRiskPropertyList) {
        if(!switchRiskProperty){
            return;
        }
        riskPropertyMapper.saveCaseRiskPropertyList(caseRiskPropertyList);
    }

    /**
     * 删除选择标的
     * @param caseRiskPropertyDTO
     */
    @Override
    public void removeCaseRiskProperty(CaseRiskPropertyDTO caseRiskPropertyDTO) {
        if(!switchRiskProperty){
            return;
        }
        riskPropertyMapper.removeCaseRiskProperty(caseRiskPropertyDTO);
    }

	@Override
	public void deleteCaseRiskProperty(String reportNo, int caseTimes, String taskId) {
		if (!switchRiskProperty) {
			return;
		}
		riskPropertyMapper.deleteCaseRiskProperty(reportNo, caseTimes, taskId);
	}

    /**
     * 是否雇主责任险
     *
     * @param reportNo
     * @param policyNo
     * @return
     */
    @Override
    public boolean isEmployerInsurance(String reportNo, String policyNo) {
        if (!switchRiskProperty) {
            return false;
        }
        List<String> policyNoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(policyNo)) {
            policyNoList.add(policyNo);
        } else if (StringUtils.isNotEmpty(reportNo)) {
            policyNoList = policyInfoMapper.getPolicyNo(reportNo);
        }

        if (ListUtils.isEmptyList(policyNoList)) {
            return false;
        }

        for (String plyNo : policyNoList) {
            ProductInfoDTO prudocutInfo = new ProductInfoDTO();
            if(globalPolicyService.checkGlobalPolicyNo(plyNo)){
                //用保单号查询clms_policy_info表 distinct 只返回一条的标的和产品大类编码
//                prudocutInfo = policyInfoMapper.getPrudocutInfo(plyNo);
                prudocutInfo.setProductClass("03");
            }else{
                prudocutInfo = ocasMapper.getPrudocutInfo(plyNo);
            }
            if(ObjectUtil.isNotEmpty(prudocutInfo) && BaseConstant.TARGET_TYPE_MEDICAL.equals(prudocutInfo.getTargetType())
                    && ProductClassEnum.PRODUCT_CLASS_04.getType().equals(prudocutInfo.getProductClass())){
                return true;
            }
        }
        return false;
    }

    /**
     * 是否雇主责任险
     *
     * @param reportNo
     * @param policyNo
     * @return
     */
    @Override
    public boolean isEmployerInsuranceNew(String reportNo, String policyNo) {
        List<String> policyNoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(policyNo)) {
            policyNoList.add(policyNo);
        } else if (StringUtils.isNotEmpty(reportNo)) {
            policyNoList = policyInfoMapper.getPolicyNo(reportNo);
        }

        if (ListUtils.isEmptyList(policyNoList)) {
            return false;
        }

        for (String plyNo : policyNoList) {
            ProductInfoDTO prudocutInfo = ocasMapper.getPrudocutInfo(policyNo);
            if(prudocutInfo != null && BaseConstant.TARGET_TYPE_EMPLOYER.equals(prudocutInfo.getTargetType())
                    && ProductClassEnum.PRODUCT_CLASS_04.getType().equals(prudocutInfo.getProductClass())){
                return true;
            }
        }
        return false;
    }

    /**
     * @param caseDTO
     * @return
     */
    @Override
    public List<CaseRiskPropertyDTO> getLastTaskIdCaseRiskPropertyList(CaseRiskPropertyDTO caseDTO) {
        List<CaseRiskPropertyDTO> caseRiskList = riskPropertyMapper.getLastTaskIdCaseRiskPropertyList(caseDTO);
        parseRiskDetail(caseRiskList);
        return caseRiskList;
    }

    @Override
    public void updateReportRiskPropertyTaskId(CaseRiskPropertyDTO caseRiskPropertyDTO) {
        riskPropertyMapper.updateReportRiskPropertyTaskId(caseRiskPropertyDTO);
    }

    @Override
    public String getCaseRiskPropertyLastTaskId(CaseRiskPropertyDTO caseDTO) {
        return riskPropertyMapper.getCaseRiskPropertyLastTaskId(caseDTO);
    }
}
