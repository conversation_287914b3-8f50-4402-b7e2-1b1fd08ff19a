package com.paic.ncbs.claim.dao.mapper.qualitychecke;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityRecord;
import org.apache.ibatis.annotations.Param;
import java.util.List;
public interface ClmsQualityRecordMapper  extends BaseMapper<ClmsQualityRecord> {

    /**
     * 根据报案号和赔付次数查询质检轨迹记录
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 质检轨迹记录列表
     */
    List<ClmsQualityRecord> selectByReportNoAndCaseTimes(@Param("reportNo") String reportNo, @Param("caseTimes") Short caseTimes, @Param("qualityId") String qualityId);

     int insertrecord (ClmsQualityRecord clmsQualityRecord);
}
