package com.paic.ncbs.claim.model.dto.copypolicy;

import com.paic.ncbs.policy.dto.*;
import lombok.Data;

import java.util.List;

@Data
public class ClaimContractDTO {
    private static final long serialVersionUID = 1053677201982677599L;
    private BaseInfoDTO baseInfo;
    private ExtendDTO extendInfo;
    private SaleDTO saleInfo;
    private List<PayInfoDTO> payInfoList;
    private CoinsuranceDTO coinsuranceInfo;
    private List<RiskGroupDTO> riskGroupInfoList;
    private List<RiskGroupDTO> oldRiskGroupInfoList;
    private List<ApplicantDTO> applicantInfoList;
    private List<SpecialPromiseDTO> specialPromiseList;
    private List<SpecialPromiseDTO> contractPolicyEnsureRelList;
    private List<AttachmentGroupDTO> attachmentGroupList;
    private List<ShareDTO> shareInfoList;
    private List<ShareRelationDTO> shareRelationInfoList;
    private SendInfoDTO sendInfo;
    private List<SelfCardInfoDTO> selfCardInfoList;
    private CombineDTO combineInfo;
    private List<RescueServiceDTO> rescueServiceList;
    private ExtendGroupDTO extendGroupInfo;
    private List<RiskAddressDTO> riskAddressInfoList;
    private List<NoclaimInfoDTO> noclaimInfoList;
    private List<CompensationLimitDTO> compensationLimitInfoList;
    private List<InsurantDTO> insurantInfoList;
    private List<BeneficaryDTO> beneficaryInfoList;
    private ReinsuranceInfoDTO reinsuranceInfo;
    private SelfCardDTO selfCard;
    private List<BankCardInfoDTO> bankCardInfoList;
    private List<AccountInfoDTO> accountInfoList;
    private List<SelfCardApplicantDTO> selfCardApplicantInfoList;
    private List<SelfCardActivateDTO> selfCardActivateDTOList;
    private RelationDTO relationInfo;
    private String isUseLastFee;
    private RealNameRegisterDTO realNameRegisterDTO;
    private List<ValidateRepeatInfoDTO> validateRepeatInfoList;
    private List<GuaranteeDTO> guaranteeInfoList;
    private LabelInfoDTO labelInfo;
    private AddWorkCommReqDTO addWorkCommReqDTO;
    private List<PlyApplyFreeze> plyApplyFreezes;
    private List<DeclarationDTO> declarationList;
    private List<ContractDTO> termContractDTOList;
    private List<PolicyUnderwritingHistoryDTO> underwritingHistoryDTOList;
    private String bizType;
    private List<Property> propertyList;

    public ClaimContractDTO() {
    }

    public BaseInfoDTO getBaseInfo() {
        return this.baseInfo;
    }

    public ExtendDTO getExtendInfo() {
        return this.extendInfo;
    }

    public SaleDTO getSaleInfo() {
        return this.saleInfo;
    }

    public List<PayInfoDTO> getPayInfoList() {
        return this.payInfoList;
    }

    public CoinsuranceDTO getCoinsuranceInfo() {
        return this.coinsuranceInfo;
    }

    public List<RiskGroupDTO> getRiskGroupInfoList() {
        return this.riskGroupInfoList;
    }

    public List<RiskGroupDTO> getOldRiskGroupInfoList() {
        return this.oldRiskGroupInfoList;
    }

    public List<ApplicantDTO> getApplicantInfoList() {
        return this.applicantInfoList;
    }

    public List<SpecialPromiseDTO> getSpecialPromiseList() {
        return this.specialPromiseList;
    }

    public List<SpecialPromiseDTO> getContractPolicyEnsureRelList() {
        return this.contractPolicyEnsureRelList;
    }

    public List<AttachmentGroupDTO> getAttachmentGroupList() {
        return this.attachmentGroupList;
    }

    public List<ShareDTO> getShareInfoList() {
        return this.shareInfoList;
    }

    public List<ShareRelationDTO> getShareRelationInfoList() {
        return this.shareRelationInfoList;
    }

    public SendInfoDTO getSendInfo() {
        return this.sendInfo;
    }

    public List<SelfCardInfoDTO> getSelfCardInfoList() {
        return this.selfCardInfoList;
    }

    public CombineDTO getCombineInfo() {
        return this.combineInfo;
    }

    public List<RescueServiceDTO> getRescueServiceList() {
        return this.rescueServiceList;
    }

    public ExtendGroupDTO getExtendGroupInfo() {
        return this.extendGroupInfo;
    }

    public List<RiskAddressDTO> getRiskAddressInfoList() {
        return this.riskAddressInfoList;
    }

    public List<NoclaimInfoDTO> getNoclaimInfoList() {
        return this.noclaimInfoList;
    }

    public List<CompensationLimitDTO> getCompensationLimitInfoList() {
        return this.compensationLimitInfoList;
    }

    public List<InsurantDTO> getInsurantInfoList() {
        return this.insurantInfoList;
    }

    public List<BeneficaryDTO> getBeneficaryInfoList() {
        return this.beneficaryInfoList;
    }

    public ReinsuranceInfoDTO getReinsuranceInfo() {
        return this.reinsuranceInfo;
    }

    public SelfCardDTO getSelfCard() {
        return this.selfCard;
    }

    public List<BankCardInfoDTO> getBankCardInfoList() {
        return this.bankCardInfoList;
    }

    public List<AccountInfoDTO> getAccountInfoList() {
        return this.accountInfoList;
    }

    public List<SelfCardApplicantDTO> getSelfCardApplicantInfoList() {
        return this.selfCardApplicantInfoList;
    }

    public List<SelfCardActivateDTO> getselfCardActivateDTOList() {
        return this.selfCardActivateDTOList;
    }

    public RelationDTO getRelationInfo() {
        return this.relationInfo;
    }

    public String getIsUseLastFee() {
        return this.isUseLastFee;
    }

    public RealNameRegisterDTO getRealNameRegisterDTO() {
        return this.realNameRegisterDTO;
    }

    public List<ValidateRepeatInfoDTO> getValidateRepeatInfoList() {
        return this.validateRepeatInfoList;
    }

    public List<GuaranteeDTO> getGuaranteeInfoList() {
        return this.guaranteeInfoList;
    }

    public LabelInfoDTO getLabelInfo() {
        return this.labelInfo;
    }

    public AddWorkCommReqDTO getAddWorkCommReqDTO() {
        return this.addWorkCommReqDTO;
    }

    public List<PlyApplyFreeze> getPlyApplyFreezes() {
        return this.plyApplyFreezes;
    }

    public List<DeclarationDTO> getDeclarationList() {
        return this.declarationList;
    }

    public List<ContractDTO> getTermContractDTOList() {
        return this.termContractDTOList;
    }

    public List<PolicyUnderwritingHistoryDTO> getUnderwritingHistoryDTOList() {
        return this.underwritingHistoryDTOList;
    }

    public String getBizType() {
        return this.bizType;
    }

    public List<Property> getPropertyList() {
        return this.propertyList;
    }

    public void setBaseInfo(final BaseInfoDTO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public void setExtendInfo(final ExtendDTO extendInfo) {
        this.extendInfo = extendInfo;
    }

    public void setSaleInfo(final SaleDTO saleInfo) {
        this.saleInfo = saleInfo;
    }

    public void setPayInfoList(final List<PayInfoDTO> payInfoList) {
        this.payInfoList = payInfoList;
    }

    public void setCoinsuranceInfo(final CoinsuranceDTO coinsuranceInfo) {
        this.coinsuranceInfo = coinsuranceInfo;
    }

    public void setRiskGroupInfoList(final List<RiskGroupDTO> riskGroupInfoList) {
        this.riskGroupInfoList = riskGroupInfoList;
    }

    public void setOldRiskGroupInfoList(final List<RiskGroupDTO> oldRiskGroupInfoList) {
        this.oldRiskGroupInfoList = oldRiskGroupInfoList;
    }

    public void setApplicantInfoList(final List<ApplicantDTO> applicantInfoList) {
        this.applicantInfoList = applicantInfoList;
    }

    public void setSpecialPromiseList(final List<SpecialPromiseDTO> specialPromiseList) {
        this.specialPromiseList = specialPromiseList;
    }

    public void setContractPolicyEnsureRelList(final List<SpecialPromiseDTO> contractPolicyEnsureRelList) {
        this.contractPolicyEnsureRelList = contractPolicyEnsureRelList;
    }

    public void setAttachmentGroupList(final List<AttachmentGroupDTO> attachmentGroupList) {
        this.attachmentGroupList = attachmentGroupList;
    }

    public void setShareInfoList(final List<ShareDTO> shareInfoList) {
        this.shareInfoList = shareInfoList;
    }

    public void setShareRelationInfoList(final List<ShareRelationDTO> shareRelationInfoList) {
        this.shareRelationInfoList = shareRelationInfoList;
    }

    public void setSendInfo(final SendInfoDTO sendInfo) {
        this.sendInfo = sendInfo;
    }

    public void setSelfCardInfoList(final List<SelfCardInfoDTO> selfCardInfoList) {
        this.selfCardInfoList = selfCardInfoList;
    }

    public void setCombineInfo(final CombineDTO combineInfo) {
        this.combineInfo = combineInfo;
    }

    public void setRescueServiceList(final List<RescueServiceDTO> rescueServiceList) {
        this.rescueServiceList = rescueServiceList;
    }

    public void setExtendGroupInfo(final ExtendGroupDTO extendGroupInfo) {
        this.extendGroupInfo = extendGroupInfo;
    }

    public void setRiskAddressInfoList(final List<RiskAddressDTO> riskAddressInfoList) {
        this.riskAddressInfoList = riskAddressInfoList;
    }

    public void setNoclaimInfoList(final List<NoclaimInfoDTO> noclaimInfoList) {
        this.noclaimInfoList = noclaimInfoList;
    }

    public void setCompensationLimitInfoList(final List<CompensationLimitDTO> compensationLimitInfoList) {
        this.compensationLimitInfoList = compensationLimitInfoList;
    }

    public void setInsurantInfoList(final List<InsurantDTO> insurantInfoList) {
        this.insurantInfoList = insurantInfoList;
    }

    public void setBeneficaryInfoList(final List<BeneficaryDTO> beneficaryInfoList) {
        this.beneficaryInfoList = beneficaryInfoList;
    }

    public void setReinsuranceInfo(final ReinsuranceInfoDTO reinsuranceInfo) {
        this.reinsuranceInfo = reinsuranceInfo;
    }

    public void setSelfCard(final SelfCardDTO selfCard) {
        this.selfCard = selfCard;
    }

    public void setBankCardInfoList(final List<BankCardInfoDTO> bankCardInfoList) {
        this.bankCardInfoList = bankCardInfoList;
    }

    public void setAccountInfoList(final List<AccountInfoDTO> accountInfoList) {
        this.accountInfoList = accountInfoList;
    }

    public void setSelfCardApplicantInfoList(final List<SelfCardApplicantDTO> selfCardApplicantInfoList) {
        this.selfCardApplicantInfoList = selfCardApplicantInfoList;
    }

    public void setselfCardActivateDTOList(final List<SelfCardActivateDTO> selfCardActivateDTOList) {
        this.selfCardActivateDTOList = selfCardActivateDTOList;
    }

    public void setRelationInfo(final RelationDTO relationInfo) {
        this.relationInfo = relationInfo;
    }

    public void setIsUseLastFee(final String isUseLastFee) {
        this.isUseLastFee = isUseLastFee;
    }

    public void setRealNameRegisterDTO(final RealNameRegisterDTO realNameRegisterDTO) {
        this.realNameRegisterDTO = realNameRegisterDTO;
    }

    public void setValidateRepeatInfoList(final List<ValidateRepeatInfoDTO> validateRepeatInfoList) {
        this.validateRepeatInfoList = validateRepeatInfoList;
    }

    public void setGuaranteeInfoList(final List<GuaranteeDTO> guaranteeInfoList) {
        this.guaranteeInfoList = guaranteeInfoList;
    }

    public void setLabelInfo(final LabelInfoDTO labelInfo) {
        this.labelInfo = labelInfo;
    }

    public void setAddWorkCommReqDTO(final AddWorkCommReqDTO addWorkCommReqDTO) {
        this.addWorkCommReqDTO = addWorkCommReqDTO;
    }

    public void setPlyApplyFreezes(final List<PlyApplyFreeze> plyApplyFreezes) {
        this.plyApplyFreezes = plyApplyFreezes;
    }

    public void setDeclarationList(final List<DeclarationDTO> declarationList) {
        this.declarationList = declarationList;
    }

    public void setTermContractDTOList(final List<ContractDTO> termContractDTOList) {
        this.termContractDTOList = termContractDTOList;
    }

    public void setUnderwritingHistoryDTOList(final List<PolicyUnderwritingHistoryDTO> underwritingHistoryDTOList) {
        this.underwritingHistoryDTOList = underwritingHistoryDTOList;
    }

    public void setBizType(final String bizType) {
        this.bizType = bizType;
    }

    public void setPropertyList(final List<Property> propertyList) {
        this.propertyList = propertyList;
    }


    protected boolean canEqual(final Object other) {
        return other instanceof ContractDTO;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $baseInfo = this.getBaseInfo();
        result = result * 59 + ($baseInfo == null ? 43 : $baseInfo.hashCode());
        Object $extendInfo = this.getExtendInfo();
        result = result * 59 + ($extendInfo == null ? 43 : $extendInfo.hashCode());
        Object $saleInfo = this.getSaleInfo();
        result = result * 59 + ($saleInfo == null ? 43 : $saleInfo.hashCode());
        Object $payInfoList = this.getPayInfoList();
        result = result * 59 + ($payInfoList == null ? 43 : $payInfoList.hashCode());
        Object $coinsuranceInfo = this.getCoinsuranceInfo();
        result = result * 59 + ($coinsuranceInfo == null ? 43 : $coinsuranceInfo.hashCode());
        Object $riskGroupInfoList = this.getRiskGroupInfoList();
        result = result * 59 + ($riskGroupInfoList == null ? 43 : $riskGroupInfoList.hashCode());
        Object $oldRiskGroupInfoList = this.getOldRiskGroupInfoList();
        result = result * 59 + ($oldRiskGroupInfoList == null ? 43 : $oldRiskGroupInfoList.hashCode());
        Object $applicantInfoList = this.getApplicantInfoList();
        result = result * 59 + ($applicantInfoList == null ? 43 : $applicantInfoList.hashCode());
        Object $specialPromiseList = this.getSpecialPromiseList();
        result = result * 59 + ($specialPromiseList == null ? 43 : $specialPromiseList.hashCode());
        Object $contractPolicyEnsureRelList = this.getContractPolicyEnsureRelList();
        result = result * 59 + ($contractPolicyEnsureRelList == null ? 43 : $contractPolicyEnsureRelList.hashCode());
        Object $attachmentGroupList = this.getAttachmentGroupList();
        result = result * 59 + ($attachmentGroupList == null ? 43 : $attachmentGroupList.hashCode());
        Object $shareInfoList = this.getShareInfoList();
        result = result * 59 + ($shareInfoList == null ? 43 : $shareInfoList.hashCode());
        Object $shareRelationInfoList = this.getShareRelationInfoList();
        result = result * 59 + ($shareRelationInfoList == null ? 43 : $shareRelationInfoList.hashCode());
        Object $sendInfo = this.getSendInfo();
        result = result * 59 + ($sendInfo == null ? 43 : $sendInfo.hashCode());
        Object $selfCardInfoList = this.getSelfCardInfoList();
        result = result * 59 + ($selfCardInfoList == null ? 43 : $selfCardInfoList.hashCode());
        Object $combineInfo = this.getCombineInfo();
        result = result * 59 + ($combineInfo == null ? 43 : $combineInfo.hashCode());
        Object $rescueServiceList = this.getRescueServiceList();
        result = result * 59 + ($rescueServiceList == null ? 43 : $rescueServiceList.hashCode());
        Object $extendGroupInfo = this.getExtendGroupInfo();
        result = result * 59 + ($extendGroupInfo == null ? 43 : $extendGroupInfo.hashCode());
        Object $riskAddressInfoList = this.getRiskAddressInfoList();
        result = result * 59 + ($riskAddressInfoList == null ? 43 : $riskAddressInfoList.hashCode());
        Object $noclaimInfoList = this.getNoclaimInfoList();
        result = result * 59 + ($noclaimInfoList == null ? 43 : $noclaimInfoList.hashCode());
        Object $compensationLimitInfoList = this.getCompensationLimitInfoList();
        result = result * 59 + ($compensationLimitInfoList == null ? 43 : $compensationLimitInfoList.hashCode());
        Object $insurantInfoList = this.getInsurantInfoList();
        result = result * 59 + ($insurantInfoList == null ? 43 : $insurantInfoList.hashCode());
        Object $beneficaryInfoList = this.getBeneficaryInfoList();
        result = result * 59 + ($beneficaryInfoList == null ? 43 : $beneficaryInfoList.hashCode());
        Object $reinsuranceInfo = this.getReinsuranceInfo();
        result = result * 59 + ($reinsuranceInfo == null ? 43 : $reinsuranceInfo.hashCode());
        Object $selfCard = this.getSelfCard();
        result = result * 59 + ($selfCard == null ? 43 : $selfCard.hashCode());
        Object $bankCardInfoList = this.getBankCardInfoList();
        result = result * 59 + ($bankCardInfoList == null ? 43 : $bankCardInfoList.hashCode());
        Object $accountInfoList = this.getAccountInfoList();
        result = result * 59 + ($accountInfoList == null ? 43 : $accountInfoList.hashCode());
        Object $selfCardApplicantInfoList = this.getSelfCardApplicantInfoList();
        result = result * 59 + ($selfCardApplicantInfoList == null ? 43 : $selfCardApplicantInfoList.hashCode());
        Object $selfCardActivateDTOList = this.getselfCardActivateDTOList();
        result = result * 59 + ($selfCardActivateDTOList == null ? 43 : $selfCardActivateDTOList.hashCode());
        Object $relationInfo = this.getRelationInfo();
        result = result * 59 + ($relationInfo == null ? 43 : $relationInfo.hashCode());
        Object $isUseLastFee = this.getIsUseLastFee();
        result = result * 59 + ($isUseLastFee == null ? 43 : $isUseLastFee.hashCode());
        Object $realNameRegisterDTO = this.getRealNameRegisterDTO();
        result = result * 59 + ($realNameRegisterDTO == null ? 43 : $realNameRegisterDTO.hashCode());
        Object $validateRepeatInfoList = this.getValidateRepeatInfoList();
        result = result * 59 + ($validateRepeatInfoList == null ? 43 : $validateRepeatInfoList.hashCode());
        Object $guaranteeInfoList = this.getGuaranteeInfoList();
        result = result * 59 + ($guaranteeInfoList == null ? 43 : $guaranteeInfoList.hashCode());
        Object $labelInfo = this.getLabelInfo();
        result = result * 59 + ($labelInfo == null ? 43 : $labelInfo.hashCode());
        Object $addWorkCommReqDTO = this.getAddWorkCommReqDTO();
        result = result * 59 + ($addWorkCommReqDTO == null ? 43 : $addWorkCommReqDTO.hashCode());
        Object $plyApplyFreezes = this.getPlyApplyFreezes();
        result = result * 59 + ($plyApplyFreezes == null ? 43 : $plyApplyFreezes.hashCode());
        Object $declarationList = this.getDeclarationList();
        result = result * 59 + ($declarationList == null ? 43 : $declarationList.hashCode());
        Object $termContractDTOList = this.getTermContractDTOList();
        result = result * 59 + ($termContractDTOList == null ? 43 : $termContractDTOList.hashCode());
        Object $underwritingHistoryDTOList = this.getUnderwritingHistoryDTOList();
        result = result * 59 + ($underwritingHistoryDTOList == null ? 43 : $underwritingHistoryDTOList.hashCode());
        Object $bizType = this.getBizType();
        result = result * 59 + ($bizType == null ? 43 : $bizType.hashCode());
        Object $propertyList = this.getPropertyList();
        result = result * 59 + ($propertyList == null ? 43 : $propertyList.hashCode());
        return result;
    }

    public String toString() {
        return "ContractDTO(baseInfo=" + this.getBaseInfo() + ", extendInfo=" + this.getExtendInfo() + ", saleInfo=" + this.getSaleInfo() + ", payInfoList=" + this.getPayInfoList() + ", coinsuranceInfo=" + this.getCoinsuranceInfo() + ", riskGroupInfoList=" + this.getRiskGroupInfoList() + ", oldRiskGroupInfoList=" + this.getOldRiskGroupInfoList() + ", applicantInfoList=" + this.getApplicantInfoList() + ", specialPromiseList=" + this.getSpecialPromiseList() + ", contractPolicyEnsureRelList=" + this.getContractPolicyEnsureRelList() + ", attachmentGroupList=" + this.getAttachmentGroupList() + ", shareInfoList=" + this.getShareInfoList() + ", shareRelationInfoList=" + this.getShareRelationInfoList() + ", sendInfo=" + this.getSendInfo() + ", selfCardInfoList=" + this.getSelfCardInfoList() + ", combineInfo=" + this.getCombineInfo() + ", rescueServiceList=" + this.getRescueServiceList() + ", extendGroupInfo=" + this.getExtendGroupInfo() + ", riskAddressInfoList=" + this.getRiskAddressInfoList() + ", noclaimInfoList=" + this.getNoclaimInfoList() + ", compensationLimitInfoList=" + this.getCompensationLimitInfoList() + ", insurantInfoList=" + this.getInsurantInfoList() + ", beneficaryInfoList=" + this.getBeneficaryInfoList() + ", reinsuranceInfo=" + this.getReinsuranceInfo() + ", selfCard=" + this.getSelfCard() + ", bankCardInfoList=" + this.getBankCardInfoList() + ", accountInfoList=" + this.getAccountInfoList() + ", selfCardApplicantInfoList=" + this.getSelfCardApplicantInfoList() + ", selfCardActivateDTOList=" + this.getselfCardActivateDTOList() + ", relationInfo=" + this.getRelationInfo() + ", isUseLastFee=" + this.getIsUseLastFee() + ", realNameRegisterDTO=" + this.getRealNameRegisterDTO() + ", validateRepeatInfoList=" + this.getValidateRepeatInfoList() + ", guaranteeInfoList=" + this.getGuaranteeInfoList() + ", labelInfo=" + this.getLabelInfo() + ", addWorkCommReqDTO=" + this.getAddWorkCommReqDTO() + ", plyApplyFreezes=" + this.getPlyApplyFreezes() + ", declarationList=" + this.getDeclarationList() + ", termContractDTOList=" + this.getTermContractDTOList() + ", underwritingHistoryDTOList=" + this.getUnderwritingHistoryDTOList() + ", bizType=" + this.getBizType() + ", propertyList=" + this.getPropertyList() + ")";
    }
}
