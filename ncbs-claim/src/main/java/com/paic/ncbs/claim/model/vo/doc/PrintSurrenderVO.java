package com.paic.ncbs.claim.model.vo.doc;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 解约通知书VO
 * @Author: zhangjun
 * @Date: 2020/5/27
 */
@Data
public class PrintSurrenderVO {
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 投保日期
     */
    private String applyDate;
    //险种名称
    private String productName;
    /**
     * 投保人
     */
    private String applicationName;
    /**
     * 退费金额
     */
    private BigDecimal refundAmount;
    /**
     * 退费金额中文
     */
    private String refundAmountChinese;
    /**
     * 解约方式0 -不退费，1-退费
     */
    private String surrenderType;
    //退费方式 全额退费 协议退费
    private String refundType;
    /**
     * 解约批文
     */
    private String endorseComment;
    //打印日期
    private String printDate;
}
