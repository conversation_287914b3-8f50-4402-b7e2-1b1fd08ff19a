package com.paic.ncbs.claim.service.waitingcenter.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.WaitingCenterChoiceEnum;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.mapper.estimate.ClmsEstimateRecordMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.waitingcenter.*;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeApplyVO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimESPolicyInfoVO;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.waitingcenter.AbstractWaitingCenterService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 未决办事中心审批
 * @author: justinwu
 * @create 2025/5/21 17:54
 */
@Slf4j
@Service
@RefreshScope
public class UnSettleApproveWaitingCenterServiceImpl extends AbstractWaitingCenterService {

    @Value("${unsettleapprove.actionurl:http://ncbs-claim.lb.ssdev.com:48915/claim/public/unsettleapprove/callback}")
    private String actionUrl;

    @Value("${unsettleapprove.switch:N}")
    private String switchUnsettleApprove;
    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private EstimateChangeService estimateChangeService;
    @Autowired
    private RegisterCaseService registerCaseService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private ClmsEstimateRecordMapper clmsEstimateRecordMapper;
    @Autowired
    private EstimateService estimateService;



    @Override
    protected WaitingCenterReqDto buildWaitingCenterReq(String bussinessId) {
        TaskInfoDTO taskInfoDTO = taskInfoService.getTaskDtoByTaskId(bussinessId);
        if(taskInfoDTO == null) {
            throw new GlobalBusinessException("任务信息不存在");
        }
        // 获取最初审批人
        Map applyMap = this.queryApplyUm(taskInfoDTO);
        WaitingCenterReqDto waitingCenterReqDto = new WaitingCenterReqDto();
        // 当任务分类=未决修正审批时，任务主题=未决修正审批。
        // 当任务分类=立案审批时，任务主题=立案审批。
        String bussinessTitle = "立案审批";
        String bussinessAbbs ="ORR";
        if(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())) {
            bussinessTitle = "未决修正审批";
            bussinessAbbs = "OECR";
        }
        waitingCenterReqDto.setBussinessTitle(bussinessTitle);
        //必填 审批回调地址 (下文第2个接口的http地址) 支持服务发现的方式
        waitingCenterReqDto.setActionUrl(actionUrl);
        //状态更新地址 (下文第3个接口的http地址)，建议填上，可以保障数据一致性的问题 支持服务发现的方式
        waitingCenterReqDto.setUpdateUrl(null);
        //必填 原系统审批详情页面，外部系统不用填
        waitingCenterReqDto.setSystemUrl(null);
        waitingCenterReqDto.setCreateBy((String) applyMap.get("apply"));
        waitingCenterReqDto.setIsOuter(true);
        waitingCenterReqDto.setIsChange(false);
        //领域，必填
        waitingCenterReqDto.setDomain("技术运营-运维管理");
        //子领域，必填
        waitingCenterReqDto.setProcdefKey("理赔审批/未决审批");
        waitingCenterReqDto.setInstanceSource(4);
        // 审批内容
        Map retMap = new HashMap<String,String>();
        waitingCenterReqDto.setApproveContent(this.buildUnSettleApproveContent(taskInfoDTO,retMap,(String) applyMap.get("applyName")));
        if(BpmConstants.TASK_STATUS_PARALLEL_PENDING.equals(taskInfoDTO.getStatus())||BpmConstants.TASK_STATUS_PENDING.equals(taskInfoDTO.getStatus())) {
            waitingCenterReqDto.setBusinessStatus("等待审批");
            waitingCenterReqDto.setInstanceStatus(1);
            waitingCenterReqDto.setColorType(null);
        }else {
            String retChoice = (String) retMap.get("choice");
            waitingCenterReqDto.setBusinessStatus(WaitingCenterChoiceEnum.getName(retChoice));
            if(WaitingCenterChoiceEnum.CHOICE_AGREE.getCode().equals(retChoice)) {
                // 1 绿色通过 2 红色驳回 3 灰色撤销
                waitingCenterReqDto.setColorType(1);
            }else {
                // 1 绿色通过 2 红色驳回 3 灰色撤销
                waitingCenterReqDto.setColorType(2);
            }
            waitingCenterReqDto.setInstanceStatus(2);

        }
        // 审批流业务主键
        String businessNo=taskInfoDTO.getReportNo()+"_"+taskInfoDTO.getCaseTimes()+"_"+bussinessAbbs+"_"+retMap.get("applyTimes");
        waitingCenterReqDto.setBussinessId(businessNo);

        ApproveHistoryDto approveHistory = new ApproveHistoryDto();
        List<TodoTaskDto> todoTaskList = new ArrayList<>();
        if(BpmConstants.TASK_STATUS_PARALLEL_PENDING.equals(taskInfoDTO.getStatus())||BpmConstants.TASK_STATUS_PENDING.equals(taskInfoDTO.getStatus())) {
            // 待办
            TodoTaskDto todoTask = new TodoTaskDto();
            todoTask.setCandidates(taskInfoDTO.getAssigner());
            todoTask.setTaskDefName(bussinessTitle);
            todoTask.setTaskId(taskInfoDTO.getTaskId());
            List<ChoiceDto> choiceList = new ArrayList<>();
            todoTaskList.add(todoTask);
            choiceList.add(new ChoiceDto(WaitingCenterChoiceEnum.CHOICE_AGREE.getName(), WaitingCenterChoiceEnum.CHOICE_AGREE.getCode()));
            choiceList.add(new ChoiceDto(WaitingCenterChoiceEnum.CHOICE_REJECT.getName(), WaitingCenterChoiceEnum.CHOICE_REJECT.getCode()));
            todoTask.setChoiceList(choiceList);
        }
        // 审批历史
        int applyTimes = (int) retMap.get("applyTimes");
        List<TaskRecordDto> taskRecordList = this.buildTaskRecordDtoLists(taskInfoDTO,bussinessTitle,applyTimes);
        approveHistory.setTaskRecordList(taskRecordList);
        approveHistory.setTodoTaskList(todoTaskList);
        waitingCenterReqDto.setApproveHistory(approveHistory);
        return waitingCenterReqDto;
    }

    @Override
    public WaitingCenterResDto createInstance(String bussinessId) {
        log.info("调用立案未决审批开始:{}",bussinessId);
        WaitingCenterResDto waitingCenterResDto = new WaitingCenterResDto();
        if(Constants.FLAG_Y.equals(switchUnsettleApprove)) {
            waitingCenterResDto = this.createBussinessInstance(bussinessId);
        }
        log.info("调用立案未决审批结束:{}",bussinessId);
        return waitingCenterResDto;
    }

    @Override
    public ApproveCallbackResDto actionCallback(ApproveCallbackReqDto reqDto) {
        ApproveCallbackResDto approveCallbackResDto = new ApproveCallbackResDto();
        String taskIdStr = reqDto.getTaskIdStr();
        if(StringUtils.isEmptyStr(taskIdStr)) {
            approveCallbackResDto = ApproveCallbackResDto.fail("请求参数taskIdStr不能为空！");
            return approveCallbackResDto;
        }
        String choice = reqDto.getChoice();
        if(StringUtils.isEmptyStr(choice)) {
            approveCallbackResDto = ApproveCallbackResDto.fail("请求参数choice不能为空！");
            return approveCallbackResDto;
        }
        // 审批内容
        String comment = reqDto.getComment();
        TaskInfoDTO taskInfoDTO = taskInfoService.getTaskDtoByTaskId(taskIdStr);
        if(taskInfoDTO == null) {
            approveCallbackResDto = ApproveCallbackResDto.fail("任务信息不存在");
            return approveCallbackResDto;
        }
        // 放入当前处理用户
        UserInfoDTO sysTemUserInfoDTO = new UserInfoDTO();
        sysTemUserInfoDTO.setUserCode(reqDto.getApproveAccount());
        sysTemUserInfoDTO.setUserName(reqDto.getApproveAccount());
        sysTemUserInfoDTO.setComCode(ConfigConstValues.HQ_DEPARTMENT);
        Objects.requireNonNull(WebServletContext.getRequest()).getSession().setAttribute(Constants.CURR_USER,
                sysTemUserInfoDTO);
        List<String> msgList = new ArrayList<>();
        if(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())) {
            // 未决修正审批
            EstimateChangeApplyVO estimateChangeApplyVO = new EstimateChangeApplyVO();
            estimateChangeApplyVO.setReportNo(taskInfoDTO.getReportNo());
            estimateChangeApplyVO.setCaseTimes(taskInfoDTO.getCaseTimes());
            estimateChangeApplyVO.setTaskId(taskInfoDTO.getTaskId());
            estimateChangeApplyVO.setAuditRemark(comment);
            if(WaitingCenterChoiceEnum.CHOICE_AGREE.getCode().equals(choice)) {
                estimateChangeApplyVO.setAuditOpinion(ConstValues.AUDIT_AGREE_CODE);
            }else {
                estimateChangeApplyVO.setAuditOpinion(ConstValues.AUDIT_DISAGREE_CODE);
            }
            try {
                msgList = estimateChangeService.sendAuditEstimateChangeApply(estimateChangeApplyVO);
            } catch (Exception e) {
                log.error("{},未决审批异常,{}",taskInfoDTO.getReportNo(),e.getMessage());
                approveCallbackResDto = ApproveCallbackResDto.fail(taskInfoDTO.getReportNo()+"未决审批异常"+e.getMessage());
                return approveCallbackResDto;
            }
        }else {
            // 立案审批
            CaseRegisterApplyVO registerApplyVO = new CaseRegisterApplyVO();
            registerApplyVO.setReportNo(taskInfoDTO.getReportNo());
            registerApplyVO.setCaseTimes(taskInfoDTO.getCaseTimes());
            registerApplyVO.setTaskId(taskInfoDTO.getTaskId());
            registerApplyVO.setAuditRemark(comment);
            if(WaitingCenterChoiceEnum.CHOICE_AGREE.getCode().equals(choice)) {
                registerApplyVO.setAuditOpinion(ConstValues.AUDIT_AGREE_CODE);
            }else {
                registerApplyVO.setAuditOpinion(ConstValues.AUDIT_DISAGREE_CODE);
            }
            CaseRegisterApplyVO caseRegisterApplyVO = registerCaseService.getLastCaseRegisterApplyVO(taskInfoDTO.getReportNo(), taskInfoDTO.getCaseTimes());
            if(caseRegisterApplyVO != null) {
                registerApplyVO.setIdAhcsCaseRegisterApply(caseRegisterApplyVO.getIdAhcsCaseRegisterApply());
            }
            estimateService.sendRegisterAudit(registerApplyVO,msgList, false);
        }
        approveCallbackResDto = ApproveCallbackResDto.success(String.join(",",msgList));
        return approveCallbackResDto;
    }

    /**
     * 构建审批内容
     * @param taskInfoDTO
     * @param retMap
     * @param applyName
     * @return
     */
    private ApproveContentDto buildUnSettleApproveContent(TaskInfoDTO taskInfoDTO,Map retMap,String applyName) {
        //审批内容
        ApproveContentDto approveContent = new ApproveContentDto();
        List<SummaryDto> summaryList = new ArrayList<>();
        addSummary(summaryList, "案件号", taskInfoDTO.getReportNo(), null);
        Map schemeMap = this.querySchemeInfo(taskInfoDTO.getReportNo());
        addSummary(summaryList, "产品名称", (String) schemeMap.get("productName"), null);
        addSummary(summaryList, "方案名称", (String) schemeMap.get("schemeName"), null);
        if(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())) {
            // 未决修正审批
            BigDecimal estimateRecordAmount = clmsEstimateRecordMapper.getLastEstimateRecordAmount(taskInfoDTO.getReportNo(), taskInfoDTO.getCaseTimes());
            estimateRecordAmount = Optional.ofNullable(estimateRecordAmount).orElse(new BigDecimal(0));
            addSummary(summaryList, "未决金额", estimateRecordAmount.toString(), null);
            EstimateChangeApplyVO estimateChangeApplyVO = null;
            if(BpmConstants.TASK_STATUS_PARALLEL_PENDING.equals(taskInfoDTO.getStatus())||BpmConstants.TASK_STATUS_PENDING.equals(taskInfoDTO.getStatus())) {
                try {
                    estimateChangeApplyVO = estimateChangeService.getAuditEstimateChangeApplyVO(taskInfoDTO.getReportNo(), taskInfoDTO.getCaseTimes());
                } catch (Exception e) {
                    log.error("{},未决修正进行中记录查询为空",taskInfoDTO.getReportNo());
                }
            }else {
                try {
                    List<EstimateChangeApplyVO> estimateChangeApplyVOList = estimateChangeService.getEstimateChangeApplyVOList(taskInfoDTO.getReportNo(), taskInfoDTO.getCaseTimes());
                    if(ListUtils.isNotEmpty(estimateChangeApplyVOList)) {
                        estimateChangeApplyVOList = estimateChangeApplyVOList.stream().sorted(Comparator.comparing(EstimateChangeApplyVO::getAuditDate).reversed())
                                .collect(Collectors.toList());
                        estimateChangeApplyVO = estimateChangeApplyVOList.get(0);
                    }
                } catch (Exception e) {
                    log.error("{},未决修正已完成记录查询为空",taskInfoDTO.getReportNo());
                }
            }

            if(estimateChangeApplyVO==null) {
                addSummary(summaryList, "未决修正金额合计", null, null);
                addSummary(summaryList, "未决修正差额", null, null);
                addSummary(summaryList, "申请说明", null, null);
                addSummary(summaryList, "申请人", applyName, null);
                addSummary(summaryList, "申请时间", null, null);
            }else {
                BigDecimal applyAmount = Optional.ofNullable(estimateChangeApplyVO.getApplyAmount()).orElse(new BigDecimal(0));
                addSummary(summaryList, "未决修正金额合计", applyAmount.toString(), null);
                addSummary(summaryList, "未决修正差额", applyAmount.subtract(estimateRecordAmount).toString(), null);
                addSummary(summaryList, "申请说明", estimateChangeApplyVO.getApplyRemark(), null);
                addSummary(summaryList, "申请人", applyName, null);
                try {
                    addSummary(summaryList, "申请时间", DateUtils.parseToFormatString(estimateChangeApplyVO.getApplyDate(), DateUtils.FULL_DATE_STR), null);
                } catch (ParseException e) {
                    log.error("{},申请时间转化异常",taskInfoDTO.getReportNo()+e.getMessage());
                    addSummary(summaryList, "申请时间", null, null);
                }
                if(ConstValues.AUDIT_AGREE_CODE.equals(estimateChangeApplyVO.getAuditOpinion())) {
                    retMap.put("choice",WaitingCenterChoiceEnum.CHOICE_AGREE.getCode());
                }else {
                    retMap.put("choice",WaitingCenterChoiceEnum.CHOICE_REJECT.getCode());
                }
                retMap.put("comment",estimateChangeApplyVO.getAuditRemark());
                retMap.put("applyTimes",estimateChangeApplyVO.getApplyTimes());
            }
        }else {
            // 立案审批
            CaseRegisterApplyDTO caseRegisterApplyDTO = registerCaseService.getLastCaseRegisterApplyDTO(taskInfoDTO.getReportNo(), taskInfoDTO.getCaseTimes());
            if(caseRegisterApplyDTO==null) {
                addSummary(summaryList, "未决金额", null, null);
                addSummary(summaryList, "申请人", applyName, null);
                addSummary(summaryList, "申请时间", null, null);
            }else {
                addSummary(summaryList, "未决金额", caseRegisterApplyDTO.getRegisterAmount().toString(), null);
                addSummary(summaryList, "申请人", applyName, null);
                try {
                    addSummary(summaryList, "申请时间", DateUtils.parseToFormatString(caseRegisterApplyDTO.getApplyDate(), DateUtils.FULL_DATE_STR), null);
                } catch (ParseException e) {
                    log.error("{},申请时间转化异常",taskInfoDTO.getReportNo()+e.getMessage());
                    addSummary(summaryList, "申请时间", null, null);
                }
                if(ConstValues.AUDIT_AGREE_CODE.equals(caseRegisterApplyDTO.getAuditOpinion())) {
                    retMap.put("choice",WaitingCenterChoiceEnum.CHOICE_AGREE.getCode());
                }else {
                    retMap.put("choice",WaitingCenterChoiceEnum.CHOICE_REJECT.getCode());
                }
                retMap.put("comment",caseRegisterApplyDTO.getAuditRemark());
                retMap.put("applyTimes",caseRegisterApplyDTO.getApplyTimes());
            }
        }
        approveContent.setSummary(summaryList);
        return approveContent;
    }


    /**
     * 查询报案对应的产品方案信息
     * @param reportNo
     * @return
     */
    private Map querySchemeInfo(String reportNo) {
        Map retMap =  new HashMap();
        List<ClaimESPolicyInfoVO> claimESPolicyInfoVOList = taskInfoMapper.getClaimESPolicyInfoVOList(reportNo);
        String productName = null;
        String schemeName = null;
        if(claimESPolicyInfoVOList!=null) {
            ClaimESPolicyInfoVO claimESPolicyInfoVO = claimESPolicyInfoVOList.get(0);
            if(claimESPolicyInfoVO !=null) {
                productName = claimESPolicyInfoVO.getProductName();
                schemeName = claimESPolicyInfoVO.getSchemeName();
            }
        }
        retMap.put("productName",productName);
        retMap.put("schemeName",schemeName);
        return retMap;
    }


    /**
     * 构建审批历史
     * @param taskInfoDTO
     * @param bussinessTitle
     * @param applyTimes
     * @return
     */
    private List<TaskRecordDto>  buildTaskRecordDtoLists(TaskInfoDTO taskInfoDTO,String bussinessTitle,int applyTimes) {
        List<TaskRecordDto> taskRecordList = new ArrayList<>();
        if(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())) {
            // 未决修正审批
            try {
                List<EstimateChangeApplyVO> estimateChangeApplyVOList = estimateChangeService.getEstimateChangeApplyVOList(taskInfoDTO.getReportNo(), taskInfoDTO.getCaseTimes());
                if(ListUtils.isNotEmpty(estimateChangeApplyVOList)) {
                    estimateChangeApplyVOList = estimateChangeApplyVOList.stream().sorted(Comparator.comparing(EstimateChangeApplyVO::getAuditDate).reversed())
                            .collect(Collectors.toList());
                    for (EstimateChangeApplyVO estimateChangeApplyVO:estimateChangeApplyVOList) {
                        // 同一审批批次对应的审批历史
                        if(applyTimes==estimateChangeApplyVO.getApplyTimes()) {
                            TaskRecordDto taskRecord = new TaskRecordDto();
                            taskRecord.setCandidates(null);
                            taskRecord.setAssignee(estimateChangeApplyVO.getAuditUm());
                            taskRecord.setTaskDefName(bussinessTitle);
                            taskRecord.setTaskId(estimateChangeApplyVO.getId());
                            String auditOpinion = estimateChangeApplyVO.getAuditOpinion();
                            if(ConstValues.AUDIT_AGREE_CODE.equals(auditOpinion)) {
                                taskRecord.setChoice(WaitingCenterChoiceEnum.CHOICE_AGREE.getName());
                            }else {
                                taskRecord.setChoice(WaitingCenterChoiceEnum.CHOICE_REJECT.getName());
                            }
                            taskRecord.setComment(estimateChangeApplyVO.getAuditRemark());
                            Date auditDate = estimateChangeApplyVO.getAuditDate();
                            if(auditDate!=null) {
                                taskRecord.setSysCtime(auditDate.getTime());
                                taskRecord.setSysUtime(auditDate.getTime());
                            }else {
                                taskRecord.setSysCtime(new Date().getTime());
                                taskRecord.setSysUtime(new Date().getTime());
                            }
                            taskRecordList.add(taskRecord);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("{},未决修正已完成记录查询为空",taskInfoDTO.getReportNo());
            }
        }else {
            // 立案审批
            List<CaseRegisterApplyVO>  caseRegisterApplyVOList = registerCaseService.getLastCaseRegisterApplyVOList(taskInfoDTO.getReportNo(), taskInfoDTO.getCaseTimes());
            if(ListUtils.isNotEmpty(caseRegisterApplyVOList)) {
                caseRegisterApplyVOList = caseRegisterApplyVOList.stream().sorted(Comparator.comparing(CaseRegisterApplyVO::getApplyDate).reversed())
                        .collect(Collectors.toList());
                for(CaseRegisterApplyVO caseRegisterApplyVO:caseRegisterApplyVOList) {
                    if(StringUtils.isEmptyStr(caseRegisterApplyVO.getStatus()) || "2".equals(caseRegisterApplyVO.getStatus())) {
                        TaskRecordDto taskRecord = new TaskRecordDto();
                        taskRecord.setCandidates(null);
                        taskRecord.setAssignee(caseRegisterApplyVO.getAuditUm());
                        taskRecord.setTaskDefName(bussinessTitle);
                        taskRecord.setTaskId(caseRegisterApplyVO.getIdAhcsCaseRegisterApply());
                        String auditOpinion = caseRegisterApplyVO.getAuditOpinion();
                        taskRecord.setChoice(auditOpinion);
                        taskRecord.setComment(caseRegisterApplyVO.getAuditRemark());
                        Date auditDate = caseRegisterApplyVO.getAuditDate();
                        if(auditDate!=null) {
                            taskRecord.setSysCtime(auditDate.getTime());
                            taskRecord.setSysUtime(auditDate.getTime());
                        }else {
                            taskRecord.setSysCtime(new Date().getTime());
                            taskRecord.setSysUtime(new Date().getTime());
                        }
                        taskRecordList.add(taskRecord);
                    }
                }
            }
        }
        return taskRecordList;
    }

    /**
     * 获取任务对应的最初申请人
     * @param taskInfoDTO
     * @return
     */
    private Map<String,String>  queryApplyUm(TaskInfoDTO taskInfoDTO) {
        Map<String,String> retMap = new HashMap<String,String>();
        String apply = null;
        String applyName = null;
        TaskInfoDTO tmpTaskInfoDTO = taskInfoDTO;
        while(true) {
            if(StringUtils.isEmptyStr(tmpTaskInfoDTO.getPreTaskId())) {
                apply = tmpTaskInfoDTO.getApplyer();
                applyName = tmpTaskInfoDTO.getApplyerName();
                break;
            }else  {
                tmpTaskInfoDTO = taskInfoService.getTaskDtoByTaskId(tmpTaskInfoDTO.getPreTaskId());
            }
        }
        retMap.put("apply",apply);
        retMap.put("applyName",applyName);
        return retMap;
    }
}
