package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustCoinsDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CoinsureInfoMapper extends BaseDao<CoinsureInfoDTO> {

    int getCoinsureCountByReportNo(@Param("reportNo") String reportNo);

    List<CoinsureDTO> getCoinsureDescByReportNo(@Param("reportNo") String reportNo);

    List<CoinsureDTO> getCoinsureByPolicyNo(@Param("policyNo") String policyNo);

    List<CoinsureDTO> getCoinsureListByReportNo(@Param("reportNo") String reportNo);
    //查询是否存在我方主方的共保信息，确认该报案为主共保案件
    int getMainCoinsureCountByReportNo(@Param("reportNo") String reportNo);

    List<CoinsureDTO> getCoinsList(@Param("reportNo") String reportNo);

    List<PrintEntrustCoinsDTO> selectPrintEntrustList(@Param("reportNo") String reportNo
            , @Param("caseTimes") Integer caseTimes
            , @Param("claimType") String claimType
            , @Param("subTimes") Integer subTimes);
}
