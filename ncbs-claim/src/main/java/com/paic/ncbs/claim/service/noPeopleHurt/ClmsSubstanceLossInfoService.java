package com.paic.ncbs.claim.service.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsSubstanceLossInfo;

import java.util.List;

/**
 * 物质损失信息表(ClmsSubstanceLossInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:50
 */
public interface ClmsSubstanceLossInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSubstanceLossInfo queryById(String id);

    /**
     * 通过报案号查询数据
     *
     * @return 实例对象
     */
    List<ClmsSubstanceLossInfo> queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsSubstanceLossInfo 实例对象
     * @return 实例对象
     */
    ClmsSubstanceLossInfo insert(ClmsSubstanceLossInfo clmsSubstanceLossInfo);

    /**
     * 新增多条数据
     * @param substanceLossInfoList
     */
    void insertBatch(List<ClmsSubstanceLossInfo> substanceLossInfoList);

    /**
     * 修改数据
     *
     * @param clmsSubstanceLossInfo 实例对象
     * @return 实例对象
     */
    ClmsSubstanceLossInfo update(ClmsSubstanceLossInfo clmsSubstanceLossInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    void deleteByCondition(String reportNo, int caseTimes);
}
