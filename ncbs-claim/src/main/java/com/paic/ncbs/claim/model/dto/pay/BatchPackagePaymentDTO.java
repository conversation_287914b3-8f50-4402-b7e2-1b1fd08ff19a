package com.paic.ncbs.claim.model.dto.pay;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 批量打包dto
 */
public class BatchPackagePaymentDTO {

    /**
     * 支付流水号集合
     */
    @ApiModelProperty("支付流水号集合")
    private List<String> paySerialNoList;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal sumPayAmount;

    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    private String batchNo;

    public List<String> getPaySerialNoList() {
        return paySerialNoList;
    }

    public void setPaySerialNoList(List<String> paySerialNoList) {
        this.paySerialNoList = paySerialNoList;
    }

    public BigDecimal getSumPayAmount() {
        return sumPayAmount;
    }

    public void setSumPayAmount(BigDecimal sumPayAmount) {
        this.sumPayAmount = sumPayAmount;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

}
