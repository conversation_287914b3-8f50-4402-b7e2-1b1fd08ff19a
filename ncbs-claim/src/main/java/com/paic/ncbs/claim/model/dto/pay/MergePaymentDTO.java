package com.paic.ncbs.claim.model.dto.pay;

import java.math.BigDecimal;
import java.util.Date;

public class MergePaymentDTO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 批量打包批次号
     */
    private String batchNo;

    /**
     * 合作方代码
     */
    private String parterCode;

    /**
     * 合作方名称
     */
    private String parterName;

    /**
     * 收款户名
     */
    private String payeeName;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 机构代码
     */
    private String departmentCode;

    /**
     * 机构名称
     */
    private String departmentName;

    /**
     * 付用途：P1-赔款，P2-费用
     */
    private String paymentUsage;

    /**
     * 帐号类型:个人帐号=1,公司帐号=0
     */
    private String bankAccountAttribute;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 证件类型
     */
    private String clientCertificateType;

    /**
     * 客户证件号码
     */
    private String clientCertificateNo;

    /**
     * 企业/公司证件类型
     */
    private String companyCardType;

    /**
     * 领款人账号类型为公司时有值，组织机构代码：
     * 机构类型为一般机构时存社会统一信用代码，机构类型为其他时存经营证件号码
     */
    private String organizeCode;

    /**
     * 支付方式1-微信零钱，2-银行转账, 3-美团点评
     */
    private String payType;

    /**
     * 结算方式
     */
    private String collectPayApproach;

    /**
     * 开户行账号
     */
    private String clientBankAccount;

    /**
     * 客户开户银行码
     */
    private String clientBankCode;

    /**
     * 客户开户银行
     */
    private String clientBankName;

    /**
     * 开户行明细码
     */
    private String bankDetailCode;

    /**
     * 开户行明细
     */
    private String bankDetail;

    /**
     * 批次号下总结算金额
     */
    private BigDecimal sumAmount;

    /**
     * 批次号下数据条数
     */
    private Integer sumCount;

    /**
     * 支付状态
     */
    private String mergePaymentStatus;

    /**
     * 结算状态：01-数据校验失败；02-实收实付确认复核通过；11-支付成功；12-支付失败，退票
     */
    private String settlementStatus;

    /**
     * 错误原因
     */
    private String errorMsg;

    /**
     * 是否有效
     */
    private String isEffective;

    /**
     * 创建人员
     */
    private String createdBy;

    /**
     * 修改人员
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 修改时间
     */
    private Date sysUtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getParterCode() {
        return parterCode;
    }

    public void setParterCode(String parterCode) {
        this.parterCode = parterCode;
    }

    public String getParterName() {
        return parterName;
    }

    public void setParterName(String parterName) {
        this.parterName = parterName;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getPaymentUsage() {
        return paymentUsage;
    }

    public void setPaymentUsage(String paymentUsage) {
        this.paymentUsage = paymentUsage;
    }

    public String getBankAccountAttribute() {
        return bankAccountAttribute;
    }

    public void setBankAccountAttribute(String bankAccountAttribute) {
        this.bankAccountAttribute = bankAccountAttribute;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientCertificateType() {
        return clientCertificateType;
    }

    public void setClientCertificateType(String clientCertificateType) {
        this.clientCertificateType = clientCertificateType;
    }

    public String getClientCertificateNo() {
        return clientCertificateNo;
    }

    public void setClientCertificateNo(String clientCertificateNo) {
        this.clientCertificateNo = clientCertificateNo;
    }

    public String getCompanyCardType() {
        return companyCardType;
    }

    public void setCompanyCardType(String companyCardType) {
        this.companyCardType = companyCardType;
    }

    public String getOrganizeCode() {
        return organizeCode;
    }

    public void setOrganizeCode(String organizeCode) {
        this.organizeCode = organizeCode;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getCollectPayApproach() {
        return collectPayApproach;
    }

    public void setCollectPayApproach(String collectPayApproach) {
        this.collectPayApproach = collectPayApproach;
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount;
    }

    public String getClientBankCode() {
        return clientBankCode;
    }

    public void setClientBankCode(String clientBankCode) {
        this.clientBankCode = clientBankCode;
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName;
    }

    public String getBankDetailCode() {
        return bankDetailCode;
    }

    public void setBankDetailCode(String bankDetailCode) {
        this.bankDetailCode = bankDetailCode;
    }

    public String getBankDetail() {
        return bankDetail;
    }

    public void setBankDetail(String bankDetail) {
        this.bankDetail = bankDetail;
    }

    public BigDecimal getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(BigDecimal sumAmount) {
        this.sumAmount = sumAmount;
    }

    public Integer getSumCount() {
        return sumCount;
    }

    public void setSumCount(Integer sumCount) {
        this.sumCount = sumCount;
    }

    public String getMergePaymentStatus() {
        return mergePaymentStatus;
    }

    public void setMergePaymentStatus(String mergePaymentStatus) {
        this.mergePaymentStatus = mergePaymentStatus;
    }

    public String getSettlementStatus() {
        return settlementStatus;
    }

    public void setSettlementStatus(String settlementStatus) {
        this.settlementStatus = settlementStatus;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getIsEffective() {
        return isEffective;
    }

    public void setIsEffective(String isEffective) {
        this.isEffective = isEffective;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}
