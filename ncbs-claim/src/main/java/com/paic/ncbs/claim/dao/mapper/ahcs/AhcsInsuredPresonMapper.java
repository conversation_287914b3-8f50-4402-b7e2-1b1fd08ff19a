package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity;
import com.paic.ncbs.claim.model.dto.copypolicy.ClaimDetailPersonInjuryInfo;
import com.paic.ncbs.claim.model.vo.ahcs.AhcsInsuredPresonVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsInsuredPresonMapper extends BaseDao<AhcsInsuredPresonEntity> {

    List<AhcsInsuredPresonEntity> getAhcsInsuredPersionByPolicyId(String idAhcsInsuredPerson);

    List<AhcsInsuredPresonEntity> getAhcsInsuredPersionByClientNo(String clientNo);

    List<String> getAhcsInsuredPersionByClientNoAndReportNo(String clientNo, String reportNo);

    List<AhcsInsuredPresonEntity> getAhcsInsuredPersionById(String idAhcsPolicyInfo);

    int deleteByIdAhcsPolicyInfo(String idAhcsPolicyInfo);

    void insertList(@Param("list") List<AhcsInsuredPresonEntity> list);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    List<Integer> getAhcsInsuredPersionNatureByClientNoAndReportNo(String clientNo, String reportNo);

    String getRelToInsuredByReportNo(String reportNo);

    /**
     * 查询被保人信息
     */
    AhcsInsuredPresonVO getInsuredPersionByClientNoAndReportNo(String reportNo);

    ClaimDetailPersonInjuryInfo getInsuredPersionByReportNo(@Param("reportNo") String reportNo);
}