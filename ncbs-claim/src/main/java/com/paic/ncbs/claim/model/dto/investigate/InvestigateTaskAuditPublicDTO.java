package com.paic.ncbs.claim.model.dto.investigate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "外部系统调查任务审核信息")
public class InvestigateTaskAuditPublicDTO extends InvestigateTaskAuditDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "外部审核人")
    private String externalAuditor;

}