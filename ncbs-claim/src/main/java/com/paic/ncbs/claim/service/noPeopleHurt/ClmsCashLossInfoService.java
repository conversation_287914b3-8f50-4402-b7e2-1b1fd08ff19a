package com.paic.ncbs.claim.service.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsCashLossInfo;

import java.util.List;

/**
 * 现金损失信息表(ClmsCashLossInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:46
 */
public interface ClmsCashLossInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsCashLossInfo queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    List<ClmsCashLossInfo> queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsCashLossInfo 实例对象
     * @return 实例对象
     */
    ClmsCashLossInfo insert(ClmsCashLossInfo clmsCashLossInfo);

    /**
     * 修改数据
     *
     * @param clmsCashLossInfo 实例对象
     * @return 实例对象
     */
    ClmsCashLossInfo update(ClmsCashLossInfo clmsCashLossInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    void deleteByCondition(String reportNo, int caseTimes);
}
