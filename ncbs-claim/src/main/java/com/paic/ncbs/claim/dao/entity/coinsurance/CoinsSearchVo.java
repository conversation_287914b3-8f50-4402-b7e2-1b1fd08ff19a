package com.paic.ncbs.claim.dao.entity.coinsurance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CoinsSearchVo {
    private String reportNo;
    private String coinsCompanyName;
    private String policyNo;
    private String payItem;
    private String amortizationFlag;
    private String departmentCode;
    @ApiModelProperty(value = "页码")
    private int currentPage = 1;

    @ApiModelProperty(value = "每页条数")
    private int pageSize = 1;
    List<String> departmentCodes;
}
