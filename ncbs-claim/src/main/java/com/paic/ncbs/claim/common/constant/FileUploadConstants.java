package com.paic.ncbs.claim.common.constant;


import java.util.HashMap;
import java.util.Map;



public class FileUploadConstants {

    public static final String LOCAL_HOST = "http://*************:8070/claim/imgs/";

    public static final String LOCAL_PATH = "/home/<USER>/imgs/";

    private FileUploadConstants() {

    }

    public static final Map<String, String> flowType = new HashMap<String, String>();

    static {
        flowType.put("01", "首案单证");
        flowType.put("02", "二次赔付单证");
        flowType.put("03", "三次赔付单证");
        flowType.put("04", "四次赔付单证");
        flowType.put("05", "五次赔付单证");
        flowType.put("06", "六次赔付单证");
        flowType.put("07", "七次赔付单证");
        flowType.put("08", "八次赔付单证");
        flowType.put("09", "九次赔付单证");
        flowType.put("10", "十次赔付单证");
        flowType.put("11", "十一次赔付单证");
        flowType.put("12", "十二次赔付单证");
        flowType.put("13", "十三次赔付单证");
        flowType.put("14", "十四次赔付单证");
        flowType.put("15", "十五次赔付单证");
        flowType.put("16", "十六次赔付单证");
        flowType.put("17", "十七次赔付单证");
        flowType.put("18", "十八次赔付单证");
        flowType.put("19", "十九次赔付单证");
        flowType.put("20", "二十次赔付单证");
        flowType.put("51", "申诉1单证");
        flowType.put("52", "申诉2单证");
        flowType.put("53", "申诉3单证");

    }

    
    public static final String FILE_TYPE_RECORD = "02";

    //单证
    public static final String FILE_TYPE_DOCUMENT = "01";

    public static final String RECORD_TYPE_ZERO_APPLY = "RT_01";

    
    public static final String RECORD_TYPE_CONFIRM_CLAIM = "RT_06";

    public static final String DOCUMENT_SMALLTYPE_IS_NULL = "单证细类信息为空";

    public static final String DATA_TWO = "2";

    public static final String DOCUMENT_GROUP_TYPE = "1";

    public static final String IMAGE_DMZ_SERVERID = "dmz";

    public static final String INVALIDATION_FLAG = "Y";

    public static final String ALL_FLAG = "A";

    public static final String DOCUMENT_PROPERTY_TASKCODE = "taskCode";

    
    public static final String DOCUMENT_PROPERTY_APPLYTIMES = "applyTimes";

    
    public static final String DOCUMENT_PROPERTY_SUPPLEMENT = "supplement";

    
    public static final String DOCUMENT_PROPERTY_SHARE = "share";

    
    public static final String DOCUMENT_PROPERTY_CLICKORD = "clickOrder";

    
    public static final String DOCUMENT_PROPERTY_BATCHNO = "batchNo";

    
    public static final String DOCUMENT_PROPERTY_SEPARATOR = "|";

    
    public static final String DOCUMENT_UPLOAD_FLAG_Y = "Y";

    
    public static final String DOCUMENT_UPLOAD_FLAG_N = "N";

    
    public static final String CASE_STATUS_END = "0";

    public static final String DOCUMENT_TYPE_REMOVED_NAME = "已删除";

    
    public static final String DOCUMENT_GROUP_ID_REMOVED = "00";

    
    public static final Integer DOCUMENT_COUNT_ZERO = 0;

    public static final String DOCUMENT_UPLOADPERSONNEL_SEPARATOR = "-";

    public static final String DOCUMENT_UPLOADPERSONNEL_LEFT = "(";
    
    public static final String DOCUMENT_UPLOADPERSONNEL_RIGHT = ")";

    public static final String FORMAL_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String EMPTY_STRING = "";

    public static final String REMOVE_POLICYS="被剔除的保单";

    public static final String SMALL_CODE_OTHER = "014001";

    public static final String DOCUMENT_SCENE_ONLINE = "0";

    public static final String DOC_TYPE_CODE = "docTypeCode";

    public static final String DOC_TYPE_NAME = "docTypeName";

    public static final String USER_ID = "userId";

    public static final String FAIL_CODE = "901000";

    public static final long DOCUMENT_FILESIZE_LIMIT = 40 * 1024 * 1024L;

    public static final long DOC_VIDEO_FILESIZE_LIMIT = 5 * 1024 * 1024L;

    public static final String CONFIRM_CLAIM = "confirmClaim";

    public static final String CERTIFICATE_IDCARD_NAME = "身份证";

    //送收付核销坏账文件类型
    public static final String PAYMENT_FILE_TYPE = "S0002";
    //送收付核销坏账文件类型名称
    public static final String PAYMENT_FILE_TYPE_NAME = "签报";


}