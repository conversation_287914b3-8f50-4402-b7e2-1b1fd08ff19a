package com.paic.ncbs.claim.service.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsTravelDelayInfo;

/**
 * 延误信息表(ClmsTravelDelayInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:51
 */
public interface ClmsTravelDelayInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsTravelDelayInfo queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    ClmsTravelDelayInfo queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsTravelDelayInfo 实例对象
     * @return 实例对象
     */
    ClmsTravelDelayInfo insert(ClmsTravelDelayInfo clmsTravelDelayInfo);

    /**
     * 修改数据
     *
     * @param clmsTravelDelayInfo 实例对象
     * @return 实例对象
     */
    ClmsTravelDelayInfo update(ClmsTravelDelayInfo clmsTravelDelayInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    void deleteByCondition(String reportNo, int caseTimes);
}
