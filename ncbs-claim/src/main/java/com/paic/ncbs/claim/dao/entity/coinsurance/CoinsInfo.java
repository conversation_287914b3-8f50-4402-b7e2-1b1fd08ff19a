package com.paic.ncbs.claim.dao.entity.coinsurance;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * 共保摊回记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CoinsInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String idAhcsCoinsInfo;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 共保公司名称
     */
    private String reinsureCompanyName;

    /**
     * 是否我司
     */
    private String companyFlag;

    /**
     * 是否主席
     */
    private String acceptInsuranceFlag;

    /**
     * 共保比例
     */
    private BigDecimal reinsureScale;

    /**
     * 币别
     */
    private String currency;

    /**
     * 支付项目
     */
    private String payItem;

    /**
     * 摊回金额
     */
    private BigDecimal coinsAmount;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private Date sysUtime;

    private String policyNo;
    private String amortizationFlag;
    private BigDecimal reCoinsAmount;
    private Date retrunDate;
}
