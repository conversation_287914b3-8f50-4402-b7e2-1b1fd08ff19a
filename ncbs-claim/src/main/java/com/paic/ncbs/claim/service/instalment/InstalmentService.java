package com.paic.ncbs.claim.service.instalment;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.instalment.PaymentItemDownFinanceVO;
import com.paic.ncbs.claim.model.vo.instalment.PaymentItemEditableVO;
import com.paic.ncbs.claim.model.vo.instalment.PaymentInfoEditableVO;

import java.util.List;


public interface InstalmentService {

    boolean checkIfCaseContainsInstalment(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    List<PaymentItemEditableVO> isAllowChangePaymentItem(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    List<PaymentInfoEditableVO> isAllowChangePaymentInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    List<PaymentItemDownFinanceVO> findDownFinancedPaymentItem(String reportNo, Integer caseTimes) throws GlobalBusinessException;
}
