package com.paic.ncbs.claim.dao.entity.coinsurance;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CoinsAmortizationVo {
    private String reportNo;//报案号
    private Integer caseTimes;//赔付次数
    private String coinsCompanyName;//共保公司名称
    private String coinsCompanyCode;//共保公司名称
    private String companyFlag;//是否我司
    private String acceptInsuranceFlag;//是否主席
    private BigDecimal reinsureScale;//摊回比例
    private String currency;//币别
    private String payItem;//支付项目
    private BigDecimal coinsAmount;//应摊回金额
    private String policyNo;//保单号
    private String amortizationFlag;//摊回状态
    private BigDecimal reCoinsAmount;//已摊回金额
    private String retrunDate;//摊回时间
    private String departmentCode;//机构代码
    private BigDecimal coinsTax;//应摊回税额
    private String idCoinsInfo;
    private String idRecoveryRecord;
    private Integer subTimes;
}
