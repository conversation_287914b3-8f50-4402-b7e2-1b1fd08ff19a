package com.paic.ncbs.claim.service.waitingcenter;

import com.paic.ncbs.claim.model.dto.waitingcenter.ApproveCallbackReqDto;
import com.paic.ncbs.claim.model.dto.waitingcenter.ApproveCallbackResDto;
import com.paic.ncbs.claim.model.dto.waitingcenter.WaitingCenterResDto;

/**
 * 办事中心服务接口
 * @author: justinwu
 * @create 2025/5/21 17:17
 */
public interface WaitingCenterService {

    /**
     * 创建办事中心审批实例
     * @param bussinessId
     * @return  WaitingCenterResDto
     */
    public WaitingCenterResDto createInstance(String bussinessId);

    /**
     * 办事中心回调
     * @param reqDto
     * @return
     */
    public ApproveCallbackResDto actionCallback(ApproveCallbackReqDto reqDto);
}
