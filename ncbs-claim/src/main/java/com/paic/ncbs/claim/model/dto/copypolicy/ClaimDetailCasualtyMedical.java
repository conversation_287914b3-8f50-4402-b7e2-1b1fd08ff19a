package com.paic.ncbs.claim.model.dto.copypolicy;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ClaimDetailCasualtyMedical {
    private Long seqNum;//序号
    private String inOutCls;//就诊类型（1:住院2:门诊）
    private String inOutStartDate;//就诊开始日期
    private String inOutEndDate;//就诊结束日期
    private String hospital;//就诊医院名称
    private String newHospital;//我司医院代码（详见我司医院代码说明）
    private String diagnosis;//
    private String newDiagnosis;//中保协疾病代码[2066]（详见疾病代码说明）
    private String diagnosis_shpt;//平台疾病代码[2065]（上海平台必填）（详见平台疾病代码说明）
    private String visitType;//首诊/复诊（1:首诊 2:复诊）
    private BigDecimal strMedicalBillAmt;//发票总金额
    private BigDecimal strDeductionAmt;//免赔额
    private BigDecimal strFinalPayAmount;//实际支付金额

}
