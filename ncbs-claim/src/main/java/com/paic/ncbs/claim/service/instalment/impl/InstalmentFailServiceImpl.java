package com.paic.ncbs.claim.service.instalment.impl;


import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.dao.mapper.instalment.InstalmentFailMapper;
import com.paic.ncbs.claim.model.dto.instalment.InstalmentFailDTO;
import com.paic.ncbs.claim.service.instalment.InstalmentFailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("instalmentFailService")
public class InstalmentFailServiceImpl implements InstalmentFailService {

    @Autowired
    InstalmentFailMapper instalmentFailDao;



    @Override
    public List<InstalmentFailDTO> findByReportNoAndCaseTimes(String reportNo, Integer caseTimes) {
        return instalmentFailDao.findByReportNoAndCaseTimes(reportNo, caseTimes);
    }

    @Override
    public List<PaymentItemComData> findAllPaymentItem(String reportNo, Integer caseTimes) {
        return instalmentFailDao.findAllPaymentItem(reportNo, caseTimes);
    }



}
