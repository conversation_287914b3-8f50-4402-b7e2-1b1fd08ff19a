package com.paic.ncbs.claim.model.vo.taskdeal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("理赔ES信息")
public class ClaimInfoToESVO {
    @ApiModelProperty("案件主键")
    private String idCase;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("报案批次号")
    private Integer caseTimes;
    @ApiModelProperty("保单号")
    private String policyNo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("保险起期")
    private Date insuranceBeginDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("保险止期")
    private Date insuranceEndDate;
    @ApiModelProperty("上一年保单号")
    private String lastPolicyNo;

    @ApiModelProperty("产品代码")
    private String productCode;
    @ApiModelProperty("产品名称")
    private String productName;
    @ApiModelProperty("方案代码")
    private String packageCode;
    @ApiModelProperty("方案名称")
    private String packageName;

    @ApiModelProperty("被保险人代码")
    private String insuredClientCode;
    @ApiModelProperty("被保险人姓名")
    private String insuredName;
    @ApiModelProperty("标的人客户号")
    private String objectClientCode;
    @ApiModelProperty("标的名称")
    private String objectName;
    @ApiModelProperty("标的证件号")
    private String objectCertNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("报案时间")
    private Date reportDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty("事故时间")
    private Date accidentDate;

    @ApiModelProperty("就诊医院")
    private String hospitalName;
    @ApiModelProperty("就诊疾病")
    private String diagnoseCode;
    @ApiModelProperty("案件状态")
    private String caseStatus;
    @ApiModelProperty("案件性质")
    private String caseNature;
    @ApiModelProperty("赔付金额")
    private String paidAmount;
    @ApiModelProperty("数据来源")
    private String dataSource;
    @ApiModelProperty("未决金额")
    private String estimateAmount;
    @ApiModelProperty("事故经过")
    private String accidentDetail;
    @ApiModelProperty("产品大类")
    private String productType;

    @ApiModelProperty("备注")
    private String remark;
}
