package com.paic.ncbs.claim.dao.entity.qualitychecke;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class QualityInfoWithDetailVO extends ClmsQualityInfo{

    private static final long serialVersionUID = 1L;
    /**
     * 质检环节
     */
    private String inspnStage;

    /**
     * 质检规范
     */
    private String inspnStandard;

    /**
     * 差错等级(低、中、高)
     */
    private String errorLevel;

    /**
     * 偏差金额
     */
    private BigDecimal diffAmount;

    /**
     * 减损金额
     */
    private BigDecimal lossAmount;

    /**
     * 处理机构代码
     */
    private String companycode;

    /**
     * 质检发起人代码
     */
    private String qualitycode;
}
