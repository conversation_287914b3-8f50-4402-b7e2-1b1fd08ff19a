package com.paic.ncbs.claim.service.secondunderwriting;


import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPolicyConclusionEntity;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsSeconduwPolicyConclusionVO;

import java.util.List;

/**
 * 理赔二核保单层核保结论表(ClmsSeconduwPolicyConclusionEntity)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-14 14:07:13
 */
public interface ClmsSeconduwPolicyConclusionService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSeconduwPolicyConclusionEntity queryById(String id);
    
     /**
     * 通过报案号查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSeconduwPolicyConclusionEntity queryByReportNo(String reportNo, int caseTimes);


    /**
     * 新增数据
     *
     * @param clmsSeconduwPolicyConclusionEntity 实例对象
     * @return 实例对象
     */
    ClmsSeconduwPolicyConclusionEntity insert(ClmsSeconduwPolicyConclusionEntity clmsSeconduwPolicyConclusionEntity);

    /**
     * 修改数据
     *
     * @param clmsSeconduwPolicyConclusionEntity 实例对象
     * @return 实例对象
     */
    ClmsSeconduwPolicyConclusionEntity update(ClmsSeconduwPolicyConclusionEntity clmsSeconduwPolicyConclusionEntity);
    
    /**
     * 根据报案号修改数据
     *
     * @param  reportNo 报案号
     * @return 影响行数
     */
    int updateByReportNo( String reportNo, int caseTimes);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    /**
     * 批量保存
     */
    void saveBatch(List<ClmsSeconduwPolicyConclusionEntity> entityList);

    /**
     * 根据理赔二核申请表主键查询
     * @param idClmsSecondUnderwriting
     * @return
     */
    List<ClmsSeconduwPolicyConclusionVO> getPolicyConclusionVOList(String idClmsSecondUnderwriting);

}
