package com.paic.ncbs.claim.service.entrustment;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.um.model.dto.UserInfoDTO;

import java.util.List;

public interface EntrustmentService {

    /**
     * 初始化委托
     * @param entrustmentApiVo 委托信息
     * @throws GlobalBusinessException 业务异常
     */
    ResponseResult<Object> saveEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException;

    /**
     * 查询委托数据
     * @param entrustmentApiVo 委托查询参数
     * @return 委托数据
     */
    ResponseResult<EntrustmentApiVo> getEntrustData(EntrustmentApiVo entrustmentApiVo);

    /**
     * 检查是否可以发送委托
     * @throws GlobalBusinessException 业务异常
     */
    void checkIsCanSendEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException;

    /**
     * 获取事故场景数据
     * @param collectionCode 集合代码
     * @return 事故场景DTO列表
     */
    List<AccidentSceneDto> getAccidentSceneData(String collectionCode);



    /**
     * 获取用于打印的委托信息
     * @return 委托DTO列表
     */
    ResponseResult<Object> getEntrustmentForPrint(String reportNo);

    /**
     * 提交委托审批
     * @param entrustAuditDTO 委托审批信息
     * @throws GlobalBusinessException 业务异常
     */
    void submitentrustAudit(EntrustAuditDTO entrustAuditDTO) throws GlobalBusinessException;

    /**
     * 查询案件机构本级及上级有提调审批权限的所有人
     * @return 审批人信息列表
     * @throws GlobalBusinessException 业务异常
     */
    List<UserInfoDTO> getApprovalUsers(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    void convertThirdPartyTypeName(Object object);

    /**
     * 查询委托次数
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 委托次数
     */
    Integer getEntrustmentCount(String reportNo, Integer caseTimes);
}