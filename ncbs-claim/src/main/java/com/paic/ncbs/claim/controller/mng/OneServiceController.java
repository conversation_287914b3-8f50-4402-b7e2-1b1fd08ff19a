package com.paic.ncbs.claim.controller.mng;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(tags = "一服务？")
@RestController
@RequestMapping(value = "/mng/app/oneServiceAction")
public class OneServiceController extends BaseController {


    @ApiOperation("查询模型列表")
    @RequestMapping(value = "/findListModels", method = RequestMethod.POST)
    public ResponseResult<String> findListModels(@RequestBody Map<String, String> reqMap) throws GlobalBusinessException {
        return ResponseResult.success("");
    }

}
