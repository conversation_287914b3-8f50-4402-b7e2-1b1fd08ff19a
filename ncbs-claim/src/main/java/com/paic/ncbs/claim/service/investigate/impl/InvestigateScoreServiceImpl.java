package com.paic.ncbs.claim.service.investigate.impl;

import com.google.common.collect.Lists;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskAuditVO;
import com.paic.ncbs.claim.common.constant.investigate.InvestigateConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateScoreMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateScoreDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.investigate.InvestigateScoreService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


@Service("investigateScoreServiceImpl")
public class InvestigateScoreServiceImpl implements InvestigateScoreService {

    @Autowired
    private InvestigateScoreMapper investigateScoreDao;

    @Autowired
    private InvestigateTaskService investigateTaskService;

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private InvestigateTaskAuditMapper investigateTaskAuditDao;

    @Autowired
    private InvestigateTaskMapper investigateTaskDao;

    @Autowired
    private UserInfoService userInfoService;

    @Override
    public List<InvestigateScoreDTO> listInvestigateScore(String idAhcsInvestigate, String queryType) throws GlobalBusinessException {

        List<InvestigateScoreDTO> investigateScoreDTOList =  investigateScoreDao.listInvestigateScore(idAhcsInvestigate);


        if (InvestigateConstants.VALIDATE_FLAG_YES.equals(queryType)){
            return investigateScoreDTOList;
        }


        for (InvestigateScoreDTO investigateScoreDTO : investigateScoreDTOList){
            InvestigateTaskDTO investigateTaskDTO = investigateTaskService.getInvestigateTaskById(investigateScoreDTO.getIdAhcsInvestigateTask());

            if (InvestigateConstants.VALIDATE_FLAG_YES.equals(investigateTaskDTO.getIsPrimaryTask())
                    && InvestigateConstants.VALIDATE_FLAG_NO.equals(investigateTaskDTO.getIsOffsiteTask())
                    && investigateScoreDTO.getScoreUm().equals(investigateTaskDTO.getDispatchUm())){
                return Lists.newArrayList(investigateScoreDTO);
            }
        }

        return null;
    }

    @Override
    public InvestigateScoreDTO listScore(String idAhcsInvestigate) {
        return investigateScoreDao.listScore(idAhcsInvestigate);
    }

    @Override
    public void saveInvestigateScore(InvestigateScoreDTO investigateScoreDTO) throws GlobalBusinessException {
        investigateScoreDTO.setReportNo(investigateTaskService.getInvestigateTaskById(investigateScoreDTO.getIdAhcsInvestigateTask()).getReportNo());
//        DepartmentDTO dto = userInfoService.getLevel2DeptByUser(investigateScoreDTO.getScoreUm());
//        if (dto != null) {
//        }
        investigateScoreDTO.setScoreDepartmentCode(WebServletContext.getDepartmentCode());
        investigateScoreDTO.setIdAhcsInvestigateScore(UuidUtil.getUUID());
        investigateScoreDao.saveInvestigateScore(investigateScoreDTO);
    }


    @Override
    public String getInvestigateScore(InvestigateScoreDTO score) {
        BigDecimal evaluateScore = new BigDecimal(10);

        InvestigateTaskDTO investigateTaskDTO = investigateTaskService.getInvestigateTaskById(score.getIdAhcsInvestigateTask());



        long end = investigateTaskDTO.getFinishDate().getTime();
        long start = investigateTaskDTO.getCreatedDate().getTime();

        if (InvestigateConstants.VALIDATE_FLAG_YES.equals(investigateTaskDTO.getIsPrimaryTask())
                && InvestigateConstants.VALIDATE_FLAG_NO.equals(investigateTaskDTO.getIsOffsiteTask())){
            end = DateUtils.getCurrentTime().getTime();
        }

        BigDecimal day = new BigDecimal((end - start) / 86400000.0).setScale(2, BigDecimal.ROUND_HALF_UP);

        if (day.floatValue() <= InvestigateConstants.INVESTIGATE_AGING_SCORE) {

        } else if (InvestigateConstants.INVESTIGATE_AGING_SCORE < day.floatValue() && day.floatValue() <= 5 + InvestigateConstants.INVESTIGATE_AGING_SCORE) {
            evaluateScore = evaluateScore.subtract(new BigDecimal(3));
        } else if (5 + InvestigateConstants.INVESTIGATE_AGING_SCORE < day.floatValue() && day.floatValue() <= 20 + InvestigateConstants.INVESTIGATE_AGING_SCORE) {
            evaluateScore = evaluateScore.subtract(new BigDecimal(6));
        } else if (20 + InvestigateConstants.INVESTIGATE_AGING_SCORE < day.floatValue()) {
            evaluateScore = evaluateScore.subtract(new BigDecimal(10));
        } else {}


        if (InvestigateConstants.VALIDATE_FLAG_NO.equals(score.getIsAllEvidence())) {
            evaluateScore = evaluateScore.subtract(new BigDecimal(3));
        }


        if (InvestigateConstants.VALIDATE_FLAG_NO.equals(score.getIsQualified())) {
            evaluateScore = evaluateScore.subtract(new BigDecimal(5));
        }


        if (InvestigateConstants.VALIDATE_FLAG_YES.equals(score.getIsJudgeError())) {
            evaluateScore = evaluateScore.subtract(new BigDecimal(3));
        } else {


            if (InvestigateConstants.INVESTIGATE_QUALITATIVE_REFERENCE.equals(investigateTaskDTO.getInvestigateQualitative())) {
                evaluateScore = evaluateScore.add(new BigDecimal(2));
            } else if (InvestigateConstants.INVESTIGATE_QUALITATIVE_ABNORMAL.equals(investigateTaskDTO.getInvestigateQualitative())) {
                evaluateScore = evaluateScore.add(new BigDecimal(3));
            } else {}
        }


        List<InvestigateTaskAuditVO> investigateTaskAuditVOList = investigateTaskAuditDao.listInvestigateTaskAuditsByTaskId(investigateTaskDTO.getIdAhcsInvestigateTask());
        if (ListUtils.isNotEmpty(investigateTaskAuditVOList)
                && InvestigateConstants.VALIDATE_FLAG_YES.equals(investigateTaskDTO.getIsPrimaryTask())){
            evaluateScore = evaluateScore.subtract(new BigDecimal(2 * (investigateTaskAuditVOList.size()-1)));
        }

        return evaluateScore.toString();
    }



}