package com.paic.ncbs.claim.controller.mng;

import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.model.dto.other.AccidentReasonDTO;
import com.paic.ncbs.claim.model.dto.other.CityDefineDTO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.other.CountryVO;
import com.paic.ncbs.claim.service.other.CityDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping("/mng/app/cityDefineAction")
@Api(tags = {"城市信息"})
public class CityDefineController extends BaseController {

    @Resource(name = "cityDefineService")
    private CityDefineService cityDefineService;

    @ApiOperation(value = "获取市区县列表信息")
    @ResponseBody
    @RequestMapping(value = "/getCityDefineList/{cityCode}/{cityType}", method = RequestMethod.GET)
    public ResponseResult<List<CityDefineDTO>> getCityDefineList(@ApiParam("城市编码") @PathVariable("cityCode") String cityCode,
                                                                 @ApiParam("城市类型") @PathVariable("cityType") String cityType) throws GlobalBusinessException {
        LogUtil.info("获取市区县列表信息。cityCode=" + cityCode + ",cityType=" + cityType);
        List<CityDefineDTO> list = cityDefineService.getCityDefineDTOList(cityCode, cityType);
        return ResponseResult.success(list);
    }

    @ApiOperation(value = "获取国家列表信息")
    @ResponseBody
    @GetMapping(value = "/getCountryByContinent/{continentCode}")
    public ResponseResult<List<CountryVO>> getCountryByContinent(@ApiParam("大洲编码") @PathVariable("continentCode") String continentCode ) throws GlobalBusinessException {
        LogUtil.info("获取国家列表信息。continentCode=" + continentCode );
        List<CountryVO> countryByContinent = cityDefineService.getCountryByContinent(continentCode);
        return ResponseResult.success(countryByContinent);
    }

    @ApiOperation(value = "获取国家列表信息")
    @ResponseBody
    @GetMapping(value = "/getCountryByContinent4AntiMoney/{continentCode}")
    public ResponseResult<List<CountryVO>> getCountryByContinent4AntiMoney(@ApiParam("大洲编码") @PathVariable("continentCode") String continentCode ) throws GlobalBusinessException {
        LogUtil.info("获取国家列表信息。continentCode=" + continentCode );
        List<CountryVO> countryByContinent = cityDefineService.getCountryByContinent(continentCode);
        if(CommonConstant.COUNTRY_CONTINENT_CODE_ASIA.equals(continentCode)){
            //如果是亚洲，追加中国
            CountryVO countryVO = new CountryVO();
            countryVO.setCountryCode(CommonConstant.CHINA_COUNTRY_CODE);
            countryVO.setCountryName(CommonConstant.CHINA_COUNTRY_NAME);
            countryByContinent.add(0,countryVO);
        }
        return ResponseResult.success(countryByContinent);
    }

}
