package com.paic.ncbs.claim.controller.api;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.waitingcenter.ApproveCallbackReqDto;
import com.paic.ncbs.claim.model.dto.waitingcenter.ApproveCallbackResDto;
import com.paic.ncbs.claim.service.waitingcenter.WaitingCenterService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 未决审批，立案审批回调
 * @author: justinwu
 * @create 2025/5/26 10:42
 */
@Slf4j
@Api(tags = "未决审批，立案审批回调")
@RestController
@RequestMapping("/public/unsettleapprove")
public class UnSettleCallbackController  extends BaseController {

    @Autowired
    private WaitingCenterService unSettleApproveWaitingCenterServiceImpl;


    @GetMapping(value = "/create")
    public void create(String bussinessId) {
        unSettleApproveWaitingCenterServiceImpl.createInstance(bussinessId);
    }

    @PostMapping("/callback")
    public ApproveCallbackResDto approveCallback(@RequestBody ApproveCallbackReqDto reqDto) {
        log.info("办事中心-审批回调,入参：{}", JSON.toJSONString(reqDto));
        ApproveCallbackResDto approveCallbackResDto = null;
        if (reqDto == null) {
            approveCallbackResDto = ApproveCallbackResDto.fail("请求参数不能为空！");
            log.info("办事中心-审批回调,出参：{}", JSON.toJSONString(approveCallbackResDto));
            return approveCallbackResDto;
        }
        approveCallbackResDto = unSettleApproveWaitingCenterServiceImpl.actionCallback(reqDto);
        log.info("办事中心-审批回调,出参：{}", JSON.toJSONString(approveCallbackResDto));
        return approveCallbackResDto;
    }


}
