package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.model.dto.openapi.ReceiveTpaSettleRequestDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.service.common.ClmsGetPolicyMonthInfoService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.service.openapi.ReceiveTpaSettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "TPA全包理算信息接入：只做入库处理")
@Slf4j
@RestController
@RequestMapping("/public/receiveTpaSettle")
public class ReceiveTpaSettleController {
    @Autowired
    private  ReceiveTpaSettleService receiveTpaSettleService;

    @Autowired
    private ClmsGetPolicyMonthInfoService clmsGetPolicyMonthInfoService;
    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;
    /**
     * 接受TPA全包理算信息
     * @param request
     * @return
     */
    @PostMapping("/receiveTpaSettle")
    public ResponseResult<Object> receiveTpaSettle(@RequestBody ReceiveTpaSettleRequestDTO request){
        log.info("接收TPA全包理算信息={}", JsonUtils.toJsonString(request));
        receiveTpaSettleService.dealTpaSettle(request.getRequestData());

        return  ResponseResult.success(request.getRequestData());
    }
    @GetMapping(value = "/testPolicyInfo/{policyNo}")
    public ResponseResult<Object> testPolicyInfo(@PathVariable("policyNo") String policyNo){
        //2得到保单的起止日期
        PolicyDto policyDto =clmsQueryPolicyInfoService.getPolicyDto(policyNo,null);
        //3根据起止日期计算月数
        List<PolicyMonthDto> monthDtoList = clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),policyDto.getPolicyEndDate());
        return ResponseResult.success(monthDtoList);
    }
}
