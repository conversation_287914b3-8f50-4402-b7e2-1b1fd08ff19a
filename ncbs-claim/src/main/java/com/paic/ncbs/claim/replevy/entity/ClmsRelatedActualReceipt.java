package com.paic.ncbs.claim.replevy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("clms_related_actual_receipt")
public class ClmsRelatedActualReceipt implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;
    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;
    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;
    /**
     * 追偿 次数
     */
    @TableField("sub_Times")
    private Integer subTimes;
    /**
     * 关联业务id
     */
    @TableField("business_id")
    private String businessId;
    /**
     * 关联实收类型 1-追偿 2-负数重开 3-共保摊回
     */
    @TableField("receipt_type")
    private String receiptType;
    /**
     * 银行业务编码
     */
    @TableField("business_no")
    private String businessNo;

    /**
     * 交易日期
     */
    @TableField("trans_date")
    private LocalDateTime transDate;

    /**
     * 收款付款 0-收款 1-付款
     */
    @TableField("direction_type")
    private String directionType;

    /**
     * 交易金额
     */
    @TableField("trans_amount")
    private BigDecimal transAmount;

    /**
     * 银行交易流水号
     */
    @TableField("bank_trans_flow_no")
    private String bankTransFlowNo;

    /**
     * 我方银行卡号
     */
    @TableField("our_bank_account")
    private String ourBankAccount;

    /**
     * 客户打款银行卡号
     */
    @TableField("partner_bank_account")
    private String partnerBankAccount;

    /**
     * 客户打款银行账号名称
     */
    @TableField("partner_bank_account_name")
    private String partnerBankAccountName;
    /**
     * 客户银行名称
     */
    @TableField("partner_bank_name")
    private String partnerBankName;
    /**
     * 客户银行网点名称
     */
    @TableField("partner_bank_branch_name")
    private String partnerBankBranchName;
    /**
     * 附言
     */
    @TableField("post_script")
    private String postScript;

    /**
     * 核销余额
     */
    @TableField("write_off_remain_amount")
    private BigDecimal writeOffRemainAmount;
    /**
     * 核销金额
     */
    @TableField("write_off_amount")
    private BigDecimal writeOffAmount;
    /**
     * 冻结标记，F-冻结/R-释放
     */
    @TableField("freeze_flag")
    private String freezeFlag;
    /**
     * 批次号
     */
    @TableField("batch_no")
    private String batchNo;
    /**
     * 有效标志
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 标志字段 0-待核销，31-核销成功，32-核销失败
     */
    @TableField("flag")
    private String flag;
    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;

    private String idRecoveryRecord;
}
