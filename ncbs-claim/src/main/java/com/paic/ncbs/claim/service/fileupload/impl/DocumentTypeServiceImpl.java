package com.paic.ncbs.claim.service.fileupload.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.SupplementsStateEnum;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.doc.DocumentTypeMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.ExemptDocumentMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.TPAFeign;
import com.paic.ncbs.claim.model.dto.doc.CustomerSupplementsDTO;
import com.paic.ncbs.claim.model.dto.doc.DocumentTypeDTO;
import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;
import com.paic.ncbs.claim.model.dto.fileupload.ExemptDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.SelfDocTypeDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.MediaDto;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseRequestDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseResponseDTO;
import com.paic.ncbs.claim.model.dto.problem.RequestData;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.ClaimUpdateDocumentFullDateService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.fileupload.DocumentTypeService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.service.supplements.SupplementsMaterialService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service("documentTypeService")
@RefreshScope
public class DocumentTypeServiceImpl implements DocumentTypeService {

    /**
     * 回销说明
     */
    @Value("${batch.supplements.remark:超7天自动回销}")
    private String remark;

    @Autowired
    DocumentTypeMapper documentTypeMapper;

    @Autowired
    ExemptDocumentMapper exemptDocumentMapper;

    @Autowired
    FileUploadService fileUploadService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private CaseProcessService caseProcessService ;

    @Autowired
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;
    @Autowired
    private SupplementsMaterialService supplementsMaterialService;
    @Autowired
    private TPAFeign tpaFeign;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;

    @Autowired
    private  ClaimUpdateDocumentFullDateService claimUpdateDocumentFullDateService;

    @Autowired
    private IOperationRecordService operationRecordService;

    private static ExecutorService executorService = new ThreadPoolExecutor(4, 4, 60, TimeUnit.SECONDS, new LinkedBlockingDeque<>(120));

    @Override
    public String getBigCodeBySmallCode(String smallCode) throws GlobalBusinessException {
        return documentTypeMapper.getBigCodeBySmallCode(smallCode);
    }

    @Override
    public List<DocumentTypeDTO> getDocumentAllTypeList() throws GlobalBusinessException {
        List<DocumentTypeDTO> bigList = documentTypeMapper.getDocumentSmallTypeList().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                ()->new TreeSet<>(Comparator.comparing(DocumentTypeDTO::getBigCode))),ArrayList::new));
        List<DocumentTypeDTO> smallTypeList = documentTypeMapper.getDocumentSmallTypeList();
        bigList.forEach(big-> {
            big.setSmallCode(null);
            big.setSmallName(null);
            big.setSmallOrder(null);
            List<DocumentTypeDTO> smallList = smallTypeList.stream()
                    .filter(small->small.getBigCode().equals(big.getBigCode())).collect(Collectors.toList());
            big.setSmallTypeList(smallList);
        });
        return bigList;
    }

    @Override
    public List<DocumentTypeDTO> getDocumentSmallTypeList() throws GlobalBusinessException {
        return documentTypeMapper.getDocumentSmallTypeList();
    }

    @Override
    public List<SelfDocTypeDTO> getSelfTypeByScene(String scene) throws GlobalBusinessException {
        return documentTypeMapper.getSelfTypeByScene(scene);
    }

    @Override
    public Map<String, Set<String>> getExemptDocumentByReportNo(String reportNo) throws GlobalBusinessException {
        List<ExemptDocumentDTO> exemptDocumentDTOList = exemptDocumentMapper.getExemptDocument(reportNo, null);
        if (ListUtils.isEmptyList(exemptDocumentDTOList)) {
            return null;
        }
        Map<String, Set<String>> exemptMap = new HashMap<>();
        for (ExemptDocumentDTO exemptDocumentDTO : exemptDocumentDTOList) {
            String smallCodes = exemptDocumentDTO.getSmallCodes();
            if (StringUtils.isNotEmpty(smallCodes)) {
                Set<String> exemptDocSet = new HashSet<>();
                String[] smallCodeArr = smallCodes.split("\\,");
                Arrays.asList(smallCodeArr).stream().forEach(smallCode -> exemptDocSet.add(smallCode));
                exemptMap.put(StringUtils.getFlowTypeByCaseTimes(exemptDocumentDTO.getCaseTimes()), exemptDocSet);
            }
        }
        return exemptMap;
    }

    @Override
    @Transactional
    public void customerSupplements(CustomerSupplementsDTO customerSupplementsDTO) {
        /*  delete by zjtang 取消旧逻辑校验
        //校验当前案件是否存在除过主任务外其他未完成的流程,若存在，就不能发起客户补材
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setCaseTimes(customerSupplementsDTO.getCaseTimes());
        taskInfoDTO.setReportNo(customerSupplementsDTO.getReportNo());
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getUndoTaskInfoList(taskInfoDTO);
         if(ListUtils.isNotEmpty(taskInfoVOList) && taskInfoVOList.size() > 0) {
            throw new GlobalBusinessException("当前案件存在其他未处理任务，不能发起客户补材！");
        }
        */
        if (StringUtils.isNotEmpty(customerSupplementsDTO.getTaskDefinitionBpmKey())) {
            TaskInfoDTO task = taskInfoMapper.checkWorkflow(customerSupplementsDTO.getReportNo(), customerSupplementsDTO.getCaseTimes(),
                    customerSupplementsDTO.getTaskDefinitionBpmKey(), BpmConstants.TASK_STATUS_PENDING);
            if (Objects.isNull(task)) {
                throw new GlobalBusinessException(String.format("%s任务不存在或者已处理，不能发起客户补材！", BpmConstants.TASK_MAP.get(customerSupplementsDTO.getTaskDefinitionBpmKey())));
            }
        }
        //校验当前流程是否有冲突 客户补材 发起
        bpmService.processCheck(customerSupplementsDTO.getReportNo(),BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS,BpmConstants.OPERATION_INITIATE);

        if(StringUtils.isEmptyStr(customerSupplementsDTO.getTaskDefinitionBpmKey())) {
            String bpmKey = taskInfoMapper.getPendingTaskCount(customerSupplementsDTO.getReportNo(), customerSupplementsDTO.getCaseTimes());
            customerSupplementsDTO.setTaskDefinitionBpmKey(bpmKey);
        }
        String caseProcessStatus= getCaseProcessStatus(customerSupplementsDTO.getTaskDefinitionBpmKey());
        //组装任务记录表参数
        SupplementsMaterialDTO smDTO = getSupplementsMaterialDTO(customerSupplementsDTO);


        //调用客户补材接口
        SyncCaseStatusDto dto = new SyncCaseStatusDto();
        List<MediaDto> mediaList = customerSupplementsDTO.getMediaList();
        mediaList.forEach(mediaDto -> mediaDto.setSmallTypeName(documentTypeMapper.getSmallNameBySmallCode(mediaDto.getSmallTypeCode())));
        BeanUtils.copyProperties(customerSupplementsDTO,dto);
        dto.setCaseStatus(SyncCaseStatusEnum.ADDMEDIA);
        dto.setOperatorName(WebServletContext.getUserName());
        //补材任务记录
        supplementsMaterialService.saveData(smDTO);
        //通过mq同步案件
        mqProducerSyncCaseStatusService.syncCaseStatus(dto);
        //将原工作流挂起，
        bpmService.suspendOrActiveTask_oc(customerSupplementsDTO.getReportNo(),customerSupplementsDTO.getCaseTimes(), customerSupplementsDTO.getTaskDefinitionBpmKey(),true);
        //操作记录
        operationRecordService.insertOperationRecordByLabour(customerSupplementsDTO.getReportNo(), BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS, "发起", customerSupplementsDTO.getSupplementsDesc());
        //创建一个新的待客户补材的工作流
        bpmService.startProcess_oc(customerSupplementsDTO.getReportNo(),customerSupplementsDTO.getCaseTimes(), BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS);
        //同时更新状态
        caseProcessService.updateCaseProcess(customerSupplementsDTO.getReportNo(),customerSupplementsDTO.getCaseTimes(), caseProcessStatus);

    }

    /**
     * 根据业务节点得到 子任务节点代码
     * @param taskDefinitionBpmKey
     */
    private String getCaseProcessStatus(String  taskDefinitionBpmKey) {
        String caseProcessStatus= "";
        if(Objects.equals(taskDefinitionBpmKey,BpmConstants.OC_REPORT_TRACK)){
            caseProcessStatus = CaseProcessStatus.WAIT_CUSTOMER_SUPPLEMENTS_0104.getCode();
        }else if(Objects.equals(taskDefinitionBpmKey,BpmConstants.OC_CHECK_DUTY)){
            caseProcessStatus = CaseProcessStatus.WAIT_CUSTOMER_SUPPLEMENTS_0201.getCode();
        } else if (Objects.equals(taskDefinitionBpmKey,BpmConstants.OC_MANUAL_SETTLE)) {
            caseProcessStatus = CaseProcessStatus.WAIT_CUSTOMER_SUPPLEMENTS.getCode();
        }else{
            LogUtil.audit("当节点暂不支持客户补材！");
            throw new GlobalBusinessException("当前节点暂不支持客户补材！");
        }
        return caseProcessStatus;
    }

    /**
     * 设置值
     * @param customerSupplementsDTO
     */
    private SupplementsMaterialDTO getSupplementsMaterialDTO(CustomerSupplementsDTO customerSupplementsDTO) {
        SupplementsMaterialDTO supplementsMaterialDTO = new SupplementsMaterialDTO();
        supplementsMaterialDTO.setReportNo(customerSupplementsDTO.getReportNo());
        supplementsMaterialDTO.setCaseTimes(customerSupplementsDTO.getCaseTimes());
        supplementsMaterialDTO.setTaskDefinitionBpmKey(customerSupplementsDTO.getTaskDefinitionBpmKey());
        supplementsMaterialDTO.setReportAcceptUm(WebServletContext.getUserId());
        supplementsMaterialDTO.setSupplementsState(SupplementsStateEnum.SUPPLEMENTS_STATE_00.getCode());
        supplementsMaterialDTO.setSupplementsDesc(customerSupplementsDTO.getSupplementsDesc());
        List<MediaDto> mediaList = customerSupplementsDTO.getMediaList();
        if(CollectionUtil.isEmpty(mediaList)){
            throw new GlobalBusinessException("补充材料内容不能为空请确认！");
        }
        List<String> stringList =new ArrayList<>();
        for (MediaDto dto : mediaList) {
            stringList.add(dto.getSmallTypeCode());
        }
        String content = StringUtils.getMergeString(stringList, Constants.SEPARATOR);
        supplementsMaterialDTO.setSupplementsContent(content);

        return supplementsMaterialDTO;
    }

    @Override
    @Transactional
    public void customerSupplementsSuccess(String reportNo, Integer caseTimes, String remark) {
        if (StringUtils.isEmptyStr(remark)) {
            remark = "补材已完成。";
        }

        String taskId = taskInfoMapper.getTaskId(reportNo,caseTimes, BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS);
        LogUtil.audit("DocumentTypeServiceImpl customerSupplementsSuccess reportNo:{},caseTimes:{} 执行开始", reportNo, caseTimes);
        List<SupplementsMaterialDTO> dtos = supplementsMaterialService.getSupplementsMaterial(reportNo,caseTimes);
        //为空 兼容老数据，老数据不会在clms_supplements_material_task表中存在数据
        if(CollectionUtil.isEmpty(dtos)){
            dealOldServiceData(reportNo,caseTimes);
        }else{
            //不为空：新功能上线后的数据
            List<SupplementsMaterialDTO> dtoList =  dtos.stream().filter(dto->SupplementsStateEnum.SUPPLEMENTS_STATE_00.getCode().equals(dto.getSupplementsState())).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(dtoList)){
                throw new GlobalBusinessException("任务已完成，请勿重复提交！");
            }

            dtoList.get(0).setRemark(remark);
            dealServiceData(reportNo,caseTimes,dtoList.get(0));
        }
        //更新资料齐全时间
        claimUpdateDocumentFullDateService.updateDocFullDate(reportNo,caseTimes,BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS);
        //TPA全流程案件，调用TPA答复接口回复
        List<ReportInfoExEntity>  reportInfos = reportInfoExMapper.getReportInfoEx(reportNo);
        ReportInfoExEntity reportInfo = reportInfos.get(0);
        if("1".equals(reportInfo.getClaimDealWay())
                && !"channel".equals(reportInfo.getCompanyId())
                && 1 == caseTimes) {
            ProblemCaseRequestDTO problemCaseRequestDTO = new ProblemCaseRequestDTO();
            problemCaseRequestDTO.setCompanyId(reportInfo.getCompanyId());
            RequestData requestData = new RequestData();
            requestData.setRegistNo(reportNo);
            requestData.setCaseConclusion("2");
            requestData.setProblemNo(taskId);
            requestData.setProblemType("01");
            try {
                requestData.setReplyTime(DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS));
            } catch (ParseException e) {
                LogUtil.error("TPA问题件答复接口日期转换错误：{}",e.getMessage());
            }
            requestData.setRemark(remark);
            problemCaseRequestDTO.setRequestData(requestData);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复参数：{}",reportNo, JSON.toJSONString(problemCaseRequestDTO));
            ProblemCaseResponseDTO response = tpaFeign.response(problemCaseRequestDTO);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复返回：{}",reportNo, JSON.toJSONString(response));
        }
    }

    /**
     * 老数据处理
     * @param reportNo
     * @param caseTimes
     */

    private void dealOldServiceData(String reportNo, Integer caseTimes){
        //将待客户补材工作流完成，将挂起的工作流重新打开,同时更新状态
        bpmService.completeTask_oc_css(reportNo, caseTimes, BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS);
        bpmService.suspendOrActiveTask_oc_css(reportNo, caseTimes, BpmConstants.OC_MANUAL_SETTLE, false);
        caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.PENDING_SETTLE.getCode());
        LogUtil.audit("DocumentTypeServiceImpl customerSupplementsSuccess reportNo:{},caseTimes:{} 执行完成", reportNo, caseTimes);
        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS, "回销", null, WebServletContext.getUserIdForLog());
    }

    /**
     * 处理业务数据
     * @param reportNo
     * @param caseTimes
     */
    private void dealServiceData(String reportNo, Integer caseTimes,SupplementsMaterialDTO dto){
        //补材中
        //1：更新补材任务记录表状态为 01-完成
        supplementsMaterialService.updateData(dto);
        String userId = WebServletContext.getUserIdForLog();

        // 前置挂起任务是否存在
        TaskInfoDTO task = taskInfoMapper.checkWorkflow(reportNo, caseTimes, dto.getTaskDefinitionBpmKey(), BpmConstants.TASK_STATUS_PENDING);
        boolean flag = Objects.nonNull(task);

        if(!ConstValues.SYSTEM_UM.equals(userId) && !Constants.SYSTEM_USER.equals(userId)){
            LogUtil.audit("更新补材任务记录表状态为 01-完成{}", JSONObject.toJSONString(dto));
            //2将待客户补材工作流完成
            bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS);
            LogUtil.audit("completeTask_oc_css客户补材工作流完成");

            if (flag) {
                //3将挂起的工作流重新打开
                bpmService.suspendOrActiveTask_oc(reportNo, caseTimes, dto.getTaskDefinitionBpmKey(), false);
                LogUtil.audit("suspendOrActiveTask_oc_css挂起的工作流重新打开");
            }
        } else {
            LogUtil.audit("更新补材任务记录表状态为 01-完成{}", JSONObject.toJSONString(dto));
            //2将待客户补材工作流完成
            bpmService.completeTask_oc_css(reportNo, caseTimes, BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS);
            LogUtil.audit("completeTask_oc_css客户补材工作流完成");

            if (flag) {
                //3将挂起的工作流重新打开
                bpmService.suspendOrActiveTask_oc_css(reportNo, caseTimes, dto.getTaskDefinitionBpmKey(), false);
                LogUtil.audit("suspendOrActiveTask_oc_css挂起的工作流重新打开");
            }
        }

        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS, "回销", dto.getRemark(), userId);

        if (flag) {
            //4更新状态
            String caseProcessStatus = Transform.getCaseProcessStatus(dto.getTaskDefinitionBpmKey());

            caseProcessService.updateCaseProcess(reportNo, caseTimes, caseProcessStatus);
            LogUtil.audit("updateCaseProcess更新完成报案号:{},caseTimes:{},更新状态{}, 执行完成", reportNo, caseTimes,caseProcessStatus);
        }
    }



    /**
     * 批量处理超期未处理过的补材任务
     * @param dto
     */

    @Override
    @Transactional
    public void updateServiceData(SupplementsMaterialDTO dto) {
        String reportNo=dto.getReportNo();
        Integer caseTimes=dto.getCaseTimes();
        dto.setRemark(remark);

        //1：更新任务记录表状态为 02-关闭
        String taskId = taskInfoMapper.getTaskId(reportNo,caseTimes,BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS);
        supplementsMaterialService.updateServiceData(dto);
        LogUtil.info("1更新任务记录表状态为 02-关闭,报案号={},参数={}",reportNo, JsonUtils.toJsonString(dto));
        //2将待客户补材工作流完成
        bpmService.completeTask_oc_css(reportNo, caseTimes, BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS);
        LogUtil.info("2将待客户补材工作流完成,报案号={}",reportNo);
        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS, "回销", dto.getRemark(), ConstValues.SYSTEM_UM);
        TaskInfoDTO task = taskInfoMapper.checkWorkflow(reportNo, caseTimes, dto.getTaskDefinitionBpmKey(), BpmConstants.TASK_STATUS_PENDING);
        LogUtil.info("客户补材工作流完 , 报案号={} , 前置任务w",reportNo);
        if (Objects.isNull(task)) {
            LogUtil.info("客户补材批处理完成, 但前置任务不存在或者已处理, 报案号={}", reportNo);
            return;
        }

        //3将挂起的工作流重新打开
        bpmService.suspendOrActiveTask_oc_css(reportNo, caseTimes, dto.getTaskDefinitionBpmKey(), false);
        LogUtil.info("3将挂起的工作流重新打开,报案号={}",reportNo);
        //4更新状态
        String caseProcessStatus = Transform.getCaseProcessStatus(dto.getTaskDefinitionBpmKey());
        caseProcessService.updateCaseProcess(reportNo, caseTimes, caseProcessStatus);
        LogUtil.info("4更新状态,caseProcessStatus={},报案号={}",caseProcessStatus,reportNo);

        //TPA全流程案件，调用TPA答复接口回复
        List<ReportInfoExEntity>  reportInfos = reportInfoExMapper.getReportInfoEx(reportNo);
        ReportInfoExEntity reportInfo = reportInfos.get(0);
        if("1".equals(reportInfo.getClaimDealWay())
                && !"channel".equals(reportInfo.getCompanyId())
                && 1== caseTimes) {
            ProblemCaseRequestDTO problemCaseRequestDTO = new ProblemCaseRequestDTO();
            problemCaseRequestDTO.setCompanyId(reportInfo.getCompanyId());
            RequestData requestData = new RequestData();
            requestData.setRegistNo(reportNo);
            requestData.setCaseConclusion("2");
            requestData.setProblemNo(taskId);
            requestData.setProblemType("01");
            try {
                requestData.setReplyTime(DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS));
            } catch (ParseException e) {
                LogUtil.error("TPA问题件答复接口日期转换错误：{}",e.getMessage());
            }
            requestData.setRemark(remark);
            problemCaseRequestDTO.setRequestData(requestData);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复参数：{}",reportNo, JSON.toJSONString(problemCaseRequestDTO));
            ProblemCaseResponseDTO response = tpaFeign.response(problemCaseRequestDTO);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复返回：{}",reportNo, JSON.toJSONString(response));
        }
    }
    /**
     * 查询客户补材任务记录
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public List<SupplementsMaterialDTO> getCustomerSupplements(String reportNo, Integer caseTimes) {
        List<SupplementsMaterialDTO> resultList = supplementsMaterialService.getCustomerSupplements(reportNo,caseTimes);
        if(CollectionUtil.isEmpty(resultList)){
            return new ArrayList<>();
        }
        //一次性查询出证明材料类型，用于单证细类代码转换为中文给前端展示
        List<DocumentTypeDTO> smallTypeList = documentTypeMapper.getDocumentSmallTypeList();

        for (SupplementsMaterialDTO dto : resultList) {
            if(Objects.equals(SupplementsStateEnum.SUPPLEMENTS_STATE_02.getCode(),dto.getSupplementsState())){
                //给前端显示用
                dto.setSellBackType("任务到期");
            }else{
                ////给前端显示用
                dto.setSellBackType("客户提交");
            }
            //补材类型
            String content = dto.getSupplementsContent();
            if(StrUtil.isEmpty(content) || CollectionUtil.isEmpty(smallTypeList)){
                continue;
            }
            List<String> contentCodeList= StringUtils.getListWithSeparator(content,Constants.SEPARATOR);
            List<String> bigTypeCodeList = new ArrayList<>();
            List<String> smallTypeNameList = new ArrayList<>();
            for (String smallTypeCode : Optional.ofNullable(contentCodeList).orElseGet(ArrayList::new)) {
                Optional<DocumentTypeDTO> documentTypeOpt = smallTypeList.stream().filter(item -> StringUtils.isEqualStr(smallTypeCode, item.getSmallCode())).findFirst();
                if (documentTypeOpt.isPresent()){
                    if(!bigTypeCodeList.contains(documentTypeOpt.get().getBigCode())){
                        bigTypeCodeList.add(documentTypeOpt.get().getBigCode());
                    }
                    smallTypeNameList.add(documentTypeOpt.get().getSmallName());
                }
            }
            String bigTypeCode = StringUtils.getMergeString(bigTypeCodeList,Constants.SEPARATOR);
            String contentName = StringUtils.getMergeString(smallTypeNameList,Constants.SEPARATOR);
            dto.setSupplementsBigCode(bigTypeCode);
            dto.setSupplementsSmallCode(content);
            dto.setSupplementsContent(contentName);

            if(Objects.equals(SupplementsStateEnum.SUPPLEMENTS_STATE_00.getCode(),dto.getSupplementsState())) {
                dto.setUpdatedDate(null);
                dto.setUpdatedBy(null);
            }
        }
        return resultList;
    }

    /**
     * 单证类型code装换为name给前端展示
     * @param contentCodeList
     * @return
     */
    /*private String codeConvertName(List<String> contentCodeList, List<DocumentTypeDTO> smallTypeList) {
        List<String> typeNameList = new ArrayList<>();
        for (String code : contentCodeList) {
            String name = getName(code,smallTypeList);
            typeNameList.add(name);
        }
      return   StringUtils.getMergeString(typeNameList,Constants.SEPARATOR);

    }*/

    /**
     * 从细类集合中获取细类名称
     * @param code
     * @param smallTypeList
     * @return
     */
    /*private String getName(String code,List<DocumentTypeDTO> smallTypeList ){
        for (DocumentTypeDTO documentTypeDTO : smallTypeList) {
            if(Objects.equals(documentTypeDTO.getSmallCode(),code)){
               return  documentTypeDTO.getSmallName();
            }
        }
        return null;
    }*/

}
