package com.paic.ncbs.claim.model.vo.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 打包支付列表VO
 */
public class BatchPaymentVO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 机构编码
     */
    private String departmentAbbrCode;

    /**
     * 机构名称
     */
    private String departmentAbbrName;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 支付用途
     */
    private String paymentType;

    /**
     * 结算总金额
     */
    private BigDecimal sumAmount;

    /**
     * 错误原因
     */
    private String errorMsg;

    /**
     * 结算状态：01-数据校验失败；02-实收实付确认复核通过；11-支付成功；12-支付失败，退票
     */
    private String settlementStatus;

    /**
     * 支付状态
     */
    private String mergePaymentStatus;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date sysCtime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date sysUtime;

    /**
     * 帐号类型:个人帐号=1,公司帐号=0
     */
    private String bankAccountAttribute;

    /**
     * 领款方式
     */
    private String payType;

    /**
     * 结算方式。固定值：合并批量支付
     */
    private String collectPayApproach;

    /**
     * 收款人
     */
    private String payeeName;

    /**
     * 收款人账号
     */
    private String clientBankAccount;

    /**
     * 客户银行代码
     */
    private String clientBankCode;

    /**
     * 客户开户银行
     */
    private String clientBankName;

    /**
     * 开户行明细码
     */
    private String bankDetailCode;

    /**
     * 开户行明细
     */
    private String bankDetail;

    /**
     * 批量支付操作记录
     */
    private List<BatchPayOperationRecordVO> batchPayOperationRecordList;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getDepartmentAbbrCode() {
        return departmentAbbrCode;
    }

    public void setDepartmentAbbrCode(String departmentAbbrCode) {
        this.departmentAbbrCode = departmentAbbrCode;
    }

    public String getDepartmentAbbrName() {
        return departmentAbbrName;
    }

    public void setDepartmentAbbrName(String departmentAbbrName) {
        this.departmentAbbrName = departmentAbbrName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(BigDecimal sumAmount) {
        this.sumAmount = sumAmount;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getSettlementStatus() {
        return settlementStatus;
    }

    public void setSettlementStatus(String settlementStatus) {
        this.settlementStatus = settlementStatus;
    }

    public String getMergePaymentStatus() {
        return mergePaymentStatus;
    }

    public void setMergePaymentStatus(String mergePaymentStatus) {
        this.mergePaymentStatus = mergePaymentStatus;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

    public String getBankAccountAttribute() {
        return bankAccountAttribute;
    }

    public void setBankAccountAttribute(String bankAccountAttribute) {
        this.bankAccountAttribute = bankAccountAttribute;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getCollectPayApproach() {
        return collectPayApproach;
    }

    public void setCollectPayApproach(String collectPayApproach) {
        this.collectPayApproach = collectPayApproach;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount;
    }

    public String getClientBankCode() {
        return clientBankCode;
    }

    public void setClientBankCode(String clientBankCode) {
        this.clientBankCode = clientBankCode;
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName;
    }

    public String getBankDetailCode() {
        return bankDetailCode;
    }

    public void setBankDetailCode(String bankDetailCode) {
        this.bankDetailCode = bankDetailCode;
    }

    public String getBankDetail() {
        return bankDetail;
    }

    public void setBankDetail(String bankDetail) {
        this.bankDetail = bankDetail;
    }

    public List<BatchPayOperationRecordVO> getBatchPayOperationRecordList() {
        return batchPayOperationRecordList;
    }

    public void setBatchPayOperationRecordList(List<BatchPayOperationRecordVO> batchPayOperationRecordList) {
        this.batchPayOperationRecordList = batchPayOperationRecordList;
    }

}
