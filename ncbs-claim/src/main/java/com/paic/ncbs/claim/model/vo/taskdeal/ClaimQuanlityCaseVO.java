package com.paic.ncbs.claim.model.vo.taskdeal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("案件质检查询条件")
public class ClaimQuanlityCaseVO extends EntityDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("是否包含下级机构，Y/N")
    private String isIncludeSubordinates;

    @ApiModelProperty("包含下级机构,机构编码右侧去0 like")
    private List<String> departmentCodes;

    @ApiModelProperty("处理机构")
    private String handleCom;

    @ApiModelProperty("理赔人员")
    private String userCode;

    @ApiModelProperty("被保险人")
    private String insuredName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("事故时间-开始的时间")
    private Date accidStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("事故时间-当结束的时间")
    private Date accidEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("立案时间-开始的时间")
    private Date registStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("立案时间-当结束的时间")
    private Date registEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("结案时间-开始的时间")
    private Date endCaseStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("结案时间-当结束的时间")
    private Date endCaseEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("报案时间-开始时间")
    private Date reportStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("报案时间-结束时间")
    private Date reportEndTime;

    @ApiModelProperty("方案名称")
    private String packageName;

    @ApiModelProperty("理赔产品")
    private List<String> productCode;

    @ApiModelProperty("除外产品")
    private List<String> exProductCode;

    @ApiModelProperty("最小赔款金额")
    private String minmoney;

    @ApiModelProperty("最大赔款金额")
    private String maxmoney;

    @ApiModelProperty("抽取比例(%)")
    private String proportion;

    @ApiModelProperty("抽取件数")
    private String sum;
}
