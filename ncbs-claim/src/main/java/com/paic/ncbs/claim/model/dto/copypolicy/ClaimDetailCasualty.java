package com.paic.ncbs.claim.model.dto.copypolicy;

import lombok.Data;

import java.util.List;

@Data
public class ClaimDetailCasualty {
    private String lossTypeForCasulaty;//意健险损失类型（1:意外2:疾病3:其它）
    private String lossType;//具体损失类型（详见具体损失类型说明）
    private String deathYn;//被保险人是否死亡Y:死亡 N:非死亡 默认N
    private String deathDate;//被保险人死亡时间
    private String majorDiseaseYn;//是否是重大疾病(Y:是 N:否)
    private String majorDiseaseDate;//重大疾病确诊时间（如:20230905）
    private String majorDisease;//重大疾病代码（详见重大疾病代码说明）
    private String impedimentYn;//是否伤残（Y:是 N:否 默认N）
    private String disabilityStandardCls;//残标类型(0:旧残标,1:新残标)
    private String permTempDate;//伤残日期
    private String bodyPart;//伤残部位（详见伤残部位说明）
    private String workingYn;//是否为工作中（Y:是 N:否 默认N）
    private String medicalExpensesCurrency;//赔款币种
    private String lossDescription;//损失描述
    private List<ClaimDetailCasualtyMedical> claimDetailCasualtyMedicalList;
    private List<ClaimDetailCasualtyInvoice> claimDetailCasualtyInvoiceList;
    private List<ClaimDetailCasualtyDrug> claimDetailCasualtyDrugList;
}
