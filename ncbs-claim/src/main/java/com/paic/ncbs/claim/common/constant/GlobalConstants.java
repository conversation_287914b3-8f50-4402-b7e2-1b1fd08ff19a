package com.paic.ncbs.claim.common.constant;


import com.paic.ncbs.claim.common.enums.CaseProcessStatus;

import java.util.*;


public class GlobalConstants {

    private GlobalConstants() {

    }
    //费用类型 赔款估损
    public static final String FEE_TYPE_AMOUNT = "50000";
    //费用类型 公估费
    public static final String FEE_TYPE_FEE = "70002";

    //收款人类型 被保险人
    public static final String PAY_ACC_TYPE_CODE_01 = "01";

    //收款人与被保险人关系
    public static final String ACC_RELATION_CODE_06 = "06";
    public static final String ACC_RELATION_CODE_11 = "11";

    //账户类型 0：对公账户；1：个人账户
    public static final String ACC_TYPE_CODE_0 = "0";
    public static final String ACC_TYPE_CODE_1 = "1";

    //共保类型 1非共保
    public static final String COINS_TREATY_TYPE_1 = "1";
    //2 Total
    public static final String COINS_TREATY_TYPE_2 = "2";
    //3 Only Out Share
    public static final String COINS_TREATY_TYPE_3 = "3";

    //结案类型 结案类型 1 Payment Closing(支付结案）5 Claim Withdrawal(理赔注销） 6 “0” Claim Amount （0结案）
    public static final String CASE_CONCLUSION_PAYMENT_CLOSING = "1";
    public static final String CASE_CONCLUSION_CLAIM_WITHDRAWAL = "5";
    public static final String CASE_CONCLUSION_0_CLAIM_AMOUNT = "6";
    //重开
    public static final String CASE_CONCLUSION_RESTART = "3";
    //拒赔结案
    public static final String CASE_CONCLUSION_REJECT = "7";

    //dtmnCls 只有公估费或0赔、注销时为01,其他为02
    public static final String DTMNCLS_01 = "01";
    public static final String DTMNCLS_02 = "02";

    //是 否
    public static final String YES = "Y";
    public static final String No = "N";
    public static final String SUCCESS = "1";
    public static final String FAILED = "0";



}