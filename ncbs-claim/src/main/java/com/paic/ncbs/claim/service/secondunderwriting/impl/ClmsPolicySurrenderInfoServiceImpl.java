package com.paic.ncbs.claim.service.secondunderwriting.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.clms.ClmsPolicySurrenderInfoEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsPolicySurrenderInfoMapper;
import com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSecondUnderwritingMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasApplicantInfoDTO;
import com.paic.ncbs.claim.model.vo.senconduw.*;
import com.paic.ncbs.claim.service.common.ClaimCommonQueryFileInfoService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicySurrenderInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.paic.ncbs.claim.common.constant.Constants.CURRENCY_CNY;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;


/**
 * 理赔保单解约信息表(ClmsPolicySurrenderInfoEntity)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-03 14:49:01
 */
@Service("clmsPolicySurrenderInfoService")
public class ClmsPolicySurrenderInfoServiceImpl implements ClmsPolicySurrenderInfoService {
    @Resource
    private ClmsPolicySurrenderInfoMapper clmsPolicySurrenderInfoMapper;

    @Autowired
    private OcasRequest ocasRequest;

    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private ClmsSecondUnderwritingMapper clmsSecondUnderwritingMapper;

    @Autowired
    private ClaimCommonQueryFileInfoService claimCommonQueryFileInfoService;

    @Autowired
    private CaseBaseService caseBaseService;

    /**
     * 通过reportno，casetimes 查询
     *
     * @param reportNo, caseTimes
     */
    @Override
    public ClmsPolicySurrenderInfoDTO getSurrenderInfo(String reportNo, Integer caseTimes,String operateType) {
        ClmsPolicySurrenderInfoDTO returnDto = new ClmsPolicySurrenderInfoDTO();
        List<ClmsPolicySurrenderInfoVO>  surrenderInfoVOList = new ArrayList<>();
        //查询保单核保信息
        ClmsSecondUnderwritingEntity entity =  clmsSecondUnderwritingMapper.getUwInfoOrderBySerialNo(reportNo,caseTimes);
        if(!ObjectUtil.isEmpty(entity)){
            surrenderInfoVOList = clmsPolicySurrenderInfoMapper.getSurrenderList(entity.getId(),operateType);
            //处理上传后的文件 给前端反显用
            if(CollectionUtil.isNotEmpty(surrenderInfoVOList)){
                FileInfoDTO paramsDto = new FileInfoDTO();
                paramsDto.setReportNo(reportNo);
                //文件反显
                for (ClmsPolicySurrenderInfoVO vo : surrenderInfoVOList) {
                    //1上传附件信息反显查询
                    List<FileInfoDTO> infoDTOS =  getFileDtoList(vo.getFileId(),paramsDto);
                    vo.setFileInfoDTOList(infoDTOS);
                    //2合议附件上传信息反显查询
                    List<FileInfoDTO> collegialFileInfoDTOList = getFileDtoList(vo.getCollegialFileId(),paramsDto);
                    vo.setCollegialFileInfoDTOList(collegialFileInfoDTOList);
                    //保单历史报案最近事故日期查询
                    Date accidentDate = clmsPolicySurrenderInfoMapper.getPolicyReportAccdentDate(vo.getPolicyNo());
                    vo.setAccidentDate(accidentDate);
                }
            }
        }
        returnDto.setSurrenderInfoVOList(surrenderInfoVOList);
        return returnDto;

    }

    /**
     * 查询反显文件信息
     *
     * @param fileId
     * @param paramsDto
     * @return
     */
    private List<FileInfoDTO> getFileDtoList(String fileId, FileInfoDTO paramsDto) {
        List<FileInfoDTO> infoDTOS = new ArrayList<>();
        List<String> fileIdList = StringUtils.getListWithSeparator(fileId,Constants.SEPARATOR);
        if(CollectionUtil.isNotEmpty(fileIdList)){
            for (String fileid: fileIdList) {
                paramsDto.setFileId(fileid);
                FileInfoDTO fileInfoDTO =  claimCommonQueryFileInfoService.getFileInfo(paramsDto);
                infoDTOS.add(fileInfoDTO);
            }
        }
        return infoDTOS;
    }

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public void saveData(ClmsPolicySurrenderInfoDTO dto) {
        //0:插入前删除原来录入的数据（新增，更新都是同一个按钮）
        clmsPolicySurrenderInfoMapper.deleteByReportNoAndCaseTimes(dto.getReportNo(),dto.getCaseTimes());
        //1入参校验
        List<ClmsPolicySurrenderInfoEntity> entityList = dealData(dto);
        //插入
        if(CollectionUtil.isNotEmpty(entityList)){
            this.clmsPolicySurrenderInfoMapper.insertBatch(entityList);
        }

    }

    /**
     * 业务数据处理
     * @param dto
     */
    private List<ClmsPolicySurrenderInfoEntity> dealData(ClmsPolicySurrenderInfoDTO dto) {
        List<ClmsPolicySurrenderInfoVO> infoVOS = dto.getSurrenderInfoVOList();
        if(CollectionUtil.isEmpty(infoVOS)){
            return null;
        }
        //1:判断当前报案号，赔案号下 是否还有处于 送核审批中的任务，如果有需要等核保完成后才让保存
        ClmsSecondUnderwritingEntity uwEntity = clmsSecondUnderwritingMapper.getUWRecord(dto.getReportNo(),dto.getCaseTimes());
        if(ObjectUtil.isNotEmpty(uwEntity)){
            throw new GlobalBusinessException("还有送核审批中的任务未完成！");
        }
        List<ClmsPolicySurrenderInfoEntity> entityList =null;
        for (ClmsPolicySurrenderInfoVO vo : infoVOS) {
            //入参校验
            checkInputData(vo,dto);
            if(ObjectUtil.isNull(entityList)){
                entityList = new ArrayList<>();
            }
            ClmsPolicySurrenderInfoEntity entity = new ClmsPolicySurrenderInfoEntity();
            BeanUtils.copyProperties(vo,entity);
            entity.setId(UuidUtil.getUUID());
            entity.setFileId(dealFielIdList(vo.getFileIdList()));
            entity.setCollegialFileId(dealFielIdList(vo.getCollegialFileIdList()));
            entity.setReportNo(dto.getReportNo());
            entity.setCaseTimes(dto.getCaseTimes());
            entity.setCreatedBy(WebServletContext.getUserId());
            entity.setUpdatedBy(WebServletContext.getUserId());
            entity.setSyncPosStatus("0");//0-未同步
            entityList.add(entity);
        }

        return entityList;
    }

    /**
     * 组装fileidlist数据为 字符串 逗号隔开
     * @param fileIdList
     * @return
     */
    private String dealFielIdList(List<String> fileIdList) {
       String fieldId =  StringUtils.getMergeString(fileIdList,Constants.SEPARATOR);
       return fieldId;
    }

    /**
     * 入参校验
     * @param vo
     */
    private void checkInputData(ClmsPolicySurrenderInfoVO vo,ClmsPolicySurrenderInfoDTO dto){
        String   resultReportNo =  clmsPolicySurrenderInfoMapper.getClaimingInfo(vo.getPolicyNo(),dto.getReportNo());
        if(StrUtil.isNotEmpty(resultReportNo)){
            throw new GlobalBusinessException("保单号"+vo.getPolicyNo()+"有其他理赔在途案件,"+resultReportNo+"案件尚在处理中!");
        }
        if(ObjectUtil.isEmpty(vo.getPolicyNo())){
            throw new GlobalBusinessException("保单号不能为空！");
        }
        if(ObjectUtil.isEmpty(vo.getSurrenderType())){
            throw new GlobalBusinessException("解约方式不能为空！");
        }
        if(ObjectUtil.isEmpty(vo.getSurrenderReason())){
            throw new GlobalBusinessException("解约原因不能为空！");
        }
        if(ObjectUtil.isEmpty(vo.getEffectiveDate())){
            throw new GlobalBusinessException("解约生效日不能为空！");
        }
        if(ObjectUtil.isEmpty(vo.getIdSeconduwPolicyConclusion())){
            throw new GlobalBusinessException("二核保单核保结论主键必填！");
        }
        //应收保费，实收保费，未满期保费的校验
        if(ObjectUtil.isEmpty(vo.getTotalActualPremium())){
            throw new GlobalBusinessException("实收保费不能为空！");
        }
        if(ObjectUtil.isEmpty(vo.getTotalAgreePremium())){
            throw new GlobalBusinessException("应收保费不能为空！");
        }
        if(ObjectUtil.isEmpty(vo.getUnearnedPrem())){
            throw new GlobalBusinessException("未满期净保费不能为空！");
        }
        if(CollectionUtil.isEmpty(vo.getFileIdList())){
            throw new GlobalBusinessException("请上传附件");
        }
        if(vo.getFileIdList().size()>10){
            throw new GlobalBusinessException("上传文件个数不能超过10个！");
        }
        //合议的情况 合议附件必传
        if(Objects.equals(vo.getCollegialFlag(),"1")){
            if(CollectionUtil.isEmpty(vo.getCollegialFileIdList())){
                throw new GlobalBusinessException("选择合议，会议附件必传");
            }
            if(vo.getCollegialFileIdList().size()>10){
                throw new GlobalBusinessException("合议附件个数不能超过10个！");
            }
        }
        //选择退费的情况下才会录入退费金额：对录入退费金额的情况做校验
        if(Objects.equals(vo.getSurrenderType(), Constants.SURRENDER_TYPE_ONE)){

            if(ObjectUtil.isEmpty(vo.getRefundType())){
                throw new GlobalBusinessException("解约退费金额类型不能为空！");
            }
            //1-约定退费金额的校验
            if(Objects.equals(vo.getRefundType(),Constants.REFUND_TYPE_ONE)){
                if(ObjectUtil.isEmpty(vo.getRefundAmount())){
                    throw new GlobalBusinessException("解约退费金额不能为空！");
                }
                if(!BigDecimalUtils.compareBigDecimalPlus(vo.getRefundAmount(), BigDecimal.ZERO)){
                    throw new GlobalBusinessException("约定退费金额不能小于等于0");
                }
                if(BigDecimalUtils.compareBigDecimalPlus(vo.getRefundAmount(), vo.getTotalActualPremium())){
                    throw new GlobalBusinessException("约定退费金额不能大于实收总保费");
                }
                //若约定退费金额≥未满期净保费*200%，则默认选择合议，不可更改
                if(BigDecimalUtils.compareBigDecimalPlusOrEqual(vo.getRefundAmount(),vo.getUnearnedPrem().multiply(BigDecimalUtils.getBigDecimal("2")))){
                    if(!Objects.equals(vo.getCollegialFlag(),"1")){
                        throw new GlobalBusinessException("约定退费金额大于等于未满期净保费*200%,只能选择合议");
                    }
                }
            }
            //0-退未满期净保费的情况校验
            if(Objects.equals(vo.getRefundType(),Constants.REFUND_TYPE_ZERO)){
                //退费金额直接取未满期净保费
                vo.setRefundAmount(vo.getUnearnedPrem());
            }
        }
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsPolicySurrenderInfoMapper.deleteById(id) > 0;
    }

    @Override
    public Integer getSurrenderCount(String reportNo, Integer caseTimes) {
        return clmsPolicySurrenderInfoMapper.getSurrenderCount( reportNo, caseTimes);
    }

    @Override
    public ClmsPolicyCalculateAgreeDTO calculateAgree(String policyNo, String surrenderDate) {
        Map<String,String> param = new HashMap<>();
        param.put("policyNo",policyNo);
        param.put("surrenderDate",surrenderDate);
        String result = ocasRequest.calculateAgree(param);
        if (StringUtils.isEmptyStr(result)){
            throw new GlobalBusinessException("调用批改接口失败");
        }
        ClmsPolicyCalculateAgreeDTO calculateAgreeDTO = JSON.parseObject(result, ClmsPolicyCalculateAgreeDTO.class);
        LogUtil.info("调用批改-calculateAgree-返回结果-resultMap-" + JSON.toJSONString(calculateAgreeDTO));
        if (!"00000".equals(calculateAgreeDTO.getReturnCode())) {
            throw new GlobalBusinessException("调用批改接口失败，返回结果：" + calculateAgreeDTO.getReturnMsg());
        }
        String surrenderAmount = calculateAgreeDTO.getSurrenderAmount();
        if (StringUtils.isEmptyStr(calculateAgreeDTO.getSurrenderAmountAgreeAll()) ||StringUtils.isEmptyStr(calculateAgreeDTO.getSurrenderAmountAll())
                || StringUtils.isEmptyStr(surrenderAmount)){
            throw new GlobalBusinessException("调用批改接口失败，返回结果异常，有金额是空的！");
        }
        // 批改返回的是负数，理赔展示成正的
        if (surrenderAmount.startsWith("-")){
            calculateAgreeDTO.setSurrenderAmount(surrenderAmount.replace("-",""));
        }
        return calculateAgreeDTO;
    }

    @Override
    public SurrenderCheckVO checkIsSurrender(String reportNo, Integer caseTimes) {
        List<ClmsPolicySurrenderInfoEntity> surrenderInfo = clmsPolicySurrenderInfoMapper.getSurrenderInfo(reportNo, caseTimes);
        SurrenderCheckVO surrenderCheckVO = SurrenderCheckVO.builder().build();
        surrenderCheckVO.setIsExistSurrender("N");
        if (CollectionUtils.isNotEmpty(surrenderInfo)){
            surrenderCheckVO.setIsExistSurrender("Y");
            StringJoiner sj = new StringJoiner("、");
            surrenderInfo.forEach(s-> sj.add(s.getPolicyNo()+"保单"));
            String surrenderMsg = "提交后" + sj.toString()+ "解约，请确认！";
            surrenderCheckVO.setSurrenderMsg(surrenderMsg);
        }
        return surrenderCheckVO;
    }

    /**
     * 保单号下理赔在途的报案号信息查询 返回报案号
     *
     * @param policyNo
     * @param reportNo
     * @return
     */
    @Override
    public String getClaimingInfo(String policyNo, String reportNo) {
        return clmsPolicySurrenderInfoMapper.getClaimingInfo(policyNo,reportNo);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void applyPolicySurrender(String reportNo, Integer caseTimes) {
        //查询保单核保信息 再判断是否有解约
        LogUtil.info("applyPolicySurrender-start");

        List<ClmsPolicySurrenderInfoVO> surrenderInfoVOList = clmsPolicySurrenderInfoMapper.getSurrenderListByReportNo(reportNo,caseTimes);
        if (CollectionUtils.isEmpty(surrenderInfoVOList)) {
            return;
        }
        LogUtil.info(reportNo+"解约信息={}" + JSON.toJSONString(surrenderInfoVOList));
        // 多保单同时解约是否要保证同时成功或者失败
        for (ClmsPolicySurrenderInfoVO s : surrenderInfoVOList) {
            // 已同步过无需再同步
            if (StringUtils.isEmptyStr(s.getId()) || "1".equals(s.getSyncPosStatus())){
                continue;
            }
            SurrendApplyReq surrendApplyReq = new SurrendApplyReq();
            SurrendEdrBaseInfo surrendEdrBaseInfo = new SurrendEdrBaseInfo();
            surrendEdrBaseInfo.setPolicyNo(s.getPolicyNo());
            surrendEdrBaseInfo.setApplyDate(new Date());
            surrendEdrBaseInfo.setEffectiveDate(new Date());
            // 解约不退费
            if ("0".equals(s.getSurrenderType())){
                surrendEdrBaseInfo.setActualPremiumChange(0d);
            } else {
                BigDecimal refundAmount = nvl(s.getRefundAmount(),0);
                surrendEdrBaseInfo.setActualPremiumChange(refundAmount.doubleValue());
            }

            surrendEdrBaseInfo.setCurrencyCode(CURRENCY_CNY);
            surrendEdrBaseInfo.setInputBy(s.getCreatedBy());
            surrendEdrBaseInfo.setInputDate(new Date());
            surrendEdrBaseInfo.setCancelContractTime(s.getEffectiveDate());
            surrendEdrBaseInfo.setCancelContractReason(s.getSurrenderReason());
            surrendEdrBaseInfo.setHandler(s.getCreatedBy());
            surrendApplyReq.setEdrBaseInfo(surrendEdrBaseInfo);
            OcasApplicantInfoDTO applicantInfo = Optional.ofNullable(ocasMapper.getApplicantInfo(s.getPolicyNo())).orElseThrow(() -> new GlobalBusinessException("投保人信息异常！"));
            EdrCancellation edrCancellation = new EdrCancellation();
            // todo jiajiemi
            edrCancellation.setPaymentPersonName(applicantInfo.getName());
            edrCancellation.setCertificateNo(applicantInfo.getCertificateNo());
            edrCancellation.setCertificateType(applicantInfo.getCertificateType());
            edrCancellation.setEndorseComment(s.getEndorseComment());
            edrCancellation.setClaimCommentFlag(StringUtils.isNotEmpty(s.getEndorseComment()) ? "1" : "0");
            surrendApplyReq.setEdrCancellation(edrCancellation);
            List<AttachmentDTO> attachmentList = new ArrayList<>();
            //文件反显
            FileInfoDTO paramsDto = new FileInfoDTO();
            paramsDto.setReportNo(reportNo);
            //1上传附件信息反显查询
            List<FileInfoDTO> infoDTOS = getFileDtoList(s.getFileId(), paramsDto);
            //2合议附件上传信息反显查询
            List<FileInfoDTO> collegialFileInfoDTOList = getFileDtoList(s.getCollegialFileId(), paramsDto);
            infoDTOS.addAll(collegialFileInfoDTOList);
            infoDTOS.forEach(i -> {
                AttachmentDTO attachmentDTO = new AttachmentDTO();
                attachmentDTO.setDocumentNo(reportNo);
                attachmentDTO.setDocumentClass("T51");
                attachmentDTO.setDocumentFormat(i.getFileFormat());
                attachmentDTO.setDocumentName(i.getFileName());
                attachmentDTO.setDocumentSize(String.valueOf(i.getFileSize()));
                attachmentDTO.setUploadDate(i.getUpdatedDate());
                attachmentDTO.setUploadPath(i.getFileId());
                attachmentDTO.setUploadPersonnel(i.getUploadPersonnel());
                attachmentDTO.setUrl(i.getFileUrl());
                attachmentList.add(attachmentDTO);
            });

            surrendApplyReq.setAttachmentList(attachmentList);
            LogUtil.info("调用批改-applyPolicySurrender-入参-surrendApplyReq-" + JSON.toJSONString(surrendApplyReq));
            String applyEndorse = ocasRequest.applyEndorse(surrendApplyReq);
            if (StringUtils.isEmptyStr(applyEndorse)) {
                throw new GlobalBusinessException("解约调用批改接口失败");
            }
            Map map = JSON.parseObject(applyEndorse, Map.class);
            LogUtil.info("调用批改-applyPolicySurrender-返回结果-resultMap-" + JSON.toJSONString(map));
            if (!GlobalResultStatus.SUCCESS.getCode().equals(map.get("responseCode"))) {
                LogUtil.error("调用批改-applyPolicySurrender-返回结果-resultMap-" + JSON.toJSONString(map));
                throw new GlobalBusinessException(s.getPolicyNo() + "解约调用批改接口失败");
            } else {
                // 把数据更新成已同步
                ClmsPolicySurrenderInfoEntity clmsPolicySurrenderInfoEntity = new ClmsPolicySurrenderInfoEntity();
                clmsPolicySurrenderInfoEntity.setId(s.getId());
                clmsPolicySurrenderInfoEntity.setSyncPosStatus("1");
                clmsPolicySurrenderInfoMapper.update(clmsPolicySurrenderInfoEntity);
                LogUtil.info(s.getPolicyNo() + "调用批改-applyPolicySurrender-解约成功");
            }
        }
    }

    @Override
    public void checkSurrenderIsOtherCase(String reportNo,Integer caseTimes){
        List<ClmsPolicySurrenderInfoVO> surrenderInfoVOList = clmsPolicySurrenderInfoMapper.getSurrenderListByReportNo(reportNo, caseTimes);
        if (ObjectUtil.isNotEmpty(surrenderInfoVOList)) {
            List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, String.valueOf(caseTimes));
            for (CaseBaseEntity caseBaseEntity : caseBaseInfoList) {
                String resultReportNo = clmsPolicySurrenderInfoMapper.getClaimingInfo(caseBaseEntity.getPolicyNo(), reportNo);
                // 校验是否有在途解约
                if (StrUtil.isNotEmpty(resultReportNo)) {
                    throw new GlobalBusinessException("保单号" + caseBaseEntity.getPolicyNo() + "有其他理赔在途案件," + resultReportNo + "案件尚在处理中!");
                }
            }
        }
    }

}
