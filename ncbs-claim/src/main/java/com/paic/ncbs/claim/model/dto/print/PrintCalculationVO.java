package com.paic.ncbs.claim.model.dto.print;

import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PrintCalculationVO {
    private String reportNo;
    private Integer caseTimes;
    /**
     * 文件类型1-pdf 2-word
     */
    private String fileType;
    /**
     * 打印人
     */
    private String printName;
    /**
     * 被保人
     */
    private String insuredName;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 保单起期
     */
    private String insuranceBeginDate;
    /**
     * 保单止期
     */
    private String insuranceEndDate;
    /**
     * 出险日期
     */
    private String accidentDate;
    /**
     * 出险地点
     */
    private String accidentAddress;
    /**
     * 出险原因
     */
    private String accidentReason;
    /**
     * 查勘说明
     */
    private String surveyDetail;
    /**
     * 批单信息
     */
    private List<PrintDutyVO> detailArr;
    private String chsEndorsement;
    /**
     * 赔款总额
     */
    private BigDecimal sumTotal;
    /**
     * 赔款币种
     */
    private String currency;
    /**
     * 总赔款
     */
    private BigDecimal sumPay;
    /**
     * 总费用
     */
    private BigDecimal sumFee;
    /**
     *已赔付赔款
     */
    private BigDecimal paidPay;
    /**
     * 已赔付费用
     */
    private BigDecimal paidFee;
    /**
     * 已赔付总金额
     */
    private BigDecimal paidTotal;
    /**
     * 本次赔款
     */
    private BigDecimal currentPay;
    /**
     * 本次费用
     */
    private BigDecimal currentFee;
    /**
     * 本次总金额
     */
    private BigDecimal currentTotal;
    /**
     * 本次我方份额赔款
     */
    private BigDecimal currentOurPay;
    /**
     * 本次我方份额费用
     */
    private BigDecimal currentOurFee;
    /**
     * 本次我方份额总金额
     */
    private BigDecimal currentOurTotal;
    /**
     * 垫付共保方赔款
     */
    private BigDecimal advanceCoinsPay;
    /**
     * 垫付方费用
     */
    private BigDecimal advanceCoinsFee;
    /**
     * 垫付方总金额
     */
    private BigDecimal advanceCoinsTotal;
    /**
     * 支付列表
     */
    List<PaymentItemDTO> paymentItemList;
    /**
     * 理算提交人
     */
    private String settleName;
    /**
     * 核赔通过时间
     */
    private String underWriteDate;

}
