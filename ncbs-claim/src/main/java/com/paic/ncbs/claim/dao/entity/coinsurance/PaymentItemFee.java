package com.paic.ncbs.claim.dao.entity.coinsurance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 支付项目费用子表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Getter
@Setter
@TableName("clm_payment_item_fee")
public class PaymentItemFee implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付项目表关联主键
     */
    @TableField("id_clm_payment_item")
    private String idClmPaymentItem;

    /**
     *  保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     *  报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     *  赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     *  赔案号
     */
    @TableField("case_no")
    private String caseNo;

    /**
     *  赔付批次号
     */
    @TableField("id_clm_batch")
    private String idClmBatch;

    /**
     *  赔付类型(理算支付类型)（1-赔付 2-预陪 3-垫付 4-追偿 5-垫付转回 6-代位追偿 7-预付 8-残值 9-营改增  10-追偿预付  11-追偿预收 12-一赔预收 13-风险）
     */
    @TableField("claim_type")
    private String claimType;

    /**
     *  赔款类型次数，预陪/垫付/追偿 次数
     */
    @TableField("sub_times")
    private Integer subTimes;

    /**
     *  赔款类型：(13-赔款 11-预赔 12-垫付 1h-追偿,1j直接理赔费用)对应表clm_common_parameter中payment_type
     */
    @TableField("payment_type")
    private String paymentType;

    /**
     *  支付对象信息表主键
     */
    @TableField("id_clm_payment_info")
    private String idClmPaymentInfo;

    /**
     *  收付标识:(收款＝0，付款＝1)
     */
    @TableField("collect_pay_sign")
    private String collectPaySign;

    /**
     *  支付总金额
     */
    @TableField("payment_amount")
    private BigDecimal paymentAmount;

    /**
     *  支付币种
     */
    @TableField("payment_currency_code")
    private String paymentCurrencyCode;

    /**
     *  客户名称
     */
    @TableField("client_name")
    private String clientName;

    /**
     *  客户证件类型：(01-身份证;02-护照;03-军人证;05-驾驶证;06-港澳回乡证或台胞证;99-其它;)对应表clm_common_parameter中zjlx00
     */
    @TableField("client_certificate_type")
    private String clientCertificateType;

    /**
     *  客户证件号码
     */
    @TableField("client_certificate_no")
    private String clientCertificateNo;

    /**
     *  客户银行代码
     */
    @TableField("client_bank_code")
    private String clientBankCode;

    /**
     *  客户开户银行
     */
    @TableField("client_bank_name")
    private String clientBankName;

    /**
     *  客户帐号
     */
    @TableField("client_bank_account")
    private String clientBankAccount;

    /**
     *  客户手机
     */
    @TableField("client_mobile")
    private String clientMobile;

    /**
     *  客户类型：(01=被保险人,02=受益人,03=受害人,04=法院,05=被委托人,99=其他等)，是否对应表clm_common_parameter中collection_code=khlb00,
     */
    @TableField("client_type")
    private String clientType;

    /**
     *  省份名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     *  城市名称
     */
    @TableField("city_name")
    private String cityName;

    /**
     *  地区码
     */
    @TableField("region_code")
    private String regionCode;

    /**
     *  支付方式：(01-公司柜面;02-实时支付03-批量转账,217-指令支付，对应表clm_common_parameter中sftj00)
     */
    @TableField("collect_pay_approach")
    private String collectPayApproach;

    /**
     *  帐号类型:个人帐号=1,公司帐号=0
     */
    @TableField("bank_account_attribute")
    private String bankAccountAttribute;

    /**
     *  案件合并标识：(01=不合并,02=待合并,03=已合并)
     */
    @TableField("merge_sign")
    private String mergeSign;

    /**
     * 支付项目状态：(10=草稿,20=预赔暂存,11=送收付成功待支付,70=支付失败待修改,80=支付成功,90=作废)
     */
    @TableField("payment_item_status")
    private String paymentItemStatus;

    /**
     *  有效起止时间
     */
    @TableField("effective_date")
    private LocalDateTime effectiveDate;

    /**
     *  有效终止时间
     */
    @TableField("invalidate_date")
    private LocalDateTime invalidateDate;

    /**
     *  备注/附言
     */
    @TableField("remark")
    private String remark;

    /**
     *  是否共保摊回：y=是，n=否
     */
    @TableField("is_coinsure")
    private String isCoinsure;

    /**
     *  数据来源，区分理赔各子领域以及其新老数据，默认n代表新车险理赔数据，c代码原车险理赔数据，np代表新财产险理赔数据，op代表原财产险理赔数据，a代表新农险数据
     */
    @TableField("migrate_from")
    private String migrateFrom;

    /**
     * 计算书号（原收付编号）
     */
    @TableField("collect_pay_no")
    private String collectPayNo;

    /**
     * 资金id（原历史通知单主键）
     */
    @TableField("his_id_clm_payment_notice")
    private String hisIdClmPaymentNotice;

    /**
     *  代位追偿理算表主键
     */
    @TableField("id_clm_subrogation_settle")
    private String idClmSubrogationSettle;

    /**
     *  汇率
     */
    @TableField("exchange_rate")
    private BigDecimal exchangeRate;

    /**
     *  转换后金额
     */
    @TableField("convert_amount")
    private BigDecimal convertAmount;

    /**
     * 归档时间
     */
    @TableField("archive_date")
    private LocalDateTime archiveDate;

    /**
     *  下发财务扩展字段
     */
    @TableField("extend_info")
    private String extendInfo;

    /**
     * 开户行明细
     */
    @TableField("bank_detail")
    private String bankDetail;

    /**
     * 支付修改信息记录
     */
    @TableField("modify_info")
    private String modifyInfo;

    /**
     * 支付时间
     */
    @TableField("pay_date")
    private LocalDateTime payDate;

    /**
     * 退回时间
     */
    @TableField("pay_back_date")
    private LocalDateTime payBackDate;

    /**
     * 资金id
     */
    @TableField("financial_id")
    private String financialId;

    /**
     * 计算书号
     */
    @TableField("compensate_no")
    private String compensateNo;

    /**
     * 序号
     */
    @TableField("serial_no")
    private Integer serialNo;

    /**
     *  领款人账号类型为公司时有值，组织机构代码：机构类型为一般机构时存社会统一信用代码，机构类型为其他时存经营证件号码
     */
    @TableField("organize_code")
    private String organizeCode;

    /**
     * 与被保人关系
     */
    @TableField("client_relation")
    private String clientRelation;

    /**
     * 开户行明细码
     */
    @TableField("bank_detail_code")
    private String bankDetailCode;

    /**
     * 是否已加密 y-已加密；n-未加密
     */
    @TableField("sdb_mark")
    private String sdbMark;

    /**
     * 机构类型：机构类型为一般机构时 统一社会信用代码必填，机构类型为其他时经营证件号必填
     */
    @TableField("agency_type")
    private String agencyType;

    /**
     * 客户号通过crm查询返回
     */
    @TableField("customer_no")
    private String customerNo;

    /**
     * 企业/公司证件类型 ：certificatetypeenum枚举类，统一社会信用代码，组织机构代码，税务登记证，营业执照，其他证件
     */
    @TableField("company_card_type")
    private String companyCardType;

    /**
     * 微信openid，供指令支付使用
     */
    @TableField("open_id")
    private String openId;

    /**
     * 微保支付类型
     */
    @TableField("pay_type")
    private String payType;

    /**
     * 费用类型
     */
    @TableField("fee_type")
    private String feeType;

    /**
     * 共保标志 0非共保1共保
     */
    @TableField("coinsurance_mark")
    private String coinsuranceMark;

    /**
     * 是否主承保 0否1是
     */
    @TableField("accept_insurance_flag")
    private String acceptInsuranceFlag;

    /**
     * 共保公司编码
     */
    @TableField("coinsurance_company_code")
    private String coinsuranceCompanyCode;

    /**
     * 共保公司名称
     */
    @TableField("coinsurance_company_name")
    private String coinsuranceCompanyName;

    /**
     * 共保比例
     */
    @TableField("coinsurance_ratio")
    private BigDecimal coinsuranceRatio;

    /**
     * 是否全额给付 0否1是
     */
    @TableField("is_full_pay")
    private String isFullPay;

    /**
     * 共保实付金额
     */
    @TableField("coinsurance_actual_amount")
    private BigDecimal coinsuranceActualAmount;

    /**
     * 财务支付金额
     */
    @TableField("finance_payment_amount")
    private BigDecimal financePaymentAmount;

    /**
     * 支付凭证地址
     */
    @TableField("payment_voucher_url")
    private String paymentVoucherUrl;

    /**
     * 理赔合并支付表id
     */
    @TableField("id")
    private Integer id;

    /**
     * 核销批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 支付金额(赔款领款人信息显示)
     */
    @TableField("chg_payment_amount")
    private BigDecimal chgPaymentAmount;

    /**
     * 我司承担金额变化量(赔款领款人信息显示)
     */
    @TableField("chg_coinsurance_actual_amount")
    private BigDecimal chgCoinsuranceActualAmount;

    /**
     * 实付金额变化量(赔款领款人信息显示)
     */
    @TableField("chg_paid_amount")
    private BigDecimal chgPaidAmount;

    /**
     * 主键
     */
    @TableId(value = "id_clm_payment_item_fee", type = IdType.INPUT)
    private String idClmPaymentItemFee;

    /**
     * 共保不含税费用金额
     */
    @TableField("coinsurance_actual_not_tax_fee")
    private BigDecimal coinsuranceActualNotTaxFee;

    /**
     * 共保税额
     */
    @TableField("coinsurance_actual_tax")
    private BigDecimal coinsuranceActualTax;

    /**
     *  创建人员,um编码
     */
    @TableField("created_by")
    private String createdBy;

    /**
     *  创建日期
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     *  修改人员,um编码
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     *  更新时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;
}
