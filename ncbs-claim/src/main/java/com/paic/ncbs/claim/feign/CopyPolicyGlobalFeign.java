package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.config.FeignConfiguration;
import com.paic.ncbs.claim.model.dto.copypolicy.GlobalPolicyResponseDTO;
import com.paic.ncbs.claim.model.dto.copypolicy.GlobalReportDto;
import com.paic.ncbs.claim.model.dto.copypolicy.GlobalRequestDto;
import com.paic.ncbs.claim.model.dto.copypolicy.GlobalReturnDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
//@FeignClient(name = "global", url = "http://109.101.105.81:31511/ChnWeb/gc",configuration =  {FeignConfiguration.class})
//@FeignClient(name = "global", url = "http://10.20.0.62:31511/ChnWeb/gc",configuration =  {FeignConfiguration.class})
@FeignClient(name = "global", url = "http://10.20.0.62:31511/ChnWeb/gc",configuration =  {FeignConfiguration.class})
public interface CopyPolicyGlobalFeign {

    //global保单列表查询
    @PostMapping("/GC_AcceptInterface.do?method=openApifindPolicyList")
    String findPolicyListByTPA(@RequestBody GlobalPolicyResponseDTO globalPolicyResponseDTO);

    //global保单详情查询
    @PostMapping("/GC_AcceptInterface.do?method=queryPolicyCopyIForTPA")
    String findPolicyDetailByTPA(@RequestBody GlobalPolicyResponseDTO globalPolicyResponseDTO);

    //global估损回流接口
    @PostMapping("/GC_AcceptInterface.do?method=saveClaimReserveInfoForTPA")
    String saveClaimReserveInfoForTPA(@RequestBody GlobalRequestDto globalRequestDto);

    //global立案回流接口
    @PostMapping("/GC_AcceptInterface.do?method=saveAccidentReceptionInfoForTPA")
    String saveAccidentReceptionInfoForTPA(@RequestBody GlobalRequestDto globalRequestDto);

    //global报案回流接口
    @PostMapping("/GC_AcceptInterface.do?method=saveAcceptanceInfoForTPA")
    String saveAcceptanceInfoForTPA(@RequestBody GlobalRequestDto globalRequestDto);

    //global理算回流接口
    @PostMapping("/GC_AcceptInterface.do?method=saveDetailInfoForNewClaim")
    String saveDetailInfoForTPA(@RequestBody GlobalRequestDto globalRequestDto);

    //global报案回流接口
    @PostMapping("/GC_AcceptInterface.do?method=saveAcceptanceInfoForKFGZ")
    String saveAcceptanceInfoForKFGZ(@RequestBody GlobalRequestDto globalRequestDto);

    //global结案重开接口
    @PostMapping("/GC_AcceptInterface.do?method=openApiClaimClose")
    String openApiClaimClose(@RequestBody GlobalRequestDto globalRequestDto);

    //global公估接口
    @PostMapping("/GC_AcceptInterface.do?method=saveAppointmentInfoForTPA")
    String saveAppointmentInfoForTPA(@RequestBody GlobalRequestDto globalRequestDto);

    //global标的查询接口
    @PostMapping("/GC_AcceptInterface.do?method=openApiFindPolicyObjects")
    String openApiFindPolicyObjects(@RequestBody GlobalRequestDto globalRequestDto);
}
