package com.paic.ncbs.claim.dao.mapper.coinsurance;

import com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 共保摊回主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
public interface RecoveryRecordMapper extends BaseMapper<RecoveryRecord> {

    RecoveryRecord selectByIdRecovery(@Param("idRecoveryRecord") String idRecoveryRecord);

    RecoveryRecord selectByBatchNo(@Param("batchNo") String batchNo);
}
