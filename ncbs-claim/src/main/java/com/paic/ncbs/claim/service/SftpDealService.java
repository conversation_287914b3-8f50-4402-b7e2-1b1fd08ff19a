package com.paic.ncbs.claim.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ssh.Sftp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.base.constant.Constants;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.config.XieZhuSftpConfig;
import com.paic.ncbs.claim.dao.entity.sync.ClmFileSyncEntity;
import com.paic.ncbs.claim.dao.mapper.sync.ClmFileSyncMapper;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.vo.fileupolad.DocumentTypeVO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.file.model.FileResponse;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.paic.ncbs.claim.common.util.DateUtils.DATE_FORMAT_YYYYMMDD;

@Service
@Slf4j
public class SftpDealService {

    @Autowired
    private ClmFileSyncMapper clmFileSyncMapper;

    @Autowired
    private XieZhuSftpConfig xieZhuSftpConfig;

    @Autowired
    private FileUploadService fileUploadService;

//    @Autowired
//    private FtpServerOpenApi ftpServerOpenApi;

    @Autowired
    private FileCommonService fileCommonService;

    public void execute(Integer number) throws IOException {

        if (null == number || 0 == number) {
            number = xieZhuSftpConfig.getFrequency();
        }

        List<ClmFileSyncEntity> caseList = clmFileSyncMapper.getCaseList(number);
        if (CollectionUtils.isEmpty(caseList)) {
            log.warn("同步协筑案件查询为空");
            return;
        }
        for (ClmFileSyncEntity clmFileSyncEntity : caseList) {
            FileInfoDTO fileInfoDTO = new FileInfoDTO();
            String reportNo = clmFileSyncEntity.getReportNo();
            fileInfoDTO.setReportNo(reportNo);
            fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
            LogUtil.audit("查看单证reportNo={}", fileInfoDTO.getReportNo());
            // 获取文件列表
            List<FileInfoVO> fileUploadVOList = fileUploadService.getDocumentList("", fileInfoDTO);
            if (CollectionUtils.isEmpty(fileUploadVOList)) {
                log.warn("同步协筑sftp未查询到案件影像fileUploadVOList，案件号：{}", clmFileSyncEntity.getReportNo());
                continue;
            }
            List<String> fileUrlList = new ArrayList<>();

            for (FileInfoVO fileInfoVO : fileUploadVOList) {
                List<DocumentTypeVO> smallTypeList = fileInfoVO.getSmallTypeList();
                if (CollectionUtils.isEmpty(smallTypeList)) {
                    log.warn("同步协筑sftp未查询到案件影像smallTypeList，案件号：{}", clmFileSyncEntity.getReportNo());
                    continue;
                }
                for (DocumentTypeVO documentTypeVO : smallTypeList) {
                    List<FileInfoDTO> documentList = documentTypeVO.getDocumentList();
                    if (CollectionUtils.isEmpty(documentList)) {
                        log.warn("同步协筑sftp未查询到案件影像documentList，案件号：{}", clmFileSyncEntity.getReportNo());
                        continue;
                    }
                    for (FileInfoDTO infoDTO : documentList) {
                        if (StringUtils.isNotEmpty(infoDTO.getFileUrl())) {
                            fileUrlList.add(infoDTO.getFileUrl());
                        }
                    }
                }
            }
            if(CollectionUtils.isEmpty(fileUrlList)){
                log.warn("同步协筑sftp未查询到案件影像fileUrlList，案件号：{}", clmFileSyncEntity.getReportNo());
                continue;
            }
            log.info("fileUrlList:{}", JsonUtils.toJsonString(fileUrlList));
            File zipFile = new File(reportNo + ".zip");
            try {
                createZipFile(fileUrlList, zipFile);
                uploadZipToSftp(zipFile, xieZhuSftpConfig.getHost(), xieZhuSftpConfig.getPort(), xieZhuSftpConfig.getUser(), xieZhuSftpConfig.getPass(), xieZhuSftpConfig.getRemotePath(), String.valueOf(fileUrlList.size()), reportNo);
            }catch (Exception e){
                log.error("上传协筑sftp异常，reportNo:{}", reportNo, e);
                continue;
            }
            clmFileSyncEntity.setSyncTime(new Date());
            clmFileSyncEntity.setSyncStatus("1");
            clmFileSyncMapper.updateClmFileSync(clmFileSyncEntity);
        }

        // 可以写入参数
//        JSONArray jsonArray = getParameters();
//        for (int m = 0; m < jsonArray.size(); m++) {
//            JSONObject tempParameters = (JSONObject)jsonArray.get(m);
//            String result =  httpPost(tempParameters.toJSONString());
//            JSONObject jSONObject = JSONObject.parseObject(result);
//            String resultCode = jSONObject.getString("code");
//            if("000000".equals(resultCode)){
//                JSONArray dataArr = jSONObject.getJSONArray("data");
//
//                for(int i=0;i<dataArr.size();i++) {
//                    JSONObject js = (JSONObject) dataArr.get(i);
//                    String flowName = js.getString("flowName");
//                    String flowType = js.getString("flowType");
//                    String documentGroupId = js.getString("documentGroupId");
//                    JSONArray  smallTypeList = js.getJSONArray("smallTypeList");
//                    System.out.println("flowName:"+flowName+",smallTypeList:"+smallTypeList.size());
//                    for (int j = 0; j < smallTypeList.size(); j++) {
//                        JSONObject smallTypeOb = (JSONObject) smallTypeList.get(j);
//                        JSONArray documentList = smallTypeOb.getJSONArray("documentList");
//                        if(documentList == null || documentList.isEmpty()){
//                            continue;
//                        }
//                        String smallCode = smallTypeOb.getString("smallCode");
//                        String smallName = smallTypeOb.getString("smallName");
//                        for (int k = 0; k < documentList.size(); k++) {
//                            JSONObject documentOb = (JSONObject) documentList.get(k);
//                            String fileUrl = documentOb.getString("fileUrl");
//                            String fileName = documentOb.getString("fileName");
//                            System.out.println("documentGroupId:" + documentGroupId);
//                            System.out.println("smallCode:" + smallCode);
//                            System.out.println("fileName:" + fileName);
//                            System.out.println("fileUrl:" + fileUrl);
//                            System.out.println("============");
//                            String filePath =
//                                    documentGroupId + File.separator +  flowName + File.separator + smallName ;
//                            // dowloadFile(filePath,fileName,"https://img2.baidu.com/it/u=3227619927," +
//                            //         "365499885&fm=253&fmt=auto&app=120&f=JPEG?w=938&h=500");
////                            dowloadFile(filePath,fileName,fileUrl);
//                            fileUrlList.add(fileUrl);
//                        }
//                    }
//                }
//                File zipFile = new File(tempParameters.getString("reportNo") + ".zip");
//                createZipFile(fileUrlList, zipFile);
//                uploadZipToSftp(zipFile,"**************",51133,"SANXING","SANXING123!","/", String.valueOf(fileUrlList.size()),tempParameters.getString("reportNo"));
//            }
//        }

    }

    public static void createZipFile(List<String> imagePaths, File zipFile) throws Exception {
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(Files.newOutputStream(zipFile.toPath()))) {
            for (String imagePath : imagePaths) {
                String fileName = imagePath.substring(imagePath.lastIndexOf('/') + 1, imagePath.lastIndexOf('?'));
                URL url = new URL(imagePath);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                // 设置连接超时时间
                connection.setConnectTimeout(5000);
                // 设置读取超时时间
                connection.setReadTimeout(5000);
//                if (imageFile.exists()) {
                    try (InputStream inputStream = connection.getInputStream()) {
                        ZipEntry zipEntry = new ZipEntry(fileName);
                        zipOutputStream.putNextEntry(zipEntry);
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = inputStream.read(buffer)) > 0) {
                            zipOutputStream.write(buffer, 0, length);
                        }
                        zipOutputStream.closeEntry();
                    }
//                }
            }
        }
    }

    public static void dowloadFile(String filePath, String fileName, String fileUrl) {
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置连接超时时间
            connection.setConnectTimeout(5000);
            // 设置读取超时时间
            connection.setReadTimeout(5000);
            // 获取文件大小
            // int fileSize = connection.getContentLength();
            // 创建一个缓冲区
            //BufferedReader inputStream = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            // 打开文件
            filePath = "D:/tempdown/" + filePath;
            File folder = new File(filePath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            try (InputStream inputStream = connection.getInputStream();
                 OutputStream out = Files.newOutputStream(Paths.get(folder.getPath() + "\\" + fileName))) {
                // 将缓冲区中的数据写入文件
                byte[] buffer = new byte[2048];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 关闭连接
            connection.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String httpPost(String postParameters) {
        String result = null;
        try {
            // STG
//            URL url = new URL("http://ncbs-claim.lb.ssdev.com:48915/claim/doc/app/fileUploadAction/getDocumentList");
            URL url = new URL("http://ncbs-claim.lb.ssprd.com:48915/claim/doc/app/fileUploadAction/getDocumentList");
            // URL url = new URL("http://100.0.167.34:48913/claim/doc/app/fileUploadAction/getDocumentList");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法，可以是GET、POST等
            connection.setRequestMethod("POST");
            // 设置请求头，例如自定义的头部信息
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Connection", "keep-alive");
            connection.addRequestProperty("Content-Type", "application/json;charset=utf-8");
            connection.addRequestProperty("authType", "C");

            // 发送POST请求必须设置如下两行
            connection.setDoOutput(true);
            connection.setDoInput(true);

            byte[] outputInBytes = postParameters.getBytes("UTF-8");
            OutputStream os = connection.getOutputStream();
            os.write(outputInBytes);
            os.close();

            // 发起请求
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            // 读取响应内容
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            result = response.toString();
            System.out.println("Response Body: " + result);

            // 关闭连接
            connection.disconnect();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    private static JSONArray getParameters() {
        String postParameters = "[{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000024209\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000026574\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000030946\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000033595\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000034503\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000037980\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000038649\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000039089\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000039150\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040029\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040688\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041936\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042948\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000043160\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000043592\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000044421\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045026\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045836\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000047015\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000047056\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049295\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049595\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052989\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000057518\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000058330\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000059254\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000059685\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000059779\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063690\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063947\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063959\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065001\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065279\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000066057\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000068652\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000068744\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000071150\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000072532\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000072901\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000080151\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000087037\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000092967\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000023231\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000026231\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000034010\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000036030\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000037198\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000039997\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040535\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042262\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042838\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000043245\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000043267\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045524\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000046578\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000047522\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000048126\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000048221\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000050738\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052439\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000054593\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000056613\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000059135\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000059586\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000059599\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063502\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065014\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065725\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000066345\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000067252\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000071130\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000074034\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000077011\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000023683\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000028530\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000029298\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000039602\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042116\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042276\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000044705\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000046674\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000047133\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049446\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051567\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000057017\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000057796\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000058009\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000059208\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061021\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061244\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061993\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000067648\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069207\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069632\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000071043\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000074700\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000083199\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000083950\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000088938\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000023208\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000028500\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000029331\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000030374\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000032057\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000033644\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040420\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041247\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041248\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041675\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041783\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042176\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000044680\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045281\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000046673\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000046676\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000047292\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000048046\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000048167\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049315\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000050763\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051392\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051721\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052244\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052784\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000054674\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000055251\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000056124\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000059986\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061148\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061232\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062011\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063725\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000067216\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069210\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000071905\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000072001\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000072036\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000078563\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000080886\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000029330\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000030077\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000031110\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000034805\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000036618\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000038454\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000039665\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040966\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042685\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042782\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000046665\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000047117\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000048729\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000050370\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000050813\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051975\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052187\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052215\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052991\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000056696\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000056756\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000056796\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000057005\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000057341\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000060024\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000060026\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000060531\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061259\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062644\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063021\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063039\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069324\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069712\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000073023\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000074724\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000074865\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000075938\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000077171\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000082630\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000086962\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000030229\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000038876\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040120\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040928\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040991\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041329\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042317\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000043506\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049547\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049667\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051072\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051139\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051544\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051678\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052119\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061229\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061255\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062223\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062450\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062461\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062963\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063014\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065277\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065802\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000068756\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069206\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069659\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069663\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000069752\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000070065\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000070578\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000070884\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000081628\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000081881\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000088863\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000090774\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000091347\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000035862\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000039224\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000040639\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042258\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042775\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045028\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045397\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045982\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000047937\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000050430\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000050814\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051640\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000052466\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000058855\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061137\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063503\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065927\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000072798\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000073496\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000075453\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000075559\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000075579\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000076022\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000090818\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000025188\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000029935\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000031008\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000031109\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000034558\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000036626\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041707\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042146\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000042387\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000043076\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000046760\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000048967\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049260\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049399\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049781\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000053259\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000055102\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000055152\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000055173\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000055730\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000056281\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000057890\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000057982\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061245\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000061601\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062151\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062524\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000062665\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065616\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000066344\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000070062\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000070692\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000073779\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000076755\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000077496\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000082565\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000083146\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000084883\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000086114\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000023679\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000024255\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000030225\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000031339\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000031633\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000037207\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000039726\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041357\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000041800\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000044774\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045080\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000045297\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000046553\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000046671\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000047341\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000048523\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049102\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049374\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000049675\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000051085\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000054572\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000054640\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000060056\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063256\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000063467\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000065188\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000068593\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000073866\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000079236\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000081683\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000086221\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000087788\"},{\"caseTimes\":\"1\",\"reportNo\":\"98080000000000091880\"}]";
        return JSONArray.parseArray(postParameters);
    }

    public void uploadZipToSftp(File zipFile, String host, int port, String user, String pass, String remotePath, String count, String reportNo) throws Exception {
        String fileId = UUID.randomUUID().toString();
        FileResponse fileResponse = fileCommonService.upload(zipFile, fileId,zipFile.getName());
        if (!Constants.SUCCESS.equals(fileResponse.getCode())) {
            log.error("zipFile 文件上传cos异常，fileResponse信息为：{},reportNo:{}", JSONObject.toJSONString(fileResponse), reportNo);
            throw new Exception("文件上传异常");
        }
        String filePath = remotePath + DateUtils.dateFormat(new Date(), DATE_FORMAT_YYYYMMDD);
//        FileDownloadReqVO fileDownloadReqVO = new FileDownloadReqVO();
//        FtpServerReqVO.FtpDTO ftpDTO = new FtpServerReqVO.FtpDTO();
//        ftpDTO.setFtpUrl(filePath);
//        ftpDTO.setFtpServerUrl(host);
//        ftpDTO.setFtpServerPort(port);
//        ftpDTO.setFtpUserName(user);
//        ftpDTO.setFtpUserPassWord(pass);
//        ftpDTO.setFtpTempPatch("/temp/claim");
//        fileDownloadReqVO.setFtpDTO(ftpDTO);
//
//        SourceInfoVO sourceInfoVO = new SourceInfoVO();
//        sourceInfoVO.setSourceFileUrl(fileResponse.getUrl());
//        sourceInfoVO.setSourceFileName(zipFile.getName());
//        sourceInfoVO.setSourceFileDownloadFlag("cos");
//        fileDownloadReqVO.setSourceInfo(sourceInfoVO);
//        FtpServerRespVO ftpServerRespVO = ftpServerOpenApi.sftpFileUpload(fileDownloadReqVO);
//        if(!"000000".equals(ftpServerRespVO.getResultCode())){
//            log.error("zipFile 文件上传sftp异常，ftpServerRespVO信息为：{},reportNo:{}", JSONObject.toJSONString(ftpServerRespVO), reportNo);
//            throw new Exception("文件上传异常");
//        }

        InputStream inputStream = new ByteArrayInputStream(count.getBytes());
        File outputFile = new File(reportNo + ".txt");
        convertInputStreamToFile(inputStream, outputFile);
        FileResponse fileResponseTxt = fileCommonService.upload(outputFile, fileId, outputFile.getName());
        if (!Constants.SUCCESS.equals(fileResponseTxt.getCode())) {
            log.error("txtFile 文件上传cos异常，fileResponse信息为：{},reportNo:{}", JSONObject.toJSONString(fileResponseTxt), reportNo);
            throw new Exception("文件上传异常");
        }
//        sourceInfoVO.setSourceFileUrl(fileResponseTxt.getUrl());
//        sourceInfoVO.setSourceFileName(outputFile.getName());
//        FtpServerRespVO ftpServerRespVOTxt = ftpServerOpenApi.sftpFileUpload(fileDownloadReqVO);
//        if(!"000000".equals(ftpServerRespVO.getResultCode())){
//            log.error("txtFile 文件上传sftp异常，ftpServerRespVOTxt信息为：{},reportNo:{}", JSONObject.toJSONString(ftpServerRespVOTxt), reportNo);
//            throw new Exception("文件上传异常");
//        }

//        try (Sftp sftp = new Sftp(host, port, user, pass)) {
//            // 执行写入
//            String filePath = remotePath + DateUtils.dateFormat(new Date(), DATE_FORMAT_YYYYMMDD);
//            System.out.println(("************上传到FTP文件路径名称****************" + filePath));
//            // 执行写入
//            String fileName = zipFile.getName();
//            String dir = StrUtil.removeSuffix(filePath, fileName);
//            try {
//                sftp.mkDirs(dir);
//                System.out.println(("SFTP创建目录成功，目录dir:" + dir));
//            } catch (Exception e) {
//                System.out.println(("SFTP创建目录失败，目录dir:" + dir + ",异常：" + e.getMessage()));
//            }
//            sftp.upload(filePath, zipFile);
//
//            InputStream inputStream = new ByteArrayInputStream(count.getBytes());
//            File outputFile = new File(reportNo + ".txt");
//            convertInputStreamToFile(inputStream, outputFile);
//            sftp.upload(filePath, outputFile);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    public static void convertInputStreamToFile(InputStream inputStream, File file) throws IOException {
        try (OutputStream outputStream = Files.newOutputStream(file.toPath())) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
        }
    }
}