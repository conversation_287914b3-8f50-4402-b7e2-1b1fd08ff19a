package com.paic.ncbs.claim.service.indicators.impl;

import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 沟通时效计算
 */
@Service("Indicator4communicate")
public class Indicator4communicate extends  ClmsCaseIndicatorServiceImpl{
    @Override
    protected void saveStablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.stable4communicate(lastLog, now);
    }

    @Override
    protected void resaveUnstablePart(ClmsCaseIndicatorLog lastLog, LocalDateTime now) {
        this.baseMapper.unstable4communicate(lastLog, now);
    }
}
