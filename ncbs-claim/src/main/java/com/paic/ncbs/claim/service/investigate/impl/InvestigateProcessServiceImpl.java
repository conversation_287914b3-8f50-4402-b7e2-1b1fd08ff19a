package com.paic.ncbs.claim.service.investigate.impl;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateProcessMapper;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.investigate.InvestigateProcessService;
import com.paic.ncbs.claim.service.report.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service("investigateProcessService")
public class InvestigateProcessServiceImpl implements InvestigateProcessService {

    @Autowired
    private InvestigateProcessMapper investigateProcessDao;

	@Autowired
	private ReportService reportService;

	@Autowired
	private AhcsCommonService ahcsCommonService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addInvestigateProcess(InvestigateProcessDTO investigateProcess) {
        investigateProcess.setIdAhcsInvestigateProcess(UuidUtil.getUUID());
        investigateProcessDao.addInvestigateProcess(investigateProcess);

    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addInvestigateProcessList(List<InvestigateProcessDTO> investigateProcessList, String userId) {

        for (InvestigateProcessDTO investigateProcess : investigateProcessList) {
        	investigateProcess.setCreatedBy(userId);
        	if (StringUtils.isEmptyStr(investigateProcess.getIdAhcsInvestigateProcess())) {
        		investigateProcess.setIdAhcsInvestigateProcess(UuidUtil.getUUID());
			}
        }
        ahcsCommonService.batchHandlerTransactionalWithArgs(InvestigateProcessMapper.class,
						investigateProcessList, 40,"addInvestigateProcessList");
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInvestigateProcessById(String idAhcsInvestigateProcess) {

        investigateProcessDao.deleteInvestigateProcessById(idAhcsInvestigateProcess);

    }


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveOrUpdateInvestigateProcess(InvestigateProcessDTO investigateProcess) throws GlobalBusinessException {

		if (StringUtils.isEmptyStr(investigateProcess.getCustomerName())) {
			String reportNo = investigateProcessDao.getReportNoByTaskId(investigateProcess.getIdAhcsInvestigateTask());

			JSONObject reportInfo = reportService.requestReportDomainInfo(reportNo);
			ReportCustomerInfoVO reportCustomerInfo = reportInfo.getObject("reportCustomerInfo", ReportCustomerInfoVO.class);
			String customerName = "";
			if (null != reportCustomerInfo){
				customerName = reportCustomerInfo.getName();
			}

			if (StringUtils.isEmptyStr(customerName)) {
				customerName = "异常";
			}
			investigateProcess.setCustomerName(customerName);
		}

		if (StringUtils.isEmptyStr(investigateProcess.getIdAhcsInvestigateProcess())) {

			investigateProcess.setIdAhcsInvestigateProcess(UuidUtil.getUUID());
	        investigateProcessDao.addInvestigateProcess(investigateProcess);
		} else {
			investigateProcessDao.modifyInvestigateProcess(investigateProcess);
		}
	}

}