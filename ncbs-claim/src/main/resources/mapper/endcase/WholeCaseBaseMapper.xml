<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity">
        <id column="ID_CLM_WHOLE_CASE_BASE" property="idClmWholeCaseBase" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="INDEMNITY_CONCLUSION" property="indemnityConclusion" jdbcType="VARCHAR"/>
        <result column="IS_AGENT_CASE" property="isAgentCase" jdbcType="VARCHAR"/>
        <result column="HUGE_ACCIDENT_CODE" property="hugeAccidentCode" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_GROUP_ID" property="documentGroupId" jdbcType="VARCHAR"/>
        <result column="WHOLE_CASE_STATUS" property="wholeCaseStatus" jdbcType="VARCHAR"/>
        <result column="MIGRATE_FROM" property="migrateFrom" jdbcType="VARCHAR"/>
        <result column="CASE_TIMES" property="caseTimes" jdbcType="DECIMAL"/>
        <result column="INDEMNITY_MODEL" property="indemnityModel" jdbcType="VARCHAR"/>
        <result column="ALLOW_QUICK_FINISH" property="allowQuickFinish" jdbcType="VARCHAR"/>
        <result column="END_CASE_DATE" property="endCaseDate" jdbcType="TIMESTAMP"/>
        <result column="REGISTER_DATE" property="registerDate" jdbcType="TIMESTAMP"/>
        <result column="IS_REGISTER" property="isRegister" jdbcType="VARCHAR"/>
        <result column="SETTLE_END_DATE" property="settleEndDate" jdbcType="TIMESTAMP"/>
        <result column="CASE_CANCEL_REASON" property="caseCancelReason" jdbcType="VARCHAR"/>
        <result column="CASE_FINISHER_UM" property="caseFinisherUm" jdbcType="VARCHAR"/>
        <result column="REGISTER_UM" property="registerUm" jdbcType="VARCHAR"/>
        <result column="SETTLER_UM" property="settlerUm" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_FULL_DATE" property="documentFullDate" jdbcType="TIMESTAMP"/>
        <result column="RECEIVE_VOUCHER_UM" property="receiveVoucherUm" jdbcType="VARCHAR"/>
        <result column="SETTLE_START_DATE" property="settleStartDate" jdbcType="TIMESTAMP"/>
        <result column="CASE_GUDE_PAGE_NO" property="caseGudePageNo" jdbcType="VARCHAR"/>
        <result column="IS_HUGE_ACCIDENT" property="isHugeAccident" jdbcType="VARCHAR"/>
        <result column="IS_IMPORTANT_CASE" property="isImportantCase" jdbcType="VARCHAR"/>
        <result column="REJECT_REASON_CODE" property="rejectReasonCode" jdbcType="VARCHAR"/>
        <result column="CANCEL_REASON_CODE" property="cancelReasonCode" jdbcType="VARCHAR"/>
        <result column="CASE_REJECT_REASON" property="caseRejectReason" jdbcType="VARCHAR"/>
        <result column="MEDIATION_EXPLAIN" property="mediationExplain" jdbcType="VARCHAR"/>
        <result column="ARCHIVE_DATE" property="archiveDate" jdbcType="TIMESTAMP"/>
        <result column="ARCHIVE_BY" property="archiveBy" jdbcType="VARCHAR"/>
        <result column="CASE_TYPE" property="caseType" jdbcType="VARCHAR"/>
        <result column="FIRST_MIND_DESC" property="firstMindDesc" jdbcType="VARCHAR"/>
        <result column="FIRST_AUDITING_USER" property="firstAuditingUser" jdbcType="VARCHAR"/>
        <result column="FIRST_AUDITING_DATE" property="firstAuditingDate" jdbcType="TIMESTAMP"/>
        <result column="AGENT_TYPE" property="agentType" jdbcType="VARCHAR"/>
        <result column="VERIFY_UM" property="verifyUm" jdbcType="VARCHAR"/>
        <result column="VERIFY_DATE" property="verifyDate" jdbcType="TIMESTAMP"/>
        <result column="END_CASE_NO" property="endCaseNo" jdbcType="VARCHAR"/>
        <result column="REGIST_NO" property="registNo" jdbcType="TIMESTAMP"/>
        <result column="injury_reason_code" property="injuryReasonCode" jdbcType="VARCHAR"/>
        <result column="case_identification" property="caseIdentification" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO" id="result">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_CLM_WHOLE_CASE_BASE" property="wholeCaseBaseId"/>
        <result column="WHOLE_CASE_STATUS" property="wholeCaseStatus"/>
        <result column="INDEMNITY_CONCLUSION" property="indemnityConclusion"/>
        <result column="INDEMNITY_MODEL" property="indemnityModel"/>
        <result column="IS_REGISTER" property="isRegister"/>
        <result column="REGISTER_DATE" property="registerDate"/>
        <result column="CASE_TYPE" property="caseType"/>
        <result column="REGISTER_UM" property="registerUm"/>
        <result column="RECEIVE_VOUCHER_UM" property="receiveVoucherUm"/>
        <result column="DOCUMENT_FULL_DATE" property="documentFullDate" />
        <result column="END_CASE_DATE" property="endCaseDate" />
        <result column="IS_HUGE_ACCIDENT" property="isHugeAccident" />
        <result column="END_CASE_DATE_STR" property="endCaseDateStr" />
        <result column="REGISTER_DATE_STR" property="registerDateStr"/>
        <result column="DOCUMENT_FULL_DATE_STR" property="documentFullDateStr" />
        <result column="SETTLE_END_DATE" property="settleEndDate" />
        <result column="VERIFY_DATE" property="verifyDate" />
        <result column="CASE_FINISHER_UM" property="caseFinisherUm" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="REPORT_DATE_STR" property="reportDateStr" />
        <result column="INSURED_NAME" property="insuredName" />
        <result column="migrate_from" property="migrateFrom" />
        <result column="is_person_trace" property="isPersonTrace"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO" id="caseStatusResult">
        <result column="WHOLE_CASE_STATUS" property="wholeCaseStatus"/>
        <result column="INDEMNITY_CONCLUSION" property="indemnityConclusion"/>
        <result column="INDEMNITY_MODEL" property="indemnityModel"/>
        <result column="REGISTER_DATE" property="registerDateStr"/>
        <result column="END_CASE_DATE" property="endCaseDateStr"/>
        <result column="PROCESS_STATUS" property="processStatus"/>

    </resultMap>


    <sql id="Base_Column_List">
        ID_CLM_WHOLE_CASE_BASE, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_NO,
        INDEMNITY_CONCLUSION, IS_AGENT_CASE, HUGE_ACCIDENT_CODE, DOCUMENT_GROUP_ID, WHOLE_CASE_STATUS,
        MIGRATE_FROM, CASE_TIMES, INDEMNITY_MODEL, ALLOW_QUICK_FINISH, END_CASE_DATE, REGISTER_DATE,
        IS_REGISTER, SETTLE_END_DATE, CASE_CANCEL_REASON, CASE_FINISHER_UM, REGISTER_UM,
        SETTLER_UM, DOCUMENT_FULL_DATE, RECEIVE_VOUCHER_UM, SETTLE_START_DATE, CASE_GUDE_PAGE_NO,
        IS_HUGE_ACCIDENT, IS_IMPORTANT_CASE, REJECT_REASON_CODE, CANCEL_REASON_CODE, CASE_REJECT_REASON,
        MEDIATION_EXPLAIN, ARCHIVE_DATE, ARCHIVE_BY, CASE_TYPE, FIRST_MIND_DESC, FIRST_AUDITING_USER,
        FIRST_AUDITING_DATE, AGENT_TYPE, VERIFY_UM, VERIFY_DATE,REGIST_NO,END_CASE_NO,injury_reason_code,case_identification
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_WHOLE_CASE_BASE
        where ID_CLM_WHOLE_CASE_BASE = #{idClmWholeCaseBase,jdbcType=VARCHAR}
    </select>
    <select id="getWholeCaseBaseByReport" resultType="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity" >
        select
        <include refid="Base_Column_List"/>
        from CLM_WHOLE_CASE_BASE
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>
    <select id="getWholeCaseBase" resultType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO" >
        select
        <include refid="Base_Column_List"/>
        , ( select  `DEPARTMENT_ABBR_NAME` DEPARTMENT_NAME  from  department_define  where DEPARTMENT_CODE =( select  distinct  `DEPARTMENT_CODE`  from  clms_policy_info
         where  `REPORT_NO`  =  #{reportNo,jdbcType=VARCHAR}  limit 1
            )) DEPARTMENT_NAME ,(	 select  `PROCESS_STATUS`   from   clms_case_process  where  `REPORT_NO`  =  #{reportNo,jdbcType=VARCHAR} and CASE_TIMES = #{caseTimes} limit 1  ) PROCESS_STATUS
        from CLM_WHOLE_CASE_BASE
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR} and CASE_TIMES = #{caseTimes,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLM_WHOLE_CASE_BASE
        where ID_CLM_WHOLE_CASE_BASE = #{idClmWholeCaseBase,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity">
        insert into CLM_WHOLE_CASE_BASE (ID_CLM_WHOLE_CASE_BASE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        INDEMNITY_CONCLUSION, IS_AGENT_CASE, HUGE_ACCIDENT_CODE,
        DOCUMENT_GROUP_ID, WHOLE_CASE_STATUS, MIGRATE_FROM,
        CASE_TIMES, INDEMNITY_MODEL, ALLOW_QUICK_FINISH,
        END_CASE_DATE, REGISTER_DATE, IS_REGISTER,
        SETTLE_END_DATE, CASE_CANCEL_REASON, CASE_FINISHER_UM,
        REGISTER_UM, SETTLER_UM, DOCUMENT_FULL_DATE,
        RECEIVE_VOUCHER_UM, SETTLE_START_DATE, CASE_GUDE_PAGE_NO,
        IS_HUGE_ACCIDENT, IS_IMPORTANT_CASE, REJECT_REASON_CODE,
        CANCEL_REASON_CODE, CASE_REJECT_REASON, MEDIATION_EXPLAIN,
        ARCHIVE_DATE, ARCHIVE_BY, CASE_TYPE,
        FIRST_MIND_DESC, FIRST_AUDITING_USER, FIRST_AUDITING_DATE,
        AGENT_TYPE, VERIFY_UM,
        VERIFY_DATE,ARCHIVE_DATE2,injury_reason_code,case_identification)
        values (#{idClmWholeCaseBase,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{reportNo,jdbcType=VARCHAR},
        #{indemnityConclusion,jdbcType=VARCHAR}, #{isAgentCase,jdbcType=VARCHAR}, #{hugeAccidentCode,jdbcType=VARCHAR},
        #{documentGroupId,jdbcType=VARCHAR}, #{wholeCaseStatus,jdbcType=VARCHAR}, #{migrateFrom,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=DECIMAL}, #{indemnityModel,jdbcType=VARCHAR}, #{allowQuickFinish,jdbcType=VARCHAR},
        #{endCaseDate,jdbcType=TIMESTAMP}, #{registerDate,jdbcType=TIMESTAMP}, #{isRegister,jdbcType=VARCHAR},
        #{settleEndDate,jdbcType=TIMESTAMP}, #{caseCancelReason,jdbcType=VARCHAR}, #{caseFinisherUm,jdbcType=VARCHAR},
        #{registerUm,jdbcType=VARCHAR}, #{settlerUm,jdbcType=VARCHAR}, #{documentFullDate,jdbcType=TIMESTAMP},
        #{receiveVoucherUm,jdbcType=VARCHAR}, #{settleStartDate,jdbcType=TIMESTAMP}, #{caseGudePageNo,jdbcType=VARCHAR},
        #{isHugeAccident,jdbcType=VARCHAR}, #{isImportantCase,jdbcType=VARCHAR}, #{rejectReasonCode,jdbcType=VARCHAR},
        #{cancelReasonCode,jdbcType=VARCHAR}, #{caseRejectReason,jdbcType=VARCHAR},
        #{mediationExplain,jdbcType=VARCHAR},
        #{archiveDate,jdbcType=TIMESTAMP}, #{archiveBy,jdbcType=VARCHAR}, #{caseType,jdbcType=VARCHAR},
        #{firstMindDesc,jdbcType=VARCHAR}, #{firstAuditingUser,jdbcType=VARCHAR},
        #{firstAuditingDate,jdbcType=TIMESTAMP},
        #{agentType,jdbcType=VARCHAR}, #{verifyUm,jdbcType=VARCHAR},
        #{verifyDate,jdbcType=TIMESTAMP},now(),#{injuryReasonCode,jdbcType=VARCHAR},#{caseIdentification,jdbcType=VARCHAR})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity">
        update CLM_WHOLE_CASE_BASE
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="indemnityConclusion != null">
                INDEMNITY_CONCLUSION = #{indemnityConclusion,jdbcType=VARCHAR},
            </if>
            <if test="isAgentCase != null">
                IS_AGENT_CASE = #{isAgentCase,jdbcType=VARCHAR},
            </if>
            <if test="hugeAccidentCode != null">
                HUGE_ACCIDENT_CODE = #{hugeAccidentCode,jdbcType=VARCHAR},
            </if>
            <if test="documentGroupId != null">
                DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR},
            </if>
            <if test="wholeCaseStatus != null">
                WHOLE_CASE_STATUS = #{wholeCaseStatus,jdbcType=VARCHAR},
            </if>
            <if test="migrateFrom != null">
                MIGRATE_FROM = #{migrateFrom,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
            </if>
            <if test="indemnityModel != null">
                INDEMNITY_MODEL = #{indemnityModel,jdbcType=VARCHAR},
            </if>
            <if test="allowQuickFinish != null">
                ALLOW_QUICK_FINISH = #{allowQuickFinish,jdbcType=VARCHAR},
            </if>
            <if test="endCaseDate != null">
                END_CASE_DATE = #{endCaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="registerDate != null">
                REGISTER_DATE = #{registerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="isRegister != null">
                IS_REGISTER = #{isRegister,jdbcType=VARCHAR},
            </if>
            <if test="settleEndDate != null">
                SETTLE_END_DATE = #{settleEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="caseCancelReason != null">
                CASE_CANCEL_REASON = #{caseCancelReason,jdbcType=VARCHAR},
            </if>
            <if test="caseFinisherUm != null">
                CASE_FINISHER_UM = #{caseFinisherUm,jdbcType=VARCHAR},
            </if>
            <if test="registerUm != null">
                REGISTER_UM = #{registerUm,jdbcType=VARCHAR},
            </if>
            <if test="settlerUm != null">
                SETTLER_UM = #{settlerUm,jdbcType=VARCHAR},
            </if>
            <if test="documentFullDate != null">
                DOCUMENT_FULL_DATE = #{documentFullDate,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveVoucherUm != null">
                RECEIVE_VOUCHER_UM = #{receiveVoucherUm,jdbcType=VARCHAR},
            </if>
            <if test="settleStartDate != null">
                SETTLE_START_DATE = #{settleStartDate,jdbcType=TIMESTAMP},
            </if>
            <if test="caseGudePageNo != null">
                CASE_GUDE_PAGE_NO = #{caseGudePageNo,jdbcType=VARCHAR},
            </if>
            <if test="isHugeAccident != null">
                IS_HUGE_ACCIDENT = #{isHugeAccident,jdbcType=VARCHAR},
            </if>
            <if test="isImportantCase != null">
                IS_IMPORTANT_CASE = #{isImportantCase,jdbcType=VARCHAR},
            </if>
            <if test="rejectReasonCode != null">
                REJECT_REASON_CODE = #{rejectReasonCode,jdbcType=VARCHAR},
            </if>
            <if test="cancelReasonCode != null">
                CANCEL_REASON_CODE = #{cancelReasonCode,jdbcType=VARCHAR},
            </if>
            <if test="caseRejectReason != null">
                CASE_REJECT_REASON = #{caseRejectReason,jdbcType=VARCHAR},
            </if>
            <if test="mediationExplain != null">
                MEDIATION_EXPLAIN = #{mediationExplain,jdbcType=VARCHAR},
            </if>
            <if test="archiveDate != null">
                ARCHIVE_DATE = #{archiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="archiveBy != null">
                ARCHIVE_BY = #{archiveBy,jdbcType=VARCHAR},
            </if>
            <if test="caseType != null">
                CASE_TYPE = #{caseType,jdbcType=VARCHAR},
            </if>
            <if test="firstMindDesc != null">
                FIRST_MIND_DESC = #{firstMindDesc,jdbcType=VARCHAR},
            </if>
            <if test="firstAuditingUser != null">
                FIRST_AUDITING_USER = #{firstAuditingUser,jdbcType=VARCHAR},
            </if>
            <if test="firstAuditingDate != null">
                FIRST_AUDITING_DATE = #{firstAuditingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="agentType != null">
                AGENT_TYPE = #{agentType,jdbcType=VARCHAR},
            </if>
            <if test="verifyUm != null">
                VERIFY_UM = #{verifyUm,jdbcType=VARCHAR},
            </if>
            <if test="verifyDate != null">
                VERIFY_DATE = #{verifyDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID_CLM_WHOLE_CASE_BASE = #{idClmWholeCaseBase,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity">
        update CLM_WHOLE_CASE_BASE
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        INDEMNITY_CONCLUSION = #{indemnityConclusion,jdbcType=VARCHAR},
        IS_AGENT_CASE = #{isAgentCase,jdbcType=VARCHAR},
        HUGE_ACCIDENT_CODE = #{hugeAccidentCode,jdbcType=VARCHAR},
        DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR},
        WHOLE_CASE_STATUS = #{wholeCaseStatus,jdbcType=VARCHAR},
        MIGRATE_FROM = #{migrateFrom,jdbcType=VARCHAR},
        CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
        INDEMNITY_MODEL = #{indemnityModel,jdbcType=VARCHAR},
        ALLOW_QUICK_FINISH = #{allowQuickFinish,jdbcType=VARCHAR},
        END_CASE_DATE = #{endCaseDate,jdbcType=TIMESTAMP},
        REGISTER_DATE = #{registerDate,jdbcType=TIMESTAMP},
        IS_REGISTER = #{isRegister,jdbcType=VARCHAR},
        SETTLE_END_DATE = #{settleEndDate,jdbcType=TIMESTAMP},
        CASE_CANCEL_REASON = #{caseCancelReason,jdbcType=VARCHAR},
        CASE_FINISHER_UM = #{caseFinisherUm,jdbcType=VARCHAR},
        REGISTER_UM = #{registerUm,jdbcType=VARCHAR},
        SETTLER_UM = #{settlerUm,jdbcType=VARCHAR},
        DOCUMENT_FULL_DATE = #{documentFullDate,jdbcType=TIMESTAMP},
        RECEIVE_VOUCHER_UM = #{receiveVoucherUm,jdbcType=VARCHAR},
        SETTLE_START_DATE = #{settleStartDate,jdbcType=TIMESTAMP},
        CASE_GUDE_PAGE_NO = #{caseGudePageNo,jdbcType=VARCHAR},
        IS_HUGE_ACCIDENT = #{isHugeAccident,jdbcType=VARCHAR},
        IS_IMPORTANT_CASE = #{isImportantCase,jdbcType=VARCHAR},
        REJECT_REASON_CODE = #{rejectReasonCode,jdbcType=VARCHAR},
        CANCEL_REASON_CODE = #{cancelReasonCode,jdbcType=VARCHAR},
        CASE_REJECT_REASON = #{caseRejectReason,jdbcType=VARCHAR},
        MEDIATION_EXPLAIN = #{mediationExplain,jdbcType=VARCHAR},
        ARCHIVE_DATE = #{archiveDate,jdbcType=TIMESTAMP},
        ARCHIVE_BY = #{archiveBy,jdbcType=VARCHAR},
        CASE_TYPE = #{caseType,jdbcType=VARCHAR},
        FIRST_MIND_DESC = #{firstMindDesc,jdbcType=VARCHAR},
        FIRST_AUDITING_USER = #{firstAuditingUser,jdbcType=VARCHAR},
        FIRST_AUDITING_DATE = #{firstAuditingDate,jdbcType=TIMESTAMP},
        AGENT_TYPE = #{agentType,jdbcType=VARCHAR},
        VERIFY_UM = #{verifyUm,jdbcType=VARCHAR},
        VERIFY_DATE = #{verifyDate,jdbcType=TIMESTAMP}
        where ID_CLM_WHOLE_CASE_BASE = #{idClmWholeCaseBase,jdbcType=VARCHAR}
    </update>
    <select id="getWholeCaseBaseByReportNoAndCaseTimes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLM_WHOLE_CASE_BASE
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
    </select>

    <select id="getClaimStatusByReportNos" resultType="java.lang.String" parameterType="java.lang.String">
        select b.report_no
        from clm_whole_case_base b
        where b.report_no in
        (
        <foreach collection="reportNos" item="reportNo" separator=",">
            #{reportNo,jdbcType=VARCHAR}
        </foreach>
        )
        and b.whole_case_status <![CDATA[!= ]]> '0'
    </select>

    <update id="modifyHugeAccident" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
        update
        CLM_WHOLE_CASE_BASE
        set UPDATED_BY=#{updatedBy},
        UPDATED_DATE=NOW(),
        HUGE_ACCIDENT_CODE=#{hugeAccidentCode},
        HUGE_ACCIDENT_ID=#{hugeAccidentId},
        IS_HUGE_ACCIDENT=#{isHugeAccident}
        where REPORT_NO=#{reportNo}
        and CASE_TIMES=#{caseTimes}
    </update>

    <select id="getWholeCaseBase2" resultMap="result" >
        select
        REPORT_NO,
        CASE_TIMES,
        ID_CLM_WHOLE_CASE_BASE,
        WHOLE_CASE_STATUS,
        INDEMNITY_CONCLUSION,
        INDEMNITY_MODEL,
        REGISTER_DATE,
        REGISTER_UM,
        IS_REGISTER,
        CASE_TYPE,
        RECEIVE_VOUCHER_UM,
        END_CASE_DATE,
        DOCUMENT_FULL_DATE,
        IS_HUGE_ACCIDENT,
        HUGE_ACCIDENT_ID,
        SETTLE_END_DATE,
        CASE_FINISHER_UM,
        VERIFY_DATE,CREATED_DATE,migrate_from,
        (SELECT date_format(ARCHIVE_TIME,'%Y-%m-%d %T') FROM CLMS_CASE_PROCESS P WHERE P.REPORT_NO = #{reportNo} and P.CASE_TIMES=#{caseTimes} limit 1) REPORT_DATE_STR,
        (SELECT NAME FROM CLMS_INSURED_PERSON PER WHERE PER.ID_AHCS_POLICY_INFO = (SELECT ID_AHCS_POLICY_INFO FROM CLMS_POLICY_INFO PI WHERE PI.REPORT_NO = #{reportNo} limit 1) limit 1) INSURED_NAME,
        is_person_trace
        from CLM_WHOLE_CASE_BASE where REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
    </select>


    <select id="getCaseTypeCode" resultType="string">
        select h.case_type
        from clm_whole_case_base h
        where h.report_no=#{reportNo}
        and h.case_times=#{caseTimes}
    </select>


    <update id="modifyWholeCaseBase" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
        update
        CLM_WHOLE_CASE_BASE
        <trim prefix="set" suffixOverrides=",">
            <if test="updatedBy != null">UPDATED_BY=#{updatedBy},</if>
            UPDATED_DATE=NOW(),
            <if test="indemnityConclusion != null">INDEMNITY_CONCLUSION=#{indemnityConclusion},</if>
            <if test="indemnityModel != null">INDEMNITY_MODEL=#{indemnityModel},</if>
            <if test="registerDate != null">REGISTER_DATE=#{registerDate},</if>
            <if test="registerUm != null">REGISTER_UM=#{registerUm},</if>
            <if test="isRegister != null">IS_REGISTER=#{isRegister},</if>
            <if test="documentFullDate != null">DOCUMENT_FULL_DATE=#{documentFullDate},</if>
            <if test="verifyUm != null">VERIFY_UM=#{verifyUm},</if>
            <if test="verifyUm != null">VERIFY_DATE=NOW(),</if>
            <if test="receiveVoucherUm != null">RECEIVE_VOUCHER_UM=#{receiveVoucherUm},</if>
            <if test="hugeAccidentCode != null">HUGE_ACCIDENT_CODE=#{hugeAccidentCode},</if>
            <if test="isHugeAccident != null">IS_HUGE_ACCIDENT=#{isHugeAccident},</if>
            <if test="caseType != null">CASE_TYPE=#{caseType},</if>
            <if test=" registNo!= null">regist_No=#{registNo},</if>
            <if test=" wholeCaseStatus!= null">WHOLE_CASE_STATUS=#{wholeCaseStatus},</if>
        </trim>
        where REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
    </update>

    <select id="getReportDate" resultType="java.util.Date" >
        select
        t.report_date
        from clm_report_info t
        where t.report_no =#{reportNo}
    </select>

    <select id="getWholeCaseStatus" resultType="java.lang.String">
        select wcb.WHOLE_CASE_STATUS
        from clm_whole_case_base wcb
        where wcb.REPORT_NO = #{reportNo} and wcb.CASE_TIMES = #{caseTimes}
    </select>

    <select id="getHugeInfo" resultType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO" >
        select
        IS_HUGE_ACCIDENT isHugeAccident,
        HUGE_ACCIDENT_CODE hugeAccidentCode,
        HUGE_ACCIDENT_ID hugeAccidentId
        from
        CLM_WHOLE_CASE_BASE
        where
        REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
    </select>

    <update id="modifyIsSelfHelp">
        UPDATE CLM_WHOLE_CASE_BASE_EX SET UPDATED_DATE=now(),UPDATED_BY=#{userId},
        IS_SELF_HELP=#{isSelfHelp} WHERE REPORT_NO=#{reportNo} AND CASE_TIMES=#{caseTimes}
    </update>

    <update id="modifyWholeCaseEndCaseNo">
        UPDATE   CLM_WHOLE_CASE_BASE SET END_CASE_NO =#{endCaseNo}
        WHERE  REPORT_NO = #{reportNo}   and CASE_TIMES = #{caseTimes}
    </update>

    <select id="getWholeCaseIndemnityStatus" resultMap="caseStatusResult">
        select wcb.WHOLE_CASE_STATUS,
               wcb.INDEMNITY_CONCLUSION,
               wcb.INDEMNITY_MODEL,
               date_format(wcb.END_CASE_DATE, '%Y-%m-%d %H:%i:%s') END_CASE_DATE,
               date_format(wcb.REGISTER_DATE, '%Y-%m-%d %H:%i:%s') REGISTER_DATE,
               cp.PROCESS_STATUS
        from CLM_WHOLE_CASE_BASE wcb,
             CLMS_CASE_PROCESS cp
        where cp.REPORT_NO = wcb.REPORT_NO
          and cp.CASE_TIMES = wcb.CASE_TIMES
          and wcb.REPORT_NO = #{reportNo}
          and wcb.CASE_TIMES = #{caseTimes}
    </select>

    <select id="getWholeCaseIndemnityStatusZt" resultMap="caseStatusResult">
        select wcb.WHOLE_CASE_STATUS,
               wcb.INDEMNITY_CONCLUSION,
               wcb.INDEMNITY_MODEL,
               date_format(wcb.END_CASE_DATE, '%Y-%m-%d %H:%i:%s') END_CASE_DATE,
               date_format(wcb.REGISTER_DATE, '%Y-%m-%d %H:%i:%s') REGISTER_DATE
        from CLM_WHOLE_CASE_BASE wcb
        where wcb.REPORT_NO = #{reportNo}
          and wcb.CASE_TIMES = #{caseTimes}
    </select>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLM_WHOLE_CASE_BASE (ID_CLM_WHOLE_CASE_BASE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        INDEMNITY_CONCLUSION, IS_AGENT_CASE, HUGE_ACCIDENT_CODE,
        DOCUMENT_GROUP_ID, WHOLE_CASE_STATUS, MIGRATE_FROM,
        CASE_TIMES, INDEMNITY_MODEL, ALLOW_QUICK_FINISH,
        END_CASE_DATE, REGISTER_DATE, IS_REGISTER,
        SETTLE_END_DATE, CASE_CANCEL_REASON, CASE_FINISHER_UM,
        REGISTER_UM, SETTLER_UM, DOCUMENT_FULL_DATE,
        RECEIVE_VOUCHER_UM, SETTLE_START_DATE, CASE_GUDE_PAGE_NO,
        IS_HUGE_ACCIDENT, IS_IMPORTANT_CASE, REJECT_REASON_CODE,
        CANCEL_REASON_CODE, CASE_REJECT_REASON, MEDIATION_EXPLAIN,
        ARCHIVE_DATE, ARCHIVE_BY, CASE_TYPE,
        FIRST_MIND_DESC, FIRST_AUDITING_USER, FIRST_AUDITING_DATE,
        AGENT_TYPE, VERIFY_UM,
        VERIFY_DATE,ARCHIVE_DATE2,case_identification)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idClmWholeCaseBase,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP}, #{item.reportNo,jdbcType=VARCHAR},
            #{item.indemnityConclusion,jdbcType=VARCHAR}, #{item.isAgentCase,jdbcType=VARCHAR}, #{item.hugeAccidentCode,jdbcType=VARCHAR},
            #{item.documentGroupId,jdbcType=VARCHAR}, #{item.wholeCaseStatus,jdbcType=VARCHAR}, #{item.migrateFrom,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=DECIMAL}, #{item.indemnityModel,jdbcType=VARCHAR}, #{item.allowQuickFinish,jdbcType=VARCHAR},
            #{item.endCaseDate,jdbcType=TIMESTAMP}, #{item.registerDate,jdbcType=TIMESTAMP}, #{item.isRegister,jdbcType=VARCHAR},
            #{item.settleEndDate,jdbcType=TIMESTAMP}, #{item.caseCancelReason,jdbcType=VARCHAR}, #{item.caseFinisherUm,jdbcType=VARCHAR},
            #{item.registerUm,jdbcType=VARCHAR}, #{item.settlerUm,jdbcType=VARCHAR}, #{item.documentFullDate,jdbcType=TIMESTAMP},
            #{item.receiveVoucherUm,jdbcType=VARCHAR}, #{item.settleStartDate,jdbcType=TIMESTAMP}, #{item.caseGudePageNo,jdbcType=VARCHAR},
            #{item.isHugeAccident,jdbcType=VARCHAR}, #{item.isImportantCase,jdbcType=VARCHAR}, #{item.rejectReasonCode,jdbcType=VARCHAR},
            #{item.cancelReasonCode,jdbcType=VARCHAR}, #{item.caseRejectReason,jdbcType=VARCHAR},
            #{item.mediationExplain,jdbcType=VARCHAR},
            #{item.archiveDate,jdbcType=TIMESTAMP}, #{item.archiveBy,jdbcType=VARCHAR}, #{item.caseType,jdbcType=VARCHAR},
            #{item.firstMindDesc,jdbcType=VARCHAR}, #{item.firstAuditingUser,jdbcType=VARCHAR},
            #{item.firstAuditingDate,jdbcType=TIMESTAMP},
            #{item.agentType,jdbcType=VARCHAR}, #{item.verifyUm,jdbcType=VARCHAR},
            #{item.verifyDate,jdbcType=TIMESTAMP},now(),#{caseIdentification,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLM_WHOLE_CASE_BASE (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_CLM_WHOLE_CASE_BASE,
            REPORT_NO,
            CASE_TIMES,
            WHOLE_CASE_STATUS,
            INDEMNITY_CONCLUSION,
            INDEMNITY_MODEL,
            ALLOW_QUICK_FINISH,
            IS_AGENT_CASE,
            IS_REGISTER,
            REGISTER_UM,
            REGISTER_DATE,
            DOCUMENT_FULL_DATE,
            HUGE_ACCIDENT_CODE,
            DOCUMENT_GROUP_ID,
            MIGRATE_FROM,
            RECEIVE_VOUCHER_UM,
            CASE_GUDE_PAGE_NO,
            MEDIATION_EXPLAIN,
            IS_HUGE_ACCIDENT,
            IS_IMPORTANT_CASE,
            CASE_TYPE,
            FIRST_MIND_DESC,
            FIRST_AUDITING_USER,
            FIRST_AUDITING_DATE,
            AGENT_TYPE,
            HUGE_ACCIDENT_ID,
            ARCHIVE_DATE2,
            REGIST_NO,
            INJURY_REASON_CODE,
            case_identification
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            #{idClmWholeCaseBase},
            REPORT_NO,
            #{reopenCaseTimes},
            #{caseStatus},
            #{indemnityConclusion},
            INDEMNITY_MODEL,
            ALLOW_QUICK_FINISH,
            IS_AGENT_CASE,
            IS_REGISTER,
            REGISTER_UM,
            REGISTER_DATE,
            NOW(),
            HUGE_ACCIDENT_CODE,
            DOCUMENT_GROUP_ID,
            MIGRATE_FROM,
            RECEIVE_VOUCHER_UM,
            CASE_GUDE_PAGE_NO,
            MEDIATION_EXPLAIN,
            IS_HUGE_ACCIDENT,
            IS_IMPORTANT_CASE,
            CASE_TYPE,
            FIRST_MIND_DESC,
            FIRST_AUDITING_USER,
            FIRST_AUDITING_DATE,
            AGENT_TYPE,
            HUGE_ACCIDENT_ID,
            NOW(),
            REGIST_NO,
            INJURY_REASON_CODE,
            case_identification
        FROM CLM_WHOLE_CASE_BASE
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
    </insert>
    <select id="getCaseTimes" parameterType="java.lang.String" resultType="java.lang.Integer" >
        select CASE_TIMES from clm_whole_case_base cwcb
        where REPORT_NO =#{reportNo}
        order by CASE_TIMES desc limit 1
    </select>
    <select id="getCusWholeCaseInfo" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
        select WHOLE_CASE_STATUS wholeCaseStatus,
        INDEMNITY_CONCLUSION indemnityConclusion,
        END_CASE_DATE endCaseDate,
        case_times caseTimes
        from clm_whole_case_base
        where report_no=#{reportNo}
        ORDER BY case_times desc limit 1
    </select>

    <select id="getWholeCaseBaseByPendPay" resultMap="result">
        SELECT DISTINCT
            W.REPORT_NO,
            W.CASE_TIMES,
            W.ID_CLM_WHOLE_CASE_BASE,
            W.WHOLE_CASE_STATUS,
            W.INDEMNITY_CONCLUSION,
            W.INDEMNITY_MODEL,
            W.REGISTER_DATE,
            W.REGISTER_UM,
            W.IS_REGISTER,
            W.CASE_TYPE,
            W.RECEIVE_VOUCHER_UM,
            W.END_CASE_DATE,
            W.DOCUMENT_FULL_DATE,
            W.IS_HUGE_ACCIDENT,
            W.HUGE_ACCIDENT_ID,
            W.SETTLE_END_DATE,
            W.CASE_FINISHER_UM,
            W.VERIFY_DATE,
            W.CREATED_DATE,
            W.MIGRATE_FROM
        FROM CLM_WHOLE_CASE_BASE W
                 JOIN CLMS_BATCH_AUTO_CLOSE B ON W.REPORT_NO = B.REPORT_NO
                 JOIN CLM_PAYMENT_ITEM PI ON W.REPORT_NO = PI.REPORT_NO
            AND W.CASE_TIMES = PI.CASE_TIMES
        WHERE PI.PAYMENT_ITEM_STATUS = '10'
          AND B.THIRD_BATCH_NO IN ('20240301', '20240229');
    </select>
    <select id="getverfiyCodeCount" resultType="java.lang.Integer">
        select count(*) from clm_whole_case_base cwcb ,
        clm_payment_item cpi
        where cwcb.REPORT_NO =cpi.REPORT_NO
        and cwcb.CASE_TIMES =cpi.CASE_TIMES
        and cwcb.REPORT_NO = #{reportNo}
        and cwcb.CASE_TIMES <![CDATA[ < ]]> #{caseTimes} and cwcb.INDEMNITY_CONCLUSION ='1'
        and cpi.PAYMENT_AMOUNT <![CDATA[ >= ]]> 0
    </select>
    <select id="selectGlobalSettle" resultType="com.paic.ncbs.claim.model.dto.copypolicy.GlobalSettleDto">
        select INDEMNITY_CONCLUSION terCls,
                rc00_rciv_year fkRc00RcivYear,
                rc00_rciv_seq_num fkRc00RcivSeqNum,
                ac00_rciv_date fkRcivDate,
                ac00_rciv_seq_num fkRcivSeqNum,
                agrm_seq_num fkAgrmSeqNum
                from clm_whole_case_base cwcb,clm_global_return_info cgri
                where cgri.report_no = cwcb.REPORT_NO and cwcb.REPORT_NO = #{reportNo} and cwcb.CASE_TIMES = #{caseTimes};
    </select>
    <update id="updateDocFullDate" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
        update CLM_WHOLE_CASE_BASE
        set DOCUMENT_FULL_DATE=#{documentFullDate},
        UPDATED_DATE=now()
        where REPORT_NO=#{reportNo}
        and CASE_TIMES=#{caseTimes}
    </update>
    <update id="updateIsPersonTrace">
        update CLM_WHOLE_CASE_BASE
        set IS_PERSON_TRACE=#{isPersonTrace},
        UPDATED_DATE=now()
        where REPORT_NO= #{reportNo}
        and CASE_TIMES= #{caseTimes}
    </update>
    <update id="updateCaseStatus" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
        update CLM_WHOLE_CASE_BASE
        set whole_case_status=#{wholeCaseStatus},
        UPDATED_DATE=now(),
        END_CASE_DATE=now(),
        end_case_no= #{endCaseNo}
        where REPORT_NO= #{reportNo}
        and CASE_TIMES= #{caseTimes}
    </update>
</mapper>