<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity">
        <id column="DEPARTMENT_CODE" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="DEPARTMENT_CHINESE_NAME" property="departmentChineseName"
                jdbcType="VARCHAR"/>
        <result column="DEPARTMENT_ABBR_NAME" property="departmentAbbrName"
                jdbcType="VARCHAR"/>
        <result column="DEPARTMENT_LEVEL" property="departmentLevel"
                jdbcType="DECIMAL"/>
        <result column="FOUND_DATE" property="foundDate" jdbcType="TIMESTAMP"/>
        <result column="CHINESE_ADDRESS" property="chineseAddress"
                jdbcType="VARCHAR"/>
        <result column="POSTCODE" property="postcode" jdbcType="VARCHAR"/>
        <result column="DEPARTMENT_ENGLISH_NAME" property="departmentEnglishName"
                jdbcType="VARCHAR"/>
        <result column="ENGLISH_ADDRESS" property="englishAddress"
                jdbcType="VARCHAR"/>
        <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR"/>
        <result column="FAX" property="fax" jdbcType="VARCHAR"/>
        <result column="UPPER_DEPARTMENT_CODE" property="upperDepartmentCode"
                jdbcType="VARCHAR"/>
        <result column="LINK_MAN_CODE" property="linkManCode" jdbcType="VARCHAR"/>
        <result column="REPORT_BEFORE_REGISTER" property="reportBeforeRegister"
                jdbcType="CHAR"/>
        <result column="INVALIDATE_DATE" property="invalidateDate"
                jdbcType="TIMESTAMP"/>
        <result column="DEPARTMENT_MARK" property="departmentMark"
                jdbcType="VARCHAR"/>
        <result column="ALARM_MARK" property="alarmMark" jdbcType="CHAR"/>
        <result column="VEHICLE_REPORT_TELEPHONE" property="vehicleReportTelephone"
                jdbcType="VARCHAR"/>
        <result column="DEFAULT_LISCENSE_CODE" property="defaultLiscenseCode"
                jdbcType="VARCHAR"/>
        <result column="DEFAULT_LISCENSE" property="defaultLiscense"
                jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="VEHICLE_LISCENCE_CODE_PREFIX" property="vehicleLiscenceCodePrefix"
                jdbcType="VARCHAR"/>
        <result column="TELEPHONE_AREA_CODE" property="telephoneAreaCode"
                jdbcType="VARCHAR"/>
        <result column="ROW_ID" property="rowId" jdbcType="VARCHAR"/>
        <result column="DEPARTMENT_TYPE" property="departmentType"
                jdbcType="CHAR"/>
        <result column="IS_VIRTUAL" property="isVirtual" jdbcType="CHAR"/>
        <result column="VALIDATE_DATE" property="validateDate"
                jdbcType="TIMESTAMP"/>
        <result column="IS_VIP" property="isVip" jdbcType="CHAR"/>
        <result column="DISTRICT_CODE" property="districtCode"
                jdbcType="VARCHAR"/>
        <result column="IS_DEPARTMENT_NODE" property="isDepartmentNode"
                jdbcType="CHAR"/>
        <result column="IS_NEW_DEPARTMENT_NODE" property="isNewDepartmentNode"
                jdbcType="CHAR"/>
        <result column="DEPARTMENT_NODE_TYPE" property="departmentNodeType"
                jdbcType="CHAR"/>
        <result column="DEPARTMENT_NODE_STATUS" property="departmentNodeStatus"
                jdbcType="CHAR"/>
        <result column="UPLOAD_GROUPSID" property="uploadGroupsid"
                jdbcType="VARCHAR"/>
        <result column="UPGRADE_DATE" property="upgradeDate" jdbcType="TIMESTAMP"/>
        <result column="DEPARTMENT_NODE_TYPE_CLASS" property="departmentNodeTypeClass"
                jdbcType="CHAR"/>
        <result column="DEPARTMENT_NODE_STATUS_DATE" property="departmentNodeStatusDate"
                jdbcType="TIMESTAMP"/>
        <result column="DEPARTMENT_NODE_NAME" property="departmentNodeName"
                jdbcType="VARCHAR"/>
        <result column="TAX_REGISTER_NO" property="taxRegisterNo"
                jdbcType="VARCHAR"/>
        <result column="CHANNEL_ATTRIBUTE" property="channelAttribute"
                jdbcType="VARCHAR"/>
        <result column="IS_TOWN_DEPARTMENT" property="isTownDepartment"
                jdbcType="VARCHAR"/>
        <result column="TOWN_DEPARTMENT_TYPE" property="townDepartmentType"
                jdbcType="VARCHAR"/>
        <result column="VOLUME_LASTYEAR" property="volumeLastyear"
                jdbcType="VARCHAR"/>
        <result column="FIRST_YEAR_COMMISSION" property="firstYearCommission"
                jdbcType="VARCHAR"/>
        <result column="FIRST_PLAN" property="firstPlan" jdbcType="VARCHAR"/>
        <result column="SECOND_PLAN" property="secondPlan" jdbcType="VARCHAR"/>
        <result column="THIRD_PLAN" property="thirdPlan" jdbcType="VARCHAR"/>
        <result column="INSURED_PLACE" property="insuredPlace"
                jdbcType="VARCHAR"/>
        <result column="TAX_REGISTER_NAME" property="taxRegisterName"
                jdbcType="VARCHAR"/>
        <result column="CENTER_OR_PERIPHERY" property="centerOrPeriphery"
                jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        DEPARTMENT_CODE, CREATED_BY, CREATED_DATE, UPDATED_BY,
        UPDATED_DATE,
        DEPARTMENT_CHINESE_NAME,
        DEPARTMENT_ABBR_NAME,
        DEPARTMENT_LEVEL, FOUND_DATE, CHINESE_ADDRESS, POSTCODE,
        DEPARTMENT_ENGLISH_NAME,
        ENGLISH_ADDRESS, TELEPHONE, FAX,
        UPPER_DEPARTMENT_CODE, LINK_MAN_CODE,
        REPORT_BEFORE_REGISTER,
        INVALIDATE_DATE, DEPARTMENT_MARK, ALARM_MARK,
        VEHICLE_REPORT_TELEPHONE,
        DEFAULT_LISCENSE_CODE,
        DEFAULT_LISCENSE,
        EMAIL, VEHICLE_LISCENCE_CODE_PREFIX, TELEPHONE_AREA_CODE, ROW_ID,
        DEPARTMENT_TYPE, IS_VIRTUAL, VALIDATE_DATE, IS_VIP, DISTRICT_CODE,
        IS_DEPARTMENT_NODE,
        IS_NEW_DEPARTMENT_NODE, DEPARTMENT_NODE_TYPE,
        DEPARTMENT_NODE_STATUS, UPLOAD_GROUPSID,
        UPGRADE_DATE,
        DEPARTMENT_NODE_TYPE_CLASS, DEPARTMENT_NODE_STATUS_DATE,
        DEPARTMENT_NODE_NAME,
        TAX_REGISTER_NO, CHANNEL_ATTRIBUTE,
        IS_TOWN_DEPARTMENT, TOWN_DEPARTMENT_TYPE,
        VOLUME_LASTYEAR,
        FIRST_YEAR_COMMISSION, FIRST_PLAN, SECOND_PLAN, THIRD_PLAN,
        INSURED_PLACE,
        TAX_REGISTER_NAME,
        CENTER_OR_PERIPHERY
    </sql>

    <resultMap type="com.paic.ncbs.claim.model.dto.user.DepartmentDTO" id="departmentDTO">
        <result property="code" column="DEPARTMENT_CODE"/>
        <result property="name" column="DEPARTMENT_ABBR_NAME"/>
        <result property="level" column="DEPARTMENT_LEVEL"/>
        <result property="departmentChineseName" column="DEPARTMENT_CHINESE_NAME"/>
        <result property="showName" column="SHOW_NAME"/>
    </resultMap>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from DEPARTMENT_DEFINE
        where DEPARTMENT_CODE =
        #{idDepartmentDefine,jdbcType=VARCHAR}
    </select>

    <select id="getSubDepartment" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from DEPARTMENT_DEFINE
        where UPPER_DEPARTMENT_CODE =
        #{departmentCode,jdbcType=VARCHAR}
        AND (INVALIDATE_DATE is null or
        INVALIDATE_DATE>sysdate())
    </select>

    <select id="getDepartmentByDepartmentCode" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from department_define a
        where a.department_level in ('1','2')
        and a.invalidate_date is null
        and (a.DEPARTMENT_CODE = #{departmentCode}
        or a.UPPER_DEPARTMENT_CODE = #{departmentCode} )
        order by a.department_code
    </select>

    <select id="getDepartmentInfo" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from DEPARTMENT_DEFINE
        where DEPARTMENT_CODE =
        #{departmentCode,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from
        DEPARTMENT_DEFINE
        where DEPARTMENT_CODE =
        #{departmentCode,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity">
        insert into DEPARTMENT_DEFINE (DEPARTMENT_CODE,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY, UPDATED_DATE,
        DEPARTMENT_CHINESE_NAME,
        DEPARTMENT_ABBR_NAME, DEPARTMENT_LEVEL,
        FOUND_DATE,
        CHINESE_ADDRESS, POSTCODE, DEPARTMENT_ENGLISH_NAME,
        ENGLISH_ADDRESS, TELEPHONE, FAX,
        UPPER_DEPARTMENT_CODE, LINK_MAN_CODE,
        REPORT_BEFORE_REGISTER,
        INVALIDATE_DATE, DEPARTMENT_MARK, ALARM_MARK,
        VEHICLE_REPORT_TELEPHONE, DEFAULT_LISCENSE_CODE,
        DEFAULT_LISCENSE,
        EMAIL, VEHICLE_LISCENCE_CODE_PREFIX,
        TELEPHONE_AREA_CODE, ROW_ID,
        DEPARTMENT_TYPE,
        IS_VIRTUAL, VALIDATE_DATE, IS_VIP,
        DISTRICT_CODE,
        IS_DEPARTMENT_NODE, IS_NEW_DEPARTMENT_NODE,
        DEPARTMENT_NODE_TYPE,
        DEPARTMENT_NODE_STATUS, UPLOAD_GROUPSID,
        UPGRADE_DATE,
        DEPARTMENT_NODE_TYPE_CLASS, DEPARTMENT_NODE_STATUS_DATE,
        DEPARTMENT_NODE_NAME, TAX_REGISTER_NO, CHANNEL_ATTRIBUTE,
        IS_TOWN_DEPARTMENT, TOWN_DEPARTMENT_TYPE, VOLUME_LASTYEAR,
        FIRST_YEAR_COMMISSION, FIRST_PLAN, SECOND_PLAN,
        THIRD_PLAN,
        INSURED_PLACE, TAX_REGISTER_NAME,
        CENTER_OR_PERIPHERY)
        values
        (#{departmentCode,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR},
        #{updatedDate,jdbcType=TIMESTAMP},
        #{departmentChineseName,jdbcType=VARCHAR},
        #{departmentAbbrName,jdbcType=VARCHAR},
        #{departmentLevel,jdbcType=DECIMAL}, #{foundDate,jdbcType=TIMESTAMP},
        #{chineseAddress,jdbcType=VARCHAR}, #{postcode,jdbcType=VARCHAR},
        #{departmentEnglishName,jdbcType=VARCHAR},
        #{englishAddress,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR},
        #{fax,jdbcType=VARCHAR},
        #{upperDepartmentCode,jdbcType=VARCHAR},
        #{linkManCode,jdbcType=VARCHAR},
        #{reportBeforeRegister,jdbcType=CHAR},
        #{invalidateDate,jdbcType=TIMESTAMP},
        #{departmentMark,jdbcType=VARCHAR}, #{alarmMark,jdbcType=CHAR},
        #{vehicleReportTelephone,jdbcType=VARCHAR},
        #{defaultLiscenseCode,jdbcType=VARCHAR},
        #{defaultLiscense,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR},
        #{vehicleLiscenceCodePrefix,jdbcType=VARCHAR},
        #{telephoneAreaCode,jdbcType=VARCHAR}, #{rowId,jdbcType=VARCHAR},
        #{departmentType,jdbcType=CHAR},
        #{isVirtual,jdbcType=CHAR},
        #{validateDate,jdbcType=TIMESTAMP}, #{isVip,jdbcType=CHAR},
        #{districtCode,jdbcType=VARCHAR}, #{isDepartmentNode,jdbcType=CHAR},
        #{isNewDepartmentNode,jdbcType=CHAR},
        #{departmentNodeType,jdbcType=CHAR},
        #{departmentNodeStatus,jdbcType=CHAR},
        #{uploadGroupsid,jdbcType=VARCHAR},
        #{upgradeDate,jdbcType=TIMESTAMP},
        #{departmentNodeTypeClass,jdbcType=CHAR},
        #{departmentNodeStatusDate,jdbcType=TIMESTAMP},
        #{departmentNodeName,jdbcType=VARCHAR},
        #{taxRegisterNo,jdbcType=VARCHAR},
        #{channelAttribute,jdbcType=VARCHAR},
        #{isTownDepartment,jdbcType=VARCHAR},
        #{townDepartmentType,jdbcType=VARCHAR},
        #{volumeLastyear,jdbcType=VARCHAR},
        #{firstYearCommission,jdbcType=VARCHAR},
        #{firstPlan,jdbcType=VARCHAR}, #{secondPlan,jdbcType=VARCHAR},
        #{thirdPlan,jdbcType=VARCHAR}, #{insuredPlace,jdbcType=VARCHAR},
        #{taxRegisterName,jdbcType=VARCHAR},
        #{centerOrPeriphery,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity">
        update DEPARTMENT_DEFINE
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentChineseName != null">
                DEPARTMENT_CHINESE_NAME =
                #{departmentChineseName,jdbcType=VARCHAR},
            </if>
            <if test="departmentAbbrName != null">
                DEPARTMENT_ABBR_NAME =
                #{departmentAbbrName,jdbcType=VARCHAR},
            </if>
            <if test="departmentLevel != null">
                DEPARTMENT_LEVEL = #{departmentLevel,jdbcType=DECIMAL},
            </if>
            <if test="foundDate != null">
                FOUND_DATE = #{foundDate,jdbcType=TIMESTAMP},
            </if>
            <if test="chineseAddress != null">
                CHINESE_ADDRESS = #{chineseAddress,jdbcType=VARCHAR},
            </if>
            <if test="postcode != null">
                POSTCODE = #{postcode,jdbcType=VARCHAR},
            </if>
            <if test="departmentEnglishName != null">
                DEPARTMENT_ENGLISH_NAME =
                #{departmentEnglishName,jdbcType=VARCHAR},
            </if>
            <if test="englishAddress != null">
                ENGLISH_ADDRESS = #{englishAddress,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                TELEPHONE = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                FAX = #{fax,jdbcType=VARCHAR},
            </if>
            <if test="upperDepartmentCode != null">
                UPPER_DEPARTMENT_CODE =
                #{upperDepartmentCode,jdbcType=VARCHAR},
            </if>
            <if test="linkManCode != null">
                LINK_MAN_CODE = #{linkManCode,jdbcType=VARCHAR},
            </if>
            <if test="reportBeforeRegister != null">
                REPORT_BEFORE_REGISTER =
                #{reportBeforeRegister,jdbcType=CHAR},
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE = #{invalidateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentMark != null">
                DEPARTMENT_MARK = #{departmentMark,jdbcType=VARCHAR},
            </if>
            <if test="alarmMark != null">
                ALARM_MARK = #{alarmMark,jdbcType=CHAR},
            </if>
            <if test="vehicleReportTelephone != null">
                VEHICLE_REPORT_TELEPHONE =
                #{vehicleReportTelephone,jdbcType=VARCHAR},
            </if>
            <if test="defaultLiscenseCode != null">
                DEFAULT_LISCENSE_CODE =
                #{defaultLiscenseCode,jdbcType=VARCHAR},
            </if>
            <if test="defaultLiscense != null">
                DEFAULT_LISCENSE = #{defaultLiscense,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                EMAIL = #{email,jdbcType=VARCHAR},
            </if>
            <if test="vehicleLiscenceCodePrefix != null">
                VEHICLE_LISCENCE_CODE_PREFIX =
                #{vehicleLiscenceCodePrefix,jdbcType=VARCHAR},
            </if>
            <if test="telephoneAreaCode != null">
                TELEPHONE_AREA_CODE =
                #{telephoneAreaCode,jdbcType=VARCHAR},
            </if>
            <if test="rowId != null">
                ROW_ID = #{rowId,jdbcType=VARCHAR},
            </if>
            <if test="departmentType != null">
                DEPARTMENT_TYPE = #{departmentType,jdbcType=CHAR},
            </if>
            <if test="isVirtual != null">
                IS_VIRTUAL = #{isVirtual,jdbcType=CHAR},
            </if>
            <if test="validateDate != null">
                VALIDATE_DATE = #{validateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="isVip != null">
                IS_VIP = #{isVip,jdbcType=CHAR},
            </if>
            <if test="districtCode != null">
                DISTRICT_CODE = #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="isDepartmentNode != null">
                IS_DEPARTMENT_NODE = #{isDepartmentNode,jdbcType=CHAR},
            </if>
            <if test="isNewDepartmentNode != null">
                IS_NEW_DEPARTMENT_NODE =
                #{isNewDepartmentNode,jdbcType=CHAR},
            </if>
            <if test="departmentNodeType != null">
                DEPARTMENT_NODE_TYPE =
                #{departmentNodeType,jdbcType=CHAR},
            </if>
            <if test="departmentNodeStatus != null">
                DEPARTMENT_NODE_STATUS =
                #{departmentNodeStatus,jdbcType=CHAR},
            </if>
            <if test="uploadGroupsid != null">
                UPLOAD_GROUPSID = #{uploadGroupsid,jdbcType=VARCHAR},
            </if>
            <if test="upgradeDate != null">
                UPGRADE_DATE = #{upgradeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentNodeTypeClass != null">
                DEPARTMENT_NODE_TYPE_CLASS =
                #{departmentNodeTypeClass,jdbcType=CHAR},
            </if>
            <if test="departmentNodeStatusDate != null">
                DEPARTMENT_NODE_STATUS_DATE =
                #{departmentNodeStatusDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentNodeName != null">
                DEPARTMENT_NODE_NAME =
                #{departmentNodeName,jdbcType=VARCHAR},
            </if>
            <if test="taxRegisterNo != null">
                TAX_REGISTER_NO = #{taxRegisterNo,jdbcType=VARCHAR},
            </if>
            <if test="channelAttribute != null">
                CHANNEL_ATTRIBUTE = #{channelAttribute,jdbcType=VARCHAR},
            </if>
            <if test="isTownDepartment != null">
                IS_TOWN_DEPARTMENT =
                #{isTownDepartment,jdbcType=VARCHAR},
            </if>
            <if test="townDepartmentType != null">
                TOWN_DEPARTMENT_TYPE =
                #{townDepartmentType,jdbcType=VARCHAR},
            </if>
            <if test="volumeLastyear != null">
                VOLUME_LASTYEAR = #{volumeLastyear,jdbcType=VARCHAR},
            </if>
            <if test="firstYearCommission != null">
                FIRST_YEAR_COMMISSION =
                #{firstYearCommission,jdbcType=VARCHAR},
            </if>
            <if test="firstPlan != null">
                FIRST_PLAN = #{firstPlan,jdbcType=VARCHAR},
            </if>
            <if test="secondPlan != null">
                SECOND_PLAN = #{secondPlan,jdbcType=VARCHAR},
            </if>
            <if test="thirdPlan != null">
                THIRD_PLAN = #{thirdPlan,jdbcType=VARCHAR},
            </if>
            <if test="insuredPlace != null">
                INSURED_PLACE = #{insuredPlace,jdbcType=VARCHAR},
            </if>
            <if test="taxRegisterName != null">
                TAX_REGISTER_NAME = #{taxRegisterName,jdbcType=VARCHAR},
            </if>
            <if test="centerOrPeriphery != null">
                CENTER_OR_PERIPHERY =
                #{centerOrPeriphery,jdbcType=VARCHAR},
            </if>
        </set>
        where DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity">
        update DEPARTMENT_DEFINE
        set CREATED_BY =
        #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE =
        #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY =
        #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE =
        #{updatedDate,jdbcType=TIMESTAMP},
        DEPARTMENT_CHINESE_NAME =
        #{departmentChineseName,jdbcType=VARCHAR},
        DEPARTMENT_ABBR_NAME =
        #{departmentAbbrName,jdbcType=VARCHAR},
        DEPARTMENT_LEVEL =
        #{departmentLevel,jdbcType=DECIMAL},
        FOUND_DATE =
        #{foundDate,jdbcType=TIMESTAMP},
        CHINESE_ADDRESS =
        #{chineseAddress,jdbcType=VARCHAR},
        POSTCODE =
        #{postcode,jdbcType=VARCHAR},
        DEPARTMENT_ENGLISH_NAME =
        #{departmentEnglishName,jdbcType=VARCHAR},
        ENGLISH_ADDRESS =
        #{englishAddress,jdbcType=VARCHAR},
        TELEPHONE =
        #{telephone,jdbcType=VARCHAR},
        FAX = #{fax,jdbcType=VARCHAR},
        UPPER_DEPARTMENT_CODE = #{upperDepartmentCode,jdbcType=VARCHAR},
        LINK_MAN_CODE = #{linkManCode,jdbcType=VARCHAR},
        REPORT_BEFORE_REGISTER = #{reportBeforeRegister,jdbcType=CHAR},
        INVALIDATE_DATE = #{invalidateDate,jdbcType=TIMESTAMP},
        DEPARTMENT_MARK = #{departmentMark,jdbcType=VARCHAR},
        ALARM_MARK =
        #{alarmMark,jdbcType=CHAR},
        VEHICLE_REPORT_TELEPHONE =
        #{vehicleReportTelephone,jdbcType=VARCHAR},
        DEFAULT_LISCENSE_CODE =
        #{defaultLiscenseCode,jdbcType=VARCHAR},
        DEFAULT_LISCENSE =
        #{defaultLiscense,jdbcType=VARCHAR},
        EMAIL = #{email,jdbcType=VARCHAR},
        VEHICLE_LISCENCE_CODE_PREFIX =
        #{vehicleLiscenceCodePrefix,jdbcType=VARCHAR},
        TELEPHONE_AREA_CODE =
        #{telephoneAreaCode,jdbcType=VARCHAR},
        ROW_ID =
        #{rowId,jdbcType=VARCHAR},
        DEPARTMENT_TYPE =
        #{departmentType,jdbcType=CHAR},
        IS_VIRTUAL =
        #{isVirtual,jdbcType=CHAR},
        VALIDATE_DATE =
        #{validateDate,jdbcType=TIMESTAMP},
        IS_VIP = #{isVip,jdbcType=CHAR},
        DISTRICT_CODE = #{districtCode,jdbcType=VARCHAR},
        IS_DEPARTMENT_NODE =
        #{isDepartmentNode,jdbcType=CHAR},
        IS_NEW_DEPARTMENT_NODE =
        #{isNewDepartmentNode,jdbcType=CHAR},
        DEPARTMENT_NODE_TYPE =
        #{departmentNodeType,jdbcType=CHAR},
        DEPARTMENT_NODE_STATUS =
        #{departmentNodeStatus,jdbcType=CHAR},
        UPLOAD_GROUPSID =
        #{uploadGroupsid,jdbcType=VARCHAR},
        UPGRADE_DATE =
        #{upgradeDate,jdbcType=TIMESTAMP},
        DEPARTMENT_NODE_TYPE_CLASS =
        #{departmentNodeTypeClass,jdbcType=CHAR},
        DEPARTMENT_NODE_STATUS_DATE =
        #{departmentNodeStatusDate,jdbcType=TIMESTAMP},
        DEPARTMENT_NODE_NAME =
        #{departmentNodeName,jdbcType=VARCHAR},
        TAX_REGISTER_NO =
        #{taxRegisterNo,jdbcType=VARCHAR},
        CHANNEL_ATTRIBUTE =
        #{channelAttribute,jdbcType=VARCHAR},
        IS_TOWN_DEPARTMENT =
        #{isTownDepartment,jdbcType=VARCHAR},
        TOWN_DEPARTMENT_TYPE =
        #{townDepartmentType,jdbcType=VARCHAR},
        VOLUME_LASTYEAR =
        #{volumeLastyear,jdbcType=VARCHAR},
        FIRST_YEAR_COMMISSION =
        #{firstYearCommission,jdbcType=VARCHAR},
        FIRST_PLAN =
        #{firstPlan,jdbcType=VARCHAR},
        SECOND_PLAN =
        #{secondPlan,jdbcType=VARCHAR},
        THIRD_PLAN =
        #{thirdPlan,jdbcType=VARCHAR},
        INSURED_PLACE =
        #{insuredPlace,jdbcType=VARCHAR},
        TAX_REGISTER_NAME =
        #{taxRegisterName,jdbcType=VARCHAR},
        CENTER_OR_PERIPHERY =
        #{centerOrPeriphery,jdbcType=VARCHAR}
        where DEPARTMENT_CODE =
        #{departmentCode,jdbcType=VARCHAR}
    </update>

    <select id="queryDepartmentInfoByDeptCode" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.user.DepartmentDTO">
        select
        d.DEPARTMENT_CODE AS "departmentCode",
        d.DEPARTMENT_CHINESE_NAME AS "departmentChineseName",
        d.DEPARTMENT_CHINESE_NAME AS "departmentName",
        d.DEPARTMENT_ABBR_NAME AS "departmentAbbrName",
        d.DEPARTMENT_LEVEL AS "departmentLevel",
        d.CHINESE_ADDRESS AS "chineseAddress",
        d.POSTCODE AS "postcode",
        d.DEPARTMENT_ENGLISH_NAME AS "departmentEnglishName",
        d.ENGLISH_ADDRESS AS "englishAddress",
        d.TELEPHONE AS "telephone",
        d.FAX AS "fax",
        d.UPPER_DEPARTMENT_CODE AS "upperDepartmentCode",
        d.LINK_MAN_CODE AS "linkManCode",
        d.INVALIDATE_DATE AS "invalidateDate",
        d.DEPARTMENT_MARK AS "departmentMark",
        d.ALARM_MARK AS "alarmMark",
        d.DEFAULT_LISCENSE_CODE AS "defaultLiscenseCode",
        d.DEFAULT_LISCENSE AS "defaultLiscense",
        d.EMAIL AS "email",
        d.VEHICLE_LISCENCE_CODE_PREFIX AS "vehicleLiscenceCodePrefix",
        d.TELEPHONE_AREA_CODE AS "telephoneAreaCode",
        d.IS_VIP AS "isVip",
        d.DISTRICT_CODE AS "districtCode",
        d.IS_DEPARTMENT_NODE AS "isDepartmentNode",
        d.IS_NEW_DEPARTMENT_NODE AS "isNewDepartmentNode",
        d.DEPARTMENT_NODE_TYPE AS "departmentNodeType",
        d.DEPARTMENT_NODE_STATUS AS "departmentNodeStatus",
        d.DEPARTMENT_NODE_TYPE_CLASS AS "departmentNodeTypeClass",
        d.DEPARTMENT_NODE_NAME AS "departmentNodeName",
        d.TAX_REGISTER_NO AS "taxRegisterNo",
        d.CHANNEL_ATTRIBUTE AS "channelAttribute",
        d.IS_TOWN_DEPARTMENT AS "isTownDepartment",
        d.TOWN_DEPARTMENT_TYPE AS "townDepartmentType",
        d.VOLUME_LASTYEAR AS "volumeLastyear",
        d.FIRST_YEAR_COMMISSION AS "firstYearCommission",
        d.FIRST_PLAN AS "firstPlan",
        d.SECOND_PLAN AS "secondPlan",
        d.THIRD_PLAN AS "thirdPlan",
        d.INSURED_PLACE AS "insuredPlace",
        d.TAX_REGISTER_NAME AS "taxRegisterName"
        from department_define d
        where d.department_code = #{deptCode}
    </select>

    <select id="getSelectDepartmentList" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.vo.report.DepartmentVO">
        select
            DEPARTMENT_CODE departmentCode,
            DEPARTMENT_CHINESE_NAME departmentChineseName,
            DEPARTMENT_ABBR_NAME departmentAbbrName,
            DEPARTMENT_LEVEL departmentLevel,
            UPPER_DEPARTMENT_CODE upperDepartmentCode
        from department_define
        where UPPER_DEPARTMENT_CODE = '775'
            or department_code = '1'
    </select>

    <select id="querySameLevelDepartmentListByDeptCode" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity">
        SELECT
        d.DEPARTMENT_CODE AS "departmentCode",
        d.DEPARTMENT_CHINESE_NAME AS "departmentChineseName",
        d.DEPARTMENT_ABBR_NAME AS "departmentName",
        d.DEPARTMENT_LEVEL AS "departmentLevel",
        d.CHINESE_ADDRESS AS "chineseAddress",
        d.POSTCODE AS "postcode",
        d.DEPARTMENT_ENGLISH_NAME AS "departmentEnglishName",
        d.ENGLISH_ADDRESS AS "englishAddress",
        d.TELEPHONE AS "telephone",
        d.FAX AS "fax",
        d.UPPER_DEPARTMENT_CODE AS "upperDepartmentCode",
        d.LINK_MAN_CODE AS "linkManCode",
        d.INVALIDATE_DATE AS "invalidateDate",
        d.DEPARTMENT_MARK AS "departmentMark",
        d.ALARM_MARK AS "alarmMark",
        d.DEFAULT_LISCENSE_CODE AS "defaultLiscenseCode",
        d.DEFAULT_LISCENSE AS "defaultLiscense",
        d.EMAIL AS "email",
        d.VEHICLE_LISCENCE_CODE_PREFIX AS "vehicleLiscenceCodePrefix",
        d.TELEPHONE_AREA_CODE AS "telephoneAreaCode",
        d.IS_VIP AS "isVip",
        d.DISTRICT_CODE AS "districtCode",
        d.IS_DEPARTMENT_NODE AS "isDepartmentNode",
        d.IS_NEW_DEPARTMENT_NODE AS "isNewDepartmentNode",
        d.DEPARTMENT_NODE_TYPE AS "departmentNodeType",
        d.DEPARTMENT_NODE_STATUS AS "departmentNodeStatus",
        d.DEPARTMENT_NODE_TYPE_CLASS AS "departmentNodeTypeClass",
        d.DEPARTMENT_NODE_NAME AS "departmentNodeName",
        d.TAX_REGISTER_NO AS "taxRegisterNo",
        d.CHANNEL_ATTRIBUTE AS "channelAttribute",
        d.IS_TOWN_DEPARTMENT AS "isTownDepartment",
        d.TOWN_DEPARTMENT_TYPE AS "townDepartmentType",
        d.VOLUME_LASTYEAR AS "volumeLastyear",
        d.FIRST_YEAR_COMMISSION AS "firstYearCommission",
        d.FIRST_PLAN AS "firstPlan",
        d.SECOND_PLAN AS "secondPlan",
        d.THIRD_PLAN AS "thirdPlan",
        d.INSURED_PLACE AS "insuredPlace",
        d.TAX_REGISTER_NAME AS "taxRegisterName"
        from department_define d
        where d.upper_department_code =
        (select b.upper_department_code
        from department_define b
        where b.department_code = #{deptCode}
        )
        and department_code != '1'
        <![CDATA[and (d.invalidate_date is null or d.invalidate_date > sysdate()) ]]>
        order by d.department_code asc
    </select>

    <select id="queryDepartmentListByLevel" parameterType="java.lang.Integer"
            resultType="com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity">
        SELECT
        d.DEPARTMENT_CODE AS "departmentCode",
        d.DEPARTMENT_CHINESE_NAME AS "departmentChineseName",
        d.DEPARTMENT_ABBR_NAME AS "departmentName",
        d.DEPARTMENT_LEVEL AS "departmentLevel",
        d.CHINESE_ADDRESS AS "chineseAddress",
        d.POSTCODE AS "postcode",
        d.DEPARTMENT_ENGLISH_NAME AS "departmentEnglishName",
        d.ENGLISH_ADDRESS AS "englishAddress",
        d.TELEPHONE AS "telephone",
        d.FAX AS "fax",
        d.UPPER_DEPARTMENT_CODE AS "upperDepartmentCode",
        d.LINK_MAN_CODE AS "linkManCode",
        d.INVALIDATE_DATE AS "invalidateDate",
        d.DEPARTMENT_MARK AS "departmentMark",
        d.ALARM_MARK AS "alarmMark",
        d.DEFAULT_LISCENSE_CODE AS "defaultLiscenseCode",
        d.DEFAULT_LISCENSE AS "defaultLiscense",
        d.EMAIL AS "email",
        d.VEHICLE_LISCENCE_CODE_PREFIX AS "vehicleLiscenceCodePrefix",
        d.TELEPHONE_AREA_CODE AS "telephoneAreaCode",
        d.IS_VIP AS "isVip",
        d.DISTRICT_CODE AS "districtCode",
        d.IS_DEPARTMENT_NODE AS "isDepartmentNode",
        d.IS_NEW_DEPARTMENT_NODE AS "isNewDepartmentNode",
        d.DEPARTMENT_NODE_TYPE AS "departmentNodeType",
        d.DEPARTMENT_NODE_STATUS AS "departmentNodeStatus",
        d.DEPARTMENT_NODE_TYPE_CLASS AS "departmentNodeTypeClass",
        d.DEPARTMENT_NODE_NAME AS "departmentNodeName",
        d.TAX_REGISTER_NO AS "taxRegisterNo",
        d.CHANNEL_ATTRIBUTE AS "channelAttribute",
        d.IS_TOWN_DEPARTMENT AS "isTownDepartment",
        d.TOWN_DEPARTMENT_TYPE AS "townDepartmentType",
        d.VOLUME_LASTYEAR AS "volumeLastyear",
        d.FIRST_YEAR_COMMISSION AS "firstYearCommission",
        d.FIRST_PLAN AS "firstPlan",
        d.SECOND_PLAN AS "secondPlan",
        d.THIRD_PLAN AS "thirdPlan",
        d.INSURED_PLACE AS "insuredPlace",
        d.TAX_REGISTER_NAME AS "taxRegisterName"
        FROM department_define d
        where d.department_level = #{level}
        <![CDATA[and (d.invalidate_date is null or d.invalidate_date > sysdate()) ]]>
        order by d.department_code
    </select>

    <select id="queryParentDepartmentInfoByDeptCode" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity">
        SELECT
        d.DEPARTMENT_CODE AS "departmentCode",
        d.DEPARTMENT_CHINESE_NAME AS "departmentChineseName",
        d.DEPARTMENT_ABBR_NAME AS "departmentName",
        d.DEPARTMENT_LEVEL AS "departmentLevel",
        d.CHINESE_ADDRESS AS "chineseAddress",
        d.POSTCODE AS "postcode",
        d.DEPARTMENT_ENGLISH_NAME AS "departmentEnglishName",
        d.ENGLISH_ADDRESS AS "englishAddress",
        d.TELEPHONE AS "telephone",
        d.FAX AS "fax",
        d.UPPER_DEPARTMENT_CODE AS "upperDepartmentCode",
        d.LINK_MAN_CODE AS "linkManCode",
        d.INVALIDATE_DATE AS "invalidateDate",
        d.DEPARTMENT_MARK AS "departmentMark",
        d.ALARM_MARK AS "alarmMark",
        d.DEFAULT_LISCENSE_CODE AS "defaultLiscenseCode",
        d.DEFAULT_LISCENSE AS "defaultLiscense",
        d.EMAIL AS "email",
        d.VEHICLE_LISCENCE_CODE_PREFIX AS "vehicleLiscenceCodePrefix",
        d.TELEPHONE_AREA_CODE AS "telephoneAreaCode",
        d.IS_VIP AS "isVip",
        d.DISTRICT_CODE AS "districtCode",
        d.IS_DEPARTMENT_NODE AS "isDepartmentNode",
        d.IS_NEW_DEPARTMENT_NODE AS "isNewDepartmentNode",
        d.DEPARTMENT_NODE_TYPE AS "departmentNodeType",
        d.DEPARTMENT_NODE_STATUS AS "departmentNodeStatus",
        d.DEPARTMENT_NODE_TYPE_CLASS AS "departmentNodeTypeClass",
        d.DEPARTMENT_NODE_NAME AS "departmentNodeName",
        d.TAX_REGISTER_NO AS "taxRegisterNo",
        d.CHANNEL_ATTRIBUTE AS "channelAttribute",
        d.IS_TOWN_DEPARTMENT AS "isTownDepartment",
        d.TOWN_DEPARTMENT_TYPE AS "townDepartmentType",
        d.VOLUME_LASTYEAR AS "volumeLastyear",
        d.FIRST_YEAR_COMMISSION AS "firstYearCommission",
        d.FIRST_PLAN AS "firstPlan",
        d.SECOND_PLAN AS "secondPlan",
        d.THIRD_PLAN AS "thirdPlan",
        d.INSURED_PLACE AS "insuredPlace",
        d.TAX_REGISTER_NAME AS "taxRegisterName"
        FROM department_define d
        where d.department_code= (SELECT t.upper_department_code
        FROM department_define t
        where t.department_code = #{deptCode}
        <![CDATA[and (d.invalidate_date is null or d.invalidate_date > sysdate()))]]>
    </select>

    <select id="queryChildDepartmentRecursion" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.user.DepartmentDTO">
        SELECT DEPARTMENT_CODE departmentCode ,
               DEPARTMENT_CHINESE_NAME departmentChineseName,
               DEPARTMENT_ABBR_NAME departmentAbbrName,
               DEPARTMENT_LEVEL departmentLevel,
               (CASE WHEN DEPARTMENT_CODE = '1' THEN '1' ELSE UPPER_DEPARTMENT_CODE END )upperDepartmentCode
        FROM department_define WHERE DEPARTMENT_CODE = #{deptCode}
        UNION
        select code,name,abbr_name,lev,(CASE WHEN code = '1' THEN '1' ELSE parent_code END )
        from (
                 select t1.DEPARTMENT_CODE code,
                        t1.DEPARTMENT_CHINESE_NAME name,
                        t1.DEPARTMENT_ABBR_NAME abbr_name,
                        t1.DEPARTMENT_LEVEL lev,
                        t1.UPPER_DEPARTMENT_CODE parent_code,
                        if(find_in_set(UPPER_DEPARTMENT_CODE, @ids) > 0, @ids := concat(@ids, ',', DEPARTMENT_CODE), 0) as ischild
                 from (
                          select DEPARTMENT_CODE,
                                 UPPER_DEPARTMENT_CODE,
                                 DEPARTMENT_CHINESE_NAME ,
                                 DEPARTMENT_ABBR_NAME,
                                 DEPARTMENT_LEVEL
                          from department_define t
                          order by DEPARTMENT_CODE
                      ) t1,
                      (select @ids := #{deptCode}) t2
             ) t3
        where ischild != 0
        ORDER BY departmentCode;
    </select>

    <select id="queryParentDepartmentRecursion" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.user.DepartmentDTO">
        SELECT DEPARTMENT_CODE departmentCode ,
               DEPARTMENT_CHINESE_NAME departmentChineseName,
               DEPARTMENT_ABBR_NAME departmentAbbrName,
               DEPARTMENT_LEVEL departmentLevel,
               (CASE WHEN DEPARTMENT_CODE = '1' THEN '1' ELSE UPPER_DEPARTMENT_CODE END )upperDepartmentCode
        FROM department_define WHERE DEPARTMENT_CODE = #{deptCode}
        UNION
        select code,name,abbr_name,lev,(CASE WHEN code = '1' THEN '1' ELSE parent_code END )
        from (
                 select t1.DEPARTMENT_CODE code,
                        t1.DEPARTMENT_CHINESE_NAME name,
                        t1.DEPARTMENT_ABBR_NAME abbr_name,
                        t1.DEPARTMENT_LEVEL lev,
                        t1.UPPER_DEPARTMENT_CODE parent_code,
                        if(find_in_set(DEPARTMENT_CODE, @ids) > 0, @ids := concat(@ids, ',', UPPER_DEPARTMENT_CODE), 0) as isparent
                 from (
                          select DEPARTMENT_CODE,
                                 UPPER_DEPARTMENT_CODE,
                                 DEPARTMENT_CHINESE_NAME ,
                                 DEPARTMENT_ABBR_NAME,
                                 DEPARTMENT_LEVEL
                          from department_define t
                          order by DEPARTMENT_CODE desc
                      ) t1,
                      (select @ids := (select UPPER_DEPARTMENT_CODE from department_define where DEPARTMENT_CODE = #{deptCode})) t2
                 order by DEPARTMENT_CODE desc
             ) t3
        where isparent != 0
        ORDER BY departmentCode;
    </select>

    <select id="getDepartmentL2ByCode" resultType="com.paic.ncbs.claim.model.dto.user.DepartmentDTO">
        select t.parent_department_code  code, s.department_abbr_name  name,
        s.DEPARTMENT_CHINESE_NAME departmentChineseName,
        concat(s.department_code,'-',s.department_abbr_name) as showName
        from department_define s, department_relation t
        where s.department_code = t.parent_department_code
        and t.department_level = '2'
        and t.child_department_code = #{departmentCode,jdbcType=VARCHAR}
    </select>

    <select id="getDepartmentList" resultMap="departmentDTO">
        select a.DEPARTMENT_CODE,
        a.DEPARTMENT_ABBR_NAME,
        a.DEPARTMENT_CHINESE_NAME,
        concat(a.DEPARTMENT_CODE,'-',a.DEPARTMENT_ABBR_NAME)as SHOW_NAME
        from department_define a
        where a.department_level = '2'
        and a.invalidate_date is null
        order by a.department_code
    </select>

    <select id="getLevel2DeptByLevel3" resultType="com.paic.ncbs.claim.model.dto.user.DepartmentDTO">
        select t.parent_department_code as code,
        (select dd.department_abbr_name
        from department_define dd
        where dd.department_code = t.parent_department_code) as name
        from department_relation t
        where t.department_level = '2'
        and t.child_department_code = #{departmentCode}
    </select>

    <select id="queryChildDepartments" resultType="com.paic.ncbs.claim.model.dto.user.DepartmentDTO">
        select t.child_department_code code, s.department_abbr_name name,
        s.department_code ||'-'||s.department_abbr_name as showName,
        s.department_chinese_name as departmentChineseName,
        s.department_level as "level"
        from department_define s, department_relation t
        where s.department_code = t.child_department_code
        and t.parent_department_code = #{departmentCode,jdbcType=VARCHAR}
    </select>

    <select id="queryDepartmentNameByDeptCode" resultType="java.lang.String">
        select department_abbr_name
        from department_define
        where department_code = #{deptCode,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="getLevel2DeptCode" resultType="java.lang.String">
        SELECT DD.DEPARTMENT_CODE
        FROM DEPARTMENT_DEFINE DD
        WHERE DD.DEPARTMENT_CODE IN
        (SELECT DD2.PARENT_DEPARTMENT_CODE
        FROM DEPARTMENT_DEFINE DD1, DEPARTMENT_RELATION DD2
        WHERE DD1.DEPARTMENT_CODE = DD2.CHILD_DEPARTMENT_CODE
        AND DD1.DEPARTMENT_CODE =  #{deptCode,jdbcType=VARCHAR})
        AND DD.DEPARTMENT_LEVEL = '2'
    </select>

    <select id="getChildCodeList" resultType="java.lang.String">
        select DEPARTMENT_CODE
        from department_define
        where UPPER_DEPARTMENT_CODE in
        <foreach collection="deptCodeList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="filterLevelDeptCode" resultType="com.paic.ncbs.um.model.dto.SystemComInfoDTO">
        SELECT DD.DEPARTMENT_CODE comCode,
               DD.DEPARTMENT_ABBR_NAME comCodeName
        FROM DEPARTMENT_DEFINE DD
        WHERE  DEPARTMENT_LEVEL <![CDATA[ >= ]]> (select DEPARTMENT_LEVEL
        from department_define d1 where d1.department_code = #{deptCode,jdbcType=VARCHAR} limit 1)
        AND DD.DEPARTMENT_CODE IN
        <foreach collection="sysComList" item="item" open="(" close=")" separator=",">
            #{item.comCode,jdbcType=VARCHAR}
        </foreach>

    </select>

    <select id="getTopDepartment" resultType="com.paic.ncbs.claim.model.dto.user.DepartmentDTO">
        WITH RECURSIVE DeptPath AS (
            SELECT
            department_code,
            upper_department_code,
            department_code AS top_department
            FROM department_define
            WHERE department_code = #{deptCode,jdbcType=VARCHAR}

            UNION ALL

            SELECT
            d.department_code,
            d.upper_department_code,
            dp.top_department
            FROM department_define d
            JOIN DeptPath dp ON d.department_code = dp.upper_department_code
            where d.department_code != '775' and d.department_code != '1'
        )
        SELECT top_department as department_code,upper_department_code
        FROM DeptPath
        WHERE upper_department_code in ('775','1')
    </select>
</mapper>