<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.user.DepartmentUserMapper">

	<select id="getDepartmentUser" resultType="com.paic.ncbs.claim.model.vo.user.DepartmentUserVO"
			parameterType="com.paic.ncbs.claim.model.dto.user.DepartmentUserDTO">
		select
			a.department_code as departmentCode,
			a.department_name as departmentName
		from clm_department_user a
		where a.user_id = #{userId,jdbcType=VARCHAR}
	</select>

</mapper>