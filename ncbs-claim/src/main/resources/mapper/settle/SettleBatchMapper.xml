<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.SettleBatchMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.SettleBatchInfoDTO" id="settleBatchInfoDTO">
        <result property="idAhcsSettleBatchInfo" column="ID_AHCS_SETTLE_BATCH_INFO"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="idAhcsBatch" column="ID_AHCS_BATCH"/>
        <result property="policyPayAmount" column="POLICY_PAY_AMOUNT"/>
        <result property="policyFee" column="POLICY_FEE"/>
        <result property="finalPayAmount" column="FINAL_PAY_AMOUNT"/>
        <result property="finalFee" column="FINAL_FEE"/>
        <result property="prePayAmount" column="PRE_PAY_AMOUNT"/>
        <result property="preFeeAmount" column="PRE_FEE_AMOUNT"/>
        <result property="migrateFrom" column="MIGRATE_FROM"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="accommodationAmount" column="ACCOMMODATION_AMOUNT"/>
        <result property="protocolAmount" column="PROTOCOL_AMOUNT"/>
        <result property="indemnityMode" column="INDEMNITY_MODE" />
    </resultMap>

    <select id="getSettleAmountsSum" resultMap="settleBatchInfoDTO">
        SELECT
        (SUM(IFNULL(POLICY_PAY,0))-SUM(IFNULL(POLICY_PRE_PAY,0))) FINAL_PAY_AMOUNT,
        SUM(IFNULL(POLICY_PAY,0)) POLICY_PAY_AMOUNT,
        (SUM(IFNULL(POLICY_SUM_FEE,0))) FINAL_FEE,
        (SUM(IFNULL(POLICY_SUM_FEE,0))+SUM(IFNULL(POLICY_PRE_FEE,0))) POLICY_FEE,
        SUM(IFNULL(POLICY_PRE_PAY,0)) PRE_PAY_AMOUNT,
        SUM(IFNULL(POLICY_PRE_FEE,0)) PRE_FEE_AMOUNT,
        (select concat (CB.INDEMNITY_CONCLUSION,CASE WHEN CB.INDEMNITY_MODEL= '1' THEN '' ELSE CB.INDEMNITY_MODEL END) as
                    INDEMNITY_MODE from CLM_WHOLE_CASE_BASE CB where CB.REPORT_NO=#{reportNo} and
             CB.CASE_TIMES=#{caseTimes}) INDEMNITY_MODE
        FROM CLM_POLICY_PAY A
        WHERE REPORT_NO=#{reportNo,jdbcType=VARCHAR}
        AND CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
        AND EXISTS(
        SELECT 1 FROM CLMS_POLICY_INFO B WHERE A.CASE_NO = B.CASE_NO
        AND A.POLICY_NO = B.POLICY_NO
        AND A.REPORT_NO = B.REPORT_NO
        )
    </select>

    <select id="getAcmAndProAmountSum" resultMap="settleBatchInfoDTO">
        select sum(IFNULL(a.protocol_amount,0)) PROTOCOL_AMOUNT,
               sum(IFNULL(a.accommodation_amount,0)) ACCOMMODATION_AMOUNT
        from CLMS_duty_detail_pay a where a.case_times=#{caseTimes,jdbcType=INTEGER} AND a.IS_EFFECTIVE = 'Y' and
        a.case_no in
        (select case_no from clm_policy_pay where
        clm_policy_pay.report_no=#{reportNo,jdbcType=VARCHAR} and
        clm_policy_pay.case_times =#{caseTimes,jdbcType=INTEGER}) and a.claim_type='1'
    </select>

    <select id="getPartSettleSum" resultMap="settleBatchInfoDTO">
        SELECT
        SUM(IFNULL(POLICY_PAY,0)) POLICY_PAY_AMOUNT,
        (SUM(IFNULL(POLICY_SUM_FEE,0))+SUM(IFNULL(DECREASE_FEE,0))) POLICY_FEE,
        SUM(IFNULL(POLICY_PRE_FEE,0)) PRE_FEE_AMOUNT
        FROM CLM_POLICY_PAY A
        WHERE REPORT_NO=#{reportNo,jdbcType=VARCHAR}
        AND CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
        AND CASE_NO !=#{caseNo,jdbcType=VARCHAR}
        AND EXISTS(
        SELECT 1 FROM CLMS_POLICY_INFO B WHERE A.CASE_NO = B.CASE_NO
        AND A.POLICY_NO = B.POLICY_NO
        AND A.REPORT_NO = B.REPORT_NO
        )
    </select>

    <select id="getPartAcmAndProSum" resultMap="settleBatchInfoDTO">
        select sum(IFNULL(a.protocol_amount,0)) PROTOCOL_AMOUNT,sum(IFNULL(a.accommodation_amount,0)) ACCOMMODATION_AMOUNT
        from CLMS_duty_detail_pay a where a.case_times=#{caseTimes,jdbcType=INTEGER} AND a.IS_EFFECTIVE = 'Y' and
        a.case_no in
        (select case_no from clm_policy_pay where
        clm_policy_pay.report_no=#{reportNo,jdbcType=VARCHAR}
        and clm_policy_pay.case_times =#{caseTimes,jdbcType=INTEGER}
        and case_no != #{caseNo,jdbcType=VARCHAR} ) and a.claim_type='1'
    </select>

    <select id="getSettleUser" resultType="java.lang.String">
        select SETTLE_USER_UM from CLM_BATCH d where d.report_no = #{reportNo} and d.case_times = #{caseTimes}
    </select>

    <select id="getSettleReviewRemark" resultType="java.lang.String">
        SELECT VERIFY_OPINION
        FROM CLMS_VERIFY V WHERE V.VERIFY_OPINION IS NOT NULL
        AND V.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        AND V.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
        ORDER BY V.VERIFY_DATE DESC
        LIMIT 1;
    </select>
    <select id="getSettleFee" resultMap="settleBatchInfoDTO">
        select sum
        from CLM_BATCH d where d.report_no = #{reportNo} and d.case_times = #{caseTimes}
    </select>

</mapper>