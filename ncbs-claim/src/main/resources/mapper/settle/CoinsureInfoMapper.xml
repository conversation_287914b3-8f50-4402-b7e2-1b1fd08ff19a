<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.CoinsureInfoMapper">

    <select id="getCoinsureCountByReportNo" resultType="java.lang.Integer">
        select count(1)
        from CLMS_COINSURE t
        where t.ID_AHCS_POLICY_INFO in
        (select ID_AHCS_POLICY_INFO
        from CLMS_POLICY_INFO
        where report_no = #{reportNo,jdbcType=VARCHAR})
    </select>

    <select id="getCoinsureDescByReportNo" resultType="com.paic.ncbs.claim.model.dto.settle.CoinsureDTO">
        select a.POLICY_NO              policyNo,
               b.ACCEPT_INSURANCE_FLAG  acceptInsuranceFlag,
               b.REINSURE_SCALE         reinsureScale
        from clms_policy_info a, clms_coinsure b
        where a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
        and a.DEPARTMENT_CODE = b.REINSURE_COMPANY_CODE
        and a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <select id="getCoinsureByPolicyNo" resultType="com.paic.ncbs.claim.model.dto.settle.CoinsureDTO">
        select
            b.COINSURANCE_TYPE       coinsuranceType,
            b.REINSURE_SCALE         reinsureScale,
            b.REINSURE_COMPANY_CODE  reinsureCompanyCode,
            b.REINSURE_COMPANY_NAME  reinsureCompanyName
        from  clms_coinsure b
        where b.ID_AHCS_POLICY_INFO = (select ID_AHCS_POLICY_INFO  from clms_policy_info  where POLICY_NO = #{policyNo,jdbcType=VARCHAR} order by CREATED_DATE  desc limit 1)
    </select>

    <select id="getCoinsureListByReportNo" resultType="com.paic.ncbs.claim.model.dto.settle.CoinsureDTO">
        select a.POLICY_NO       policyNo,
        b.COINSURANCE_TYPE       coinsuranceType,
        b.ACCEPT_INSURANCE_FLAG  acceptInsuranceFlag,
        b.REINSURE_SCALE         reinsureScale,
        b.REINSURE_COMPANY_CODE  reinsureCompanyCode,
        b.REINSURE_COMPANY_NAME  reinsureCompanyName,
        IF(a.DEPARTMENT_CODE = b.REINSURE_COMPANY_CODE, '1', '0') companyFlag
        from clms_policy_info a, clms_coinsure b
        where a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
        and a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>
    <select id="getMainCoinsureCountByReportNo" resultType="java.lang.Integer">
        select count(1) from clms_coinsure cc ,clms_policy_info cpi where
                cc.ID_AHCS_POLICY_INFO = cpi.ID_AHCS_POLICY_INFO
                and cpi.DEPARTMENT_CODE = cc.REINSURE_COMPANY_CODE
                and cc.ACCEPT_INSURANCE_FLAG ='1' and cpi.REPORT_NO = #{reportNo};
    </select>
    <select id="getCoinsList" resultType="com.paic.ncbs.claim.model.dto.settle.CoinsureDTO">
        select
                b.COINSURANCE_TYPE       coinsuranceType,
                b.ACCEPT_INSURANCE_FLAG  acceptInsuranceFlag,
                b.REINSURE_SCALE         reinsureScale,
                b.REINSURE_COMPANY_CODE  reinsureCompanyCode,
                b.REINSURE_COMPANY_NAME  reinsureCompanyName,
                IF(a.DEPARTMENT_CODE = b.REINSURE_COMPANY_CODE, '1', '0') companyFlag,
                a.policy_no
                from CLMS_COINSURE b,CLMS_POLICY_INFO a
                where b.ID_AHCS_POLICY_INFO = a.ID_AHCS_POLICY_INFO and a.REPORT_NO =#{reportNo};
    </select>
    <select id="selectPrintEntrustList" resultType="com.paic.ncbs.claim.model.dto.print.PrintEntrustCoinsDTO">
        select
                cpi.PAYMENT_TYPE printType,
                cc.REINSURE_COMPANY_NAME coinsComName,
                (select name from clms_insured_person cip where id_ahcs_policy_info = cpi2.ID_AHCS_POLICY_INFO) coinsName,
                cpi2.PRODUCT_NAME productName,
                cpi2.POLICY_NO policyNo,
                cpi.REPORT_NO reportNo,
                (select ACCIDENT_DATE  from clm_report_accident cra  where report_no = cpi.REPORT_NO) accidentDate,
                if(cpi.payment_type in ('11J','1J'),(select sum(if(cii.INVOICE_TYPE in('004','028','030'),cii.no_tax_amount,cii.total_amount ))  from clms_invoice_info cii
                where cii.id_ahcs_fee_pay in (select cfp.ID_AHCS_FEE_PAY
                from clms_fee_pay cfp
                where cfp.ID_CLM_PAYMENT_ITEM in (select cpi1.ID_CLM_PAYMENT_ITEM
                from clm_payment_item cpi1
                where cpi1.report_no=#{reportNo}))),SUM(CPI.chg_payment_amount)) as totalAmount,
                cc.REINSURE_SCALE reinsureScale,
                cpi2.department_code DepartmentCode
                from clm_payment_item cpi
                inner join clms_policy_info cpi2 on cpi.REPORT_NO = cpi2.REPORT_NO
                inner join clms_coinsure cc on cc.ID_AHCS_POLICY_INFO = cpi2.ID_AHCS_POLICY_INFO and cc.REINSURE_COMPANY_CODE != cpi2.DEPARTMENT_CODE
                where cpi.report_no = #{reportNo} and cpi.CASE_TIMES =#{caseTimes}
                <if test="claimType!=null and claimType==1">
                    and cpi.PAYMENT_TYPE in ('13','1J')
                </if>
                <if test="claimType!=null and claimType==2">
                    and cpi.PAYMENT_TYPE in ('11','11J')
                    <if test="subTimes!=null and subTimes !=''">
                        and cpi.sub_times = #{subTimes}
                    </if>
                </if>
                and cpi.IS_FULL_PAY ='1'
        group by coinscomname,PAYMENT_TYPE
    </select>

</mapper>