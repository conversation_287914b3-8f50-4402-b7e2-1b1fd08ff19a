<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper">

	<resultMap type = "com.paic.ncbs.claim.model.vo.settle.PolicyInfoVO" id = "policyInfoMap">
			<result column = "POLICY_STATUS" property = "policyStatus"/>
			<result column = "POLICY_NO" property = "policyNo"/>
			<result column = "department_code" property = "departmentCode"/>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.vo.settle.LifeInsuranceVO" id="LifeInsuranceVOResult">
	     <result column="REPORT_NO" property="reportNo"/>
	     <result column="CASE_TIMES" property="caseTimes"/>
	     <result column="CASE_STATE" property="caseState"/>
	     <result column="STATE" property="state"/>
	     <result column="AMOUNT" property="amount"/>
	     <result column="REPORT_DATE" property="reportDate"/>  
	</resultMap>
	
	<resultMap type ="com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO" id="policyNoMap">
	    <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo"/>
		<result column="POLICY_NO" property="policyNo"/>
        <result column="CASE_NO" property="caseNo" />
        <result column="POLICY_CER_NO" property="policyCerNo"/>
		<result column="GENERATE_FLAG" property="generateFlag" />
		<result column="BUSINESS_TYPE" property="businessType" />
		<result column="IS_UPLOAD_FLAT_SUCCESSED" property="isUploadFlatSuccessed"/>
		<result column="INSURANCE_BEGIN_TIME" property="insuranceBeginTime" />
		<result column="INSURANCE_END_TIME" property="insuranceEndTime" />
		<result column="DEPARTMENT_CODE" property="departmentCode" />
		<result column="TOTAL_INSURED_AMOUNT" property="totalInsuredAmount" />
		<result column="PRODUCT_CODE" property="productCode" />
		<result column="SYSTEM_ID" property="systemId" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO" id="policyInfoResultMap">
        <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo" />
        <result column="REPORT_NO" property="reportNo" />
        <result column="POLICY_NO" property="policyNo" />
        <result column="CASE_NO" property="caseNo" />
        <result column="ACCEPT_INSURANCE_DATE" property="acceptInsuranceDate" />
        <result column="DEPARTMENT_CODE" property="departmentCode" />
        <result column="INSURANCE_BEGIN_TIME" property="insuranceBeginTime" />
        <result column="INSURANCE_END_TIME" property="insuranceEndTime" />
        <result column="UNDERWRITE_DATE" property="underwriteDate" />
        <result column="POLICY_STATUS" property="policyStatus" />
        <result column="PRODUCT_CODE" property="productCode" />
        <result column="PRODUCT_NAME" property="productName" />
        <result column="PRODUCT_VERSION" property="productVersion" />
        <result column="PRODUCT_PACKAGE_TYPE" property="productPackageType" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="TOTAL_AGREE_PREMIUM" property="totalAgreePremium" />
        <result column="TOTAL_ACTUAL_PREMIUM" property="totalActualPremium" />
        <result column="AMOUNT_CURRENCY_CODE" property="amountCurrencyCode" />
        <result column="PREMIUM_CURRENCY_CODE" property="premiumCurrencyCode" />
        <result column="DATA_SOURCE" property="dataSource" />
        <result column="COINSURANCE_MARK" property="coinsuranceMark" />
        <result column="TOTAL_INSURED_AMOUNT" property="totalInsuredAmount" />
        <result column="REPLY_CODE" property="replyCode" />
        <result column="REPLY_NAME" property="replyName" />
        <result column="SYSTEM_ID" property="systemId" />
        <result column="ENDORSE_SYSTEM_ID" property="endorseSystemId" />
        <result column="SALE_AGENT_CODE" property="saleAgentCode" />
        <result column="SALE_AGENT_NAME" property="saleAgentName" />
        <result column="SOCIAL_FLAG" property="socialFlag" />
        <result column="AGREENMENT_DEFINE" property="agreenmentDefine" />
        <result column="BUSINESS_SOURCE_CODE" property="businessSourceCode" />
        <result column="BUSINESS_SOURCE_NAME" property="businessSourceName" />
        <result column="BUSINESS_SOURCE_DETAIL_CODE" property="businessSourceDetailCode" />
        <result column="BUSINESS_SOURCE_DETAIL_NAME" property="businessSourceDetailName" />
        <result column="CHANNEL_SOURCE_CODE" property="channelSourceCode" />
        <result column="CHANNEL_SOURCE_NAME" property="channelSourceName" />
        <result column="CHANNEL_SOURCE_DETAIL_CODE" property="channelSourceDetailCode" />
        <result column="CHANNEL_SOURCE_DETAIL_NAME" property="channelSourceDetailName" />
        <result column="VIRTUAL_TARGET_NUM" property="virtualTargetNum" />
        <result column="APPLY_DATE" property="applyDate" />
        <result column="TRAFFIC_NO" property="trafficNo" />
        <result column="PAY_TERM_NO" property="payTermNo" />
        <result column="IS_POLICY_BEFORE_PAY_FEE" property="isPolicyBeforePayFee" />
        <result column="POLICY_EXTEND" property="policyExtend" />
        <result column="ORG_PRODUCT_CODE" property="orgProductCode" />
        <result column="PRODUCT_CLASS" property="productClass" />
    </resultMap>

	<resultMap type = "com.paic.ncbs.claim.model.vo.settle.PolicyInfoVO" id = "resultPolicyInfo">
		<result column = "POLICY_NO" property = "policyNo"/>
		<result column = "PRODUCT_CODE" property = "productCode"/>
		<result column = "PRODUCT_NAME" property = "productName"/>
		<result column = "REMARK" property = "remark"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.openapi.PolicyVO" id="openPolicyInfoMap">
		<result column="POLICY_NO" property="policyNo" />
		<result column="DEPARTMENT_CODE" property="departmentCode" />
		<result column="INSURANCE_BEGIN_TIME" property="insuranceBeginTime" />
		<result column="INSURANCE_END_TIME" property="insuranceEndTime" />
		<result column="POLICY_STATUS" property="policyStatus" />
		<result column="PRODUCT_CODE" property="productCode" />
		<result column="PRODUCT_NAME" property="productName" />
		<result column="PRODUCT_VERSION" property="productVersion" />
		<result column="BUSINESS_TYPE" property="businessType" />
		<result column="TOTAL_INSURED_AMOUNT" property="sumAmount" />
		<result column="IS_FACULTATIVE_BUSINESS" property="isFacultativeBusiness" />
		<result column="COINSURANCE_MARK" property="coinsuranceMark" />
		<collection property="planList" resultMap="openPlanMap" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.openapi.PlanVO" id="openPlanMap">
		<result column="PLAN_CODE" property="planCode" />
		<result column="PLAN_NAME" property="planName" />
		<result column="TAX_RATE" property="taxRate" />
		<collection property="dutyList" resultMap="openDutyMap" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.openapi.DutyVO" id="openDutyMap">
		<result column="DUTY_CODE" property="dutyCode" />
		<result column="DUTY_NAME" property="dutyName" />
	</resultMap>

	<select id="getPolicyStatus" resultMap="policyInfoMap">
		 select *
		 from clms_policy_info t
		 where t.report_no=#{reportNo}
	</select>

	<select id="getPolicyInfo" resultMap="resultPolicyInfo">
		select
		t.policy_no,
		t.product_code,
		t.product_name,
		t.remark
		from
		clms_policy_info t
		where t.report_no=#{reportNo}
	</select>

	<select id="getPolicyintoList" resultMap="policyInfoMap"> 
		select distinct
			pi.policy_no,
		    pi.department_code
		from CLMS_POLICY_INFO pi
	    where REPORT_NO=#{reportNo}
	</select>

	<select id="getPolicyAndCustomerInfoList"
			resultType="com.paic.ncbs.claim.model.vo.settle.PolicyAndCustomerInfoVO">
		SELECT pi.policy_no policyNo,
		   pi.policy_cer_no policyCerNo,
           case pi.policy_status when '01' then '退保' when '02' then '注销' when '03' then '退保恢复' when '04' then '契撤' when 'B5' then '在保' when 'P' then '缴清' when 'S' then '已退保' when 'C' then '已注销' when 'P1' THEN '承保' else policy_status end as "policyStatusName",
	       pi.insurance_begin_time insuranceBeginTime,
	       pi.insurance_end_time insuranceEndTime,
	       pi.insurance_begin_time insuredBeginTime,
	       pi.insurance_end_time insuredEndTime,
	       ph.name insuranceApplicantName,
	       rc.CLIENT_NO clientNo,
	       rc.NAME clientName,
		   rc.CERTIFICATE_TYPE clientPersonnelType,
		   rc.CERTIFICATE_NO clientIdNo,
	       pi.department_code departmentCode,
        (select replace(concat(d.department_abbr_name), ',', '') from department_define d
        where d.department_code = pi.department_code) departmentName,
		PI.product_code productCode,
		PI.product_name productName,
		PI.PRODUCT_PACKAGE_TYPE productPackageType,
		(SELECT rg.RISK_GROUP_NAME FROM ply_risk_group rg
		WHERE rg.POLICY_NO = PI.POLICY_NO AND rg.PRODUCT_PACKAGE_TYPE = PI.PRODUCT_PACKAGE_TYPE
		order by rg.created_date desc limit 1) riskGroupName,
		PI.total_insured_amount totalInsuredAmount
		FROM CLMS_POLICY_INFO PI, CLMS_POLICY_HOLDER PH, CLMS_REPORT_CUSTOMER RC
	 WHERE PI.REPORT_NO = RC.REPORT_NO
	   AND PI.ID_AHCS_POLICY_INFO = PH.ID_AHCS_POLICY_INFO
	   AND PI.REPORT_NO = #{reportNo}    order by PI.Case_No
	</select>

	<select id="getPolicyInfoListByReportNo" resultMap="policyInfoResultMap">
		SELECT T.CREATED_BY,
		       T.CREATED_DATE,
		       T.UPDATED_BY,
		       T.UPDATED_DATE,
		       T.ID_AHCS_POLICY_INFO,
		       T.REPORT_NO,
		       T.POLICY_NO,
		       T.ACCEPT_INSURANCE_DATE,
		       T.DEPARTMENT_CODE,
		       T.INSURANCE_BEGIN_TIME,
		       T.INSURANCE_END_TIME,
		       T.UNDERWRITE_DATE,
		       T.POLICY_STATUS,
		       T.PRODUCT_CODE,
		       T.PRODUCT_NAME,
		       T.BUSINESS_TYPE,
		       T.TOTAL_AGREE_PREMIUM,
		       T.TOTAL_ACTUAL_PREMIUM,
		       T.AMOUNT_CURRENCY_CODE,
		       T.PREMIUM_CURRENCY_CODE,
		       T.DATA_SOURCE,
		       T.COINSURANCE_MARK,
		       T.TOTAL_INSURED_AMOUNT,
		       T.REPLY_CODE,
		       T.REPLY_NAME,
		       T.SYSTEM_ID,
		       T.ENDORSE_SYSTEM_ID,
		       T.SALE_AGENT_CODE,
		       T.SALE_AGENT_NAME,
		       T.SOCIAL_FLAG,
		       T.AGREENMENT_DEFINE,
		       T.BUSINESS_SOURCE_CODE,
		       T.BUSINESS_SOURCE_NAME,
		       T.BUSINESS_SOURCE_DETAIL_CODE,
		       T.BUSINESS_SOURCE_DETAIL_NAME,
		       T.CHANNEL_SOURCE_CODE,
		       T.CHANNEL_SOURCE_NAME,
		       T.CHANNEL_SOURCE_DETAIL_CODE,
		       T.CHANNEL_SOURCE_DETAIL_NAME,
		       T.VIRTUAL_TARGET_NUM,
		       T.POLICY_VALID,
		       T.PRODUCT_VERSION,
		       T.ENDORSE_NO,
		       T.EDR_EFFECTIVE_DATE,
		       T.APPLY_DATE,
		       T.PRODUCT_PACKAGE_TYPE,
		       T.SUBJECT_ID,
		       T.BATCH_NO,
		       T.CASE_NO,
		       T.APPLY_DATE,
		       T.TRAFFIC_NO,
		       T.POLICY_EXTEND,
		       T.PAY_TERM_NO,
		       T.IS_POLICY_BEFORE_PAY_FEE,
		       T.ORG_PRODUCT_CODE
		FROM   CLMS_POLICY_INFO T WHERE T.REPORT_NO = #{reportNo}
	</select>

	<select id="getPolicyInfoListByDutyCode" resultMap="policyInfoResultMap">
		SELECT T.CREATED_BY,
		       T.CREATED_DATE,
		       T.UPDATED_BY,
		       T.UPDATED_DATE,
		       T.ID_AHCS_POLICY_INFO,
		       T.REPORT_NO,
		       T.POLICY_NO,
		       T.ACCEPT_INSURANCE_DATE,
		       T.DEPARTMENT_CODE,
		       T.INSURANCE_BEGIN_TIME,
		       T.INSURANCE_END_TIME,
		       T.UNDERWRITE_DATE,
		       T.POLICY_STATUS,
		       T.PRODUCT_CODE,
		       T.PRODUCT_NAME,
		       T.BUSINESS_TYPE,
		       T.TOTAL_AGREE_PREMIUM,
		       T.TOTAL_ACTUAL_PREMIUM,
		       T.AMOUNT_CURRENCY_CODE,
		       T.PREMIUM_CURRENCY_CODE,
		       T.DATA_SOURCE,
		       T.COINSURANCE_MARK,
		       T.TOTAL_INSURED_AMOUNT,
		       T.REPLY_CODE,
		       T.REPLY_NAME,
		       T.SYSTEM_ID,
		       T.ENDORSE_SYSTEM_ID,
		       T.SALE_AGENT_CODE,
		       T.SALE_AGENT_NAME,
		       T.SOCIAL_FLAG,
		       T.AGREENMENT_DEFINE,
		       T.BUSINESS_SOURCE_CODE,
		       T.BUSINESS_SOURCE_NAME,
		       T.BUSINESS_SOURCE_DETAIL_CODE,
		       T.BUSINESS_SOURCE_DETAIL_NAME,
		       T.CHANNEL_SOURCE_CODE,
		       T.CHANNEL_SOURCE_NAME,
		       T.CHANNEL_SOURCE_DETAIL_CODE,
		       T.CHANNEL_SOURCE_DETAIL_NAME,
		       T.VIRTUAL_TARGET_NUM,
		       T.POLICY_VALID,
		       T.PRODUCT_VERSION,
		       T.ENDORSE_NO,
		       T.EDR_EFFECTIVE_DATE,
		       T.APPLY_DATE,
		       T.PRODUCT_PACKAGE_TYPE,
		       T.SUBJECT_ID,
		       T.BATCH_NO,
		       T.CASE_NO,
		       T.APPLY_DATE,
		       T.TRAFFIC_NO,
		       T.POLICY_EXTEND
		FROM   CLMS_POLICY_INFO T , CLMS_POLICY_PLAN B, CLMS_POLICY_DUTY C
		WHERE T.ID_AHCS_POLICY_INFO = B.ID_AHCS_POLICY_INFO
             AND B.ID_AHCS_POLICY_PLAN = C.ID_AHCS_POLICY_PLAN
             and T.REPORT_NO = #{reportNo}
             and C.DUTY_CODE = #{dutyCode}
             order by C.DUTY_AMOUNT desc
	</select>

	<select id="getEHisPolicies"  resultMap="policyInfoResultMap">
        SELECT T.CREATED_BY,
        T.CREATED_DATE,
        T.UPDATED_BY,
        T.UPDATED_DATE,
        T.ID_AHCS_POLICY_INFO,
        T.REPORT_NO,
        T.POLICY_NO,
        T.ACCEPT_INSURANCE_DATE,
        T.DEPARTMENT_CODE,
        T.INSURANCE_BEGIN_TIME,
        T.INSURANCE_END_TIME,
        T.UNDERWRITE_DATE,
        T.POLICY_STATUS,
        T.PRODUCT_CODE,
        T.PRODUCT_NAME,
        T.BUSINESS_TYPE,
        T.TOTAL_AGREE_PREMIUM,
        T.TOTAL_ACTUAL_PREMIUM,
        T.AMOUNT_CURRENCY_CODE,
        T.PREMIUM_CURRENCY_CODE,
        T.DATA_SOURCE,
        T.COINSURANCE_MARK,
        T.TOTAL_INSURED_AMOUNT,
        T.REPLY_CODE,
        T.REPLY_NAME,
        T.SYSTEM_ID,
        T.ENDORSE_SYSTEM_ID,
        T.SALE_AGENT_CODE,
        T.SALE_AGENT_NAME,
        T.SOCIAL_FLAG,
        T.AGREENMENT_DEFINE,
        T.BUSINESS_SOURCE_CODE,
        T.BUSINESS_SOURCE_NAME,
        T.BUSINESS_SOURCE_DETAIL_CODE,
        T.BUSINESS_SOURCE_DETAIL_NAME,
        T.CHANNEL_SOURCE_CODE,
        T.CHANNEL_SOURCE_NAME,
        T.CHANNEL_SOURCE_DETAIL_CODE,
        T.CHANNEL_SOURCE_DETAIL_NAME,
        T.VIRTUAL_TARGET_NUM,
        T.POLICY_VALID,
        T.PRODUCT_VERSION,
        T.ENDORSE_NO,
        T.EDR_EFFECTIVE_DATE,
        T.APPLY_DATE,
        T.PRODUCT_PACKAGE_TYPE,
        T.SUBJECT_ID,
        T.BATCH_NO,
        T.CASE_NO,
        T.APPLY_DATE
        FROM   CLMS_POLICY_INFO T WHERE T.REPORT_NO = #{reportNo}
        and T.PRODUCT_CODE IN
        <foreach collection="prodCodeList" index="index" item="prodCode" open="(" separator="," close=")">
            #{prodCode}
        </foreach>
	</select>

 	<select id="getInsuredPersonCerNo" resultType="String"> 
	 	 select ip.certificate_no
		   from CLMS_insured_person ip,clms_policy_info pi
		  where ip.id_ahcs_policy_info = pi.id_ahcs_policy_info
		    and pi.report_no = #{reportNo} 
		    limit 1
	</select>
	
	<select id="getLifeInsuranceInfo" resultMap="LifeInsuranceVOResult">  
		SELECT TT.REPORT_NO AS REPORT_NO,
		       TT.CASE_TIMES AS CASE_TIMES,
		       CRI.REPORT_DATE AS REPORT_DATE,
		       TT.WHOLE_CASE_STATUS AS STATE,
		       CP.PROCESS_STATUS AS CASE_STATE,
		       ifnull((SELECT SUM(POLICY_PAY) POLICY_PAY_AMOUNT
               FROM   CLM_POLICY_PAY P
               WHERE  P.REPORT_NO = TT.REPORT_NO
               AND    P.CASE_TIMES = TT.CASE_TIMES),
               0) AS AMOUNT
		FROM   CLM_WHOLE_CASE_BASE TT, CLM_REPORT_INFO CRI, CLMS_CASE_PROCESS CP
		WHERE  TT.REPORT_NO = CRI.REPORT_NO
		AND    TT.REPORT_NO = CP.REPORT_NO
		AND    TT.CASE_TIMES = CP.CASE_TIMES
		AND    TT.REPORT_NO = #{reportNo}
		AND    TT.CASE_TIMES = #{caseTimes}
		AND    EXISTS (SELECT 1
		        FROM   CLMS_POLICY_INFO PI
		        WHERE  TT.REPORT_NO = PI.REPORT_NO
		        AND    PI.CHANNEL_SOURCE_CODE = '7'
		        AND    PI.CHANNEL_SOURCE_DETAIL_CODE IN ('D', 'L')
		        AND    TT.REPORT_NO = #{reportNo}
		        AND    TT.CASE_TIMES = #{caseTimes})
	</select>

    <!-- 根据保单id获取电子保单号和大保单号 -->	
	<select id="getPolicyCerNoById" parameterType="string" resultMap="policyNoMap"> 
	 	 select pi.ID_AHCS_POLICY_INFO,
	 	        pi.POLICY_CER_NO,
	 	        pi.POLICY_NO,
	 	        pi.CASE_NO,
	            pi.IS_UPLOAD_FLAT_SUCCESSED
		   from CLMS_POLICY_INFO pi
		  where pi.ID_AHCS_POLICY_INFO = #{idPolicyInfo} 
		        limit 1
	</select>
	
	<!-- 根据报案号、赔案id获取保单信息表id -->
	<select id="getIdPolicyInfo" parameterType="string" resultMap="policyNoMap">
	   select pi.ID_AHCS_POLICY_INFO,
	          pi.POLICY_CER_NO,
	          pi.POLICY_NO,
              pi.CASE_NO,
              pi.IS_UPLOAD_FLAT_SUCCESSED,
	          pi.GENERATE_FLAG    
	     from CLMS_policy_info pi
	    where pi.policy_no = 
	          (select c.policy_no from clm_case_base c where c.id_clm_case_base=#{idClmCaseBase} limit 1)
	      and pi.report_no=#{reportNo}
	      limit 1
	</select>
	
	<!-- 根据赔案号、赔付次数获取电子保单号 -->
	<select id="getIdPolicyInfoByCaseNo" parameterType="string" resultMap="policyNoMap">
	   select pi.ID_AHCS_POLICY_INFO,
	          pi.POLICY_CER_NO,
	          pi.POLICY_NO,
              pi.CASE_NO,
	          pi.IS_UPLOAD_FLAT_SUCCESSED,
	          pi.GENERATE_FLAG,
	          pi.BUSINESS_TYPE,
		      pi.SYSTEM_ID,
	          pi.PRODUCT_CODE 
	     from CLMS_policy_info pi
	    where pi.report_no = #{reportNo} 
	      and pi.CASE_NO = #{caseNo} 
	      limit 1
	</select>
	
	<!-- 根据赔案号、报案号获取保单起止期 -->
	<select id="getPolicyValidityPeriod" parameterType="string" resultMap="policyNoMap">
	   select pi.INSURANCE_BEGIN_TIME,
	          pi.INSURANCE_END_TIME,
	          pi.POLICY_NO,
              pi.CASE_NO,
	          pi.POLICY_CER_NO,
	          pi.DEPARTMENT_CODE,
	          pi.ID_AHCS_POLICY_INFO,
	          pi.TOTAL_INSURED_AMOUNT,
	          pi.BUSINESS_TYPE,
	          pi.PRODUCT_CODE    
	     from CLMS_policy_info pi
	    where pi.report_no = #{reportNo} 
	      and pi.CASE_NO = #{caseNo} 
	      limit 1
	</select>
	
	<!-- 报案：获取赔案保额大于0的所有险种 -->
	<select id="getReportPlanByCaseNo" resultType="string">
	    select pp.plan_code 
		  from CLMS_policy_info pi,
		       CLMS_policy_plan pp
		 where pi.id_ahcs_policy_info = pp.id_ahcs_policy_info
			   and pi.report_no = #{reportNo} 
			   and pi.case_no = #{caseNo} 
		   <if test="totalInsuredAmount != null">
	 <![CDATA[ and pi.TOTAL_INSURED_AMOUNT > #{totalInsuredAmount}  ]]>
	       </if>
	</select>
	
	<!-- 结案：获取赔案赔款大于0的所有险种 -->
	<select id="getEndCasePlanByCaseNo" resultType="string">
	    select PLAN_CODE 
		  from CLM_PLAN_PAY 
		 where CASE_NO = #{caseNo}
			   and CASE_TIMES = #{caseTimes} 
		 	   and CLAIM_TYPE = '1' 
	 <![CDATA[ and PLAN_PAY_AMOUNT > 0  ]]>
	</select>
	
	<!--通过报案号、赔付次数、机构查询要上传的案件的保单机构 -->
	<select id="getDeptListByUploadCase" resultMap="policyNoMap">
        select pi.DEPARTMENT_CODE, pi.ID_AHCS_POLICY_INFO
		  from CLMS_policy_info pi
		 where pi.report_no = #{reportNo} 
		       and (select t.parent_department_code
		              from department_define s, department_relation t
			         where s.department_code = t.parent_department_code
				           and t.department_level = '2'
				           and t.child_department_code = pi.DEPARTMENT_CODE
				           limit 1) = #{level2Dept}
	</select>
	
	<!-- 获取保单的承保机构 -->
	<select id="getPolicyDeptByCaseNo" parameterType="string" resultType="string"> 
	   select pi.department_code
		 from CLMS_policy_info pi
	    where pi.report_no = #{reportNo}
	          and pi.case_no = #{caseNo}
	          limit 1
	</select>

	<!-- 获取保单的承保机构 -->
	<select id="getPolicyDeptByReportNo" parameterType="string" resultType="string">
		select pi.department_code
		from CLMS_policy_info pi
		where pi.report_no = #{reportNo}
		limit 1
	</select>
	
	<!-- 获取赔案的产品代码 -->
    <select id="getProductCodeByCaseNo" resultType="string">
        select pi.PRODUCT_CODE 
          from CLMS_POLICY_INFO pi
         where pi.REPORT_NO = #{reportNo}
               and pi.CASE_NO in 
               <foreach collection="caseNoList" index="index" item="caseNo" open="(" separator="," close=")">    
		     	    #{caseNo}
		       </foreach>
		       and pi.POLICY_VALID = 'Y'   
    </select>
    
    	<!-- 获取赔案的保单号 -->
    <select id="getPolicyNoByCaseNo" resultType="string">
        select pi.POLICY_NO 
          from CLMS_POLICY_INFO pi
         where pi.REPORT_NO = #{reportNo}
               and pi.CASE_NO in 
               <foreach collection="caseNoList" index="index" item="caseNo" open="(" separator="," close=")">    
		     	    #{caseNo}
		       </foreach>
		       and pi.POLICY_VALID = 'Y'   
    </select>
    
    <!-- 获取案件是否存在E生保保单 -->
    <select id="getCaseNoByReportProduct" resultType="string">
        select pi.CASE_NO 
          from CLMS_POLICY_INFO pi
         where pi.REPORT_NO = #{reportNo}
               and pi.PRODUCT_CODE in 
               <foreach collection="productCodeList" index="index" item="productCode" open="(" separator="," close=")">    
		     	    #{productCode}
		       </foreach>   
		       and pi.POLICY_VALID = 'Y'   
    </select>
    
    <!-- 获取E生保的保单号 -->
    <select id="getPolicyNoByProductCode" resultType="string">
        select distinct pi.POLICY_NO 
          from CLMS_POLICY_INFO pi
         where pi.REPORT_NO = #{reportNo}
               and pi.PRODUCT_CODE in 
               <foreach collection="productCodeList" index="index" item="productCode" open="(" separator="," close=")">    
		     	    #{productCode}
		       </foreach>   
		       and pi.POLICY_VALID = 'Y'   
    </select>

	<!-- 根据报案号取一个保单号 -->
	<select id="getRandomPolicyByReport" parameterType="string" resultType="string">
	   select POLICY_NO from CLMS_policy_info pi where pi.report_no = #{reportNo}  limit 1
	</select>

	<!--获取当前赔案的报案保单信息-->
	<select id="getReportPolicyInfoByCaseNo" resultType="com.paic.ncbs.claim.model.dto.report.ReportPolicyInfoDTO">
        select t1.policy_no as policyNo,
        t1.policy_cer_no as policyCerNo,
        t1.report_no as reportNo,
        t1.case_no as caseNo,
        t3.client_no as clientNo
        from CLMS_POLICY_INFO t1,
        CLMS_INSURED_PERSON  t2,
        CLMS_REPORT_CUSTOMER t3
        where case_no = #{caseNo}
        and t1.ID_AHCS_POLICY_INFO = t2.ID_AHCS_POLICY_INFO
        and t1.report_no = t3.report_no
        and t2.client_no = t3.client_no
    </select>

	<!--获取历史结案的的报案保单信息-->
	<select id="getHisCaseNoList" resultType="java.lang.String">
		select distinct t1.case_no
		from CLMS_policy_info t1,
		clms_insured_person t2,
		clm_whole_case_base t3
		where
		t1.policy_no=#{policyNo}
		<if test="policyCerNo!=null and policyCerNo!=''">
			and t1.policy_cer_no=#{policyCerNo}
		</if>
		and t1.id_ahcs_policy_info=t2.id_ahcs_policy_info
		and t2.client_no=#{clientNo}
		and t3.report_no=t1.report_no
		and t3.whole_case_status='0'
	</select>

	<select id="getPolicyDeptName" parameterType="string"  resultType="string" >
		select
		(select d.department_abbr_name from department_define d where d.department_code=t2.parent_department_code) department_abbr_name
		from CLMS_policy_info t1,department_relation t2
		where t1.department_code = t2.child_department_code and  t2.department_level = '2'
		and t1.report_no = #{reportNo}
		and t1.case_no = #{caseNo}
		limit 1
	</select>
	
	<select id="getPolicyNo" parameterType="java.lang.String" resultType="java.lang.String">
		select policy_no
		from clms_policy_info
		where report_no = #{reportNo,jdbcType=VARCHAR}
	</select>

	<select id="getByPolicyNo" parameterType="java.lang.String" resultMap="policyInfoResultMap">
		select *
		from clms_policy_info
		where policy_no = #{policyNo,jdbcType=VARCHAR}
	</select>

	<select id="getAccidentDateStr" parameterType="java.lang.String" resultType="java.lang.String">
		select date_format(c.ACCIDENT_DATE ,'%Y-%m-%d')
		from clms_policy_info a,clms_report_customer b ,clm_report_accident c
		where a.REPORT_NO = b.REPORT_NO
		and b.REPORT_NO = c.REPORT_NO
		and a.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		and b.CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
		and b.NAME = #{name,jdbcType=VARCHAR}
	</select>

	<select id="getOpenPolicyInfoList" resultMap="openPolicyInfoMap">
		SELECT  p.POLICY_NO,
				p.DEPARTMENT_CODE,
				p.INSURANCE_BEGIN_TIME,
				p.INSURANCE_END_TIME,
				p.POLICY_STATUS,
				p.PRODUCT_CODE,
				p.PRODUCT_NAME,
				p.PRODUCT_VERSION,
				p.BUSINESS_TYPE,
				p.TOTAL_INSURED_AMOUNT,
				p.IS_FACULTATIVE_BUSINESS,
				p.COINSURANCE_MARK,
				pp.PLAN_CODE,
				pp.PLAN_NAME,
				pp.TAX_RATE,
				pd.DUTY_CODE,
				pd.DUTY_NAME
		FROM CLMS_POLICY_INFO p
		LEFT JOIN CLMS_POLICY_PLAN pp ON p.ID_AHCS_POLICY_INFO=pp.ID_AHCS_POLICY_INFO
		LEFT JOIN CLMS_POLICY_DUTY pd ON pp.ID_AHCS_POLICY_PLAN=pd.ID_AHCS_POLICY_PLAN
		WHERE p.REPORT_NO = #{reportNo}
	</select>

	<select id="getCompositePolicy" resultType="com.paic.ncbs.claim.model.vo.openapi.CompositePolicyVO">
		select a.POLICY_NO,a.PRODUCT_CODE,b.PLAN_CODE,c.DUTY_CODE,d.ATTRIBUTE_CODE,d.ATTRIBUTE_VALUE,d.ID_AHCS_DUTY_ATTRIBUTE as dutyAttributeId
		from clms_policy_info a,clms_policy_plan b,clms_policy_duty c,clms_duty_attribute d
		where a.ID_AHCS_POLICY_INFO =b.ID_AHCS_POLICY_INFO
		and b.ID_AHCS_POLICY_PLAN =c.ID_AHCS_POLICY_PLAN
		and c.ID_AHCS_POLICY_DUTY =d.ID_AHCS_POLICY_DUTY
		and a.REPORT_NO = #{reportNo}
	</select>

	<select id="getCompositePolicyByPolicyNo"
			resultType="com.paic.ncbs.claim.model.vo.openapi.CompositePolicyVO">
		select a.POLICY_NO,a.PRODUCT_CODE,b.PLAN_CODE,c.DUTY_CODE,d.ATTRIBUTE_CODE,d.ATTRIBUTE_VALUE,d.ID_AHCS_DUTY_ATTRIBUTE as dutyAttributeId,a.POLICY_NO,c.DUTY_CODE
		from clms_policy_info a,clms_policy_plan b,clms_policy_duty c,clms_duty_attribute d
		where a.ID_AHCS_POLICY_INFO =b.ID_AHCS_POLICY_INFO
		and b.ID_AHCS_POLICY_PLAN =c.ID_AHCS_POLICY_PLAN
		and c.ID_AHCS_POLICY_DUTY =d.ID_AHCS_POLICY_DUTY
		and a.POLICY_NO = #{policyNo}
		and a.REPORT_NO=#{reportNo}
	</select>

	<select id="getPackageType"  resultType="java.lang.String">
		select PRODUCT_PACKAGE_TYPE productPackageType
			from clms_policy_info
		where REPORT_NO=#{reportNo}
		and POLICY_NO = #{policyNo}
	</select>
    <select id="getPrudocutInfo" resultType="com.paic.ncbs.claim.model.dto.ocas.ProductInfoDTO">
		select product_class
				from clms_policy_info where POLICY_NO =#{policyNo} order by CREATED_DATE  desc limit 1
	</select>
    <select id="getPlanDutyList" resultType="com.paic.ncbs.claim.model.dto.copypolicy.ClaimReserveItem">
		select DUTY_CODE agrmRiskCode ,'50000' mnpvCls,nvl(CHG_DUTY_PAY_AMOUNT,DUTY_PAY_AMOUNT )  osOriginalMny   from clm_plan_duty_pay cpdp ,clms_policy_info cpi
				where cpdp.CASE_NO = cpi.CASE_NO and cpdp.CASE_TIMES =#{caseTimes} and cpi.REPORT_NO =#{reportNo} and DUTY_PAY_AMOUNT > 0
				and CLAIM_TYPE =#{claimType}
		<if test="subTimes != null and subTimes!='0'.toString()">
			and SUB_TIMES =#{subTimes}
		</if>
				union all
				select cfp.DUTY_CODE,'70002',cii.no_tax_amount from clms_fee_pay cfp ,clms_invoice_info cii
				where cii.ID_AHCS_FEE_PAY = cfp.ID_AHCS_FEE_PAY and cfp.REPORT_NO =#{reportNo} and CASE_TIMES =#{caseTimes}
				and CLAIM_TYPE =#{claimType}
		<if test="subTimes != null and subTimes!='0'.toString()">
			and SUB_TIMES =#{subTimes}
		</if>
	</select>

</mapper>