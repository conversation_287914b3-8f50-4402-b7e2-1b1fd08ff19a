<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.fee.FeePayDTO" id="result4">
		<result column="report_No" property="reportNo" />
		<result column="policy_No" property="policyNo" />
		<result column="product_code" property="productCode" />
		<result column="policy_cer_No" property="policyCerNo" />
		<result column="department_Name" property="departmentName" />
		<result column="case_No" property="caseNo" />
		<result column="case_Times" property="caseTimes" />
		<result column="claim_type" property="claimType" />
		<result column="sub_times" property="subTimes" />

		<collection property="feeInfos"
                    ofType="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO" column="CASE_NO">
			<result column="fee_Amount" property="feeAmount" />
			<result column="fee_Type" property="feeType" />
			<result column="id_ahcs_fee_pay" property="idAhcsFeePay" />
			<result column="id_Clm_Payment_Info" property="idClmPaymentInfo" />
			<result column="CLIENT_NAME" property="clientName" />
			<result column="INVOICE_NO" property="invoiceNo" />
            <result column="id_Clm_Payment_Item" property="idClmPaymentItem" />
			<result column="isFullPay" property="isFullPay" />
			<result column="dutyCode" property="dutyCode" />
			<!--<association column="{id_ahcs_fee_pay}"
						 property="invoiceInfo"
						 javaType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO"
						 select="getInvoiceInfo"/>-->
		</collection>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO" id="feeInfoDTOMap">
			<result property="createdBy" column="CREATED_BY" />
			<result property="createdDate" column="CREATED_DATE" />
			<result property="updatedBy" column="UPDATED_BY" />
			<result property="updatedDate" column="UPDATED_DATE" />
			<result property="idAhcsFeePay" column="ID_AHCS_FEE_PAY" />
			<result property="reportNo" column="REPORT_NO" />
			<result property="caseTimes" column="CASE_TIMES" />
			<result column="CASE_NO" property="caseNo" />
			<result column="POLICY_NO" property="policyNo" />
			<result column="FEE_TYPE" property="feeType" />
			<result column="FEE_AMOUNT" property="feeAmount" />
			<result column="ID_CLM_PAYMENT_INFO" property="idClmPaymentInfo" />
			<result column="CLIENT_NAME" property="clientName" />
			<result column="IS_EFFECTIVE" property="isEffective" />
			<result column="CLAIM_TYPE" property="claimType" />
			<result column="ID_CLM_PAYMENT_ITEM" property="idClmPaymentItem" />
			<result column="SUB_TIMES" property="subTimes" />
			<result column="LOSS_OBJECT_NO" property="lossObjectNo" />
			<result column="INVOICE_NO" property="invoiceNo" />
			<result column="isFullPay" property="isFullPay" />
			<result column="dutyCode" property="dutyCode" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO" id="feeInfoDTOAndInvoice">
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsFeePay" column="ID_AHCS_FEE_PAY" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result column="CASE_NO" property="caseNo" />
		<result column="POLICY_NO" property="policyNo" />
		<result column="FEE_TYPE" property="feeType" />
		<result column="FEE_AMOUNT" property="feeAmount" />
		<result column="ID_CLM_PAYMENT_INFO" property="idClmPaymentInfo" />
		<result column="CLIENT_NAME" property="clientName" />
		<result column="IS_EFFECTIVE" property="isEffective" />
		<result column="CLAIM_TYPE" property="claimType" />
		<result column="ID_CLM_PAYMENT_ITEM" property="idClmPaymentItem" />
		<result column="SUB_TIMES" property="subTimes" />
		<result column="LOSS_OBJECT_NO" property="lossObjectNo" />
		<result column="INVOICE_NO" property="invoiceNo" />
	<!--	<association column="id_ahcs_fee_pay"
					 property="invoiceInfo"
					 javaType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO"
					 select="getInvoiceInfo"/>-->
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.fee.CaseFeePayDTO" id="caseFeePay">
		<result column="FEE_AMOUNT" property="feeAmountSum" />
			<result column="POLICY_NO" property="policyNo" />
			<result column="CASE_NO" property="caseNo" />
			<result column="CLAIM_TYPE" property="claimType" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.settle.BatchDTO" id="batchDTOMap">
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="idAhcsBatch" column="ID_CLM_BATCH" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="settleUserUm" column="SETTLE_USER_UM" />
		<result property="settleTime" column="SETTLE_TIME" />
		<result property="settleStatus" column="SETTLE_STATUS" />
		<result property="batchSettleType" column="BATCH_SETTLE_TYPE" />
		<result property="migrateFrom" column="MIGRATE_FROM" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.fee.FeePayDTO" id="feePayMap">
		<result column="FEE_TYPE" property="feeType" />
		<result column="FEE_AMOUNT" property="feeAmount" />
		<result column="FEE_TYPE_NAME" property="feeTypeName" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.fee.FeePayDTO" id="result3">
		<result column="report_No" property="reportNo" />
		<result column="policy_No" property="policyNo" />
		<result column="product_code" property="productCode" />
		<result column="POLICY_CER_NO" property="policyCerNo" />
		<result column="department_Name" property="departmentName" />
		<result column="case_Times" property="caseTimes" />
		<result column="case_No" property="caseNo" />
		<result column="department_code" property="departmentCode" />
	</resultMap>

	<select id="getFeePays" resultMap="result4">
		select t2.report_no,
	       t1.policy_no,
           t1.product_code,
	       t1.policy_cer_no,
	       (select department_abbr_name from department_define t3 where t3.department_code=t1.department_code) as department_name,
	       t2.case_no,
	       t2.case_times,
	       t2.fee_amount,
	       t2.fee_type,
	       t2.id_ahcs_fee_pay,
	       t2.id_clm_payment_info,
	       t2.sub_times,
	       t2.CLIENT_NAME,
	       t2.CLAIM_TYPE,
         t2. ID_CLM_PAYMENT_ITEM,
	       (select INVOICE_NO
	          from CLMS_INVOICE_INFO
	         where ID_AHCS_FEE_PAY = t2.id_ahcs_fee_pay
           limit 0,1) as INVOICE_NO,
		   (select is_full_pay from clm_payment_item cpi where t2.ID_CLM_PAYMENT_ITEM = cpi.ID_CLM_PAYMENT_ITEM) isFullPay,
		   t2.DUTY_CODE dutyCode
	  	 from CLMS_policy_info t1, CLMS_fee_pay t2, clm_case_base t4
		 where t1.case_no = t4.case_no
		   and t2.case_no = t4.case_no
		   and t2.case_times = t4.case_times
		   
		   and t2.report_no = #{reportNo}
		   and t2.CASE_TIMES = #{caseTimes}
		   and t2.IS_EFFECTIVE = 'Y'
		<if test="claimType != null and claimType != ''">
			and t2.CLAIM_TYPE = #{claimType}
		</if>
		<if test="subTimes != null">
			and t2.SUB_TIMES = #{subTimes}
		</if>
        <if test="taskId != null and taskId != ''">
            and t2. ID_CLM_PAYMENT_ITEM = #{taskId}
        </if>
	</select>

	<select id="getReplevyFeePays" resultMap="result4">
		select  t2.report_no,
				t1.policy_no,
				t1.product_code,
				t1.policy_cer_no,
				(select department_abbr_name from department_define t3 where t3.department_code=t1.department_code) as department_name,
				t1.case_no,
				t2.case_times,
				t2.charge_money as fee_amount,
				t2.charge_type as fee_type,
				t2.id as id_ahcs_fee_pay,
				t2.payment_info_id as id_clm_payment_info,
				t2.CLIENT_NAME,
				'1' as CLAIM_TYPE,
				(select INVOICE_NO from CLMS_INVOICE_INFO where ID_AHCS_FEE_PAY = t2.id limit 0,1) as INVOICE_NO
		from CLMS_policy_info t1, clms_replevy_charge t2, clm_case_base t4
		where t1.case_no = t4.case_no
			  and t2.report_no  = t4.report_no
			  and t2.case_times = t4.case_times
			  and t2.report_no = #{reportNo}
			  and t2.CASE_TIMES = #{caseTimes}
			  and t2.valid_flag  = 'Y'
		<if test="taskId != null and taskId != ''">
			and t2.ID_CLM_PAYMENT_ITEM = #{taskId}
		</if>
	</select>

	<!-- 删除预赔费用 -->
	<update id="delPrepayFee" parameterType="string">
		UPDATE CLMS_FEE_PAY SET IS_EFFECTIVE='N' ,UPDATED_DATE = now() WHERE ID_CLM_PAYMENT_ITEM IN
		<foreach collection="clmPaymentItemId" index="index" item="payItemId" open="(" separator="," close=")">
			#{payItemId}
		</foreach>
	</update>

	<insert id="insertFeePay" parameterType="com.paic.ncbs.claim.model.dto.fee.FeeCostDTO">
		insert into
		clms_fee_pay(
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_FEE_PAY,
		REPORT_NO,
		POLICY_NO,
		CASE_NO,
		CASE_TIMES,
		FEE_TYPE,
		FEE_AMOUNT,
		ID_CLM_PAYMENT_INFO,
		CLAIM_TYPE,
	    IS_EFFECTIVE,
		CLIENT_NAME,
		ID_CLM_PAYMENT_ITEM,
		SUB_TIMES,
		LOSS_OBJECT_NO,
		ARCHIVE_TIME,
		DUTY_CODE
		)
		values
		(#{createdBy,jdbcType=NVARCHAR},
		now(),
		#{updatedBy,jdbcType=NVARCHAR},
		now(),
		#{idAhcsFeePay,jdbcType=NVARCHAR},
		#{reportNo,jdbcType=NVARCHAR},
		#{policyNo,jdbcType=NVARCHAR},
		#{caseNo,jdbcType=NVARCHAR},
		#{caseTimes,jdbcType=NUMERIC},
		#{feeType,jdbcType=NVARCHAR},
		#{feeAmount,jdbcType=NUMERIC},
		#{idClmPaymentInfo,jdbcType=NVARCHAR},
		#{claimType,jdbcType=NVARCHAR},
		 'Y',
		#{clientName,jdbcType=NVARCHAR},
		#{idClmPaymentItem,jdbcType=NVARCHAR},
		#{subTimes,jdbcType=NUMERIC},
		#{lossObjectNo,jdbcType=NVARCHAR},
		now(),
		#{dutyCode,jdbcType=NVARCHAR}
		)
	</insert>

	<update id="updateFeePay">
		update CLMS_fee_pay
		set
		<if test="clientName != null and clientName != ''">
			 CLIENT_NAME = #{clientName,jdbcType=NVARCHAR},
		</if>
		<if test="idClmPaymentInfo != null and idClmPaymentInfo != ''">
			 ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=NVARCHAR}, 
		</if>
		<if test="idClmPaymentItem != null and idClmPaymentItem != ''">
			 ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=NVARCHAR}, 
		</if>
		FEE_AMOUNT = #{feeAmount,jdbcType=NUMERIC}, FEE_TYPE = #{feeType,jdbcType=NVARCHAR},UPDATED_DATE = now()
		where ID_AHCS_FEE_PAY
		= #{idAhcsFeePay}
	</update>


	<update id="updateClmPaymentItem">
		update CLMS_fee_pay
		set
		<if test="idClmPaymentItem != null and idClmPaymentItem != ''">
			ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=NVARCHAR},
		</if>
	    UPDATED_DATE = now()
		where ID_AHCS_FEE_PAY
		= #{idAhcsFeePay}
	</update>


	<insert id="addInvoiceInfo" parameterType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO">
		insert into  clms_invoice_info(
		created_by,
		created_date,
		updated_by,
		updated_date,
		id_ahcs_invoice_info,
		id_ahcs_fee_pay,
		invoice_code,
		invoice_no,
		invoice_type,
		buy_company,
		taxbuyer_no,
		sell_company,
		sell_taxpayer_no,
		no_tax_amount,
		tax_amount,
		total_amount,
		archive_time,
		tax_rate,
		invoice_date,
		file_id,
		is_modified_flag
		)
		VALUES(
		#{createdBy},
		now(),
		#{updatedBy},
		now(),
		left(hex(uuid()),32),
		#{idAhcsFeePay},
		#{invoiceCode},
		#{invoiceNo},
		#{invoiceType},
		#{buyCompany},
		#{taxbuyerNo},
		#{sellCompany},
		#{sellTaxpayerNo},
		#{noTaxAmount},
		#{taxAmount},
		#{totalAmount},
		now(),
		#{taxRate},
		#{invoiceDate},
		#{fileId},
		#{isModifiedFlag}
		)
	</insert>
	<!--  根据对象更新 部分更新 发票信息   -->
	<update id="modifyInvoiceInfo" parameterType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO">
		UPDATE CLMS_INVOICE_INFO
		<set>
			<if test="createdBy != null and createdBy != '' ">
				CREATED_BY  = #{createdBy},
			</if>
			<if test="createdDate != null ">
				CREATED_DATE  = #{createdDate},
			</if>
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy},
			</if>
			<if test="idAhcsFeePay != null and idAhcsFeePay != '' ">
				ID_AHCS_FEE_PAY  = #{idAhcsFeePay},
			</if>
			<if test="invoiceCode != null and invoiceCode != '' ">
				INVOICE_CODE  = #{invoiceCode},
			</if>
			<if test="invoiceNo != null and invoiceNo != '' ">
				INVOICE_NO  = #{invoiceNo},
			</if>
			<if test="invoiceType != null and invoiceType != '' ">
				INVOICE_TYPE  = #{invoiceType},
			</if>
			<if test="buyCompany != null and buyCompany != '' ">
				BUY_COMPANY  = #{buyCompany},
			</if>
			<if test="taxbuyerNo != null and taxbuyerNo != '' ">
				taxbuyer_no  = #{taxbuyerNo},
			</if>
			<if test="sellCompany != null and sellCompany != '' ">
				SELL_COMPANY  = #{sellCompany},
			</if>
			<if test="sellTaxpayerNo != null and sellTaxpayerNo != '' ">
				SELL_TAXPAYER_NO  = #{sellTaxpayerNo},
			</if>
			<if test="noTaxAmount != null and noTaxAmount != '' ">
				no_tax_amount  = #{noTaxAmount},
			</if>
			<if test="taxAmount != null and taxAmount != '' ">
				TAX_AMOUNT  = #{taxAmount},
			</if>
			<if test="totalAmount != null and totalAmount != '' ">
				TOTAL_AMOUNT  = #{totalAmount},
			</if>
			<if test="taxRate != null and taxRate != '' ">
				tax_rate  = #{taxRate},
			</if>
			<if test="invoiceDate != null">
				invoice_date  = #{invoiceDate},
			</if>
			<if test="isModifiedFlag != null">
				is_modified_flag  = #{isModifiedFlag},
			</if>
			<if test="fileId != null">
				file_id  = #{fileId},
			</if>
			UPDATED_DATE=now()
		</set>
		WHERE ID_AHCS_INVOICE_INFO=#{idAhcsInvoiceInfo}
	</update>

	<!--  根据ID全量更新 发票信息   -->
	<update id="updateInvoiceInfoById" parameterType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO">
		UPDATE CLMS_INVOICE_INFO
		SET
			CREATED_BY = #{createdBy,jdbcType=VARCHAR},
			CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
			UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
			UPDATED_DATE = now(),
			ID_AHCS_FEE_PAY = #{idAhcsFeePay,jdbcType=VARCHAR},
			INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
			INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
			INVOICE_TYPE = #{invoiceType,jdbcType=VARCHAR},
			BUY_COMPANY = #{buyCompany,jdbcType=VARCHAR},
			taxbuyer_no = #{taxbuyerNo,jdbcType=VARCHAR},
			SELL_COMPANY = #{sellCompany,jdbcType=VARCHAR},
			SELL_TAXPAYER_NO = #{sellTaxpayerNo,jdbcType=VARCHAR},
			no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
			TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
			TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
			tax_rate = #{taxRate,jdbcType=INTEGER},
			invoice_date = #{invoiceDate,jdbcType=DATE},
			file_id = #{fileId,jdbcType=VARCHAR},
			is_modified_flag = #{isModifiedFlag,jdbcType=VARCHAR}
		WHERE ID_AHCS_INVOICE_INFO = #{idAhcsInvoiceInfo,jdbcType=VARCHAR}
	</update>
	<select id="getPolicyDepartment" resultMap="result3">
		select t1.report_no,
		t1.policy_no,
		t1.policy_cer_no,
		t1.product_code,
		t1.department_code,
		t3.department_abbr_name as department_name,
		t4.case_no,
		t4.case_times
		from clms_policy_info t1,
		department_define t3,
		clm_case_base t4
		where t1.case_no=t4.case_no
		and t1.report_no=t4.report_no
		and t1.department_code=t3.department_code
		and t4.report_no=#{reportNo}
		and t4.CASE_TIMES=#{caseTimes}
		order by t1.policy_no
	</select>

	<select id="hasEqualsInvoiceNo" resultType = "int">
		select count(*) from CLMS_INVOICE_INFO where INVOICE_NO = #{invoiceNo}
	</select>

	<update id="removeFeePay" parameterType="java.lang.String">
		update CLMS_fee_pay set  IS_EFFECTIVE='N',UPDATED_DATE = now() WHERE id_ahcs_fee_pay=#{idAhcsFeePay}
	</update>

	<delete id="delFeePay" parameterType="java.lang.String">
		DELETE FROM CLMS_fee_pay WHERE ID_AHCS_FEE_PAY = #{idAhcsFeePay}
	</delete>

	<select id="getFeeByClaimType" resultType="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO">
		select FEE_TYPE feeType,
		FEE_AMOUNT feeAmount ,
		policy_no policyNo,
		CLAIM_TYPE claimType,
		CASE_NO caseNo,
		ID_AHCS_FEE_PAY idAhcsFeePay
		from CLMS_fee_pay fp
		where fp.report_no = #{reportNo,jdbcType=VARCHAR}
		and fp.case_times = #{caseTimes,jdbcType=NUMERIC}
		and fp.IS_EFFECTIVE='Y'
		<if test="claimType != null and claimType != ''">
			and fp.CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		</if>
		<if test="subTimes != null">
			and fp.SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
		</if>
	</select>


	<select id="getSyncPrePayItem" resultType="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO">
		select *
		from CLMS_fee_pay fp
		where fp.report_no = #{reportNo,jdbcType=VARCHAR}
		and fp.case_times = #{caseTimes,jdbcType=NUMERIC}
		and fp.IS_EFFECTIVE='Y'
		and fp.CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
		<if test="subTimes != null">
			and fp.SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
		</if>
	</select>

	<select id="getFeePayAmount" resultType="java.math.BigDecimal">
		select SUM(FEE_AMOUNT) from CLMS_fee_pay f where  is_effective='Y'
		<if test="feePay.reportNo!= null and feePay.reportNo != ''">
			and f.report_no = #{feePay.reportNo}
		</if>
		<if test="feePay.caseTimes!= null and feePay.caseTimes > 0">
			and f.case_times = #{feePay.caseTimes}
		</if>
		<if test="feePay.policyNo!= null and feePay.policyNo != ''">
			and f.policy_no = #{feePay.policyNo}
		</if>
		<if test="feePay.caseNo!= null and feePay.caseNo != ''">
			and f.case_no = #{feePay.caseNo}
		</if>
		<if test="feePay.claimType!= null and feePay.claimType != ''">
			and claim_type= #{feePay.claimType}
            <if test="feePay.claimType == 2">
                <!--如果是预赔,就不返回审批驳回的预赔费用-->
                and exists (select pf.id_ahcs_prepay_info from CLMS_prepay_info pf, CLMS_duty_prepay_info dp where
                dp.id_ahcs_prepay_info = pf.id_ahcs_prepay_info and dp.report_no = f.report_no
                and dp.case_times = f.case_times and dp.sub_times = f.sub_times and pf.verify_options = '1')
            </if>
		</if>
	</select>

	<select id="getPolicyFeePayAmount" resultType="java.math.BigDecimal">
	select SUM(FEE_AMOUNT) from CLMS_fee_pay f where is_effective='Y' and
	FEE_TYPE != 'FEE_07'
	and f.CLAIM_TYPE = #{claimType,jdbcType=NVARCHAR}
	and f.report_no = #{reportNo,jdbcType=NVARCHAR}
	and f.case_times = #{caseTimes,jdbcType=NUMERIC}
	and f.policy_no = #{policyNo,jdbcType=NVARCHAR}
	</select>

	<select id="getFeePayById" resultMap="feeInfoDTOMap">
		select
		CREATED_BY ,
		CREATED_DATE ,
		UPDATED_BY ,
		UPDATED_DATE,
		ID_AHCS_FEE_PAY,
		REPORT_NO ,
		CASE_TIMES,
		CASE_NO ,
		POLICY_NO,
		FEE_TYPE ,
		FEE_AMOUNT,
		ID_CLM_PAYMENT_INFO,
		IS_EFFECTIVE,
		CLAIM_TYPE,
		CLIENT_NAME,
		ID_CLM_PAYMENT_ITEM,
		SUB_TIMES,
		LOSS_OBJECT_NO
		from CLMS_fee_pay fp where
		fp.ID_AHCS_FEE_PAY = #{idAhcsFeePay}
		and fp.IS_EFFECTIVE='Y'
	</select>

	<select id="getFeePayByParam" resultMap="feeInfoDTOMap">
		select
		CREATED_BY ,
		CREATED_DATE ,
		UPDATED_BY ,
		UPDATED_DATE,
		ID_AHCS_FEE_PAY,
		REPORT_NO ,
		CASE_TIMES,
		CASE_NO ,
		POLICY_NO,
		FEE_TYPE ,
		FEE_AMOUNT,
		ID_CLM_PAYMENT_INFO,
		IS_EFFECTIVE,
		CLAIM_TYPE,
		CLIENT_NAME,
		ID_CLM_PAYMENT_ITEM,
		SUB_TIMES,
		(select INVOICE_NO
	          from CLMS_INVOICE_INFO
	         where ID_AHCS_FEE_PAY = t2.id_ahcs_fee_pay
           limit 0,1) as INVOICE_NO,
		t2.is_full_pay isFullPay,
		t2.DUTY_CODE dutyCode
		from CLMS_fee_pay t2
		where t2.report_no = #{reportNo}
		   and t2.CASE_TIMES = #{caseTimes}
		   and t2.IS_EFFECTIVE = 'Y'
		<if test="claimType != null and claimType != ''">
			and t2.CLAIM_TYPE = #{claimType}
		</if>
		<if test="subTimes != null">
			and t2.SUB_TIMES = #{subTimes}
		</if>
		order by t2.created_date

	</select>

	<select id="getIdClmPaymentInfos" resultType="java.lang.String">
	select p.id_clm_payment_info from CLMS_fee_pay p where p.report_no=#{reportNo}
	and p.case_times=#{caseTimes}
	</select>

	<delete id="removeInvoiceInfo" parameterType="java.lang.String">
		DELETE FROM CLMS_INVOICE_INFO WHERE ID_AHCS_FEE_PAY = #{idAhcsFeePay}
	</delete>

	<select id="getInvoiceInfo" parameterType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO"
		 resultType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO">
		SELECT
		ID_AHCS_INVOICE_INFO idAhcsInvoiceInfo,
		ID_AHCS_FEE_PAY 	 idAhcsFeePay,
		INVOICE_CODE         invoiceCode,
		INVOICE_NO           invoiceNo,
		INVOICE_TYPE         invoiceType,
		BUY_COMPANY     	 buyCompany,
		taxbuyer_no          taxbuyerNo,
		SELL_COMPANY     	 sellCompany,
		SELL_TAXPAYER_NO     sellTaxpayerNo,
		no_tax_amount        noTaxAmount,
		TAX_AMOUNT           taxAmount,
		TOTAL_AMOUNT         totalAmount,
		tax_rate             taxRate,
		invoice_date         invoiceDate,
		file_id              fileId,
		return_date returnDate,
		return_type returnType,
		return_reason returnReason,
		is_modified_flag  isModifiedFlag
		FROM CLMS_INVOICE_INFO
		WHERE ID_AHCS_FEE_PAY = #{idAhcsFeePay}
		<if test="isModifiedFlag != null and isModifiedFlag != ''">
			and is_modified_flag in ('1','3')
		</if>
		<if test="isModifiedFlag == null || isModifiedFlag == ''">
			and is_modified_flag in ('0','2')
		</if>
		<if test="invoiceCode != null and invoiceCode != '' ">
			and invoice_code  = #{invoiceCode}
		</if>
		<if test="invoiceNo != null and invoiceNo != '' ">
			and invoice_no  = #{invoiceNo}
		</if>
		limit 0,1
	</select>

	<select id="getInvoiceInfoById" parameterType="java.lang.String"
			resultType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO">
		SELECT
		ID_AHCS_INVOICE_INFO idAhcsInvoiceInfo,
		ID_AHCS_FEE_PAY 	 idAhcsFeePay,
		INVOICE_CODE         invoiceCode,
		INVOICE_NO           invoiceNo,
		INVOICE_TYPE         invoiceType,
		BUY_COMPANY     	 buyCompany,
		taxbuyer_no          taxbuyerNo,
		SELL_COMPANY     	 sellCompany,
		SELL_TAXPAYER_NO     sellTaxpayerNo,
		no_tax_amount        noTaxAmount,
		TAX_AMOUNT           taxAmount,
		TOTAL_AMOUNT         totalAmount,
		tax_rate             taxRate,
		invoice_date         invoiceDate,
		file_id              fileId,
		return_date returnDate,
		return_type returnType,
		return_reason returnReason,
		is_modified_flag  isModifiedFlag
		FROM CLMS_INVOICE_INFO
		WHERE ID_AHCS_FEE_PAY = #{id}
	</select>
	<!--  根据对象更新 部分更新 发票信息   -->
	<update id="modefyInvoiceInfo" parameterType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO">
		UPDATE CLMS_INVOICE_INFO
		<set>

			<if test="createdBy != null and createdBy != '' ">
				CREATED_BY  = #{createdBy},
			</if>

			<if test="createdDate != null ">
				CREATED_DATE  = #{createdDate},
			</if>

			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy},
			</if>


			<if test="idAhcsFeePay != null and idAhcsFeePay != '' ">
				ID_AHCS_FEE_PAY  = #{idAhcsFeePay},
			</if>

			<if test="invoiceCode != null and invoiceCode != '' ">
				INVOICE_CODE  = #{invoiceCode},
			</if>

			<if test="invoiceNo != null and invoiceNo != '' ">
				INVOICE_NO  = #{invoiceNo},
			</if>

			<if test="invoiceType != null and invoiceType != '' ">
				INVOICE_TYPE  = #{invoiceType},
			</if>

			<if test="buyCompany != null and buyCompany != '' ">
				BUY_COMPANY  = #{buyCompany},
			</if>

			<if test="taxbuyerNo != null and taxbuyerNo != '' ">
				taxbuyer_no  = #{taxbuyerNo},
			</if>

			<if test="sellCompany != null and sellCompany != '' ">
				SELL_COMPANY  = #{sellCompany},
			</if>

			<if test="sellTaxpayerNo != null and sellTaxpayerNo != '' ">
				SELL_TAXPAYER_NO  = #{sellTaxpayerNo},
			</if>

			<if test="noTaxAmount != null and noTaxAmount != '' ">
				no_tax_amount  = #{noTaxAmount},
			</if>

			<if test="taxAmount != null and taxAmount != '' ">
				TAX_AMOUNT  = #{taxAmount},
			</if>

			<if test="totalAmount != null and totalAmount != '' ">
				TOTAL_AMOUNT  = #{totalAmount},
			</if>
			<if test="taxRate != null and taxRate != '' ">
				tax_rate  = #{taxRate},
			</if>
			<if test="invoiceDate != null and invoiceDate != '' ">
				invoice_date  = #{invoiceDate},
			</if>
			UPDATED_DATE=now()
		</set>
		WHERE ID_AHCS_INVOICE_INFO=#{idAhcsInvoiceInfo}
	</update>

	<select id="getCaseBaseBySome" resultType="java.lang.String">
		select
		       a.CASE_NO caseNo
		from clm_case_base a
		where  a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  and  a.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
		  and  a.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		  limit 0,1
	</select>

	<!-- 根据报案号和赔付次数查询理赔费用总和 -->
	<select id="getFeePayForSum" resultType="java.lang.String">
		select sum(t.fee_amount)
		from CLMS_fee_pay t
		where  t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  and  t.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
		  and  t.IS_EFFECTIVE='Y'
	</select>

	<!-- 获取预赔费用总金额 -->
	<select id="getPrePayAmountForSum" resultType="java.lang.String">
		select
		       sum(t.fee_amount)
		from CLMS_fee_pay t
		where  t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  and  t.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
		  and  t.IS_EFFECTIVE='Y'
		  and  t.CLAIM_TYPE = '2'

	</select>

	<!-- 查询批单模板文本-->
	<select id="getEndorseTemplate" parameterType="java.lang.String" resultType="java.lang.String">
		select template_content  from CLMS_text_template where template_code = concat('ENDORSE_' ,#{ indemnityMode})
	</select>


	<insert id="insertBatch" parameterType="com.paic.ncbs.claim.model.dto.settle.BatchDTO" >
		insert into clm_batch (CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLM_BATCH, REPORT_NO,
		CASE_TIMES, SETTLE_USER_UM, SETTLE_TIME, SETTLE_STATUS, BATCH_SETTLE_TYPE,
		MIGRATE_FROM) values (
		#{batchDto.createdBy,jdbcType=VARCHAR},
		now(),
		#{batchDto.updatedBy,jdbcType=VARCHAR},
		now(),
		#{batchDto.idAhcsBatch,jdbcType=VARCHAR},
		#{batchDto.reportNo,jdbcType=VARCHAR},
		#{batchDto.caseTimes,jdbcType=INTEGER},
		#{batchDto.settleUserUm,jdbcType=VARCHAR},
		#{batchDto.settleTime,jdbcType=DATE},
		#{batchDto.settleStatus,jdbcType=VARCHAR},
		#{batchDto.batchSettleType,jdbcType=VARCHAR},
		'na')
	</insert>

	 <select id="getBatchByReportNo" resultMap="batchDTOMap">
		SELECT
			  b.ID_CLM_BATCH     ,
			  b.REPORT_NO         ,
			  b.CASE_TIMES        ,
			  b.SETTLE_USER_UM    ,
			  b.SETTLE_TIME       ,
			  b.SETTLE_STATUS     ,
			  b.BATCH_SETTLE_TYPE ,
			  b.MIGRATE_FROM
		 FROM  CLM_BATCH b
		 WHERE b.REPORT_NO=#{reportNo,jdbcType=VARCHAR}
		 AND   b.CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
		 AND   b.batch_settle_type = '01'
     	limit 0,1
	</select>

	<!-- 根据报案号查询所有保单号 -->
	<select id="getAllPolicyByReportNo" resultType="java.lang.String">
		select a.policy_no
		  from CLMS_policy_info a
		 where a.case_no in
		       (select t.case_no
		          from clm_case_base t
		         where report_no = #{reportNo,jdbcType=VARCHAR}
		           and case_times = #{caseTimes,jdbcType=INTEGER})
	</select>


	<!-- 根据报案号查询所有保单号 -->
	<select id="getInsuredNameByReportNo" resultType="java.lang.String">
		select t.name from  CLMS_report_customer t where t.report_no=#{reportNo,jdbcType=VARCHAR}
	</select>

    <!-- 根据支付信息id(CLM_PAYMENT_INFO表id)查询理赔费用id -->
	<select id="getIdFeePayByIdPayinfo" parameterType="com.paic.ncbs.claim.model.dto.fee.FeePayDTO" resultType="string">
	    select fp.ID_AHCS_FEE_PAY
	      from CLMS_FEE_PAY fp
	     where fp.IS_EFFECTIVE='Y'
	           and fp.ID_CLM_PAYMENT_INFO in
	           <foreach collection="idClmPaymentInfoList" index="index" item="idClmPaymentInfo" open="(" separator="," close=")">
				   #{idClmPaymentInfo}
			   </foreach>
	</select>

	<!-- 根据支付信息id(CLM_PAYMENT_INFO表id)查询理赔费用id -->
	<select id="getIdFeePayByIdPaymentInfo" parameterType="string" resultType="string">
	    select fp.ID_AHCS_FEE_PAY
	      from CLMS_FEE_PAY fp
	     where fp.IS_EFFECTIVE='Y'
	           and fp.ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo}
	</select>

	<!-- 根据报案号赔付次数查询理赔费用id -->
	<select id="getIdFeePayByRnCt" resultType="string">
	    select fp.ID_AHCS_FEE_PAY
	      from CLMS_FEE_PAY fp
	     where fp.REPORT_NO = #{reportNo}
	           and fp.CASE_TIMES = #{caseTimes}
	           and fp.IS_EFFECTIVE='Y'
	</select>

	<!-- 删除发票信息 -->
	<delete id="removeInvoiceInfoList" parameterType="string">
	   delete  CLMS_INVOICE_INFO
		where ID_AHCS_FEE_PAY in
		     <foreach collection="idAhcsFeePayList" index="index" item="idAhcsFeePay" open="(" separator="," close=")">
		         #{idAhcsFeePay}
		     </foreach>
	</delete>

	<!-- 删除理赔费用 -->
	<update id="removeFeePayList" parameterType="string">
		update CLMS_FEE_PAY
		   set IS_EFFECTIVE='N'
		   ,UPDATED_DATE = now()
		 where ID_AHCS_FEE_PAY in
		 <foreach collection="idAhcsFeePayList" index="index" item="idAhcsFeePay" open="(" separator="," close=")">
		      #{idAhcsFeePay}
		 </foreach>
	</update>

	<!-- 获取理赔费用总额 -->
	<select id="getSumFeePay" resultType="java.math.BigDecimal">
	    select sum(fp.fee_amount)
	      from CLMS_FEE_PAY fp
	     where fp.REPORT_NO = #{reportNo}
	           and fp.CASE_TIMES = #{caseTimes}
	           and fp.IS_EFFECTIVE='Y'
	</select>

    <!-- 将费用信息置失效 -->
	<update id="delFeeByReport">
		    UPDATE CLMS_FEE_PAY T
		       SET T.IS_EFFECTIVE = 'N',
		       	   T.UPDATED_DATE = now()
		     WHERE T.REPORT_NO = #{reportNo}
		       AND T.CASE_TIMES = #{caseTimes}
		       AND T.CLAIM_TYPE = #{claimType}
		       AND T.IS_EFFECTIVE = 'Y'
	</update>

	<select id="getCaseNoByPolicyNo" resultType="String" parameterType="com.paic.ncbs.claim.model.dto.fee.FeePayDTO">
		select t.case_no from CLMS_policy_info  t where t.report_no=#{reportNo} and t.policy_no=#{policyNo}
	</select>

    <!--获取案件费用类型及金额-->
	<select id="getFeeTypeAmountByReportNo" resultMap="feePayMap">
		select t.FEE_TYPE,
		       (select t.value_chinese_name
		          from clm_common_parameter t
		         where t.department_code='2'
		               and t.collection_code='AHCS_FEE_TYPE'
		               and t.value_code=t.FEE_TYPE
		               limit 0,1) FEE_TYPE_NAME,
		       t.FEE_AMOUNT
		  from CLMS_FEE_PAY t
		 where t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		       and t.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
		       and t.FEE_AMOUNT > 0
		       and t.CLAIM_TYPE = '1'
		       and t.IS_EFFECTIVE='Y'
	</select>

	<insert id="addFeePayList" parameterType="java.util.List">
		insert into
		clms_fee_pay(
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_FEE_PAY,
		REPORT_NO,
		POLICY_NO,
		CASE_NO,
		CASE_TIMES,
		FEE_TYPE,
		FEE_AMOUNT,
		ID_CLM_PAYMENT_INFO,
		CLAIM_TYPE,
		IS_EFFECTIVE,
		CLIENT_NAME,
		ID_CLM_PAYMENT_ITEM,
		SUB_TIMES,
		ARCHIVE_TIME,
		duty_code,
		is_full_pay
		)
		values
		<foreach collection="feeList" item="item" index="index" separator="," >
			(#{item.createdBy,jdbcType=NVARCHAR},
			now(),
			#{item.updatedBy,jdbcType=NVARCHAR},
			now(),
			#{item.idAhcsFeePay,jdbcType=NVARCHAR},
			#{item.reportNo,jdbcType=NVARCHAR},
			#{item.policyNo,jdbcType=NVARCHAR},
			#{item.caseNo,jdbcType=NVARCHAR},
			#{item.caseTimes,jdbcType=NUMERIC},
			#{item.feeType,jdbcType=NVARCHAR},
			#{item.feeAmount,jdbcType=NUMERIC},
			#{item.idClmPaymentInfo,jdbcType=NVARCHAR},
			#{item.claimType,jdbcType=NVARCHAR},
			'Y',
			#{item.clientName,jdbcType=NVARCHAR},
			#{item.idClmPaymentItem,jdbcType=NVARCHAR},
			#{item.subTimes,jdbcType=NUMERIC},
			now(),
			#{item.dutyCode},
			#{item.isFullPay}
			)
		</foreach>
	</insert>

	<!-- 将预赔费用信息置失效 -->
	<update id="delPreFeeBySubTimes" parameterType="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO">
		UPDATE CLMS_FEE_PAY T
		SET T.IS_EFFECTIVE = 'N',
		T.UPDATED_DATE = now(),
		T.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR}
		WHERE T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND T.CASE_TIMES  = #{caseTimes,jdbcType=NUMERIC}
		AND T.SUB_TIMES   = #{subTimes,jdbcType=NUMERIC}
		AND T.CLAIM_TYPE  = '2'
		AND T.IS_EFFECTIVE = 'Y'
	</update>

	<select id="getPolicyPrePayFeeList" resultType="com.paic.ncbs.claim.model.vo.ahcs.PrePaymentVO">
		select policy_no         policyNo,
			case_no              caseNo,
			id_clm_payment_info  idClmPaymentInfo,
			fee_type             feeType,
			sum(fee_amount)      paymentAmount,
			(select department_code  from clms_policy_info b
		where b.report_no=report_no and b.policy_no=policy_no limit 1) departmentCode,
		duty_code dutyCode,
		is_full_pay isFullPay,
		id_ahcs_fee_pay		idAhcsFeePay
		from clms_fee_pay
		where report_no = #{reportNo,jdbcType=VARCHAR}
		and case_times  = #{caseTimes,jdbcType=NUMERIC}
		and sub_times   = #{subTimes,jdbcType=NUMERIC}
		and claim_type  = '2'
		and IS_EFFECTIVE = 'Y'
		group by policy_no, case_no, id_clm_payment_info, fee_type
	</select>

	<select id="getFeePayByIdClmPaymentInfo" resultMap="feeInfoDTOAndInvoice">
		select
		CREATED_BY ,
		CREATED_DATE ,
		UPDATED_BY ,
		UPDATED_DATE,
		ID_AHCS_FEE_PAY,
		REPORT_NO ,
		CASE_TIMES,
		CASE_NO ,
		POLICY_NO,
		FEE_TYPE ,
		FEE_AMOUNT,
		ID_CLM_PAYMENT_INFO,
		IS_EFFECTIVE,
		CLAIM_TYPE,
		CLIENT_NAME,
		ID_CLM_PAYMENT_ITEM,
		SUB_TIMES,
		(select INVOICE_NO
		from CLMS_INVOICE_INFO
		where ID_AHCS_FEE_PAY = t2.id_ahcs_fee_pay
		limit 0,1) as INVOICE_NO,
		duty_code
		from CLMS_fee_pay t2
		where t2.ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem}
		and t2.IS_EFFECTIVE = 'Y'
	</select>

	<insert id="addInvoiceInfoList" parameterType="java.util.List">
		INSERT INTO  CLMS_INVOICE_INFO(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVOICE_INFO,
			ID_AHCS_FEE_PAY,
			INVOICE_CODE,
			INVOICE_NO,
			INVOICE_TYPE,
			BUY_COMPANY,
		    taxbuyer_no,
			SELL_COMPANY,
			SELL_TAXPAYER_NO,
		    no_tax_amount,
			TAX_AMOUNT,
			TOTAL_AMOUNT,
			ARCHIVE_TIME,
			tax_rate,
		    invoice_date,
			file_id,
			is_modified_flag
		)
		VALUES
		<foreach collection="invoiceList" separator="," index="index" item="item">
		(
			#{item.createdBy,jdbcType=NVARCHAR},
			now(),
			#{item.updatedBy,jdbcType=NVARCHAR},
			now(),
			#{item.idAhcsInvoiceInfo,jdbcType=NVARCHAR},
			#{item.idAhcsFeePay,jdbcType=NVARCHAR},
			#{item.invoiceCode,jdbcType=NVARCHAR},
			#{item.invoiceNo,jdbcType=NVARCHAR},
			#{item.invoiceType,jdbcType=NVARCHAR},
			#{item.buyCompany,jdbcType=NVARCHAR},
			#{item.taxbuyerNo,jdbcType=NVARCHAR},
			#{item.sellCompany,jdbcType=NVARCHAR},
			#{item.sellTaxpayerNo,jdbcType=NVARCHAR},
			#{item.noTaxAmount,jdbcType=NUMERIC},
			#{item.taxAmount,jdbcType=NUMERIC},
			#{item.totalAmount,jdbcType=NUMERIC},
			now(),
			#{item.taxRate},
			#{item.invoiceDate},
			#{item.fileId},
			#{item.isModifiedFlag}
		)
		</foreach>
	</insert>

	<delete id="delPreInvoiceBySubTimes" parameterType="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO">
		delete from CLMS_INVOICE_INFO
		where ID_AHCS_FEE_PAY
		in (select ID_AHCS_FEE_PAY from CLMS_fee_pay T where T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND T.CASE_TIMES  = #{caseTimes,jdbcType=NUMERIC}
		AND T.SUB_TIMES   = #{subTimes,jdbcType=NUMERIC}
		AND T.CLAIM_TYPE  = '2'
		AND T.IS_EFFECTIVE = 'Y')
	</delete>

	<select id="getInvoiceByFeeIds" resultType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO">
		select ID_AHCS_FEE_PAY idAhcsFeePay,
		INVOICE_CODE           invoiceCode,
		INVOICE_NO             invoiceNo,
		INVOICE_TYPE           invoiceType,
		BUY_COMPANY            buyCompany,
		taxbuyer_no            taxbuyerNo,
		TOTAL_AMOUNT           totalAmount,
		tax_rate               taxRate,
		invoice_date           invoiceDate,
		SELL_COMPANY           sellCompany,
		SELL_TAXPAYER_NO       sellTaxpayerNo,
		TAX_AMOUNT             taxAmount,
		NO_TAX_AMOUNT          noTaxAmount
		from CLMS_INVOICE_INFO
		where ID_AHCS_FEE_PAY  in (
		<foreach collection="feeIdList" separator="," index="index" item="item">
			#{item,jdbcType=NVARCHAR}
		</foreach>
		)
	</select>

	<select id="getPrePayFeeAmountByParam" resultMap="feeInfoDTOMap">
		select
		t2.POLICY_NO,
		sum(ifnull(t2.FEE_AMOUNT,0)) FEE_AMOUNT
		from CLMS_fee_pay t2
		where t2.report_no = #{reportNo}
		and t2.CASE_TIMES = #{caseTimes}
		and t2.IS_EFFECTIVE = 'Y'
		and t2.CLAIM_TYPE = '2'
		and t2.SUB_TIMES = #{subTimes}
		group by policy_no

	</select>

	<update id="updateFeeInvoiceBackResult" >
		update clms_invoice_info
		set is_modified_flag = '1',
		return_date = now(),
		return_type = '1',
		<if test="returnReason != null and returnReason != ''">
			return_reason = #{returnReason},
		</if>
		updated_date = now()
		where id_ahcs_fee_pay = #{idAhcsFeePay}
		and invoice_code  = #{invoiceCode}
		and invoice_no  = #{invoiceNo}
		and is_modified_flag in ('0','2')
	</update>

	<select id="getInvoiceInfoByPaySerialNo" resultType="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO">
		select id_ahcs_fee_pay idAhcsFeePay,
		case_times caseTimes
		from clms_fee_pay
		where id_clm_payment_item = #{paySerialNo}
	</select>

	<select id="getInvoiceInfoByReportNo" resultType="com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO">
        select y.case_no caseNo,
        y.fee_amount feeAmount,
        o.return_date returnDate,
        o.return_type returnType,
        o.return_reason returnReason,
        o.is_modified_flag isModifiedFlag
        from clms_fee_pay y,
        clms_invoice_info o
        where y.id_ahcs_fee_pay=o.id_ahcs_fee_pay
        and y.report_no = #{reportNo}
        and y.case_times = #{caseTimes}
        and o.is_modified_flag in ('1','3')
        <if test="taskId != null and taskId != ''">
            and y.id_clm_payment_item = #{taskId}
        </if>
        limit 1
	</select>

	<select id="getReplevyInvoiceInfoByReportNo" resultType="com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO">
		select y.case_times,o.is_modified_flag,
			   (select case_no from clms_replevy_main m where m.report_no = y.report_no order by sys_ctime desc limit 1) caseNo,
		    y.charge_money  feeAmount,
			o.return_date returnDate,
			o.return_type returnType,
			o.return_reason returnReason,
		    o.is_modified_flag  isModifiedFlag
		from clms_replevy_charge y,
			clms_invoice_info o
		where y.id=o.id_ahcs_fee_pay
		  and y.report_no = #{reportNo}
		  and y.case_times = #{caseTimes}
		  and o.is_modified_flag in ('1','3')
		<if test="taskId != null and taskId != ''">
			and y.id_clm_payment_item = #{taskId}
		</if>
		limit 1
	</select>
	<select id="getInvoiceListByReportNo" resultType="com.paic.ncbs.claim.model.dto.pay.FeeInvoiceSendPaymentDTO">
		select
			y.id_clm_payment_item batchNo,
			o.id_ahcs_invoice_info  idAhcsInvoiceInfo,
			o.invoice_code invoiceCode,
			o.invoice_no invoiceNo,
			o.invoice_date invoiceDate,
			o.file_id invoiceView
		from clms_fee_pay y,
		clms_invoice_info o
		where y.id_ahcs_fee_pay=o.id_ahcs_fee_pay
		and y.report_no = #{reportNo}
		and y.case_times = #{caseTimes}
		and o.is_modified_flag = '3'
        <if test="idClmPaymentItem != null and idClmPaymentItem != ''">
                and y.id_clm_payment_item = #{idClmPaymentItem}
        </if>
	</select>
	<select id="getReplevyInvoiceListByReportNo" resultType="com.paic.ncbs.claim.model.dto.pay.FeeInvoiceSendPaymentDTO">
		select
		y.id_clm_payment_item batchNo,
		o.id_ahcs_invoice_info  idAhcsInvoiceInfo,
		o.invoice_code invoiceCode,
		o.invoice_no invoiceNo,
		o.invoice_date invoiceDate,
		o.file_id invoiceView
		from clms_replevy_charge y,
		clms_invoice_info o
		where y.id=o.ID_AHCS_FEE_PAY
		and y.report_no = #{reportNo}
		and y.case_times = #{caseTimes}
		and o.is_modified_flag = '3'
		<if test="idClmPaymentItem != null and idClmPaymentItem != ''">
			and y.id_clm_payment_item = #{idClmPaymentItem}
		</if>
	</select>

	<select id="getInvoiceTypeList" resultType="com.paic.ncbs.claim.model.dto.pay.InvoiceTypeTaxInfoDTO">
		select
			y.id_clm_payment_item idClmPaymentItem,
			o.invoice_type invoiceType,
			o.id_ahcs_invoice_info  idAhcsInvoiceInfo,
			o.invoice_code invoiceCode,
			o.invoice_no invoiceNo
		from clms_fee_pay y,
			clms_invoice_info o
		where y.id_ahcs_fee_pay=o.id_ahcs_fee_pay
		and id_clm_payment_item = #{paySerialNo}
		and o.is_modified_flag in ('0','2')
	</select>
    <select id="getCoinsFeeSettle" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.SettleCoinsReqVo">
		select
				FEE_AMOUNT amount,
				INVOICE_TYPE,
				TAX_AMOUNT
				from clms_fee_pay cfp ,clms_invoice_info cii
				where cfp.ID_AHCS_FEE_PAY = cii.ID_AHCS_FEE_PAY
				and cfp.IS_EFFECTIVE ='Y'
				and cfp.REPORT_NO =#{reportNo} and cfp.CASE_TIMES =#{caseTimes} and cfp.CLAIM_TYPE =#{payType}
				<if test="subTimes!=null and subTimes!='' and subTimes!=0">
					and cfp.sub_times = #{subTimes}
				</if>
	</select>
	<select id="selectFeeDutyCodes" resultType="java.lang.String">
		select DUTY_CODE from CLMS_policy_info t1, CLMS_fee_pay t2 where t1.REPORT_NO =#{reportNo}
				and t2.CASE_TIMES =#{caseTimes}
				and t1.case_no = t2.case_no and t2.IS_EFFECTIVE ='Y'
				and t2.CLAIM_TYPE =#{claimType};
	</select>
	<select id="getInvoiceList" resultType="com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO">
		select * from clms_invoice_info cii ,clms_fee_pay cfp where cii.ID_AHCS_FEE_PAY =cfp.ID_AHCS_FEE_PAY and cfp.ID_CLM_PAYMENT_ITEM in
		<foreach collection="idList" open="(" separator="," close=")" item="id">
			#{id}
		</foreach>
	</select>

	<select id="getClmsInvoiceInfoById" resultType = "com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO" parameterType="java.lang.String">
		select
		SELL_TAXPAYER_NO, NO_TAX_AMOUNT, TAX_AMOUNT, TOTAL_AMOUNT, ARCHIVE_TIME, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_INVOICE_INFO, ID_AHCS_FEE_PAY, INVOICE_CODE, INVOICE_NO, INVOICE_TYPE, BUY_COMPANY, TAXBUYER_NO, SELL_COMPANY, TAX_RATE, INVOICE_DATE, FILE_ID, IS_MODIFIED_FLAG, RETURN_DATE, RETURN_TYPE, RETURN_REASON
		from CLMS_INVOICE_INFO where id_ahcs_fee_pay = #{id}
	</select>
	<select id="getReplevyInvoiceTypeList" resultType="com.paic.ncbs.claim.model.dto.pay.InvoiceTypeTaxInfoDTO">
		select
		c.id_clm_payment_item idClmPaymentItem,
		o.invoice_type invoiceType,
		o.id_ahcs_invoice_info  idAhcsInvoiceInfo,
		o.invoice_code invoiceCode,
		o.invoice_no invoiceNo
		from clms_replevy_charge c,
		clms_invoice_info o
		where c.id=o.ID_AHCS_FEE_PAY
		and id_clm_payment_item = #{paySerialNo}
		and o.is_modified_flag in ('0','2')
	</select>
	<select id="getInvoiceFeeInfoByPaySerialNo" resultType="com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO">
		select id_ahcs_fee_pay idAhcsFeePay,
				case_times caseTimes
				from clms_fee_pay
				where id_clm_payment_item = (select ID_CLM_PAYMENT_ITEM from clm_payment_item_fee cpif where cpif.id_clm_payment_item_fee =#{paySerialNo})
	</select>
	<select id="getInvoiceFeeInfoByReportNo"
			resultType="com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO">
		select y.case_no caseNo,
		y.fee_amount feeAmount,
		o.return_date returnDate,
		o.return_type returnType,
		o.return_reason returnReason,
		o.is_modified_flag isModifiedFlag
		from clms_fee_pay y,
		clms_invoice_info o
		where y.id_ahcs_fee_pay=o.id_ahcs_fee_pay
		and y.report_no = #{reportNo}
		and y.case_times = #{caseTimes}
		and o.is_modified_flag in ('1','3')
		<if test="taskId != null and taskId != ''">
			and y.id_clm_payment_item = (select ID_CLM_PAYMENT_ITEM from clm_payment_item_fee cpif where cpif.id_clm_payment_item_fee = #{taskId})
		</if>
		limit 1
	</select>
	<select id="getFeeItemPays" resultMap="result4">
		select t2.report_no,
		t1.policy_no,
		t1.product_code,
		t1.policy_cer_no,
		(select department_abbr_name from department_define t3 where t3.department_code=t1.department_code) as department_name,
		t2.case_no,
		t2.case_times,
		t2.fee_amount,
		t2.fee_type,
		t2.id_ahcs_fee_pay,
		t2.id_clm_payment_info,
		t2.sub_times,
		t2.CLIENT_NAME,
		t2.CLAIM_TYPE,
		t2. ID_CLM_PAYMENT_ITEM,
		(select INVOICE_NO
		from CLMS_INVOICE_INFO
		where ID_AHCS_FEE_PAY = t2.id_ahcs_fee_pay
		limit 0,1) as INVOICE_NO,
		(select is_full_pay from clm_payment_item cpi where t2.ID_CLM_PAYMENT_ITEM = cpi.ID_CLM_PAYMENT_ITEM) isFullPay,
		t2.DUTY_CODE dutyCode
		from CLMS_policy_info t1, CLMS_fee_pay t2, clm_case_base t4
		where t1.case_no = t4.case_no
		and t2.case_no = t4.case_no
		and t2.case_times = t4.case_times

		and t2.report_no = #{reportNo}
		and t2.CASE_TIMES = #{caseTimes}
		and t2.IS_EFFECTIVE = 'Y'
		<if test="claimType != null and claimType != ''">
			and t2.CLAIM_TYPE = #{claimType}
		</if>
		<if test="subTimes != null">
			and t2.SUB_TIMES = #{subTimes}
		</if>
		<if test="taskId != null and taskId != ''">
			and t2. ID_CLM_PAYMENT_ITEM = (select ID_CLM_PAYMENT_ITEM from clm_payment_item_fee cpif where cpif.id_clm_payment_item_fee = #{taskId})
		</if>
	</select>
	<select id="getInvoiceFeeListByReportNo"
			resultType="com.paic.ncbs.claim.model.dto.pay.FeeInvoiceSendPaymentDTO">
		select
		#{idClmPaymentItem} batchNo,
		o.id_ahcs_invoice_info  idAhcsInvoiceInfo,
		o.invoice_code invoiceCode,
		o.invoice_no invoiceNo,
		o.invoice_date invoiceDate,
		o.file_id invoiceView
		from clms_fee_pay y,
		clms_invoice_info o
		where y.id_ahcs_fee_pay=o.id_ahcs_fee_pay
		and y.report_no = #{reportNo}
		and y.case_times = #{caseTimes}
		and o.is_modified_flag = '3'
		<if test="idClmPaymentItem != null and idClmPaymentItem != ''">
			and y.id_clm_payment_item = (select ID_CLM_PAYMENT_ITEM from clm_payment_item_fee cpif where cpif.id_clm_payment_item_fee = #{idClmPaymentItem})
		</if>
	</select>
</mapper>