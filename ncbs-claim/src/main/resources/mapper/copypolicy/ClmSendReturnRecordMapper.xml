<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.copypolicy.ClmSendReturnRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.copypolicy.ClmSendReturnRecord">
        <id column="id" property="id" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="send_times" property="sendTimes" />
        <result column="request_type" property="requestType" />
        <result column="request_param" property="requestParam" />
        <result column="response_param" property="responseParam" />
        <result column="is_success" property="isSuccess" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>
    <update id="updateByReportNoAndType">
        UPDATE clm_send_return_record SET  send_times=#{sendTimes}, request_param=#{requestParam}, response_param=#{responseParam}, is_success=#{isSuccess}, remark=#{remark}, sys_utime=SYSDATE() WHERE report_no=#{reportNo} and request_type =#{requestType}
        <if test="caseTimes!=null">
            and case_times = #{caseTimes}
        </if>
    </update>
    <select id="selectByReportNo" resultType="com.paic.ncbs.claim.dao.entity.copypolicy.ClmSendReturnRecord">
        select * from clm_send_return_record csrr where report_no = #{reportNo} and request_type = #{requestType}
        <if test="caseTimes!=null">
            and case_times = #{caseTimes}
        </if>
    </select>

</mapper>
