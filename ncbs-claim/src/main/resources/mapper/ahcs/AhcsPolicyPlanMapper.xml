<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanEntity">
        <id column="ID_AHCS_POLICY_PLAN" property="idAhcsPolicyPlan" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo" jdbcType="VARCHAR"/>
        <result column="PLAN_NAME" property="planName" jdbcType="VARCHAR"/>
        <result column="PLAN_CODE" property="planCode" jdbcType="VARCHAR"/>
        <result column="RESCUE_COMPANY" property="rescueCompany" jdbcType="VARCHAR"/>
        <result column="IS_MAIN" property="isMain" jdbcType="VARCHAR"/>
        <result column="GROUP_CODE" property="groupCode" jdbcType="VARCHAR"/>
        <result column="ORG_PLAN_CODE" property="orgPlanCode" jdbcType="VARCHAR"/>
        <result column="ORG_PLAN_NAME" property="orgPlanName" jdbcType="VARCHAR"/>
        <result column="APPLY_NUM" property="applyNum" jdbcType="DECIMAL"/>
        <result column="tax_rate" property="taxRate" jdbcType="DECIMAL"/>

    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_POLICY_PLAN, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        PLAN_NAME, PLAN_CODE, RESCUE_COMPANY, IS_MAIN ,GROUP_CODE, ORG_PLAN_CODE, ORG_PLAN_NAME, APPLY_NUM,tax_rate
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_PLAN
        where ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR}
    </select>
    <select id="selectByIdAhcsPolicyInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_PLAN
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>
    <select id="selectPolicyInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_PLAN a
        where
        a.plan_code = #{planCode,jdbcType=VARCHAR}
        and a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no = #{policyNo,jdbcType=VARCHAR}
        <if test="selfCardNo !=null and selfCardNo != ''">
            and b.self_card_no = #{selfCardNo,jdbcType=VARCHAR}
        </if>
        )
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_POLICY_PLAN
        where ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByIdAhcsPolicyInfo" parameterType="java.lang.String">
        delete from CLMS_POLICY_PLAN
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanEntity">
        insert into clms_policy_plan (ID_AHCS_POLICY_PLAN, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        PLAN_NAME, PLAN_CODE, RESCUE_COMPANY,
        IS_MAIN, GROUP_CODE, ORG_PLAN_CODE, ORG_PLAN_NAME, APPLY_NUM,tax_rate,
        id_ply_risk_group,risk_group_no,risk_group_name)
        values (#{idAhcsPolicyPlan,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        #{planName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{rescueCompany,jdbcType=VARCHAR},
        #{isMain,jdbcType=VARCHAR}, #{groupCode,jdbcType=VARCHAR}, #{orgPlanCode,jdbcType=VARCHAR},
        #{orgPlanName,jdbcType=VARCHAR},
        #{applyNum,jdbcType=DECIMAL},#{taxRate,jdbcType=DECIMAL},
        #{idPlyRiskGroup,jdbcType=VARCHAR},#{riskGroupNo},#{riskGroupName})
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_POLICY_PLAN (ID_AHCS_POLICY_PLAN, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        PLAN_NAME, PLAN_CODE, RESCUE_COMPANY,
        IS_MAIN, GROUP_CODE, ORG_PLAN_CODE, ORG_PLAN_NAME, APPLY_NUM,tax_rate, id_ply_risk_group, risk_group_no, risk_group_name)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsPolicyPlan,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsPolicyInfo,jdbcType=VARCHAR},
            #{item.planName,jdbcType=VARCHAR}, #{item.planCode,jdbcType=VARCHAR},
            #{item.rescueCompany,jdbcType=VARCHAR},
            #{item.isMain,jdbcType=VARCHAR}, #{item.groupCode,jdbcType=VARCHAR},
            #{item.orgPlanCode,jdbcType=VARCHAR},
            #{item.orgPlanName,jdbcType=VARCHAR}, #{item.applyNum,jdbcType=DECIMAL},#{item.taxRate,jdbcType=DECIMAL},
            #{item.idPlyRiskGroup,jdbcType=VARCHAR}, #{item.riskGroupNo,jdbcType=VARCHAR},#{item.riskGroupName,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanEntity">
        insert into CLMS_POLICY_PLAN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyPlan != null">
                ID_AHCS_POLICY_PLAN,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO,
            </if>
            <if test="planName != null">
                PLAN_NAME,
            </if>
            <if test="planCode != null">
                PLAN_CODE,
            </if>
            <if test="rescueCompany != null">
                RESCUE_COMPANY,
            </if>
            <if test="isMain != null">
                IS_MAIN,
            </if>
            <if test="groupCode != null">
                GROUP_CODE,
            </if>
            <if test="orgPlanCode != null">
                ORG_PLAN_CODE,
            </if>
            <if test="orgPlanName != null">
                ORG_PLAN_NAME,
            </if>
            <if test="applyNum != null">
                APPLY_NUM,
            </if>
            <if test="taxRate != null">
                tax_rate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyPlan != null">
                #{idAhcsPolicyPlan,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                #{planName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="rescueCompany != null">
                #{rescueCompany,jdbcType=VARCHAR},
            </if>
            <if test="isMain != null">
                #{isMain,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="orgPlanCode != null">
                #{orgPlanCode,jdbcType=VARCHAR},
            </if>
            <if test="orgPlanName != null">
                #{orgPlanName,jdbcType=VARCHAR},
            </if>
            <if test="applyNum != null">
                #{applyNum,jdbcType=DECIMAL},
            </if>
            <if test="taxRate != null">
                #{taxRate,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanEntity">
        update CLMS_POLICY_PLAN
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                PLAN_NAME = #{planName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                PLAN_CODE = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="rescueCompany != null">
                RESCUE_COMPANY = #{rescueCompany,jdbcType=VARCHAR},
            </if>
            <if test="isMain != null">
                IS_MAIN = #{isMain,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                GROUP_CODE = #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="orgPlanCode != null">
                ORG_PLAN_CODE = #{orgPlanCode,jdbcType=VARCHAR},
            </if>
            <if test="orgPlanName != null">
                ORG_PLAN_NAME = #{orgPlanName,jdbcType=VARCHAR},
            </if>
            <if test="applyNum != null">
                APPLY_NUM = #{applyNum,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanEntity">
        update CLMS_POLICY_PLAN
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        PLAN_NAME = #{planName,jdbcType=VARCHAR},
        PLAN_CODE = #{planCode,jdbcType=VARCHAR},
        RESCUE_COMPANY = #{rescueCompany,jdbcType=VARCHAR},
        IS_MAIN = #{isMain,jdbcType=VARCHAR},
        GROUP_CODE = #{groupCode,jdbcType=VARCHAR},
        ORG_PLAN_CODE = #{orgPlanCode,jdbcType=VARCHAR},
        ORG_PLAN_NAME = #{orgPlanName,jdbcType=VARCHAR},
        APPLY_NUM = #{applyNum,jdbcType=DECIMAL}
        where ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete from CLMS_POLICY_PLAN a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR})
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_PLAN
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>
    <!-- 根据保单号查询保单方案编码-->
    <select id="getPolicyProductPackage" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT PRODUCT_PACKAGE_TYPE FROM ply_risk_group
        where POLICY_NO=#{policyNo} limit 1
    </select>

    <!-- 根据报案号查询保单号和险种代码 -->
    <select id="getPolicyPlanInfo" resultType="com.paic.ncbs.claim.model.dto.settle.PolicyPlanDTO" parameterType="java.lang.String">
        SELECT
            a.report_no reportNo,
            a.POLICY_NO policyNo,
            b.PLAN_CODE planCode
        FROM
            clms_policy_info a,
            clms_policy_plan b
        WHERE
            a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
          AND a.REPORT_NO = #{reportNo}
    </select>
    <select id="getPlanList" resultType="com.paic.ncbs.claim.dao.entity.duty.ReportPlanDutyVo">
        SELECT
                *
                FROM
                clms_policy_info a,
                clms_policy_plan b
                WHERE
                a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
                AND a.REPORT_NO = #{reportNo}
    </select>
    <select id="selectTaxRateByDutyCode" resultType="java.lang.String">
        select cpd.DUTY_CODE from clms_policy_plan cpp,clms_policy_info cpi,clms_policy_duty cpd
        where cpi.report_no=#{reportNo}
        and cpd.DUTY_CODE in
        <foreach collection="dutyCodes" item="dutyCodes" open="(" separator="," close=")">
            #{dutyCodes}
        </foreach>
        and cpi.ID_AHCS_POLICY_INFO = cpp.ID_AHCS_POLICY_INFO
        and cpd.ID_AHCS_POLICY_PLAN = cpp.ID_AHCS_POLICY_PLAN
        and cpp.tax_rate >0 and cpp.tax_rate is not null;
    </select>
    <select id="getPlanCodeByDutyCode" resultType="java.lang.String">
        select PLAN_CODE
                from clms_policy_plan cpp ,
                clms_policy_duty cpd,
                clms_policy_info cpi
                where cpp.id_ahcs_policy_plan = cpd.ID_AHCS_POLICY_PLAN
                and cpd.DUTY_CODE =#{dutyCode} and cpi.report_no =#{reportNo}
                and cpi.ID_AHCS_POLICY_INFO =cpp.ID_AHCS_POLICY_INFO ;
    </select>
    <select id="getPlanTaxRate" resultType="com.paic.ncbs.policy.dto.PlanDTO">
        select * from clms_policy_plan cpp
                inner join clms_policy_info cpi on cpp.ID_AHCS_POLICY_INFO = cpi.ID_AHCS_POLICY_INFO
                where cpi.REPORT_NO =#{reportNo} group by cpp.PLAN_CODE;
    </select>
    <select id="getTaxRate" resultType="java.math.BigDecimal">
        select tax_rate  from clms_policy_plan cpp
                where id_ahcs_policy_info =
                (select ID_AHCS_POLICY_INFO from clms_policy_info cpi where report_no = #{reportNo})
                and PLAN_CODE = #{planCode} limit 1
    </select>

</mapper>