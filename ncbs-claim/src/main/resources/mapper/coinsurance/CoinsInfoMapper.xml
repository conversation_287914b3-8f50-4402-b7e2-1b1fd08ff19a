<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.coinsurance.CoinsInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.coinsurance.CoinsInfo">
        <id column="id_ahcs_coins_info" property="idAhcsCoinsInfo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="reinsure_company_name" property="reinsureCompanyName" />
        <result column="company_flag" property="companyFlag" />
        <result column="accept_insurance_flag" property="acceptInsuranceFlag" />
        <result column="reinsure_scale" property="reinsureScale" />
        <result column="currency" property="currency" />
        <result column="pay_item" property="payItem" />
        <result column="Coins_amount" property="coinsAmount" />
        <result column="policy_no" property="policyNo" />
        <result column="amortization_flag" property="amortizationFlag" />
        <result column="re_coins_amount" property="reCoinsAmount" />
        <result column="retrun_date" property="retrunDate" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>
    <insert id="insertList">
        <foreach collection="coinsInfoList" item="item" separator=";">
            INSERT INTO
            clm_coins_info
            (id_ahcs_coins_info,
            report_no,
            case_times,
            reinsure_company_name,
            company_flag,
            accept_insurance_flag,
            reinsure_scale,
            currency,
            pay_item,
            Coins_amount,
            created_by,
            sys_ctime,
            updated_by,
            sys_utime,
            policy_no,
            amortization_flag)
            VALUES(#{item.idAhcsCoinsInfo},
            #{item.reportNo},
            #{item.caseTimes},
            #{item.reinsureCompanyName},
            #{item.companyFlag},
            #{item.acceptInsuranceFlag},
            #{item.reinsureScale},
            #{item.currency},
            #{item.payItem},
            #{item.coinsAmount},
            #{item.createdBy},
            now(),
            #{item.updatedBy},
            now(),
            #{item.policyNo},
            #{item.amortizationFlag})
        </foreach>
    </insert>
    <select id="selectCoinsList" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmortizationVo">
        select id_ahcs_coins_info idAhcsCoinsInfo,
        cpi.REPORT_NO reportNo,
        cci.case_times caseTimes,
        cpi.POLICY_NO policyNo,
        cci.reinsure_company_name reinsureCompanyName,
        cci.company_flag companyFlag,
        cci.accept_insurance_flag acceptInsuranceFlag,
        cci.reinsure_scale reinsureScale,
        cci.currency currency,
        cci.pay_item payItem,
        cci.Coins_amount coinsAmount,
        cci.amortization_flag amortizationFlag,
        cci.re_coins_amount reCoinsAmount,
        cci.retrun_date retrunDate,
        cpi.DEPARTMENT_CODE departmentCode
        from clm_coins_info cci , clms_policy_info cpi
                where cci.report_no = cpi.REPORT_NO
        <if test="reportNo!=null and reportNo !=''">
            and cci.report_no = #{reportNo}
        </if>
        <if test="departmentCodes != null">
            and cpi.DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="departmentCode != null and departmentCode!=''">
            and cpi.DEPARTMENT_CODE = #{departmentCode}
        </if>
        <if test="policyNo!=null and policyNo!=''">
            and cci.policy_no = #{policyNo}
        </if>
        <if test="reinsureCompanyName!=null and reinsureCompanyName!=''">
            and cci.reinsure_company_name like CONCAT('%',#{reinsureCompanyName},'%')
        </if>
        <if test="payItem!=null and payItem!=''">
            and cci.pay_item = #{payItem}
        </if>
        <if test="amortizationFlag!=null and amortizationFlag!=''">
            and cci.amortization_flag=#{amortizationFlag}
        </if>
    </select>
    <select id="selectByIdList" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmortizationVo">
        select id_ahcs_coins_info idAhcsCoinsInfo,
        cpi.REPORT_NO reportNo,
        cci.case_times caseTimes,
        cpi.POLICY_NO policyNo,
        cci.reinsure_company_name reinsureCompanyName,
        cci.company_flag companyFlag,
        cci.accept_insurance_flag acceptInsuranceFlag,
        cci.reinsure_scale reinsureScale,
        cci.currency currency,
        cci.pay_item payItem,
        cci.Coins_amount coinsAmount,
        cci.amortization_flag amortizationFlag,
        cci.re_coins_amount reCoinsAmount,
        cci.retrun_date retrunDate,
        cpi.DEPARTMENT_CODE departmentCode
        from clm_coins_info cci , clms_policy_info cpi
                where cci.report_no = cpi.REPORT_NO
        and cci.id_ahcs_coins_info in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
