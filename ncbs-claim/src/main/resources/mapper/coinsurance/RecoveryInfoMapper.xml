<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.coinsurance.RecoveryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryInfo">
        <id column="id_coins_info" property="idCoinsInfo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="coins_company_code" property="coinsCompanyCode" />
        <result column="coins_company_name" property="coinsCompanyName" />
        <result column="company_flag" property="companyFlag" />
        <result column="main_flag" property="mainFlag" />
        <result column="coins_rate" property="coinsRate" />
        <result column="currency" property="currency" />
        <result column="pay_item" property="payItem" />
        <result column="coins_amount" property="coinsAmount" />
        <result column="policy_no" property="policyNo" />
        <result column="recovery_flag" property="recoveryFlag" />
        <result column="recovery_amount" property="recoveryAmount" />
        <result column="recovery_date" property="recoveryDate" />
        <result column="recovery_batch_no" property="recoveryBatchNo" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="sub_times" property="subTimes" />
    </resultMap>
    <insert id="insertList">
        INSERT clms_recovery_info
        (
        id_coins_info,
        report_no,
        case_times,
        coins_company_code,
        coins_company_name,
        company_flag,
        main_flag,
        coins_rate,
        currency,
        pay_item,
        coins_amount,
        policy_no,
        recovery_flag,
        recovery_amount,
        recovery_date,
        recovery_batch_no,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime,
        sub_times
        )
        VALUES
        <foreach collection="recoveryInfoList" item="item" index="index" separator="," >
        (#{item.idCoinsInfo},
        #{item.reportNo},
        #{item.caseTimes},
        #{item.coinsCompanyCode},
        #{item.coinsCompanyName},
        #{item.companyFlag},
        #{item.mainFlag},
        #{item.coinsRate},
        #{item.currency},
        #{item.payItem},
        #{item.coinsAmount},
        #{item.policyNo},
        #{item.recoveryFlag},
        #{item.recoveryAmount},
        #{item.recoveryDate},
        #{item.recoveryBatchNo},
        #{item.createdBy},
        now(),
        #{item.updatedBy},
        now(),
        #{item.subTimes}
            )
        </foreach>
    </insert>
    <update id="updateList">
        <foreach collection="coinsAmortizationVoList" item="coinsAmor" separator=";">
        update clms_recovery_info
        set id_recovery_record = #{idRecoveryRecord},
        recovery_amount = #{coinsAmor.reCoinsAmount},
        recovery_flag = '1',
        recovery_date = now()
        where id_coins_info =
            #{coinsAmor.idCoinsInfo}
        </foreach>
    </update>
    <update id="updateByRecovery">
        update clms_recovery_info
        <if test="paymentStatus == '31'">
            set
            recovery_date = now(),
            recovery_flag = '1'
        </if>
        <if test="paymentStatus == '32'">
            set
            recovery_flag = '0',
            recovery_date = null,
            id_recovery_record = null
        </if>
        where id_recovery_record = #{idRecoveryRecord}
    </update>
    <select id="selectCoinsList" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmortizationVo">
        select id_coins_info idCoinsInfo,
        cpi.REPORT_NO reportNo,
        cri.case_times caseTimes,
        cpi.POLICY_NO policyNo,
        cri.coins_company_name coinsCompanyName,
        cri.company_flag companyFlag,
        cri.main_flag acceptInsuranceFlag,
        cri.coins_rate reinsureScale,
        cri.currency currency,
        cri.pay_item payItem,
        cri.Coins_amount coinsAmount,
        cri.recovery_flag amortizationFlag,
        cri.recovery_amount reCoinsAmount,
        cri.recovery_date retrunDate,
        cri.sub_times,
        (select department_abbr_name
        from department_define
        where department_define.department_code =cpi.DEPARTMENT_CODE) departmentCode
        from clms_recovery_info cri , clms_policy_info cpi
        where cri.report_no = cpi.REPORT_NO
        <if test="reportNo!=null and reportNo !=''">
            and cri.report_no = #{reportNo}
        </if>
        <if test="departmentCodes != null">
            and cpi.DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="departmentCode != null and departmentCode!=''">
            and cpi.DEPARTMENT_CODE = #{departmentCode}
        </if>
        <if test="policyNo!=null and policyNo!=''">
            and cri.policy_no = #{policyNo}
        </if>
        <if test="coinsCompanyName!=null and coinsCompanyName!=''">
            and cri.coins_company_name like CONCAT('%',#{coinsCompanyName},'%')
        </if>
        <if test="payItem!=null and payItem!=''">
            and cri.pay_item = #{payItem}
        </if>
        <if test="amortizationFlag!=null and amortizationFlag!=''">
            and cri.recovery_flag=#{amortizationFlag}
        </if>
    </select>
    <select id="selectByIdList" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmortizationVo">
        select id_coins_info idCoinsInfo,
        cpi.REPORT_NO reportNo,
        cri.case_times caseTimes,
        cpi.POLICY_NO policyNo,
        cri.coins_company_name coinsCompanyName,
        cri.coins_company_code coinsCompanyCode,
        cri.company_flag companyFlag,
        cri.main_flag acceptInsuranceFlag,
        cri.coins_rate reinsureScale,
        cri.currency currency,
        cri.pay_item payItem,
        cri.Coins_amount coinsAmount,
        cri.recovery_flag amortizationFlag,
        cri.recovery_amount reCoinsAmount,
        cri.recovery_date retrunDate,
        cpi.DEPARTMENT_CODE departmentCode,
        cri.id_recovery_record idRecoveryRecord,
        cri.sub_times
        from clms_recovery_info cri , clms_policy_info cpi
        where cri.report_no = cpi.REPORT_NO
        and cri.id_coins_info in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectByIdRecovery" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryInfo">
        select * from clms_recovery_info where id_recovery_record = #{idRecoveryRecord}
    </select>
    <select id="selectAmortByRecordId"
            resultType="com.paic.ncbs.claim.dao.entity.coinsurance.CoinsAmortizationVo">
        select id_coins_info idCoinsInfo,
                cpi.REPORT_NO reportNo,
                cri.case_times caseTimes,
                cpi.POLICY_NO policyNo,
                cri.coins_company_name coinsCompanyName,
                cri.coins_company_code coinsCompanyCode,
                cri.company_flag companyFlag,
                cri.main_flag acceptInsuranceFlag,
                cri.coins_rate reinsureScale,
                cri.currency currency,
                cri.pay_item payItem,
                cri.Coins_amount coinsAmount,
                cri.recovery_flag amortizationFlag,
                cri.recovery_amount reCoinsAmount,
                cri.recovery_date retrunDate,
                cpi.DEPARTMENT_CODE departmentCode,
                cri.id_recovery_record idRecoveryRecord,
                cri.sub_times
                from clms_recovery_info cri , clms_policy_info cpi
                where cri.report_no = cpi.REPORT_NO
                and cri.id_recovery_record = #{idRecoveryRecord}
    </select>

</mapper>
