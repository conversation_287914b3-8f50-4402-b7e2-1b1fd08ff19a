<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.coinsurance.PaymentItemFeeMapper">

    <sql id="Base_Column_List">
        ID_CLM_PAYMENT_ITEM_FEE,ID_CLM_PAYMENT_ITEM,POLICY_NO,REPORT_NO,CASE_NO,ID_CLM_BATCH,CASE_TIMES,CLAIM_TYPE,SUB_TIMES,PAYMENT_TYPE,ID_CLM_PAYMENT_INFO,
        COLLECT_PAY_SIGN,PAYMENT_AMOUNT,PAYMENT_CURRENCY_CODE,CLIENT_NAME,CLIENT_CERTIFICATE_TYPE,CLIENT_CERTIFICATE_NO,
        CLIENT_BANK_CODE,CLIENT_BANK_NAME,CLIENT_BANK_ACCOUNT,CLIENT_MOBILE,CLIENT_TYPE,PROVINCE_NAME,CITY_NAME,REGION_CODE,
        COLLECT_PAY_APPROACH,BANK_ACCOUNT_ATTRIBUTE,MERGE_SIGN,PAYMENT_ITEM_STATUS,EFFECTIVE_DATE,INVALIDATE_DATE,
        REMARK,IS_COINSURE,MIGRATE_FROM,COLLECT_PAY_NO,HIS_ID_CLM_PAYMENT_NOTICE,ID_CLM_SUBROGATION_SETTLE,EXCHANGE_RATE,CONVERT_AMOUNT,
        EXTEND_INFO ,BANK_DETAIL,CREATED_BY,sys_ctime as CREATED_DATE,UPDATED_BY,sys_utime as UPDATED_DATE,MODIFY_INFO,PAY_DATE,PAY_BACK_DATE,FINANCIAL_ID,
        COMPENSATE_NO,SERIAL_NO,ORGANIZE_CODE,CLIENT_RELATION,BANK_DETAIL_CODE,OPEN_ID,PAY_TYPE,FEE_TYPE,
        COINSURANCE_MARK,ACCEPT_INSURANCE_FLAG,COINSURANCE_COMPANY_CODE,COINSURANCE_COMPANY_NAME,COINSURANCE_RATIO,IS_FULL_PAY,COINSURANCE_ACTUAL_AMOUNT,
        FINANCE_PAYMENT_AMOUNT,PAYMENT_VOUCHER_URL,
        (case when CASE_TIMES=1 then PAYMENT_AMOUNT
        else ifnull(CHG_PAYMENT_AMOUNT,FINANCE_PAYMENT_AMOUNT) end) as CHG_PAYMENT_AMOUNT,
        BATCH_NO,CHG_COINSURANCE_ACTUAL_AMOUNT,CHG_PAID_AMOUNT
        ,CUSTOMER_NO,id
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.coinsurance.PaymentItemFee">
        <id column="id_clm_payment_item_fee" property="idClmPaymentItemFee" />
        <result column="id_clm_payment_item" property="idClmPaymentItem" />
        <result column="policy_no" property="policyNo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="case_no" property="caseNo" />
        <result column="id_clm_batch" property="idClmBatch" />
        <result column="claim_type" property="claimType" />
        <result column="sub_times" property="subTimes" />
        <result column="payment_type" property="paymentType" />
        <result column="id_clm_payment_info" property="idClmPaymentInfo" />
        <result column="collect_pay_sign" property="collectPaySign" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="payment_currency_code" property="paymentCurrencyCode" />
        <result column="client_name" property="clientName" />
        <result column="client_certificate_type" property="clientCertificateType" />
        <result column="client_certificate_no" property="clientCertificateNo" />
        <result column="client_bank_code" property="clientBankCode" />
        <result column="client_bank_name" property="clientBankName" />
        <result column="client_bank_account" property="clientBankAccount" />
        <result column="client_mobile" property="clientMobile" />
        <result column="client_type" property="clientType" />
        <result column="province_name" property="provinceName" />
        <result column="city_name" property="cityName" />
        <result column="region_code" property="regionCode" />
        <result column="collect_pay_approach" property="collectPayApproach" />
        <result column="bank_account_attribute" property="bankAccountAttribute" />
        <result column="merge_sign" property="mergeSign" />
        <result column="payment_item_status" property="paymentItemStatus" />
        <result column="effective_date" property="effectiveDate" />
        <result column="invalidate_date" property="invalidateDate" />
        <result column="remark" property="remark" />
        <result column="is_coinsure" property="isCoinsure" />
        <result column="migrate_from" property="migrateFrom" />
        <result column="collect_pay_no" property="collectPayNo" />
        <result column="his_id_clm_payment_notice" property="hisIdClmPaymentNotice" />
        <result column="id_clm_subrogation_settle" property="idClmSubrogationSettle" />
        <result column="exchange_rate" property="exchangeRate" />
        <result column="convert_amount" property="convertAmount" />
        <result column="archive_date" property="archiveDate" />
        <result column="extend_info" property="extendInfo" />
        <result column="bank_detail" property="bankDetail" />
        <result column="modify_info" property="modifyInfo" />
        <result column="pay_date" property="payDate" />
        <result column="pay_back_date" property="payBackDate" />
        <result column="financial_id" property="financialId" />
        <result column="compensate_no" property="compensateNo" />
        <result column="serial_no" property="serialNo" />
        <result column="organize_code" property="organizeCode" />
        <result column="client_relation" property="clientRelation" />
        <result column="bank_detail_code" property="bankDetailCode" />
        <result column="sdb_mark" property="sdbMark" />
        <result column="agency_type" property="agencyType" />
        <result column="customer_no" property="customerNo" />
        <result column="company_card_type" property="companyCardType" />
        <result column="open_id" property="openId" />
        <result column="pay_type" property="payType" />
        <result column="fee_type" property="feeType" />
        <result column="coinsurance_mark" property="coinsuranceMark" />
        <result column="accept_insurance_flag" property="acceptInsuranceFlag" />
        <result column="coinsurance_company_code" property="coinsuranceCompanyCode" />
        <result column="coinsurance_company_name" property="coinsuranceCompanyName" />
        <result column="coinsurance_ratio" property="coinsuranceRatio" />
        <result column="is_full_pay" property="isFullPay" />
        <result column="coinsurance_actual_amount" property="coinsuranceActualAmount" />
        <result column="finance_payment_amount" property="financePaymentAmount" />
        <result column="payment_voucher_url" property="paymentVoucherUrl" />
        <result column="id" property="id" />
        <result column="batch_no" property="batchNo" />
        <result column="chg_payment_amount" property="chgPaymentAmount" />
        <result column="chg_coinsurance_actual_amount" property="chgCoinsuranceActualAmount" />
        <result column="chg_paid_amount" property="chgPaidAmount" />
        <result column="coinsurance_actual_not_tax_fee" property="coinsuranceActualNotTaxFee" />
        <result column="coinsurance_actual_tax" property="coinsuranceActualTax" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>
    <resultMap id="PaymentItemResultMap" type="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
        <id column="id_clm_payment_item" property="idClmPaymentItem" />
        <id column="id_clm_payment_item_fee" property="idClmPaymentItemFee" />
        <result column="policy_no" property="policyNo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="case_no" property="caseNo" />
        <result column="id_clm_batch" property="idClmBatch" />
        <result column="claim_type" property="claimType" />
        <result column="sub_times" property="subTimes" />
        <result column="payment_type" property="paymentType" />
        <result column="id_clm_payment_info" property="idClmPaymentInfo" />
        <result column="collect_pay_sign" property="collectPaySign" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="payment_currency_code" property="paymentCurrencyCode" />
        <result column="client_name" property="clientName" />
        <result column="client_certificate_type" property="clientCertificateType" />
        <result column="client_certificate_no" property="clientCertificateNo" />
        <result column="client_bank_code" property="clientBankCode" />
        <result column="client_bank_name" property="clientBankName" />
        <result column="client_bank_account" property="clientBankAccount" />
        <result column="client_mobile" property="clientMobile" />
        <result column="client_type" property="clientType" />
        <result column="province_name" property="provinceName" />
        <result column="city_name" property="cityName" />
        <result column="region_code" property="regionCode" />
        <result column="collect_pay_approach" property="collectPayApproach" />
        <result column="bank_account_attribute" property="bankAccountAttribute" />
        <result column="merge_sign" property="mergeSign" />
        <result column="payment_item_status" property="paymentItemStatus" />
        <result column="effective_date" property="effectiveDate" />
        <result column="invalidate_date" property="invalidateDate" />
        <result column="remark" property="remark" />
        <result column="is_coinsure" property="isCoinsure" />
        <result column="migrate_from" property="migrateFrom" />
        <result column="collect_pay_no" property="collectPayNo" />
        <result column="his_id_clm_payment_notice" property="hisIdClmPaymentNotice" />
        <result column="id_clm_subrogation_settle" property="idClmSubrogationSettle" />
        <result column="exchange_rate" property="exchangeRate" />
        <result column="convert_amount" property="convertAmount" />
        <result column="archive_date" property="archiveDate" />
        <result column="extend_info" property="extendInfo" />
        <result column="bank_detail" property="bankDetail" />
        <result column="modify_info" property="modifyInfo" />
        <result column="pay_date" property="payDate" />
        <result column="pay_back_date" property="payBackDate" />
        <result column="financial_id" property="financialId" />
        <result column="compensate_no" property="compensateNo" />
        <result column="serial_no" property="serialNo" />
        <result column="organize_code" property="organizeCode" />
        <result column="client_relation" property="clientRelation" />
        <result column="bank_detail_code" property="bankDetailCode" />
        <result column="agency_type" property="agencyType" />
        <result column="customer_no" property="customerNo" />
        <result column="company_card_type" property="companyCardType" />
        <result column="open_id" property="openId" />
        <result column="pay_type" property="payType" />
        <result column="fee_type" property="feeType" />
        <result column="coinsurance_mark" property="coinsuranceMark" />
        <result column="accept_insurance_flag" property="acceptInsuranceFlag" />
        <result column="coinsurance_company_code" property="coinsuranceCompanyCode" />
        <result column="coinsurance_company_name" property="coinsuranceCompanyName" />
        <result column="coinsurance_ratio" property="coinsuranceRatio" />
        <result column="is_full_pay" property="isFullPay" />
        <result column="coinsurance_actual_amount" property="coinsuranceActualAmount" />
        <result column="coinsurance_actual_tax" property="coinsuranceActualTax" />
        <result column="finance_payment_amount" property="financePaymentAmount" />
        <result column="coinsurance_actual_not_tax_fee" property="coinsuranceActualNoTax" />
        <result column="payment_voucher_url" property="paymentVoucherUrl" />
        <result column="id" property="id" />
        <result column="batch_no" property="batchNo" />
        <result column="chg_payment_amount" property="chgPaymentAmount" />
        <result column="chg_coinsurance_actual_amount" property="chgCoinsuranceActualAmount" />
        <result column="chg_paid_amount" property="chgPaidAmount" />
    </resultMap>
    <resultMap id="resultDTO" type="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
        <id column="ID_CLM_PAYMENT_ITEM" property="idClmPaymentItem"/>
        <result column="POLICY_NO" property="policyNo"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_NO" property="caseNo"/>
        <result column="ID_CLM_BATCH" property="idClmBatch"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="CLAIM_TYPE" property="claimType"/>
        <result column="SUB_TIMES"  property="subTimes"/>
        <result column="PAYMENT_TYPE"  property="paymentType"/>
        <result column="ID_CLM_PAYMENT_INFO"  property="idClmPaymentInfo"/>
        <result column="COLLECT_PAY_SIGN"  property="collectPaySign"/>
        <result column="PAYMENT_AMOUNT"  property="paymentAmount"/>
        <result column="PAYMENT_CURRENCY_CODE"  property="paymentCurrencyCode"/>
        <result column="CLIENT_NAME"  property="clientName"/>
        <result column="CLIENT_CERTIFICATE_TYPE"  property="clientCertificateType"/>
        <result column="CLIENT_CERTIFICATE_NO"  property="clientCertificateNo"/>
        <result column="CLIENT_BANK_CODE"  property="clientBankCode"/>
        <result column="CLIENT_BANK_NAME"  property="clientBankName"/>
        <result column="CLIENT_BANK_ACCOUNT"  property="clientBankAccount"/>
        <result column="CLIENT_MOBILE"  property="clientMobile"/>
        <result column="CLIENT_TYPE"  property="clientType"/>
        <result column="PROVINCE_NAME"  property="provinceName"/>
        <result column="CITY_NAME"  property="cityName"/>
        <result column="REGION_CODE"  property="regionCode"/>
        <result column="COLLECT_PAY_APPROACH"  property="collectPayApproach"/>
        <result column="BANK_ACCOUNT_ATTRIBUTE"  property="bankAccountAttribute"/>
        <result column="MERGE_SIGN"  property="mergeSign"/>
        <result column="PAYMENT_ITEM_STATUS"  property="paymentItemStatus"/>
        <result column="EFFECTIVE_DATE"  property="effectiveDate"/>
        <result column="INVALIDATE_DATE"  property="invalidateDate"/>
        <result column="REMARK"  property="remark"/>
        <result column="IS_COINSURE"  property="isCoinsure"/>
        <result column="MIGRATE_FROM"  property="migrateFrom"/>
        <result column="COLLECT_PAY_NO"  property="collectPayNo"/>
        <result column="HIS_ID_CLM_PAYMENT_NOTICE"  property="hisIdClmPaymentNotice"/>
        <result column="ID_CLM_SUBROGATION_SETTLE"  property="idClmSubrogationSettle"/>
        <result column="EXCHANGE_RATE"  property="exchangeRate"/>
        <result column="CONVERT_AMOUNT"  property="convertAmount"/>
        <result column="EXTEND_INFO"  property="extendInfo"/>
        <result column="BANK_DETAIL"  property="bankDetail"/>
        <result column="CREATED_BY"  property="createdBy"/>
        <result column="CREATED_DATE"  property="createdDate"/>
        <result column="UPDATED_BY"  property="updatedBy"/>
        <result column="UPDATED_DATE"  property="updatedDate"/>
        <result column="MODIFY_INFO"  property="modifyInfo"/>
        <result column="SERIAL_NO"  property="serialNo"/>
        <result column="ORGANIZE_CODE"  property="organizeCode"/>
        <result column="COMPENSATE_NO"  property="compensateNo"/>
        <result column="PAY_DATE"  property="payDate"/>
        <result column="PAY_BACK_DATE"  property="payBackDate"/>
        <result column="FINANCIAL_ID"  property="financialId"/>
        <result column="COMPENSATE_NO"  property="compensateNo"/>
        <result column="PAY_DATE"  property="payDate"/>
        <result column="PAY_BACK_DATE"  property="payBackDate"/>
        <result column="FINANCIAL_ID"  property="financialId"/>
        <result column="CLIENT_RELATION"  property="clientRelation"/>
        <result column="BANK_DETAIL_CODE"  property="bankDetailCode"/>
        <result column="OPEN_ID" property="openId"/>
        <result column="PAY_TYPE" property="payType"/>
        <result column="FEE_TYPE"  property="feeType"/>
        <result column="COINSURANCE_MARK"  property="coinsuranceMark"/>
        <result column="ACCEPT_INSURANCE_FLAG"  property="acceptInsuranceFlag"/>
        <result column="COINSURANCE_COMPANY_CODE"  property="coinsuranceCompanyCode"/>
        <result column="COINSURANCE_COMPANY_NAME"  property="coinsuranceCompanyName"/>
        <result column="COINSURANCE_RATIO"  property="coinsuranceRatio"/>
        <result column="IS_FULL_PAY"  property="isFullPay"/>
        <result column="COINSURANCE_ACTUAL_AMOUNT"  property="coinsuranceActualAmount"/>
        <result column="FINANCE_PAYMENT_AMOUNT" property="financePaymentAmount"/>
        <result column="PAYMENT_VOUCHER_URL" property="paymentVoucherUrl"/>
        <result column="WHOLE_CASE_STATUS" property="wholeCaseStatus"/>
        <result column="PRODUCT_CODE" property="productCode"/>
    </resultMap>

    <insert id="insertList">
            INSERT INTO CLM_PAYMENT_ITEM_fee(
            CREATED_BY,
            sys_ctime,
            UPDATED_BY,
            sys_utime,
            ID_CLM_PAYMENT_ITEM,
            POLICY_NO,
            REPORT_NO,
            CASE_NO,
            ID_CLM_BATCH,
            CASE_TIMES,
            CLAIM_TYPE,
            SUB_TIMES,
            PAYMENT_TYPE,
            ID_CLM_PAYMENT_INFO,
            COLLECT_PAY_SIGN,
            PAYMENT_AMOUNT,
            PAYMENT_CURRENCY_CODE,
            CLIENT_NAME,
            CLIENT_CERTIFICATE_TYPE,
            CLIENT_CERTIFICATE_NO,
            CLIENT_BANK_CODE,
            CLIENT_BANK_NAME,
            CLIENT_BANK_ACCOUNT,
            CLIENT_MOBILE,
            CLIENT_TYPE,
            PROVINCE_NAME,
            CITY_NAME,
            REGION_CODE,
            COLLECT_PAY_APPROACH,
            BANK_ACCOUNT_ATTRIBUTE,
            MERGE_SIGN,
            PAYMENT_ITEM_STATUS,
            EFFECTIVE_DATE,
            INVALIDATE_DATE,
            REMARK,
            IS_COINSURE,
            MIGRATE_FROM,
            COLLECT_PAY_NO,
            HIS_ID_CLM_PAYMENT_NOTICE,
            ID_CLM_SUBROGATION_SETTLE,
            EXCHANGE_RATE,
            CONVERT_AMOUNT,
            EXTEND_INFO,BANK_DETAIL,COMPENSATE_NO,SERIAL_NO,ORGANIZE_CODE,CLIENT_RELATION,BANK_DETAIL_CODE,
            AGENCY_TYPE,CUSTOMER_NO,COMPANY_CARD_TYPE,OPEN_ID,PAY_TYPE,FEE_TYPE,
            COINSURANCE_MARK,
            ACCEPT_INSURANCE_FLAG,
            COINSURANCE_COMPANY_CODE,
            COINSURANCE_COMPANY_NAME,
            COINSURANCE_RATIO,
            IS_FULL_PAY,
            COINSURANCE_ACTUAL_AMOUNT,
            FINANCE_PAYMENT_AMOUNT,
            PAYMENT_VOUCHER_URL,
            id_clm_payment_item_fee,
            coinsurance_actual_not_tax_fee,
            coinsurance_actual_tax)
            VALUES
            <foreach collection="paymentItemFeeList" item="item" index="index" separator="," >
                (#{item.createdBy,jdbcType=VARCHAR},
                now(),
                #{item.updatedBy,jdbcType=VARCHAR},
                now(),
                #{item.idClmPaymentItem,jdbcType=VARCHAR},
                #{item.policyNo,jdbcType=VARCHAR},
                #{item.reportNo,jdbcType=VARCHAR},
                #{item.caseNo,jdbcType=VARCHAR},
                #{item.idClmBatch,jdbcType=VARCHAR},
                #{item.caseTimes,jdbcType=NUMERIC},
                #{item.claimType,jdbcType=VARCHAR},
                #{item.subTimes,jdbcType=NUMERIC},
                #{item.paymentType,jdbcType=VARCHAR},
                #{item.idClmPaymentInfo,jdbcType=VARCHAR},
                #{item.collectPaySign,jdbcType=VARCHAR},
                #{item.paymentAmount,jdbcType=DECIMAL},
                #{item.paymentCurrencyCode,jdbcType=VARCHAR},
                #{item.clientName,jdbcType=VARCHAR},
                #{item.clientCertificateType,jdbcType=VARCHAR},
                #{item.clientCertificateNo,jdbcType=VARCHAR},
                #{item.clientBankCode,jdbcType=VARCHAR},
                #{item.clientBankName,jdbcType=VARCHAR},
                #{item.clientBankAccount,jdbcType=VARCHAR},
                #{item.clientMobile,jdbcType=VARCHAR},
                #{item.clientType,jdbcType=VARCHAR},
                #{item.provinceName,jdbcType=VARCHAR},
                #{item.cityName,jdbcType=VARCHAR},
                #{item.regionCode,jdbcType=VARCHAR},
                #{item.collectPayApproach,jdbcType=VARCHAR},
                #{item.bankAccountAttribute,jdbcType=VARCHAR},
                #{item.mergeSign,jdbcType=VARCHAR},
                #{item.paymentItemStatus,jdbcType=VARCHAR},
                #{item.effectiveDate,jdbcType=TIMESTAMP},
                #{item.invalidateDate,jdbcType=TIMESTAMP},
                #{item.remark,jdbcType=VARCHAR},
                #{item.isCoinsure,jdbcType=VARCHAR},
                #{item.migrateFrom,jdbcType=VARCHAR},
                #{item.collectPayNo,jdbcType=VARCHAR},
                #{item.hisIdClmPaymentNotice,jdbcType=VARCHAR},
                #{item.idClmSubrogationSettle,jdbcType=VARCHAR},
                #{item.exchangeRate,jdbcType=VARCHAR},
                #{item.convertAmount,jdbcType=VARCHAR},
                #{item.extendInfo,jdbcType=VARCHAR},#{item.bankDetail,jdbcType=VARCHAR},#{item.compensateNo,jdbcType=VARCHAR},
                #{item.serialNo,jdbcType=NUMERIC},#{item.organizeCode,jdbcType=VARCHAR},
                #{item.clientRelation,jdbcType=NUMERIC},
                #{item.bankDetailCode,jdbcType=NUMERIC},
                #{item.agencyType,jdbcType=VARCHAR},
                #{item.customerNo,jdbcType=VARCHAR},
                #{item.companyCardType,jdbcType=VARCHAR},
                #{item.openId,jdbcType=VARCHAR},
                #{item.payType,jdbcType=VARCHAR},
                #{item.feeType,jdbcType=VARCHAR},
                #{item.coinsuranceMark,jdbcType=VARCHAR},
                #{item.acceptInsuranceFlag,jdbcType=VARCHAR},
                #{item.coinsuranceCompanyCode,jdbcType=VARCHAR},
                #{item.coinsuranceCompanyName,jdbcType=VARCHAR},
                #{item.coinsuranceRatio,jdbcType=DECIMAL},
                #{item.isFullPay,jdbcType=VARCHAR},
                #{item.coinsuranceActualAmount,jdbcType=DECIMAL},
                #{item.financePaymentAmount,jdbcType=DECIMAL},
                #{item.paymentVoucherUrl,jdbcType=VARCHAR},
                #{item.idClmPaymentItemFee},
                #{item.coinsuranceActualNotTaxFee},
                #{item.coinsuranceActualTax})
            </foreach>
    </insert>
    <update id="updateItemStatusByRecovery">
        <foreach collection="recoveryInfoList" item="recovery">
            update clm_payment_item_fee cpi
            set cpi.sys_utime = now(),
            cpi.PAYMENT_ITEM_STATUS = #{paymentStatus,jdbcType=VARCHAR}
            where REPORT_NO =#{recovery.reportNo} and CASE_TIMES  =#{recovery.caseTimes} and COINSURANCE_COMPANY_CODE =#{recovery.coinsCompanyCode}
        </foreach>
    </update>
    <update id="updatePaymentItemFee">
        UPDATE CLM_PAYMENT_ITEM_fee
        SET sys_utime = now(),
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR}
        <if test="serialNo != null">
            , SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
        </if>
        <if test="organizeCode != null">
            , ORGANIZE_CODE = #{organizeCode,jdbcType=VARCHAR}
        </if>
        <if test="paymentItemStatus != null">
            , PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
        </if>
        <if test="clientName != null">
            , CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
        </if>
        <if test="paymentAmount != null">
            , PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL}
        </if>
        <if test="policyNo != null">
            , POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        </if>
        <if test="hisIdClmPaymentNotice != null">
            , HIS_ID_CLM_PAYMENT_NOTICE = #{hisIdClmPaymentNotice,jdbcType=VARCHAR}
        </if>
        <if test="extendInfo != null">
            , EXTEND_INFO = #{extendInfo,jdbcType=VARCHAR}
        </if>
        <if test="bankDetail != null">
            , BANK_DETAIL = #{bankDetail,jdbcType=VARCHAR}
        </if>
        <if test="clientName != null">, CLIENT_NAME = #{clientName,jdbcType=VARCHAR} </if>
        <if test="effectiveDate != null">, EFFECTIVE_DATE = #{effectiveDate,jdbcType=TIMESTAMP} </if>
        <if test="clientCertificateType != null">, CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR} </if>
        <if test="clientCertificateNo != null">, CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR} </if>
        <if test="clientBankCode != null">, CLIENT_BANK_CODE = #{clientBankCode,jdbcType=VARCHAR} </if>
        <if test="clientBankName != null">, CLIENT_BANK_NAME = #{clientBankName,jdbcType=VARCHAR} </if>
        <if test="clientBankAccount != null">, CLIENT_BANK_ACCOUNT = #{clientBankAccount,jdbcType=VARCHAR} </if>
        <if test="clientMobile != null">, CLIENT_MOBILE = #{clientMobile,jdbcType=VARCHAR} </if>
        <if test="clientType != null">, CLIENT_TYPE = #{clientType,jdbcType=VARCHAR} </if>
        <if test="provinceName != null">, PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR} </if>
        <if test="cityName != null">, CITY_NAME = #{cityName,jdbcType=VARCHAR} </if>
        <if test="regionCode != null">, REGION_CODE = #{regionCode,jdbcType=VARCHAR} </if>
        <if test="invalidateDate != null">, INVALIDATE_DATE = #{invalidateDate,jdbcType=TIMESTAMP} </if>
        <if test="remark != null">, REMARK = #{remark,jdbcType=VARCHAR} </if>
        <if test="archiveDate != null">, ARCHIVE_DATE = #{archiveDate,jdbcType=TIMESTAMP} </if>
        <if test="modifyInfo != null">, MODIFY_INFO = #{modifyInfo,jdbcType=VARCHAR} </if>
        <if test="payDate != null">, PAY_DATE = #{payDate,jdbcType=TIMESTAMP} </if>
        <if test="payBackDate != null">, PAY_BACK_DATE = #{payBackDate,jdbcType=TIMESTAMP} </if>
        <if test="financialId != null">, FINANCIAL_ID = #{financialId,jdbcType=VARCHAR} </if>
        <if test="compensateNo != null">, COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR} </if>
        <if test="bankDetailCode != null">, BANK_DETAIL_CODE = #{bankDetailCode,jdbcType=VARCHAR}</if>
        <if test="clientRelation != null">, CLIENT_RELATION = #{clientRelation,jdbcType=VARCHAR}</if>
        <if test="customerNo != null">, customer_no = #{customerNo}</if>
        <if test="companyCardType != null and companyCardType!=''">
            ,COMPANY_CARD_TYPE=#{companyCardType,jdbcType=VARCHAR}
        </if>
        <if test="feeType != null and feeType!=''">
            ,FEE_TYPE=#{feeType,jdbcType=VARCHAR}
        </if>
        <if test="payType != null and payType!=''">
            ,pay_type=#{payType,jdbcType=VARCHAR}
        </if>
        <if test="collectPayApproach != null and collectPayApproach!=''">
            ,COLLECT_PAY_APPROACH=#{collectPayApproach,jdbcType=VARCHAR}
        </if>
        <if test="coinsuranceMark != null and coinsuranceMark!=''">
            ,COINSURANCE_MARK=#{coinsuranceMark,jdbcType=VARCHAR}
        </if>
        <if test="acceptInsuranceFlag != null and acceptInsuranceFlag!=''">
            ,ACCEPT_INSURANCE_FLAG=#{acceptInsuranceFlag,jdbcType=VARCHAR}
        </if>
        <if test="coinsuranceCompanyCode != null and coinsuranceCompanyCode!=''">
            ,COINSURANCE_COMPANY_CODE=#{coinsuranceCompanyCode,jdbcType=VARCHAR}
        </if>
        <if test="coinsuranceCompanyName != null and coinsuranceCompanyName!=''">
            ,COINSURANCE_COMPANY_NAME=#{coinsuranceCompanyName,jdbcType=VARCHAR}
        </if>
        <if test="coinsuranceRatio != null">
            ,COINSURANCE_RATIO=#{coinsuranceRatio,jdbcType=DECIMAL}
        </if>
        <if test="isFullPay != null and isFullPay!=''">
            ,IS_FULL_PAY=#{isFullPay,jdbcType=VARCHAR}
        </if>
        <if test="coinsuranceActualAmount != null">
            ,COINSURANCE_ACTUAL_AMOUNT=#{coinsuranceActualAmount,jdbcType=DECIMAL}
        </if>
        <if test="financePaymentAmount != null">
            , FINANCE_PAYMENT_AMOUNT = #{financePaymentAmount,jdbcType=DECIMAL}
        </if>
        <if test="paymentVoucherUrl != null">
            , PAYMENT_VOUCHER_URL = #{paymentVoucherUrl,jdbcType=VARCHAR}
        </if>
        <if test="chgPaymentAmount != null">
            , CHG_PAYMENT_AMOUNT = #{chgPaymentAmount,jdbcType=DECIMAL}
        </if>
        <if test="chgCoinsuranceActualAmount != null">
            , CHG_COINSURANCE_ACTUAL_AMOUNT = #{chgCoinsuranceActualAmount,jdbcType=DECIMAL}
        </if>
        <if test="chgPaidAmount != null">
            , CHG_PAID_AMOUNT = #{chgPaidAmount,jdbcType=DECIMAL}
        </if>
        WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
        <if test="idClmPaymentInfo != null">
            AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
        </if>
        <if test="idClmPaymentItem != null">
            AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null">
            AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
        </if>
    </update>
    <update id="updateMergePaymentStatus">
        update CLM_PAYMENT_ITEM_fee cpif
        set cpif.sys_utime = now(),
        cpif.PAYMENT_ITEM_STATUS = #{paymentStatus,jdbcType=VARCHAR}
        where cpif.PAYMENT_ITEM_STATUS != '90'
        and cpif.id_clm_payment_item = (select cpi.id_clm_payment_item from clm_payment_item cpi
        where cpi.id = (select cmp.id from clms_merge_payment cmp
        where cpi.id = cmp.id
        and cmp.batch_no = #{batchNo}))
    </update>
    <update id="updateOtherPaymentStatus">
        update CLM_PAYMENT_ITEM_fee
        set sys_utime = now(),
        PAYMENT_ITEM_STATUS = #{paymentStatus,jdbcType=INTEGER}
        WHERE ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem}
    </update>
    <delete id="delPaymentItemFee">
        delete from CLM_PAYMENT_ITEM_fee
        WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
        <if test="caseNo != null">
            AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
        </if>
        <if test="claimType != null">
            AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
        </if>
        <if test="paymentType != null">
            AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
        </if>
        <if test="subTimes != null">
            AND SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
        </if>
    </delete>
    <select id="getPaymentItemAndFeeItem" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO"
    resultMap="PaymentItemResultMap">
        SELECT
                cpif.*
                FROM CLM_PAYMENT_ITEM cpi,clm_payment_item_fee cpif
                WHERE cpi.PAYMENT_ITEM_STATUS not in  ('90','20')
                AND cpi.REPORT_NO = #{reportNo}
                AND cpi.CASE_TIMES = #{caseTimes}
        <if test="paymentItemStatus!=null and paymentItemStatus!=''">
            and cpi.PAYMENT_ITEM_STATUS = #{paymentItemStatus}
        </if>
                and cpi.ID_CLM_PAYMENT_ITEM = cpif.id_clm_payment_item
    </select>
    <select id="getHisPaymentItemFee" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO" resultMap="resultDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM CLM_PAYMENT_ITEM_FEE
        WHERE CLAIM_TYPE != '4'
        <if test="reportNo != null">
            AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="paymentItemStatus != null">
            AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
        </if>
        <if test="caseTimes != null">
            AND CASE_TIMES <![CDATA[ <= ]]> #{caseTimes,jdbcType=NUMERIC}
        </if>
        <if test="clientName != null">
            AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
        </if>
        <if test="clientBankAccount != null">
            AND CLIENT_BANK_ACCOUNT = #{clientBankAccount}
        </if>
        <if test=" serialNo != null ">
            AND SERIAL_NO = #{serialNo,jdbcType=NUMERIC}
        </if>
        <if test="compensateNo != null">
            AND COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
        </if>
        <if test="idClmPaymentInfo != null">
            AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
        </if>
        <if test="idClmPaymentItem != null">
            AND ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem,jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null">
            AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
        </if>
        <if test="policyNo != null">
            AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        </if>
        <if test="claimType != null">
            AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
        </if>
        <if test="paymentType != null">
            AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
        </if>
        <if test="paymentItemStatus != null">
            AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
        </if>
        <if test="subTimes != null">
            AND SUB_TIMES = #{subTimes,jdbcType=VARCHAR}
        </if>
        <if test="coinsuranceCompanyCode != null">
            AND COINSURANCE_COMPANY_CODE = #{coinsuranceCompanyCode,jdbcType=VARCHAR}
        </if>
        order by POLICY_NO,REPORT_NO,CASE_TIMES,PAYMENT_TYPE
    </select>
    <select id="selectByPaySerialNo" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.PaymentItemFee">
        select * from clm_payment_item_fee where ID_CLM_PAYMENT_ITEM_fee = #{IdPaymentItemFee}
    </select>
    <select id="getAllPaymentItem" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM CLM_PAYMENT_ITEM_FEE
        WHERE CLAIM_TYPE != '4'
        <if test="reportNo != null">
            AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="paymentItemStatus != null">
            AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
        </if>
        <if test="caseTimes != null">
            AND CASE_TIMES <![CDATA[ <= ]]> #{caseTimes,jdbcType=NUMERIC}
        </if>
        <if test="clientName != null">
            AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
        </if>
        <if test="clientBankAccount != null">
            AND CLIENT_BANK_ACCOUNT = #{clientBankAccount}
        </if>
        <if test=" serialNo != null ">
            AND SERIAL_NO = #{serialNo,jdbcType=NUMERIC}
        </if>
        <if test="compensateNo != null">
            AND COMPENSATE_NO = #{compensateNo,jdbcType=VARCHAR}
        </if>
        <if test="idClmPaymentInfo != null">
            AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
        </if>
        <if test="idClmPaymentItem != null">
            AND ID_CLM_PAYMENT_ITEM_fee = #{idClmPaymentItem,jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null">
            AND CASE_NO = #{caseNo,jdbcType=VARCHAR}
        </if>
        <if test="policyNo != null">
            AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        </if>
        <if test="claimType != null">
            AND CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
        </if>
        <if test="paymentType != null">
            AND PAYMENT_TYPE = #{paymentType,jdbcType=VARCHAR}
        </if>
        <if test="paymentItemStatus != null">
            AND PAYMENT_ITEM_STATUS = #{paymentItemStatus,jdbcType=VARCHAR}
        </if>
        <if test="subTimes != null">
            AND SUB_TIMES = #{subTimes,jdbcType=VARCHAR}
        </if>
        <if test="coinsuranceCompanyCode != null">
            AND COINSURANCE_COMPANY_CODE = #{coinsuranceCompanyCode,jdbcType=VARCHAR}
        </if>
        order by POLICY_NO,REPORT_NO,CASE_TIMES,PAYMENT_TYPE
    </select>
    <select id="getPaymentItemFeeById" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.PaymentItemFee">
       select * from clm_payment_item_fee cpif
                where cpif.PAYMENT_ITEM_STATUS != '90'
                and cpif.id_clm_payment_item = (select cpi.id_clm_payment_item from clm_payment_item cpi
                where cpi.id = (select cmp.id from clms_merge_payment cmp
                where cpi.id = cmp.id
                and cmp.batch_no = #{batchNo})) limit 1;
    </select>
    <select id="getPaymentItemFeeByPaymentId"
            resultType="com.paic.ncbs.claim.dao.entity.coinsurance.PaymentItemFee">
        select * from clm_payment_item where id_clm_payment_item = #{idClmPaymentItem}
    </select>

</mapper>
