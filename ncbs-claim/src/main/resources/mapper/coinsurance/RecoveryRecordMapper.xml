<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.coinsurance.RecoveryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryRecord">
        <id column="id_recovery_record" property="idRecoveryRecord" />
        <result column="sum_amor_amount" property="sumAmorAmount" />
        <result column="sum_receipts_amount" property="sumReceiptsAmount" />
        <result column="amount_difference_reason" property="amountDifferenceReason" />
        <result column="company_mismatch_reason" property="companyMismatchReason" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>
    <select id="selectByIdRecovery" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryRecord">
        select * from clms_recovery_record where id_recovery_record = #{idRecoveryRecord}
    </select>
    <select id="selectByBatchNo" resultType="com.paic.ncbs.claim.dao.entity.coinsurance.RecoveryRecord">
        select * from clms_recovery_record where batch_no = #{batchNo}
    </select>

</mapper>
