<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.print.ClmCoinsPrintRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.print.ClmCoinsPrintRecord">
        <id column="id" property="id" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="send_times" property="sendTimes" />
        <result column="claim_type" property="claimType" />
        <result column="is_success" property="isSuccess" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>
    <update id="updatePrintLog">
        update clm_coins_print_record set
        is_success = #{isSuccess},
        remark = #{remark},
        updated_by = #{updatedBy},
        send_times = #{sendTimes}
        where
        report_no = #{reportNo}
        and case_times = #{caseTimes}
        and claim_type = #{claimType}
    </update>
    <select id="selectByHis" resultType="com.paic.ncbs.claim.model.dto.print.ClmCoinsPrintRecord">
        select * from clm_coins_print_record where
        report_no = #{reportNo}
        and case_times = #{caseTimes}
        and claim_type = #{claimType}
    </select>

</mapper>
