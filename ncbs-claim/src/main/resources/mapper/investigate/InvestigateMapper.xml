<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO" id="InvestigateMap">
		<id property="idAhcsInvestigate" column="ID_AHCS_INVESTIGATE" />
		<result property="orderNo" column="ORDER_NO" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="insuredApplyStatus" column="INSURED_APPLY_STATUS" />
		<result property="accidentScene" column="ACCIDENT_SCENE" />
		<result property="initiateInvestigateNode" column="INITIATE_INVESTIGATE_NODE" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="investigateStatus" column="INVESTIGATE_STATUS" />
		<result property="processStatus" column="PROCESS_STATUS" />
		<result property="investigateItems" column="INVESTIGATE_ITEMS" />
		<result property="investigateDepartment" column="INVESTIGATE_DEPARTMENT" />
		<result property="primaryInvestigatorUm" column="PRIMARY_INVESTIGATOR_UM" />
		<result property="initMode" column="INIT_MODE" />
		<result property="investigateConclusion" column="INVESTIGATE_CONCLUSION" />
		<result property="allocateInfo" column="ALLOCATE_INFO" />
		<result property="investigateQualitative" column="INVESTIGATE_QUALITATIVE" />
		<result property="taskInstId" column="TASK_INST_ID" />
		<result property="idRmRunBatchRecord" column="ID_RM_RUN_BATCH_RECORD" />
		<result property="isEvaluateInvestigate" column="IS_EVALUATE_INVESTIGATE" />
		<result property="isOldInvestigate" column="IS_OLD_INVESTIGATE" />
		
		<result property="isHasAdjustingFee" column="IS_HAS_ADJUSTING_FEE" />
		<result property="feeAuditOption" column="FEE_AUDIT_OPTION" />
		<result property="initiateDepartment" column="INITIATE_DEPARTMENT" />

		<result property="estimate" column="ESTIMATE" />
		<result property="other" column="OTHER" />
		<result property="operate" column="operate" />

		<result property="commonEstimateFee" column="COMMON_ESTIMAT_FEE" />
		<result property="investigateFlag" column="INVESTIGATE_FLAG" />
		<result property="serverCode" column="SERVER_CODE" />
		<result property="auditorInfo" column="AUDITOR_INFO" />
	</resultMap>
	
	
	

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateAdditionalDTO" id="InvestigateAdditionalMap">
		<id property="idAhcsInvestigateAdditional" column="ID_AHCS_INVESTIGATE_ADDITIONAL" />
		<result property="idAhcsInvestigate" column="ID_AHCS_INVESTIGATE" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="initiateInvestigateNode" column="INITIATE_INVESTIGATE_NODE" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="investigateItems" column="INVESTIGATE_ITEMS" />
		<result property="initMode" column="INIT_MODE" />
		<result property="idRmRunBatchRecord" column="ID_RM_RUN_BATCH_RECORD" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateAdditionalVO" extends="InvestigateAdditionalMap" id="InvestigateAdditionalVOMap">
		<result property="initiatorUmName" column="INITIATOR_UM_NAME" />
	</resultMap>
	
	

	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateVO" extends="InvestigateMap" id="InvestigateVOMap">
		<result property="initiatorUmName" column="INITIATOR_UM_NAME" />
		<result property="primaryInvestigatorUmName" column="PRIMARY_INVESTIGATOR_UM_NAME" />
		<result property="investigateDepartmentName" column="DEPARTMENT_NAME" />
		<result property="investigateQualitativeName" column="INVESTIGATE_QUALITATIVE_NAME" />
	</resultMap>


	<resultMap type="com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto" id="accidentSceneMap">
		<id property="departmentCode" column="ID_AHCS_INVESTIGATE" />
		<result property="collectionCode" column="COLLECTION_CODE" />
		<result property="collectionName" column="COLLECTION_NAME" />
		<result property="valueCode" column="VALUE_CODE" />
		<result property="valueChineseName" column="VALUE_CHINESE_NAME" />


		<collection property="subAccidentScene"
					ofType="com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO"
					select="getDictSubItems"
					column="{valueCode = VALUE_CODE, collectionCode=COLLECTION_CODE}">
		</collection>

	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO" id="commonParameter">
		<result property="collectionCode" column="COLLECTION_CODE"/>
		<result property="collectionName" column="COLLECTION_NAME"/>
		<result property="valueCode" column="LOWER_VALUE_CODE"/>
		<result property="valueChineseName" column="LOWER_VALUE_NAME"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateEvaluateDTO" id="InvestigateEvaluateMap">
		<id property="idAhcsInvestigateEvaluate" column="ID_AHCS_INVESTIGATE_EVALUATE" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="idAhcsInvestigateTotal" column="ID_AHCS_INVESTIGATE_TOTAL" />
		<result property="idAhcsInvestigate" column="ID_AHCS_INVESTIGATE" />
		<result property="version" column="VERSION" />
		<result property="evaluateTime" column="EVALUATE_TIME" />
		<result property="evaluateUm" column="EVALUATE_UM" />
		<result property="isQualified" column="IS_QUALIFIED" />
		<result property="isAllEvidence" column="IS_ALL_EVIDENCE" />
		<result property="evaluateScore" column="EVALUATE_SCORE" />
		<result property="isJudgeError" column="IS_JUDGE_ERROR" />
		<result property="rejectReason" column="REJECT_REASON" />
	</resultMap>



	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateEvaluateVO" extends="InvestigateEvaluateMap" id="InvestigateEvaluateVOMap">
		<result property="evaluateUmName" column="EVALUATE_UM_NAME" />	
	</resultMap>
	


	<insert id="addInvestigate" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO" >
		INSERT INTO CLMS_INVESTIGATE (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE,
			REPORT_NO,
			CASE_TIMES,
			INSURED_APPLY_STATUS,
			ACCIDENT_SCENE,
			INITIATE_INVESTIGATE_NODE,
			INITIATOR_UM,
			INVESTIGATE_STATUS,
			INVESTIGATE_ITEMS,
			INVESTIGATE_DEPARTMENT,
			PRIMARY_INVESTIGATOR_UM,
			INIT_MODE,
			INVESTIGATE_CONCLUSION,
			ALLOCATE_INFO,
			INVESTIGATE_QUALITATIVE,
			TASK_INST_ID,
			ID_RM_RUN_BATCH_RECORD,
			IS_EVALUATE_INVESTIGATE,
			IS_OLD_INVESTIGATE,
			IS_HAS_ADJUSTING_FEE,
			FEE_AUDIT_OPTION,
			INITIATE_DEPARTMENT,
			initiator_um_name,
			primary_investigator_um_name,
			ARCHIVE_TIME,
			ESTIMATE ,
		    OTHER,operate,
			INVESTIGATE_FLAG,
			SERVER_CODE,
			AUDITOR_INFO
		)
	VALUES (
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsInvestigate ,jdbcType=VARCHAR},
			#{reportNo ,jdbcType=VARCHAR},
			#{caseTimes ,jdbcType=NUMERIC},
			#{insuredApplyStatus ,jdbcType=VARCHAR},
			#{accidentScene ,jdbcType=VARCHAR},
			#{initiateInvestigateNode ,jdbcType=VARCHAR},
			#{initiatorUm ,jdbcType=VARCHAR},
			#{investigateStatus ,jdbcType=NUMERIC},
			#{investigateItems ,jdbcType=VARCHAR},
			#{investigateDepartment ,jdbcType=VARCHAR},
			#{primaryInvestigatorUm ,jdbcType=VARCHAR},
			#{initMode ,jdbcType=VARCHAR},
			#{investigateConclusion ,jdbcType=VARCHAR},
			#{allocateInfo ,jdbcType=VARCHAR},
			#{investigateQualitative ,jdbcType=VARCHAR},
			#{taskInstId ,jdbcType=VARCHAR},
			#{idRmRunBatchRecord ,jdbcType=VARCHAR},
			#{isEvaluateInvestigate ,jdbcType=VARCHAR},
			#{isOldInvestigate ,jdbcType=VARCHAR},
			#{isHasAdjustingFee ,jdbcType=VARCHAR},
			#{feeAuditOption ,jdbcType=VARCHAR},
			#{initiateDepartment ,jdbcType=VARCHAR},
			(select user_name from CLMS_user_info t where user_id=#{initiatorUm ,jdbcType=VARCHAR}),
			(select user_name from CLMS_user_info t where user_id=#{primaryInvestigatorUm ,jdbcType=VARCHAR}),
			now(),
			#{estimate,jdbcType=DECIMAL},
			#{other ,jdbcType=VARCHAR},
			#{operate ,jdbcType=NUMERIC},
			#{investigateFlag},
			#{serverCode},
			#{auditorInfo ,jdbcType=VARCHAR}
	)
	</insert>



	<update id="modifyInvestigate" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO">
		UPDATE CLMS_INVESTIGATE
		<set>

			<if test="createdBy != null and createdBy != '' ">
				CREATED_BY  = #{createdBy},
			</if>

			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy},
			</if>
			UPDATED_DATE=now(),

			<if test="reportNo != null and reportNo != '' ">
				REPORT_NO  = #{reportNo},
			</if>

			<if test="caseTimes != null ">
				CASE_TIMES  = #{caseTimes},
			</if>

			<if test="insuredApplyStatus != null and insuredApplyStatus != '' ">
				INSURED_APPLY_STATUS  = #{insuredApplyStatus},
			</if>

			<if test="accidentScene != null and accidentScene != '' ">
				ACCIDENT_SCENE  = #{accidentScene},
			</if>

			<if test="initiateInvestigateNode != null and initiateInvestigateNode != '' ">
				INITIATE_INVESTIGATE_NODE  = #{initiateInvestigateNode},
			</if>

			<if test="initiatorUm != null and initiatorUm != '' ">
				INITIATOR_UM  = #{initiatorUm},
				INITIATOR_UM_name  = (select user_name from CLMS_user_info t where user_id=#{initiatorUm}),
			</if>

			<if test="investigateStatus != null ">
				INVESTIGATE_STATUS  = #{investigateStatus},
			</if>

			<if test="investigateItems != null and investigateItems != '' ">
				INVESTIGATE_ITEMS  = #{investigateItems},
			</if>

			<if test="investigateDepartment != null and investigateDepartment != '' ">
				INVESTIGATE_DEPARTMENT  = #{investigateDepartment},
				INVESTIGATE_DEPARTMENT_name  = (select department_abbr_name from department_define  where department_code=#{investigateDepartment}),
			</if>

			<if test="primaryInvestigatorUm != null and primaryInvestigatorUm != '' ">
				PRIMARY_INVESTIGATOR_UM  = #{primaryInvestigatorUm},
				PRIMARY_INVESTIGATOR_UM_name  = (select user_name from CLMS_user_info t where user_id=#{primaryInvestigatorUm}),
			</if>

			<if test="initMode != null and initMode != '' ">
				INIT_MODE  = #{initMode},
			</if>

			<if test="investigateConclusion != null and investigateConclusion != '' ">
				INVESTIGATE_CONCLUSION  = #{investigateConclusion},
			</if>

			<if test="allocateInfo != null and allocateInfo != '' ">
				ALLOCATE_INFO  = #{allocateInfo},
			</if>

			<if test="investigateQualitative != null and investigateQualitative != '' ">
				INVESTIGATE_QUALITATIVE  = #{investigateQualitative},
			</if>

			<if test="taskInstId != null and taskInstId != '' ">
				TASK_INST_ID  = #{taskInstId},
			</if>

			<if test="initiateDepartment != null and initiateDepartment != '' ">
				INITIATE_DEPARTMENT  = #{initiateDepartment},
			</if>


			<if test="estimate != null and estimate != '' ">
				estimate  = #{estimate},
			</if>
			<if test="other != null and other != '' ">
				other  = #{other},
			</if>
			<if test="operate != null and operate != '' ">
				operate  = #{operate},
			</if>
			<if test="serverCode != null and serverCode != '' ">
				server_code  = #{serverCode},
			</if>
			<if test="auditorInfo != null and auditorInfo != '' ">
				auditor_info  = #{auditorInfo},
			</if>
		</set>
		WHERE ID_AHCS_INVESTIGATE=#{idAhcsInvestigate}
	</update>
	

	<select id="getInvestigateById" resultMap="InvestigateVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INSURED_APPLY_STATUS,
			t.ACCIDENT_SCENE,
			t.INITIATE_INVESTIGATE_NODE,
			t.INITIATOR_UM,
			t.INVESTIGATE_STATUS,
			t.INVESTIGATE_ITEMS,
			t.INVESTIGATE_DEPARTMENT,
			t.PRIMARY_INVESTIGATOR_UM,
			t.INIT_MODE,
			t.INVESTIGATE_CONCLUSION,
			t.ALLOCATE_INFO,
			t.INVESTIGATE_QUALITATIVE,
			t.TASK_INST_ID,
			t.ID_RM_RUN_BATCH_RECORD,
			t.IS_EVALUATE_INVESTIGATE,
			t.IS_OLD_INVESTIGATE,
			IS_HAS_ADJUSTING_FEE,
			t.FEE_AUDIT_OPTION,
			t.INITIATE_DEPARTMENT,
	        t.INITIATOR_UM  as INITIATOR_UM_NAME,
      		t.PRIMARY_INVESTIGATOR_UM  as PRIMARY_INVESTIGATOR_UM_NAME,
		    (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as DEPARTMENT_NAME,
		    (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.INVESTIGATE_QUALITATIVE limit 1) as INVESTIGATE_QUALITATIVE_NAME
				,t.ESTIMATE ,
			t.OTHER,t.operate,t.COMMON_ESTIMAT_FEE,
			t.INVESTIGATE_FLAG,
			t.SERVER_CODE
		from CLMS_INVESTIGATE t
		where  t.ID_AHCS_INVESTIGATE=#{idAhcsInvestigate} 
	</select>



	<select id="getNoCommitData" resultMap="InvestigateVOMap">
		select
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INSURED_APPLY_STATUS,
			t.ACCIDENT_SCENE,
			t.INITIATE_INVESTIGATE_NODE,
			t.INITIATOR_UM,
			t.INVESTIGATE_STATUS,
			t.INVESTIGATE_ITEMS,
			t.INVESTIGATE_DEPARTMENT,
			t.PRIMARY_INVESTIGATOR_UM,
			t.INIT_MODE,
			t.INVESTIGATE_CONCLUSION,
			t.ALLOCATE_INFO,
			t.INVESTIGATE_QUALITATIVE,
			t.TASK_INST_ID,
			t.ID_RM_RUN_BATCH_RECORD,
			t.IS_EVALUATE_INVESTIGATE,
			t.IS_OLD_INVESTIGATE,
			IS_HAS_ADJUSTING_FEE,
			t.FEE_AUDIT_OPTION,
			t.INITIATE_DEPARTMENT,
			IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1),t.INITIATOR_UM) as INITIATOR_UM_NAME,
			IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.PRIMARY_INVESTIGATOR_UM limit 1),t.PRIMARY_INVESTIGATOR_UM) as PRIMARY_INVESTIGATOR_UM_NAME,
			(select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as DEPARTMENT_NAME,
			(select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.INVESTIGATE_QUALITATIVE limit 1) as INVESTIGATE_QUALITATIVE_NAME
			,t.ESTIMATE ,
			t.OTHER,t.operate,
			t.AUDITOR_INFO
		from CLMS_INVESTIGATE t
		   where REPORT_NO = #{reportNo, jdbcType = VARCHAR}
				  AND CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
                 AND OPERATE = 0
	</select>

	<select id="getInvestigateByTaskId" resultMap="InvestigateMap">
		select 
			t1.CREATED_BY,
			t1.CREATED_DATE,
			t1.UPDATED_BY,
			t1.UPDATED_DATE,
			t1.ID_AHCS_INVESTIGATE,
			t1.REPORT_NO,
			t1.CASE_TIMES,
			t1.INSURED_APPLY_STATUS,
			t1.ACCIDENT_SCENE,
			t1.INITIATE_INVESTIGATE_NODE,
			t1.INITIATOR_UM,
			t1.INVESTIGATE_STATUS,
			t1.INVESTIGATE_ITEMS,
			t1.INVESTIGATE_DEPARTMENT,
			t1.PRIMARY_INVESTIGATOR_UM,
			t1.INIT_MODE,
			t1.INVESTIGATE_CONCLUSION,
			t1.ALLOCATE_INFO,
			t1.INVESTIGATE_QUALITATIVE,
			t1.TASK_INST_ID,
			t1.ID_RM_RUN_BATCH_RECORD,
			t1.IS_EVALUATE_INVESTIGATE,
			t1.IS_OLD_INVESTIGATE,
			t1.IS_HAS_ADJUSTING_FEE,
			t1.FEE_AUDIT_OPTION,
			t1.INITIATE_DEPARTMENT
		from CLMS_INVESTIGATE t1, CLMS_INVESTIGATE_TASK t2
		where 
			t1.ID_AHCS_INVESTIGATE = t2.ID_AHCS_INVESTIGATE
		AND t2.ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
	</select>	
	
	

	<select id="hasSameInvestigate" resultType="java.lang.Integer" >
		select count(ID_AHCS_INVESTIGATE)
		  from CLMS_INVESTIGATE
		 where REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		   AND CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		   AND INVESTIGATE_STATUS != 4
		   AND INVESTIGATE_STATUS != 5
		   AND INVESTIGATE_STATUS != 6

	</select>
	

	<select id="hasInvestigateRecord" resultType="java.lang.Integer" >
		select count(ID_AHCS_INVESTIGATE) from CLMS_INVESTIGATE where REPORT_NO  = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES  = #{caseTimes,jdbcType=NUMERIC}
		AND INVESTIGATE_STATUS  != 5 
	</select>
	


	<select id="getHistoryInvestigate" resultMap="InvestigateVOMap">
		SELECT (@rownum := @rownum +1) AS ORDER_NO,
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INSURED_APPLY_STATUS,
			t.ACCIDENT_SCENE,
			t.INITIATE_INVESTIGATE_NODE,
			t.INITIATOR_UM,
			t.INVESTIGATE_STATUS,
			t.INVESTIGATE_ITEMS,
			t.INVESTIGATE_DEPARTMENT,
			t.PRIMARY_INVESTIGATOR_UM,
			t.INIT_MODE,
			t.INVESTIGATE_CONCLUSION,
			t.ALLOCATE_INFO,
			t.INVESTIGATE_QUALITATIVE,
			t.TASK_INST_ID,
			t.ID_RM_RUN_BATCH_RECORD,
			t.IS_EVALUATE_INVESTIGATE,
			t.IS_OLD_INVESTIGATE,
			t.IS_HAS_ADJUSTING_FEE,
			t.FEE_AUDIT_OPTION,
			t.INITIATE_DEPARTMENT,
		IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1),t.INITIATOR_UM) as INITIATOR_UM_NAME,
		IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.PRIMARY_INVESTIGATOR_UM limit 1),t.PRIMARY_INVESTIGATOR_UM) as PRIMARY_INVESTIGATOR_UM_NAME,
		(select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as DEPARTMENT_NAME

		FROM CLMS_INVESTIGATE t,(SELECT @rownum := 0) AS rn
		<where>
			<if test="caseTimes != null and caseTimes != '' ">
				AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
			</if>
			AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		</where>
		ORDER BY CREATED_DATE
	</select>

	<select id="getFirstInvestigate" resultMap="InvestigateVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INSURED_APPLY_STATUS,
			t.ACCIDENT_SCENE,
			t.INITIATE_INVESTIGATE_NODE,
			t.INITIATOR_UM,
			t.INVESTIGATE_STATUS,
			t.INVESTIGATE_ITEMS,
			t.INVESTIGATE_DEPARTMENT,
			t.PRIMARY_INVESTIGATOR_UM,
			t.INIT_MODE,
			t.INVESTIGATE_CONCLUSION,
			t.ALLOCATE_INFO,
			t.INVESTIGATE_QUALITATIVE,
			t.TASK_INST_ID,
			t.ID_RM_RUN_BATCH_RECORD,
			t.IS_EVALUATE_INVESTIGATE,
			t.IS_OLD_INVESTIGATE,
			IS_HAS_ADJUSTING_FEE,
			t.FEE_AUDIT_OPTION,
			t.INITIATE_DEPARTMENT,
	        t.INITIATOR_UM  as INITIATOR_UM_NAME,
      		t.PRIMARY_INVESTIGATOR_UM  as PRIMARY_INVESTIGATOR_UM_NAME,
		    (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as DEPARTMENT_NAME,
		    (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.INVESTIGATE_QUALITATIVE limit 1) as INVESTIGATE_QUALITATIVE_NAME
				,t.ESTIMATE ,
			t.OTHER,t.operate,t.COMMON_ESTIMAT_FEE,
			t.INVESTIGATE_FLAG,
			t.SERVER_CODE
		from CLMS_INVESTIGATE t
		where  t.REPORT_NO=#{reportNo,jdbcType=VARCHAR} 
		  and t.CASE_TIMES=#{caseTimes,jdbcType=NUMERIC}
		order by t.CREATED_DATE
		limit 1
	</select>

	<select id="getHistoryOutInvestigate" resultMap="InvestigateVOMap">
		SELECT
		t.ID_AHCS_INVESTIGATE,
		t.REPORT_NO,
		(@rownum := @rownum +1) AS ORDER_NO,
		'外部调查' AS INIT_MODE,
		t.CASE_TIMES,
		t.INVESTIGATE_DEPARTMENT,
		(CASE WHEN t.INVESTIGATE_STATUS = '1' THEN '待审批(分配)'
		WHEN t.INVESTIGATE_STATUS = '2' THEN '调查处理中'
		WHEN t.INVESTIGATE_STATUS = '3' THEN '调查待审核'
		WHEN t.INVESTIGATE_STATUS = '4' THEN '调查已完成'
		WHEN t.INVESTIGATE_STATUS = '5' THEN '提调退回'
		WHEN t.INVESTIGATE_STATUS = '6' THEN '总调查评定完成'
		END) AS PROCESS_STATUS,
		t.INVESTIGATE_STATUS,
		t.ACCIDENT_SCENE,
		t.PRIMARY_INVESTIGATOR_UM,
		t.INVESTIGATE_ITEMS,
		t.SERVER_CODE
		FROM CLMS_INVESTIGATE t,(SELECT @rownum := 0) AS rn
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND INIT_MODE = '02'
		<if test="caseTimes != null and caseTimes != '' ">
			AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		</if>
		ORDER BY CREATED_DATE
	</select>

	<select id="getInvestigateRecord" resultMap="InvestigateVOMap">
		SELECT (@rownum := @rownum +1) AS ORDER_NO,
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INSURED_APPLY_STATUS,
			t.ACCIDENT_SCENE,
			t.INITIATE_INVESTIGATE_NODE,
			t.INITIATOR_UM,
			t.INVESTIGATE_STATUS,
			t.INVESTIGATE_ITEMS,
			t.INVESTIGATE_DEPARTMENT,
			t.PRIMARY_INVESTIGATOR_UM,
			t.INIT_MODE,
			t.INVESTIGATE_CONCLUSION,
			t.ALLOCATE_INFO,
			t.INVESTIGATE_QUALITATIVE,
			t.TASK_INST_ID,
			t.ID_RM_RUN_BATCH_RECORD,
			t.IS_EVALUATE_INVESTIGATE,
			t.IS_OLD_INVESTIGATE,
			t.IS_HAS_ADJUSTING_FEE,
			t.FEE_AUDIT_OPTION,
			t.INITIATE_DEPARTMENT,
			(select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1) as INITIATOR_UM_NAME,
		    (select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.PRIMARY_INVESTIGATOR_UM limit 1) as PRIMARY_INVESTIGATOR_UM_NAME,
		    (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as DEPARTMENT_NAME

		FROM CLMS_INVESTIGATE t,(SELECT @rownum := 0) AS rn
		WHERE 
			CASE_TIMES = #{caseTimes,jdbcType=NUMERIC} 
		AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		ORDER BY CREATED_DATE DESC
	</select>
	

	<select id="getInvestigateState" resultType="java.lang.Integer">
		select b.INVESTIGATE_STATUS
		  from (SELECT t.INVESTIGATE_STATUS
		          FROM CLMS_INVESTIGATE t
		         WHERE t.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC} 
		           AND t.REPORT_NO = #{reportNo,jdbcType=VARCHAR} 
		         order by t.created_date desc) b
		limit 1
	</select>
	

	<select id="getAccidentSceneData" resultMap="accidentSceneMap" >
		select p.*
		  FROM CLM_COMMON_PARAMETER p
		 WHERE p.COLLECTION_CODE = concat('ACCI_SCENE_' , #{collectionCode})
		   and p.VALUE_CODE not in
		       (SELECT M.LOWER_VALUE_CODE
		          FROM CLM_COMMON_PARAM_MAPPING M
		         WHERE M.COLLECTION_CODE = concat('ACCI_SCENE_' , #{collectionCode}))
	</select>

	<select id="getUpperValueName" resultType="java.lang.String">
		SELECT M.UPPER_VALUE_NAME
		FROM CLM_COMMON_PARAM_MAPPING M
		WHERE M.COLLECTION_CODE = concat('ACCI_SCENE_' , 'IS_0101')
			AND LOWER_VALUE_CODE = #{valueCode,jdbcType=VARCHAR}
		LIMIT 1
	</select>

<!--	<select id="getDictSubItems" resultMap="commonParameter" >-->
<!--		SELECT DEPARTMENT_CODE,-->
<!--		COLLECTION_CODE,-->
<!--		COLLECTION_NAME,-->
<!--		VALUE_CODE,-->
<!--		VALUE_CHINESE_NAME,-->
<!--		DISPLAY_NO-->
<!--		FROM CLM_COMMON_PARAMETER-->
<!--		WHERE COLLECTION_CODE = #{collectionCode}-->
<!--		AND VALUE_CODE IN-->
<!--		(SELECT M.LOWER_VALUE_CODE-->
<!--		FROM CLM_COMMON_PARAM_MAPPING M-->
<!--		WHERE M.COLLECTION_CODE = #{collectionCode}-->
<!--		AND M.UPPER_VALUE_CODE = #{valueCode})-->
<!--	</select>-->





	<select id="getDictSubItems" resultMap="commonParameter" >
		SELECT DEPARTMENT_CODE,
			   COLLECTION_CODE,
			   COLLECTION_NAME,
			   `LOWER_VALUE_NAME` ,`LOWER_VALUE_CODE`
		FROM CLM_COMMON_PARAM_MAPPING
		WHERE COLLECTION_CODE =  #{collectionCode}
	    AND `UPPER_VALUE_CODE` IN
		(SELECT M.`VALUE_CODE`
		from CLM_COMMON_PARAMETER M
		WHERE M.COLLECTION_CODE =   #{collectionCode}
	    AND M.`VALUE_CODE` = #{valueCode} ) order by  `LOWER_VALUE_CODE`
	</select>
	

	<select id="getInvestigateStartDate" resultType="string">
		SELECT date_format(t.CREATED_DATE,'%Y-%m-%d %T') CREATED_DATE
		  FROM CLMS_INVESTIGATE t
		 WHERE t.REPORT_NO = #{reportNo}  
		       and t.CASE_TIMES = #{caseTimes} 
		       and t.INVESTIGATE_STATUS in (4,6)
		       order by t.UPDATED_DATE desc
		limit 1
	</select>
	

	<select id="getInvestigationStartDate" resultType="string">
		SELECT date_format(t.CREATED_DATE,'%Y%m%d') CREATED_DATE
		  FROM CLMS_INVESTIGATE t
		 WHERE t.REPORT_NO = #{reportNo}  
		       and t.CASE_TIMES = #{caseTimes} 
		       and t.INVESTIGATE_STATUS in (4,6)
		       order by t.UPDATED_DATE desc
		limit 1
	</select>
	
	<select id="getNoFinishInvestigateRecord" resultMap="InvestigateMap">
		select 
			t1.CREATED_BY,
			t1.CREATED_DATE,
			t1.UPDATED_BY,
			t1.UPDATED_DATE,
			t1.ID_AHCS_INVESTIGATE,
			t1.REPORT_NO,
			t1.CASE_TIMES,
			t1.INSURED_APPLY_STATUS,
			t1.ACCIDENT_SCENE,
			t1.INITIATE_INVESTIGATE_NODE,
			t1.INITIATOR_UM,
			t1.INVESTIGATE_STATUS,
			t1.INVESTIGATE_ITEMS,
			t1.INVESTIGATE_DEPARTMENT,
			t1.PRIMARY_INVESTIGATOR_UM,
			t1.INIT_MODE,
			t1.INVESTIGATE_CONCLUSION,
			t1.ALLOCATE_INFO,
			t1.INVESTIGATE_QUALITATIVE,
			t1.TASK_INST_ID,
			t1.ID_RM_RUN_BATCH_RECORD,
			t1.IS_EVALUATE_INVESTIGATE,
			t1.IS_OLD_INVESTIGATE,
			t1.IS_HAS_ADJUSTING_FEE,
			t1.FEE_AUDIT_OPTION,
			t1.INITIATE_DEPARTMENT
		from CLMS_INVESTIGATE t1
		where t1.REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		   AND t1.CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		   AND t1.INVESTIGATE_STATUS != 4
		   AND t1.INVESTIGATE_STATUS != 5
		   AND t1.INVESTIGATE_STATUS != 6
		   AND  t1.operate  = 1
		   limit 1
	</select>
	

	<select id="getNoFinishIdInvestigate" resultType="string">
		select i.ID_AHCS_INVESTIGATE 
		  from CLMS_INVESTIGATE i
		 where i.REPORT_NO = #{reportNo} 
			   and i.CASE_TIMES = #{caseTimes} 
			   and i.INVESTIGATE_STATUS != 4 
			   and i.INVESTIGATE_STATUS != 5 
			   and i.INVESTIGATE_STATUS != 6 
			   limit 1
	</select>
	

	<select id="getAdditionalCountByInvestigateId" resultType="java.lang.Integer" >
		select count(id_ahcs_investigate_additional)
		  from CLMS_INVESTIGATE_additional
		 where id_ahcs_investigate = #{idAhcsInvestigate, jdbcType = VARCHAR}
	</select>
	

	<insert id="addInvestigateAdditional" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateAdditionalDTO" >
		INSERT INTO CLMS_INVESTIGATE_additional (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_ADDITIONAL,
			ID_AHCS_INVESTIGATE,
			REPORT_NO,
			CASE_TIMES,
			INITIATE_INVESTIGATE_NODE,
			INITIATOR_UM,
			INVESTIGATE_ITEMS,
			INIT_MODE,
			ID_RM_RUN_BATCH_RECORD,
			initiator_um_name,
			ARCHIVE_TIME
		)
	VALUES (
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsInvestigateAdditional ,jdbcType=VARCHAR},
			#{idAhcsInvestigate ,jdbcType=VARCHAR},
			#{reportNo ,jdbcType=VARCHAR},
			#{caseTimes ,jdbcType=NUMERIC},
			#{initiateInvestigateNode ,jdbcType=VARCHAR},
			#{initiatorUm ,jdbcType=VARCHAR},
			#{investigateItems ,jdbcType=VARCHAR},
			#{initMode ,jdbcType=VARCHAR},
			#{idRmRunBatchRecord ,jdbcType=VARCHAR},
			(select user_name from CLMS_user_info t where user_id=#{initiatorUm ,jdbcType=VARCHAR}),
			now()
	)
	</insert>
	
	

	<select id="getAdditionalByInvestigateId" resultMap="InvestigateAdditionalVOMap">
		select 
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_ADDITIONAL,
			ID_AHCS_INVESTIGATE,
			REPORT_NO,
			CASE_TIMES,
			INITIATE_INVESTIGATE_NODE,
			INITIATOR_UM,
			INVESTIGATE_ITEMS,
			INIT_MODE,
			ID_RM_RUN_BATCH_RECORD,
			IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1),t.INITIATOR_UM) as INITIATOR_UM_NAME
		from CLMS_INVESTIGATE_additional t
		where 
			ID_AHCS_INVESTIGATE = #{idAhcsInvestigate}
	</select>
	
	
	

	<select id="getLatestInvestigateId" resultType="java.lang.String" >
		select 
			ID_AHCS_INVESTIGATE
		from CLMS_INVESTIGATE
		where REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		   AND CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		   order by updated_date desc
		limit 1
	</select>
	

	<select id="getLatestInvestigateId2" resultType="java.lang.String" >
		select 
			ID_AHCS_INVESTIGATE
		from CLMS_INVESTIGATE
		where REPORT_NO = #{reportNo, jdbcType = VARCHAR}		   

		   order by updated_date desc
		limit 1
	</select>
	

	<select id="getIdRmRunBatchRecords" resultType="java.lang.String" >
		select ID_RM_RUN_BATCH_RECORD
		  from CLMS_investigate_additional t
		 where t.REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		   AND t.CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		   AND t.INITIATE_INVESTIGATE_NODE = #{initiateInvestigateNode, jdbcType = VARCHAR}
		   
		Union
		
		select ID_RM_RUN_BATCH_RECORD
		  from CLMS_investigate a
		 where a.REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		   AND a.CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		   AND a.INITIATE_INVESTIGATE_NODE = #{initiateInvestigateNode, jdbcType = VARCHAR}
	</select>
	
	

	<select id="getInvestigateEvaluateByIdAhcsInvestigate" resultMap="InvestigateEvaluateVOMap">
		select CREATED_BY,
		       CREATED_DATE,
		       UPDATED_BY,
		       UPDATED_DATE,
		       ID_AHCS_INVESTIGATE_EVALUATE,
		       REPORT_NO,
		       CASE_TIMES,
		       ID_AHCS_INVESTIGATE_TOTAL,
		       ID_AHCS_INVESTIGATE,
		       VERSION,
		       EVALUATE_TIME,
		       EVALUATE_UM,
		       IS_QUALIFIED,
		       IS_ALL_EVIDENCE,
		       EVALUATE_SCORE,
		       IS_JUDGE_ERROR,
		       REJECT_REASON,
		       (select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.EVALUATE_UM limit 1) as EVALUATE_UM_NAME
		  from CLMS_INVESTIGATE_EVALUATE t
		 where t.id_ahcs_investigate = #{idAhcsInvestigate, jdbcType = VARCHAR}
	</select>
	
	

	<select id="getInvestigateEvaluateVersionByIdAhcsInvestigate" resultMap="InvestigateEvaluateVOMap">
		select CREATED_BY,
		       CREATED_DATE,
		       UPDATED_BY,
		       UPDATED_DATE,
		       ID_AHCS_INVESTIGATE_EVALUATE,
		       REPORT_NO,
		       CASE_TIMES,
		       ID_AHCS_INVESTIGATE_TOTAL,
		       ID_AHCS_INVESTIGATE,
		       VERSION,
		       EVALUATE_TIME,
		       EVALUATE_UM,
		       IS_QUALIFIED,
		       IS_ALL_EVIDENCE,
		       EVALUATE_SCORE,
		       IS_JUDGE_ERROR,
		       REJECT_REASON,
		       IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info where user_id = t.EVALUATE_UM limit 1),t.EVALUATE_UM) as EVALUATE_UM_NAME
		  from CLMS_INVESTIGATE_EVALUATE t
		 where t.version = (select a.version - 1
		                      from CLMS_investigate_evaluate a
		                     where a.id_ahcs_investigate = #{idAhcsInvestigate,jdbcType = VARCHAR})
		   and t.id_ahcs_investigate_total =
		       (select b.id_ahcs_investigate_total
		          from CLMS_investigate_evaluate b
		         where b.id_ahcs_investigate = #{idAhcsInvestigate,jdbcType = VARCHAR})
	</select>
	
	

	<insert id="addInvestigateEvaluate" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateEvaluateDTO" >
		INSERT INTO CLMS_INVESTIGATE_EVALUATE (
			CREATED_BY,
		       CREATED_DATE,
		       UPDATED_BY,
		       UPDATED_DATE,
		       ID_AHCS_INVESTIGATE_EVALUATE,
		       REPORT_NO,
		       CASE_TIMES,
		       ID_AHCS_INVESTIGATE_TOTAL,
		       ID_AHCS_INVESTIGATE,
		       VERSION,
		       EVALUATE_TIME,
		       EVALUATE_UM,
		       IS_QUALIFIED,
		       IS_ALL_EVIDENCE,
		       EVALUATE_SCORE,
		       IS_JUDGE_ERROR,
		       REJECT_REASON,
		       evaluate_um_name,
		       ARCHIVE_TIME
			)
		VALUES (
				#{createdBy ,jdbcType=VARCHAR},
				now(),
				#{updatedBy ,jdbcType=VARCHAR},
				now(),
				#{idAhcsInvestigateEvaluate ,jdbcType=VARCHAR},
				#{reportNo ,jdbcType=VARCHAR},
				#{caseTimes ,jdbcType=NUMERIC},
				#{idAhcsInvestigateTotal ,jdbcType=VARCHAR},
				#{idAhcsInvestigate ,jdbcType=VARCHAR},
				#{version ,jdbcType=NUMERIC},
				#{evaluateTime ,jdbcType=TIMESTAMP},
				#{evaluateUm ,jdbcType=VARCHAR},
				#{isQualified ,jdbcType=VARCHAR},
				#{isAllEvidence ,jdbcType=VARCHAR},
				#{evaluateScore ,jdbcType=VARCHAR},
				#{isJudgeError ,jdbcType=VARCHAR},
				#{rejectReason ,jdbcType=VARCHAR},
				(select user_name from CLMS_user_info t where user_id=#{evaluateUm ,jdbcType=VARCHAR}),
				now()
		)
	</insert>
	
	

	<update id="modifyInvestigateEvaluate" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateEvaluateDTO">
		UPDATE CLMS_INVESTIGATE_EVALUATE
		<set>
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy},
			</if>
			UPDATED_DATE=now(),

			<if test="version != null and version != '' ">
				VERSION  = #{version ,jdbcType=NUMERIC},
			</if>
			
			<if test="evaluateTime != null ">
				EVALUATE_TIME  = #{evaluateTime ,jdbcType=TIMESTAMP},
			</if>
			
			<if test="evaluateUm != null and evaluateUm != '' ">
				EVALUATE_UM  = #{evaluateUm ,jdbcType=VARCHAR},
			</if>

			<if test="isQualified != null and isQualified != '' ">
				IS_QUALIFIED  = #{isQualified ,jdbcType=VARCHAR},
			</if>
			
			<if test="isAllEvidence != null and isAllEvidence != '' ">
				IS_ALL_EVIDENCE  = #{isAllEvidence ,jdbcType=VARCHAR},
			</if>
			
			<if test="evaluateScore != null and evaluateScore != '' ">
				EVALUATE_SCORE  = #{evaluateScore ,jdbcType=VARCHAR},
			</if>
			
			<if test="isJudgeError != null and isJudgeError != '' ">
				IS_JUDGE_ERROR  = #{isJudgeError ,jdbcType=VARCHAR},
			</if>
			
			<if test="rejectReason != null and rejectReason != '' ">
				REJECT_REASON  = #{rejectReason ,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE ID_AHCS_INVESTIGATE=#{idAhcsInvestigate}
	</update>
	

	<select id="getNoFinishEvaluateTotalByReportNo" resultMap="InvestigateMap">
		select  
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE,
			REPORT_NO,
			CASE_TIMES,
			INSURED_APPLY_STATUS,
			ACCIDENT_SCENE,
			INITIATE_INVESTIGATE_NODE,
			INITIATOR_UM,
			INVESTIGATE_STATUS,
			INVESTIGATE_ITEMS,
			INVESTIGATE_DEPARTMENT,
			PRIMARY_INVESTIGATOR_UM,
			INIT_MODE,
			INVESTIGATE_CONCLUSION,
			ALLOCATE_INFO,
			INVESTIGATE_QUALITATIVE,
			TASK_INST_ID,
			ID_RM_RUN_BATCH_RECORD,
			IS_EVALUATE_INVESTIGATE,
			IS_OLD_INVESTIGATE,
			IS_HAS_ADJUSTING_FEE,
			FEE_AUDIT_OPTION
		  from CLMS_INVESTIGATE t
		  where t.REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		   AND t.CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		   and t.INVESTIGATE_STATUS != 4
		   and t.INVESTIGATE_STATUS != 5
		   and t.INVESTIGATE_STATUS != 6
		limit 1
	</select>
	
	

	<select id="getInvestigateByTotalInvestigateId" resultMap="InvestigateVOMap">
		SELECT (@rownum := @rownum +1) AS ORDER_NO,
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE,
			t.REPORT_NO,
			t.CASE_TIMES,
			t.INSURED_APPLY_STATUS,
			t.ACCIDENT_SCENE,
			t.INITIATE_INVESTIGATE_NODE,
			t.INITIATOR_UM,
			t.INVESTIGATE_STATUS,
			t.INVESTIGATE_ITEMS,
			t.INVESTIGATE_DEPARTMENT,
			t.PRIMARY_INVESTIGATOR_UM,
			t.INIT_MODE,
			t.INVESTIGATE_CONCLUSION,
			t.ALLOCATE_INFO,
			t.INVESTIGATE_QUALITATIVE,
			t.TASK_INST_ID,
			t.ID_RM_RUN_BATCH_RECORD,
			t.IS_EVALUATE_INVESTIGATE,
			t.IS_OLD_INVESTIGATE,
			t.IS_HAS_ADJUSTING_FEE,
			t.FEE_AUDIT_OPTION,
			IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1),t.INITIATOR_UM) as INITIATOR_UM_NAME,
		    IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.PRIMARY_INVESTIGATOR_UM limit 1),t.PRIMARY_INVESTIGATOR_UM) as PRIMARY_INVESTIGATOR_UM_NAME,
		    (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as DEPARTMENT_NAME
	      from CLMS_investigate t,(SELECT @rownum := 0) AS rn
	     where t.id_ahcs_investigate in
	           (select b.id_ahcs_investigate
	              from CLMS_investigate_evaluate b
	             where b.id_ahcs_investigate_total = #{idAhcsInvestigate,jdbcType=VARCHAR})
	       and t.IS_EVALUATE_INVESTIGATE = 'Y'
	       and t.operate = 1
	     ORDER BY t.CREATED_DATE
	</select>
	
	

	<select id="getEvaluateIndexByTotalId" resultType="String" >
		select a.version
		  from CLMS_investigate_evaluate a
		 where a.id_ahcs_investigate = #{idAhcsInvestigate, jdbcType = VARCHAR}
	</select>
	
	

	<select id="getSerialNumberByInvestigateId" resultType="Integer" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO" >
		select count(1)
		  from CLMS_investigate t
		 where t.report_no = #{reportNo, jdbcType = VARCHAR}
		   and t.case_times = #{caseTimes, jdbcType = NUMERIC}
		   and CREATED_DATE &lt;= #{createdDate, jdbcType = TIMESTAMP}
	</select>
	

	<select id="getEvaluateCountByTotalId" resultType="Integer" >
		select count(1)
		  from CLMS_investigate_evaluate a
		 where a.id_ahcs_investigate_total = #{idAhcsInvestigate, jdbcType = VARCHAR}
	</select>
	

	<select id="getCountInvestigateForRisk" resultType="Integer" >
		select count(1)
		  from CLMS_investigate t
		 where t.report_no = #{reportNo, jdbcType = VARCHAR}
		   and t.case_times = #{caseTimes, jdbcType = NUMERIC}
		   and t.init_mode = '01'
		   and t.INVESTIGATE_STATUS in ('1','2','3') 
		   and t.initiate_investigate_node in 
		   	<foreach collection =  "list" item =  "item" index =  "index" open =  "(" separator =  "," close =  ")"> 
		  		#{item}
		 	</foreach> 
	</select>	
	

	<select id="getCountInvestigateByreportNo" resultType="Integer" >
		select count(1)
		  from CLMS_investigate t
		 where t.report_no = #{reportNo, jdbcType = VARCHAR}
	</select>


	<select id="listAllInitiatorUm" resultType="java.lang.String" >
		select distinct INITIATOR_UM
		  from CLMS_investigate t
	</select>


	<select id="getIsOldInvestigateByreportNo" resultType="String" >
		select IS_OLD_INVESTIGATE
		  from CLMS_investigate t
		 where t.report_no = #{reportNo, jdbcType = VARCHAR}
		 limit 1
	</select>

	<select id="countNoCommit" resultMap="InvestigateMap">
		select *
		from CLMS_investigate t
		where t.report_no = #{reportNo}
        and t.case_Times =  #{caseTimes}
        and operate  =  0
	</select>

	<select id="getSelectItemName" resultType="java.lang.String" parameterType="list">
		select GROUP_CONCAT(`LOWER_VALUE_NAME`  order by LOWER_VALUE_CODE separator ';') from    CLM_COMMON_PARAM_MAPPING  where
		`COLLECTION_CODE`  = 'ACCI_SCENE_IS_0101'
		 and `LOWER_VALUE_CODE`  in (
		     <foreach collection="collectionCode" item="item" separator=",">
				 #{item}
			 </foreach>
		     )
	</select>

    <select id="getIvvesigateCount" resultType="java.lang.Integer">
		select count(0)   from  CLMS_INVESTIGATE  where operate  = 1 and REPORT_NO=#{reportNo}
		<if test="caseTimes != null and caseTimes != '' ">
			AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		</if>
	</select>


    <update id="transformForInvestigate">
		UPDATE CLMS_INVESTIGATE set report_no=#{newReportNo, jdbcType = VARCHAR}, UPDATED_DATE=now()
		WHERE report_no=#{oldReportNo, jdbcType = VARCHAR}
	</update>
	<update id="transformForAdditional">
		UPDATE CLMS_investigate_additional set report_no=#{newReportNo, jdbcType = VARCHAR}, UPDATED_DATE=now()
		WHERE report_no=#{oldReportNo, jdbcType = VARCHAR}
	</update>
	<update id="transformForEvaluate">
		UPDATE CLMS_investigate_evaluate set report_no=#{newReportNo, jdbcType = VARCHAR}, UPDATED_DATE=now()
		WHERE report_no=#{oldReportNo, jdbcType = VARCHAR}
	</update>
	<update id="transformForTask">
		UPDATE CLMS_investigate_task set report_no=#{newReportNo, jdbcType = VARCHAR}, UPDATED_DATE=now()
		WHERE report_no=#{oldReportNo, jdbcType = VARCHAR}
	</update>
	<update id="transformForInfo">
		UPDATE CLMS_task_info set report_no=#{newReportNo, jdbcType = VARCHAR}, UPDATED_DATE=now()
		WHERE report_no=#{oldReportNo, jdbcType = VARCHAR}
	</update>
	<update id="transformForPool">
		UPDATE CLMS_task_pool set report_no=#{newReportNo, jdbcType = VARCHAR}, UPDATED_DATE=now()
		WHERE report_no=#{oldReportNo, jdbcType = VARCHAR}
	</update>
	<update id="transformForfile">
		UPDATE CLMS_file_info set report_no=#{newReportNo, jdbcType = VARCHAR}, UPDATED_DATE=now()
		WHERE report_no=#{oldReportNo, jdbcType = VARCHAR}
	</update>
	
	

	<update id="modifyInvestigateForFee">
		UPDATE CLMS_INVESTIGATE
		set IS_HAS_ADJUSTING_FEE=#{isHasAdjustingFee, jdbcType = VARCHAR}, UPDATED_DATE=now(),
			COMMON_ESTIMAT_FEE = #{commonEstimateFee}
		WHERE ID_AHCS_INVESTIGATE=#{idAhcsInvestigate}
	</update>
	
	<update id="modifyInvestigateForOption">
		UPDATE CLMS_INVESTIGATE
		set FEE_AUDIT_OPTION=#{feeAuditOption, jdbcType = VARCHAR}, UPDATED_DATE=now()
		WHERE ID_AHCS_INVESTIGATE=#{idAhcsInvestigate}
	</update>
	
	
	<delete id="deleteInvestigateForEhis">
	   		delete from CLMS_investigate_task_audit where id_ahcs_investigate_task in (select id_ahcs_investigate_task from CLMS_investigate_task where id_ahcs_investigate in (select id_ahcs_investigate from CLMS_investigate where report_no=#{reportNo, jdbcType = VARCHAR} and case_times=#{caseTimes, jdbcType = NUMERIC} and IS_OLD_INVESTIGATE='E'));
		    delete from CLMS_investigate_process where id_ahcs_investigate_task in (select id_ahcs_investigate_task from CLMS_investigate_task where id_ahcs_investigate in (select id_ahcs_investigate from CLMS_investigate where report_no=#{reportNo, jdbcType = VARCHAR} and case_times=#{caseTimes, jdbcType = NUMERIC} and IS_OLD_INVESTIGATE='E'));
	   	   	delete from CLMS_investigate_task where id_ahcs_investigate in (select id_ahcs_investigate from CLMS_investigate where report_no=#{reportNo, jdbcType = VARCHAR} and case_times=#{caseTimes, jdbcType = NUMERIC} and IS_OLD_INVESTIGATE='E');
	   		
	        delete from CLMS_investigate_audit where id_ahcs_investigate in (select id_ahcs_investigate from CLMS_investigate where report_no=#{reportNo, jdbcType = VARCHAR} and case_times=#{caseTimes, jdbcType = NUMERIC} and IS_OLD_INVESTIGATE='E');
	   	    delete from CLMS_investigate_additional where id_ahcs_investigate in (select id_ahcs_investigate from CLMS_investigate where report_no=#{reportNo, jdbcType = VARCHAR} and case_times=#{caseTimes, jdbcType = NUMERIC} and IS_OLD_INVESTIGATE='E');
	   		delete from CLMS_investigate where report_no=#{reportNo, jdbcType = VARCHAR} and case_times=#{caseTimes, jdbcType = NUMERIC} and IS_OLD_INVESTIGATE='E';
    </delete>

	<update id="synchronizeDepartment">
		<foreach collection="investigateDTOList" item="item" open="begin" separator=";" close=";end;">
			update CLMS_investigate
			set INITIATE_DEPARTMENT = #{item.initiateDepartment, jdbcType=VARCHAR},
			UPDATED_DATE = now()
			where INITIATOR_UM = #{item.initiatorUm, jdbcType=VARCHAR}
		</foreach>
	</update>



	<delete id="deleteInvestigateNoOperate">
		delete from CLMS_investigate where report_no=#{reportNo} and case_times=#{caseTimes } and operate ='0';
	</delete>

    <select id="getInvestigateRecordByPolicyClient" resultMap="InvestigateMap">
        select t1.CREATED_BY,
               t1.CREATED_DATE,
               t1.UPDATED_BY,
               t1.UPDATED_DATE,
               t1.ID_AHCS_INVESTIGATE,
               t1.REPORT_NO,
               t1.CASE_TIMES,
               t1.INSURED_APPLY_STATUS,
               t1.ACCIDENT_SCENE,
               t1.INITIATE_INVESTIGATE_NODE,
               t1.INITIATOR_UM,
               t1.INVESTIGATE_STATUS,
               t1.INVESTIGATE_ITEMS,
               t1.INVESTIGATE_DEPARTMENT,
               t1.PRIMARY_INVESTIGATOR_UM,
               t1.INIT_MODE,
               t1.INVESTIGATE_CONCLUSION,
               t1.ALLOCATE_INFO,
               t1.INVESTIGATE_QUALITATIVE,
               t1.TASK_INST_ID,
               t1.ID_RM_RUN_BATCH_RECORD,
               t1.IS_EVALUATE_INVESTIGATE,
               t1.IS_OLD_INVESTIGATE,
               t1.IS_HAS_ADJUSTING_FEE,
               t1.FEE_AUDIT_OPTION,
               t1.INITIATE_DEPARTMENT
        from CLMS_INVESTIGATE t1
                 JOIN CLM_CASE_BASE CB ON CB.REPORT_NO = T1.REPORT_NO
            AND CB.CASE_TIMES = T1.CASE_TIMES
                 JOIN CLMS_REPORT_CUSTOMER RC ON RC.REPORT_NO = CB.REPORT_NO
        WHERE (CB.POLICY_NO, RC.CLIENT_NO) IN (SELECT CB1.POLICY_NO,
                                                      RC1.CLIENT_NO
                                               FROM CLM_CASE_BASE CB1
                                                        JOIN CLMS_REPORT_CUSTOMER RC1 ON RC1.REPORT_NO = CB1.REPORT_NO
                                               WHERE CB1.REPORT_NO = #{reportNo, jdbcType = VARCHAR}
                                                 AND CB1.CASE_TIMES = #{caseTimes, jdbcType = NUMERIC})
		    AND t1.OPERATE = '1'
        <if test="investigateStatusList != null and investigateStatusList.size() > 0">
            AND t1.INVESTIGATE_STATUS IN
            <foreach collection="investigateStatusList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getAbnormalInvestigateByClientNo" resultMap="InvestigateMap">
        select t1.CREATED_BY,
               t1.CREATED_DATE,
               t1.UPDATED_BY,
               t1.UPDATED_DATE,
               t1.ID_AHCS_INVESTIGATE,
               t1.REPORT_NO,
               t1.CASE_TIMES,
               t1.INSURED_APPLY_STATUS,
               t1.ACCIDENT_SCENE,
               t1.INITIATE_INVESTIGATE_NODE,
               t1.INITIATOR_UM,
               t1.INVESTIGATE_STATUS,
               t1.INVESTIGATE_ITEMS,
               t1.INVESTIGATE_DEPARTMENT,
               t1.PRIMARY_INVESTIGATOR_UM,
               t1.INIT_MODE,
               t1.INVESTIGATE_CONCLUSION,
               t1.ALLOCATE_INFO,
               t1.INVESTIGATE_QUALITATIVE,
               t1.TASK_INST_ID,
               t1.ID_RM_RUN_BATCH_RECORD,
               t1.IS_EVALUATE_INVESTIGATE,
               t1.IS_OLD_INVESTIGATE,
               t1.IS_HAS_ADJUSTING_FEE,
               t1.FEE_AUDIT_OPTION,
               t1.INITIATE_DEPARTMENT
        from CLMS_INVESTIGATE t1
                 JOIN CLMS_REPORT_CUSTOMER RC ON RC.REPORT_NO = t1.REPORT_NO
                 JOIN CLMS_INVESTIGATE_TASK IT ON IT.ID_AHCS_INVESTIGATE = T1.ID_AHCS_INVESTIGATE
                 JOIN CLMS_INVESTIGATE_TASK_AUDIT IA ON IA.ID_AHCS_INVESTIGATE_TASK = IT.ID_AHCS_INVESTIGATE_TASK
                 JOIN CLMS_INVESTIGATE_SCORE cis on cis.ID_AHCS_INVESTIGATE_TASK = it.ID_AHCS_INVESTIGATE_TASK
        WHERE RC.CLIENT_NO = #{clientNo, jdbcType = VARCHAR}
          AND t1.INVESTIGATE_QUALITATIVE = 'IQ_0503'
          and ia.REVIEW_OPINION = '1'
          and cis.IS_JUDGE_ERROR = 'N'
    </select>

	<select id="getParamMappingDto" resultMap="commonParameter" parameterType="map" >
		SELECT DEPARTMENT_CODE,
		COLLECTION_CODE,
		COLLECTION_NAME,
		`LOWER_VALUE_NAME` ,`LOWER_VALUE_CODE`
		FROM CLM_COMMON_PARAM_MAPPING
		WHERE COLLECTION_CODE =  #{collectionCode}
		AND `UPPER_VALUE_CODE` IN
		(SELECT M.`VALUE_CODE`
		from CLM_COMMON_PARAMETER M
		WHERE M.COLLECTION_CODE =   #{collectionCode}
		AND M.`VALUE_CODE` = #{valueCode} ) order by  `LOWER_VALUE_CODE`
	</select>
</mapper>