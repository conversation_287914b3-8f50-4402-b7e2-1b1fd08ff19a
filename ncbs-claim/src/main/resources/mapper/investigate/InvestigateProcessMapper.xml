<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateProcessMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO" id="InvestigateProcessMap">
		<id property="idAhcsInvestigateProcess" column="ID_AHCS_INVESTIGATE_PROCESS" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigateTask" column="ID_AHCS_INVESTIGATE_TASK" />
		<result property="investigateWay" column="INVESTIGATE_WAY" />
		<result property="investigateDate" column="INVESTIGATE_DATE" />
		<result property="customerName" column="CUSTOMER_NAME" />
		<result property="resultDescription" column="RESULT_DESCRIPTION" />
		<result property="otherInformation" column="OTHER_INFORMATION" />
	</resultMap>


	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateProcessVO" extends="InvestigateProcessMap" id="InvestigateProcessVOMap">
		
		<result property="investigateWayName" column="INVESTIGATE_WAY_NAME" />
		
	</resultMap>
	
	

	<insert id="addInvestigateProcess" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO" >
		INSERT INTO CLMS_INVESTIGATE_PROCESS (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_PROCESS,
			ID_AHCS_INVESTIGATE_TASK,
			INVESTIGATE_WAY,
			INVESTIGATE_DATE,
			CUSTOMER_NAME,
			RESULT_DESCRIPTION,
			OTHER_INFORMATION,
			ARCHIVE_TIME
		)
	VALUES (
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsInvestigateProcess ,jdbcType=VARCHAR},
			#{idAhcsInvestigateTask ,jdbcType=VARCHAR},
			#{investigateWay ,jdbcType=VARCHAR},
			#{investigateDate ,jdbcType=DATE},
			#{customerName ,jdbcType=VARCHAR},
			#{resultDescription ,jdbcType=VARCHAR},
			#{otherInformation ,jdbcType=VARCHAR},
			now()
	)
	</insert>
	
	
	

	<insert id="addInvestigateProcessList" parameterType="java.util.List">
		<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">   
			INSERT INTO CLMS_INVESTIGATE_PROCESS (
				CREATED_BY,
				CREATED_DATE,
				UPDATED_BY,
				UPDATED_DATE,
				ID_AHCS_INVESTIGATE_PROCESS,
				ID_AHCS_INVESTIGATE_TASK,
				INVESTIGATE_WAY,
				INVESTIGATE_DATE,
				CUSTOMER_NAME,
				RESULT_DESCRIPTION,
				OTHER_INFORMATION,
				ARCHIVE_TIME
			)
		VALUES (
			#{item.createdBy ,jdbcType=VARCHAR},
			now(),
			#{item.updatedBy ,jdbcType=VARCHAR},
			now(),
			#{item.idAhcsInvestigateProcess ,jdbcType=VARCHAR},
			#{item.idAhcsInvestigateTask ,jdbcType=VARCHAR},
			#{item.investigateWay ,jdbcType=VARCHAR},
			#{item.investigateDate ,jdbcType=DATE},
			#{item.customerName ,jdbcType=VARCHAR},
			#{item.resultDescription ,jdbcType=VARCHAR},
			#{item.otherInformation ,jdbcType=VARCHAR},
			now()
		)
		</foreach>
	</insert>
	
	
	

	<update id="modifyInvestigateProcess" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO">
		UPDATE CLMS_INVESTIGATE_PROCESS
		<set>
					
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy}, 
			</if>
			UPDATED_DATE=now(),
					
			<if test="investigateWay != null and investigateWay != '' ">
				INVESTIGATE_WAY  = #{investigateWay}, 
			</if>
					
			<if test="investigateDate != null ">
				INVESTIGATE_DATE  = #{investigateDate}, 
			</if>
					
			<if test="customerName != null and customerName != '' ">
				CUSTOMER_NAME  = #{customerName}, 
			</if>
					
			<if test="resultDescription != null and resultDescription != '' ">
				RESULT_DESCRIPTION  = #{resultDescription}, 
			</if>
					
			<if test="otherInformation != null and otherInformation != '' ">
				OTHER_INFORMATION  = #{otherInformation}, 
			</if>
		</set>
		WHERE ID_AHCS_INVESTIGATE_PROCESS=#{idAhcsInvestigateProcess} 
	</update>
	
		

	<delete id="deleteInvestigateProcessById" parameterType="String">
		DELETE FROM CLMS_INVESTIGATE_PROCESS
		where  ID_AHCS_INVESTIGATE_PROCESS=#{idAhcsInvestigateProcess} 
	</delete>


	<select id="getInvestigateProcessByTaskId" resultMap="InvestigateProcessVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_PROCESS,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.INVESTIGATE_WAY,
			t.INVESTIGATE_DATE,
			t.CUSTOMER_NAME,
			t.RESULT_DESCRIPTION,
			t.OTHER_INFORMATION,
			
			(select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.INVESTIGATE_WAY limit 1) as INVESTIGATE_WAY_NAME
		
		from CLMS_INVESTIGATE_PROCESS t
		where  ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
	</select>
	
	

	<select id="getInvestigateProcessAssistByInvestigateId" resultMap="InvestigateProcessVOMap">
		select 
		    t.CREATED_BY,
		    t.CREATED_DATE,
		    t.UPDATED_BY,
		    t.UPDATED_DATE,
		    t.ID_AHCS_INVESTIGATE_PROCESS,
		    t.ID_AHCS_INVESTIGATE_TASK,
		    t.INVESTIGATE_WAY,
		    t.INVESTIGATE_DATE,
		    t.CUSTOMER_NAME,
		    t.RESULT_DESCRIPTION,
		    t.OTHER_INFORMATION,
		    
		    (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.INVESTIGATE_WAY limit 1) as INVESTIGATE_WAY_NAME
    
		from CLMS_INVESTIGATE_PROCESS t
	    where  ID_AHCS_INVESTIGATE_TASK in (
	    select a.id_ahcs_investigate_task from CLMS_investigate_task a where a.is_primary_task='N' and a.id_ahcs_investigate=#{idAhcsInvestigate}
	    )
	</select>
	

	<select id="getCustomerNameByTaskId" resultType="String">
		select a.name
		  from CLMS_REPORT_CUSTOMER a
		 where a.report_no =
		       (select t.report_no
		          from CLMS_investigate_task t
		         where t.id_ahcs_investigate_task =
		               #{idAhcsInvestigateTask})	
	</select>
	

	<select id="getReportNoByTaskId" resultType="String">
		select t.report_no
          from CLMS_investigate_task t
         where t.id_ahcs_investigate_task =
               #{idAhcsInvestigateTask}
	</select>
	
	<delete id="deleteByInvestigateTaskId" parameterType="String">
		DELETE FROM CLMS_INVESTIGATE_PROCESS
		where ID_AHCS_INVESTIGATE_TASK = #{idAhcsInvestigateTask}
	</delete>
	
	
</mapper>