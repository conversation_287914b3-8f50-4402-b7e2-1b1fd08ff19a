<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditPublicMapper">

    <select id="getInvestigateTaskList" resultType="com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO">
        SELECT
            t.TASK_DEFINITION_BPM_KEY AS "taskDefinitionBpmKey",
            t.report_no AS "reportNo",
            dd.DEPARTMENT_ABBR_NAME AS "policyDepartmentName",
            a.name AS "insuredName",
            cpi.PRODUCT_NAME AS "productName",
            CASE
                WHEN t.TASK_DEFINITION_BPM_KEY IN ('OC_RESTART_CASE_APPROVAL', 'OC_RESTART_CASE_MODIFY') THEN TIMESTAMPDIFF(hour, crc.UPDATED_DATE, NOW())
                ELSE TIMESTAMPDIFF(hour, b.REPORT_DATE, NOW())
            END AS "caseLength",
            DATEDIFF(NOW(), t.CREATED_DATE) AS "currentLength",
            t.ASSIGNEE_TIME AS "assigneeTime",
            t.APPLYER_NAME AS "submitName",
            cit.ID_AHCS_INVESTIGATE AS "idAhcsInvestigate",
            cit.CASE_TIMES AS "caseTimes",
            t.TASK_ID AS "taskId"
        FROM
            clms_task_info t
        JOIN clms_investigate_task cit ON t.task_id = cit.id_ahcs_investigate_task
        JOIN clms_investigate ci ON ci.ID_AHCS_INVESTIGATE = cit.ID_AHCS_INVESTIGATE
        JOIN clms_report_customer a ON a.report_no = t.report_no
        JOIN clm_report_info b ON b.report_no = t.report_no
        JOIN clms_policy_info cpi ON cpi.REPORT_NO = t.REPORT_NO
        LEFT JOIN department_define dd ON dd.DEPARTMENT_CODE = t.DEPARTMENT_CODE
        LEFT JOIN clm_restart_case_record crc ON crc.report_no = t.report_no AND crc.case_times = t.case_times
        WHERE
            t.status IN ('0', '3')
            AND t.TASK_DEFINITION_BPM_KEY = 'OC_MAJOR_INVESTIGATE'
            <if test="investigateDepartment != null and !investigateDepartment.isEmpty()">
                AND ci.INVESTIGATE_DEPARTMENT IN
                <foreach collection="investigateDepartment" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="investigateDepartment == null or investigateDepartment.isEmpty()">
                AND ci.INVESTIGATE_DEPARTMENT = ''
            </if>
    </select>

</mapper>