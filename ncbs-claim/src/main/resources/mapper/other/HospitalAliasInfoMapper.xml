<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.HospitalAliasMapper">
	<resultMap type="com.paic.ncbs.claim.model.vo.other.HospitalAliasInfoVO" id="hospitalAliasMap">
		<id column="id" property="id"/>
		<result column="created_by" property="createdBy"/>
		<result column="updated_by" property="updatedBy"/>
		<result column="delete_flag" property="deleteFlag"/>
		<result column="hospital_id" property="hospitalId"/>
		<result column="hospital_alias" property="hospitalAlias"/>
		<result column="data_source" property="dataSource"/>
		<result column="sys_ctime" property="sysCtime"/>
		<result column="sys_utime" property="sysUtime"/>
	</resultMap>

    <!--查询医院别名信息-->
	<select id="getHospitalAliasList" resultMap="hospitalAliasMap">
		select id,
               created_by,
               updated_by,
               delete_flag,
               hospital_id,
               hospital_alias,
               data_source,
               sys_ctime,
               sys_utime
		  FROM clm_hospital_alias_info
		  where hospital_id = #{hospitalId}
            and delete_flag = '0'
	</select>

    <!--新增医院别名信息-->
    <insert id="batchAddHospitalAlias">
        insert into clm_hospital_alias_info
            (created_by,
            updated_by,
            delete_flag,
            hospital_id,
            hospital_alias,
            data_source,
            sys_ctime,
            sys_utime)
            values
        <foreach collection="dtoList" item="data" separator=",">
            (#{data.createdBy},
            #{data.updatedBy},
            #{data.deleteFlag},
            #{data.hospitalId},
            #{data.hospitalAlias},
            #{data.dataSource},
            now(),
            now())
        </foreach>
    </insert>

    <!--查询已存在的医院别名信息-->
    <select id="selectExistingAliases" resultType="java.util.Map">
        select
            hospital_id AS hospitalId,
            hospital_alias AS hospitalAlias
        FROM clm_hospital_alias_info
        WHERE (hospital_id, hospital_alias) IN
        <foreach collection="dtoList" item="item" open="(" separator="," close=")">
            (#{item.hospitalId}, #{item.hospitalAlias})
        </foreach>
    </select>

    <update id="deleteHospitalInfo" parameterType="com.paic.ncbs.claim.model.dto.other.HospitalAliasInfoDTO">
        update clm_hospital_alias_info
        set sys_utime = now(),
        updated_by = #{updatedBy,jdbcType=VARCHAR},
        delete_flag = #{deleteFlag,jdbcType=NUMERIC}
        WHERE id = #{id,jdbcType=NUMERIC}
    </update>


</mapper>