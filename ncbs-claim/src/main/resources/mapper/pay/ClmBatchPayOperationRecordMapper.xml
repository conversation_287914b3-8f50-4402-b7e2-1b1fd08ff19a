<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pay.ClmBatchPayOperationRecordMapper">

    <resultMap type="com.paic.ncbs.claim.model.vo.pay.BatchPayOperationRecordVO" id="ClmBatchPayOperationRecordMap">
        <id column="id" property="id"/>
        <result property="batch_no" column="batchNo" />
        <result property="operation_node" column="operationNode" />
        <result property="payee_name" column="payeeName" />
        <result property="payee_bank_account" column="payeeBankAccount" />
        <result property="created_by" column="createdBy" />
        <result property="updated_by" column="updatedBy" />
        <result property="sys_ctime" column="sysCtime" />
        <result property="sys_utime" column="sysUtime" />
    </resultMap>

    <insert id="addClmBatchPayOperationRecord"
            parameterType="com.paic.ncbs.claim.model.vo.pay.BatchPayOperationRecordVO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO clm_batch_pay_operation_record(
        del_flag,
        batch_no,
        operation_node,
        payee_name,
        payee_bank_account,
        remark,
        created_by,
        updated_by,
        sys_ctime,
        sys_utime)
        VALUES(
        #{delFlag,jdbcType=VARCHAR},
        #{batchNo,jdbcType=VARCHAR},
        #{operationNode,jdbcType=VARCHAR},
        #{payeeName,jdbcType=VARCHAR},
        #{payeeBankAccount,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{updatedBy,jdbcType=VARCHAR},
        now(),
        now())
    </insert>

    <select id="queryOperationRecordByBatchNo" resultMap="ClmBatchPayOperationRecordMap">
        select
            cbpor.id,
            cbpor.batch_no,
            cbpor.operation_node,
            cbpor.payee_name,
            cbpor.payee_bank_account,
            cbpor.created_by,
            cbpor.updated_by,
            cbpor.sys_ctime,
            cbpor.sys_utime
        from clm_batch_pay_operation_record cbpor
        where cbpor.batch_no = #{batchNo,jdbcType=VARCHAR}
        and cbpor.del_flag = 'N'
        order by cbpor.sys_ctime desc
    </select>

</mapper>

