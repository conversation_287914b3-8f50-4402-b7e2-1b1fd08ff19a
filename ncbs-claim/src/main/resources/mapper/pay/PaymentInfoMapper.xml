<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper">

	<resultMap id="resultDTO" type="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO">
		<id column="ID_CLM_PAYMENT_INFO" property="idClmPaymentInfo"/>
		<result column="REPORT_NO" property="reportNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="APPLY_NO" property="applyNo"/>
		<result column="SUB_TIMES" property="subTimes"/>
		<result column="ID_CLM_CHANNEL_PROCESS" property="idClmChannelProcess"/>
		<result column="PAYMENT_INFO_TYPE" property="paymentInfoType"/>
		<result column="COLLECT_PAY_APPROACH" property="collectPayApproach"/>
		<result column="BANK_ACCOUNT_ATTRIBUTE"  property="bankAccountAttribute"/>
		<result column="CITY_NAME"  property="cityName"/>
		<result column="CLIENT_BANK_ACCOUNT"  property="clientBankAccount"/>
		<result column="CLIENT_BANK_CODE"  property="clientBankCode"/>
		<result column="CLIENT_BANK_NAME"  property="clientBankName"/>
		<result column="CLIENT_CERTIFICATE_NO"  property="clientCertificateNo"/>
		<result column="CLIENT_CERTIFICATE_TYPE"  property="clientCertificateType"/>
		<result column="CLIENT_MOBILE"  property="clientMobile"/>
		<result column="CLIENT_NAME"  property="clientName"/>
		<result column="CLIENT_TYPE"  property="clientType"/>
		<result column="PROVINCE_NAME"  property="provinceName"/>
		<result column="REGION_CODE"  property="regionCode"/>
		<result column="PAYMENT_INFO_STATUS"  property="paymentInfoStatus"/>
		<result column="VALID_BEGIN_DATE"  property="validBeginDate"/>
		<result column="VALID_END_DATE"  property="validEndDate"/>
		<result column="REMARK"  property="remark"/>
		<result column="IS_MERGE_PAY"  property="isMergePay"/>
		<result column="DOCUMENT_GROUP_ID"  property="documentGroupId"/>
		<result column="MIGRATE_FROM"  property="migrateFrom"/>
		<result column="COUNTER_PAYMENT_REASON"  property="counterPaymentReason"/>
		<result column="OTHER_REASON"  property="otherReason"/>
		<result column="DATA_SOURCE"  property="dataSource"/>
		<result column="ORGANIZE_CODE"  property="organizeCode"/>
		<result column="IS_TAXPAYER"  property="isTaxpayer"/>
		<result column="IS_AGREE_IMMEDIATELY_PAY"  property="isAgreeImmediatelyPay"/>
		<result column="PAYMENT_USAGE"  property="paymentUsage"/>
		<result column="MERGE_STRATEGY"  property="mergeStrategy"/>
		<result column="BANK_DETAIL"  property="bankDetail"/>
		<result column="CITY_CODE"  property="cityCode"/>
		<result column="CLIENT_RELATION"  property="clientRelation"/>
		<result column="BANK_DETAIL_CODE"  property="bankDetailCode"/>
		<result column="CUSTOMER_NO" property="customerNo"/>
		<result column="AGENCY_TYPE" property="agencyType"/>
		<result column="COMPANY_CARD_TYPE" property="companyCardType"/>
		<result column="OPEN_ID" property="openId"/>
		<result column="PAY_TYPE" property="payType"/>
	</resultMap>

	<insert id="addPaymentInfo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO">
		INSERT INTO CLM_PAYMENT_INFO(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_CLM_PAYMENT_INFO,
			REPORT_NO,
			APPLY_NO,
			CASE_TIMES,
			SUB_TIMES,
			ID_CLM_CHANNEL_PROCESS,
			PAYMENT_INFO_TYPE,
			COLLECT_PAY_APPROACH,
			BANK_ACCOUNT_ATTRIBUTE,
			CITY_NAME,
			CLIENT_BANK_ACCOUNT,
			CLIENT_BANK_CODE,
			CLIENT_BANK_NAME,
			CLIENT_CERTIFICATE_NO,
			CLIENT_CERTIFICATE_TYPE,
			CLIENT_MOBILE,
			CLIENT_NAME,
			CLIENT_TYPE,
			PROVINCE_NAME,
			REGION_CODE,
			PAYMENT_INFO_STATUS,
			VALID_BEGIN_DATE,
			VALID_END_DATE,
			REMARK,
			IS_MERGE_PAY,
			DOCUMENT_GROUP_ID,
			MIGRATE_FROM,
			COUNTER_PAYMENT_REASON,
			OTHER_REASON,
			DATA_SOURCE,
			ORGANIZE_CODE,
			IS_TAXPAYER,
			IS_AGREE_IMMEDIATELY_PAY,
			PAYMENT_USAGE,
			ARCHIVE_DATE,
			MERGE_STRATEGY,
		    BANK_DETAIL,
		    CITY_CODE,
			CLIENT_RELATION,
			BANK_DETAIL_CODE,
			CUSTOMER_NO,
		    AGENCY_TYPE,
			COMPANY_CARD_TYPE,
			PAY_TYPE,
			OPEN_ID
		)
		VALUES(
			#{createdBy,jdbcType=VARCHAR},
			now(),
			#{updatedBy,jdbcType=VARCHAR},
			now(),
			#{idClmPaymentInfo,jdbcType=VARCHAR},
			#{reportNo,jdbcType=VARCHAR},
			#{applyNo,jdbcType=VARCHAR},
			#{caseTimes,jdbcType=NUMERIC},
			#{subTimes,jdbcType=NUMERIC},
			#{idClmChannelProcess,jdbcType=VARCHAR},
			#{paymentInfoType,jdbcType=VARCHAR},
			#{collectPayApproach,jdbcType=VARCHAR},
			#{bankAccountAttribute,jdbcType=VARCHAR},
			#{cityCode,jdbcType=VARCHAR},
			#{clientBankAccount,jdbcType=VARCHAR},
			#{clientBankCode,jdbcType=VARCHAR},
			#{clientBankName,jdbcType=VARCHAR},
			#{clientCertificateNo,jdbcType=VARCHAR},
			#{clientCertificateType,jdbcType=VARCHAR},
			#{clientMobile,jdbcType=VARCHAR},
			#{clientName,jdbcType=VARCHAR},
			#{clientType,jdbcType=VARCHAR},
			#{provinceName,jdbcType=VARCHAR},
			#{regionCode,jdbcType=VARCHAR},
			#{paymentInfoStatus,jdbcType=VARCHAR},
			#{validBeginDate,jdbcType=TIMESTAMP},
			#{validEndDate,jdbcType=TIMESTAMP},
			#{remark,jdbcType=VARCHAR},
			#{isMergePay,jdbcType=VARCHAR},
			#{documentGroupId,jdbcType=VARCHAR},
			#{migrateFrom,jdbcType=VARCHAR},
			#{counterPaymentReason,jdbcType=VARCHAR},
			#{otherReason,jdbcType=VARCHAR},
			#{dataSource,jdbcType=VARCHAR},
			#{organizeCode,jdbcType=VARCHAR},
			#{isTaxpayer,jdbcType=VARCHAR},
			#{isAgreeImmediatelyPay,jdbcType=VARCHAR},
			#{paymentUsage,jdbcType=VARCHAR},
			now(),
			#{mergeStrategy,jdbcType=VARCHAR},
			#{bankDetail,jdbcType=VARCHAR},
			#{cityCode,jdbcType=VARCHAR},
			#{clientRelation,jdbcType=VARCHAR},
			#{bankDetailCode,jdbcType=VARCHAR},
			#{customerNo,jdbcType=VARCHAR},
			#{agencyType,jdbcType=VARCHAR},
			#{companyCardType,jdbcType=VARCHAR},
			#{payType,jdbcType=VARCHAR},
			#{openId, jdbcType=VARCHAR}
		)
	</insert>

	<select id="getPaymentInfoByReportNo" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentInfo">
		select
			id_clm_payment_info idClmPaymentInfo,
			report_no reportNo,
			case_times caseTimes,
			collect_pay_approach collectPayApproach,
			client_bank_account clientBankAccount,
			client_bank_name clientBankName,
			client_name clientName,
			client_type clientType,
			client_mobile clientMobile,
			payment_Usage paymentUsage,
			payment_Info_Type paymentInfoType
		from clm_payment_info
		where report_no = #{reportNo}
		order by created_date desc
	</select>

	<select id="getPaymentInfo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO" resultMap="resultDTO">
		SELECT
			ID_CLM_PAYMENT_INFO,
			REPORT_NO,
			CASE_TIMES,
			SUB_TIMES,
			ID_CLM_CHANNEL_PROCESS,
			PAYMENT_INFO_TYPE,
			COLLECT_PAY_APPROACH,
			BANK_ACCOUNT_ATTRIBUTE,
			CITY_NAME,
			CLIENT_BANK_ACCOUNT,
			CLIENT_BANK_CODE,
			CLIENT_BANK_NAME,
			CLIENT_CERTIFICATE_NO,
			CLIENT_CERTIFICATE_TYPE,
			CLIENT_MOBILE,
			CLIENT_NAME,
			CLIENT_TYPE,
			PROVINCE_NAME,
			REGION_CODE,
			PAYMENT_INFO_STATUS,
			VALID_BEGIN_DATE,
			VALID_END_DATE,
			REMARK,
			IS_MERGE_PAY,
			DOCUMENT_GROUP_ID,
			MIGRATE_FROM,
			COUNTER_PAYMENT_REASON,
			OTHER_REASON,
			DATA_SOURCE,
			ORGANIZE_CODE,
			IS_TAXPAYER,
			IS_AGREE_IMMEDIATELY_PAY,
			PAYMENT_USAGE,
			MERGE_STRATEGY,
		    BANK_DETAIL,CITY_CODE,
			CLIENT_RELATION,
			BANK_DETAIL_CODE,
			AGENCY_TYPE,
			CUSTOMER_NO,
			COMPANY_CARD_TYPE,
			PAY_TYPE,
			OPEN_ID
		FROM CLM_PAYMENT_INFO
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND PAYMENT_INFO_STATUS != '1'
		<if test="subTimes != null">
			AND SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
		</if>
		<if test="paymentInfoType != null and paymentInfoType != ''">
			AND PAYMENT_INFO_TYPE = #{paymentInfoType,jdbcType=VARCHAR}
		</if>
		<if test="paymentUsage != null and paymentUsage != ''">
			AND PAYMENT_USAGE = #{paymentUsage,jdbcType=VARCHAR}
		</if>
	</select>
	<sql id="Base_Column_List">
		COMPANY_CARD_TYPE,CUSTOMER_NO,AGENCY_TYPE,BANK_DETAIL_CODE,CLIENT_RELATION,CITY_CODE,BANK_DETAIL,VALID_END_DATE,VALID_BEGIN_DATE,PAYMENT_INFO_STATUS,REGION_CODE,PROVINCE_NAME,CLIENT_TYPE,CLIENT_NAME, CLIENT_MOBILE,CLIENT_CERTIFICATE_TYPE,CLIENT_CERTIFICATE_NO,CLIENT_BANK_NAME,CLIENT_BANK_CODE,CLIENT_BANK_ACCOUNT,CITY_NAME,BANK_ACCOUNT_ATTRIBUTE,COLLECT_PAY_APPROACH,PAYMENT_INFO_TYPE,ID_CLM_CHANNEL_PROCESS, SUB_TIMES,CASE_TIMES,REPORT_NO,ID_CLM_PAYMENT_INFO,MERGE_STRATEGY,ARCHIVE_DATE,
		PAYMENT_USAGE,IS_AGREE_IMMEDIATELY_PAY,IS_TAXPAYER,ORGANIZE_CODE,DATA_SOURCE,OTHER_REASON, COUNTER_PAYMENT_REASON,MIGRATE_FROM,DOCUMENT_GROUP_ID,IS_MERGE_PAY,REMARK
	</sql>
	<select id="getPaymentInfoById" parameterType="java.lang.String" resultMap="resultDTO">
		SELECT
		ID_CLM_PAYMENT_INFO,
		REPORT_NO,
		CASE_TIMES,
		SUB_TIMES,
		ID_CLM_CHANNEL_PROCESS,
		PAYMENT_INFO_TYPE,
		COLLECT_PAY_APPROACH,
		BANK_ACCOUNT_ATTRIBUTE,
		CITY_NAME,
		CLIENT_BANK_ACCOUNT,
		CLIENT_BANK_CODE,
		CLIENT_BANK_NAME,
		CLIENT_CERTIFICATE_NO,
		CLIENT_CERTIFICATE_TYPE,
		CLIENT_MOBILE,
		CLIENT_NAME,
		CLIENT_TYPE,
		PROVINCE_NAME,
		REGION_CODE,
		PAYMENT_INFO_STATUS,
		VALID_BEGIN_DATE,
		VALID_END_DATE,
		REMARK,
		IS_MERGE_PAY,
		DOCUMENT_GROUP_ID,
		MIGRATE_FROM,
		COUNTER_PAYMENT_REASON,
		OTHER_REASON,
		DATA_SOURCE,
		ORGANIZE_CODE,
		IS_TAXPAYER,
		IS_AGREE_IMMEDIATELY_PAY,
		PAYMENT_USAGE,
		MERGE_STRATEGY,BANK_DETAIL,CITY_CODE,
		CLIENT_RELATION,
		BANK_DETAIL_CODE,
		CUSTOMER_NO,
		AGENCY_TYPE,
		COMPANY_CARD_TYPE,
		PAY_TYPE,
		OPEN_ID
		FROM CLM_PAYMENT_INFO
		WHERE ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
		AND PAYMENT_INFO_STATUS != '1'
	</select>
	<update id="updateById" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO">
		UPDATE CLM_PAYMENT_INFO
		set UPDATED_DATE = now(),
		UPDATED_BY=#{updatedBy},
		PAYMENT_INFO_STATUS='1'
		where ID_CLM_PAYMENT_INFO=#{idClmPaymentInfo}
	</update>
	<update id="updatePaymentInfo" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO">
		UPDATE CLM_PAYMENT_INFO
		SET UPDATED_DATE = now(),
		UPDATED_BY = #{updatedBy,jdbcType=VARCHAR}
		<if test="collectPayApproach != null">
			, COLLECT_PAY_APPROACH = #{collectPayApproach,jdbcType=VARCHAR}
		</if>
		<if test="clientBankAccount != null">
			, CLIENT_BANK_ACCOUNT = #{clientBankAccount,jdbcType=VARCHAR}
		</if>
		<if test="clientBankCode != null">
			, CLIENT_BANK_CODE = #{clientBankCode,jdbcType=VARCHAR}
		</if>
		<if test="clientBankName != null">
			, CLIENT_BANK_NAME = #{clientBankName,jdbcType=VARCHAR}
		</if>
		<if test="clientCertificateNo != null">
			, CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR}
		</if>
		<if test="clientCertificateType != null">
			, CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR}
		</if>
		<if test="clientMobile != null">
			, CLIENT_MOBILE = #{clientMobile,jdbcType=VARCHAR}
		</if>
		<if test="clientName != null">
			, CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
		</if>
		<if test="clientType != null">
			, CLIENT_TYPE = #{clientType,jdbcType=VARCHAR}
		</if>
		<if test="provinceName != null">
			, PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR}
		</if>
		<if test="cityName != null">
			, CITY_NAME = #{cityCode,jdbcType=VARCHAR}
		</if>
		<if test="organizeCode != null">
			, ORGANIZE_CODE = #{organizeCode,jdbcType=VARCHAR}
		</if>
		<if test="paymentUsage != null">
			, PAYMENT_USAGE = #{paymentUsage,jdbcType=VARCHAR}
		</if>
		<if test="paymentInfoStatus != null">
			, PAYMENT_INFO_STATUS = #{paymentInfoStatus,jdbcType=VARCHAR}
		</if>
		<if test="cityCode != null">
			, CITY_CODE = #{cityCode,jdbcType=VARCHAR}
		</if>
		<if test="bankDetail != null">
			, BANK_DETAIL = #{bankDetail,jdbcType=VARCHAR}
		</if>
		<if test="regionCode != null">
			, REGION_CODE = #{regionCode,jdbcType=VARCHAR}
		</if>
		<if test="bankAccountAttribute != null">
			, BANK_ACCOUNT_ATTRIBUTE = #{bankAccountAttribute,jdbcType=VARCHAR}
		</if>
		<if test="remark != null">
			, remark = #{remark,jdbcType=VARCHAR}
		</if>
		<if test="bankDetailCode != null">
			, BANK_DETAIL_CODE = #{bankDetailCode,jdbcType=VARCHAR}
		</if>
		<if test="clientRelation != null">
			, CLIENT_RELATION = #{clientRelation,jdbcType=VARCHAR}
		</if>
		<if test="agencyType != null and agencyType!=''">
			, AGENCY_TYPE = #{agencyType,jdbcType=VARCHAR}
		</if>
		<if test="customerNo != null and customerNo!=''">
			, CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
		</if>
		<if test="companyCardType != null and companyCardType!=''">
			, COMPANY_CARD_TYPE = #{companyCardType,jdbcType=VARCHAR}
		</if>
		<if test="payType != null and payType!=''">
			, PAY_TYPE = #{payType,jdbcType=VARCHAR}
		</if>
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
	</update>

	<update id="batchUpdate" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO">
		<foreach collection="paymentInfoList" item="item" separator=";">
			update clm_payment_info
			set updated_date = now(),
			updated_by = #{item.updatedBy}
			<if test="item.customerNo != null and item.customerNo!=''">
				, customer_no = #{item.customerNo}
			</if>
			WHERE report_no = #{item.reportNo}
			AND case_times = #{item.caseTimes}
			<if test="item.idClmPaymentInfo != null">
				and id_clm_payment_info = #{item.idClmPaymentInfo}
			</if>
		</foreach>
	</update>

	<select id="getPaymentInfoForOnly" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO" resultType="int">
		SELECT
		count(1)
		FROM CLM_PAYMENT_INFO
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND PAYMENT_INFO_STATUS = '0'
		AND BANK_ACCOUNT_ATTRIBUTE = '1'
		AND CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR}
		AND CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR}
		AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
        and pay_type = #{payType,jdbcType=VARCHAR}
        AND PAYMENT_USAGE = #{paymentUsage,jdbcType=VARCHAR}
        AND CLIENT_BANK_ACCOUNT = #{clientBankAccount,jdbcType=VARCHAR}
		<if test="idClmPaymentInfo != null">
			AND ID_CLM_PAYMENT_INFO != #{idClmPaymentInfo,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getPaymentInfoForComOnly" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO" resultType="int">
		SELECT
		count(1)
		FROM CLM_PAYMENT_INFO
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND PAYMENT_INFO_STATUS = '0'
		AND BANK_ACCOUNT_ATTRIBUTE = '0'
		AND ORGANIZE_CODE = #{organizeCode,jdbcType=VARCHAR}
		AND CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
        and COMPANY_CARD_TYPE = #{companyCardType,jdbcType=VARCHAR}
        and pay_type = #{payType,jdbcType=VARCHAR}
        AND PAYMENT_USAGE = #{paymentUsage,jdbcType=VARCHAR}
        AND CLIENT_BANK_ACCOUNT = #{clientBankAccount,jdbcType=VARCHAR}
		<if test="idClmPaymentInfo != null">
			AND ID_CLM_PAYMENT_INFO != #{idClmPaymentInfo,jdbcType=VARCHAR}
		</if>
	</select>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLM_PAYMENT_INFO (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_CLM_PAYMENT_INFO,
			REPORT_NO,
			CASE_TIMES,
			SUB_TIMES,
			PAYMENT_INFO_TYPE,
			COLLECT_PAY_APPROACH,
			BANK_ACCOUNT_ATTRIBUTE,
			CITY_NAME,
			CLIENT_BANK_ACCOUNT,
			CLIENT_BANK_CODE,
			CLIENT_BANK_NAME,
			CLIENT_CERTIFICATE_NO,
			CLIENT_CERTIFICATE_TYPE,
			CLIENT_MOBILE,
			CLIENT_NAME,
			CLIENT_TYPE,
			PROVINCE_NAME,
			REGION_CODE,
			PAYMENT_INFO_STATUS,
			VALID_BEGIN_DATE,
			VALID_END_DATE,
			BANK_DETAIL,
			CITY_CODE,
			REMARK,
			IS_MERGE_PAY,
			DOCUMENT_GROUP_ID,
			MIGRATE_FROM,
			COUNTER_PAYMENT_REASON,
			OTHER_REASON,
			DATA_SOURCE,
			ORGANIZE_CODE,
			IS_TAXPAYER,
			IS_AGREE_IMMEDIATELY_PAY,
			PAYMENT_USAGE,
			ARCHIVE_DATE,
			MERGE_STRATEGY,
			CLIENT_RELATION,
			BANK_DETAIL_CODE,
		    AGENCY_TYPE,
		    CUSTOMER_NO,
		    PAY_TYPE,
		    OPEN_ID
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			MD5(UUID()),
			REPORT_NO,
			#{reopenCaseTimes},
			SUB_TIMES,
			PAYMENT_INFO_TYPE,
			COLLECT_PAY_APPROACH,
			BANK_ACCOUNT_ATTRIBUTE,
			CITY_NAME,
			CLIENT_BANK_ACCOUNT,
			CLIENT_BANK_CODE,
			CLIENT_BANK_NAME,
			CLIENT_CERTIFICATE_NO,
			CLIENT_CERTIFICATE_TYPE,
			CLIENT_MOBILE,
			CLIENT_NAME,
			CLIENT_TYPE,
			PROVINCE_NAME,
			REGION_CODE,
			PAYMENT_INFO_STATUS,
			VALID_BEGIN_DATE,
			VALID_END_DATE,
			BANK_DETAIL,
			CITY_CODE,
			REMARK,
			IS_MERGE_PAY,
			DOCUMENT_GROUP_ID,
			MIGRATE_FROM,
			COUNTER_PAYMENT_REASON,
			OTHER_REASON,
			DATA_SOURCE,
			ORGANIZE_CODE,
			IS_TAXPAYER,
			IS_AGREE_IMMEDIATELY_PAY,
			PAYMENT_USAGE,
			NOW(),
			MERGE_STRATEGY,
			CLIENT_RELATION,
			BANK_DETAIL_CODE,
		    AGENCY_TYPE,
		    CUSTOMER_NO,
		    PAY_TYPE,
		    OPEN_ID
		FROM CLM_PAYMENT_INFO
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND PAYMENT_INFO_STATUS != '1'
	</insert>

	<select id="queryCustomerNoToSupplementedFromInfo" resultMap="resultDTO">
		select
		id_clm_payment_info,
		report_no,
		case_times,
		client_certificate_no,
		client_certificate_type,
		bank_account_attribute,
		client_mobile,
		client_name,
		customer_no
		from clm_payment_info
		where customer_no is null
	</select>
	<!--  查询公司账户信息 -->
	<select id="getCompanyPaymentInfoByNameAndTypeAndCardNo" parameterType="com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO" resultMap="resultDTO">
		select <include refid="Base_Column_List"></include>
		from clm_payment_info
		where
		CLIENT_NAME =#{clientName}
		and BANK_ACCOUNT_ATTRIBUTE='0'
		and PAYMENT_INFO_STATUS='0' limit 1

	</select>
	<!-- 查询个人 -->
	<select id="getIdvPaymentInfoByNameAndTypeAndCardNo" parameterType="com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO" resultMap="resultDTO">
		select <include refid="Base_Column_List"></include>
		from clm_payment_info cpi
		where
			cpi.CLIENT_NAME =#{clientName}
			and cpi.CLIENT_CERTIFICATE_TYPE =#{clientCertificateType}
			and cpi.CLIENT_CERTIFICATE_NO =#{clientCertificateNo}
			and cpi.BANK_ACCOUNT_ATTRIBUTE='1'
			and PAYMENT_INFO_STATUS='0' limit 1

	</select>
	<select id="getPaymentEffInfoByReportNo" parameterType="java.lang.String" resultType="java.lang.Integer">
		select count(1) from clm_payment_info where  report_no=#{reportNo}
		and PAYMENT_INFO_STATUS='0';
	</select>

	<update id="updateFullPaymentInfoById" parameterType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO">
		UPDATE CLM_PAYMENT_INFO
		SET
			UPDATED_DATE = now(),
			UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
			REPORT_NO = #{reportNo,jdbcType=VARCHAR},
			CASE_TIMES = #{caseTimes,jdbcType=NUMERIC},
			SUB_TIMES = #{subTimes,jdbcType=NUMERIC},
			ID_CLM_CHANNEL_PROCESS = #{idClmChannelProcess,jdbcType=VARCHAR},
			PAYMENT_INFO_TYPE = #{paymentInfoType,jdbcType=VARCHAR},
			COLLECT_PAY_APPROACH = #{collectPayApproach,jdbcType=VARCHAR},
			BANK_ACCOUNT_ATTRIBUTE = #{bankAccountAttribute,jdbcType=VARCHAR},
			CITY_NAME = #{cityName,jdbcType=VARCHAR},
			CLIENT_BANK_ACCOUNT = #{clientBankAccount,jdbcType=VARCHAR},
			CLIENT_BANK_CODE = #{clientBankCode,jdbcType=VARCHAR},
			CLIENT_BANK_NAME = #{clientBankName,jdbcType=VARCHAR},
			CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR},
			CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR},
			CLIENT_MOBILE = #{clientMobile,jdbcType=VARCHAR},
			CLIENT_NAME = #{clientName,jdbcType=VARCHAR},
			CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},
			PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
			REGION_CODE = #{regionCode,jdbcType=VARCHAR},
			PAYMENT_INFO_STATUS = #{paymentInfoStatus,jdbcType=VARCHAR},
			VALID_BEGIN_DATE = #{validBeginDate,jdbcType=TIMESTAMP},
			VALID_END_DATE = #{validEndDate,jdbcType=TIMESTAMP},
			REMARK = #{remark,jdbcType=VARCHAR},
			IS_MERGE_PAY = #{isMergePay,jdbcType=VARCHAR},
			DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR},
			MIGRATE_FROM = #{migrateFrom,jdbcType=VARCHAR},
			COUNTER_PAYMENT_REASON = #{counterPaymentReason,jdbcType=VARCHAR},
			OTHER_REASON = #{otherReason,jdbcType=VARCHAR},
			DATA_SOURCE = #{dataSource,jdbcType=VARCHAR},
			ORGANIZE_CODE = #{organizeCode,jdbcType=VARCHAR},
			IS_TAXPAYER = #{isTaxpayer,jdbcType=VARCHAR},
			IS_AGREE_IMMEDIATELY_PAY = #{isAgreeImmediatelyPay,jdbcType=VARCHAR},
			PAYMENT_USAGE = #{paymentUsage,jdbcType=VARCHAR},
			MERGE_STRATEGY = #{mergeStrategy,jdbcType=VARCHAR},
			BANK_DETAIL = #{bankDetail,jdbcType=VARCHAR},
			CITY_CODE = #{cityCode,jdbcType=VARCHAR},
			CLIENT_RELATION = #{clientRelation,jdbcType=VARCHAR},
			BANK_DETAIL_CODE = #{bankDetailCode,jdbcType=VARCHAR},
			CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
			AGENCY_TYPE = #{agencyType,jdbcType=VARCHAR},
			COMPANY_CARD_TYPE = #{companyCardType,jdbcType=VARCHAR},
			PAY_TYPE = #{payType,jdbcType=VARCHAR},
			OPEN_ID = #{openId,jdbcType=VARCHAR}
		WHERE ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo,jdbcType=VARCHAR}
	</update>
	<select id="getPaymentInfoDtoByReportNo" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO">
		select * from clm_payment_info cpi where REPORT_NO =#{reportNo} and CASE_TIMES =#{caseTimes} and PAYMENT_USAGE ='P2';
	</select>

	<select id="selectPaymentInfoByReportNo" resultType="com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO">
		select
		<include refid="Base_Column_List"/>
		from clm_payment_info
		where report_no=#{reportNo}
		and PAYMENT_INFO_STATUS='0'
	</select>
	<update id="updateReportNo" parameterType="com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO" >
		UPDATE CLM_PAYMENT_INFO
		set UPDATED_DATE = now(),
		REPORT_NO = #{reportNo},
		UPDATED_BY = #{updatedBy}
		where ID_CLM_PAYMENT_INFO=#{idClmPaymentInfo}
	</update>
</mapper>