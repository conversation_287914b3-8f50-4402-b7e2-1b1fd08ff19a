<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.ClmsItemLossMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.duty.ClmsItemLoss">
        <id column="id" property="id" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="serial_no" property="serialNo" />
        <result column="loss_class" property="lossClass" />
        <result column="item_id" property="itemId" />
        <result column="prop_type" property="propType" />
        <result column="prop_name" property="propName" />
        <result column="guarantee_type" property="guaranteeType" />
        <result column="fee_type" property="feeType" />
        <result column="item_desc" property="itemDesc" />
        <result column="pay_info" property="payInfo" />
        <result column="report_amount" property="reportAmount" />
        <result column="loss_amount" property="lossAmount" />
        <result column="salvage_value" property="salvageValue" />
        <result column="ins_rate" property="insRate" />
        <result column="duty_detail_code" property="dutyDetailCode" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <select id="getClmsItemLossService" resultMap="BaseResultMap">
        SELECT id, report_no, case_times, serial_no, loss_class, item_id, prop_type,
        prop_name, guarantee_type, fee_type, item_desc, pay_info, report_amount, loss_amount,
        salvage_value, ins_rate, duty_detail_code, remark
        FROM clms_item_loss
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>

    <insert id="saveClmsItemLoss" parameterType="com.paic.ncbs.claim.model.dto.duty.ClmsItemLoss">
        INSERT INTO clms_item_loss (
        id, report_no, case_times, serial_no, loss_class, item_id, prop_type,
        prop_name, guarantee_type, fee_type, item_desc, pay_info, report_amount, loss_amount,
        salvage_value, ins_rate, duty_detail_code, remark, created_by, sys_ctime,
        updated_by, sys_utime,batch_no
        ) VALUES (
        replace(UUID(), '-', ''), #{reportNo}, #{caseTimes}, #{serialNo}, #{lossClass}, #{itemId}, #{propType},
        #{propName}, #{guaranteeType}, #{feeType}, #{itemDesc}, #{payInfo}, #{reportAmount}, #{lossAmount},
        #{salvageValue}, #{insRate}, #{dutyDetailCode}, #{remark}, #{createdBy}, NOW(),
        #{updatedBy}, NOW(),#{barchNo}
        )
    </insert>

    <update id="updateClmsItemLoss" parameterType="com.paic.ncbs.claim.model.dto.duty.ClmsItemLoss">
        UPDATE clms_item_loss
        <set>
            duty_detail_code = #{dutyDetailCode},
            updated_by = #{updatedBy},
            sys_utime = NOW(),
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteByCondition" >
        DELETE FROM clms_item_loss
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </delete>

    <delete id="deleteById" parameterType="string">
        DELETE FROM clms_item_loss WHERE id = #{id}
    </delete>

</mapper>
