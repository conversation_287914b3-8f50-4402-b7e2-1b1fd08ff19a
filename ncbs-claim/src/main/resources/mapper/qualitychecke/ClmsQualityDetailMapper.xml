<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result column="serial_no" property="serialNo" />
        <result column="case_times" property="caseTimes" />
        <result column="inspn_stage" property="inspnStage" />
        <result column="inspn_standard" property="inspnStandard" />
        <result column="error_level" property="errorLevel" />
        <result column="diff_amount" property="diffAmount" />
        <result column="loss_amount" property="lossAmount" />
        <result column="report_no" property="reportNo" />
        <result column="policy_no" property="policyNo" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <!-- 根据ID查询质检详情 -->
    <select id="selectQualityDetailById" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM clms_quality_detail WHERE id = #{id}
    </select>

    <!-- 查询所有质检详情 -->
    <select id="selectAllQualityDetails" resultMap="BaseResultMap">
        SELECT * FROM clms_quality_detail
    </select>

    <!-- 根据条件查询质检详情 -->
    <select id="selectQualityDetailByCondition" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail" resultMap="BaseResultMap">
        SELECT * FROM clms_quality_detail
        <where>
            <if test="serialNo != null and serialNo != ''">AND serial_no = #{serialNo}</if>
            <if test="caseTimes != null">AND case_times = #{caseTimes}</if>
            <if test="inspnStage != null and inspnStage != ''">AND inspn_stage = #{inspnStage}</if>
            <if test="reportNo != null and reportNo != ''">AND report_no = #{reportNo}</if>
            <if test="policyNo != null and policyNo != ''">AND policy_no = #{policyNo}</if>
        </where>
    </select>

    <!-- 根据serialNo查询质检详情列表 -->
    <select id="selectQualityDetailsBySerialNo" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM clms_quality_detail WHERE serial_no = #{serialNo}
    </select>

    <!-- 插入质检详情 -->
    <insert id="insertQualityDetail" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail">
        INSERT INTO clms_quality_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            serial_no,
            case_times,
            inspn_stage,
            inspn_standard,
            error_level,
            diff_amount,
            loss_amount,
            report_no,
            policy_no,
            created_by,
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            updated_by,
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            #{serialNo,jdbcType=VARCHAR},
            #{caseTimes,jdbcType=NUMERIC},
            #{inspnStage,jdbcType=VARCHAR},
            #{inspnStandard,jdbcType=VARCHAR},
            #{errorLevel,jdbcType=VARCHAR},
            #{diffAmount,jdbcType=NUMERIC},
            #{lossAmount,jdbcType=NUMERIC},
            #{reportNo,jdbcType=VARCHAR},
            #{policyNo,jdbcType=VARCHAR},
            #{createdBy,jdbcType=VARCHAR},
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            #{updatedBy,jdbcType=VARCHAR},
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 更新质检详情 -->
    <update id="updateQualityDetail" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail">
        UPDATE clms_quality_detail
        <set>
            <if test="serialNo != null">serial_no = #{serialNo},</if>
            <if test="caseTimes != null">case_times = #{caseTimes},</if>
            <if test="inspnStage != null">inspn_stage = #{inspnStage},</if>
            <if test="inspnStandard != null">inspn_standard = #{inspnStandard},</if>
            <if test="errorLevel != null">error_level = #{errorLevel},</if>
            <if test="diffAmount != null">diff_amount = #{diffAmount},</if>
            <if test="lossAmount != null">loss_amount = #{lossAmount},</if>
            <if test="reportNo != null">report_no = #{reportNo},</if>
            <if test="policyNo != null">policy_no = #{policyNo},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除质检详情 -->
    <delete id="deleteQualityDetailById" parameterType="string">
    DELETE FROM clms_quality_detail WHERE id = #{id}
    </delete>

    <!-- 根据serialNo删除质检详情 -->
    <delete id="deleteQualityDetailsBySerialNo" parameterType="string">
        DELETE FROM clms_quality_detail WHERE serial_no = #{serialNo}
    </delete>
</mapper>
