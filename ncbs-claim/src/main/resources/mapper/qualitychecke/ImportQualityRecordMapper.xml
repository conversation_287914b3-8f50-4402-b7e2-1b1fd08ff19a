<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.qualitychecke.ImportQualityRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.qualitychecke.ImportQualityRecord">
        <id column="id" property="id"/>
        <result column="batch_no" property="batchNo"/>
        <result column="report_no" property="reportNo"/>
        <result column="emp_no" property="qinitiator"/>
        <result column="import_time" property="importTime"/>
        <result column="status" property="taskStatus"/>
        <result column="fail_reason" property="failReason"/>
        <result column="created_by" property="createdBy"/>
        <result column="sys_ctime" property="sysCtime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="sys_utime" property="sysUtime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, batch_no, report_no, emp_no, import_time, status,  fail_reason, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据批次号和创建人查询质检导入记录 -->
    <select id="selectQualityInfoByBatch" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM import_quality_record
        <where>
        <if test="batchno != null and batchno != ''">AND batch_no = #{batchno}</if>
        <if test="qinitiator != null and qinitiator != ''">AND emp_no = #{qinitiator}</if>
        </where>
    </select>

    <select id="selectMaxBatchNoByDate" parameterType="string" resultType="string">
        SELECT MAX(batch_no) FROM import_quality_record
        WHERE batch_no LIKE #{batchNoPrefix}
    </select>

    <!-- 插入质检导入记录 -->
    <insert id="qualityRecordInsert" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ImportQualityRecord">
        INSERT INTO import_quality_record (
        id,
        batch_no,
        report_no,
        emp_no,
        import_time,
        status,
        fail_reason,
        created_by,
        updated_by
        ) VALUES (
        #{id},
        #{batchNo},
        #{reportNo},
        #{qinitiator},
        #{importTime},
        #{taskStatus},
        #{failReason},
        #{createdBy},
        #{updatedBy}
        )
    </insert>

    <!-- 插入质检导入记录 -->
    <insert id="qualityRecordBatchInsert"  parameterType="java.util.List">
        INSERT INTO import_quality_record (
            id,
            batch_no,
            report_no,
            emp_no,
            import_time,
            status,
            fail_reason,
            created_by,
            updated_by
        )
        <foreach collection="list" item="record" index="index" separator=" union all ">
            SELECT
            #{record.id},
            #{record.batchNo},
            #{record.reportNo},
            #{record.qinitiator},
            #{record.importTime},
            #{record.taskStatus},
            #{record.failReason},
            #{record.createdBy},
            #{record.updatedBy}
        </foreach>
    </insert>
</mapper>
