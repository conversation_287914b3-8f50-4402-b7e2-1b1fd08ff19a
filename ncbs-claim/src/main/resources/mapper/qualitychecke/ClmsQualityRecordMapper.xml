<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityRecord">
        <id column="id" property="id"/>
        <result column="quality_id" property="qualityId"/>
        <result column="handler" property="handler"/>
        <result column="process_time" property="processTime"/>
        <result column="operate_type" property="node"/>
        <result column="locus_idea" property="locusIdea"/>
        <result column="locus_detail" property="locusDetail"/>
        <result column="report_no" property="reportNo"/>
        <result column="case_times" property="caseTimes"/>
        <result column="policy_no" property="policyNo"/>
        <result column="created_by" property="createdBy"/>
        <result column="sys_ctime" property="sysCtime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="sys_utime" property="sysUtime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, quality_id, handler, process_time, operate_type, locus_idea, locus_detail,
        report_no, case_times, policy_no, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据报案号和赔付次数查询质检轨迹记录 -->
    <select id="selectByReportNoAndCaseTimes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM clms_quality_record
        WHERE report_no = #{reportNo} AND case_times = #{caseTimes} AND quality_id = #{qualityId}
        ORDER BY sys_ctime ASC
    </select>

    <!-- 插入质检轨迹记录 -->
    <insert id="insertrecord" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityRecord">
        INSERT INTO clms_quality_record (
        id,
        quality_id,
        handler,
        process_time,
        operate_type,
        locus_idea,
        locus_detail,
        report_no,
        case_times,
        policy_no,
        created_by,
        updated_by
        ) VALUES (
        #{id},
        #{qualityId},
        #{handler},
        #{processTime},
        #{node},
        #{locusIdea},
        #{locusDetail},
        #{reportNo},
        #{caseTimes},
        #{policyNo},
        #{createdBy},
        #{updatedBy}
        )
    </insert>
</mapper>
