<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="qualityResultMap" type="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result column="serial_no" property="serialNo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="regist_no" property="registNo" />
        <result column="handle_com" property="handleCom" />
        <result column="product_name" property="productName" />
        <result column="close_date" property="closeDate" />
        <result column="policy_no" property="policyNo" />
        <result column="case_nature" property="caseNature" />
        <result column="insured_person" property="insuredPerson" />
        <result column="case_amount" property="caseAmount" />
        <result column="case_creator" property="caseCreator" />
        <result column="investigator" property="investigator" />
        <result column="adjuster" property="adjuster" />
        <result column="claim_approver" property="claimApprover" />
        <result column="com_code" property="comCode" />
        <result column="claim_org" property="claimOrg" />
        <result column="qiser_no" property="qiserNo" />
        <result column="qinitiator" property="qinitiator" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="qinspector" property="qinspector" />
        <result column="quality_result" property="qualityResult" />
        <result column="is_end" property="isEnd" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="task_status" property="taskStatus" />
        <result column="batch_no" property="batchNo" />
        <result column="import_time" property="importTime" />
        <result column="opinion_detail" property="opinionDetail" />
        <result column="operator_tpa" property="operatorTpa" />
        <result column="process_time" property="processTime" />
        <result column="handler" property="handler" />
        <result column="node" property="node" />
        <result column="locus_idea" property="locusIdea" />
        <result column="locus_detail" property="locusDetail" />
        <result column="approval_opinion" property="approvalOpinion" />
        <result column="approval_desc" property="approvalDesc" />
        <result column="atransfer_person" property="atransferPerson" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="qualityType" property="qualityType" />
        <result column="isglobal" property="isglobal" />
    </resultMap>

    <!-- 质检信息及详情联合查询结果映射 -->
    <resultMap id="qualityInfoDetailResultMap" type="com.paic.ncbs.claim.dao.entity.qualitychecke.QualityInfoDetailVO">
        <association property="qualityInfo" javaType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo">
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result column="serial_no" property="serialNo" />
            <result column="report_no" property="reportNo" />
            <result column="case_times" property="caseTimes" />
            <result column="regist_no" property="registNo" />
            <result column="handle_com" property="handleCom" />
            <result column="product_name" property="productName" />
            <result column="close_date" property="closeDate" />
            <result column="policy_no" property="policyNo" />
            <result column="case_nature" property="caseNature" />
            <result column="insured_person" property="insuredPerson" />
            <result column="case_amount" property="caseAmount" />
            <result column="case_creator" property="caseCreator" />
            <result column="investigator" property="investigator" />
            <result column="adjuster" property="adjuster" />
            <result column="claim_approver" property="claimApprover" />
            <result column="com_code" property="comCode" />
            <result column="claim_org" property="claimOrg" />
            <result column="qiser_no" property="qiserNo" />
            <result column="qinitiator" property="qinitiator" />
            <result column="start_time" property="startTime" />
            <result column="end_time" property="endTime" />
            <result column="qinspector" property="qinspector" />
            <result column="quality_result" property="qualityResult" />
            <result column="is_end" property="isEnd" />
            <result column="payment_amount" property="paymentAmount" />
            <result column="task_status" property="taskStatus" />
            <result column="batch_no" property="batchNo" />
            <result column="import_time" property="importTime" />
            <result column="opinion_detail" property="opinionDetail" />
            <result column="operator_tpa" property="operatorTpa" />
            <result column="process_time" property="processTime" />
            <result column="handler" property="handler" />
            <result column="qualityType" property="qualityType" />
            <result column="node" property="node" />
            <result column="locus_idea" property="locusIdea" />
            <result column="locus_detail" property="locusDetail" />
            <result column="approval_opinion" property="approvalOpinion" />
            <result column="approval_desc" property="approvalDesc" />
            <result column="atransfer_person" property="atransferPerson" />
            <result column="created_by" property="createdBy" />
            <result column="sys_ctime" property="sysCtime" />
            <result column="updated_by" property="updatedBy" />
            <result column="sys_utime" property="sysUtime" />
            <result column="isglobal" property="isglobal" />
        </association>
        <collection property="qualityDetails" ofType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail"
                    column="{reportNo=report_no,caseTimes=case_times,serialNo=id}"
                    select="selectQualityDetailsByReportNoAndCaseTimesAndSerialNo">
            <result property="id" column="id"/>
            <result property="serialNo" column="serial_no"/>
            <result property="caseTimes" column="case_times"/>
            <result property="inspnStage" column="inspn_stage"/>
            <result property="inspnStandard" column="inspn_standard"/>
            <result property="errorLevel" column="error_level"/>
            <result property="diffAmount" column="diff_amount"/>
            <result property="lossAmount" column="loss_amount"/>
            <result property="reportNo" column="report_no"/>
            <result property="policyNo" column="policy_no"/>
            <result property="createdBy" column="created_by"/>
            <result property="sysCtime" column="sys_ctime"/>
            <result property="updatedBy" column="updated_by"/>
            <result property="sysUtime" column="sys_utime"/>
        </collection>
    </resultMap>
    <!-- 质检信息及详情VO映射结果 -->
    <resultMap id="qualityInfoWithDetailResultMap" type="com.paic.ncbs.claim.dao.entity.qualitychecke.QualityInfoWithDetailVO">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result column="serial_no" property="serialNo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="regist_no" property="registNo" />
        <result column="handle_com" property="handleCom" />
        <result column="product_name" property="productName" />
        <result column="close_date" property="closeDate" />
        <result column="policy_no" property="policyNo" />
        <result column="case_nature" property="caseNature" />
        <result column="insured_person" property="insuredPerson" />
        <result column="case_amount" property="caseAmount" />
        <result column="case_creator" property="caseCreator" />
        <result column="investigator" property="investigator" />
        <result column="adjuster" property="adjuster" />
        <result column="claim_approver" property="claimApprover" />
        <result column="com_code" property="comCode" />
        <result column="claim_org" property="claimOrg" />
        <result column="qiser_no" property="qiserNo" />
        <result column="qinitiator" property="qinitiator" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="qinspector" property="qinspector" />
        <result column="quality_result" property="qualityResult" />
        <result column="is_end" property="isEnd" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="task_status" property="taskStatus" />
        <result column="batch_no" property="batchNo" />
        <result column="import_time" property="importTime" />
        <result column="opinion_detail" property="opinionDetail" />
        <result column="operator_tpa" property="operatorTpa" />
        <result column="process_time" property="processTime" />
        <result column="handler" property="handler" />
        <result column="node" property="node" />
        <result column="locus_idea" property="locusIdea" />
        <result column="locus_detail" property="locusDetail" />
        <result column="approval_opinion" property="approvalOpinion" />
        <result column="approval_desc" property="approvalDesc" />
        <result column="atransfer_person" property="atransferPerson" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="qualityType" property="qualityType" />
        <!-- 添加质检详情字段 -->
        <result column="inspn_stage" property="inspnStage" />
        <result column="inspn_standard" property="inspnStandard" />
        <result column="error_level" property="errorLevel" />
        <result column="diff_amount" property="diffAmount" />
        <result column="loss_amount" property="lossAmount" />
        <result column="isglobal" property="isglobal" />
    </resultMap>
    <!-- 根据ID查询质检信息 -->
    <select id="selectQualityInfoById" parameterType="java.lang.String" resultMap="qualityResultMap">
        SELECT serial_no, isglobal,report_no, case_times, regist_no, handle_com, product_name, close_date,
        policy_no, case_nature, insured_person, case_amount, case_creator, investigator,
        adjuster, claim_approver, com_code, claim_org, qiser_no, qinitiator, start_time,
        end_time, qinspector, quality_result, is_end, payment_amount, task_status,
        batch_no, import_time, opinion_detail, operator_tpa, qualityType ,process_time, handler,
        node, locus_idea, locus_detail, approval_opinion, approval_desc, atransfer_person,
        created_by, sys_ctime, updated_by, sys_utime FROM clms_quality_info WHERE id = #{id,jdbcType=VARCHAR}
    </select>

    <!-- 查询当前账号所有质检信息 -->
    <select id="selectAllQualityInfo" resultMap="qualityResultMap" parameterType="string">
        SELECT serial_no, isglobal,report_no, case_times, regist_no, handle_com, product_name, close_date,
        policy_no, case_nature, insured_person, case_amount, case_creator, investigator,
        adjuster, claim_approver, com_code, claim_org, qiser_no, qinitiator, start_time,
        end_time, qinspector, quality_result, is_end, payment_amount, task_status,
        batch_no, import_time, opinion_detail, operator_tpa, process_time, handler,
        node, locus_idea, qualityType,locus_detail, approval_opinion, approval_desc, atransfer_person,
        created_by, sys_ctime, updated_by, sys_utime FROM clms_quality_info WHERE qinspector = #{qinspector,jdbcType=VARCHAR}
    </select>

    <!-- 根据条件查询质检信息 -->
    <select id="selectQualityInfoByCondition" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo" resultMap="qualityResultMap">
        SELECT id, serial_no, isglobal, report_no, case_times, regist_no, handle_com, product_name, close_date,
        policy_no, case_nature, insured_person, case_amount, case_creator, investigator,
        adjuster, claim_approver, com_code, claim_org, qiser_no, qinitiator, start_time,
        end_time, qinspector, qualityType, quality_result, is_end, payment_amount, task_status,
        batch_no, import_time, opinion_detail, operator_tpa, process_time, handler,
        node, locus_idea, locus_detail, approval_opinion, approval_desc, atransfer_person,
        created_by, sys_ctime, updated_by, sys_utime FROM clms_quality_info
        <where>
            <if test="id != null and id != ''">AND id = #{id}</if>
            <if test="reportNo != null and reportNo != ''">AND report_no = #{reportNo}</if>
            <if test="caseTimes != null">AND case_times = #{caseTimes}</if>
            <if test="qinitiator != null and qinitiator != ''">AND qinitiator = #{qinitiator}</if>
            <if test="registNo != null and registNo != ''">AND regist_no = #{registNo}</if>
            <if test="policyNo != null and policyNo != ''">AND policy_no = #{policyNo}</if>
            <if test="insuredPerson != null and insuredPerson != ''">AND insured_person = #{insuredPerson}</if>
            <if test="qualityResult != null and qualityResult != ''">AND quality_result = #{qualityResult}</if>
            <if test="taskStatus != null and taskStatus != ''">AND task_status = #{taskStatus}</if>
        </where>
        ORDER BY start_time ASC
    </select>

    <!-- 更新质检信息 -->
    <update id="updateQualityInfo" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo">
        UPDATE clms_quality_info
        <set>
            <if test="handleCom != null">handle_com = #{handleCom},</if>
            <if test="isEnd != null and isEnd != ''">is_end = #{isEnd},</if>
            <if test="caseNature != null">case_nature = #{caseNature},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="opinionDetail != null">opinion_detail = #{opinionDetail},</if>
            <if test="operatorTpa != null">operator_tpa = #{operatorTpa},</if>
            <if test="node != null">node = #{node},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="qualityType != null">qualityType = #{qualityType}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除质检信息 -->
    <delete id="deleteQualityInfoById" parameterType="java.lang.String">
        DELETE FROM clms_quality_info WHERE id = #{id,jdbcType=VARCHAR}
    </delete>


    <!-- 插入质检信息 -->
    <insert id="insertQualityInfo" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo">
        INSERT INTO clms_quality_info (
        id,serial_no,isglobal, report_no, case_times, regist_no, handle_com, product_name, close_date,
        policy_no, case_nature, insured_person, case_amount, case_creator, investigator,
        adjuster, claim_approver, com_code, claim_org, qiser_no, qinitiator, start_time,
        end_time, qinspector, quality_result, is_end, payment_amount, task_status,
        batch_no, import_time, opinion_detail, qualityType, operator_tpa, process_time, handler,
        node, locus_idea, locus_detail, approval_opinion, approval_desc, atransfer_person,
        created_by, updated_by
        ) VALUES (
        #{id},
        #{serialNo}, #{isglobal}, #{reportNo}, #{caseTimes}, #{registNo}, #{handleCom}, #{productName}, #{closeDate},
        #{policyNo}, #{caseNature}, #{insuredPerson}, #{caseAmount}, #{caseCreator}, #{investigator},
        #{adjuster}, #{claimApprover}, #{comCode}, #{claimOrg}, #{qiserNo}, #{qinitiator}, #{startTime},
        #{endTime}, #{qinspector}, #{qualityResult}, #{isEnd}, #{paymentAmount}, #{taskStatus},
        #{batchNo}, #{importTime}, #{opinionDetail}, #{qualityType}, #{operatorTpa}, #{processTime}, #{handler},
        #{node}, #{locusIdea}, #{locusDetail}, #{approvalOpinion}, #{approvalDesc}, #{atransferPerson},
        #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 查询表中最大的serialNo -->
    <select id="selectMaxSerialNo" resultType="string">
        SELECT COALESCE(MAX(CAST(serial_no AS UNSIGNED)), 0) FROM clms_quality_info
        WHERE report_no = #{reportNo} AND case_times = #{caseTimes}
    </select>

    <!-- 根据报案号、赔付次数和ID关联查询质检信息及详情 -->
    <select id="selectQualityInfoDetailByCondition" resultMap="qualityInfoDetailResultMap">
        SELECT
        info.id AS id,
        info.serial_no AS serial_no,
        info.isglobal AS isglobal,
        info.report_no AS report_no,
        info.case_times AS case_times,
        info.regist_no AS regist_no,
        info.handle_com AS handle_com,
        info.product_name AS product_name,
        info.close_date AS close_date,
        info.policy_no AS policy_no,
        info.case_nature AS case_nature,
        info.insured_person AS insured_person,
        info.case_amount AS case_amount,
        info.case_creator AS case_creator,
        info.investigator AS investigator,
        info.adjuster AS adjuster,
        info.claim_approver AS claim_approver,
        info.com_code AS com_code,
        info.claim_org AS claim_org,
        info.qiser_no AS qiser_no,
        info.qinitiator AS qinitiator,
        info.start_time AS start_time,
        info.end_time AS end_time,
        info.qinspector AS qinspector,
        info.quality_result AS quality_result,
        info.is_end AS is_end,
        info.payment_amount AS payment_amount,
        info.task_status AS task_status,
        info.batch_no AS batch_no,
        info.import_time AS import_time,
        info.opinion_detail AS opinion_detail,
        info.operator_tpa AS operator_tpa,
        info.process_time AS process_time,
        info.handler AS handler,
        info.node AS node,
        info.qualityType AS qualityType,
        info.locus_idea AS locus_idea,
        info.locus_detail AS locus_detail,
        info.approval_opinion AS approval_opinion,
        info.approval_desc AS approval_desc,
        info.atransfer_person AS atransfer_person,
        info.created_by AS created_by,
        info.sys_ctime AS sys_ctime,
        info.updated_by AS updated_by,
        info.sys_utime AS sys_utime
        FROM clms_quality_info info
        WHERE info.report_no = #{reportNo}
        AND info.case_times = #{caseTimes}
        AND info.id = #{id}
        AND info.is_end = '0'
    </select>

    <!-- 根据报案号、赔付次数和序列号查询质检详情 -->
    <select id="selectQualityDetailsByReportNoAndCaseTimesAndSerialNo" resultType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityDetail">
        SELECT
        id,
        serial_no AS serialNo,
        case_times AS caseTimes,
        inspn_stage AS inspnStage,
        inspn_standard AS inspnStandard,
        error_level AS errorLevel,
        diff_amount AS diffAmount,
        loss_amount AS lossAmount,
        report_no AS reportNo,
        policy_no AS policyNo,
        created_by AS createdBy,
        sys_ctime AS sysCtime,
        updated_by AS updatedBy,
        sys_utime AS sysUtime
        FROM clms_quality_detail
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        AND serial_no = #{serialNo}
    </select>

    <!-- 根据多种条件查询质检信息 -->
    <select id="selectQualityInfoByConditions" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.QualityQueryVO" resultMap="qualityInfoWithDetailResultMap">
        SELECT
        info.id, info.isglobal, info.serial_no, info.report_no, info.case_times, info.regist_no, info.handle_com, cpi.PRODUCT_NAME AS "product_name", info.close_date,
        info.policy_no, info.case_nature, info.insured_person, info.case_amount, info.case_creator, info.investigator,
        info.adjuster, info.claim_approver, info.com_code, info.claim_org, info.qiser_no, info.qinitiator, info.start_time,
        info.end_time, info.qinspector, info.quality_result, info.is_end,
        CASE
        WHEN cs.TOTAL_SETTLE_AMOUNT IS NOT NULL AND cs.TOTAL_SETTLE_AMOUNT > 0 THEN cs.TOTAL_SETTLE_AMOUNT
        ELSE cer.ESTIMATE_AMOUNT
        END AS payment_amount,
        info.task_status,
        info.batch_no, info.import_time, info.opinion_detail, info.qualityType, info.operator_tpa, info.process_time, info.handler,
        info.node, info.locus_idea, info.locus_detail, info.approval_opinion, info.approval_desc, info.atransfer_person,
        info.created_by, info.sys_ctime, info.updated_by, info.sys_utime,info.qualityType,
        detail.inspn_stage, detail.inspn_standard, detail.error_level, detail.diff_amount, detail.loss_amount
        FROM clms_quality_info info
        INNER JOIN clms_quality_detail detail ON info.id = detail.serial_no
        LEFT JOIN (
        SELECT REPORT_NO, CASE_TIMES, SUM(POLICY_SUM_PAY) AS TOTAL_SETTLE_AMOUNT
        FROM CLM_POLICY_PAY
        GROUP BY REPORT_NO, CASE_TIMES
        ) cs ON cs.REPORT_NO = info.REPORT_NO AND cs.CASE_TIMES = info.CASE_TIMES
        LEFT JOIN clms_estimate_record cer ON cer.report_no = info.report_no AND cer.case_times = info.case_times AND cer.ESTIMATE_TYPE = '02'
        LEFT JOIN clms_policy_info cpi ON cpi.REPORT_NO = info.report_no
        <where>
            <if test="handleCom != null and handleCom != ''">AND info.handle_com = #{handleCom}</if>
            <if test="minCloseDate != null">AND info.close_date &gt;= #{minCloseDate}</if>
            <if test="maxCloseDate != null">AND info.close_date &lt;= #{maxCloseDate}</if>
            <if test="qinspector != null and qinspector != ''">AND info.handler = #{qinspector}</if>
            <if test="minStartTime != null">AND info.start_time &gt;= #{minStartTime}</if>
            <if test="maxStartTime != null">AND info.start_time &lt;= #{maxStartTime}</if>
            <if test="minEndTime != null">AND info.end_time &gt;= #{minEndTime}</if>
            <if test="maxEndTime != null">AND info.end_time &lt;= #{maxEndTime}</if>
            <if test="taskStatus != null and taskStatus != ''">AND info.task_status = #{taskStatus}</if>
            <if test="productName != null and productName.length > 0">
                AND cpi.product_code IN
                <foreach collection="productName" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if  test="excludeProductName != null and excludeProductName.length > 0">
                AND cpi.product_code NOT IN
                <foreach collection="excludeProductName" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="reportNo != null and reportNo != ''">AND info.report_no = #{reportNo}</if>
            <if test="caseTimes != null">AND info.case_times = #{caseTimes}</if>
            <if test="id != null">AND info.id = #{id}</if>
            <if test="isProblemCase != null and isProblemCase != ''">
                <choose>
                    <when test='isProblemCase == "0"'>
                        AND info.case_nature IN ('1', '2')
                    </when>
                    <when test='isProblemCase == "1"'>
                        AND info.case_nature = '0'
                    </when>
                </choose>
            </if>
            <!-- 关联质检详情表查询差错等级 -->
            <if test="errorLevel != null and errorLevel != ''">
                AND detail.error_level = #{errorLevel}
            </if>
            <if test="minCaseAmount != null and minCaseAmount != ''">
                AND (
                CASE
                WHEN cs.TOTAL_SETTLE_AMOUNT IS NOT NULL AND cs.TOTAL_SETTLE_AMOUNT > 0 THEN cs.TOTAL_SETTLE_AMOUNT
                ELSE cer.ESTIMATE_AMOUNT
                END >= #{minCaseAmount}
                )
            </if>
            <if test="maxCaseAmount != null and maxCaseAmount != ''">
                AND (
                CASE
                WHEN cs.TOTAL_SETTLE_AMOUNT IS NOT NULL AND cs.TOTAL_SETTLE_AMOUNT > 0 THEN cs.TOTAL_SETTLE_AMOUNT
                ELSE cer.ESTIMATE_AMOUNT
                END &lt;= #{maxCaseAmount}
                )
            </if>
            AND info.qiser_no = (
            SELECT MAX(qiser_no)
            FROM clms_quality_info
            WHERE report_no = info.report_no and case_times = info.case_times
            )
        </where>
        ORDER BY info.start_time ASC
    </select>

    <!-- 根据报案号和赔付次数更新质检信息 -->
    <update id="updateQualityInfoByReportNoAndCaseTimes" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo">
        UPDATE clms_quality_info
        <set>
            <if test="qualityResult != null and qualityResult != ''">quality_result = #{qualityResult},</if>
            <if test="isEnd != null and isEnd != ''">is_end = #{isEnd},</if>
            <if test="taskStatus != null and taskStatus != ''">task_status = #{taskStatus},</if>
            <if test="node != null and node != ''">node = #{node},</if>
            <if test="approvalOpinion != null and approvalOpinion != ''">approval_opinion = #{approvalOpinion},</if>
            <if test="approvalDesc != null and approvalDesc != ''">approval_desc = #{approvalDesc},</if>
            <if test="atransferPerson != null and atransferPerson != ''">atransfer_person = #{atransferPerson},</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by = #{updatedBy},</if>
            <if test="qualityType != null and qualityType != ''">qualityType = #{qualityType},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="handler != null and handler != ''">handler = #{handler},</if>
            <if test="qinspector != null and qinspector != ''">qinspector = #{qinspector},</if>
            sys_utime = NOW()
        </set>
        WHERE report_no = #{reportNo} AND case_times = #{caseTimes} AND id = #{id}
    </update>

    <!-- 根据日期前缀查询最大批次号 -->
    <select id="selectMaxBatchNoByDate" parameterType="string" resultType="string">
        SELECT MAX(batch_no) FROM clms_quality_info
        WHERE batch_no LIKE #{batchNoPrefix}
    </select>

    <!-- 根据条件查询质检信息 -->
    <select id="selectQualityInfoByBatch" parameterType="com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo" resultMap="qualityResultMap">
        SELECT serial_no, isglobal, report_no, case_times, regist_no, handle_com, product_name, close_date,
        policy_no, case_nature, insured_person, case_amount, case_creator, investigator,
        adjuster, claim_approver, com_code, claim_org, qiser_no, qinitiator, start_time,
        end_time, qinspector, quality_result, is_end, payment_amount, task_status,
        batch_no, import_time, opinion_detail, qualityType, operator_tpa, process_time, handler,
        node, locus_idea, locus_detail, approval_opinion, approval_desc, atransfer_person,
        created_by, sys_ctime, updated_by, sys_utime FROM clms_quality_info
        <where>
            <if test="batchno != null and batchno != ''">AND batch_no = #{batchno}</if>
            <if test="batchno == null or batchno == ''">AND batch_no IS NOT NULL AND batch_no != ''</if>
            <if test="qinitiator != null and qinitiator != ''">AND qinitiator = #{qinitiator}</if>
        </where>
    </select>

    <!-- 根据报案号和赔付次数查询qiser_no最大的质检信息 -->
    <select id="selectQualityInfoWithMaxQiserNo" resultMap="qualityResultMap">
        SELECT serial_no, isglobal, report_no, case_times, regist_no, handle_com, product_name, close_date,
        policy_no, case_nature, insured_person, case_amount, case_creator, investigator,
        adjuster, claim_approver, com_code, claim_org, qiser_no, qinitiator, start_time,
        end_time, qinspector, quality_result, is_end, payment_amount, task_status,
        batch_no, import_time, opinion_detail, operator_tpa, qualityType ,process_time, handler,
        node, locus_idea, locus_detail, approval_opinion, approval_desc, atransfer_person,
        created_by, sys_ctime, updated_by, sys_utime FROM clms_quality_info
        WHERE report_no = #{reportNo} AND case_times = #{caseTimes}
        ORDER BY qiser_no DESC
        LIMIT 1
    </select>
</mapper>
