<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.doc.PrintMapper">



    <!--newcode-->
    <resultMap type="com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO" id="wholeCaseVO">
        <result property="reportNo" column="report_no"/>
        <result property="caseTimes" column="case_times"/>
        <result property="reportDate" column="report_date"/>
        <result property="accidentDate" column="accident_date"/>
        <result property="insuredName" column="name"/>
        <result property="clientType" column="client_type"/>
        <result property="clientTypeName" column="client_type_name"/>
        <result property="processStatusName" column="processStatusName"/>
        <result property="processStatus" column="process_status"/>
        <result property="indemnityConclusion" column="indemnity_conclusion"/>
        <result property="indemnityModel" column="indemnity_model"/>
        <result property="isRegister" column="IS_REGISTER"/>
        <result property="endCaseFlag" column="whole_case_status"/>
        <result property="endCaseDate" column="end_case_date"/>
        <result property="verifyUm" column="VERIFY_UM"/>
        <result property="fileId" column="file_id"/>
    </resultMap>

    <select id="getHistoryCase" resultMap="wholeCaseVO">
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        concat(a.indemnity_conclusion,IFNULL(a.indemnity_model,"")) indemnity_conclusion
        from
        clm_whole_case_base a,clm_report_info d
        <if test="policyNo != null || caseNo != null">
            ,clm_case_base b
        </if>
        <if test="reportBatchNo != null">
            ,clms_batch_report_temp c
        </if>
        where
        a.report_no = d.report_no
        <if test="policyNo != null || caseNo != null">
            and a.report_no = b.report_no
        </if>
        <if test="reportBatchNo != null">
            and a.report_no = c.report_no
        </if>
        <if test="reportNo != null">
            and a.report_no = #{reportNo}
        </if>
        <if test="policyNo != null">
            and b.policy_no = #{policyNo}
        </if>
        <if test="caseNo != null">
            and b.case_no = #{caseNo}
        </if>
        <if test="reportBatchNo != null">
            and c.report_batch_no = #{reportBatchNo}
        </if>
        and a.whole_case_status = 0
    </select>

    <select id="getSumPay" resultType="java.math.BigDecimal">
        select IFNULL( sum(pp.POLICY_SUM_PAY) , 0)
        from CLM_POLICY_PAY pp
        where pp.REPORT_NO=#{reportNo} and pp.CASE_TIMES=#{caseTimes}
    </select>

    <select id="getAccidentDateByReportNo" resultType="java.util.Date">
        select a.accident_date from clm_report_accident a where a.report_no = #{reportNo}
    </select>

    <select id="getInsuredNameByReportNo" resultType="java.lang.String">
        select a.name from clms_insured_person a,clms_policy_info b
        where b.ID_AHCS_POLICY_INFO = a.ID_AHCS_POLICY_INFO
        and b.report_no = #{reportNo} limit 1
    </select>

    <resultMap type="com.paic.ncbs.claim.model.vo.doc.PrintCaseInfoVO" id="printCaseInfo">
        <result column="report_no" property="reportNo"/>
        <result column="name" property="insuredName"/>
        <result column="certificate_no" property="certificateNo"/>
        <result column="accident_date" property="accidentDate"/>
        <result column="end_case_date" property="endCaseDate"/>
    </resultMap>
    <select id="getPrintCaseInfoVO" resultMap="printCaseInfo">
        select a.report_no,b.name,b.certificate_no,a.end_case_date,c.accident_date
        from clm_whole_case_base a,
        clm_report_accident c,
        (select d.name,d.certificate_no,e.report_no
        from clms_insured_person d,clms_policy_info e
        where d.ID_AHCS_POLICY_INFO = e.ID_AHCS_POLICY_INFO
        and e.report_no = #{reportNo} limit 1) b
        where a.report_no = #{reportNo}
        and a.case_times = #{caseTimes}
        and a.whole_case_status='0'
        and a.report_no = c.report_no
        and a.report_no = b.report_no
    </select>

    <resultMap type="com.paic.ncbs.claim.model.vo.doc.PrintDutyPayVO" id="printDutyPayVO">
        <result column="name" property="holderName"/>
        <result column="policy_no" property="policyNo"/>
    </resultMap>
    <select id="getPrintClaimInfo" resultMap="printDutyPayVO">
        select a.name ,b.policy_no
        from clms_policy_holder a,clms_policy_info b
        where b.id_ahcs_policy_info = a.id_ahcs_policy_info
        and b.report_no = #{report_no}
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.EndorsementDTO"
               id="result">
        <id property="idAhcsEndorsement" column="ID_AHCS_ENDORSEMENT"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="endorsementRemark" column="ENDORSEMENT_REMARK"/>
    </resultMap>
    <select id="getEndorsementInfo" resultMap="result">
        select
        ID_AHCS_ENDORSEMENT,
        REPORT_NO,
        CASE_TIMES,
        ENDORSEMENT_REMARK
        from CLMS_ENDORSEMENT
        where REPORT_NO=#{reportNo} and CASE_TIMES =#{caseTimes}
    </select>

    <select id="getHolderNameByPolicyNo" resultType="java.lang.String">
        SELECT DISTINCT NAME HOLDERNAME FROM CLMS_POLICY_HOLDER H,CLMS_POLICY_INFO P
        WHERE H.ID_AHCS_POLICY_INFO=P.ID_AHCS_POLICY_INFO
        AND P.REPORT_NO=#{reportNo}
        <if test="policyNo != null ">
            AND P.POLICY_NO = #{policyNo}
        </if>
        <if test="policyCerNo != null ">
            AND P.POLICY_CER_NO = #{policyCerNo}
        </if>
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.SettleBatchInfoDTO" id="settleBatchInfoDTO">
        <result property="idAhcsSettleBatchInfo" column="ID_AHCS_SETTLE_BATCH_INFO"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="idAhcsBatch" column="ID_AHCS_BATCH"/>
        <result property="policyPayAmount" column="POLICY_PAY_AMOUNT"/>
        <result property="policyFee" column="POLICY_FEE"/>
        <result property="finalPayAmount" column="FINAL_PAY_AMOUNT"/>
        <result property="finalFee" column="FINAL_FEE"/>
        <result property="prePayAmount" column="PRE_PAY_AMOUNT"/>
        <result property="preFeeAmount" column="PRE_FEE_AMOUNT"/>
        <result property="migrateFrom" column="MIGRATE_FROM"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="accommodationAmount" column="ACCOMMODATION_AMOUNT"/>
        <result property="protocolAmount" column="PROTOCOL_AMOUNT"/>
    </resultMap>

    <select id="getSettleAmounts" resultMap="settleBatchInfoDTO">
        SELECT
        SUM(IFNULL(POLICY_PAY,0)) FINAL_PAY_AMOUNT,
        (SUM(IFNULL(POLICY_PAY,0))) POLICY_PAY_AMOUNT,
        SUM(IFNULL(POLICY_PRE_PAY,0)) PRE_PAY_AMOUNT
        FROM CLM_POLICY_PAY A
        WHERE REPORT_NO=#{reportNo,jdbcType=VARCHAR}
        AND CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
        AND EXISTS(
        SELECT 1 FROM CLMS_POLICY_INFO B WHERE A.CASE_NO = B.CASE_NO
        AND A.POLICY_NO = B.POLICY_NO
        AND A.REPORT_NO = B.REPORT_NO
        )
    </select>

    <resultMap type="com.paic.ncbs.claim.model.vo.doc.PrintBillInfoVO"
               id="PrintBillInfoVO">
        <result property="billNo" column="BILL_NO"/>
        <result property="startDate" column="START_DATE"/>
        <result property="therapyTypeName" column="VALUE_CHINESE_NAME"/>
        <result property="hospitalName" column="HOSPITAL_NAME"/>
        <result property="billAmount" column="BILL_AMOUNT"/>
        <result property="deductibleAmount" column="DEDUCTIBLE_AMOUNT"/>
        <result property="immoderateAmount" column="IMMODERATE_AMOUNT"/>
        <result property="partialDeductible" column="PARTIAL_DEDUCTIBLE"/>
        <result property="reasonableAmount" column="REASONABLE_AMOUNT"/>
        <result property="prepaidAmount" column="PREPAID_AMOUNT"/>
    </resultMap>
    <select id="getPrintBillInfoVO" resultMap="PrintBillInfoVO">
        select distinct a.BILL_NO,
        a.START_DATE,
        b.VALUE_CHINESE_NAME,
        a.HOSPITAL_NAME,
        a.BILL_AMOUNT,
        a.DEDUCTIBLE_AMOUNT,
        a.IMMODERATE_AMOUNT,
        a.PARTIAL_DEDUCTIBLE,
        a.REASONABLE_AMOUNT,
        a.PREPAID_AMOUNT
        from clms_bill_info a,clm_common_parameter b
        where a.THERAPY_TYPE = b.VALUE_CODE
        and a.REPORT_NO = #{reportNo}
        and a.CASE_TIMES = #{caseTimes}
        and a.IS_EFFECTIVE = 'Y'
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.pay.PaymentItemComData" id="paymentItemComData">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CLIENT_NAME" property="clientName"/>
        <result column="PAYMENT_AMOUNT" property="paymentAmount"/>
        <result column="CLIENT_BANK_NAME" property="clientBankName"/>
        <result column="CLIENT_BANK_ACCOUNT" property="clientBankAccount"/>
        <result column="PAYMENT_ITEM_STATUS" property="paymentItemStatus"/>
        <result column="PAYMENT_TYPE" property="paymentType"/>
        <result column="CLAIM_TYPE" property="claimType"/>
    </resultMap>
    <select id="requestPaymentItemDTOList" resultMap="paymentItemComData">
        select a.REPORT_NO,
        a.CLIENT_NAME,
        a.PAYMENT_AMOUNT,
        a.CLIENT_BANK_ACCOUNT,
        a.CLIENT_BANK_NAME,
        a.PAYMENT_ITEM_STATUS,
        a.PAYMENT_TYPE,
        a.CLAIM_TYPE
        from clm_payment_item a
        where a.REPORT_NO = #{reportNo}
        and a.CASE_TIMES = #{caseTimes}
        and PAYMENT_ITEM_STATUS not in ('90','20')
        and COLLECT_PAY_SIGN = '1'
        and PAYMENT_TYPE in ('13','11')
    </select>

    <resultMap type="com.paic.ncbs.claim.model.vo.doc.PrintDutyPayInfoVO" id="dutyPayInfo">
        <result column="policy_no" property="policyNo"/>
        <result column="duty_code" property="dutyCode"/>
        <result column="duty_name" property="dutyName"/>
        <result column="duty_amount" property="baseAmountPay"/>
        <result column="alread_pay" property="alreadyPay"/>
        <result column="remain_money" property="remainMoney"/>
    </resultMap>
    <select id="getDutyPayInfo" resultMap="dutyPayInfo">
        select t.policy_no,t.duty_code,t.duty_name,t.duty_amount
        ,ifnull(b.duty_pay_amount,0) alread_pay, (t.duty_amount - ifnull(b.duty_pay_amount,0)) remain_money
        from
        ( select t1.report_no,t1.policy_no,t3.duty_code,t3.duty_name,t3.duty_amount from CLMS_policy_info t1,
        CLMS_policy_plan t2, CLMS_policy_duty t3
        where t1.id_ahcs_policy_info = t2.id_ahcs_policy_info
        and t2.id_ahcs_policy_plan = t3.id_ahcs_policy_plan
        and t1.report_no = #{reportNo}
        ) t left join
        (select t.policy_no,d.duty_code,sum(d.duty_pay_amount) duty_pay_amount from CLM_CASE_BASE t,CLM_PLAN_DUTY_PAY d
        where t.case_no=d.case_no and t.REPORT_NO = #{reportNo} group by t.policy_no,d.duty_code) b
        on t.policy_no = b.policy_no
        and t.duty_code = b.duty_code
    </select>

    <select id="getPrintCount" resultType="int">
        select count(1) from clms_print_record a where a.REPORT_NO = #{reportNo} and a.CASE_TIMES = #{caseTimes}
    </select>

    <select id="getCaseZeroCancelInfo" resultType="com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO">
        select
        b.id_ahcs_zero_cancel_apply idAhcsZeroCancelApply,
        b.report_no reportNo,
        b.case_times caseTimes,
        b.apply_type applyType,
        b.REASON_CODE reasonCode,
        b.apply_reason_details applyReasonDetails,
        b.apply_um applyUm,
        b.apply_date applyDate,
        b.verify_um verifyUm,
        b.verify_date verifyDate,
        b.VERIFY_REASON_CODE verifyReasonCode,
        b.verify_options verifyOptions,
        b.verify_remark verifyRemark,
        b.status status,
        b.APPLY_FROM applyFrom
        from
        (select *
        from CLMS_zero_cancel_apply a
        where a.report_no = #{reportNo}
        and a.case_times = #{caseTimes}
        and a.status = '2'
        and a.apply_type = #{applyType}
        order by a.apply_times desc) as b
        limit 1
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO" id="verifyConclusionDTO">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CONLUSION_CAUSE_CODE" property="conclusionCauseCode"/>
        <result column="CONLUSION_CAUSE_DESC" property="conclusionCauseDesc"/>
    </resultMap>
    <select id="getRefuseInfo" resultType="com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO">
        select a.REPORT_NO reportNo,a.CONLUSION_CAUSE_CODE conclusionCauseCode,a.CONLUSION_CAUSE_DESC conclusionCauseDesc,
        AUDITING_COMMONT  auditingCommont
        from clms_verify_conclusion a
        where a.REPORT_NO = #{reportNo}
        and a.CASE_TIMES = #{caseTimes}
        and  IS_EFFECTIVE  = 'Y'
        limit 1
    </select>

    <select id="getPolicyNoListByReportNo" resultType="java.lang.String">
        SELECT DISTINCT CASE WHEN
        ifnull(POLICY_CER_NO,0) = 0
        THEN POLICY_NO ELSE CONCAT(POLICY_NO,'(',POLICY_CER_NO,')')
        END FROM CLMS_POLICY_INFO
        WHERE REPORT_NO = #{reportNo}
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO" id="caseStatusResult">
        <result column="WHOLE_CASE_STATUS" property="wholeCaseStatus"/>
        <result column="INDEMNITY_CONCLUSION" property="indemnityConclusion"/>
        <result column="INDEMNITY_MODEL" property="indemnityModel"/>
        <result column="REGISTER_DATE" property="registerDateStr"/>
        <result column="END_CASE_DATE" property="endCaseDateStr"/>
        <result column="PROCESS_STATUS" property="processStatus"/>
    </resultMap>
    <select id="getWholeCaseIndemnityStatus" resultMap="caseStatusResult">
        select wcb.WHOLE_CASE_STATUS,
        wcb.INDEMNITY_CONCLUSION,
        wcb.INDEMNITY_MODEL
        from CLM_WHOLE_CASE_BASE wcb
        where wcb.REPORT_NO = #{reportNo}
        and wcb.CASE_TIMES = #{caseTimes}
    </select>

    <insert id="addPrintRecord" parameterType="com.paic.ncbs.claim.model.dto.doc.PrintRecordDTO">
        INSERT INTO CLMS_PRINT_RECORD
        (CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,ID_AHCS_PRINT_RECORD, REPORT_NO, CASE_TIMES, USER_ID, PRINT_DATE,ARCHIVE_TIME)
        VALUES (#{createdBy}, SYSDATE(), #{updatedBy}, SYSDATE(), #{idAhcsPrintRecord},#{reportNo}, #{caseTimes}, #{userId}, SYSDATE(),
         SYSDATE()
        )
    </insert>

    <select id="getHistoryCaseByReportNo" resultMap="wholeCaseVO">
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        concat(a.indemnity_conclusion,IFNULL(a.indemnity_model,"")) indemnity_conclusion
        from
        clm_whole_case_base a,clm_report_info d
        where a.report_no = d.report_no
        and a.report_no = #{reportNo,jdbcType=VARCHAR}
        and a.whole_case_status = 0
        <if test="departmentCodes != null ">
            and d.ACCEPT_DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getHistoryCaseByPolicyCaseNo" resultMap="wholeCaseVO">
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        concat(a.indemnity_conclusion,IFNULL(a.indemnity_model,"")) indemnity_conclusion
        from
        clm_whole_case_base a,clm_case_base b, clm_report_info d
        where a.report_no = b.report_no
        and a.case_times = b.case_times
        and b.report_no = d.report_no
        and a.whole_case_status = 0
        <if test="policyNo != null">
            and b.policy_no = #{policyNo,jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null">
            and b.case_no = #{caseNo,jdbcType=VARCHAR}
        </if>
        <if test="batchNo != null">
            and (exists (select 1 from clms_batch_report_temp t where t.REPORT_BATCH_NO = #{batchNo,jdbcType=VARCHAR})
            or exists (select 1 from clms_batch_auto_close t where t.BATCH_NO=#{batchNo,jdbcType=VARCHAR}))
        </if>
        <if test="departmentCodes != null ">
            and d.ACCEPT_DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getHistoryCaseByBatchNo" resultMap="wholeCaseVO">
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        concat(a.indemnity_conclusion,IFNULL(a.indemnity_model,"")) indemnity_conclusion
        from
        clm_whole_case_base a,clm_report_info d,clms_batch_report_temp b
        where a.report_no = d.report_no
        and d.REPORT_NO =b.REPORT_NO
        and b.REPORT_BATCH_NO = #{batchNo,jdbcType=VARCHAR}
        and a.whole_case_status = 0
        <if test="departmentCodes != null ">
            and d.ACCEPT_DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        union
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        concat(a.indemnity_conclusion,IFNULL(a.indemnity_model,"")) indemnity_conclusion
        from
        clm_whole_case_base a,clm_report_info d,clms_batch_auto_close c
        where a.report_no = d.report_no
        and d.REPORT_NO = c.REPORT_NO
        and c.BATCH_NO  = #{batchNo,jdbcType=VARCHAR}
        and a.whole_case_status = 0
        <if test="departmentCodes != null ">
            and d.ACCEPT_DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="saveClaimNoticeFileId">
        update clm_whole_case_base set file_id = #{fileId}
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
    </update>
    <update id="updateCalculationId">
        update clm_whole_case_base set calculation_id = #{calculationId}
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
    </update>

    <update id="updateEntrustmentFileId">
        update CLMS_ENTRUST_MAIN set file_id = #{fileId}
        where ID_ENTRUST = #{idEntrust,jdbcType=VARCHAR}
    </update>

    <update id="saveCommissionFileId">
        update CLMS_INVESTIGATE set file_id = #{fileId}
        where ID_AHCS_INVESTIGATE = #{idAhcsInvestigate,jdbcType=VARCHAR}
    </update>

    <!-- 检查ID是否存在于调查表中 -->
    <select id="existsInInvestigateTable" resultType="int">
        SELECT COUNT(1) FROM CLMS_INVESTIGATE WHERE ID_AHCS_INVESTIGATE = #{id}
    </select>

    <!-- 检查ID是否存在于委托表中 -->
    <select id="existsInEntrustTable" resultType="int">
        SELECT COUNT(1) FROM CLMS_ENTRUST_MAIN WHERE ID_ENTRUST = #{id}
    </select>

    <select id="findFileId" resultType="java.lang.String">
        SELECT file_id from clm_whole_case_base
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
    </select>

    <select id="findCommissionFileId" resultType="java.lang.String">
        SELECT file_id from CLMS_INVESTIGATE
        where ID_AHCS_INVESTIGATE = #{idAhcsInvestigate,jdbcType=VARCHAR}
    </select>

    <select id="findEntrustmentFileId" resultType="java.lang.String">
        SELECT file_id from CLMS_ENTRUST_MAIN
        where ID_ENTRUST = #{idEntrust,jdbcType=VARCHAR}
    </select>

    <select id="selectParentDepartmentCodeByReportNo" resultType="java.lang.String">
        select PARENT_DEPARTMENT_CODE
        from department_relation
        where DEPARTMENT_LEVEL = 3
          and CHILD_DEPARTMENT_CODE =
              (select DEPARTMENT_CODE
               from CLMS_POLICY_INFO
               where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
               LIMIT 1)
    </select>

    <select id="findFileIdList" resultMap="wholeCaseVO">
        SELECT report_no,
               case_times,
               file_id from clm_whole_case_base
        where REPORT_NO = #{reportNo} and (file_id is not null or file_id != '') order by CASE_TIMES desc
    </select>
    <select id="getPaySum" resultType="java.math.BigDecimal">
        select IFNULL( sum(pp.POLICY_SUM_PAY) , 0)
        from CLM_POLICY_PAY pp
        where pp.REPORT_NO=#{reportNo}
        and pp.CASE_TIMES <![CDATA[ < ]]> #{caseTimes}
    </select>
    <select id="findCalculationId" resultType="java.lang.String">
        SELECT calculation_id from clm_whole_case_base
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
    </select>
</mapper>